package com.stylishlink.common.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 分页响应类
 * @param <T> 数据类型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PageResponse<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 数据列表
     */
    private List<T> records;

    /**
     * 总记录数
     */
    private Long total;

    /**
     * 总页数
     */
    private Long pages;

    /**
     * 当前页码
     */
    private Long current;

    /**
     * 每页大小
     */
    private Long size;

    /**
     * 是否有下一页
     */
    private Boolean hasNext;

    /**
     * 是否有上一页
     */
    private Boolean hasPrevious;

    /**
     * 静态构建方法
     */
    public static <T> PageResponse<T> of(List<T> records, Long total, Long current, Long size) {
        Long pages = (total + size - 1) / size;
        return PageResponse.<T>builder()
                .records(records)
                .total(total)
                .pages(pages)
                .current(current)
                .size(size)
                .hasNext(current < pages)
                .hasPrevious(current > 1)
                .build();
    }
} 