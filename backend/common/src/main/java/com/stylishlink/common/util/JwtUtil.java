package com.stylishlink.common.util;

import io.jsonwebtoken.*;
import io.jsonwebtoken.security.Keys;
import io.jsonwebtoken.security.SignatureException; // Correct for 0.12.x
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.crypto.SecretKey;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.Date;
import java.util.function.Function;

@Component
public class JwtUtil {

    private static final Logger logger = LoggerFactory.getLogger(JwtUtil.class);

    @Value("${jwt.secret:DefaultSecretKeyWhichShouldBeOverriddenInConfig}") // Ensure this key is strong for HS256
    private String jwtSecretString;

    @Value("${jwt.expirationMs:86400000}") // Default to 1 day (24 * 60 * 60 * 1000)
    private long jwtExpirationMs;

    private SecretKey getSigningKey() {
        // Decode the Base64 string first, then get bytes
        byte[] keyBytes = Base64.getDecoder().decode(jwtSecretString.getBytes(StandardCharsets.UTF_8));
        // For HS256, key length should be at least 256 bits (32 bytes).
        // Keys.hmacShaKeyFor will generate a key of appropriate length if the input is shorter,
        // but it's best practice to provide a strong, sufficiently long secret string.
        return Keys.hmacShaKeyFor(keyBytes);
    }

    public String generateToken(String subject) {
        return Jwts.builder()
                .subject(subject) // Use .subject() instead of .setSubject()
                .issuedAt(new Date())
                .expiration(new Date(System.currentTimeMillis() + jwtExpirationMs)) // Use .expiration() instead of .setExpiration()
                .signWith(getSigningKey(), Jwts.SIG.HS256) // Use Jwts.SIG.HS256 for 0.12.x
                .compact();
    }

    private JwtParser getParser() {
        // Based on Maven error: Jwts.parser() returns JwtParserBuilder in the effective jjwt version.
        // JwtParserBuilder should have verifyWith() and build().
        return Jwts.parser() // This is assumed to return JwtParserBuilder
                       .verifyWith(getSigningKey()) // verifyWith should be on JwtParserBuilder
                       .build(); // build() should return JwtParser
    }

    public Claims getAllClaimsFromToken(String token) {
        try {
            return getParser().parseSignedClaims(token).getPayload(); 
        } catch (JwtException e) {
            logger.error("Error parsing claims from token: {}", e.getMessage());
            throw e; 
        }
    }

    public <T> T getClaimFromToken(String token, Function<Claims, T> claimsResolver) {
        final Claims claims = getAllClaimsFromToken(token);
        return claimsResolver.apply(claims);
    }

    public String getSubjectFromToken(String token) {
        return getClaimFromToken(token, Claims::getSubject);
    }

    public Date getExpirationDateFromToken(String token) {
        return getClaimFromToken(token, Claims::getExpiration);
    }

    private Boolean isTokenExpired(String token) {
        try {
            final Date expiration = getExpirationDateFromToken(token);
            return expiration.before(new Date());
        } catch (JwtException e) {
            logger.warn("Could not determine expiration from token (likely parsing error), assuming expired/invalid: {}", e.getMessage());
            return true;
        }
    }

    public Boolean validateToken(String token, String subject) {
        try {
            final String tokenSubject = getSubjectFromToken(token);
            // getAllClaimsFromToken (which getSubjectFromToken uses) will throw if signature is invalid or token is malformed.
            return (tokenSubject.equals(subject) && !isTokenExpired(token));
        } catch (SignatureException e) {
            logger.error("Invalid JWT signature (validateToken with subject): {}", e.getMessage());
        } catch (MalformedJwtException e) {
            logger.error("Invalid JWT format (validateToken with subject): {}", e.getMessage());
        } catch (ExpiredJwtException e) {
            logger.error("JWT token is expired (validateToken with subject): {}", e.getMessage());
        } catch (UnsupportedJwtException e) {
            logger.error("JWT token is unsupported (validateToken with subject): {}", e.getMessage());
        } catch (IllegalArgumentException e) { // ClaimsJws argument is null or empty or only whitespace
            logger.error("JWT claims string is empty or invalid (validateToken with subject): {}", e.getMessage());
        } catch (JwtException e) { // Catch-all for other JWT related issues from parsing in getSubjectFromToken
            logger.error("JWT processing error (validateToken with subject): {}", e.getMessage());
        }
        return false;
    }

    public boolean validateToken(String token) {
        try {
            getParser().parseSignedClaims(token);
            return !isTokenExpired(token); 
        } catch (SignatureException e) {
            logger.error("Invalid JWT signature (validateToken): {} for token: {}", e.getMessage(), token);
        } catch (MalformedJwtException e) {
            logger.error("Invalid JWT format (validateToken): {} for token: {}", e.getMessage(), token);
        } catch (ExpiredJwtException e) {
            logger.error("JWT token is expired (validateToken): {} for token: {}", e.getMessage(), token);
        } catch (UnsupportedJwtException e) {
            logger.error("JWT token is unsupported (validateToken): {} for token: {}", e.getMessage(), token);
        } catch (IllegalArgumentException e) { // ClaimsJws argument is null or empty or only whitespace
            logger.error("JWT claims string is empty or invalid (validateToken): {} for token: {}", e.getMessage(), token);
        } catch (JwtException e) { // Catch-all for other JWT related issues from parsing
            logger.error("JWT validation error (validateToken): {} for token: {}", e.getMessage(), token);
        }
        return false;
    }
} 