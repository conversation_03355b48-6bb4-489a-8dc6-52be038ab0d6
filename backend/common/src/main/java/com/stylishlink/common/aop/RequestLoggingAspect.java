package com.stylishlink.common.aop;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.MDC;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.ui.Model;
import org.springframework.ui.ModelMap;
import org.springframework.validation.Errors;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Aspect
@Component
@Slf4j
public class RequestLoggingAspect {

    private final ObjectMapper objectMapper;
    private static final String TRACE_ID_HEADER = "X-Trace-ID";
    private static final String TRACE_ID_MDC_KEY = "traceId";

    public RequestLoggingAspect() {
        this.objectMapper = new ObjectMapper();
        this.objectMapper.registerModule(new JavaTimeModule());
        this.objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        this.objectMapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
    }

    @Pointcut("@within(org.springframework.web.bind.annotation.RestController) && execution(public * *(..))")
    public void restControllerMethods() {
        // Pointcut definition
    }

    @Around("restControllerMethods() && !within(org.springdoc.webflux..*)")
    public Object logApiRequestResponse(ProceedingJoinPoint joinPoint) throws Throwable {
        String traceId = null;
        HttpServletRequest request = null;
        HttpServletResponse response = null;
        long startTime = 0L;
        String className = null;
        String methodName = null;
        boolean isWebFluxEnvironment = false;

        try {
            // 检查是否为WebFlux环境
            try {
                ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
                if (attributes != null) {
                    request = attributes.getRequest();
                    response = attributes.getResponse();
                }
            } catch (Exception e) {
                // 在WebFlux环境中可能无法获取ServletRequestAttributes
                isWebFluxEnvironment = true;
                log.debug("WebFlux环境检测: 无法获取ServletRequestAttributes, 跳过request/response处理");
            }

            // 生成traceId
            if (request != null) {
                traceId = request.getHeader(TRACE_ID_HEADER);
            }

            if (traceId == null || traceId.isEmpty()) {
                traceId = UUID.randomUUID().toString().replace("-", "");
            }

            MDC.put(TRACE_ID_MDC_KEY, traceId);

            // 只在非WebFlux环境中设置response header
            if (response != null && !isWebFluxEnvironment) {
                response.setHeader(TRACE_ID_HEADER, traceId);
            }

            className = joinPoint.getSignature().getDeclaringTypeName();
            methodName = joinPoint.getSignature().getName();
            Object[] args = joinPoint.getArgs();
            String requestParams = "N/A";

            try {
                Object[] serializableArgs = filterSerializableArgs(args);
                if (serializableArgs.length > 0) {
                    requestParams = objectMapper.writeValueAsString(serializableArgs);
                } else if (args.length > 0 && serializableArgs.length == 0) {
                    requestParams = "[Contains only non-serializable arguments]";
                }
            } catch (Exception e) {
                log.warn("Error serializing request parameters for {}.{}: {}", className, methodName, e.getMessage());
                requestParams = "[Request Serialization Error]";
            }

            log.info("===> Request: {}.{}() | Params: {}", className, methodName, requestParams);

            startTime = System.currentTimeMillis();
            Object result;
            result = joinPoint.proceed(); // Proceed with the original method execution
            
            long duration = System.currentTimeMillis() - startTime;
            String responseBody = "N/A";
            try {
                if (result != null) {
                    Object resultToLog = result;
                    if (result instanceof ResponseEntity) {
                        resultToLog = ((ResponseEntity<?>) result).getBody();
                        if (response != null) { // Log status from ResponseEntity if response object is available
                             log.info("     Response Status for {}.{}: {}", className, methodName, ((ResponseEntity<?>) result).getStatusCode());
                        }
                    }
                    if (resultToLog != null) {
                        responseBody = objectMapper.writeValueAsString(resultToLog);
                    } else if (result instanceof ResponseEntity && ((ResponseEntity<?>) result).getBody() == null) {
                        responseBody = "[ResponseEntity with null body]";
                    }
                } else {
                    responseBody = "[null]";
                }
            } catch (Exception e) {
                log.warn("Error serializing response body for {}.{}: {}", className, methodName, e.getMessage());
                responseBody = "[Response Serialization Error]";
            }
            log.info("<=== Response: {}.{}() | Duration: {}ms | Result: {}", className, methodName, duration, responseBody);
            return result;

        } catch (Throwable throwable) {
            long durationCaught = 0L;
            if (startTime > 0L) {
                durationCaught = System.currentTimeMillis() - startTime;
            }
            String finalClassName = className != null ? className : joinPoint.getSignature().getDeclaringTypeName();
            String finalMethodName = methodName != null ? methodName : joinPoint.getSignature().getName();

            log.error("<=== Error in {}.{}() | Duration: {}ms | Exception: {}: {}",
                    finalClassName, finalMethodName, durationCaught, throwable.getClass().getSimpleName(), throwable.getMessage(), throwable);
            throw throwable; // Re-throw the original exception
        } finally {
            MDC.remove(TRACE_ID_MDC_KEY);
        }
    }

    private Object[] filterSerializableArgs(Object[] args) {
        if (args == null) {
            return new Object[0];
        }
        List<Object> serializableArgs = new ArrayList<>();
        for (Object arg : args) {
            if (arg != null &&
                !isServletRequest(arg) &&
                !isServletResponse(arg) &&
                !(arg instanceof MultipartFile) &&
                !(arg instanceof MultipartFile[]) && // Handle array of MultipartFiles
                !(arg instanceof Resource) &&
                !(arg instanceof InputStream) &&
                !(arg instanceof OutputStream) &&
                !(arg instanceof Model) &&
                !(arg instanceof ModelMap) &&
                !(arg instanceof Errors) &&
                !(arg instanceof NativeWebRequest) &&
                !(arg instanceof HttpEntity && !(((HttpEntity<?>) arg).getBody() instanceof String || ((HttpEntity<?>) arg).getBody() == null || isSimpleType(((HttpEntity<?>) arg).getBody()))) && // Allow HttpEntity with simple body types
                !(arg.getClass().getName().startsWith("org.springframework.security.core.")) &&
                !(arg.getClass().getName().startsWith("org.springframework.web.method.support.")) && // e.g. HandlerMethodArgumentResolver
                !(arg.getClass().getName().startsWith("org.springframework.http.converter.")) && // e.g. HttpMessageConverter
                !(arg.getClass().getName().startsWith("org.springframework.http.server.reactive.")) && // WebFlux ServerHttpRequest/Response
                !(arg.getClass().getName().startsWith("org.springframework.web.server.")) // WebFlux ServerWebExchange
                ) {
                serializableArgs.add(arg);
            }
        }
        return serializableArgs.toArray();
    }

    /**
     * 安全地检查是否为ServletRequest类型，兼容WebFlux环境
     */
    private boolean isServletRequest(Object arg) {
        try {
            return arg instanceof ServletRequest;
        } catch (NoClassDefFoundError e) {
            // 在WebFlux环境中ServletRequest类不存在，通过类名进行字符串匹配
            return arg.getClass().getName().contains("ServletRequest");
        }
    }

    /**
     * 安全地检查是否为ServletResponse类型，兼容WebFlux环境
     */
    private boolean isServletResponse(Object arg) {
        try {
            return arg instanceof ServletResponse;
        } catch (NoClassDefFoundError e) {
            // 在WebFlux环境中ServletResponse类不存在，通过类名进行字符串匹配
            return arg.getClass().getName().contains("ServletResponse");
        }
    }

    private boolean isSimpleType(Object obj) {
        if (obj == null) return true;
        Class<?> clazz = obj.getClass();
        return clazz.isPrimitive() ||
               clazz.equals(String.class) ||
               clazz.equals(Boolean.class) ||
               clazz.equals(Character.class) ||
               Number.class.isAssignableFrom(clazz) ||
               java.util.Date.class.isAssignableFrom(clazz) || // Include dates
               java.time.temporal.Temporal.class.isAssignableFrom(clazz); // Include Java 8 time types
    }
} 