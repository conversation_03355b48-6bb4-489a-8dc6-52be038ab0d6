package com.stylishlink.payment.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 订阅订单实体
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("payment_subscription_order")
public class SubscriptionOrder implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private String userId;

    /**
     * 订阅产品ID
     */
    @TableField("product_id")
    private String productId;

    /**
     * 订阅类型（MONTHLY-月卡, QUARTERLY-季卡, YEARLY-年卡）
     */
    @TableField("type")
    private String type;

    /**
     * 订阅金额(分)
     */
    @TableField("amount")
    private Long amount;

    /**
     * 订阅周期(月)
     */
    @TableField("period_months")
    private Integer periodMonths;

    /**
     * 订阅状态（PENDING-待支付, ACTIVE-激活, EXPIRED-过期, CANCELLED-已取消）
     */
    @TableField("status")
    private String status;

    /**
     * 支付订单ID
     */
    @TableField("payment_order_id")
    private String paymentOrderId;

    /**
     * 是否自动续费
     */
    @TableField("is_auto_renew")
    private Boolean isAutoRenew;

    /**
     * 是否删除（逻辑删除）
     */
    @TableLogic
    @TableField("deleted")
    private Integer deleted;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    /**
     * 开始日期
     */
    @TableField("start_date")
    private LocalDateTime startDate;

    /**
     * 结束日期
     */
    @TableField("end_date")
    private LocalDateTime endDate;

    /**
     * 取消时间
     */
    @TableField("canceled_at")
    private LocalDateTime canceledAt;
} 