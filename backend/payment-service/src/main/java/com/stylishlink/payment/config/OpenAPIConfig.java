package com.stylishlink.payment.config;

import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.servers.Server;
import io.swagger.v3.oas.models.tags.Tag;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * 支付服务OpenAPI配置
 */
@Configuration
public class OpenAPIConfig {

    @Value("${server.port:8087}")
    private String serverPort;

    @Bean
    public OpenAPI paymentServiceOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("支付服务API")
                        .description("StylishLink支付服务API文档，提供会员充值、购买特权、订单管理、支付回调等功能")
                        .version("1.0.0")
                        .contact(new Contact()
                                .name("StylishLink团队")
                                .email("<EMAIL>")
                                .url("https://stylishlink.com"))
                        .license(new License()
                                .name("MIT")
                                .url("https://opensource.org/licenses/MIT")))
                .servers(List.of(
                        new Server()
                                .url("http://localhost:" + serverPort)
                                .description("本地开发环境"),
                        new Server()
                                .url("https://api.stylishlink.com")
                                .description("生产环境")
                ))
                .addSecurityItem(new SecurityRequirement()
                        .addList("Bearer Authentication"))
                .components(new Components()
                        .addSecuritySchemes("Bearer Authentication", 
                                new SecurityScheme()
                                        .type(SecurityScheme.Type.HTTP)
                                        .scheme("bearer")
                                        .bearerFormat("JWT")
                                        .description("请在请求头中添加 Authorization: Bearer {token}")))
                .tags(List.of(
                        new Tag()
                                .name("订单管理")
                                .description("订单创建、查询、取消、退款等接口"),
                        new Tag()
                                .name("支付处理")
                                .description("支付发起、支付确认、支付状态查询等接口"),
                        new Tag()
                                .name("会员充值")
                                .description("会员套餐、充值记录、余额查询等接口"),
                        new Tag()
                                .name("特权购买")
                                .description("高级功能购买、特权激活、使用记录等接口"),
                        new Tag()
                                .name("支付回调")
                                .description("第三方支付平台回调处理接口"),
                        new Tag()
                                .name("财务报表")
                                .description("收入统计、交易报表、对账数据等接口")
                ));
    }
} 