package com.stylishlink.payment.dto.response;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 发票响应
 */
@Data
public class InvoiceResponse {
    
    /**
     * 发票ID
     */
    private String invoiceId;
    
    /**
     * 发票状态（PENDING-待开具, PROCESSING-开具中, COMPLETED-已完成, FAILED-失败）
     */
    private String status;
    
    /**
     * 发票URL
     */
    private String invoiceUrl;
    
    /**
     * 预计完成时间
     */
    private LocalDateTime estimatedTime;
    
    /**
     * 开具时间
     */
    private LocalDateTime issuedAt;
} 