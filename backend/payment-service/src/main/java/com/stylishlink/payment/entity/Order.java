package com.stylishlink.payment.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 业务订单实体
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("payment_order_business")
public class Order implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private String userId;

    /**
     * 订单类型（RECHARGE-充值, SUBSCRIPTION-订阅）
     */
    @TableField("type")
    private String type;

    /**
     * 订单状态（PENDING-待支付, PAID-已支付, COMPLETED-已完成, CANCELLED-已取消）
     */
    @TableField("status")
    private String status;

    /**
     * 总金额(分)
     */
    @TableField("total_amount")
    private Long totalAmount;

    /**
     * 折扣金额(分)
     */
    @TableField("discount_amount")
    private Integer discountAmount;

    /**
     * 最终金额(分)
     */
    @TableField("final_amount")
    private Long finalAmount;

    /**
     * 支付订单ID
     */
    @TableField("payment_order_id")
    private String paymentOrderId;

    /**
     * 订单项目(JSON格式)
     */
    @TableField("items")
    private String items;

    /**
     * 备注
     */
    @TableField("remarks")
    private String remarks;

    /**
     * 是否删除（逻辑删除）
     */
    @TableLogic
    @TableField("deleted")
    private Integer deleted;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    /**
     * 支付时间
     */
    @TableField("paid_at")
    private LocalDateTime paidAt;

    /**
     * 完成时间
     */
    @TableField("completed_at")
    private LocalDateTime completedAt;
} 