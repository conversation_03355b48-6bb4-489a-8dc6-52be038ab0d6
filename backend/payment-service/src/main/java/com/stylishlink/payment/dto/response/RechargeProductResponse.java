package com.stylishlink.payment.dto.response;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 充值产品响应
 */
@Data
public class RechargeProductResponse {
    
    /**
     * 产品ID
     */
    private String id;
    
    /**
     * 产品名称
     */
    private String name;
    
    /**
     * 价格(分)
     */
    private Long price;
    
    /**
     * 基础灵感值
     */
    private Integer baseAmount;
    
    /**
     * 赠送灵感值
     */
    private Integer bonusAmount;
    
    /**
     * 总灵感值
     */
    private Integer totalAmount;
    
    /**
     * 是否热门
     */
    private Boolean isPopular;
    
    /**
     * 是否限时
     */
    private Boolean isLimited;
    
    /**
     * 有效期开始
     */
    private LocalDateTime validFrom;
    
    /**
     * 有效期结束
     */
    private LocalDateTime validTo;
} 