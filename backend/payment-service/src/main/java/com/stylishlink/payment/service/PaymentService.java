package com.stylishlink.payment.service;

import com.stylishlink.payment.dto.request.CreatePaymentOrderRequest;
import com.stylishlink.payment.dto.request.CreateRechargeOrderRequest;
import com.stylishlink.payment.dto.response.PaymentOrderResponse;
import com.stylishlink.payment.dto.response.RechargeProductResponse;

import java.util.List;

/**
 * 支付服务接口
 */
public interface PaymentService {
    
    /**
     * 创建支付订单
     */
    PaymentOrderResponse createPaymentOrder(CreatePaymentOrderRequest request);
    
    /**
     * 查询支付订单
     */
    PaymentOrderResponse getPaymentOrder(String orderId);
    
    /**
     * 获取充值产品列表
     */
    List<RechargeProductResponse> getRechargeProducts();
    
    /**
     * 创建充值订单
     */
    PaymentOrderResponse createRechargeOrder(CreateRechargeOrderRequest request);
} 