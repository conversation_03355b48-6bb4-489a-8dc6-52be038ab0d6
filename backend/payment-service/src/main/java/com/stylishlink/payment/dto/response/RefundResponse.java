package com.stylishlink.payment.dto.response;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 退款响应
 */
@Data
public class RefundResponse {
    
    /**
     * 退款ID
     */
    private String refundId;
    
    /**
     * 退款状态（PENDING-待处理, PROCESSING-处理中, COMPLETED-已完成, FAILED-失败）
     */
    private String status;
    
    /**
     * 退款金额(分)
     */
    private Long amount;
    
    /**
     * 预计完成时间
     */
    private LocalDateTime estimatedTime;
    
    /**
     * 处理时间
     */
    private LocalDateTime processedAt;
    
    /**
     * 完成时间
     */
    private LocalDateTime completedAt;
} 