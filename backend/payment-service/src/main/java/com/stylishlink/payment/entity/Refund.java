package com.stylishlink.payment.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 退款记录实体
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("payment_refund")
public class Refund implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private String userId;

    /**
     * 订单ID
     */
    @TableField("order_id")
    private String orderId;

    /**
     * 支付订单ID
     */
    @TableField("payment_order_id")
    private String paymentOrderId;

    /**
     * 退款金额(分)
     */
    @TableField("amount")
    private Long amount;

    /**
     * 退款原因
     */
    @TableField("reason")
    private String reason;

    /**
     * 退款状态（PENDING-待处理, PROCESSING-处理中, COMPLETED-已完成, FAILED-失败）
     */
    @TableField("status")
    private String status;

    /**
     * 第三方退款流水号
     */
    @TableField("refund_id")
    private String refundId;

    /**
     * 备注
     */
    @TableField("remarks")
    private String remarks;

    /**
     * 审核人员ID
     */
    @TableField("reviewer_id")
    private String reviewerId;

    /**
     * 审核意见
     */
    @TableField("review_comment")
    private String reviewComment;

    /**
     * 是否删除（逻辑删除）
     */
    @TableLogic
    @TableField("deleted")
    private Integer deleted;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    /**
     * 处理时间
     */
    @TableField("processed_at")
    private LocalDateTime processedAt;

    /**
     * 完成时间
     */
    @TableField("completed_at")
    private LocalDateTime completedAt;
} 