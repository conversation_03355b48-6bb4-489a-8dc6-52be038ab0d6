package com.stylishlink.payment.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 发票实体
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("payment_invoice")
public class Invoice implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private String userId;

    /**
     * 订单ID
     */
    @TableField("order_id")
    private String orderId;

    /**
     * 发票类型（PERSONAL-个人, COMPANY-企业）
     */
    @TableField("type")
    private String type;

    /**
     * 发票抬头
     */
    @TableField("title")
    private String title;

    /**
     * 税号
     */
    @TableField("tax_number")
    private String taxNumber;

    /**
     * 接收邮箱
     */
    @TableField("email")
    private String email;

    /**
     * 金额(分)
     */
    @TableField("amount")
    private Long amount;

    /**
     * 发票状态（PENDING-待开具, PROCESSING-开具中, COMPLETED-已完成, FAILED-失败）
     */
    @TableField("status")
    private String status;

    /**
     * 发票URL
     */
    @TableField("invoice_url")
    private String invoiceUrl;

    /**
     * 发票号码
     */
    @TableField("invoice_number")
    private String invoiceNumber;

    /**
     * 备注
     */
    @TableField("remarks")
    private String remarks;

    /**
     * 是否删除（逻辑删除）
     */
    @TableLogic
    @TableField("deleted")
    private Integer deleted;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    /**
     * 开具时间
     */
    @TableField("issued_at")
    private LocalDateTime issuedAt;
} 