package com.stylishlink.payment.controller;

import com.stylishlink.payment.dto.ApiResponse;
import com.stylishlink.payment.dto.request.CreatePaymentOrderRequest;
import com.stylishlink.payment.dto.request.CreateRechargeOrderRequest;
import com.stylishlink.payment.dto.response.PaymentOrderResponse;
import com.stylishlink.payment.dto.response.RechargeProductResponse;
import com.stylishlink.payment.service.PaymentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 支付控制器
 */
@Slf4j
@RestController
@RequestMapping("/payment")
@RequiredArgsConstructor
@Tag(name = "支付管理", description = "支付订单和充值管理接口")
public class PaymentController {

    private final PaymentService paymentService;

    /**
     * 创建支付订单
     */
    @PostMapping("/order/create")
    @Operation(summary = "创建支付订单", description = "创建新的支付订单")
    public ApiResponse<PaymentOrderResponse> createPaymentOrder(
            @Valid @RequestBody CreatePaymentOrderRequest request) {
        try {
            log.info("创建支付订单请求: {}", request);
            PaymentOrderResponse response = paymentService.createPaymentOrder(request);
            return ApiResponse.success(response);
        } catch (Exception e) {
            log.error("创建支付订单失败", e);
            return ApiResponse.error("创建支付订单失败: " + e.getMessage());
        }
    }

    /**
     * 查询支付订单
     */
    @GetMapping("/order/{orderId}")
    @Operation(summary = "查询支付订单", description = "根据订单ID查询支付订单状态")
    public ApiResponse<PaymentOrderResponse> getPaymentOrder(@PathVariable String orderId) {
        try {
            log.info("查询支付订单: {}", orderId);
            PaymentOrderResponse response = paymentService.getPaymentOrder(orderId);
            return ApiResponse.success(response);
        } catch (Exception e) {
            log.error("查询支付订单失败", e);
            return ApiResponse.error("查询支付订单失败: " + e.getMessage());
        }
    }

    /**
     * 获取充值产品列表
     */
    @GetMapping("/recharge/products")
    @Operation(summary = "获取充值产品列表", description = "获取所有可用的充值产品")
    public ApiResponse<List<RechargeProductResponse>> getRechargeProducts() {
        try {
            log.info("获取充值产品列表");
            List<RechargeProductResponse> products = paymentService.getRechargeProducts();
            return ApiResponse.success(products);
        } catch (Exception e) {
            log.error("获取充值产品列表失败", e);
            return ApiResponse.error("获取充值产品列表失败: " + e.getMessage());
        }
    }

    /**
     * 创建充值订单
     */
    @PostMapping("/recharge/order")
    @Operation(summary = "创建充值订单", description = "创建灵感值充值订单")
    public ApiResponse<PaymentOrderResponse> createRechargeOrder(
            @Valid @RequestBody CreateRechargeOrderRequest request) {
        try {
            log.info("创建充值订单请求: {}", request);
            PaymentOrderResponse response = paymentService.createRechargeOrder(request);
            return ApiResponse.success(response);
        } catch (Exception e) {
            log.error("创建充值订单失败", e);
            return ApiResponse.error("创建充值订单失败: " + e.getMessage());
        }
    }
} 