package com.stylishlink.payment.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 订阅产品实体
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("payment_subscription_product")
public class SubscriptionProduct implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 产品名称
     */
    @TableField("name")
    private String name;

    /**
     * 产品描述
     */
    @TableField("description")
    private String description;

    /**
     * 订阅类型（MONTHLY-月卡, QUARTERLY-季卡, YEARLY-年卡）
     */
    @TableField("type")
    private String type;

    /**
     * 订阅周期(月)
     */
    @TableField("period_months")
    private Integer periodMonths;

    /**
     * 价格(分)
     */
    @TableField("price")
    private Long price;

    /**
     * 每日奖励灵感值
     */
    @TableField("daily_reward")
    private Integer dailyReward;

    /**
     * 周期内总奖励
     */
    @TableField("total_reward")
    private Integer totalReward;

    /**
     * 包含特权(JSON格式)
     */
    @TableField("privileges")
    private String privileges;

    /**
     * 图标URL
     */
    @TableField("icon")
    private String icon;

    /**
     * 是否热门
     */
    @TableField("is_popular")
    private Boolean isPopular;

    /**
     * 是否可用
     */
    @TableField("is_active")
    private Boolean isActive;

    /**
     * 折扣(相对于月卡)
     */
    @TableField("discount")
    private BigDecimal discount;

    /**
     * 排序顺序
     */
    @TableField("sort_order")
    private Integer sortOrder;

    /**
     * 是否删除（逻辑删除）
     */
    @TableLogic
    @TableField("deleted")
    private Integer deleted;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
} 