package com.stylishlink.payment.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 支付订单实体
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("payment_order")
public class PaymentOrder implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private String userId;

    /**
     * 源订单ID
     */
    @TableField("origin_order_id")
    private String originOrderId;

    /**
     * 订单类型（RECHARGE-充值, SUBSCRIPTION-订阅）
     */
    @TableField("type")
    private String type;

    /**
     * 支付渠道（WECHAT-微信, ALIPAY-支付宝）
     */
    @TableField("channel")
    private String channel;

    /**
     * 支付状态（PENDING-待支付, PAID-已支付, EXPIRED-已过期, FAILED-失败）
     */
    @TableField("status")
    private String status;

    /**
     * 金额(分)
     */
    @TableField("amount")
    private Long amount;

    /**
     * 商品名称
     */
    @TableField("subject")
    private String subject;

    /**
     * 商品描述
     */
    @TableField("description")
    private String description;

    /**
     * 第三方交易ID
     */
    @TableField("transaction_id")
    private String transactionId;

    /**
     * 通知地址
     */
    @TableField("notify_url")
    private String notifyUrl;

    /**
     * 返回地址
     */
    @TableField("return_url")
    private String returnUrl;

    /**
     * 支付URL
     */
    @TableField("payment_url")
    private String paymentUrl;

    /**
     * 客户端IP
     */
    @TableField("client_ip")
    private String clientIp;

    /**
     * 是否删除（逻辑删除）
     */
    @TableLogic
    @TableField("deleted")
    private Integer deleted;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    /**
     * 支付时间
     */
    @TableField("paid_at")
    private LocalDateTime paidAt;

    /**
     * 过期时间
     */
    @TableField("expired_at")
    private LocalDateTime expiredAt;
} 