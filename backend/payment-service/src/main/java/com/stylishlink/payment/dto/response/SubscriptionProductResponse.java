package com.stylishlink.payment.dto.response;

import lombok.Data;
import java.math.BigDecimal;
import java.util.List;

/**
 * 订阅产品响应
 */
@Data
public class SubscriptionProductResponse {
    
    /**
     * 产品ID
     */
    private String id;
    
    /**
     * 产品名称
     */
    private String name;
    
    /**
     * 订阅类型（MONTHLY-月卡, QUARTERLY-季卡, YEARLY-年卡）
     */
    private String type;
    
    /**
     * 价格(分)
     */
    private Long price;
    
    /**
     * 订阅周期(月)
     */
    private Integer periodMonths;
    
    /**
     * 每日奖励灵感值
     */
    private Integer dailyReward;
    
    /**
     * 周期内总奖励
     */
    private Integer totalReward;
    
    /**
     * 包含特权
     */
    private List<String> privileges;
    
    /**
     * 折扣(相对于月卡)
     */
    private BigDecimal discount;
} 