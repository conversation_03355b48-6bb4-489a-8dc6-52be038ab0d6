package com.stylishlink.payment.dto.request;

import lombok.Data;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;

/**
 * 退款申请请求
 */
@Data
public class RefundRequest {
    
    /**
     * 用户ID
     */
    @NotBlank(message = "用户ID不能为空")
    private String userId;
    
    /**
     * 订单ID
     */
    @NotBlank(message = "订单ID不能为空")
    private String orderId;
    
    /**
     * 退款金额(分)
     */
    @NotNull(message = "退款金额不能为空")
    @Positive(message = "退款金额必须大于0")
    private Long amount;
    
    /**
     * 退款原因
     */
    @NotBlank(message = "退款原因不能为空")
    private String reason;
    
    /**
     * 备注
     */
    private String remarks;
} 