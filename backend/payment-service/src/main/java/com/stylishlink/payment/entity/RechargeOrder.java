package com.stylishlink.payment.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 充值订单实体
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("payment_recharge_order")
public class RechargeOrder implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private String userId;

    /**
     * 充值产品ID
     */
    @TableField("product_id")
    private String productId;

    /**
     * 充值金额(分)
     */
    @TableField("amount")
    private Long amount;

    /**
     * 灵感值数量
     */
    @TableField("inspiration_amount")
    private Integer inspirationAmount;

    /**
     * 赠送灵感值
     */
    @TableField("bonus_amount")
    private Integer bonusAmount;

    /**
     * 充值状态（PENDING-待支付, PAID-已支付, COMPLETED-已完成, FAILED-失败）
     */
    @TableField("status")
    private String status;

    /**
     * 支付订单ID
     */
    @TableField("payment_order_id")
    private String paymentOrderId;

    /**
     * 是否首充
     */
    @TableField("is_first_recharge")
    private Boolean isFirstRecharge;

    /**
     * 首充奖励
     */
    @TableField("first_recharge_bonus")
    private Integer firstRechargeBonus;

    /**
     * 是否删除（逻辑删除）
     */
    @TableLogic
    @TableField("deleted")
    private Integer deleted;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    /**
     * 完成时间
     */
    @TableField("completed_at")
    private LocalDateTime completedAt;
} 