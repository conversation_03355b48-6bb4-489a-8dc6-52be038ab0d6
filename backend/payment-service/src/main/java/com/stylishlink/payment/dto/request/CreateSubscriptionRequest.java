package com.stylishlink.payment.dto.request;

import lombok.Data;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 创建订阅请求
 */
@Data
public class CreateSubscriptionRequest {
    
    /**
     * 用户ID
     */
    @NotBlank(message = "用户ID不能为空")
    private String userId;
    
    /**
     * 订阅产品ID
     */
    @NotBlank(message = "产品ID不能为空")
    private String productId;
    
    /**
     * 是否自动续费
     */
    @NotNull(message = "自动续费设置不能为空")
    private Boolean autoRenew;
    
    /**
     * 支付渠道（WECHAT-微信, ALIPAY-支付宝）
     */
    @NotBlank(message = "支付渠道不能为空")
    private String channel;
} 