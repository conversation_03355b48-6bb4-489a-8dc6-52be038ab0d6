package com.stylishlink.payment.dto.request;

import lombok.Data;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;

/**
 * 创建支付订单请求
 */
@Data
public class CreatePaymentOrderRequest {
    
    /**
     * 用户ID
     */
    @NotBlank(message = "用户ID不能为空")
    private String userId;
    
    /**
     * 源订单ID
     */
    @NotBlank(message = "源订单ID不能为空")
    private String originOrderId;
    
    /**
     * 订单类型（RECHARGE-充值, SUBSCRIPTION-订阅）
     */
    @NotBlank(message = "订单类型不能为空")
    private String type;
    
    /**
     * 支付渠道（WECHAT-微信, ALIPAY-支付宝）
     */
    @NotBlank(message = "支付渠道不能为空")
    private String channel;
    
    /**
     * 支付金额(分)
     */
    @NotNull(message = "支付金额不能为空")
    @Positive(message = "支付金额必须大于0")
    private Long amount;
    
    /**
     * 商品名称
     */
    @NotBlank(message = "商品名称不能为空")
    private String subject;
    
    /**
     * 商品描述
     */
    private String description;
    
    /**
     * 客户端IP
     */
    private String clientIp;
    
    /**
     * 通知地址
     */
    private String notifyUrl;
    
    /**
     * 返回地址
     */
    private String returnUrl;
} 