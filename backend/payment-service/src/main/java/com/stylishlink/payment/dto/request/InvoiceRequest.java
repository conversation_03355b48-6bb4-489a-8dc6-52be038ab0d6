package com.stylishlink.payment.dto.request;

import lombok.Data;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import jakarta.validation.constraints.Email;

/**
 * 发票申请请求
 */
@Data
public class InvoiceRequest {
    
    /**
     * 用户ID
     */
    @NotBlank(message = "用户ID不能为空")
    private String userId;
    
    /**
     * 订单ID
     */
    @NotBlank(message = "订单ID不能为空")
    private String orderId;
    
    /**
     * 发票类型（PERSONAL-个人, COMPANY-企业）
     */
    @NotBlank(message = "发票类型不能为空")
    private String type;
    
    /**
     * 发票抬头
     */
    @NotBlank(message = "发票抬头不能为空")
    private String title;
    
    /**
     * 税号
     */
    private String taxNumber;
    
    /**
     * 接收邮箱
     */
    @NotBlank(message = "接收邮箱不能为空")
    @Email(message = "邮箱格式不正确")
    private String email;
    
    /**
     * 金额(分)
     */
    @NotNull(message = "金额不能为空")
    @Positive(message = "金额必须大于0")
    private Long amount;
} 