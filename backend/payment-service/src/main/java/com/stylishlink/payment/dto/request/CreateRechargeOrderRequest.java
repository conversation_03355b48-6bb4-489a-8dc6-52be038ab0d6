package com.stylishlink.payment.dto.request;

import lombok.Data;
import jakarta.validation.constraints.NotBlank;

/**
 * 创建充值订单请求
 */
@Data
public class CreateRechargeOrderRequest {
    
    /**
     * 用户ID
     */
    @NotBlank(message = "用户ID不能为空")
    private String userId;
    
    /**
     * 充值产品ID
     */
    @NotBlank(message = "产品ID不能为空")
    private String productId;
    
    /**
     * 支付渠道（WECHAT-微信, ALIPAY-支付宝）
     */
    @NotBlank(message = "支付渠道不能为空")
    private String channel;
} 