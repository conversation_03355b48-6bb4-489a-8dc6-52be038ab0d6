package com.stylishlink.payment.dto.response;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 支付订单响应
 */
@Data
public class PaymentOrderResponse {
    
    /**
     * 订单ID
     */
    private String orderId;
    
    /**
     * 支付状态（PENDING-待支付, PAID-已支付, EXPIRED-已过期, FAILED-失败）
     */
    private String status;
    
    /**
     * 支付金额(分)
     */
    private Long amount;
    
    /**
     * 支付URL
     */
    private String paymentUrl;
    
    /**
     * 二维码
     */
    private String qrCode;
    
    /**
     * 支付渠道
     */
    private String channel;
    
    /**
     * 第三方交易ID
     */
    private String transactionId;
    
    /**
     * 支付时间
     */
    private LocalDateTime paidAt;
    
    /**
     * 过期时间
     */
    private LocalDateTime expiredAt;
} 