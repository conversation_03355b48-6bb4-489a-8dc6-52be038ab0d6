package com.stylishlink.payment.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 充值产品实体
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("payment_recharge_product")
public class RechargeProduct implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 产品名称
     */
    @TableField("name")
    private String name;

    /**
     * 产品描述
     */
    @TableField("description")
    private String description;

    /**
     * 价格(分)
     */
    @TableField("price")
    private Long price;

    /**
     * 基础灵感值
     */
    @TableField("base_amount")
    private Integer baseAmount;

    /**
     * 赠送灵感值
     */
    @TableField("bonus_amount")
    private Integer bonusAmount;

    /**
     * 总灵感值
     */
    @TableField("total_amount")
    private Integer totalAmount;

    /**
     * 兑换比例(灵感值/元)
     */
    @TableField("exchange_rate")
    private BigDecimal exchangeRate;

    /**
     * 图标URL
     */
    @TableField("icon")
    private String icon;

    /**
     * 是否热门
     */
    @TableField("is_popular")
    private Boolean isPopular;

    /**
     * 是否可用
     */
    @TableField("is_active")
    private Boolean isActive;

    /**
     * 是否限时
     */
    @TableField("is_limited")
    private Boolean isLimited;

    /**
     * 排序顺序
     */
    @TableField("sort_order")
    private Integer sortOrder;

    /**
     * 是否删除（逻辑删除）
     */
    @TableLogic
    @TableField("deleted")
    private Integer deleted;

    /**
     * 有效期开始
     */
    @TableField("valid_from")
    private LocalDateTime validFrom;

    /**
     * 有效期结束
     */
    @TableField("valid_to")
    private LocalDateTime validTo;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
} 