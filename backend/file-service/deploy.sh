#!/bin/bash

# 配置参数
SSH_USER="root"
SSH_PASSWORD=$DEPLOY_SSH_PASSWORD #IDEA环境变量
SSH_HOST="*************"
LOCAL_JAR_PATH="target/*.jar"
REMOTE_DIR="/opt/apps/file-service"
REMOTE_JAR_PATH="$REMOTE_DIR/stylishlink-file-service-1.0.0-SNAPSHOT.jar"
STOP_SCRIPT="sh start.sh stop"
START_SCRIPT="sh start.sh start"
LOG_PATH="$REMOTE_DIR/logs/stylishlink-file-service.log"
BACKUP_DIR="$REMOTE_DIR/backups"

# 等待jar包编译完成
echo "Waiting for JAR file to be ready..."
while [ ! -f $LOCAL_JAR_PATH ]; do
  sleep 1
done

BACKUP_FILE="$BACKUP_DIR/stylishlink-file-service-1.0.0-SNAPSHOT.$(date +%Y%m%d_%H%M%S).bak"
# 执行部署
echo "Starting deployment..."
sshpass -p "$SSH_PASSWORD" ssh $SSH_USER@$SSH_HOST << EOF
  # 停止服务
  echo "Stopping service..."
  cd $REMOTE_DIR
  $STOP_SCRIPT

  # 备份旧jar包
  echo "Backing up old JAR..."
  mkdir -p $BACKUP_DIR

  mv "$REMOTE_JAR_PATH" "$BACKUP_FILE"

  # 上传新jar包
  echo "Uploading new JAR..."
EOF

# 使用scp上传文件
echo "Uploading new JAR and waiting for completion..."
sshpass -p "$SSH_PASSWORD" scp $LOCAL_JAR_PATH $SSH_USER@$SSH_HOST:$REMOTE_JAR_PATH
if [ $? -eq 0 ]; then
  echo "JAR file upload completed successfully."
else
  echo "JAR file upload failed!"
  exit 1
fi
# 继续执行远程命令
sshpass -p "$SSH_PASSWORD" ssh $SSH_USER@$SSH_HOST << EOF
  # 启动服务
  echo "Starting service..."
  cd $REMOTE_DIR
  $START_SCRIPT
  
  # 检查日志
  echo "Checking logs..."
  tail -f $LOG_PATH | while read line; do
    if echo "\$line" | grep -q "Started"; then
      pkill -P \$\$ tail
      echo "Service started successfully!"
      exit 0
    fi
  done
EOF

echo "Deployment completed!"