# StylishLink File Service

## 服务概述

文件存取服务（file-service）是StylishLink系统的核心基础服务，基于腾讯云COS提供统一的文件管理能力。

- **服务名称**: file-service
- **服务端口**: 8088  
- **API版本**: v1
- **认证方式**: JWT Token

## 功能特性

### 核心功能
- 单文件上传
- 批量文件上传
- 分片上传（大文件）
- 文件下载
- 文件信息管理
- 文件列表查询
- 文件删除（逻辑/物理）

### 存储支持
- 腾讯云COS
- 阿里云OSS
- 本地存储

### 文件处理
- 图片缩略图生成
- 图片水印添加
- 文件类型检测
- 文件完整性校验

### 安全特性
- 访问权限控制
- 文件URL签名
- 用户配额管理
- 文件格式限制

## 技术架构

### 技术栈
- **框架**: Spring Boot 3.x
- **数据库**: MySQL 8.0
- **缓存**: Redis
- **ORM**: MyBatis Plus
- **服务发现**: Nacos
- **文件存储**: 腾讯云COS/阿里云OSS

## 快速开始

### 环境要求
- JDK 17+
- MySQL 8.0+
- Redis 6.0+
- Nacos 2.0+

### 启动服务
```bash
# 编译项目
mvn clean compile

# 启动服务
mvn spring-boot:run
```

## API接口

### 文件上传
```http
POST /api/v1/files/upload
Content-Type: multipart/form-data

Parameters:
- file: 上传的文件
- userId: 用户ID
- category: 文件分类
- accessLevel: 访问级别（可选）
```

### 获取文件信息
```http
GET /api/v1/files/{fileId}
```

### 获取文件列表
```http
GET /api/v1/files?userId={userId}&page=1&size=20
```

### 删除文件
```http
DELETE /api/v1/files/{fileId}?force=false
```

### 获取下载链接
```http
GET /api/v1/files/{fileId}/download-url?expiresIn=3600
```

## 数据模型

### 文件分类
- USER_AVATAR: 用户头像
- USER_PHOTO: 用户照片
- CLOTHING_IMAGE: 衣物图片
- ACCESSORY_IMAGE: 饰品图片
- OUTFIT_IMAGE: 搭配图片
- AI_GENERATED_VIDEO: AI生成视频
- AI_GENERATED_IMAGE: AI生成图片
- SYSTEM_ICON: 系统图标
- SYSTEM_BACKGROUND: 系统背景
- TEMP_FILE: 临时文件

### 访问级别
- PUBLIC: 公开访问
- PRIVATE: 私有访问
- PROTECTED: 受保护访问
- INTERNAL: 内部访问

### 文件状态
- UPLOADING: 上传中
- UPLOADED: 已上传
- PROCESSING: 处理中
- PROCESSED: 已处理
- AVAILABLE: 可用
- DELETED: 已删除
- EXPIRED: 已过期

## 错误码

| 错误码 | 含义 | 说明 |
|--------|------|------|
| 10001 | 文件不存在 | 文件记录未找到 |
| 10002 | 文件上传失败 | 文件上传过程异常 |
| 10003 | 文件下载失败 | 文件下载过程异常 |
| 10004 | 文件格式不支持 | 不支持的文件格式 |
| 10005 | 文件大小超限 | 文件大小超过限制 |
| 10006 | 存储配额不足 | 用户存储配额不足 |
| 10007 | 文件处理失败 | 图片处理或转码失败 |
| 10008 | 分片上传失败 | 分片上传过程异常 |
| 10009 | 文件校验失败 | 文件完整性校验失败 |
| 10010 | 存储服务异常 | 腾讯云COS服务异常 |

## 注意事项

1. **文件大小限制**: 单文件最大100MB，分片上传支持更大文件
2. **支持格式**: 图片(jpg,jpeg,png,gif,webp)，视频(mp4,avi,mov)，文档(pdf,doc,docx)
3. **安全性**: 所有文件操作需要JWT认证，敏感文件需要权限验证
4. **配额限制**: 每用户默认1GB存储配额，超出需要购买扩容
5. **CDN缓存**: 公开文件自动CDN缓存，私有文件通过临时URL访问
6. **数据合规**: 严格遵循数据保护法规，支持数据导出和删除

## 联系方式

如有问题请联系开发团队或提交Issue。 