-- File Service Database Schema

-- 文件信息表
CREATE TABLE `file_info` (
    `id` varchar(32) NOT NULL COMMENT '文件ID',
    `user_id` varchar(32) NOT NULL COMMENT '用户ID',
    `original_name` varchar(255) NOT NULL COMMENT '原始文件名',
    `storage_name` varchar(255) NOT NULL COMMENT '存储文件名',
    `bucket_name` varchar(128) DEFAULT NULL COMMENT '存储桶名称',
    `object_key` varchar(512) NOT NULL COMMENT '对象键',
    `category` varchar(32) NOT NULL COMMENT '文件分类',
    `file_type` varchar(32) NOT NULL COMMENT '文件类型',
    `mime_type` varchar(128) NOT NULL COMMENT 'MIME类型',
    `file_size` bigint NOT NULL COMMENT '文件大小',
    `md5_hash` varchar(32) NOT NULL COMMENT 'MD5哈希',
    `sha256_hash` varchar(64) DEFAULT NULL COMMENT 'SHA256哈希',
    `status` varchar(32) NOT NULL COMMENT '文件状态',
    `access_level` varchar(32) NOT NULL COMMENT '访问级别',
    `url` varchar(512) NOT NULL COMMENT '文件URL',
    `thumbnail_url` varchar(512) DEFAULT NULL COMMENT '缩略图URL',
    `etag` varchar(128) DEFAULT NULL COMMENT 'ETag',
    `metadata` json DEFAULT NULL COMMENT '扩展元数据',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `expires_at` datetime DEFAULT NULL COMMENT '过期时间',
    `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除标记',
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_category` (`category`),
    KEY `idx_file_type` (`file_type`),
    KEY `idx_status` (`status`),
    KEY `idx_created_at` (`created_at`),
    KEY `idx_md5_hash` (`md5_hash`),
    KEY `idx_deleted` (`deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文件信息表';

-- 分片上传表
CREATE TABLE `file_multipart_upload` (
    `upload_id` varchar(64) NOT NULL COMMENT '上传ID',
    `file_id` varchar(32) NOT NULL COMMENT '文件ID',
    `user_id` varchar(32) NOT NULL COMMENT '用户ID',
    `file_name` varchar(255) NOT NULL COMMENT '文件名',
    `file_size` bigint NOT NULL COMMENT '文件大小',
    `chunk_size` int NOT NULL COMMENT '分片大小',
    `total_chunks` int NOT NULL COMMENT '总分片数',
    `uploaded_chunks` json DEFAULT NULL COMMENT '已上传分片列表',
    `status` varchar(32) NOT NULL COMMENT '上传状态',
    `bucket` varchar(128) NOT NULL COMMENT '存储桶',
    `object_key` varchar(512) NOT NULL COMMENT '对象键',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `expires_at` datetime NOT NULL COMMENT '过期时间',
    `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除标记',
    PRIMARY KEY (`upload_id`),
    KEY `idx_file_id` (`file_id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_status` (`status`),
    KEY `idx_created_at` (`created_at`),
    KEY `idx_expires_at` (`expires_at`),
    KEY `idx_deleted` (`deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分片上传表';

-- 文件访问日志表（可选，用于统计和审计）
CREATE TABLE `file_access_log` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '日志ID',
    `file_id` varchar(32) NOT NULL COMMENT '文件ID',
    `user_id` varchar(32) DEFAULT NULL COMMENT '访问用户ID',
    `access_type` varchar(32) NOT NULL COMMENT '访问类型：UPLOAD,DOWNLOAD,DELETE,VIEW',
    `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
    `user_agent` varchar(512) DEFAULT NULL COMMENT '用户代理',
    `referer` varchar(512) DEFAULT NULL COMMENT '来源页面',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '访问时间',
    PRIMARY KEY (`id`),
    KEY `idx_file_id` (`file_id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_access_type` (`access_type`),
    KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文件访问日志表';

-- 文件标签表（可选，用于文件分类和搜索）
CREATE TABLE `file_tag` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '标签ID',
    `file_id` varchar(32) NOT NULL COMMENT '文件ID',
    `tag_name` varchar(64) NOT NULL COMMENT '标签名称',
    `tag_value` varchar(255) DEFAULT NULL COMMENT '标签值',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_file_tag` (`file_id`, `tag_name`),
    KEY `idx_tag_name` (`tag_name`),
    KEY `idx_tag_value` (`tag_value`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文件标签表';

-- 用户存储配额表（可选，用于配额管理）
CREATE TABLE `file_user_quota` (
    `user_id` varchar(32) NOT NULL COMMENT '用户ID',
    `used_space` bigint NOT NULL DEFAULT '0' COMMENT '已使用空间（字节）',
    `total_space` bigint NOT NULL DEFAULT '1073741824' COMMENT '总空间（字节，默认1GB）',
    `file_count` int NOT NULL DEFAULT '0' COMMENT '文件数量',
    `max_file_count` int NOT NULL DEFAULT '10000' COMMENT '最大文件数量',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户存储配额表'; 