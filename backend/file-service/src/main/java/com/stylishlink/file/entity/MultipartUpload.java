package com.stylishlink.file.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.stylishlink.file.entity.enums.MultipartUploadStatus;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 分片上传实体
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("multipart_upload")
public class MultipartUpload {

    /**
     * 上传ID
     */
    @TableId(type = IdType.INPUT)
    private String uploadId;

    /**
     * 文件ID
     */
    private String fileId;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 文件大小
     */
    private Long fileSize;

    /**
     * 分片大小
     */
    private Integer chunkSize;

    /**
     * 总分片数
     */
    private Integer totalChunks;

    /**
     * 已上传分片列表
     */
    private List<Integer> uploadedChunks;

    /**
     * 上传状态
     */
    private MultipartUploadStatus status;

    /**
     * 存储桶
     */
    private String bucket;

    /**
     * 对象键
     */
    private String objectKey;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 过期时间
     */
    private LocalDateTime expiresAt;

    /**
     * 逻辑删除标记
     */
    private Boolean deleted;
} 