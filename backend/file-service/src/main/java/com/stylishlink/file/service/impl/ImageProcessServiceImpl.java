package com.stylishlink.file.service.impl;

import com.stylishlink.file.exception.FileServiceException;
import com.stylishlink.file.service.ImageProcessService;
import lombok.extern.slf4j.Slf4j;
import net.coobird.thumbnailator.Thumbnails;
import net.coobird.thumbnailator.geometry.Positions;
import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;

/**
 * 图片处理服务实现类
 */
@Slf4j
@Service
public class ImageProcessServiceImpl implements ImageProcessService {

    @Override
    public byte[] generateThumbnail(InputStream inputStream, int width, int height, double quality) {
        try {
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            
            Thumbnails.of(inputStream)
                    .size(width, height)
                    .outputQuality(quality)
                    .outputFormat("jpg")
                    .toOutputStream(outputStream);
            
            return outputStream.toByteArray();
            
        } catch (IOException e) {
            log.error("生成缩略图失败", e);
            throw new FileServiceException(10007, "图片处理失败: " + e.getMessage());
        }
    }

    @Override
    public byte[] addWatermark(InputStream inputStream, String watermarkText, 
                             int fontSize, String color, double opacity, String position) {
        try {
            BufferedImage originalImage = ImageIO.read(inputStream);
            if (originalImage == null) {
                throw new FileServiceException(10007, "无法读取图片文件");
            }

            // 创建水印图片
            BufferedImage watermarkImage = createWatermarkImage(
                watermarkText, fontSize, color, opacity
            );

            // 确定水印位置
            Positions watermarkPosition = parsePosition(position);

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            
            Thumbnails.of(originalImage)
                    .watermark(watermarkPosition, watermarkImage, (float) opacity)
                    .scale(1.0)
                    .outputFormat("jpg")
                    .toOutputStream(outputStream);
            
            return outputStream.toByteArray();
            
        } catch (IOException e) {
            log.error("添加水印失败", e);
            throw new FileServiceException(10007, "图片处理失败: " + e.getMessage());
        }
    }

    @Override
    public byte[] compressImage(InputStream inputStream, double quality, int maxWidth, int maxHeight) {
        try {
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            
            Thumbnails.of(inputStream)
                    .size(maxWidth, maxHeight)
                    .outputQuality(quality)
                    .outputFormat("jpg")
                    .toOutputStream(outputStream);
            
            return outputStream.toByteArray();
            
        } catch (IOException e) {
            log.error("压缩图片失败", e);
            throw new FileServiceException(10007, "图片处理失败: " + e.getMessage());
        }
    }

    @Override
    public ImageInfo getImageInfo(InputStream inputStream) {
        try {
            BufferedImage image = ImageIO.read(inputStream);
            if (image == null) {
                throw new FileServiceException(10007, "无法读取图片文件");
            }

            ImageInfo imageInfo = new ImageInfo();
            imageInfo.setWidth(image.getWidth());
            imageInfo.setHeight(image.getHeight());
            imageInfo.setFormat("UNKNOWN"); // 可以通过其他方式获取格式信息
            
            return imageInfo;
            
        } catch (IOException e) {
            log.error("获取图片信息失败", e);
            throw new FileServiceException(10007, "图片处理失败: " + e.getMessage());
        }
    }

    /**
     * 创建水印图片
     */
    private BufferedImage createWatermarkImage(String text, int fontSize, String color, double opacity) {
        // 创建字体
        Font font = new Font("Microsoft YaHei", Font.BOLD, fontSize);
        
        // 创建临时图片来计算文字尺寸
        BufferedImage tempImage = new BufferedImage(1, 1, BufferedImage.TYPE_INT_ARGB);
        Graphics2D tempGraphics = tempImage.createGraphics();
        tempGraphics.setFont(font);
        FontMetrics fontMetrics = tempGraphics.getFontMetrics();
        int textWidth = fontMetrics.stringWidth(text);
        int textHeight = fontMetrics.getHeight();
        tempGraphics.dispose();

        // 创建水印图片
        BufferedImage watermarkImage = new BufferedImage(
            textWidth + 20, textHeight + 20, BufferedImage.TYPE_INT_ARGB
        );
        
        Graphics2D graphics = watermarkImage.createGraphics();
        graphics.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        
        // 设置颜色和透明度
        Color textColor = parseColor(color);
        graphics.setColor(new Color(
            textColor.getRed(), 
            textColor.getGreen(), 
            textColor.getBlue(), 
            (int) (255 * opacity)
        ));
        
        graphics.setFont(font);
        graphics.drawString(text, 10, textHeight);
        graphics.dispose();

        return watermarkImage;
    }

    /**
     * 解析位置
     */
    private Positions parsePosition(String position) {
        switch (position.toUpperCase()) {
            case "TOP_LEFT": return Positions.TOP_LEFT;
            case "TOP_CENTER": return Positions.TOP_CENTER;
            case "TOP_RIGHT": return Positions.TOP_RIGHT;
            case "CENTER_LEFT": return Positions.CENTER_LEFT;
            case "CENTER": return Positions.CENTER;
            case "CENTER_RIGHT": return Positions.CENTER_RIGHT;
            case "BOTTOM_LEFT": return Positions.BOTTOM_LEFT;
            case "BOTTOM_CENTER": return Positions.BOTTOM_CENTER;
            case "BOTTOM_RIGHT": return Positions.BOTTOM_RIGHT;
            default: return Positions.BOTTOM_RIGHT;
        }
    }

    /**
     * 解析颜色
     */
    private Color parseColor(String colorStr) {
        try {
            if (colorStr.startsWith("#")) {
                return Color.decode(colorStr);
            } else {
                switch (colorStr.toUpperCase()) {
                    case "WHITE": return Color.WHITE;
                    case "BLACK": return Color.BLACK;
                    case "RED": return Color.RED;
                    case "GREEN": return Color.GREEN;
                    case "BLUE": return Color.BLUE;
                    default: return Color.WHITE;
                }
            }
        } catch (Exception e) {
            log.warn("解析颜色失败，使用默认白色: {}", colorStr);
            return Color.WHITE;
        }
    }
} 