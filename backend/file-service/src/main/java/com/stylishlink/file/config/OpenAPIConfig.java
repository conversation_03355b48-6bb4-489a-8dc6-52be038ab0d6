package com.stylishlink.file.config;

import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.servers.Server;
import io.swagger.v3.oas.models.tags.Tag;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * 文件服务OpenAPI配置
 */
@Configuration
public class OpenAPIConfig {

    @Value("${server.port:8088}")
    private String serverPort;

    @Bean
    public OpenAPI fileServiceOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("文件服务API")
                        .description("StylishLink文件服务API文档，提供文件上传、下载、管理、图片处理等功能")
                        .version("1.0.0")
                        .contact(new Contact()
                                .name("StylishLink团队")
                                .email("<EMAIL>")
                                .url("https://stylishlink.com"))
                        .license(new License()
                                .name("MIT")
                                .url("https://opensource.org/licenses/MIT")))
                .servers(List.of(
                        new Server()
                                .url("http://localhost:" + serverPort)
                                .description("本地开发环境"),
                        new Server()
                                .url("https://api.stylishlink.com")
                                .description("生产环境")
                ))
                .addSecurityItem(new SecurityRequirement()
                        .addList("Bearer Authentication"))
                .components(new Components()
                        .addSecuritySchemes("Bearer Authentication", 
                                new SecurityScheme()
                                        .type(SecurityScheme.Type.HTTP)
                                        .scheme("bearer")
                                        .bearerFormat("JWT")
                                        .description("请在请求头中添加 Authorization: Bearer {token}")))
                .tags(List.of(
                        new Tag()
                                .name("文件上传")
                                .description("文件上传、批量上传、断点续传等接口"),
                        new Tag()
                                .name("文件管理")
                                .description("文件查询、删除、移动、重命名等接口"),
                        new Tag()
                                .name("图片处理")
                                .description("图片压缩、缩放、裁剪、水印等接口"),
                        new Tag()
                                .name("文件下载")
                                .description("文件下载、预览、临时访问等接口"),
                        new Tag()
                                .name("存储管理")
                                .description("存储配置、容量统计、清理等接口")
                ));
    }
} 