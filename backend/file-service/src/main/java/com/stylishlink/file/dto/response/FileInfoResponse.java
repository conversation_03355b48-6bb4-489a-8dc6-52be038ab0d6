package com.stylishlink.file.dto.response;

import com.stylishlink.file.entity.enums.AccessLevel;
import com.stylishlink.file.entity.enums.FileCategory;
import com.stylishlink.file.entity.enums.FileStatus;
import com.stylishlink.file.entity.enums.FileType;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 文件信息响应DTO
 */
@Data
public class FileInfoResponse {

    /**
     * 文件ID
     */
    private String id;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 原始文件名
     */
    private String originalName;

    /**
     * 存储文件名
     */
    private String storageName;

    /**
     * 存储桶名称
     */
    private String bucketName;

    /**
     * 对象键
     */
    private String objectKey;

    /**
     * 文件分类
     */
    private FileCategory category;

    /**
     * 文件类型
     */
    private FileType fileType;

    /**
     * MIME类型
     */
    private String mimeType;

    /**
     * 文件大小
     */
    private Long fileSize;

    /**
     * MD5哈希
     */
    private String md5Hash;

    /**
     * 文件状态
     */
    private FileStatus status;

    /**
     * 访问级别
     */
    private AccessLevel accessLevel;

    /**
     * 文件URL
     */
    private String url;

    /**
     * 缩略图URL
     */
    private String thumbnailUrl;

    /**
     * 扩展元数据
     */
    private Map<String, Object> metadata;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
} 