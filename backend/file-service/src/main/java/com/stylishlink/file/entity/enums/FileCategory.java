package com.stylishlink.file.entity.enums;

import lombok.Getter;

/**
 * 文件分类枚举
 */
@Getter
public enum FileCategory {
    USER_AVATAR("用户头像"),
    USER_PHOTO("用户照片"),
    CLOTHING_IMAGE("衣物图片"),
    ACCESSORY_IMAGE("饰品图片"),
    OUTFIT_IMAGE("搭配图片"),
    AI_GENERATED_VIDEO("AI生成视频"),
    AI_GENERATED_IMAGE("AI生成图片"),
    SYSTEM_ICON("系统图标"),
    SYSTEM_BACKGROUND("系统背景"),
    TEMP_FILE("临时文件");

    private final String description;

    FileCategory(String description) {
        this.description = description;
    }
} 