package com.stylishlink.file.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 文件服务配置类
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "file")
public class FileConfig {

    /**
     * 存储配置
     */
    private Storage storage = new Storage();

    /**
     * 文件限制配置
     */
    private Limits limits = new Limits();

    /**
     * 图片处理配置
     */
    private Image image = new Image();

    /**
     * 安全配置
     */
    private Security security = new Security();

    @Data
    public static class Storage {
        private String type = "cos";
        private Local local = new Local();
        private Cos cos = new Cos();
        private Oss oss = new Oss();
    }

    @Data
    public static class Local {
        private String path = "/data/files";
        private String urlPrefix = "http://localhost:8088/files";
    }

    @Data
    public static class Cos {
        private String region = "ap-guangzhou";
        private String secretId;
        private String secretKey;
        private String bucketName;
        private String cdnDomain;
        private String urlPrefix;
    }

    @Data
    public static class Oss {
        private String endpoint;
        private String accessKeyId;
        private String accessKeySecret;
        private String bucketName;
        private String cdnDomain;
    }

    @Data
    public static class Limits {
        private String maxFileSize = "100MB";
        private String maxUserQuota = "1GB";
        private String imageFormats = "jpg,jpeg,png,gif,webp,bmp";
        private String videoFormats = "mp4,avi,mov,wmv,flv,mkv";
        private String documentFormats = "pdf,doc,docx,xls,xlsx,ppt,pptx,txt";
    }

    @Data
    public static class Image {
        private Thumbnail thumbnail = new Thumbnail();
        private Watermark watermark = new Watermark();
    }

    @Data
    public static class Thumbnail {
        private int width = 200;
        private int height = 200;
        private double quality = 0.8;
    }

    @Data
    public static class Watermark {
        private boolean enabled = true;
        private String text = "StylishLink";
        private int fontSize = 24;
        private String color = "#FFFFFF";
        private double opacity = 0.7;
        private String position = "BOTTOM_RIGHT";
    }

    @Data
    public static class Security {
        private String allowedOrigins = "*";
        private int urlExpires = 3600;
        private AccessControl accessControl = new AccessControl();
    }

    @Data
    public static class AccessControl {
        private boolean enabled = true;
        private String defaultLevel = "PRIVATE";
    }
} 