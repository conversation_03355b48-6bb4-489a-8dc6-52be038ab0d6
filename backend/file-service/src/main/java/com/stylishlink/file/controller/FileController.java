package com.stylishlink.file.controller;

import com.stylishlink.common.dto.ApiResponse;
import com.stylishlink.common.exception.BusinessException;
import com.stylishlink.file.dto.request.UploadFileRequest;
import com.stylishlink.file.dto.response.FileInfoResponse;
import com.stylishlink.file.dto.response.FileUploadResponse;
import com.stylishlink.file.entity.enums.AccessLevel;
import com.stylishlink.file.entity.enums.FileCategory;
import com.stylishlink.file.service.FileService;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 文件服务控制器
 */
@Slf4j
@RestController
@RequestMapping("/files")
public class FileController {

    @Autowired
    private FileService fileService;

    /**
     * 单文件上传
     */
    @PostMapping("/upload")
    public ApiResponse<FileUploadResponse> uploadFile(
            @RequestParam("file") MultipartFile file,
            @RequestParam("category") FileCategory category,
            @RequestParam(value = "accessLevel", defaultValue = "PRIVATE") AccessLevel accessLevel) {
        try {
            String userId = getCurrentUserId();
            log.info("用户 {} 上传文件，类别: {}, 权限: {}", userId, category, accessLevel);
            
            UploadFileRequest request = new UploadFileRequest();
            request.setUserId(userId);
            request.setCategory(category);
            // 如果是system用户，自动设为公共访问
            request.setAccessLevel("system".equals(userId) ? AccessLevel.PUBLIC : accessLevel);
            
            FileUploadResponse response = fileService.uploadFile(file, request);
            return ApiResponse.success("文件上传成功", response);
        } catch (Exception e) {
            log.error("文件上传失败", e);
            return ApiResponse.error(10002, "文件上传失败: " + e.getMessage());
        }
    }

    /**
     * 批量文件上传
     */
    @PostMapping("/upload-batch")
    public ApiResponse<List<FileUploadResponse>> uploadFiles(
            @RequestParam("files") List<MultipartFile> files,
            @RequestParam("category") FileCategory category,
            @RequestParam(value = "accessLevel", defaultValue = "PRIVATE") AccessLevel accessLevel) {
        try {
            String userId = getCurrentUserId();
            log.info("用户 {} 批量上传文件，类别: {}, 权限: {}", userId, category, accessLevel);
            
            UploadFileRequest request = new UploadFileRequest();
            request.setUserId(userId);
            request.setCategory(category);
            // 如果是system用户，自动设为公共访问
            request.setAccessLevel("system".equals(userId) ? AccessLevel.PUBLIC : accessLevel);
            
            List<FileUploadResponse> responses = fileService.uploadFiles(files, request);
            return ApiResponse.success("批量上传完成", responses);
        } catch (Exception e) {
            log.error("批量文件上传失败", e);
            return ApiResponse.error(10002, "批量文件上传失败: " + e.getMessage());
        }
    }

    /**
     * 获取文件信息
     */
    @GetMapping("/{fileId}")
    public ApiResponse<FileInfoResponse> getFileInfo(@PathVariable String fileId) {
        try {
            String userId = getCurrentUserId();
            log.info("用户 {} 获取文件信息: {}", userId, fileId);
            
            FileInfoResponse response = fileService.getFileInfo(fileId);
            
            // 权限检查：system用户可以访问所有文件，普通用户只能访问自己的文件或公共文件
            if (!"system".equals(userId)) {
                if (!userId.equals(response.getUserId()) && response.getAccessLevel() != AccessLevel.PUBLIC) {
                    return ApiResponse.error(10403, "无权访问该文件");
                }
            }
            
            return ApiResponse.success("获取文件信息成功", response);
        } catch (Exception e) {
            log.error("获取文件信息失败", e);
            return ApiResponse.error(10001, "获取文件信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取文件列表
     */
    @GetMapping
    public ApiResponse<List<FileInfoResponse>> getFileList(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size) {
        try {
            String userId = getCurrentUserId();
            log.info("用户 {} 获取文件列表，页码: {}, 大小: {}", userId, page, size);
            
            List<FileInfoResponse> responses = fileService.getFileList(userId, page, size);
            return ApiResponse.success("获取文件列表成功", responses);
        } catch (Exception e) {
            log.error("获取文件列表失败", e);
            return ApiResponse.error(10001, "获取文件列表失败: " + e.getMessage());
        }
    }

    /**
     * 删除文件
     */
    @DeleteMapping("/{fileId}")
    public ApiResponse<Void> deleteFile(
            @PathVariable String fileId,
            @RequestParam(defaultValue = "false") boolean force) {
        try {
            String userId = getCurrentUserId();
            log.info("用户 {} 删除文件: {}, 强制删除: {}", userId, fileId, force);
            
            // 权限检查：只有文件所有者或system用户可以删除文件
            FileInfoResponse fileInfo = fileService.getFileInfo(fileId);
            if (!"system".equals(userId) && !userId.equals(fileInfo.getUserId())) {
                return ApiResponse.error(10403, "无权删除该文件");
            }
            
            fileService.deleteFile(fileId, force);
            return ApiResponse.success("删除文件成功", null);
        } catch (Exception e) {
            log.error("删除文件失败", e);
            return ApiResponse.error(10001, "删除文件失败: " + e.getMessage());
        }
    }

    /**
     * 获取下载链接
     */
    @GetMapping("/{fileId}/download-url")
    public ApiResponse<String> getDownloadUrl(
            @PathVariable String fileId,
            @RequestParam(defaultValue = "3600") int expiresIn) {
        try {
            String userId = getCurrentUserId();
            log.info("用户 {} 获取文件下载链接: {}", userId, fileId);
            
            // 权限检查：system用户可以访问所有文件，普通用户只能访问自己的文件或公共文件
            FileInfoResponse fileInfo = fileService.getFileInfo(fileId);
            if (!"system".equals(userId)) {
                if (!userId.equals(fileInfo.getUserId()) && fileInfo.getAccessLevel() != AccessLevel.PUBLIC) {
                    return ApiResponse.error(10403, "无权访问该文件");
                }
            }
            
            String downloadUrl = fileService.getDownloadUrl(fileId, expiresIn);
            return ApiResponse.success("获取下载链接成功", downloadUrl);
        } catch (Exception e) {
            log.error("获取下载链接失败", e);
            return ApiResponse.error(10003, "获取下载链接失败: " + e.getMessage());
        }
    }

    /**
     * 获取当前用户ID（增强版）
     * 支持多种获取方式：
     * 1. SecurityContext中的Authentication（JWT认证后设置）
     * 2. 网关传递的头部信息（X-Authenticated-User-Id）
     * 3. 直接从Authorization头部解析JWT
     * 
     * 特殊处理：
     * - 如果返回"system"，表示系统用户，拥有所有文件的访问权限
     * - 普通用户只能访问自己的私有文件和所有公共文件
     */
    private String getCurrentUserId() {
        // 方式1: 从SecurityContext获取（过滤器设置的）
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null &&
                authentication.isAuthenticated() &&
                !"anonymousUser".equals(authentication.getName())) {
            log.debug("从SecurityContext获取用户ID: {}", authentication.getName());
            return authentication.getName();
        }

        // 方式2: 从网关传递的头部获取
        try {
            HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes()).getRequest();
            String gatewayUserId = request.getHeader("X-Authenticated-User-Id");
            if (StringUtils.hasText(gatewayUserId)) {
                log.debug("从网关头部获取用户ID: {}", gatewayUserId);
                return gatewayUserId;
            }

//            // 方式3: 直接从Authorization头部解析JWT（备用方案）
//            String authHeader = request.getHeader("Authorization");
//            if (StringUtils.hasText(authHeader) && authHeader.startsWith("Bearer ")) {
//                String token = authHeader.substring(7);
//                if (jwtService.validateToken(token)) {
//                    String userId = jwtService.extractUserId(token);
//                    log.debug("从JWT token获取用户ID: {}", userId);
//                    return userId;
//                }
//            }
            
            // 如果都获取不到，返回system表示系统用户（用于调试或系统调用）
            log.warn("无法获取用户ID，返回system用户");
            return "system";
        } catch (Exception e) {
            log.warn("获取用户ID时发生异常: {}", e.getMessage());
        }

        // 如果都获取不到，抛出异常
        throw new BusinessException("1001", "无法获取当前用户信息，请先登录");
    }
} 