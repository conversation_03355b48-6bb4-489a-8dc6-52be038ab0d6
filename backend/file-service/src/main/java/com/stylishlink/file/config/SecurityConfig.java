package com.stylishlink.file.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.SecurityFilterChain;

/**
 * 文件服务安全配置
 */
@Configuration
@EnableWebSecurity
public class SecurityConfig {

    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        http
            // 禁用CSRF
            .csrf(AbstractHttpConfigurer::disable)
            
            // 配置会话管理为无状态
            .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
            
            // 配置请求授权
            .authorizeHttpRequests(authz -> authz
                // SpringDoc相关路径允许访问
                .requestMatchers(
                    "/v3/api-docs/**",
                    "/swagger-ui/**",
                    "/swagger-ui.html",
                    "/swagger-config",
                    "/webjars/**"
                ).permitAll()
                
                // Actuator健康检查端点
                .requestMatchers("/actuator/health").permitAll()
                
                // 文件服务相关接口暂时允许所有访问，用于调试403问题
                .requestMatchers("/files/**").permitAll()
                
                // 其他请求需要认证 (如果项目中还存在其他不由/files/**覆盖的受保护端点)
                // 如果所有业务接口都在/files/**下，可以考虑移除下面这行或注释掉
                .anyRequest().authenticated()
            )
            
            // 禁用HTTP Basic认证
            .httpBasic(AbstractHttpConfigurer::disable)
            
            // 禁用表单登录
            .formLogin(AbstractHttpConfigurer::disable);

        return http.build();
    }
} 