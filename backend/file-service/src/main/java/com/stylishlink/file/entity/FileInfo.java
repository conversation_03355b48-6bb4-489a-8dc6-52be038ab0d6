package com.stylishlink.file.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.stylishlink.file.entity.enums.AccessLevel;
import com.stylishlink.file.entity.enums.FileCategory;
import com.stylishlink.file.entity.enums.FileStatus;
import com.stylishlink.file.entity.enums.FileType;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 文件信息实体
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("file_info")
public class FileInfo {

    /**
     * 文件ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 原始文件名
     */
    private String originalName;

    /**
     * 存储文件名
     */
    private String storageName;

    /**
     * 存储桶名称
     */
    private String bucketName;

    /**
     * 对象键
     */
    private String objectKey;

    /**
     * 文件分类
     */
    private FileCategory category;

    /**
     * 文件类型
     */
    private FileType fileType;

    /**
     * MIME类型
     */
    private String mimeType;

    /**
     * 文件大小
     */
    private Long fileSize;

    /**
     * MD5哈希
     */
    private String md5Hash;

    /**
     * SHA256哈希
     */
    private String sha256Hash;

    /**
     * 文件状态
     */
    private FileStatus status;

    /**
     * 访问级别
     */
    private AccessLevel accessLevel;

    /**
     * 文件URL
     */
    private String url;

    /**
     * 缩略图URL
     */
    private String thumbnailUrl;

    /**
     * ETag
     */
    private String etag;

    /**
     * 扩展元数据
     */
    private Map<String, Object> metadata;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 过期时间
     */
    private LocalDateTime expiresAt;

    /**
     * 逻辑删除标记
     */
    @TableLogic
    private Boolean deleted;
} 