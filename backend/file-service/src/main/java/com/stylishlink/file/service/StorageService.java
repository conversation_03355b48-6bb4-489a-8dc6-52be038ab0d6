package com.stylishlink.file.service;

import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;

/**
 * 存储服务接口
 */
public interface StorageService {

    /**
     * 上传文件
     *
     * @param file       文件
     * @param bucketName 存储桶名称
     * @param objectKey  对象键
     * @return 文件访问URL
     */
    String uploadFile(MultipartFile file, String bucketName, String objectKey);

    /**
     * 上传文件流
     *
     * @param inputStream 文件流
     * @param bucketName  存储桶名称
     * @param objectKey   对象键
     * @param contentType 内容类型
     * @param contentLength 内容长度
     * @return 文件访问URL
     */
    String uploadFile(InputStream inputStream, String bucketName, String objectKey, 
                     String contentType, long contentLength);

    /**
     * 删除文件
     *
     * @param bucketName 存储桶名称
     * @param objectKey  对象键
     */
    void deleteFile(String bucketName, String objectKey);

    /**
     * 获取文件下载URL
     *
     * @param bucketName 存储桶名称
     * @param objectKey  对象键
     * @param expiresIn  过期时间（秒）
     * @return 下载URL
     */
    String getDownloadUrl(String bucketName, String objectKey, int expiresIn);

    /**
     * 检查文件是否存在
     *
     * @param bucketName 存储桶名称
     * @param objectKey  对象键
     * @return 是否存在
     */
    boolean fileExists(String bucketName, String objectKey);

    /**
     * 获取文件大小
     *
     * @param bucketName 存储桶名称
     * @param objectKey  对象键
     * @return 文件大小
     */
    long getFileSize(String bucketName, String objectKey);
} 