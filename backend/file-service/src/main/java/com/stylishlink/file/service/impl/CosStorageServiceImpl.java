package com.stylishlink.file.service.impl;

import com.qcloud.cos.COSClient;
import com.qcloud.cos.ClientConfig;
import com.qcloud.cos.auth.BasicCOSCredentials;
import com.qcloud.cos.auth.COSCredentials;
import com.qcloud.cos.exception.CosClientException;
import com.qcloud.cos.exception.CosServiceException;
import com.qcloud.cos.http.HttpMethodName;
import com.qcloud.cos.model.*;
import com.qcloud.cos.region.Region;
import com.stylishlink.file.config.FileConfig;
import com.stylishlink.file.exception.FileServiceException;
import com.stylishlink.file.service.StorageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.PostConstruct;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Date;

/**
 * 腾讯云COS存储服务实现
 */
@Slf4j
@Service
@ConditionalOnProperty(name = "file.storage.type", havingValue = "cos")
public class CosStorageServiceImpl implements StorageService {

    @Autowired
    private FileConfig fileConfig;

    private COSClient cosClient;

    @PostConstruct
    public void init() {
        try {
            FileConfig.Cos cosConfig = fileConfig.getStorage().getCos();
            
            if (!StringUtils.hasText(cosConfig.getSecretId()) || 
                !StringUtils.hasText(cosConfig.getSecretKey())) {
                log.warn("COS配置不完整，将使用模拟模式");
                return;
            }

            COSCredentials cred = new BasicCOSCredentials(
                cosConfig.getSecretId(), 
                cosConfig.getSecretKey()
            );
            
            Region region = new Region(cosConfig.getRegion());
            ClientConfig clientConfig = new ClientConfig(region);
            
            this.cosClient = new COSClient(cred, clientConfig);
            
            log.info("COS客户端初始化成功，区域: {}", cosConfig.getRegion());
        } catch (Exception e) {
            log.error("COS客户端初始化失败", e);
            throw new FileServiceException(10010, "存储服务初始化失败");
        }
    }

    @Override
    public String uploadFile(MultipartFile file, String bucketName, String objectKey) {
        try {
            if (cosClient == null) {
                // 模拟模式
                return buildMockUrl(bucketName, objectKey);
            }

            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentLength(file.getSize());
            metadata.setContentType(file.getContentType());

            PutObjectRequest putObjectRequest = new PutObjectRequest(
                bucketName, objectKey, file.getInputStream(), metadata
            );

            PutObjectResult result = cosClient.putObject(putObjectRequest);
            log.info("文件上传成功，ETag: {}", result.getETag());

            return buildFileUrl(bucketName, objectKey);
            
        } catch (CosServiceException e) {
            log.error("COS服务异常: {}", e.getMessage(), e);
            throw new FileServiceException(10010, "存储服务异常: " + e.getMessage());
        } catch (CosClientException e) {
            log.error("COS客户端异常: {}", e.getMessage(), e);
            throw new FileServiceException(10010, "存储服务异常: " + e.getMessage());
        } catch (IOException e) {
            log.error("文件读取异常: {}", e.getMessage(), e);
            throw new FileServiceException(10002, "文件上传失败");
        }
    }

    @Override
    public String uploadFile(InputStream inputStream, String bucketName, String objectKey,
                           String contentType, long contentLength) {
        try {
            if (cosClient == null) {
                // 模拟模式
                return buildMockUrl(bucketName, objectKey);
            }

            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentLength(contentLength);
            metadata.setContentType(contentType);

            PutObjectRequest putObjectRequest = new PutObjectRequest(
                bucketName, objectKey, inputStream, metadata
            );

            PutObjectResult result = cosClient.putObject(putObjectRequest);
            log.info("文件上传成功，ETag: {}", result.getETag());

            return buildFileUrl(bucketName, objectKey);
            
        } catch (CosServiceException e) {
            log.error("COS服务异常: {}", e.getMessage(), e);
            throw new FileServiceException(10010, "存储服务异常: " + e.getMessage());
        } catch (CosClientException e) {
            log.error("COS客户端异常: {}", e.getMessage(), e);
            throw new FileServiceException(10010, "存储服务异常: " + e.getMessage());
        }
    }

    @Override
    public void deleteFile(String bucketName, String objectKey) {
        try {
            if (cosClient == null) {
                // 模拟模式
                log.info("模拟删除文件: {}/{}", bucketName, objectKey);
                return;
            }

            cosClient.deleteObject(bucketName, objectKey);
            log.info("文件删除成功: {}/{}", bucketName, objectKey);
            
        } catch (CosServiceException e) {
            log.error("COS服务异常: {}", e.getMessage(), e);
            throw new FileServiceException(10010, "存储服务异常: " + e.getMessage());
        } catch (CosClientException e) {
            log.error("COS客户端异常: {}", e.getMessage(), e);
            throw new FileServiceException(10010, "存储服务异常: " + e.getMessage());
        }
    }

    @Override
    public String getDownloadUrl(String bucketName, String objectKey, int expiresIn) {
        try {
            if (cosClient == null) {
                // 模拟模式
                return buildMockUrl(bucketName, objectKey) + "?expires=" + expiresIn;
            }

            GeneratePresignedUrlRequest req = new GeneratePresignedUrlRequest(
                bucketName, objectKey, HttpMethodName.GET
            );
            
            Date expiration = Date.from(
                LocalDateTime.now().plusSeconds(expiresIn).toInstant(ZoneOffset.UTC)
            );
            req.setExpiration(expiration);

            URL url = cosClient.generatePresignedUrl(req);
            return url.toString();
            
        } catch (CosServiceException e) {
            log.error("COS服务异常: {}", e.getMessage(), e);
            throw new FileServiceException(10010, "存储服务异常: " + e.getMessage());
        } catch (CosClientException e) {
            log.error("COS客户端异常: {}", e.getMessage(), e);
            throw new FileServiceException(10010, "存储服务异常: " + e.getMessage());
        }
    }

    @Override
    public boolean fileExists(String bucketName, String objectKey) {
        try {
            if (cosClient == null) {
                // 模拟模式
                return true;
            }

            cosClient.getObjectMetadata(bucketName, objectKey);
            return true;
            
        } catch (CosServiceException e) {
            if (e.getStatusCode() == 404) {
                return false;
            }
            log.error("COS服务异常: {}", e.getMessage(), e);
            throw new FileServiceException(10010, "存储服务异常: " + e.getMessage());
        } catch (CosClientException e) {
            log.error("COS客户端异常: {}", e.getMessage(), e);
            throw new FileServiceException(10010, "存储服务异常: " + e.getMessage());
        }
    }

    @Override
    public long getFileSize(String bucketName, String objectKey) {
        try {
            if (cosClient == null) {
                // 模拟模式
                return 1024L;
            }

            ObjectMetadata metadata = cosClient.getObjectMetadata(bucketName, objectKey);
            return metadata.getContentLength();
            
        } catch (CosServiceException e) {
            log.error("COS服务异常: {}", e.getMessage(), e);
            throw new FileServiceException(10010, "存储服务异常: " + e.getMessage());
        } catch (CosClientException e) {
            log.error("COS客户端异常: {}", e.getMessage(), e);
            throw new FileServiceException(10010, "存储服务异常: " + e.getMessage());
        }
    }

    /**
     * 构建文件访问URL
     */
    private String buildFileUrl(String bucketName, String objectKey) {
        FileConfig.Cos cosConfig = fileConfig.getStorage().getCos();
        
        if (StringUtils.hasText(cosConfig.getCdnDomain())) {
            return "https://" + cosConfig.getCdnDomain() + "/" + objectKey;
        }
        
        if (StringUtils.hasText(cosConfig.getUrlPrefix())) {
            return cosConfig.getUrlPrefix() + "/" + objectKey;
        }
        
        return String.format("https://%s.cos.%s.myqcloud.com/%s", 
                            bucketName, cosConfig.getRegion(), objectKey);
    }

    /**
     * 构建模拟URL
     */
    private String buildMockUrl(String bucketName, String objectKey) {
        return "https://mock-cos.example.com/" + bucketName + "/" + objectKey;
    }
} 