package com.stylishlink.file.exception;

/**
 * 文件服务异常类
 */
public class FileServiceException extends RuntimeException {

    private final int errorCode;

    public FileServiceException(int errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }

    public FileServiceException(int errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }

    public int getErrorCode() {
        return errorCode;
    }
} 