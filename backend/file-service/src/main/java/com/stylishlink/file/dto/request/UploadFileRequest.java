package com.stylishlink.file.dto.request;

import com.stylishlink.file.entity.enums.AccessLevel;
import com.stylishlink.file.entity.enums.FileCategory;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.util.Map;

/**
 * 文件上传请求DTO
 */
@Data
public class UploadFileRequest {

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private String userId;

    /**
     * 文件分类
     */
    @NotNull(message = "文件分类不能为空")
    private FileCategory category;

    /**
     * 访问级别
     */
    private AccessLevel accessLevel = AccessLevel.PRIVATE;

    /**
     * 扩展元数据
     */
    private Map<String, Object> metadata;
} 