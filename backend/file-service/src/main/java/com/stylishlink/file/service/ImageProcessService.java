package com.stylishlink.file.service;

import java.io.InputStream;

/**
 * 图片处理服务接口
 */
public interface ImageProcessService {

    /**
     * 生成缩略图
     *
     * @param inputStream 原图输入流
     * @param width       缩略图宽度
     * @param height      缩略图高度
     * @param quality     图片质量 (0.0-1.0)
     * @return 缩略图字节数组
     */
    byte[] generateThumbnail(InputStream inputStream, int width, int height, double quality);

    /**
     * 添加水印
     *
     * @param inputStream  原图输入流
     * @param watermarkText 水印文字
     * @param fontSize     字体大小
     * @param color        颜色
     * @param opacity      透明度
     * @param position     位置
     * @return 添加水印后的图片字节数组
     */
    byte[] addWatermark(InputStream inputStream, String watermarkText, 
                       int fontSize, String color, double opacity, String position);

    /**
     * 压缩图片
     *
     * @param inputStream 原图输入流
     * @param quality     压缩质量 (0.0-1.0)
     * @param maxWidth    最大宽度
     * @param maxHeight   最大高度
     * @return 压缩后的图片字节数组
     */
    byte[] compressImage(InputStream inputStream, double quality, int maxWidth, int maxHeight);

    /**
     * 获取图片信息
     *
     * @param inputStream 图片输入流
     * @return 图片信息
     */
    ImageInfo getImageInfo(InputStream inputStream);

    /**
     * 图片信息类
     */
    class ImageInfo {
        private int width;
        private int height;
        private String format;
        private long fileSize;

        // getters and setters
        public int getWidth() { return width; }
        public void setWidth(int width) { this.width = width; }
        
        public int getHeight() { return height; }
        public void setHeight(int height) { this.height = height; }
        
        public String getFormat() { return format; }
        public void setFormat(String format) { this.format = format; }
        
        public long getFileSize() { return fileSize; }
        public void setFileSize(long fileSize) { this.fileSize = fileSize; }
    }
} 