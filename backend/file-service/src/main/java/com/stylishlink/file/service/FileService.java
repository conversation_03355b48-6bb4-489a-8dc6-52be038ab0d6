package com.stylishlink.file.service;

import com.stylishlink.file.dto.request.UploadFileRequest;
import com.stylishlink.file.dto.response.FileInfoResponse;
import com.stylishlink.file.dto.response.FileUploadResponse;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 文件服务接口
 */
public interface FileService {

    /**
     * 单文件上传
     */
    FileUploadResponse uploadFile(MultipartFile file, UploadFileRequest request);

    /**
     * 批量文件上传
     */
    List<FileUploadResponse> uploadFiles(List<MultipartFile> files, UploadFileRequest request);

    /**
     * 获取文件信息
     */
    FileInfoResponse getFileInfo(String fileId);

    /**
     * 获取文件列表
     */
    List<FileInfoResponse> getFileList(String userId, int page, int size);

    /**
     * 删除文件
     */
    void deleteFile(String fileId, boolean force);

    /**
     * 获取下载链接
     */
    String getDownloadUrl(String fileId, int expiresIn);
} 