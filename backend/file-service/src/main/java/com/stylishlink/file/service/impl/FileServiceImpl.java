package com.stylishlink.file.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.stylishlink.file.config.FileConfig;
import com.stylishlink.file.dto.request.UploadFileRequest;
import com.stylishlink.file.dto.response.FileInfoResponse;
import com.stylishlink.file.dto.response.FileUploadResponse;
import com.stylishlink.file.entity.FileInfo;
import com.stylishlink.file.entity.enums.FileStatus;
import com.stylishlink.file.entity.enums.FileType;
import com.stylishlink.file.exception.FileServiceException;
import com.stylishlink.file.mapper.FileInfoMapper;
import com.stylishlink.file.service.FileService;
import com.stylishlink.file.service.ImageProcessService;
import com.stylishlink.file.service.StorageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.tika.Tika;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * 文件服务实现类
 */
@Slf4j
@Service
public class FileServiceImpl implements FileService {

    @Autowired
    private FileInfoMapper fileInfoMapper;

    @Autowired(required = false)
    private StorageService storageService;

    @Autowired
    private ImageProcessService imageProcessService;

    @Autowired
    private FileConfig fileConfig;

    private final Tika tika = new Tika();

    @Override
    public FileUploadResponse uploadFile(MultipartFile file, UploadFileRequest request) {
        try {
            // 生成文件ID和存储路径
            String fileId = UUID.randomUUID().toString().replace("-", "");
            
            // 检测文件类型
            String mimeType = tika.detect(file.getInputStream());
            FileType fileType = determineFileType(mimeType);
            
            // 计算文件MD5
            String md5Hash = calculateMD5(file.getBytes());
            
            // 生成存储路径
            String objectKey = generateObjectKey(request.getCategory(), fileId, file.getOriginalFilename());
            String bucketName = getBucketName();
            
            // 上传文件到存储服务
            String url="";
            if (storageService != null) {
                url = storageService.uploadFile(file, bucketName, objectKey);
            }
            
            // 处理图片（生成缩略图）
            String thumbnailUrl = null;
            if (fileType == FileType.IMAGE) {
                thumbnailUrl = generateThumbnail(file, bucketName, objectKey);
            }
            
            // 创建文件信息
            FileInfo fileInfo = new FileInfo();
            fileInfo.setId(fileId);
            fileInfo.setUserId(request.getUserId());
            fileInfo.setOriginalName(file.getOriginalFilename());
            fileInfo.setStorageName(generateStorageName(file.getOriginalFilename()));
            fileInfo.setBucketName(bucketName);
            fileInfo.setObjectKey(objectKey);
            fileInfo.setCategory(request.getCategory());
            fileInfo.setFileType(fileType);
            fileInfo.setMimeType(mimeType);
            fileInfo.setFileSize(file.getSize());
            fileInfo.setMd5Hash(md5Hash);
            fileInfo.setStatus(FileStatus.AVAILABLE);
            fileInfo.setAccessLevel(request.getAccessLevel());
            fileInfo.setUrl(url);
            fileInfo.setThumbnailUrl(thumbnailUrl);
            fileInfo.setMetadata(request.getMetadata());
            fileInfo.setCreatedAt(LocalDateTime.now());
            fileInfo.setUpdatedAt(LocalDateTime.now());
            fileInfo.setDeleted(false);
            
            // 保存到数据库
            fileInfoMapper.insert(fileInfo);
            
            // 构建响应
            FileUploadResponse response = new FileUploadResponse();
            response.setFileId(fileId);
            response.setUrl(url);
            response.setThumbnailUrl(thumbnailUrl);
            
            FileInfoResponse fileInfoResponse = new FileInfoResponse();
            BeanUtils.copyProperties(fileInfo, fileInfoResponse);
            response.setFileInfo(fileInfoResponse);
            
            log.info("文件上传成功: fileId={}, url={}", fileId, url);
            return response;
            
        } catch (Exception e) {
            log.error("文件上传失败", e);
            throw new FileServiceException(10002, "文件上传失败: " + e.getMessage());
        }
    }

    @Override
    public List<FileUploadResponse> uploadFiles(List<MultipartFile> files, UploadFileRequest request) {
        List<FileUploadResponse> responses = new ArrayList<>();
        for (MultipartFile file : files) {
            try {
                FileUploadResponse response = uploadFile(file, request);
                responses.add(response);
            } catch (Exception e) {
                log.error("批量上传文件失败: {}", file.getOriginalFilename(), e);
                // 继续处理其他文件
            }
        }
        return responses;
    }

    @Override
    public FileInfoResponse getFileInfo(String fileId) {
        FileInfo fileInfo = fileInfoMapper.selectById(fileId);
        if (fileInfo == null || fileInfo.getDeleted()) {
            throw new FileServiceException(10001, "文件不存在");
        }
        
        FileInfoResponse response = new FileInfoResponse();
        BeanUtils.copyProperties(fileInfo, response);
        return response;
    }

    @Override
    public List<FileInfoResponse> getFileList(String userId, int page, int size) {
        Page<FileInfo> pageParam = new Page<>(page, size);
        QueryWrapper<FileInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId)
                   .eq("deleted", false)
                   .orderByDesc("created_at");
        
        Page<FileInfo> result = fileInfoMapper.selectPage(pageParam, queryWrapper);
        
        List<FileInfoResponse> responses = new ArrayList<>();
        for (FileInfo fileInfo : result.getRecords()) {
            FileInfoResponse response = new FileInfoResponse();
            BeanUtils.copyProperties(fileInfo, response);
            responses.add(response);
        }
        
        return responses;
    }

    @Override
    public void deleteFile(String fileId, boolean force) {
        FileInfo fileInfo = fileInfoMapper.selectById(fileId);
        if (fileInfo == null) {
            throw new FileServiceException(10001, "文件不存在");
        }
        
        if (force) {
            // 物理删除：删除存储文件
            if (storageService != null) {
                try {
                    storageService.deleteFile(fileInfo.getBucketName(), fileInfo.getObjectKey());
                } catch (Exception e) {
                    log.warn("删除存储文件失败: {}", e.getMessage());
                }
            }
            // 删除数据库记录
            fileInfoMapper.deleteById(fileId);
        } else {
            // 逻辑删除：直接使用MyBatis Plus的deleteById方法，会自动处理@TableLogic
            fileInfoMapper.deleteById(fileId);
        }
        
        log.info("文件删除成功: fileId={}, force={}", fileId, force);
    }

    @Override
    public String getDownloadUrl(String fileId, int expiresIn) {
        FileInfo fileInfo = fileInfoMapper.selectById(fileId);
        if (fileInfo == null || fileInfo.getDeleted()) {
            throw new FileServiceException(10001, "文件不存在");
        }
        
        // 生成带签名的下载URL
        if (storageService != null) {
            try {
                return storageService.getDownloadUrl(fileInfo.getBucketName(), fileInfo.getObjectKey(), expiresIn);
            } catch (Exception e) {
                log.warn("生成下载URL失败，返回原始URL: {}", e.getMessage());
            }
        }
        
        // 返回原URL
        return fileInfo.getUrl();
    }

    /**
     * 生成缩略图
     */
    private String generateThumbnail(MultipartFile file, String bucketName, String objectKey) {
        try {
            FileConfig.Thumbnail thumbnailConfig = fileConfig.getImage().getThumbnail();
            
            byte[] thumbnailData = imageProcessService.generateThumbnail(
                file.getInputStream(),
                thumbnailConfig.getWidth(),
                thumbnailConfig.getHeight(),
                thumbnailConfig.getQuality()
            );
            
            String thumbnailObjectKey = "thumbnails/" + objectKey;
            
            if (storageService != null) {
                return storageService.uploadFile(
                    new ByteArrayInputStream(thumbnailData),
                    bucketName,
                    thumbnailObjectKey,
                    "image/jpeg",
                    thumbnailData.length
                );
            } else {
                return "https://mock.example.com/" + thumbnailObjectKey;
            }
            
        } catch (Exception e) {
            log.warn("生成缩略图失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 获取存储桶名称
     */
    private String getBucketName() {
        String type = fileConfig.getStorage().getType();
        switch (type) {
            case "cos":
                return fileConfig.getStorage().getCos().getBucketName();
            case "oss":
                return fileConfig.getStorage().getOss().getBucketName();
            default:
                return "default-bucket";
        }
    }

    /**
     * 生成对象键
     */
    private String generateObjectKey(com.stylishlink.file.entity.enums.FileCategory category, String fileId, String originalName) {
        String extension = "";
        if (originalName != null && originalName.contains(".")) {
            extension = originalName.substring(originalName.lastIndexOf("."));
        }
        
        String categoryPath = category.name().toLowerCase().replace("_", "/");
        String datePath = LocalDateTime.now().toString().substring(0, 10); // yyyy-MM-dd
        
        return String.format("%s/%s/%s%s", categoryPath, datePath, fileId, extension);
    }

    /**
     * 根据MIME类型确定文件类型
     */
    private FileType determineFileType(String mimeType) {
        if (mimeType.startsWith("image/")) {
            return FileType.IMAGE;
        } else if (mimeType.startsWith("video/")) {
            return FileType.VIDEO;
        } else if (mimeType.startsWith("audio/")) {
            return FileType.AUDIO;
        } else if (mimeType.contains("pdf") || mimeType.contains("document") || 
                   mimeType.contains("text") || mimeType.contains("spreadsheet")) {
            return FileType.DOCUMENT;
        } else if (mimeType.contains("zip") || mimeType.contains("rar") || 
                   mimeType.contains("tar") || mimeType.contains("gzip")) {
            return FileType.ARCHIVE;
        } else {
            return FileType.OTHER;
        }
    }

    /**
     * 生成存储文件名
     */
    private String generateStorageName(String originalName) {
        String extension = "";
        if (originalName != null && originalName.contains(".")) {
            extension = originalName.substring(originalName.lastIndexOf("."));
        }
        return UUID.randomUUID().toString().replace("-", "") + extension;
    }

    /**
     * 计算文件MD5
     */
    private String calculateMD5(byte[] data) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] hash = md.digest(data);
            StringBuilder sb = new StringBuilder();
            for (byte b : hash) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        } catch (NoSuchAlgorithmException e) {
            log.error("MD5计算失败", e);
            throw new FileServiceException(10009, "文件校验失败");
        }
    }
}
 