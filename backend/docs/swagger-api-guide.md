# StylishLink 微服务 API 文档访问指南

## 概述

StylishLink 项目采用微服务架构，通过 Spring Cloud Gateway 作为统一入口，集成了 SpringDoc OpenAPI 3.0 来提供完整的 API 文档服务。本指南将帮助开发者快速访问和使用各个微服务的 API 文档。

## 架构说明

### 服务架构
```
Gateway Service (8080) 
├── User Service (8081)          - 用户管理服务
├── Wardrobe Service (8082)      - 衣橱管理服务  
├── Recommendation Service (8083) - 推荐算法服务
├── AI Service (8084)            - AI功能服务
├── Operation Service (8085)     - 运营管理服务
├── Social Service (8086)        - 社交功能服务
└── Payment Service (8087)       - 支付管理服务
```

### 技术栈
- **API网关**: Spring Cloud Gateway
- **服务发现**: Nacos Discovery
- **API文档**: SpringDoc OpenAPI 3.0
- **文档聚合**: Gateway SwaggerConfig
- **认证方式**: JWT Bearer Token

## 访问方式

### 🌐 统一入口访问 (推荐)
通过 Gateway 服务访问所有微服务的 API 文档：

```
http://localhost:8080/swagger-ui.html
```

**特性**：
- ✅ 一站式访问所有服务文档
- ✅ 支持服务切换查看
- ✅ 统一的认证管理
- ✅ 动态服务发现
- ✅ 路由自动配置

### 🔄 独立服务访问
直接访问各个微服务的 API 文档：

| 服务名称 | 端口 | Swagger UI | API Docs |
|---------|------|------------|----------|
| Gateway Service | 8080 | http://localhost:8080/swagger-ui.html | http://localhost:8080/v3/api-docs |
| User Service | 8081 | http://localhost:8081/swagger-ui.html | http://localhost:8081/v3/api-docs |
| Wardrobe Service | 8082 | http://localhost:8082/swagger-ui.html | http://localhost:8082/v3/api-docs |
| Recommendation Service | 8083 | http://localhost:8083/swagger-ui.html | http://localhost:8083/v3/api-docs |
| AI Service | 8084 | http://localhost:8084/swagger-ui.html | http://localhost:8084/v3/api-docs |
| Operation Service | 8085 | http://localhost:8085/swagger-ui.html | http://localhost:8085/v3/api-docs |
| Social Service | 8086 | http://localhost:8086/swagger-ui.html | http://localhost:8086/v3/api-docs |
| Payment Service | 8087 | http://localhost:8087/swagger-ui.html | http://localhost:8087/v3/api-docs |

## 服务功能说明

### 👤 User Service (用户服务)
**主要功能**：
- 用户注册、登录、认证
- 个人信息管理
- 五行命理分析
- 用户偏好设置

**API 分类**：
- 用户认证：登录、注册、刷新令牌
- 个人信息：基本信息、头像上传、偏好设置
- 五行分析：命理计算、个性化推荐
- 账户管理：密码修改、账户注销

### 👕 Wardrobe Service (衣橱服务)
**主要功能**：
- 数字衣橱管理
- 服饰分类识别
- 穿着记录统计
- 智能整理建议

**API 分类**：
- 衣物管理：添加、编辑、删除、查询
- 智能分类：AI识别、自动标签
- 穿着统计：频率分析、搭配记录
- 整理建议：断舍离推荐、收纳优化

### 🎯 Recommendation Service (推荐服务)
**主要功能**：
- 智能穿搭推荐
- 天气适配算法
- 场景匹配分析
- 个性化搭配

**API 分类**：
- 穿搭推荐：基于五行、天气、场景的智能推荐
- 算法配置：推荐权重、偏好学习
- 历史记录：推荐历史、反馈统计
- 搭配分析：色彩搭配、风格匹配

### 🤖 AI Service (AI服务)
**主要功能**：
- 图像识别分析
- 真人试衣效果
- 智能客服助手
- 风格化处理

**API 分类**：
- 图像识别：服饰识别、颜色提取、风格分析
- 试衣功能：虚拟试穿、效果预览
- 智能助手：问答系统、搭配建议
- 图像处理：风格迁移、效果增强

### 📊 Operation Service (运营服务)
**主要功能**：
- 灵感值系统
- 任务成就管理
- 会员特权体系
- 活动运营功能

**API 分类**：
- 灵感值管理：获取、消费、交易记录
- 任务系统：每日任务、活动任务、完成记录
- 成就系统：成就解锁、进度查询、奖励领取
- 会员特权：等级管理、特权权益、升级条件
- 活动运营：促销活动、限时活动、节日特惠

### 💬 Social Service (社交服务)
**主要功能**：
- 穿搭分享展示
- 用户互动系统
- 关注粉丝管理
- 社区动态功能

**API 分类**：
- 分享管理：穿搭发布、编辑、删除
- 互动功能：点赞、收藏、评论、回复
- 关注系统：关注、取消关注、列表管理
- 社区动态：广场浏览、热门推荐、话题讨论
- 用户统计：互动数据、活跃度分析

### 💳 Payment Service (支付服务)
**主要功能**：
- 会员充值管理
- 特权功能购买
- 订单交易处理
- 财务数据统计

**API 分类**：
- 订单管理：创建、查询、取消、退款
- 支付处理：支付发起、状态查询、结果确认
- 会员充值：套餐选择、充值记录、余额查询
- 特权购买：高级功能、权限激活、使用记录
- 支付回调：第三方平台回调处理
- 财务报表：收入统计、交易分析、对账数据

## 认证说明

### JWT Token 认证
所有需要认证的接口都使用 JWT Bearer Token：

```bash
# 请求头示例
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### 获取 Token
1. 通过用户服务登录接口获取 Token：
   ```bash
   POST /api/user/login
   {
     "username": "your_username",
     "password": "your_password"
   }
   ```

2. 在 Swagger UI 中使用：
   - 点击页面右上角 "Authorize" 按钮
   - 输入 `Bearer your_token_here`
   - 点击 "Authorize" 确认

### 无需认证的接口
- 用户注册、登录
- 健康检查 `/actuator/health`
- API 文档相关路径

## 开发调试

### 本地开发环境启动顺序
1. **启动基础设施**：
   ```bash
   # Nacos (服务发现与配置中心)
   # MySQL (数据库)
   # Redis (缓存)
   ```

2. **启动微服务** (建议顺序)：
   ```bash
   # 1. 启动 Gateway Service (8080)
   # 2. 启动 User Service (8081) 
   # 3. 启动其他业务服务 (8082-8087)
   ```

3. **验证服务状态**：
   ```bash
   # 检查 Nacos 控制台
   http://************:8848/nacos
   
   # 检查 Gateway 健康状态  
   http://localhost:8080/actuator/health
   ```

### 常见问题排查

#### 1. Swagger UI 无法访问
**问题**：页面显示 404 或加载失败
**解决方案**：
- 检查服务是否正常启动
- 确认端口是否被占用
- 验证 springdoc 依赖是否正确引入

#### 2. 服务文档聚合失败
**问题**：Gateway 中无法看到其他服务文档
**解决方案**：
- 检查服务是否注册到 Nacos
- 确认网关路由配置是否正确
- 验证各服务的 `/v3/api-docs` 端点是否可访问

#### 3. 接口调用认证失败
**问题**：返回 401 Unauthorized
**解决方案**：
- 检查 JWT Token 是否有效
- 确认 Authorization 头格式：`Bearer token`
- 验证接口是否需要特定权限

#### 4. 跨域访问问题
**问题**：浏览器提示 CORS 错误
**解决方案**：
- 检查 Gateway 的 CORS 配置
- 确认前端请求头设置
- 验证 SecurityConfig 中的跨域设置

## 最佳实践

### 1. API 设计规范
- 使用 RESTful 风格设计接口
- 统一的错误码和响应格式
- 合理的 HTTP 状态码使用
- 详细的接口文档注释

### 2. 认证授权策略
- 敏感接口必须进行身份验证
- 实现细粒度的权限控制
- 定期刷新和验证 Token
- 记录关键操作的审计日志

### 3. 性能优化建议
- 使用 Redis 缓存热点数据
- 实现接口响应时间监控
- 合理设置连接池和超时时间
- 定期分析和优化慢查询

### 4. 监控运维
- 集成应用性能监控 (APM)
- 设置关键指标告警
- 定期备份重要数据
- 建立完善的日志体系

## 更新日志

### v1.0.0 (2024-01-XX)
- ✅ 完成 Gateway 服务 Swagger 聚合配置
- ✅ 集成所有 7 个微服务的 API 文档
- ✅ 统一 JWT 认证体系
- ✅ 实现动态服务发现和路由
- ✅ 完善安全配置和跨域支持

---

**技术支持**：如有问题，请联系开发团队或查看项目 GitHub Issues。 