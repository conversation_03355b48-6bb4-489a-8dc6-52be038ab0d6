package com.stylishlink.user.service;

import com.stylishlink.user.dto.request.RefreshTokenRequest;
import com.stylishlink.user.dto.request.VerifyCodeLoginRequest;
import com.stylishlink.user.dto.response.LoginResponse;
import com.stylishlink.user.dto.response.RefreshTokenResponse;
import com.stylishlink.user.dto.response.SendCodeResponse;

/**
 * 认证服务接口
 */
public interface AuthService {
    
    /**
     * 发送验证码
     * @param phone 手机号
     * @return 发送结果
     */
    SendCodeResponse sendVerificationCode(String phone);
    
    /**
     * 验证码登录（自动注册新用户）
     * @param request 登录请求
     * @return 登录结果
     */
    LoginResponse loginByVerificationCode(VerifyCodeLoginRequest request);
    
    /**
     * 刷新令牌
     * @param request 刷新令牌请求
     * @return 新令牌信息
     */
    RefreshTokenResponse refreshToken(RefreshTokenRequest request);
    
    /**
     * 退出登录
     * @param token 令牌
     */
    void logout(String token);
} 