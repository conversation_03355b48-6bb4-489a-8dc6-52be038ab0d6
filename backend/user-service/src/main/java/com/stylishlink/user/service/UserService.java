package com.stylishlink.user.service;

import com.stylishlink.user.entity.User;
import com.stylishlink.user.dto.request.CompleteProfileRequest;
import com.stylishlink.user.dto.request.UpdateProfileRequest;
import com.stylishlink.user.dto.response.UserResponse;
import com.stylishlink.user.dto.response.BodyShapeAnalysisResponse;
import com.stylishlink.user.dto.response.ProfileResponse;
import com.stylishlink.user.dto.response.UserModeResponse;
import com.stylishlink.user.dto.response.BaziResponse;
import com.stylishlink.user.dto.response.TodayEnergyResponse;
import com.stylishlink.user.dto.response.TodayEnergyBriefResponse;

import java.util.Map;
import java.util.Optional;

/**
 * 用户服务接口
 */
public interface UserService {
    
    /**
     * 根据ID查询用户
     * @param id 用户ID
     * @return 用户信息
     */
    Optional<User> findById(String id);
    
    /**
     * 根据userId查询用户
     * @param userId 用户ID
     * @return 用户信息
     */
    Optional<User> findByUserId(String userId);
    
    /**
     * 根据userId查询用户响应信息
     * @param userId 用户ID
     * @return 用户响应信息
     */
    UserResponse findUserResponseByUserId(String userId);
    
    /**
     * 根据OpenID查询用户
     * @param openId 微信OpenID
     * @return 用户信息
     */
    Optional<User> findByOpenId(String openId);
    
    /**
     * 根据手机号查询用户
     * @param phone 手机号
     * @return 用户信息
     */
    Optional<User> findByPhone(String phone);
    
    /**
     * 根据邮箱查询用户
     * @param email 邮箱
     * @return 用户信息
     */
    Optional<User> findByEmail(String email);
    
    /**
     * 通过用户名、手机号或邮箱查询用户
     * @param username 用户名、手机号或邮箱
     * @return 用户信息
     */
    Optional<User> findByUsername(String username);
    
    /**
     * 保存用户
     * @param user 用户信息
     * @return 保存后的用户信息
     */
    User save(User user);
    
    /**
     * 检查用户ID是否存在
     * @param userId 用户ID
     * @return 是否存在
     */
    boolean existsByUserId(String userId);
    
    /**
     * 检查OpenID是否存在
     * @param openId 微信OpenID
     * @return 是否存在
     */
    boolean existsByOpenId(String openId);
    
    /**
     * 检查手机号是否存在
     * @param phone 手机号
     * @return 是否存在
     */
    boolean existsByPhone(String phone);
    
    /**
     * 检查邮箱是否存在
     * @param email 邮箱
     * @return 是否存在
     */
    boolean existsByEmail(String email);
    
    /**
     * 将User实体转换为UserResponse
     * @param user 用户实体
     * @return 用户响应对象
     */
    UserResponse toUserResponse(User user);
    
    /**
     * 为用户生成JWT Token
     * @param userId 用户ID
     * @return JWT Token
     */
    String generateTokenForUser(String userId);
    
    /**
     * 完善用户信息
     * @param userId 用户ID
     * @param request 完善信息请求
     * @return 更新结果
     */
    ProfileResponse completeProfile(String userId, CompleteProfileRequest request);
    
    /**
     * 更新用户信息
     * @param userId 用户ID
     * @param request 更新信息请求
     * @return 更新结果
     */
    ProfileResponse updateProfile(String userId, UpdateProfileRequest request);
    
    /**
     * 切换用户模式
     * @param userId 用户ID
     * @param mode 用户模式
     * @return 切换结果
     */
    UserModeResponse switchUserMode(String userId, String mode);
    
    /**
     * 获取用户模式
     * @param userId 用户ID
     * @return 用户模式信息
     */
    UserModeResponse getUserMode(String userId);
    
    /**
     * 根据身材分析结果更新用户信息
     * @param userId 用户ID
     * @param analysisResult AI分析结果
     * @return 更新结果
     */
    Map<String, Object> updateBodyShapeFromAnalysis(String userId, Map<String, Object> analysisResult);
    
    /**
     * 根据文件ID进行身材分析并更新用户信息（完整业务流程）
     * @param userId 用户ID
     * @param fileId 文件ID
     * @return 分析结果和用户更新状态
     */
    BodyShapeAnalysisResponse analyzeAndUpdateBodyShape(String userId, String fileId);
    
    /**
     * 保存八字计算结果到用户信息
     * @param userId 用户ID
     * @param baziResult 八字计算结果
     * @return 保存是否成功
     */
    boolean saveBaziResult(String userId, BaziResponse.BaziResult baziResult);
    
    /**
     * 获取今日能量信息
     * @param userId 用户ID
     * @param date 指定日期，格式YYYY-MM-DD，默认今日
     * @return 今日能量信息
     */
    TodayEnergyResponse getTodayEnergy(String userId, String date);
    
    /**
     * 获取今日能量简要信息
     * @param userId 用户ID
     * @param date 指定日期，格式YYYY-MM-DD，默认今日
     * @return 今日能量简要信息
     */
    TodayEnergyBriefResponse getTodayEnergyBrief(String userId, String date);
    
    /**
     * 异步生成八字信息
     * @param userId 用户ID
     * @param fullName 姓名
     * @param birthDate 出生日期
     * @param birthTime 出生时间
     * @param birthPlace 出生地点
     * @param gender 性别
     */
    void asyncGenerateBazi(String userId, String fullName, String birthDate, String birthTime, String birthPlace, Integer gender);
    
    /**
     * 更新用户居住地区信息
     * @param userId 用户ID
     * @param residenceName 居住地点中文名称
     * @param residenceCode 居住地点地区代码
     */
    void updateUserResidence(String userId, String residenceName, String residenceCode);
} 