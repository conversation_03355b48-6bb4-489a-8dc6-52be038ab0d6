package com.stylishlink.user.service.impl;

import com.stylishlink.user.entity.UserDailyEnergy;
import com.stylishlink.user.dto.response.TodayEnergyResponse;
import com.stylishlink.user.dto.response.TodayEnergyBriefResponse;
import com.stylishlink.user.repository.UserDailyEnergyRepository;
import com.stylishlink.user.service.UserDailyEnergyService;
import com.stylishlink.user.client.AIServiceClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户每日能量服务实现
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class UserDailyEnergyServiceImpl implements UserDailyEnergyService {
    
    private final UserDailyEnergyRepository userDailyEnergyRepository;
    
    @Autowired(required = false)
    private AIServiceClient aiServiceClient;
    
    @Override
    public Optional<UserDailyEnergy> findByUserIdAndDate(String userId, String energyDate) {
        log.debug("查询用户每日能量信息: userId={}, date={}", userId, energyDate);
        return userDailyEnergyRepository.findByUserIdAndDate(userId, energyDate);
    }
    
    @Override
    @Transactional
    public UserDailyEnergy saveOrUpdate(UserDailyEnergy dailyEnergy) {
        log.info("保存或更新用户每日能量信息: userId={}, date={}", 
                dailyEnergy.getUserId(), dailyEnergy.getEnergyDate());
        
        // 检查是否已存在记录
        Optional<UserDailyEnergy> existingEnergy = userDailyEnergyRepository.findByUserIdAndDate(
                dailyEnergy.getUserId(), dailyEnergy.getEnergyDate());
        
        if (existingEnergy.isPresent()) {
            // 更新现有记录
            UserDailyEnergy existing = existingEnergy.get();
            existing.setTotalScore(dailyEnergy.getTotalScore());
            existing.setPercentage(dailyEnergy.getPercentage());
            existing.setPeakTime(dailyEnergy.getPeakTime());
            existing.setPeakTimeDescription(dailyEnergy.getPeakTimeDescription());
            existing.setDescription(dailyEnergy.getDescription());
            existing.setDateInfo(dailyEnergy.getDateInfo());
            existing.setDimensions(dailyEnergy.getDimensions());
            existing.setAdvice(dailyEnergy.getAdvice());
            existing.setLuckyElements(dailyEnergy.getLuckyElements());
            existing.setLuckyElementsSummary(dailyEnergy.getLuckyElementsSummary());
            existing.setUpdateTime(new Date());
            
            userDailyEnergyRepository.updateById(existing);
            log.info("更新用户每日能量信息成功: id={}", existing.getId());
            return existing;
        } else {
            // 插入新记录
            userDailyEnergyRepository.insert(dailyEnergy);
            log.info("保存用户每日能量信息成功: id={}", dailyEnergy.getId());
            return dailyEnergy;
        }
    }
    
    @Override
    public UserDailyEnergy createFromResponse(String userId, String energyDate, TodayEnergyResponse response) {
        log.debug("从TodayEnergyResponse创建UserDailyEnergy: userId={}, date={}", userId, energyDate);
        
        // 转换嵌套对象
        UserDailyEnergy.DateInfo dateInfo = null;
        if (response.getDateInfo() != null) {
            dateInfo = UserDailyEnergy.DateInfo.builder()
                    .gregorian(response.getDateInfo().getGregorian())
                    .lunar(response.getDateInfo().getLunar())
                    .build();
        }
        
        UserDailyEnergy.Dimensions dimensions = null;
        if (response.getDimensions() != null) {
            dimensions = UserDailyEnergy.Dimensions.builder()
                    .love(response.getDimensions().getLove())
                    .career(response.getDimensions().getCareer())
                    .wealth(response.getDimensions().getWealth())
                    .health(response.getDimensions().getHealth())
                    .relationship(response.getDimensions().getRelationship())
                    .build();
        }
        
        UserDailyEnergy.Advice advice = null;
        if (response.getAdvice() != null) {
            advice = convertAdvice(response.getAdvice());
        }
        
        UserDailyEnergy.LuckyElements luckyElements = null;
        if (response.getLuckyElements() != null) {
            // 转换颜色列表
            List<UserDailyEnergy.ColorItem> entityColors = null;
            if (response.getLuckyElements().getColors() != null) {
                entityColors = response.getLuckyElements().getColors().stream()
                        .map(color -> UserDailyEnergy.ColorItem.builder()
                                .value(color.getValue())
                                .name(color.getName())
                                .build())
                        .collect(Collectors.toList());
            }
            
            luckyElements = UserDailyEnergy.LuckyElements.builder()
                    .colors(entityColors)
                    .clothing(response.getLuckyElements().getClothing())
                    .accessories(response.getLuckyElements().getAccessories())
                    .makeup(response.getLuckyElements().getMakeup())
                    .build();
        }
        
        // 生成幸运元素简化总结
        UserDailyEnergy.LuckyElementsSummary luckyElementsSummary = null;
        if (response.getLuckyElements() != null) {
            luckyElementsSummary = generateLuckyElementsSummary(response.getLuckyElements());
        }
        
        return UserDailyEnergy.builder()
                .userId(userId)
                .energyDate(energyDate)
                .totalScore(response.getTotalScore())
                .percentage(response.getPercentage())
                .peakTime(response.getPeakTime())
                .peakTimeDescription(response.getPeakTimeDescription())
                .description(response.getDescription())
                .dateInfo(dateInfo)
                .dimensions(dimensions)
                .advice(advice)
                .luckyElements(luckyElements)
                .luckyElementsSummary(luckyElementsSummary)
                .createTime(new Date())
                .updateTime(new Date())
                .build();
    }
    
    @Override
    public TodayEnergyResponse convertToResponse(UserDailyEnergy dailyEnergy) {
        log.debug("转换UserDailyEnergy为TodayEnergyResponse: userId={}, date={}", 
                dailyEnergy.getUserId(), dailyEnergy.getEnergyDate());
        
        // 转换嵌套对象
        TodayEnergyResponse.DateInfo dateInfo = null;
        if (dailyEnergy.getDateInfo() != null) {
            dateInfo = TodayEnergyResponse.DateInfo.builder()
                    .gregorian(dailyEnergy.getDateInfo().getGregorian())
                    .lunar(dailyEnergy.getDateInfo().getLunar())
                    .build();
        }
        
        TodayEnergyResponse.Dimensions dimensions = null;
        if (dailyEnergy.getDimensions() != null) {
            dimensions = TodayEnergyResponse.Dimensions.builder()
                    .love(dailyEnergy.getDimensions().getLove())
                    .career(dailyEnergy.getDimensions().getCareer())
                    .wealth(dailyEnergy.getDimensions().getWealth())
                    .health(dailyEnergy.getDimensions().getHealth())
                    .relationship(dailyEnergy.getDimensions().getRelationship())
                    .build();
        }
        
        TodayEnergyResponse.Advice advice = null;
        if (dailyEnergy.getAdvice() != null) {
            advice = convertAdviceToResponse(dailyEnergy.getAdvice());
        }
        
        TodayEnergyResponse.LuckyElements luckyElements = null;
        if (dailyEnergy.getLuckyElements() != null) {
            // 转换颜色列表
            List<TodayEnergyResponse.ColorItem> responseColors = null;
            if (dailyEnergy.getLuckyElements().getColors() != null) {
                responseColors = dailyEnergy.getLuckyElements().getColors().stream()
                        .map(color -> TodayEnergyResponse.ColorItem.builder()
                                .value(color.getValue())
                                .name(color.getName())
                                .build())
                        .collect(Collectors.toList());
            }
            
            luckyElements = TodayEnergyResponse.LuckyElements.builder()
                    .colors(responseColors)
                    .clothing(dailyEnergy.getLuckyElements().getClothing())
                    .accessories(dailyEnergy.getLuckyElements().getAccessories())
                    .makeup(dailyEnergy.getLuckyElements().getMakeup())
                    .build();
        }
        
        return TodayEnergyResponse.builder()
                .dateInfo(dateInfo)
                .totalScore(dailyEnergy.getTotalScore())
                .percentage(dailyEnergy.getPercentage())
                .peakTime(dailyEnergy.getPeakTime())
                .peakTimeDescription(dailyEnergy.getPeakTimeDescription())
                .description(dailyEnergy.getDescription())
                .dimensions(dimensions)
                .advice(advice)
                .luckyElements(luckyElements)
                .build();
    }
    
    @Override
    public List<UserDailyEnergy> findByUserIdAndDateRange(String userId, String startDate, String endDate) {
        log.debug("查询用户指定日期范围内的能量信息: userId={}, startDate={}, endDate={}", 
                userId, startDate, endDate);
        return userDailyEnergyRepository.findByUserIdAndDateRange(userId, startDate, endDate);
    }
    
    @Override
    public List<UserDailyEnergy> findRecentByUserId(String userId, int days) {
        log.debug("查询用户最近{}天的能量信息: userId={}", days, userId);
        return userDailyEnergyRepository.findRecentByUserId(userId, days);
    }
    
    @Override
    public boolean existsByUserIdAndDate(String userId, String energyDate) {
        return userDailyEnergyRepository.existsByUserIdAndDate(userId, energyDate);
    }
    
    @Override
    @Transactional
    public int cleanupExpiredRecords(String userId) {
        log.info("清理用户过期的能量记录: userId={}", userId);
        
        // 删除30天前的记录
        String beforeDate = LocalDate.now().minusDays(30).toString();
        int deletedCount = userDailyEnergyRepository.deleteExpiredByUserId(userId, beforeDate);
        
        log.info("清理用户过期能量记录完成: userId={}, deletedCount={}", userId, deletedCount);
        return deletedCount;
    }
    
    /**
     * 转换Advice对象（从Response到Entity）
     */
    private UserDailyEnergy.Advice convertAdvice(TodayEnergyResponse.Advice responseAdvice) {
        List<UserDailyEnergy.Category> categories = null;
        if (responseAdvice.getCategories() != null) {
            categories = responseAdvice.getCategories().stream()
                    .map(this::convertCategory)
                    .collect(Collectors.toList());
        }
        
        List<UserDailyEnergy.LifeSuggestion> lifeSuggestions = null;
        if (responseAdvice.getLifeSuggestions() != null) {
            lifeSuggestions = responseAdvice.getLifeSuggestions().stream()
                    .map(suggestion -> UserDailyEnergy.LifeSuggestion.builder()
                            .icon(suggestion.getIcon())
                            .content(suggestion.getContent())
                            .build())
                    .collect(Collectors.toList());
        }
        
        return UserDailyEnergy.Advice.builder()
                .categories(categories)
                .lifeSuggestions(lifeSuggestions)
                .build();
    }
    
    /**
     * 转换Category对象（从Response到Entity）
     */
    private UserDailyEnergy.Category convertCategory(TodayEnergyResponse.Category responseCategory) {
        List<UserDailyEnergy.Item> items = null;
        if (responseCategory.getItems() != null) {
            items = responseCategory.getItems().stream()
                    .map(item -> UserDailyEnergy.Item.builder()
                            .id(item.getId())
                            .icon(item.getIcon())
                            .text(item.getText())
                            .build())
                    .collect(Collectors.toList());
        }
        
        return UserDailyEnergy.Category.builder()
                .type(responseCategory.getType())
                .label(responseCategory.getLabel())
                .items(items)
                .build();
    }
    
    /**
     * 转换Advice对象（从Entity到Response）
     */
    private TodayEnergyResponse.Advice convertAdviceToResponse(UserDailyEnergy.Advice entityAdvice) {
        List<TodayEnergyResponse.Category> categories = null;
        if (entityAdvice.getCategories() != null) {
            categories = entityAdvice.getCategories().stream()
                    .map(this::convertCategoryToResponse)
                    .collect(Collectors.toList());
        }
        
        List<TodayEnergyResponse.LifeSuggestion> lifeSuggestions = null;
        if (entityAdvice.getLifeSuggestions() != null) {
            lifeSuggestions = entityAdvice.getLifeSuggestions().stream()
                    .map(suggestion -> TodayEnergyResponse.LifeSuggestion.builder()
                            .icon(suggestion.getIcon())
                            .content(suggestion.getContent())
                            .build())
                    .collect(Collectors.toList());
        }
        
        return TodayEnergyResponse.Advice.builder()
                .categories(categories)
                .lifeSuggestions(lifeSuggestions)
                .build();
    }
    
    /**
     * 转换Category对象（从Entity到Response）
     */
    private TodayEnergyResponse.Category convertCategoryToResponse(UserDailyEnergy.Category entityCategory) {
        List<TodayEnergyResponse.Item> items = null;
        if (entityCategory.getItems() != null) {
            items = entityCategory.getItems().stream()
                    .map(item -> TodayEnergyResponse.Item.builder()
                            .id(item.getId())
                            .icon(item.getIcon())
                            .text(item.getText())
                            .build())
                    .collect(Collectors.toList());
        }
        
        return TodayEnergyResponse.Category.builder()
                .type(entityCategory.getType())
                .label(entityCategory.getLabel())
                .items(items)
                .build();
    }
    
    @Override
    public TodayEnergyBriefResponse convertToBriefResponse(UserDailyEnergy dailyEnergy) {
        log.debug("转换UserDailyEnergy为TodayEnergyBriefResponse: userId={}, date={}", 
                dailyEnergy.getUserId(), dailyEnergy.getEnergyDate());
        
        // 转换日期信息
        TodayEnergyBriefResponse.DateInfo dateInfo = null;
        if (dailyEnergy.getDateInfo() != null) {
            dateInfo = TodayEnergyBriefResponse.DateInfo.builder()
                    .gregorian(dailyEnergy.getDateInfo().getGregorian())
                    .lunar(dailyEnergy.getDateInfo().getLunar())
                    .build();
        }
        
        // 直接使用数据库中已经总结好的幸运元素数据
        TodayEnergyBriefResponse.LuckyElements luckyElements = null;
        if (dailyEnergy.getLuckyElementsSummary() != null) {
            luckyElements = TodayEnergyBriefResponse.LuckyElements.builder()
                    .clothingSummary(dailyEnergy.getLuckyElementsSummary().getClothingSummary())
                    .accessoriesSummary(dailyEnergy.getLuckyElementsSummary().getAccessoriesSummary())
                    .makeupSummary(dailyEnergy.getLuckyElementsSummary().getMakeupSummary())
                    .build();
            log.debug("使用数据库中已总结的幸运元素数据: userId={}", dailyEnergy.getUserId());
        } else {
            log.warn("数据库中缺少幸运元素总结数据: userId={}, date={}", 
                    dailyEnergy.getUserId(), dailyEnergy.getEnergyDate());
        }
        
        return TodayEnergyBriefResponse.builder()
                .dateInfo(dateInfo)
                .totalScore(dailyEnergy.getTotalScore())
                .percentage(dailyEnergy.getPercentage())
                .luckyElements(luckyElements)
                .build();
    }
    

    
    /**
     * 生成幸运元素简化总结
     */
    private UserDailyEnergy.LuckyElementsSummary generateLuckyElementsSummary(TodayEnergyResponse.LuckyElements luckyElements) {
        log.debug("开始生成幸运元素简化总结");
        
        try {
            // 如果AI服务不可用，使用本地简化逻辑
            if (aiServiceClient == null) {
                log.warn("AI服务不可用，使用本地简化逻辑");
                return generateLocalSummary(luckyElements);
            }
            
            // 构建AI请求
            Map<String, Object> request = new HashMap<>();
            request.put("clothing", luckyElements.getClothing());
            request.put("accessories", luckyElements.getAccessories());
            request.put("makeup", luckyElements.getMakeup());
            
            // 调用AI服务进行文案简化
            Map<String, Object> aiResponse = aiServiceClient.summarizeLuckyElements(request);
            
            // 解析AI响应
            Object codeObj = aiResponse.get("code");
            int code = codeObj != null ? Integer.parseInt(codeObj.toString()) : 9999;
            
            if (code == 200) {
                Object data = aiResponse.get("data");
                if (data instanceof Map) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> summaryData = (Map<String, Object>) data;
                    
                    return UserDailyEnergy.LuckyElementsSummary.builder()
                            .clothingSummary((String) summaryData.get("clothingSummary"))
                            .accessoriesSummary((String) summaryData.get("accessoriesSummary"))
                            .makeupSummary((String) summaryData.get("makeupSummary"))
                            .build();
                }
            } else {
                log.warn("AI简化总结服务返回错误，code: {}, 使用本地简化逻辑", code);
            }
            
        } catch (Exception e) {
            log.warn("调用AI简化总结服务失败，使用本地简化逻辑: {}", e.getMessage());
        }
        
        // 回退到本地简化逻辑
        return generateLocalSummary(luckyElements);
    }
    
    /**
     * 本地简化逻辑（备用方案）
     */
    private UserDailyEnergy.LuckyElementsSummary generateLocalSummary(TodayEnergyResponse.LuckyElements luckyElements) {
        return UserDailyEnergy.LuckyElementsSummary.builder()
                .clothingSummary(generateSummaryFromList(luckyElements.getClothing(), "今日服饰搭配以舒适自然为主。"))
                .accessoriesSummary(generateSummaryFromList(luckyElements.getAccessories(), "简约配饰最为适宜。"))
                .makeupSummary(generateSummaryFromList(luckyElements.getMakeup(), "自然清透妆容为佳。"))
                .build();
    }
    
    /**
     * 从列表生成简要总结（本地逻辑）
     */
    private String generateSummaryFromList(List<String> list, String defaultSummary) {
        if (list == null || list.isEmpty()) {
            return defaultSummary;
        }
        
        if (list.size() == 1) {
            return list.get(0);
        }
        
        // 组合前两条建议
        String firstSuggestion = list.get(0);
        String secondSuggestion = list.get(1);
        return firstSuggestion + " " + secondSuggestion;
    }
} 