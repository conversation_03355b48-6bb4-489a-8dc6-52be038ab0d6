package com.stylishlink.user.service.impl;

import com.stylishlink.user.dto.response.SendCodeResponse;
import com.stylishlink.user.service.VerificationCodeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.UUID;

/**
 * 验证码服务实现
 * 当前版本：不真正校验，所有验证码都通过
 */
@Service
@Slf4j
public class VerificationCodeServiceImpl implements VerificationCodeService {
    
    @Override
    public SendCodeResponse sendCode(String phone) {
        log.info("发送验证码到手机号: {}", phone);
        
        // 生成验证码标识
        String codeId = "code_" + UUID.randomUUID().toString().replace("-", "");
        
        // TODO: 集成真正的短信服务
        // 当前版本直接返回成功
        log.info("验证码已发送（模拟）, codeId: {}", codeId);
        
        return SendCodeResponse.builder()
                .codeId(codeId)
                .expiresIn(300) // 5分钟有效期
                .build();
    }
    
    @Override
    public boolean verifyCode(String phone, String code, String codeId) {
        log.info("验证验证码: phone={}, code={}, codeId={}", phone, code, codeId);
        
        // TODO: 实现真正的验证逻辑
        // 当前版本直接返回成功
        log.info("验证码验证通过（模拟）");
        
        return true;
    }
} 