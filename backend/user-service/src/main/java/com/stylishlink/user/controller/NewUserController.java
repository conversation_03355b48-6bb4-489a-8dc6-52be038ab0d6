package com.stylishlink.user.controller;

import cn.hutool.core.convert.Convert;
import com.stylishlink.common.dto.ApiResponse;
import com.stylishlink.user.dto.request.BodyShapeAnalysisRequest;
import com.stylishlink.user.dto.request.CompleteProfileRequest;
import com.stylishlink.user.dto.request.RefreshTokenRequest;
import com.stylishlink.user.dto.request.SendCodeRequest;
import com.stylishlink.user.dto.request.UpdateProfileRequest;
import com.stylishlink.user.dto.request.VerifyCodeLoginRequest;
import com.stylishlink.user.dto.request.WuxingRequest;
import com.stylishlink.user.dto.response.BodyShapeAnalysisResponse;
import com.stylishlink.user.dto.response.LoginResponse;
import com.stylishlink.user.dto.response.RefreshTokenResponse;
import com.stylishlink.user.dto.response.SendCodeResponse;
import com.stylishlink.user.dto.response.UserResponse;
import com.stylishlink.user.dto.response.ProfileResponse;
import com.stylishlink.user.dto.response.WuxingResponse;
import com.stylishlink.user.dto.response.UserModeResponse;
import com.stylishlink.user.dto.response.BaziResponse;
import com.stylishlink.user.dto.response.TodayEnergyResponse;
import com.stylishlink.user.dto.response.TodayEnergyBriefResponse;
import com.stylishlink.user.dto.response.CompleteFortuneReadingResponse;
import com.stylishlink.user.dto.response.WeatherResponse;
import com.stylishlink.user.service.AuthService;
import com.stylishlink.user.service.UserService;
import com.stylishlink.user.service.WuxingService;
import com.stylishlink.user.service.FortuneReadingService;
import com.stylishlink.user.service.JwtService;
import com.stylishlink.user.client.AIServiceClient;
import com.stylishlink.common.exception.BusinessException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;
import java.util.HashMap;
import java.util.List;

/**
 * 新用户控制器 - 按照API文档实现所有接口
 */
@RestController
@RequestMapping("/user")
@RequiredArgsConstructor
@Slf4j
public class NewUserController {
    
    private final AuthService authService;
    private final UserService userService;
    private final WuxingService wuxingService;
    private final FortuneReadingService fortuneReadingService;
    private final JwtService jwtService;
    
    @Autowired(required = false)
    private AIServiceClient aiServiceClient;
    
    /**
     * 发送验证码
     */
    @PostMapping("/send-code")
    public ApiResponse<SendCodeResponse> sendCode(@Valid @RequestBody SendCodeRequest request) {
        log.info("发送验证码: {}", request.getPhone());
        SendCodeResponse response = authService.sendVerificationCode(request.getPhone());
        return ApiResponse.success(response);
    }
    
    /**
     * 验证码登录（自动注册新用户）
     */
    @PostMapping("/login")
    public ApiResponse<LoginResponse> login(@Valid @RequestBody VerifyCodeLoginRequest request) {
        log.info("验证码登录: {}", request.getPhone());
        LoginResponse response = authService.loginByVerificationCode(request);
        return ApiResponse.success(response);
    }
    
    /**
     * 刷新JWT Token
     */
    @PostMapping("/refresh-token")
    public ApiResponse<RefreshTokenResponse> refreshToken(@Valid @RequestBody RefreshTokenRequest request) {
        log.info("刷新令牌");
        RefreshTokenResponse response = authService.refreshToken(request);
        return ApiResponse.success(response);
    }
    
    /**
     * 用户退出登录
     */
    @PostMapping("/logout")
    @Operation(summary = "用户退出登录", description = "清除服务器端认证信息")
    public ApiResponse<Map<String, String>> logout() {
        try {
            // 获取token（从请求头获取）
            HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes()).getRequest();
            String authHeader = request.getHeader("Authorization");
            String token = null;
            
            if (StringUtils.hasText(authHeader) && authHeader.startsWith("Bearer ")) {
                token = authHeader.substring(7);
            }
            
            if (token == null) {
                throw new BusinessException("1002", "未找到有效的认证令牌");
            }
            
            log.info("用户退出登录，token: {}", token.substring(0, Math.min(token.length(), 20)) + "...");
            
            // 调用认证服务执行退出逻辑
            authService.logout(token);
            
            Map<String, String> result = new HashMap<>();
            result.put("message", "退出登录成功");
            
            return ApiResponse.success(result);
            
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("退出登录时发生异常", e);
            throw new BusinessException("1001", "退出登录失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取用户档案
     */
    @GetMapping("/profile")
    public ApiResponse<UserResponse> getProfile() {
        String userId = getCurrentUserId();
        log.info("获取用户档案: {}", userId);
        UserResponse response = userService.findUserResponseByUserId(userId);
        return ApiResponse.success(response);
    }
    
    /**
     * 完善用户信息
     */
    @PutMapping("/complete-profile")
    public ApiResponse<ProfileResponse> completeProfile(@Valid @RequestBody CompleteProfileRequest request) {
        String userId = getCurrentUserId();
        log.info("完善用户信息: {}", userId);
        ProfileResponse response = userService.completeProfile(userId, request);
        return ApiResponse.success(response);
    }
    
    /**
     * 更新用户档案
     */
    @PutMapping("/profile")
    public ApiResponse<ProfileResponse> updateProfile(@Valid @RequestBody UpdateProfileRequest request) {
        String userId = getCurrentUserId();
        log.info("更新用户档案: {}", userId);
        ProfileResponse response = userService.updateProfile(userId, request);
        return ApiResponse.success(response);
    }
    
    /**
     * 获取/更新五行命理档案
     */
    @GetMapping("/wuxing")
    public ApiResponse<WuxingResponse> getWuxing() {
        String userId = getCurrentUserId();
        log.info("获取五行命理档案: {}", userId);
        WuxingResponse response = wuxingService.getWuxingProfile(userId);
        return ApiResponse.success(response);
    }
    
    @PutMapping("/wuxing")
    public ApiResponse<WuxingResponse> updateWuxing(@Valid @RequestBody WuxingRequest request) {
        String userId = getCurrentUserId();
        log.info("更新五行命理档案: {}", userId);
        WuxingResponse response = wuxingService.updateWuxingProfile(userId, request);
        return ApiResponse.success(response);
    }
    
    /**
     * 计算用户八字
     */
    @PostMapping("/bazi")
    @Operation(summary = "计算用户八字", description = "根据用户信息计算八字命理")
    public ApiResponse<BaziResponse.BaziResult> calculateBaZi(@Valid @RequestBody WuxingRequest request) {
        String userId = getCurrentUserId();
        log.info("计算用户八字: {}", userId);
        BaziResponse response = wuxingService.calculateBaZi(userId, request);
        
        // 直接返回BaziResult，不包装成完整的BaziResponse
        return ApiResponse.success(response.getBaziResult());
    }
    
    /**
     * 用户设置
     */
    @GetMapping("/settings")
    public ApiResponse<Map<String, Object>> getSettings() {
        String userId = getCurrentUserId();
        log.info("获取用户设置: {}", userId);
        // TODO: 实现用户设置功能
        return ApiResponse.success(Map.of("settings", "暂未实现"));
    }
    
    @PutMapping("/settings")
    public ApiResponse<Map<String, Object>> updateSettings(@RequestBody Map<String, Object> settings) {
        String userId = getCurrentUserId();
        log.info("更新用户设置: {}", userId);
        // TODO: 实现用户设置功能
        return ApiResponse.success(Map.of("settings", "暂未实现"));
    }
    
    /**
     * 用户模式切换
     */
    @PostMapping("/mode")
    public ApiResponse<UserModeResponse> switchMode(@RequestBody Map<String, String> request) {
        String userId = getCurrentUserId();
        String mode = request.get("mode");
        log.info("切换用户模式: {} -> {}", userId, mode);
        UserModeResponse response = userService.switchUserMode(userId, mode);
        return ApiResponse.success(response);
    }
    
    @GetMapping("/mode")
    public ApiResponse<UserModeResponse> getMode() {
        String userId = getCurrentUserId();
        log.info("获取用户模式: {}", userId);
        UserModeResponse response = userService.getUserMode(userId);
        return ApiResponse.success(response);
    }
    
    /**
     * 获取今日能量信息
     */
    @GetMapping("/today-energy")
    @Operation(summary = "获取今日能量信息", description = "获取用户当日的能量解读信息（仅能量模式用户可用）")
    public ApiResponse<TodayEnergyResponse> getTodayEnergy(
            @RequestParam(value = "date", required = false) String date) {
        String userId = getCurrentUserId();
        log.info("获取今日能量信息: userId={}, date={}", userId, date);
        
        try {
            TodayEnergyResponse response = userService.getTodayEnergy(userId, date);
            return ApiResponse.success(response);
        } catch (BusinessException e) {
            log.warn("获取今日能量信息失败: userId={}, error={}", userId, e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("获取今日能量信息发生异常: userId={}", userId, e);
            throw new BusinessException("3003", "能量计算服务异常");
        }
    }
    
    /**
     * 获取今日能量简要信息
     */
    @GetMapping("/today-energy-brief")
    @Operation(summary = "获取今日能量简要信息", description = "获取用户当日的简化能量信息（仅能量模式用户可用），适用于首页展示或小卡片场景")
    public ApiResponse<TodayEnergyBriefResponse> getTodayEnergyBrief(
            @RequestParam(value = "date", required = false) String date) {
        
        String userId = getCurrentUserId();
        log.info("获取今日能量简要信息: userId={}, date={}", userId, date);
        
        try {
            TodayEnergyBriefResponse response = userService.getTodayEnergyBrief(userId, date);
            return ApiResponse.success(response);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取今日能量简要信息时发生异常: userId={}", userId, e);
            throw new BusinessException("3003", "能量计算服务异常");
        }
    }

    /**
     * 获取用户完整运势解读
     */
    @GetMapping("/fortune/complete-reading")
    @Operation(summary = "获取用户完整运势解读", description = "获取用户的完整运势解读信息，包含八字组合、五行分析、整体运势、吉运建议和详细运势解读")
    public ApiResponse<CompleteFortuneReadingResponse> getCompleteFortuneReading(
            @RequestParam(value = "date", required = false) String date) {
        
        String userId = getCurrentUserId();
        log.info("获取用户完整运势解读: userId={}, date={}", userId, date);
        
        try {
            CompleteFortuneReadingResponse response = fortuneReadingService.getCompleteFortuneReading(userId, date);
            return ApiResponse.success(response);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取用户完整运势解读时发生异常: userId={}", userId, e);
            throw new BusinessException("3003", "能量计算服务异常");
        }
    }

    /**
     * 获取天气信息
     */
    @GetMapping("/weather")
    @Operation(summary = "获取天气信息", description = "根据城市名称或经纬度获取天气信息，同时更新用户居住地区")
    public ApiResponse<WeatherResponse> getWeather(
            @RequestParam(value = "city", required = false) String city,
            @RequestParam(value = "longitude", required = false) Double longitude,
            @RequestParam(value = "latitude", required = false) Double latitude) {
        
        String userId = getCurrentUserId();
        log.info("获取天气信息: userId={}, city={}, longitude={}, latitude={}", userId, city, longitude, latitude);
        
        if (aiServiceClient == null) {
            log.warn("AI服务客户端未配置");
            return ApiResponse.error(5000, "天气服务暂时不可用");
        }
        
        try {
            // 调用AI服务获取天气信息
            Map<String, Object> aiResponse = aiServiceClient.getWeather(city, longitude, latitude);
            
            // 检查AI服务响应
            Object codeObj = aiResponse.get("code");
            String message = (String) aiResponse.get("message");
            Object data = aiResponse.get("data");
            
            if (codeObj != null && "200".equals(codeObj.toString()) && data != null) {
                // 解析天气数据
                @SuppressWarnings("unchecked")
                Map<String, Object> weatherData = (Map<String, Object>) data;
                
                // 构建WeatherResponse
                WeatherResponse response = buildWeatherResponse(weatherData);
                
                // 更新用户居住地区信息
                updateUserResidence(userId, response);
                
                log.info("天气信息获取成功: userId={}, location={}", userId, 
                        response.getLocation() != null ? response.getLocation().getName() : "未知");
                return ApiResponse.success(response);
                
            } else {
                String errorMsg = message != null ? message : "天气查询失败";
                log.warn("AI服务返回错误: code={}, message={}", codeObj, errorMsg);
                return ApiResponse.error(5001, errorMsg);
            }
            
        } catch (Exception e) {
            log.error("获取天气信息失败: userId={}", userId, e);
            return ApiResponse.error(5000, "天气查询失败: " + e.getMessage());
        }
    }

    /**
     * 构建天气响应对象
     */
    @SuppressWarnings("unchecked")
    private WeatherResponse buildWeatherResponse(Map<String, Object> weatherData) {
        WeatherResponse response = new WeatherResponse();
        
        response.setSuccess((Boolean) weatherData.get("success"));
        response.setDataSource((String) weatherData.get("dataSource"));
        response.setTimestamp((Long) weatherData.get("timestamp"));
        response.setLastUpdate((String) weatherData.get("lastUpdate"));
        response.setErrorMessage((String) weatherData.get("errorMessage"));
        
        // 构建位置信息
        Map<String, Object> locationData = (Map<String, Object>) weatherData.get("location");
        if (locationData != null) {
            WeatherResponse.WeatherLocation location = new WeatherResponse.WeatherLocation();
            location.setId((String) locationData.get("id"));
            location.setName((String) locationData.get("name"));
            location.setCountry((String) locationData.get("country"));
            location.setPath((String) locationData.get("path"));
            location.setTimezone((String) locationData.get("timezone"));
            location.setTimezoneOffset((String) locationData.get("timezoneOffset"));
            location.setProvince((String) locationData.get("province"));
            location.setAdcode((String) locationData.get("adcode"));
            response.setLocation(location);
        }
        
        // 构建当前天气
        Map<String, Object> currentData = (Map<String, Object>) weatherData.get("current");
        if (currentData != null) {
            response.setCurrent(buildWeatherData(currentData));
        }
        
        // 构建预报天气
        List<Map<String, Object>> forecastList = (List<Map<String, Object>>) weatherData.get("forecast");
        if (forecastList != null && !forecastList.isEmpty()) {
            List<WeatherResponse.WeatherData> forecast = forecastList.stream()
                    .map(this::buildWeatherData)
                    .collect(java.util.stream.Collectors.toList());
            response.setForecast(forecast);
        }
        
        return response;
    }

    /**
     * 构建天气数据对象
     */
    private WeatherResponse.WeatherData buildWeatherData(Map<String, Object> data) {
        WeatherResponse.WeatherData weatherData = new WeatherResponse.WeatherData();
        
        weatherData.setText((String) data.get("text"));
        weatherData.setCode((String) data.get("code"));
        weatherData.setTemperature((String) data.get("temperature"));
        weatherData.setHighTemperature((String) data.get("highTemperature"));
        weatherData.setLowTemperature((String) data.get("lowTemperature"));
        weatherData.setWindDirection((String) data.get("windDirection"));
        weatherData.setWindPower((String) data.get("windPower"));
        weatherData.setWindSpeed((String) data.get("windSpeed"));
        weatherData.setHumidity((String) data.get("humidity"));
        weatherData.setPressure((String) data.get("pressure"));
        weatherData.setVisibility((String) data.get("visibility"));
        weatherData.setUvIndex((String) data.get("uvIndex"));
        weatherData.setPrecipitation((String) data.get("precipitation"));
        weatherData.setDate((String) data.get("date"));
        weatherData.setWeek((String) data.get("week"));
        weatherData.setDayWeather((String) data.get("dayWeather"));
        weatherData.setNightWeather((String) data.get("nightWeather"));
        weatherData.setDayTemperature((String) data.get("dayTemperature"));
        weatherData.setNightTemperature((String) data.get("nightTemperature"));
        
        return weatherData;
    }

    /**
     * 更新用户居住地区信息
     */
    private void updateUserResidence(String userId, WeatherResponse weatherResponse) {
        try {
            if (weatherResponse.getLocation() != null && weatherResponse.getLocation().getName() != null) {
                String residenceName = weatherResponse.getLocation().getName();
                String residenceCode = weatherResponse.getLocation().getAdcode();
                
                // 异步更新用户居住地区信息
                userService.updateUserResidence(userId, residenceName, residenceCode);
                log.info("用户居住地区信息已更新: userId={}, residenceName={}, residenceCode={}", 
                        userId, residenceName, residenceCode);
            }
        } catch (Exception e) {
            log.warn("更新用户居住地区信息失败: userId={}, error={}", userId, e.getMessage());
            // 不影响主要功能，仅记录警告日志
        }
    }
    
    /**
     * 获取当前用户ID（增强版）
     * 支持多种获取方式：
     * 1. SecurityContext中的Authentication（JWT认证后设置）
     * 2. 网关传递的头部信息（X-Authenticated-User-Id）
     * 3. 直接从Authorization头部解析JWT
     */
    private String getCurrentUserId() {
        // 方式1: 从SecurityContext获取（过滤器设置的）
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && 
            authentication.isAuthenticated() && 
            !"anonymousUser".equals(authentication.getName())) {
            log.debug("从SecurityContext获取用户ID: {}", authentication.getName());
            return authentication.getName();
        }
        
        // 方式2: 从网关传递的头部获取
        try {
            HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes()).getRequest();
            String gatewayUserId = request.getHeader("X-Authenticated-User-Id");
            if (StringUtils.hasText(gatewayUserId)) {
                log.debug("从网关头部获取用户ID: {}", gatewayUserId);
                return gatewayUserId;
            }
            
            // 方式3: 直接从Authorization头部解析JWT（备用方案）
            String authHeader = request.getHeader("Authorization");
            if (StringUtils.hasText(authHeader) && authHeader.startsWith("Bearer ")) {
                String token = authHeader.substring(7);
                if (jwtService.validateToken(token)) {
                    String userId = jwtService.extractUserId(token);
                    log.debug("从JWT token获取用户ID: {}", userId);
                    return userId;
                }
            }
        } catch (Exception e) {
            log.warn("获取用户ID时发生异常: {}", e.getMessage());
        }
        
        // 如果都获取不到，抛出异常
        throw new BusinessException("1001", "无法获取当前用户信息，请先登录");
    }

    /**
     * 人物图片识别接口
     */
    @PostMapping("/analyze-body-shape")
    public ApiResponse<BodyShapeAnalysisResponse> analyzeBodyShape(@RequestParam("file") MultipartFile file) {
        String userId = getCurrentUserId();
        log.info("分析人物体型: 用户ID={}, 文件名={}", userId, file.getOriginalFilename());
        
        if (aiServiceClient == null) {
            log.warn("AI服务客户端未配置");
            return ApiResponse.error(6001, "AI服务暂时不可用");
        }
        
        try {
            // 调用AI服务进行身材识别
            Map<String, Object> aiResponse = aiServiceClient.analyzeBodyShape(file, userId);
            
            // 解析AI服务响应
            Object codeObj = aiResponse.get("code");
            int code = codeObj != null ? Convert.toInt(codeObj.toString())  : 9999;
            
            if ( 200==code ) {
                // 成功：提取data字段作为返回数据
                Object data = aiResponse.get("data");
                log.info("AI服务调用成功，用户: {}", userId);
                
                // 这里需要将Map转换为BodyShapeAnalysisResponse
                // 但由于这个接口不更新用户信息，所以暂时使用简化的转换
                // 建议后续统一将这个逻辑也移到Service层
                @SuppressWarnings("unchecked")
                Map<String, Object> dataMap = (Map<String, Object>) data;
                
                BodyShapeAnalysisResponse response = BodyShapeAnalysisResponse.builder()
                        .isFullBodyPhoto((Boolean) dataMap.get("isFullBodyPhoto"))
                        .confidence(dataMap.get("confidence") != null ? 
                                ((Number) dataMap.get("confidence")).doubleValue() : 0.0)
                        .bodyType((String) dataMap.get("bodyType"))
                        .reason((String) dataMap.get("reason"))
                        .imageUrl((String) dataMap.get("imageUrl"))
                        .userInfoUpdated(false) // 此接口不更新用户信息
                        .updateMessage("未执行用户信息更新")
                        .build();
                
                return ApiResponse.success(response);
            } else {
                // 失败：提取message字段作为错误信息
                String message = (String) aiResponse.get("message");
                log.warn("AI服务返回错误，用户: {}, code: {}, message: {}", userId, code, message);
                return ApiResponse.error(code, message != null ? message : "身材识别失败");
            }
            
        } catch (Exception e) {
            log.error("调用AI服务失败", e);
            return ApiResponse.error(6001, "身材识别服务暂时不可用: " + e.getMessage());
        }
    }

    /**
     * 基于文件ID的人物图片识别接口
     */
    @PostMapping("/analyze-body-shape-by-file-id")
    public ApiResponse<BodyShapeAnalysisResponse> analyzeBodyShapeByFileId(@Valid @RequestBody BodyShapeAnalysisRequest request) {
        String userId = getCurrentUserId();
        String fileId = request.getFileId();
        log.info("分析人物体型（基于文件ID）: 用户ID={}, 文件ID={}", userId, fileId);
        
        try {
            // 调用UserService的完整业务流程
            BodyShapeAnalysisResponse result = userService.analyzeAndUpdateBodyShape(userId, fileId);
            
            return ApiResponse.success(result);
            
        } catch (Exception e) {
            log.error("身材分析接口调用失败，用户: {}, 文件ID: {}", userId, fileId, e);
            return ApiResponse.error(6001, "服务暂时不可用: " + e.getMessage());
        }
    }
} 