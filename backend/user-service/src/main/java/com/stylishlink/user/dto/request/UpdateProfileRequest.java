package com.stylishlink.user.dto.request;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 更新用户信息请求
 */
@Data
public class UpdateProfileRequest {
    private String nickname;
    private String avatar;
    private Integer gender;
    private String photoUrl;
    private Integer height;
    private Integer weight;
    private String bodyType;
    private String skinTone;
    private List<String> stylePreferences;
    private Map<String, String> bodyShape;
    private String mode;
    private String residenceName;     // 居住地点中文名称
    private String residenceCode;     // 居住地点地区代码
    private String fullName;
    private String birthDate;
    private String birthTime;
    private String birthPlace;
} 