package com.stylishlink.user.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 用户实体类
 */
@TableName(value = "user_info", autoResultMap = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class User {
    @TableId(type = IdType.AUTO)
    private Long id;
    
    @TableField("user_id")
    private String userId;
    
    @TableField("open_id")
    private String openId;
    
    @TableField("union_id")
    private String unionId;
    
    @TableField("nickname")
    private String nickname;
    
    @TableField("avatar")
    private String avatar;
    
    @TableField("gender")
    private Integer gender; // 1-男 2-女 0-未知
    
    @TableField("phone")
    private String phone;
    
    @TableField("email")
    private String email;
    
    @TableField("password_hash")
    private String passwordHash; // 密码哈希（新登录方式下可能不需要）
    
    @TableField("register_date")
    private Date registerDate;
    
    @TableField("last_login_date")
    private Date lastLoginDate;
    
    // 新增字段 - 基础信息
    @TableField("photo_url")
    private String photoUrl;        // 全身照URL
    
    @TableField("height")
    private Integer height;         // 身高(cm)
    
    @TableField("weight")
    private Integer weight;         // 体重(kg)
    
    @TableField("body_type")
    private String bodyType;        // 体型（标准/偏瘦/偏胖）
    
    @TableField("skin_tone")
    private String skinTone;        // 肤色
    
    @TableField(value = "style_preferences", typeHandler = JacksonTypeHandler.class)
    private List<String> stylePreferences; // 风格偏好
    
    @TableField(value = "body_shape", typeHandler = JacksonTypeHandler.class)
    private Map<String, String> bodyShape; // 体型细分
    
    @TableField("mode")
    private String mode;            // 用户模式（natural/energy）
    
    // 新增字段 - 居住地点信息
    @TableField("residence_name")
    private String residenceName;   // 居住地点中文名称
    
    @TableField("residence_code")
    private String residenceCode;   // 居住地点地区代码
    
    // 新增字段 - 五行命理信息
    @TableField("full_name")
    private String fullName;        // 姓名
    
    @TableField("birth_date")
    private String birthDate;       // 出生日期
    
    @TableField("birth_time")
    private String birthTime;       // 出生时间
    
    @TableField("birth_place")
    private String birthPlace;      // 出生地点
    
    @TableField(value = "preferences", typeHandler = JacksonTypeHandler.class)
    private UserPreferences preferences;
    
    @TableField(value = "body_info", typeHandler = JacksonTypeHandler.class)
    private UserBodyInfo bodyInfo;
    
    @TableField(value = "wuxing_profile", typeHandler = JacksonTypeHandler.class)
    private WuxingProfile wuxingProfile;
    
    @TableField(value = "settings", typeHandler = JacksonTypeHandler.class)
    private UserSettings settings;
    
    @TableField(value = "stats", typeHandler = JacksonTypeHandler.class)
    private UserStats stats;
    
    @TableField(value = "extensions", typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> extensions; // 扩展字段
    
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;
    
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
    
    /**
     * 判断是否已完善基础信息
     */
    public boolean hasBasicInfo() {
        return height != null && weight != null && bodyType != null && 
               skinTone != null && stylePreferences != null && !stylePreferences.isEmpty();
    }
    
    /**
     * 判断是否已完善五行信息
     * 新逻辑：如果"姓名"、"生日"、"出生时间"、"出生地点"、"性别"这5个字段都有值，则认为有五行信息
     * 同时保持原有逻辑兼容性：如果有实际的八字计算结果，也认为有五行信息
     */
    public boolean hasWuxingInfo() {
        // 1. 检查5个关键字段是否都有值
        boolean hasRequiredFields = fullName != null && !fullName.trim().isEmpty() &&
                                   birthDate != null && !birthDate.trim().isEmpty() &&
                                   birthTime != null && !birthTime.trim().isEmpty() &&
                                   birthPlace != null && !birthPlace.trim().isEmpty() &&
                                   gender != null;
        
        if (hasRequiredFields) {
            return true;
        }
        
        // 2. 兼容性检查：如果有实际的八字计算结果
        if (wuxingProfile != null) {
            return (wuxingProfile.getEightChar() != null && !wuxingProfile.getEightChar().trim().isEmpty()) ||
                   (wuxingProfile.getBaziDetail() != null) ||
                   (wuxingProfile.getElementAnalysis() != null);
        }
        
        return false;
    }
    
    /**
     * 用户偏好
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UserPreferences {
        private List<String> favoriteColors;
        private List<String> favoriteStyles;
        private List<String> dislikedItems;
        private Map<String, List<String>> seasonalPreferences;
        private Map<String, List<String>> occasionPreferences;
    }
    
    /**
     * 用户身体信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UserBodyInfo {
        private Integer height;
        private Integer weight;
        private String bodyShape; // 身形类型
        private String skinTone; // 肤色
        private Map<String, Integer> bodyProportions;
    }
    
    /**
     * 五行命理信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class WuxingProfile {
        private Date birthDate;
        private LunarBirth lunarBirth;
        private ElementAnalysis elementAnalysis;
        private List<String> favorable;
        private List<String> unfavorable;
        private String eightChar;  // 完整八字字符串，如："庚午 戊子 己巳 壬申"
        
        // 新增：详细八字字段
        private BaziDetail baziDetail;
        
        private Date updated;
        
        @Data
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        public static class LunarBirth {
            private Integer year;
            private Integer month;
            private Integer day;
            private String animal;
            private String element;
        }
        
        @Data
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        public static class ElementAnalysis {
            private Map<String, Integer> elementScores;
            private String dominantElement;
            private String weakestElement;
            private List<String> balancingElements;
        }
        
        /**
         * 八字详细信息
         */
        @Data
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        @com.fasterxml.jackson.annotation.JsonIgnoreProperties(ignoreUnknown = true)
        public static class BaziDetail {
            private String yearPillar;     // 年柱，如："庚子"
            private String monthPillar;    // 月柱，如："戊子"  
            private String dayPillar;      // 日柱，如："甲寅"
            private String hourPillar;     // 时柱，如："丙寅"
            private String dayMaster;      // 日主，如："甲"
            private List<String> elements; // 五行元素，如：["木", "金", "土"]
            
            // 新增：详细信息字段
            private DetailInfo detailInfo;
        }
        
        /**
         * 八字详细信息对象
         */
        @Data
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        @com.fasterxml.jackson.annotation.JsonIgnoreProperties(ignoreUnknown = true)
        public static class DetailInfo {
            private PillarDetail yearPillarDetail;    // 年柱详细信息
            private PillarDetail monthPillarDetail;   // 月柱详细信息
            private PillarDetail dayPillarDetail;     // 日柱详细信息
            private PillarDetail hourPillarDetail;    // 时柱详细信息
            private WuxingStatistics wuxingStatistics; // 五行统计信息
        }
        
        /**
         * 柱详细信息对象
         */
        @Data
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        @com.fasterxml.jackson.annotation.JsonIgnoreProperties(ignoreUnknown = true)
        public static class PillarDetail {
            private HeavenlyStemInfo heavenlyStem;    // 天干信息
            private EarthlyBranchInfo earthlyBranch;  // 地支信息
        }
        
        /**
         * 天干信息
         */
        @Data
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        @com.fasterxml.jackson.annotation.JsonIgnoreProperties(ignoreUnknown = true)
        public static class HeavenlyStemInfo {
            private String character;  // 天干字符 (如"甲")
            private String element;    // 五行属性 (金木水火土)
        }
        
        /**
         * 地支信息
         */
        @Data
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        @com.fasterxml.jackson.annotation.JsonIgnoreProperties(ignoreUnknown = true)
        public static class EarthlyBranchInfo {
            private String character;          // 地支字符 (如"子")
            private String element;            // 五行属性 (金木水火土)
            private List<HiddenStemInfo> hiddenStems; // 藏干信息列表
        }
        
        /**
         * 藏干信息
         */
        @Data
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        @com.fasterxml.jackson.annotation.JsonIgnoreProperties(ignoreUnknown = true)
        public static class HiddenStemInfo {
            private String character;  // 藏干字符 (如"癸")
            private String element;    // 五行属性 (金木水火土)
            private String strength;   // 强度 (主气/中气/余气)
        }
        
        /**
         * 五行统计信息
         */
        @Data
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        @com.fasterxml.jackson.annotation.JsonIgnoreProperties(ignoreUnknown = true)
        public static class WuxingStatistics {
            private Map<String, Integer> distribution; // 五行分布统计
            private String dominantElement;           // 主导五行
            private String weakestElement;            // 最弱五行
            private Integer totalCount;               // 总计数（含藏干）
        }
    }
    
    /**
     * 用户设置
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UserSettings {
        private Boolean pushNotification;
        private Boolean emailNotification;
        private Boolean showWuxingInfo;
        private String language;
        private String theme;
        private Map<String, Boolean> privacySettings;
    }
    
    /**
     * 用户统计
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UserStats {
        private Integer clothingCount;
        private Integer accessoryCount;
        private Integer outfitCount;
        private Integer favoriteOutfitCount;
        private Integer recommendationCount;
        private Date lastOutfitCreated;
        private Date lastRecommendation;
    }
} 