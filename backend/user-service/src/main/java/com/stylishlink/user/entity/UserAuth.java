package com.stylishlink.user.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 用户认证实体类
 */
@TableName("user_auth")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserAuth {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    @TableField("user_id")
    private String userId;
    
    @TableField("token")
    private String token;
    
    @TableField(value = "refresh_token", insertStrategy = FieldStrategy.IGNORED, updateStrategy = FieldStrategy.IGNORED)
    private String refreshToken;
    
    @TableField("expires_at")
    private Date expiresAt;
    
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private Date createdAt;
} 