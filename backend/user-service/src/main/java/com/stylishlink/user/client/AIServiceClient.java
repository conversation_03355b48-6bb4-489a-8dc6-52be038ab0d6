package com.stylishlink.user.client;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

/**
 * AI服务客户端接口
 * 直接指定URL，无需负载均衡器
 */
@FeignClient(name = "ai-service", url = "${ai-service.url:http://localhost:8084}", path = "/api/ai")
@ConditionalOnProperty(name = "ai-service.enabled", havingValue = "true", matchIfMissing = false)
public interface AIServiceClient {
    
    /**
     * 计算八字
     * @param request 计算请求
     * @return 八字结果
     */
    @PostMapping("/bazi/calculate")
    Map<String, Object> calculateBaZi(@RequestBody Map<String, Object> request);
    
    /**
     * 分析五行
     * @param request 分析请求
     * @return 五行分析结果
     */
    @PostMapping("/wuxing/analyze")
    Map<String, Object> analyzeWuxing(@RequestBody Map<String, Object> request);

    /**
     * 计算今日能量
     * @param request 能量计算请求
     * @return 今日能量结果
     */
    @PostMapping("/energy/today")
    Map<String, Object> calculateTodayEnergy(@RequestBody Map<String, Object>  request);

    /**
     * 身材识别分析
     * @param file 图片文件
     * @param userId 用户ID（可选）
     * @return 身材识别结果
     */
    @PostMapping(value = "/analyze-body-shape", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    Map<String, Object> analyzeBodyShape(@RequestPart("file") MultipartFile file,
                                         @RequestParam(value = "userId", required = false) String userId);

    /**
     * 基于图片URL的身材识别分析
     * @param imageUrl 图片URL
     * @param userId 用户ID（可选）
     * @param fileId 文件ID（可选）
     * @return 身材识别结果
     */
    @PostMapping("/analyze-body-shape-by-url")
    Map<String, Object> analyzeBodyShapeByUrl(@RequestParam("imageUrl") String imageUrl,
                                              @RequestParam(value = "userId", required = false) String userId,
                                              @RequestParam(value = "fileId", required = false) String fileId);

    /**
     * 基于文件ID的身材识别分析
     * @param fileId 文件ID
     * @param userId 用户ID（可选）
     * @return 身材识别结果
     */
    @PostMapping("/analyze-body-shape-by-file-id")
    Map<String, Object> analyzeBodyShapeByFileId(@RequestParam("fileId") String fileId,
                                                  @RequestParam(value = "userId", required = false) String userId);

    /**
     * 简化总结幸运元素文案
     * @param request 文案简化请求，包含clothing、accessories、makeup列表
     * @return 简化后的总结文案
     */
    @PostMapping("/text/summarize-lucky-elements")
    Map<String, Object> summarizeLuckyElements(@RequestBody Map<String, Object> request);

    /**
     * 生成完整运势解读
     * @param request 运势分析请求，包含用户八字信息、五行统计等
     * @return 完整运势解读结果，包含五行分析、整体运势、吉运建议、详细运势
     */
    @PostMapping("/fortune/complete-analysis")
    Map<String, Object> generateCompleteFortuneReading(@RequestBody Map<String, Object> request);

    /**
     * 获取天气信息
     * @param city 城市名称（可选）
     * @param longitude 经度（可选）
     * @param latitude 纬度（可选）
     * @return 天气信息
     */
    @GetMapping("/weather")
    Map<String, Object> getWeather(@RequestParam(value = "city", required = false) String city,
                                   @RequestParam(value = "longitude", required = false) Double longitude,
                                   @RequestParam(value = "latitude", required = false) Double latitude);
    /**
     * 返回数据样例子
     * {
     *     "code": 200,
     *     "data": {
     *         "wuxingSummary": {
     *             "summary": "喜用土，宜着黄色、棕色，忌用木，忌着绿色、青色",
     *             "favorableElements": [
     *                 "土"
     *             ],
     *             "unfavorableColors": [
     *                 "绿色",
     *                 "青色"
     *             ],
     *             "unfavorableElements": [
     *                 "木"
     *             ],
     *             "favorableColors": [
     *                 "黄色",
     *                 "棕色"
     *             ]
     *         },
     *         "luckyAdvice": {
     *             "jewelry": {
     *                 "title": "首饰佩戴",
     *                 "items": [
     *                     {
     *                         "advice": "佩戴温润的玉石或木质饰品",
     *                         "label": "材质推荐",
     *                         "type": "recommend"
     *                     }
     *                 ]
     *             },
     *             "fengshui": {
     *                 "title": "居家风水",
     *                 "items": [
     *                     {
     *                         "advice": "在家中摆放绿色植物，增强生气",
     *                         "label": "植物摆放",
     *                         "type": "recommend"
     *                     },
     *                     {
     *                         "advice": "注意睡床方向，有助于运势调和",
     *                         "label": "方位调整",
     *                         "type": "recommend"
     *                     }
     *                 ]
     *             },
     *             "clothing": {
     *                 "title": "服装选择",
     *                 "items": [
     *                     {
     *                         "advice": "多选择温和色调，避免过于鲜艳的颜色",
     *                         "label": "颜色搭配",
     *                         "type": "recommend"
     *                     },
     *                     {
     *                         "advice": "选择天然面料，如棉麻，有助于运势提升",
     *                         "label": "面料选择",
     *                         "type": "recommend"
     *                     }
     *                 ]
     *             }
     *         },
     *         "overallFortune": {
     *             "currentAge": 28,
     *             "decadeFortunes": [
     *                 {
     *                     "ageRange": "0-9岁",
     *                     "score": 78,
     *                     "decade": 1,
     *                     "yearRange": "1995-2004",
     *                     "keyEvents": [
     *                         "启蒙教育",
     *                         "性格养成"
     *                     ],
     *                     "description": "童年时期，身体健康，家庭和睦，为未来发展奠定良好基础。",
     *                     "theme": "成长奠基"
     *                 },
     *                 {
     *                     "ageRange": "10-19岁",
     *                     "score": 79,
     *                     "decade": 2,
     *                     "yearRange": "2005-2014",
     *                     "keyEvents": [
     *                         "学业成就",
     *                         "才能显现"
     *                     ],
     *                     "description": "学业运势良好，智慧开启，有贵人相助。建议专注学习，培养兴趣。",
     *                     "theme": "求学成长"
     *                 },
     *                 {
     *                     "ageRange": "20-29岁",
     *                     "score": 75,
     *                     "decade": 3,
     *                     "yearRange": "2015-2024",
     *                     "keyEvents": [
     *                         "职场初入",
     *                         "人际拓展"
     *                     ],
     *                     "description": "踏入社会，事业起步，虽有挑战但机遇并存。需积极进取，把握机会。",
     *                     "theme": "事业起步"
     *                 },
     *                 {
     *                     "ageRange": "30-39岁",
     *                     "score": 87,
     *                     "decade": 4,
     *                     "yearRange": "2025-2034",
     *                     "keyEvents": [
     *                         "事业突破",
     *                         "感情收获"
     *                     ],
     *                     "description": "事业发展期，运势上升，有重要突破。感情生活也将有所收获。",
     *                     "theme": "发展建设"
     *                 },
     *                 {
     *                     "ageRange": "40-49岁",
     *                     "score": 93,
     *                     "decade": 5,
     *                     "yearRange": "2035-2044",
     *                     "keyEvents": [
     *                         "事业巅峰",
     *                         "财富积累"
     *                     ],
     *                     "description": "人生巅峰期，事业财运双收，家庭美满。是人生最辉煌的十年。",
     *                     "theme": "事业巅峰"
     *                 },
     *                 {
     *                     "ageRange": "50-59岁",
     *                     "score": 95,
     *                     "decade": 6,
     *                     "yearRange": "2045-2054",
     *                     "keyEvents": [
     *                         "家庭稳定",
     *                         "事业成熟"
     *                     ],
     *                     "description": "成熟稳定期，事业稳固，财富积累。注重健康，享受家庭生活。",
     *                     "theme": "成熟稳定"
     *                 },
     *                 {
     *                     "ageRange": "60-69岁",
     *                     "score": 75,
     *                     "decade": 7,
     *                     "yearRange": "2055-2064",
     *                     "keyEvents": [
     *                         "智慧传承",
     *                         "声望提升"
     *                     ],
     *                     "description": "智慧收获期，经验丰富，德高望重。可考虑传承知识，培养后进。",
     *                     "theme": "收获智慧"
     *                 },
     *                 {
     *                     "ageRange": "70-79岁",
     *                     "score": 87,
     *                     "decade": 8,
     *                     "yearRange": "2065-2074",
     *                     "keyEvents": [
     *                         "天伦之乐",
     *                         "健康养生"
     *                     ],
     *                     "description": "传承经验期，享受天伦之乐，关注健康养生。运势平稳安康。",
     *                     "theme": "传承经验"
     *                 },
     *                 {
     *                     "ageRange": "80-89岁",
     *                     "score": 57,
     *                     "decade": 9,
     *                     "yearRange": "2075-2084",
     *                     "keyEvents": [
     *                         "安享晚年",
     *                         "儿孙满堂"
     *                     ],
     *                     "description": "享受人生期，儿孙满堂，生活安逸。运势平和，健康是重点。",
     *                     "theme": "享受人生"
     *                 },
     *                 {
     *                     "ageRange": "90-99岁",
     *                     "score": 53,
     *                     "decade": 10,
     *                     "yearRange": "2085-2094",
     *                     "keyEvents": [
     *                         "人生圆满",
     *                         "长寿安康"
     *                     ],
     *                     "description": "圆满安康期，人生圆满，长寿安康。晚年生活幸福美满。",
     *                     "theme": "圆满安康"
     *                 }
     *             ],
     *             "lifePhase": "探索期",
     *             "currentDecade": 3,
     *             "lifetimeOverview": "综合八字分析，您的一生运势整体呈稳步上升趋势，中年期达到人生巅峰，晚年平稳安康。重要转折点出现在30-40岁期间，需把握机遇。"
     *         },
     *         "detailedFortune": {
     *             "monthly": {
     *                 "wealth": {
     *                     "highlight": "量入为出",
     *                     "color": "#FF9800",
     *                     "content": "收入稳定，理财有道",
     *                     "status": "财运平稳"
     *                 },
     *                 "career": {
     *                     "highlight": "贵人相助",
     *                     "color": "#4CAF50",
     *                     "content": [
     *                         "工作运势整体向好",
     *                         "有新的机会出现"
     *                     ],
     *                     "status": "稳步发展"
     *                 },
     *                 "children": {
     *                     "highlight": "教育有方",
     *                     "color": "#9C27B0",
     *                     "content": [
     *                         "与子女关系良好",
     *                         "注重品格培养"
     *                     ],
     *                     "status": "亲子融洽"
     *                 },
     *                 "marriage": {
     *                     "highlight": "沟通顺畅",
     *                     "color": "#E91E63",
     *                     "content": "感情生活温馨美满",
     *                     "status": "感情和睦"
     *                 },
     *                 "health": {
     *                     "highlight": "注意休息",
     *                     "color": "#8BC34A",
     *                     "content": [
     *                         "身体状况良好",
     *                         "保持规律作息"
     *                     ],
     *                     "status": "身体健康"
     *                 }
     *             },
     *             "yearly": {
     *                 "wealth": {
     *                     "highlight": "理财有道",
     *                     "color": "#4CAF50",
     *                     "content": "收入来源多样化",
     *                     "status": "财源广进"
     *                 },
     *                 "career": {
     *                     "highlight": "机遇显现",
     *                     "color": "#2196F3",
     *                     "content": [
     *                         "年度事业运势良好",
     *                         "把握发展机会"
     *                     ],
     *                     "status": "事业进展"
     *                 },
     *                 "children": {
     *                     "highlight": "天赋发展",
     *                     "color": "#673AB7",
     *                     "content": [
     *                         "子女发展良好",
     *                         "给予适当引导"
     *                     ],
     *                     "status": "成长顺利"
     *                 },
     *                 "marriage": {
     *                     "highlight": "家庭美满",
     *                     "color": "#F44336",
     *                     "content": "感情生活更加充实",
     *                     "status": "感情深化"
     *                 },
     *                 "health": {
     *                     "highlight": "预防为主",
     *                     "color": "#009688",
     *                     "content": [
     *                         "整体健康良好",
     *                         "注意定期检查"
     *                     ],
     *                     "status": "健康平稳"
     *                 }
     *             }
     *         }
     *     },
     *     "message": "运势解读生成成功"
     * }
     */

} 