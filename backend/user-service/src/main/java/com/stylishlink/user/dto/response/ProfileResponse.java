package com.stylishlink.user.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 用户档案操作响应实体
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProfileResponse {
    
    /**
     * 操作是否成功
     */
    private Boolean success;
    
    /**
     * 响应消息
     */
    private String message;
    
    /**
     * 用户是否有基本信息
     */
    private Boolean hasBasicInfo;
    
    /**
     * 用户是否有五行信息
     */
    private Boolean hasWuxingInfo;
    
    /**
     * 更新后的用户信息
     */
    private UserResponse userInfo;
} 