package com.stylishlink.user.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.stylishlink.user.entity.UserDailyEnergy;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Optional;

/**
 * 用户每日能量信息仓库接口
 */
@Mapper
public interface UserDailyEnergyRepository extends BaseMapper<UserDailyEnergy> {
    
    /**
     * 根据用户ID和日期查询能量信息
     * @param userId 用户ID
     * @param energyDate 能量日期（格式：YYYY-MM-DD）
     * @return 能量信息
     */
    default Optional<UserDailyEnergy> findByUserIdAndDate(String userId, String energyDate) {
        UserDailyEnergy energy = selectOne(new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<UserDailyEnergy>()
                .eq("user_id", userId)
                .eq("energy_date", energyDate));
        return Optional.ofNullable(energy);
    }
    
    /**
     * 根据用户ID查询指定日期范围内的能量信息
     * @param userId 用户ID
     * @param startDate 开始日期（格式：YYYY-MM-DD）
     * @param endDate 结束日期（格式：YYYY-MM-DD）
     * @return 能量信息列表
     */
    default List<UserDailyEnergy> findByUserIdAndDateRange(String userId, String startDate, String endDate) {
        return selectList(new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<UserDailyEnergy>()
                .eq("user_id", userId)
                .ge("energy_date", startDate)
                .le("energy_date", endDate)
                .orderByDesc("energy_date"));
    }
    
    /**
     * 根据用户ID查询最近N天的能量信息
     * @param userId 用户ID
     * @param days 天数
     * @return 能量信息列表
     */
    default List<UserDailyEnergy> findRecentByUserId(String userId, int days) {
        return selectList(new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<UserDailyEnergy>()
                .eq("user_id", userId)
                .orderByDesc("energy_date")
                .last("LIMIT " + days));
    }
    
    /**
     * 检查用户在指定日期是否已有能量记录
     * @param userId 用户ID
     * @param energyDate 能量日期（格式：YYYY-MM-DD）
     * @return 是否存在
     */
    default boolean existsByUserIdAndDate(String userId, String energyDate) {
        Long count = selectCount(new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<UserDailyEnergy>()
                .eq("user_id", userId)
                .eq("energy_date", energyDate));
        return count != null && count > 0;
    }
    
    /**
     * 删除用户指定日期的能量记录
     * @param userId 用户ID
     * @param energyDate 能量日期（格式：YYYY-MM-DD）
     * @return 删除的记录数
     */
    default int deleteByUserIdAndDate(String userId, String energyDate) {
        return delete(new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<UserDailyEnergy>()
                .eq("user_id", userId)
                .eq("energy_date", energyDate));
    }
    
    /**
     * 删除用户过期的能量记录（超过指定天数）
     * @param userId 用户ID
     * @param beforeDate 指定日期之前的记录将被删除（格式：YYYY-MM-DD）
     * @return 删除的记录数
     */
    default int deleteExpiredByUserId(String userId, String beforeDate) {
        return delete(new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<UserDailyEnergy>()
                .eq("user_id", userId)
                .lt("energy_date", beforeDate));
    }
} 