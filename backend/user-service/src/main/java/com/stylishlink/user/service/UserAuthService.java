package com.stylishlink.user.service;

import com.stylishlink.user.entity.UserAuth;

import java.util.Date;
import java.util.Optional;

/**
 * 用户认证服务接口
 */
public interface UserAuthService {
    
    /**
     * 保存认证信息（不保存refresh_token）
     * @param userId 用户ID
     * @param token 认证令牌
     * @param expiresAt 过期时间
     * @return 保存的认证信息
     */
    UserAuth saveAuthInfo(String userId, String token, Date expiresAt);
    
    /**
     * 根据token查询认证信息
     * @param token 认证令牌
     * @return 认证信息
     */
    Optional<UserAuth> findByToken(String token);
    
    /**
     * 根据refresh token查询认证信息
     * @param refreshToken 刷新令牌
     * @return 认证信息
     */
    Optional<UserAuth> findByRefreshToken(String refreshToken);
    
    /**
     * 根据用户ID查询认证信息
     * @param userId 用户ID
     * @return 认证信息
     */
    Optional<UserAuth> findByUserId(String userId);
    
    /**
     * 根据用户ID删除认证信息（用于登出）
     * @param userId 用户ID
     * @return 是否删除成功
     */
    boolean removeByUserId(String userId);
    
    /**
     * 根据token删除认证信息（用于登出）
     * @param token 认证令牌
     * @return 是否删除成功
     */
    boolean removeByToken(String token);
    
    /**
     * 更新认证信息（用于token刷新，不保存refresh_token）
     * @param userId 用户ID
     * @param newToken 新认证令牌
     * @param newExpiresAt 新过期时间
     * @return 更新后的认证信息
     */
    UserAuth updateAuthInfo(String userId, String newToken, Date newExpiresAt);
} 