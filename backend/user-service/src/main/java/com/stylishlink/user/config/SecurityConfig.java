package com.stylishlink.user.config;

import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

/**
 * 安全配置
 */
@Configuration
@EnableWebSecurity
@RequiredArgsConstructor
public class SecurityConfig {
    
    private final JwtAuthenticationFilter jwtAuthenticationFilter;
    
    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        return http
                .csrf(AbstractHttpConfigurer::disable)
                .authorizeHttpRequests(auth -> auth
                        // 旧的认证接口（保持兼容）
                        .requestMatchers("/auth/**").permitAll()
                        // 新的用户接口 - 允许匿名访问发送验证码和登录
                        .requestMatchers("/user/send-code").permitAll()
                        .requestMatchers("/user/login").permitAll()
                        .requestMatchers("/user/refresh-token").permitAll()
                        .requestMatchers("/user/bazi").permitAll()
                        .requestMatchers("/user/**").permitAll()  // 临时开放所有user接口
                        // 支持带API前缀的路径
                        .requestMatchers("/api/user/send-code").permitAll()
                        .requestMatchers("/api/user/login").permitAll()
                        .requestMatchers("/api/user/refresh-token").permitAll()
                        .requestMatchers("/api/user/bazi").permitAll()
                        .requestMatchers("/api/user/**").permitAll()  // 临时开放所有user接口
                        // 健康检查和错误页面
                        .requestMatchers("/error").permitAll()
                        .requestMatchers("/health").permitAll()
                        // 监控端点
                        .requestMatchers("/actuator/**").permitAll()
                        // SpringDoc OpenAPI & Swagger UI paths
                        .requestMatchers("/v3/api-docs/**").permitAll()
                        .requestMatchers("/user/v3/api-docs/**").permitAll()
                        .requestMatchers("/swagger-ui/**").permitAll()
                        .requestMatchers("/swagger-ui.html").permitAll()
                        // 静态资源路径排除认证
                        .requestMatchers("/static/**", "/public/**", "/webjars/**", "/favicon.ico").permitAll()
                        // 其他接口需要认证
                        .anyRequest().authenticated()
                )
                .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
                // 添加JWT认证过滤器
                .addFilterBefore(jwtAuthenticationFilter, UsernamePasswordAuthenticationFilter.class)
                .build();
    }
    
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }
} 