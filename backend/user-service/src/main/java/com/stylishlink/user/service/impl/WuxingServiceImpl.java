package com.stylishlink.user.service.impl;

import com.stylishlink.user.entity.User;
import com.stylishlink.user.dto.request.WuxingRequest;
import com.stylishlink.user.dto.response.WuxingResponse;
import com.stylishlink.user.dto.response.BaziResponse;
import com.stylishlink.user.service.UserService;
import com.stylishlink.user.service.WuxingService;
import com.stylishlink.user.client.AIServiceClient;
import com.stylishlink.user.enums.BaziEnum;
import com.stylishlink.common.exception.BusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.List;
import java.util.ArrayList;
import java.util.Optional;

/**
 * 五行命理服务实现
 */
@Service
@Slf4j
public class WuxingServiceImpl implements WuxingService {
    
    private final UserService userService;
    
    @Autowired(required = false)
    private AIServiceClient aiServiceClient;
    
    public WuxingServiceImpl(UserService userService) {
        this.userService = userService;
    }
    
    @Override
    public WuxingResponse getWuxingProfile(String userId) {
        log.info("获取用户五行档案: {}", userId);
        
        try {
            User user = userService.findByUserId(userId)
                    .orElseThrow(() -> new BusinessException("USER_NOT_FOUND", "用户不存在"));
            
            boolean hasWuxingInfo = user.hasWuxingInfo();
            
            return WuxingResponse.builder()
                    .success(true)
                    .message(hasWuxingInfo ? "五行档案获取成功" : "用户暂无五行信息")
                    .fullName(user.getFullName())
                    .birthDate(user.getBirthDate())
                    .birthTime(user.getBirthTime())
                    .birthPlace(user.getBirthPlace())
                    .hasWuxingInfo(hasWuxingInfo)
                    .build();
                    
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取用户五行档案失败: {}", userId, e);
            throw new BusinessException("GET_WUXING_FAILED", "获取五行档案失败: " + e.getMessage());
        }
    }
    
    @Override
    public WuxingResponse updateWuxingProfile(String userId, WuxingRequest request) {
        log.info("更新用户五行档案: {}", userId);
        
        try {
            User user = userService.findByUserId(userId)
                    .orElseThrow(() -> new BusinessException("USER_NOT_FOUND", "用户不存在"));
            
            // 更新用户基本信息
            user.setFullName(request.getFullName());
            user.setBirthDate(request.getBirthDate());
            user.setBirthTime(request.getBirthTime());
            user.setBirthPlace(request.getBirthPlace());
            user.setUpdateTime(new Date());
            
            userService.save(user);
            
            if (aiServiceClient != null) {
                // 准备AI分析请求
                Map<String, Object> aiRequest = new HashMap<>();
                aiRequest.put("fullName", request.getFullName());
                aiRequest.put("birthDate", request.getBirthDate());
                aiRequest.put("birthTime", request.getBirthTime());
                aiRequest.put("birthPlace", request.getBirthPlace());
                aiRequest.put("gender", request.getGender());
                
                try {
                    // 调用AI服务进行五行分析
                    Map<String, Object> aiResponse = aiServiceClient.analyzeWuxing(aiRequest);
                    
                    // 解析AI响应
                    Object codeObj = aiResponse.get("code");
                    String code = codeObj != null ? codeObj.toString() : "unknown";
                    
                    if ("200".equals(code)) {
                        Object data = aiResponse.get("data");
                        if (data instanceof Map) {
                            @SuppressWarnings("unchecked")
                            Map<String, Object> analysisData = (Map<String, Object>) data;
                            WuxingResponse.WuxingAnalysis analysis = convertToWuxingAnalysis(analysisData);
                            
                            return WuxingResponse.builder()
                                    .success(true)
                                    .message("五行档案更新成功")
                                    .fullName(user.getFullName())
                                    .birthDate(user.getBirthDate())
                                    .birthTime(user.getBirthTime())
                                    .birthPlace(user.getBirthPlace())
                                    .wuxingAnalysis(analysis)
                                    .hasWuxingInfo(true)
                                    .build();
                        }
                    } else {
                        String message = (String) aiResponse.get("message");
                        log.warn("AI五行分析失败: {}", message);
                    }
                } catch (Exception e) {
                    log.warn("调用AI服务失败，仅保存基本信息: {}", e.getMessage());
                }
            }
            
            // 如果AI分析失败，仍然返回成功（仅保存基本信息）
            return WuxingResponse.builder()
                    .success(true)
                    .message("五行档案基本信息更新成功")
                    .fullName(user.getFullName())
                    .birthDate(user.getBirthDate())
                    .birthTime(user.getBirthTime())
                    .birthPlace(user.getBirthPlace())
                    .hasWuxingInfo(true)
                    .build();
                    
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("更新用户五行档案失败: {}", userId, e);
            throw new BusinessException("UPDATE_WUXING_FAILED", "更新五行档案失败: " + e.getMessage());
        }
    }
    
    @Override
    public BaziResponse calculateBaZi(String userId, WuxingRequest request) {
        log.info("计算用户八字: {}", userId);
        
        if (aiServiceClient == null) {
            throw new BusinessException("AI_SERVICE_UNAVAILABLE", "AI服务暂时不可用");
        }
        
        try {
            // 准备AI请求
            Map<String, Object> aiRequest = new HashMap<>();
            aiRequest.put("fullName", request.getFullName());
            aiRequest.put("birthDate", request.getBirthDate());
            aiRequest.put("birthTime", request.getBirthTime());
            aiRequest.put("birthPlace", request.getBirthPlace());
            aiRequest.put("gender", request.getGender());
            
            // 调用AI服务计算八字
            Map<String, Object> aiResponse = aiServiceClient.calculateBaZi(aiRequest);
            
            // 解析AI响应
            Object codeObj = aiResponse.get("code");
            String code = codeObj != null ? codeObj.toString() : "unknown";
            
            if (!"200".equals(code)) {
                String message = (String) aiResponse.get("message");
                throw new BusinessException("BAZI_CALCULATION_FAILED", 
                    message != null ? message : "八字计算失败");
            }
            
            Object data = aiResponse.get("data");
            if (!(data instanceof Map)) {
                throw new BusinessException("BAZI_DATA_FORMAT_ERROR", "八字计算返回数据格式异常");
            }
            
            @SuppressWarnings("unchecked")
            Map<String, Object> baziData = (Map<String, Object>) data;
            
            // 从baziData中提取八字结果
            BaziResponse.BaziResult baziResult = extractBaziResult(baziData);
            
            // 保存八字结果到用户信息表
            boolean saveSuccess = userService.saveBaziResult(userId, baziResult);
            if (saveSuccess) {
                log.info("八字结果已保存到用户信息表，用户: {}", userId);
            } else {
                log.warn("八字结果保存失败，但不影响接口返回，用户: {}", userId);
            }
            
            return BaziResponse.builder()
                    .success(true)
                    .message("八字计算成功")
                    .baziResult(baziResult)
                    .build();
                    
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("计算八字失败: {}", userId, e);
            throw new BusinessException("BAZI_CALCULATION_ERROR", "八字计算服务异常: " + e.getMessage());
        }
    }
    
    /**
     * 从AI返回数据中提取八字结果
     */
    private BaziResponse.BaziResult extractBaziResult(Map<String, Object> data) {
        // 首先检查是否有baziData字段
        Object baziDataObj = data.get("baziData");
        Map<String, Object> baziData;
        
        if (baziDataObj instanceof Map) {
            @SuppressWarnings("unchecked")
            Map<String, Object> baziDataMap = (Map<String, Object>) baziDataObj;
            baziData = baziDataMap;
        } else {
            // 如果没有baziData字段，直接从data中提取
            baziData = data;
        }
        
        log.info("提取八字数据: {}", baziData);
        
        // 提取基本八字信息
        String yearPillar = getStringValue(baziData, "yearPillar", "");
        String monthPillar = getStringValue(baziData, "monthPillar", "");
        String dayPillar = getStringValue(baziData, "dayPillar", "");
        String hourPillar = getStringValue(baziData, "hourPillar", "");
        
        // 日主处理：如果AI服务没有返回dayMaster，从dayPillar中提取天干
        String dayMaster = getStringValue(baziData, "dayMaster", "");
        if (dayMaster.isEmpty() && !dayPillar.isEmpty() && dayPillar.length() >= 1) {
            dayMaster = dayPillar.substring(0, 1); // 日柱的第一个字符是天干（日主）
        }
        
        // 五行元素处理：如果AI服务没有返回elements，从wuxingDistribution中提取
        List<String> elements = getListValue(baziData, "elements");
        if (elements.isEmpty()) {
            Object wuxingDist = baziData.get("wuxingDistribution");
            if (wuxingDist instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> wuxingMap = (Map<String, Object>) wuxingDist;
                elements = new ArrayList<>();
                for (String element : wuxingMap.keySet()) {
                    Object count = wuxingMap.get(element);
                    if (count instanceof Number && ((Number) count).intValue() > 0) {
                        elements.add(element);
                    }
                }
            }
        }
        
        // 生成详细信息
        BaziResponse.DetailInfo detailInfo = generateDetailInfo(yearPillar, monthPillar, dayPillar, hourPillar);
        
        return BaziResponse.BaziResult.builder()
                .yearPillar(yearPillar)
                .monthPillar(monthPillar)
                .dayPillar(dayPillar)
                .hourPillar(hourPillar)
                .dayMaster(dayMaster)
                .elements(elements)
                .detailInfo(detailInfo)
                .build();
    }
    
    /**
     * 生成详细信息，通过枚举对照表查找天干地支五行属性
     */
    private BaziResponse.DetailInfo generateDetailInfo(String yearPillar, String monthPillar, 
                                                        String dayPillar, String hourPillar) {
        
        // 生成各柱的详细信息
        BaziResponse.PillarDetail yearDetail = generatePillarDetail(yearPillar);
        BaziResponse.PillarDetail monthDetail = generatePillarDetail(monthPillar);
        BaziResponse.PillarDetail dayDetail = generatePillarDetail(dayPillar);
        BaziResponse.PillarDetail hourDetail = generatePillarDetail(hourPillar);
        
        // 生成五行统计信息
        BaziResponse.WuxingStatistics wuxingStats = generateWuxingStatistics(
                yearDetail, monthDetail, dayDetail, hourDetail);
        
        return BaziResponse.DetailInfo.builder()
                .yearPillarDetail(yearDetail)
                .monthPillarDetail(monthDetail)
                .dayPillarDetail(dayDetail)
                .hourPillarDetail(hourDetail)
                .wuxingStatistics(wuxingStats)
                .build();
    }
    
    /**
     * 生成单个柱的详细信息
     */
    private BaziResponse.PillarDetail generatePillarDetail(String pillar) {
        if (pillar == null || pillar.length() < 2) {
            log.warn("柱信息格式不正确: {}", pillar);
            return BaziResponse.PillarDetail.builder().build();
        }
        
        // 提取天干地支字符
        String stemChar = pillar.substring(0, 1);    // 天干
        String branchChar = pillar.substring(1, 2);  // 地支
        
        // 查找天干信息
        BaziResponse.HeavenlyStemInfo stemInfo = generateHeavenlyStemInfo(stemChar);
        
        // 查找地支信息（包括藏干）
        BaziResponse.EarthlyBranchInfo branchInfo = generateEarthlyBranchInfo(branchChar);
        
        return BaziResponse.PillarDetail.builder()
                .heavenlyStem(stemInfo)
                .earthlyBranch(branchInfo)
                .build();
    }
    
    /**
     * 生成天干信息
     */
    private BaziResponse.HeavenlyStemInfo generateHeavenlyStemInfo(String stemChar) {
        Optional<BaziEnum.HeavenlyStem> stemOpt = BaziEnum.HeavenlyStem.findByCharacter(stemChar);
        
        if (stemOpt.isPresent()) {
            BaziEnum.HeavenlyStem stem = stemOpt.get();
            return BaziResponse.HeavenlyStemInfo.builder()
                    .character(stem.getCharacter())
                    .element(stem.getElement())
                    .build();
        } else {
            log.warn("未找到天干信息: {}", stemChar);
            return BaziResponse.HeavenlyStemInfo.builder()
                    .character(stemChar)
                    .element("")
                    .build();
        }
    }
    
    /**
     * 生成地支信息（包括藏干）
     */
    private BaziResponse.EarthlyBranchInfo generateEarthlyBranchInfo(String branchChar) {
        Optional<BaziEnum.EarthlyBranch> branchOpt = BaziEnum.EarthlyBranch.findByCharacter(branchChar);
        
        if (branchOpt.isPresent()) {
            BaziEnum.EarthlyBranch branch = branchOpt.get();
            
            // 查找藏干信息
            List<BaziResponse.HiddenStemInfo> hiddenStems = generateHiddenStemInfos(branchChar);
            
            return BaziResponse.EarthlyBranchInfo.builder()
                    .character(branch.getCharacter())
                    .element(branch.getElement())
                    .hiddenStems(hiddenStems)
                    .build();
        } else {
            log.warn("未找到地支信息: {}", branchChar);
            return BaziResponse.EarthlyBranchInfo.builder()
                    .character(branchChar)
                    .element("")
                    .hiddenStems(new ArrayList<>())
                    .build();
        }
    }
    
    /**
     * 生成藏干信息列表
     */
    private List<BaziResponse.HiddenStemInfo> generateHiddenStemInfos(String branchChar) {
        Optional<BaziEnum.BranchHiddenStems> hiddenOpt = 
                BaziEnum.BranchHiddenStems.findByBranchCharacter(branchChar);
        
        if (hiddenOpt.isPresent()) {
            List<BaziEnum.HiddenStem> hiddenStems = hiddenOpt.get().getHiddenStems();
            List<BaziResponse.HiddenStemInfo> result = new ArrayList<>();
            
            for (BaziEnum.HiddenStem hidden : hiddenStems) {
                result.add(BaziResponse.HiddenStemInfo.builder()
                        .character(hidden.getCharacter())
                        .element(hidden.getElement())
                        .strength(hidden.getStrength())
                        .build());
            }
            
            return result;
        } else {
            log.warn("未找到地支藏干信息: {}", branchChar);
            return new ArrayList<>();
        }
    }
    
    /**
     * 生成五行统计信息
     */
    private BaziResponse.WuxingStatistics generateWuxingStatistics(
            BaziResponse.PillarDetail year, BaziResponse.PillarDetail month,
            BaziResponse.PillarDetail day, BaziResponse.PillarDetail hour) {
        
        Map<String, Integer> distribution = new HashMap<>();
        distribution.put("金", 0);
        distribution.put("木", 0);
        distribution.put("水", 0);
        distribution.put("火", 0);
        distribution.put("土", 0);
        
        // 统计所有天干地支及藏干的五行分布
        List<BaziResponse.PillarDetail> pillars = List.of(year, month, day, hour);
        int totalCount = 0;
        
        for (BaziResponse.PillarDetail pillar : pillars) {
            if (pillar == null) continue;
            
            // 统计天干
            if (pillar.getHeavenlyStem() != null && pillar.getHeavenlyStem().getElement() != null) {
                String element = pillar.getHeavenlyStem().getElement();
                distribution.put(element, distribution.getOrDefault(element, 0) + 1);
                totalCount++;
            }
            
            // 统计地支本气
            if (pillar.getEarthlyBranch() != null && pillar.getEarthlyBranch().getElement() != null) {
                String element = pillar.getEarthlyBranch().getElement();
                distribution.put(element, distribution.getOrDefault(element, 0) + 1);
                totalCount++;
            }
            
            // 统计藏干
            if (pillar.getEarthlyBranch() != null && pillar.getEarthlyBranch().getHiddenStems() != null) {
                for (BaziResponse.HiddenStemInfo hidden : pillar.getEarthlyBranch().getHiddenStems()) {
                    if (hidden != null && hidden.getElement() != null) {
                        String element = hidden.getElement();
                        distribution.put(element, distribution.getOrDefault(element, 0) + 1);
                        totalCount++;
                    }
                }
            }
        }
        
        // 找出主导和最弱五行
        String dominantElement = distribution.entrySet().stream()
                .filter(entry -> entry.getValue() > 0)
                .max(Map.Entry.comparingByValue())
                .map(Map.Entry::getKey)
                .orElse("");
        
        String weakestElement = distribution.entrySet().stream()
                .filter(entry -> entry.getValue() > 0)
                .min(Map.Entry.comparingByValue())
                .map(Map.Entry::getKey)
                .orElse("");
        
        return BaziResponse.WuxingStatistics.builder()
                .distribution(distribution)
                .dominantElement(dominantElement)
                .weakestElement(weakestElement)
                .totalCount(totalCount)
                .build();
    }
    
    /**
     * 转换AI返回数据为五行分析
     */
    private WuxingResponse.WuxingAnalysis convertToWuxingAnalysis(Map<String, Object> data) {
        return WuxingResponse.WuxingAnalysis.builder()
                .primaryElement(getStringValue(data, "primaryElement", ""))
                .secondaryElement(getStringValue(data, "secondaryElement", ""))
                .luckyColor(getStringValue(data, "luckyColor", ""))
                .avoidColor(getStringValue(data, "avoidColor", ""))
                .characteristics(getListValue(data, "characteristics"))
                .suggestions(getListValue(data, "suggestions"))
                .build();
    }
    
    /**
     * 辅助方法：获取字符串值
     */
    private String getStringValue(Map<String, Object> map, String key, String defaultValue) {
        Object value = map.get(key);
        return value != null ? value.toString() : defaultValue;
    }
    
    /**
     * 辅助方法：获取列表值
     */
    @SuppressWarnings("unchecked")
    private List<String> getListValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value instanceof List) {
            List<Object> objectList = (List<Object>) value;
            List<String> stringList = new ArrayList<>();
            for (Object item : objectList) {
                if (item != null) {
                    stringList.add(item.toString());
                }
            }
            return stringList;
        }
        return new ArrayList<>();
    }
} 