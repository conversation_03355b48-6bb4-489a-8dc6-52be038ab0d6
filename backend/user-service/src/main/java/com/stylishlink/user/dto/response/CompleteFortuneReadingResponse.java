package com.stylishlink.user.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 完整运势解读响应实体
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CompleteFortuneReadingResponse {
    
    /**
     * 八字组合
     */
    private BaziCombination baziCombination;
    
    /**
     * 五行分析
     */
    private WuxingAnalysis wuxingAnalysis;
    
    /**
     * 整体运势
     */
    private OverallFortune overallFortune;
    
    /**
     * 吉运建议
     */
    private LuckyAdvice luckyAdvice;
    
    /**
     * 详细运势解读
     */
    private DetailedFortune detailedFortune;
    
    /**
     * 八字组合
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BaziCombination {
        private PillarInfo year;
        private PillarInfo month;
        private PillarInfo day;
        private PillarInfo hour;
        
        @Data
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        public static class PillarInfo {
            private String tiangan;
            private String dizhi;
            private List<String> canggan;
        }
    }
    
    /**
     * 五行分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class WuxingAnalysis {
        private List<ElementInfo> elements;
        private String analysis;
        
        @Data
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        public static class ElementInfo {
            private String element;
            private Integer percentage;
            private Boolean isRizhu;
        }
    }
    
    /**
     * 整体运势
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OverallFortune {
        private Integer currentAge;                    // 当前年龄
        private List<DecadeFortune> decadeFortunes;   // 十年运势分析
        private String lifePhase;                     // 人生阶段
        private Integer currentDecade;                // 当前所在十年期
        private String lifetimeOverview;              // 一生运势概览
        
        @Data
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        public static class DecadeFortune {
            private Integer decade;                   // 第几个十年
            private String ageRange;                  // 年龄范围，如"0-9岁"
            private String yearRange;                 // 年份范围，如"1990-1999"
            private Integer score;                    // 运势分数
            private String theme;                     // 主题
            private String description;               // 描述
            private List<String> keyEvents;          // 关键事件
        }
    }
    
    /**
     * 吉运建议
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LuckyAdvice {
        private AdviceCategory clothing;
        private AdviceCategory jewelry;
        private AdviceCategory fengshui;
        
        @Data
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        public static class AdviceCategory {
            private String title;
            private List<AdviceItem> items;
        }
        
        @Data
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        public static class AdviceItem {
            private String label;
            private String advice;
            private String type; // recommend, avoid
        }
    }
    
    /**
     * 详细运势解读
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DetailedFortune {
        private Map<String, FortuneDetail> monthly;
        private Map<String, FortuneDetail> yearly;
        
        @Data
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        public static class FortuneDetail {
            private String status;
            private String highlight;
            private String color;
            private Object content; // 支持 String 或 List<String>
        }
    }
} 