package com.stylishlink.user.service;

import com.stylishlink.user.dto.request.WuxingRequest;
import com.stylishlink.user.dto.response.WuxingResponse;
import com.stylishlink.user.dto.response.BaziResponse;

/**
 * 五行命理服务接口
 */
public interface WuxingService {
    
    /**
     * 获取用户五行档案
     * @param userId 用户ID
     * @return 五行档案信息
     */
    WuxingResponse getWuxingProfile(String userId);
    
    /**
     * 更新用户五行档案
     * @param userId 用户ID
     * @param request 五行信息请求
     * @return 更新结果
     */
    WuxingResponse updateWuxingProfile(String userId, WuxingRequest request);
    
    /**
     * 计算用户八字
     * @param userId 用户ID
     * @param request 用户信息
     * @return 八字计算结果
     */
    BaziResponse calculateBaZi(String userId, WuxingRequest request);
} 