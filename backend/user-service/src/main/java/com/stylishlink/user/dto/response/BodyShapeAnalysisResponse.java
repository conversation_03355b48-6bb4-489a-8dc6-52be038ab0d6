package com.stylishlink.user.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 身材分析响应实体
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BodyShapeAnalysisResponse {
    
    /**
     * 是否为正面全身照
     */
    private Boolean isFullBodyPhoto;
    
    /**
     * 识别置信度（0-100）
     */
    private Double confidence;
    
    /**
     * 推荐体型分类
     */
    private String bodyType;
    
    /**
     * 详细身材数据
     */
    private Map<String, Object> bodyShape;
    
    /**
     * 身材分析结果
     */
    private List<BodyAnalysis> analysis;
    
    /**
     * 穿搭建议
     */
    private List<StyleSuggestion> suggestions;
    
    /**
     * 失败原因（仅当isFullBodyPhoto为false时）
     */
    private String reason;
    
    /**
     * 分析使用的图片URL
     */
    private String imageUrl;
    
    /**
     * 用户信息是否已更新
     */
    private Boolean userInfoUpdated;
    
    /**
     * 用户信息更新消息
     */
    private String updateMessage;
    
    /**
     * 更新后的用户信息
     */
    private UserResponse updatedUserInfo;
    
    /**
     * 身材分析子项
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BodyAnalysis {
        private String feature;
        private String description;
        private String type;
    }
    
    /**
     * 穿搭建议子项
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class StyleSuggestion {
        private String category;
        private String content;
        private String priority;
    }
} 