package com.stylishlink.user.dto.response;

import lombok.Data;
import java.util.List;
import java.util.Map;

/**
 * 天气查询响应DTO
 */
@Data
public class WeatherResponse {

    /**
     * 位置信息
     */
    private WeatherLocation location;

    /**
     * 当前天气（实况天气）
     */
    private WeatherData current;

    /**
     * 预报天气列表（未来几天）
     */
    private List<WeatherData> forecast;

    /**
     * 数据更新时间
     */
    private String lastUpdate;

    /**
     * 数据来源
     */
    private String dataSource;

    /**
     * 查询时间戳
     */
    private Long timestamp;

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 错误信息（如果有）
     */
    private String errorMessage;

    /**
     * 天气位置信息
     */
    @Data
    public static class WeatherLocation {
        private String id;
        private String name;
        private String country;
        private String path;
        private String timezone;
        private String timezoneOffset;
        private String province;
        private String adcode;
    }

    /**
     * 天气数据
     */
    @Data
    public static class WeatherData {
        private String text;
        private String code;
        private String temperature;
        private String highTemperature;
        private String lowTemperature;
        private String windDirection;
        private String windPower;
        private String windSpeed;
        private String humidity;
        private String pressure;
        private String visibility;
        private String uvIndex;
        private String precipitation;
        private String date;
        private String week;
        private String dayWeather;
        private String nightWeather;
        private String dayTemperature;
        private String nightTemperature;
    }
} 