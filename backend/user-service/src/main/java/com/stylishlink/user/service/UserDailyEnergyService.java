package com.stylishlink.user.service;

import com.stylishlink.user.entity.UserDailyEnergy;
import com.stylishlink.user.dto.response.TodayEnergyResponse;
import com.stylishlink.user.dto.response.TodayEnergyBriefResponse;

import java.util.List;
import java.util.Optional;

/**
 * 用户每日能量服务接口
 */
public interface UserDailyEnergyService {
    
    /**
     * 根据用户ID和日期查询能量信息
     * @param userId 用户ID
     * @param energyDate 能量日期（格式：YYYY-MM-DD）
     * @return 能量信息
     */
    Optional<UserDailyEnergy> findByUserIdAndDate(String userId, String energyDate);
    
    /**
     * 保存或更新用户每日能量信息
     * @param dailyEnergy 每日能量信息
     * @return 保存后的能量信息
     */
    UserDailyEnergy saveOrUpdate(UserDailyEnergy dailyEnergy);
    
    /**
     * 根据TodayEnergyResponse创建UserDailyEnergy实体
     * @param userId 用户ID
     * @param energyDate 能量日期
     * @param response 今日能量响应
     * @return 用户每日能量实体
     */
    UserDailyEnergy createFromResponse(String userId, String energyDate, TodayEnergyResponse response);
    
    /**
     * 将UserDailyEnergy实体转换为TodayEnergyResponse
     * @param dailyEnergy 用户每日能量实体
     * @return 今日能量响应
     */
    TodayEnergyResponse convertToResponse(UserDailyEnergy dailyEnergy);
    
    /**
     * 根据用户ID查询指定日期范围内的能量信息
     * @param userId 用户ID
     * @param startDate 开始日期（格式：YYYY-MM-DD）
     * @param endDate 结束日期（格式：YYYY-MM-DD）
     * @return 能量信息列表
     */
    List<UserDailyEnergy> findByUserIdAndDateRange(String userId, String startDate, String endDate);
    
    /**
     * 根据用户ID查询最近N天的能量信息
     * @param userId 用户ID
     * @param days 天数
     * @return 能量信息列表
     */
    List<UserDailyEnergy> findRecentByUserId(String userId, int days);
    
    /**
     * 检查用户在指定日期是否已有能量记录
     * @param userId 用户ID
     * @param energyDate 能量日期（格式：YYYY-MM-DD）
     * @return 是否存在
     */
    boolean existsByUserIdAndDate(String userId, String energyDate);
    
    /**
     * 删除用户过期的能量记录（保留最近30天）
     * @param userId 用户ID
     * @return 删除的记录数
     */
    int cleanupExpiredRecords(String userId);
    
    /**
     * 将UserDailyEnergy实体转换为TodayEnergyBriefResponse
     * @param dailyEnergy 用户每日能量实体
     * @return 今日能量简要响应
     */
    TodayEnergyBriefResponse convertToBriefResponse(UserDailyEnergy dailyEnergy);
} 