package com.stylishlink.user.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 刷新令牌响应
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RefreshTokenResponse {
    
    private String token;           // 新的JWT访问令牌
    private Integer expiresIn;      // token过期时间（秒）
    private String refreshToken;    // 新的刷新令牌（可选）
} 