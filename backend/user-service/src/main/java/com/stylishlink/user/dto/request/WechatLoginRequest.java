package com.stylishlink.user.dto.request;

import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 微信登录请求对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WechatLoginRequest {
    
    /**
     * 微信登录code
     */
    @NotBlank(message = "微信登录code不能为空")
    private String code;
    
    /**
     * 用户信息加密数据
     */
    private String encryptedData;
    
    /**
     * 加密算法的初始向量
     */
    private String iv;
} 