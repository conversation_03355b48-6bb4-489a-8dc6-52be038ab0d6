package com.stylishlink.user.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * 八字相关枚举定义
 * 包含天干、地支、藏干的五行属性对照表
 */
public class BaziEnum {

    /**
     * 天干枚举
     */
    @Getter
    @AllArgsConstructor
    public enum HeavenlyStem {
        JIA("甲", "木", "阳", "阳木，如大树"),
        YI("乙", "木", "阴", "阴木，如花草"),
        BING("丙", "火", "阳", "阳火，如太阳"),
        DING("丁", "火", "阴", "阴火，如灯烛"),
        WU("戊", "土", "阳", "阳土，如高山"),
        JI("己", "土", "阴", "阴土，如田园"),
        GENG("庚", "金", "阳", "阳金，如刀剑"),
        XIN("辛", "金", "阴", "阴金，如珠宝"),
        REN("壬", "水", "阳", "阳水，如江河"),
        GUI("癸", "水", "阴", "阴水，如雨露");

        private final String character;  // 天干字符
        private final String element;    // 五行属性
        private final String yinYang;    // 阴阳属性
        private final String description; // 描述

        /**
         * 根据字符查找天干
         */
        public static Optional<HeavenlyStem> findByCharacter(String character) {
            return Arrays.stream(values())
                    .filter(stem -> stem.getCharacter().equals(character))
                    .findFirst();
        }
    }

    /**
     * 地支枚举
     */
    @Getter
    @AllArgsConstructor
    public enum EarthlyBranch {
        ZI("子", "水", "阳", "鼠", "十一月"),
        CHOU("丑", "土", "阴", "牛", "十二月"),
        YIN("寅", "木", "阳", "虎", "正月"),
        MAO("卯", "木", "阴", "兔", "二月"),
        CHEN("辰", "土", "阳", "龙", "三月"),
        SI("巳", "火", "阴", "蛇", "四月"),
        WU("午", "火", "阳", "马", "五月"),
        WEI("未", "土", "阴", "羊", "六月"),
        SHEN("申", "金", "阳", "猴", "七月"),
        YOU("酉", "金", "阴", "鸡", "八月"),
        XU("戌", "土", "阳", "狗", "九月"),
        HAI("亥", "水", "阴", "猪", "十月");

        private final String character;  // 地支字符
        private final String element;    // 五行属性
        private final String yinYang;    // 阴阳属性
        private final String zodiac;     // 生肖
        private final String month;      // 对应月份

        /**
         * 根据字符查找地支
         */
        public static Optional<EarthlyBranch> findByCharacter(String character) {
            return Arrays.stream(values())
                    .filter(branch -> branch.getCharacter().equals(character))
                    .findFirst();
        }
    }

    /**
     * 藏干信息
     */
    @Getter
    @AllArgsConstructor
    public static class HiddenStem {
        private final String character;  // 藏干字符
        private final String element;    // 五行属性
        private final String strength;   // 强度：主气/中气/余气

        public static HiddenStem of(String character, String strength) {
            Optional<HeavenlyStem> stem = HeavenlyStem.findByCharacter(character);
            String element = stem.map(HeavenlyStem::getElement).orElse("");
            return new HiddenStem(character, element, strength);
        }
    }

    /**
     * 地支藏干对照表
     */
    @Getter
    @AllArgsConstructor
    public enum BranchHiddenStems {
        ZI_HIDDEN("子", Arrays.asList(
                HiddenStem.of("癸", "主气")
        )),
        CHOU_HIDDEN("丑", Arrays.asList(
                HiddenStem.of("己", "主气"),
                HiddenStem.of("癸", "中气"),
                HiddenStem.of("辛", "余气")
        )),
        YIN_HIDDEN("寅", Arrays.asList(
                HiddenStem.of("甲", "主气"),
                HiddenStem.of("丙", "中气"),
                HiddenStem.of("戊", "余气")
        )),
        MAO_HIDDEN("卯", Arrays.asList(
                HiddenStem.of("乙", "主气")
        )),
        CHEN_HIDDEN("辰", Arrays.asList(
                HiddenStem.of("戊", "主气"),
                HiddenStem.of("乙", "中气"),
                HiddenStem.of("癸", "余气")
        )),
        SI_HIDDEN("巳", Arrays.asList(
                HiddenStem.of("丙", "主气"),
                HiddenStem.of("戊", "中气"),
                HiddenStem.of("庚", "余气")
        )),
        WU_HIDDEN("午", Arrays.asList(
                HiddenStem.of("丁", "主气"),
                HiddenStem.of("己", "中气")
        )),
        WEI_HIDDEN("未", Arrays.asList(
                HiddenStem.of("己", "主气"),
                HiddenStem.of("丁", "中气"),
                HiddenStem.of("乙", "余气")
        )),
        SHEN_HIDDEN("申", Arrays.asList(
                HiddenStem.of("庚", "主气"),
                HiddenStem.of("壬", "中气"),
                HiddenStem.of("戊", "余气")
        )),
        YOU_HIDDEN("酉", Arrays.asList(
                HiddenStem.of("辛", "主气")
        )),
        XU_HIDDEN("戌", Arrays.asList(
                HiddenStem.of("戊", "主气"),
                HiddenStem.of("辛", "中气"),
                HiddenStem.of("丁", "余气")
        )),
        HAI_HIDDEN("亥", Arrays.asList(
                HiddenStem.of("壬", "主气"),
                HiddenStem.of("甲", "中气")
        ));

        private final String branchCharacter;  // 地支字符
        private final List<HiddenStem> hiddenStems; // 藏干列表

        /**
         * 根据地支字符查找藏干
         */
        public static Optional<BranchHiddenStems> findByBranchCharacter(String branchCharacter) {
            return Arrays.stream(values())
                    .filter(item -> item.getBranchCharacter().equals(branchCharacter))
                    .findFirst();
        }
    }

    /**
     * 五行枚举
     */
    @Getter
    @AllArgsConstructor
    public enum WuxingElement {
        JIN("金", "收敛、坚固、肃杀"),
        MU("木", "生长、条达、舒展"),
        SHUI("水", "滋润、寒冷、向下"),
        HUO("火", "炎热、向上、光明"),
        TU("土", "化生、承载、包容");

        private final String name;        // 五行名称
        private final String description; // 五行特性描述

        /**
         * 根据名称查找五行
         */
        public static Optional<WuxingElement> findByName(String name) {
            return Arrays.stream(values())
                    .filter(element -> element.getName().equals(name))
                    .findFirst();
        }
    }

    /**
     * 五行相生关系枚举
     */
    @Getter
    @AllArgsConstructor
    public enum WuxingGeneration {
        MU_SHENG_HUO("木", "火", "木燃烧生火"),
        HUO_SHENG_TU("火", "土", "火燃烧成灰土"),
        TU_SHENG_JIN("土", "金", "土中蕴含金属"),
        JIN_SHENG_SHUI("金", "水", "金属遇冷凝水"),
        SHUI_SHENG_MU("水", "木", "水滋养树木");

        private final String generator;   // 生者
        private final String generated;   // 被生者
        private final String description; // 关系描述

        /**
         * 查找某个五行生什么
         */
        public static Optional<WuxingGeneration> findWhatGenerates(String element) {
            return Arrays.stream(values())
                    .filter(relation -> relation.getGenerator().equals(element))
                    .findFirst();
        }

        /**
         * 查找某个五行被什么生
         */
        public static Optional<WuxingGeneration> findWhatGeneratedBy(String element) {
            return Arrays.stream(values())
                    .filter(relation -> relation.getGenerated().equals(element))
                    .findFirst();
        }
    }

    /**
     * 五行相克关系枚举
     */
    @Getter
    @AllArgsConstructor
    public enum WuxingRestraint {
        MU_KE_TU("木", "土", "树根破土而出"),
        TU_KE_SHUI("土", "水", "土能吸水、堵水"),
        SHUI_KE_HUO("水", "火", "水能灭火"),
        HUO_KE_JIN("火", "金", "火能熔金"),
        JIN_KE_MU("金", "木", "金属能砍伐树木");

        private final String restrainer;  // 克者
        private final String restrained;  // 被克者
        private final String description; // 关系描述

        /**
         * 查找某个五行克什么
         */
        public static Optional<WuxingRestraint> findWhatRestrains(String element) {
            return Arrays.stream(values())
                    .filter(relation -> relation.getRestrainer().equals(element))
                    .findFirst();
        }

        /**
         * 查找某个五行被什么克
         */
        public static Optional<WuxingRestraint> findWhatRestrainedBy(String element) {
            return Arrays.stream(values())
                    .filter(relation -> relation.getRestrained().equals(element))
                    .findFirst();
        }
    }
} 