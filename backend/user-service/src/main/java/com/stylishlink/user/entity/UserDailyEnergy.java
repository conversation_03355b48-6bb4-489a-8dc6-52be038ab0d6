package com.stylishlink.user.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 用户每日能量信息实体类
 */
@TableName(value = "user_daily_energy", autoResultMap = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserDailyEnergy {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 用户ID
     */
    @TableField("user_id")
    private String userId;
    
    /**
     * 能量日期（格式：YYYY-MM-DD）
     */
    @TableField("energy_date")
    private String energyDate;
    
    /**
     * 今日总能量分数(0-100)
     */
    @TableField("total_score")
    private Integer totalScore;
    
    /**
     * 超过的用户百分比
     */
    @TableField("percentage")
    private Integer percentage;
    
    /**
     * 能量高峰时段
     */
    @TableField("peak_time")
    private String peakTime;
    
    /**
     * 高峰时段描述
     */
    @TableField("peak_time_description")
    private String peakTimeDescription;
    
    /**
     * 今日能量总体描述
     */
    @TableField("description")
    private String description;
    
    /**
     * 日期信息（JSON格式）
     */
    @TableField(value = "date_info", typeHandler = JacksonTypeHandler.class)
    private DateInfo dateInfo;
    
    /**
     * 五维能量评分（JSON格式）
     */
    @TableField(value = "dimensions", typeHandler = JacksonTypeHandler.class)
    private Dimensions dimensions;
    
    /**
     * 宜忌指南（JSON格式）
     */
    @TableField(value = "advice", typeHandler = JacksonTypeHandler.class)
    private Advice advice;
    
    /**
     * 幸运元素（JSON格式）
     */
    @TableField(value = "lucky_elements", typeHandler = JacksonTypeHandler.class)
    private LuckyElements luckyElements;
    
    /**
     * 幸运元素简化总结（JSON格式）
     */
    @TableField(value = "lucky_elements_summary", typeHandler = JacksonTypeHandler.class)
    private LuckyElementsSummary luckyElementsSummary;
    
    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;
    
    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
    
    // 内嵌类定义（与TodayEnergyResponse保持一致）
    
    /**
     * 日期信息
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class DateInfo {
        /**
         * 公历日期
         */
        private String gregorian;
        
        /**
         * 农历信息
         */
        private String lunar;
    }
    
    /**
     * 五维能量评分
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Dimensions {
        /**
         * 爱情运势(0-100)
         */
        private Integer love;
        
        /**
         * 事业运势(0-100)
         */
        private Integer career;
        
        /**
         * 财富运势(0-100)
         */
        private Integer wealth;
        
        /**
         * 健康运势(0-100)
         */
        private Integer health;
        
        /**
         * 人际运势(0-100)
         */
        private Integer relationship;
    }
    
    /**
     * 宜忌指南
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Advice {
        /**
         * 宜忌分类列表
         */
        private List<Category> categories;
        
        /**
         * 生活建议列表
         */
        private List<LifeSuggestion> lifeSuggestions;
    }
    
    /**
     * 宜忌分类
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Category {
        /**
         * 类型（suitable/avoid）
         */
        private String type;
        
        /**
         * 标签（宜做事项/忌做事项）
         */
        private String label;
        
        /**
         * 具体事项列表
         */
        private List<Item> items;
    }
    
    /**
     * 具体事项
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Item {
        /**
         * 事项ID
         */
        private String id;
        
        /**
         * 图标
         */
        private String icon;
        
        /**
         * 文本描述
         */
        private String text;
    }
    
    /**
     * 生活建议
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class LifeSuggestion {
        /**
         * 图标
         */
        private String icon;
        
        /**
         * 建议内容
         */
        private String content;
    }
    
    /**
     * 幸运元素
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class LuckyElements {
        /**
         * 幸运色列表
         */
        private List<ColorItem> colors;
        
        /**
         * 服饰建议列表
         */
        private List<String> clothing;
        
        /**
         * 配饰建议列表
         */
        private List<String> accessories;
        
        /**
         * 妆容建议列表
         */
        private List<String> makeup;
    }
    
    /**
     * 颜色项
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ColorItem {
        /**
         * 颜色值 (HTML颜色代码)
         */
        private String value;
        
        /**
         * 颜色名称 (中文名称)
         */
        private String name;
    }
    
    /**
     * 幸运元素简化总结
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class LuckyElementsSummary {
        /**
         * 服饰建议总结
         */
        private String clothingSummary;
        
        /**
         * 配饰建议总结
         */
        private String accessoriesSummary;
        
        /**
         * 妆容建议总结
         */
        private String makeupSummary;
    }
} 