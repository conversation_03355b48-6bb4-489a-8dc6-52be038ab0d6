package com.stylishlink.user.exception;

import com.stylishlink.common.dto.ApiResponse;
import com.stylishlink.common.exception.BusinessException;
import com.stylishlink.common.exception.ResourceNotFoundException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.resource.NoResourceFoundException;
import org.springframework.web.servlet.NoHandlerFoundException;

/**
 * 全局异常处理器
 */
@RestControllerAdvice
@Slf4j
public class GlobalExceptionHandler {
    
    /**
     * 处理业务异常
     * @param ex 业务异常
     * @return API响应
     */
    @ExceptionHandler(BusinessException.class)
    public ResponseEntity<ApiResponse<Object>> handleBusinessException(BusinessException ex) {
        log.warn("业务异常: {}", ex.getMessage());
        // 将String类型的错误码转换为int，如果转换失败则使用400
        int errorCode;
        try {
            errorCode = Integer.parseInt(ex.getCode());
        } catch (NumberFormatException e) {
            errorCode = 400; // 默认业务错误码
        }
        ApiResponse<Object> response = ApiResponse.<Object>error(errorCode, ex.getMessage());
        return ResponseEntity.ok(response);
    }
    
    /**
     * 处理资源未找到异常
     * @param ex 资源未找到异常
     * @return API响应
     */
    @ExceptionHandler(NoResourceFoundException.class)
    public ResponseEntity<ApiResponse<Object>> handleNoResourceFound(NoResourceFoundException ex) {
        log.warn("请求的资源不存在: {}", ex.getMessage());
        ApiResponse<Object> response = ApiResponse.<Object>error(404, "请求的接口不存在");
        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
    }
    
    /**
     * 处理参数校验异常
     * @param e 参数校验异常
     * @return API响应
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ApiResponse<Object> handleValidationExceptions(MethodArgumentNotValidException e) {
        log.error("参数校验失败: {}", e.getMessage(), e);
        
        // 获取第一个字段错误的详细信息
        FieldError fieldError = e.getBindingResult().getFieldError();
        String errorMessage = "参数校验失败";
        
        if (fieldError != null) {
            errorMessage = fieldError.getDefaultMessage();
        }
        
        return ApiResponse.error(1001, errorMessage);
    }
    
    /**
     * 处理绑定异常
     * @param e 绑定异常
     * @return API响应
     */
    @ExceptionHandler(BindException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ApiResponse<Object> handleBindExceptions(BindException e) {
        log.error("参数绑定失败: {}", e.getMessage(), e);
        
        // 获取第一个字段错误的详细信息
        FieldError fieldError = e.getBindingResult().getFieldError();
        String errorMessage = "参数绑定失败";
        
        if (fieldError != null) {
            errorMessage = fieldError.getDefaultMessage();
        }
        
        return ApiResponse.error(1001, errorMessage);
    }
    
    /**
     * 处理其他异常
     * @param ex 异常
     * @return API响应
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<ApiResponse<Object>> handleException(Exception ex) {
        log.error("系统异常: {}", ex.getMessage(), ex);
        ApiResponse<Object> response = ApiResponse.<Object>error(500, "系统异常，请联系管理员");
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }

    @ExceptionHandler(NoHandlerFoundException.class)
    public ResponseEntity<ApiResponse<Object>> handleNoHandlerFound(NoHandlerFoundException ex) {
        log.warn("未找到处理器: {}", ex.getMessage());
        ApiResponse<Object> response = ApiResponse.<Object>error(404, "请求的接口不存在");
        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
    }
} 