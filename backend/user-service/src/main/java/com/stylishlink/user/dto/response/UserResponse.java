package com.stylishlink.user.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 用户响应类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserResponse {
    
    /**
     * 主键ID（MySQL自增ID）
     */
    private Long id;
    
    /**
     * 用户ID（用户名）
     */
    private String userId;
    
    /**
     * 昵称
     */
    private String nickname;
    
    /**
     * 头像URL
     */
    private String avatar;
    
    /**
     * 性别（0-未知，1-男，2-女）
     */
    private Integer gender;
    
    /**
     * 手机号
     */
    private String phone;
    
    /**
     * 邮箱
     */
    private String email;
    
    /**
     * 全身照URL
     */
    private String photoUrl;
    
    /**
     * 身高(cm)
     */
    private Integer height;
    
    /**
     * 体重(kg)
     */
    private Integer weight;
    
    /**
     * 体型（标准/偏瘦/偏胖）
     */
    private String bodyType;
    
    /**
     * 肤色
     */
    private String skinTone;
    
    /**
     * 风格偏好
     */
    private List<String> stylePreferences;
    
    /**
     * 体型细分
     */
    private Map<String, String> bodyShape;
    
    /**
     * 用户模式（natural/energy）
     */
    private String mode;
    
    /**
     * 居住地点中文名称
     */
    private String residenceName;
    
    /**
     * 居住地点地区代码
     */
    private String residenceCode;
    
    /**
     * 姓名
     */
    private String fullName;
    
    /**
     * 出生日期
     */
    private String birthDate;
    
    /**
     * 出生时间
     */
    private String birthTime;
    
    /**
     * 出生地点
     */
    private String birthPlace;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
} 