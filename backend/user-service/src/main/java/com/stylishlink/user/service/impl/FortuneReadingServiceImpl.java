package com.stylishlink.user.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.stylishlink.common.exception.BusinessException;
import com.stylishlink.user.client.AIServiceClient;
import com.stylishlink.user.dto.response.CompleteFortuneReadingResponse;
import com.stylishlink.user.entity.User;
import com.stylishlink.user.entity.UserFortuneReading;
import com.stylishlink.user.repository.UserFortuneReadingRepository;
import com.stylishlink.user.service.FortuneReadingService;
import com.stylishlink.user.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.Period;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 运势解读服务实现类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class FortuneReadingServiceImpl implements FortuneReadingService {
    
    private final UserService userService;
    private final UserFortuneReadingRepository fortuneReadingRepository;
    private final ObjectMapper objectMapper;
    
    @Autowired(required = false)
    private AIServiceClient aiServiceClient;
    
    @Override
    public CompleteFortuneReadingResponse getCompleteFortuneReading(String userId, String date) {
        log.info("获取用户完整运势解读: userId={}, requestDate={}", userId, date);
        
        // 获取用户信息并验证
        User user = userService.findByUserId(userId)
                .orElseThrow(() -> new BusinessException("2001", "用户不存在"));
        
        // 验证用户模式和八字信息
        validateUserForFortuneReading(user);
        
        // 解析日期
        LocalDate readingDate = parseDate(date);
        
        // 优化缓存策略：按年月查询，避免重复生成同月的运势解读
        // 原理：运势解读主要基于月份数据，同一个月的运势分析内容基本相同
        // 如果当月已有任何一天的解读数据，直接返回，不再重新生成
        int year = readingDate.getYear();
        int month = readingDate.getMonthValue();
        
        Optional<UserFortuneReading> cachedReading = fortuneReadingRepository.findByUserIdAndYearMonth(userId, year, month);
        
        if (cachedReading.isPresent()) {
            log.info("返回当月缓存的运势解读信息: userId={}, requestDate={}, cachedDate={}", 
                    userId, readingDate, cachedReading.get().getReadingDate());
            return convertToResponse(cachedReading.get());
        }
        
        // 当月没有缓存数据，调用AI生成
        log.info("当月没有运势解读信息，开始调用AI生成: userId={}, year={}, month={}", userId, year, month);
        CompleteFortuneReadingResponse response = generateFortuneReadingByAI(user, readingDate);
        
        // 保存到缓存
        saveFortuneReadingToCache(user, readingDate, response);
        
        return response;
    }
    
    /**
     * 验证用户是否可以获取运势解读
     */
    private void validateUserForFortuneReading(User user) {
        // 检查是否为能量模式
        if (!"energy".equals(user.getMode())) {
            throw new BusinessException("3001", "用户未开启能量模式");
        }
        
        // 检查八字信息是否完整
        if (!user.hasWuxingInfo()) {
            throw new BusinessException("3002", "八字信息不完整");
        }
        
        if (user.getWuxingProfile() == null || user.getWuxingProfile().getBaziDetail() == null) {
            throw new BusinessException("3002", "八字信息不完整");
        }
    }
    
    /**
     * 解析日期字符串
     */
    private LocalDate parseDate(String date) {
        if (!StringUtils.hasText(date)) {
            return LocalDate.now();
        }
        
        try {
            return LocalDate.parse(date, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        } catch (Exception e) {
            log.warn("日期格式错误，使用今日日期: {}", date);
            return LocalDate.now();
        }
    }
    
    /**
     * 使用AI服务生成运势解读信息
     */
    private CompleteFortuneReadingResponse generateFortuneReadingByAI(User user, LocalDate date) {
        // 检查AI服务是否可用
        if (aiServiceClient == null) {
            throw new BusinessException("3003", "AI服务不可用");
        }
        
        try {
            // 获取用户八字信息
            User.WuxingProfile.BaziDetail baziDetail = user.getWuxingProfile().getBaziDetail();
            
            // 构建八字组合（本地生成）
            CompleteFortuneReadingResponse.BaziCombination baziCombination = buildBaziCombination(baziDetail);
            
            // 构建五行分析（本地生成）
            CompleteFortuneReadingResponse.WuxingAnalysis wuxingAnalysis = buildWuxingAnalysis(user.getWuxingProfile());
            
            // 构建AI请求参数
            Map<String, Object> aiRequest = new HashMap<>();
            aiRequest.put("userId", user.getUserId());
            aiRequest.put("date", date.toString());
            
            // 构建包含五行分布的完整baziDetail
            Map<String, Object> baziDetailMap = new HashMap<>();
            baziDetailMap.put("yearPillar", baziDetail.getYearPillar());
            baziDetailMap.put("monthPillar", baziDetail.getMonthPillar());
            baziDetailMap.put("dayPillar", baziDetail.getDayPillar());
            baziDetailMap.put("hourPillar", baziDetail.getHourPillar());
            baziDetailMap.put("elements", baziDetail.getElements());
            
            // 添加五行分布数据 - 使用统一的提取方法
            Map<String, Integer> wuxingDistribution = extractWuxingDistribution(user.getWuxingProfile());
            
            // 确保五行分布数据完整（默认值为0）
            Map<String, Object> wuxingDistributionMap = new HashMap<>();
            wuxingDistributionMap.put("metal", wuxingDistribution.getOrDefault("金", 0));
            wuxingDistributionMap.put("wood", wuxingDistribution.getOrDefault("木", 0));
            wuxingDistributionMap.put("water", wuxingDistribution.getOrDefault("水", 0));
            wuxingDistributionMap.put("fire", wuxingDistribution.getOrDefault("火", 0));
            wuxingDistributionMap.put("earth", wuxingDistribution.getOrDefault("土", 0));
            
            baziDetailMap.put("wuxingDistribution", wuxingDistributionMap);
            aiRequest.put("baziDetail", baziDetailMap);
            
            aiRequest.put("userInfo", Map.of(
                "fullName", user.getFullName() != null ? user.getFullName() : "",
                "gender", user.getGender() != null ? user.getGender() : 0,
                "birthDate", user.getBirthDate() != null ? user.getBirthDate().toString() : "",
                "birthTime", user.getBirthTime() != null ? user.getBirthTime().toString() : "",
                "birthPlace", user.getBirthPlace() != null ? user.getBirthPlace() : ""
            ));
            
            // 调用AI服务生成运势分析（只包含运势部分，不包含八字五行）
            Map<String, Object> aiResponse = aiServiceClient.generateCompleteFortuneReading(aiRequest);
            
            // 解析AI响应，并与本地生成的八字五行数据组合
            return parseAiFortuneResponse(aiResponse, baziCombination, wuxingAnalysis);
            
        } catch (BusinessException e) {
            // 重新抛出业务异常
            throw e;
        } catch (Exception e) {
            log.error("AI服务生成运势解读失败: userId={}, date={}", user.getUserId(), date, e);
            throw new BusinessException("3003", "AI服务调用异常：" + e.getMessage());
        }
    }

    /**
     * 解析AI服务返回的运势分析数据
     */
    @SuppressWarnings("unchecked")
    private CompleteFortuneReadingResponse parseAiFortuneResponse(Map<String, Object> aiResponse, 
                                                                  CompleteFortuneReadingResponse.BaziCombination baziCombination,
                                                                  CompleteFortuneReadingResponse.WuxingAnalysis wuxingAnalysis) {
        try {
            // AI服务返回的格式是 {code: 200, data: {...}, message: "..."}
            // 需要从data字段中提取实际数据
            Map<String, Object> responseData = (Map<String, Object>) aiResponse.get("data");
            if (responseData == null) {
                log.error("AI响应中没有data字段: {}", aiResponse);
                throw new BusinessException("3003", "AI服务响应格式错误");
            }
            
            // 从AI响应的data部分提取运势分析数据
            Map<String, Object> overallFortuneData = (Map<String, Object>) responseData.get("overallFortune");
            Map<String, Object> luckyAdviceData = (Map<String, Object>) responseData.get("luckyAdvice");
            Map<String, Object> detailedFortuneData = (Map<String, Object>) responseData.get("detailedFortune");
            
            // 转换整体运势
            CompleteFortuneReadingResponse.OverallFortune overallFortune = null;
            if (overallFortuneData != null) {
                overallFortune = objectMapper.convertValue(overallFortuneData, CompleteFortuneReadingResponse.OverallFortune.class);
            }
            
            // 转换吉运建议
            CompleteFortuneReadingResponse.LuckyAdvice luckyAdvice = null;
            if (luckyAdviceData != null) {
                luckyAdvice = objectMapper.convertValue(luckyAdviceData, CompleteFortuneReadingResponse.LuckyAdvice.class);
            }
            
            // 转换详细运势
            CompleteFortuneReadingResponse.DetailedFortune detailedFortune = null;
            if (detailedFortuneData != null) {
                detailedFortune = objectMapper.convertValue(detailedFortuneData, CompleteFortuneReadingResponse.DetailedFortune.class);
            }
            
            return CompleteFortuneReadingResponse.builder()
                    .baziCombination(baziCombination)
                    .wuxingAnalysis(wuxingAnalysis)
                    .overallFortune(overallFortune)
                    .luckyAdvice(luckyAdvice)
                    .detailedFortune(detailedFortune)
                    .build();
                    
        } catch (Exception e) {
            log.error("解析AI运势分析响应失败: {}", e.getMessage(), e);
            throw new BusinessException("3003", "AI服务数据解析异常");
        }
    }
    
    /**
     * 构建五行分析
     */
    private CompleteFortuneReadingResponse.WuxingAnalysis buildWuxingAnalysis(User.WuxingProfile wuxingProfile) {
        // 构建五行元素分析
        List<CompleteFortuneReadingResponse.WuxingAnalysis.ElementInfo> elements = new ArrayList<>();
        
        // 获取五行分布数据 - 从数据库中的实际五行统计数据获取
        Map<String, Integer> wuxingDistribution = extractWuxingDistribution(wuxingProfile);
        
        // 计算总数
        int total = wuxingDistribution.values().stream().mapToInt(Integer::intValue).sum();
        if (total == 0) {
            total = 8; // 默认八字总数
        }
        
        // 获取日主五行（假设从日柱天干推导）
        String dayMaster = getDayMasterElement(wuxingProfile.getBaziDetail());
        
        // 构建五行元素信息，并确保百分比总和为100
        String[] wuxingElements = {"金", "木", "水", "火", "土"};
        int[] counts = new int[5];
        int[] percentages = new int[5];
        int totalPercentage = 0;
        
        // 先计算每个元素的数量和初始百分比
        for (int i = 0; i < wuxingElements.length; i++) {
            String element = wuxingElements[i];
            counts[i] = wuxingDistribution.getOrDefault(element, 0);
            percentages[i] = Math.round((counts[i] * 100.0f) / total);
            totalPercentage += percentages[i];
        }
        
        // 调整百分比确保总和为100
        if (totalPercentage != 100 && total > 0) {
            // 找到数量最多的元素进行调整
            int maxIndex = 0;
            for (int i = 1; i < counts.length; i++) {
                if (counts[i] > counts[maxIndex]) {
                    maxIndex = i;
                }
            }
            percentages[maxIndex] += (100 - totalPercentage);
        }
        
        // 构建元素信息列表
        for (int i = 0; i < wuxingElements.length; i++) {
            String element = wuxingElements[i];
            boolean isRizhu = element.equals(dayMaster);
            
            CompleteFortuneReadingResponse.WuxingAnalysis.ElementInfo elementInfo = 
                CompleteFortuneReadingResponse.WuxingAnalysis.ElementInfo.builder()
                    .element(element)
                    .percentage(percentages[i])
                    .isRizhu(isRizhu)
                    .build();
            
            elements.add(elementInfo);
        }
        
        // 生成五行分析文字
        String analysisText = generateWuxingAnalysisText(wuxingDistribution, dayMaster);
        
        return CompleteFortuneReadingResponse.WuxingAnalysis.builder()
                .elements(elements)
                .analysis(analysisText)
                .build();
    }
    
    /**
     * 从五行配置中提取五行分布数据
     */
    private Map<String, Integer> extractWuxingDistribution(User.WuxingProfile wuxingProfile) {
        Map<String, Integer> distribution = new HashMap<>();
        
        // 初始化五行分布
        distribution.put("金", 0);
        distribution.put("木", 0);
        distribution.put("水", 0);
        distribution.put("火", 0);
        distribution.put("土", 0);
        
        // 第一优先级：从新格式的 detailInfo.wuxingStatistics 获取（AI精确计算）
        if (wuxingProfile.getBaziDetail() != null && 
            wuxingProfile.getBaziDetail().getDetailInfo() != null && 
            wuxingProfile.getBaziDetail().getDetailInfo().getWuxingStatistics() != null &&
            wuxingProfile.getBaziDetail().getDetailInfo().getWuxingStatistics().getDistribution() != null) {
            
            Map<String, Integer> wuxingStats = wuxingProfile.getBaziDetail().getDetailInfo().getWuxingStatistics().getDistribution();
            log.info("使用新格式 detailInfo.wuxingStatistics 数据: {}", wuxingStats);
            
            for (Map.Entry<String, Integer> entry : wuxingStats.entrySet()) {
                if (distribution.containsKey(entry.getKey())) {
                    distribution.put(entry.getKey(), entry.getValue());
                }
            }
            
            // 检查新格式数据是否有效
            boolean hasValidNewData = distribution.values().stream().anyMatch(count -> count > 0);
            if (hasValidNewData) {
                return distribution;
            }
        }
        
        // 第二优先级：从基础八字信息推算（本地计算备用方案）
        if (wuxingProfile.getBaziDetail() != null) {
            log.info("新格式五行数据为空，使用 calculateWuxingFromBazi 本地推算");
            distribution = calculateWuxingFromBazi(wuxingProfile.getBaziDetail());
        }
        
        return distribution;
    }
    
    /**
     * 从八字信息推算五行分布（备用方案）
     */
    private Map<String, Integer> calculateWuxingFromBazi(User.WuxingProfile.BaziDetail baziDetail) {
        Map<String, Integer> distribution = new HashMap<>();
        distribution.put("金", 0);
        distribution.put("木", 0);
        distribution.put("水", 0);
        distribution.put("火", 0);
        distribution.put("土", 0);
        
        // 天干五行对照表
        Map<String, String> tianganWuxing = new HashMap<>();
        tianganWuxing.put("甲", "木");
        tianganWuxing.put("乙", "木");
        tianganWuxing.put("丙", "火");
        tianganWuxing.put("丁", "火");
        tianganWuxing.put("戊", "土");
        tianganWuxing.put("己", "土");
        tianganWuxing.put("庚", "金");
        tianganWuxing.put("辛", "金");
        tianganWuxing.put("壬", "水");
        tianganWuxing.put("癸", "水");
        
        // 地支五行对照表（本气）
        Map<String, String> dizhiWuxing = new HashMap<>();
        dizhiWuxing.put("子", "水");
        dizhiWuxing.put("丑", "土");
        dizhiWuxing.put("寅", "木");
        dizhiWuxing.put("卯", "木");
        dizhiWuxing.put("辰", "土");
        dizhiWuxing.put("巳", "火");
        dizhiWuxing.put("午", "火");
        dizhiWuxing.put("未", "土");
        dizhiWuxing.put("申", "金");
        dizhiWuxing.put("酉", "金");
        dizhiWuxing.put("戌", "土");
        dizhiWuxing.put("亥", "水");
        
        // 统计年月日时四柱的天干地支五行，包括藏干
        String[] pillars = {
            baziDetail.getYearPillar(),
            baziDetail.getMonthPillar(),
            baziDetail.getDayPillar(),
            baziDetail.getHourPillar()
        };
        
        for (String pillar : pillars) {
            if (pillar != null && pillar.length() >= 2) {
                String tiangan = pillar.substring(0, 1);
                String dizhi = pillar.substring(1, 2);
                
                // 统计天干五行
                String tianganElement = tianganWuxing.get(tiangan);
                if (tianganElement != null) {
                    distribution.put(tianganElement, distribution.get(tianganElement) + 1);
                }
                
                // 统计地支本气五行
                String dizhiElement = dizhiWuxing.get(dizhi);
                if (dizhiElement != null) {
                    distribution.put(dizhiElement, distribution.get(dizhiElement) + 1);
                }
                
                // 统计地支藏干五行
                List<String> canggan = getCangganByDizhi(dizhi);
                for (String cangganChar : canggan) {
                    String cangganElement = tianganWuxing.get(cangganChar);
                    if (cangganElement != null) {
                        distribution.put(cangganElement, distribution.get(cangganElement) + 1);
                    }
                }
            }
        }
        
        return distribution;
    }
    
    /**
     * 获取日主五行
     */
    private String getDayMasterElement(User.WuxingProfile.BaziDetail baziDetail) {
        if (baziDetail == null || baziDetail.getDayPillar() == null || baziDetail.getDayPillar().length() < 1) {
            return "木"; // 默认值
        }
        
        String dayTiangan = baziDetail.getDayPillar().substring(0, 1);
        
        // 天干五行对照表
        Map<String, String> tianganWuxing = new HashMap<>();
        tianganWuxing.put("甲", "木");
        tianganWuxing.put("乙", "木");
        tianganWuxing.put("丙", "火");
        tianganWuxing.put("丁", "火");
        tianganWuxing.put("戊", "土");
        tianganWuxing.put("己", "土");
        tianganWuxing.put("庚", "金");
        tianganWuxing.put("辛", "金");
        tianganWuxing.put("壬", "水");
        tianganWuxing.put("癸", "水");
        
        return tianganWuxing.getOrDefault(dayTiangan, "木");
    }
    
    /**
     * 生成五行分析文字
     */
    private String generateWuxingAnalysisText(Map<String, Integer> wuxingDistribution, String dayMaster) {
        StringBuilder analysis = new StringBuilder();
        
        // 找出最旺和最弱的五行
        String strongestElement = "";
        String weakestElement = "";
        int maxCount = -1;
        int minCount = Integer.MAX_VALUE;
        
        for (Map.Entry<String, Integer> entry : wuxingDistribution.entrySet()) {
            int count = entry.getValue();
            if (count > maxCount) {
                maxCount = count;
                strongestElement = entry.getKey();
            }
            if (count < minCount) {
                minCount = count;
                weakestElement = entry.getKey();
            }
        }
        
        analysis.append("根据您的八字分析，");
        
        if (!strongestElement.equals(dayMaster)) {
            analysis.append("日主为").append(dayMaster).append("，");
        }
        
        if (maxCount > 0) {
            analysis.append("五行以").append(strongestElement).append("最旺");
            if (minCount == 0) {
                analysis.append("，").append(weakestElement).append("最弱");
            }
            analysis.append("。");
        }
        
        // 添加调和建议
        if (minCount == 0) {
            analysis.append("建议在日常生活中适当补充").append(weakestElement).append("元素，以平衡五行能量。");
        } else if (maxCount >= 3) {
            analysis.append("注意").append(strongestElement).append("过旺，需要适当调和其他五行。");
        } else {
            analysis.append("五行分布相对均衡，整体运势稳定。");
        }
        
        return analysis.toString();
    }

    /**
     * 构建八字组合
     */
    private CompleteFortuneReadingResponse.BaziCombination buildBaziCombination(User.WuxingProfile.BaziDetail baziDetail) {
        return CompleteFortuneReadingResponse.BaziCombination.builder()
                .year(buildPillarInfo(baziDetail.getYearPillar()))
                .month(buildPillarInfo(baziDetail.getMonthPillar()))
                .day(buildPillarInfo(baziDetail.getDayPillar()))
                .hour(buildPillarInfo(baziDetail.getHourPillar()))
                .build();
    }
    
    /**
     * 构建柱信息
     */
    private CompleteFortuneReadingResponse.BaziCombination.PillarInfo buildPillarInfo(String pillar) {
        if (pillar == null || pillar.length() < 2) {
            return CompleteFortuneReadingResponse.BaziCombination.PillarInfo.builder()
                    .tiangan("")
                    .dizhi("")
                    .canggan(new ArrayList<>())
                    .build();
        }
        
        String tiangan = pillar.substring(0, 1);
        String dizhi = pillar.substring(1, 2);
        List<String> canggan = getCangganByDizhi(dizhi);
        
        return CompleteFortuneReadingResponse.BaziCombination.PillarInfo.builder()
                .tiangan(tiangan)
                .dizhi(dizhi)
                .canggan(canggan)
                .build();
    }
    
    /**
     * 根据地支获取藏干
     */
    private List<String> getCangganByDizhi(String dizhi) {
        Map<String, List<String>> cangganMap = new HashMap<>();
        cangganMap.put("子", Arrays.asList("癸"));
        cangganMap.put("丑", Arrays.asList("己", "癸", "辛"));
        cangganMap.put("寅", Arrays.asList("甲", "丙", "戊"));
        cangganMap.put("卯", Arrays.asList("乙"));
        cangganMap.put("辰", Arrays.asList("戊", "乙", "癸"));
        cangganMap.put("巳", Arrays.asList("丙", "戊", "庚"));
        cangganMap.put("午", Arrays.asList("丁", "己"));
        cangganMap.put("未", Arrays.asList("己", "丁", "乙"));
        cangganMap.put("申", Arrays.asList("庚", "壬", "戊"));
        cangganMap.put("酉", Arrays.asList("辛"));
        cangganMap.put("戌", Arrays.asList("戊", "辛", "丁"));
        cangganMap.put("亥", Arrays.asList("壬", "甲"));
        
        return cangganMap.getOrDefault(dizhi, new ArrayList<>());
    }
    
    /**
     * 转换实体类为响应DTO
     */
    private CompleteFortuneReadingResponse convertToResponse(UserFortuneReading reading) {
        return CompleteFortuneReadingResponse.builder()
                .baziCombination(objectMapper.convertValue(reading.getBaziCombination(), CompleteFortuneReadingResponse.BaziCombination.class))
                .wuxingAnalysis(objectMapper.convertValue(reading.getWuxingAnalysis(), CompleteFortuneReadingResponse.WuxingAnalysis.class))
                .overallFortune(objectMapper.convertValue(reading.getOverallFortune(), CompleteFortuneReadingResponse.OverallFortune.class))
                .luckyAdvice(objectMapper.convertValue(reading.getLuckyAdvice(), CompleteFortuneReadingResponse.LuckyAdvice.class))
                .detailedFortune(objectMapper.convertValue(reading.getDetailedFortune(), CompleteFortuneReadingResponse.DetailedFortune.class))
                .build();
    }
    
    /**
     * 保存运势解读到缓存
     */
    private void saveFortuneReadingToCache(User user, LocalDate readingDate, CompleteFortuneReadingResponse response) {
        try {
            UserFortuneReading.UserFortuneReadingBuilder builder = UserFortuneReading.builder()
                    .userId(user.getUserId())
                    .readingDate(readingDate)
                    .aiGeneratedAt(LocalDateTime.now())
                    .createTime(new Date())
                    .updateTime(new Date());
            
            // 安全地转换每个字段，避免null值导致的问题
            if (response.getBaziCombination() != null) {
                builder.baziCombination(objectMapper.convertValue(response.getBaziCombination(), UserFortuneReading.BaziCombination.class));
            }
            
            if (response.getWuxingAnalysis() != null) {
                builder.wuxingAnalysis(objectMapper.convertValue(response.getWuxingAnalysis(), UserFortuneReading.WuxingAnalysis.class));
            }
            
            if (response.getOverallFortune() != null) {
                builder.overallFortune(objectMapper.convertValue(response.getOverallFortune(), UserFortuneReading.OverallFortune.class));
            }
            
            if (response.getLuckyAdvice() != null) {
                builder.luckyAdvice(objectMapper.convertValue(response.getLuckyAdvice(), UserFortuneReading.LuckyAdvice.class));
            }
            
            if (response.getDetailedFortune() != null) {
                builder.detailedFortune(objectMapper.convertValue(response.getDetailedFortune(), UserFortuneReading.DetailedFortune.class));
            }
            
            UserFortuneReading reading = builder.build();
            fortuneReadingRepository.insert(reading);
            log.info("运势解读已保存到缓存: userId={}, date={}", user.getUserId(), readingDate);
            
        } catch (Exception e) {
            log.error("保存运势解读到缓存失败: userId={}, date={}", user.getUserId(), readingDate, e);
            // 保存失败不影响返回结果
        }
    }
} 