package com.stylishlink.user.config;

import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.servers.Server;
import io.swagger.v3.oas.models.tags.Tag;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * 用户服务OpenAPI配置
 */
@Configuration
public class OpenAPIConfig {

    @Value("${server.port:8081}")
    private String serverPort;

    @Bean
    public OpenAPI userServiceOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("用户服务API")
                        .description("StylishLink用户服务API文档，提供用户认证、注册、信息管理等功能")
                        .version("1.0.0")
                        .contact(new Contact()
                                .name("StylishLink团队")
                                .email("<EMAIL>")
                                .url("https://stylishlink.com"))
                        .license(new License()
                                .name("MIT")
                                .url("https://opensource.org/licenses/MIT")))
                .servers(List.of(
                        new Server()
                                .url("http://localhost:" + serverPort)
                                .description("本地开发环境"),
                        new Server()
                                .url("https://api.stylishlink.com")
                                .description("生产环境")
                ))
                .addSecurityItem(new SecurityRequirement()
                        .addList("Bearer Authentication"))
                .components(new Components()
                        .addSecuritySchemes("Bearer Authentication", 
                                new SecurityScheme()
                                        .type(SecurityScheme.Type.HTTP)
                                        .scheme("bearer")
                                        .bearerFormat("JWT")
                                        .description("请在请求头中添加 Authorization: Bearer {token}")))
                .tags(List.of(
                        new Tag()
                                .name("认证管理")
                                .description("用户登录、注册、令牌验证等认证相关接口"),
                        new Tag()
                                .name("用户管理")
                                .description("用户信息查询、更新、偏好设置等用户管理接口"),
                        new Tag()
                                .name("个人资料")
                                .description("用户个人资料、五行命理信息等接口")
                ));
    }
} 