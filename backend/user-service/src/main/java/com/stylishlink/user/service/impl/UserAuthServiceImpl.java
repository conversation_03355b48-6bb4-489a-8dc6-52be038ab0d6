package com.stylishlink.user.service.impl;

import com.stylishlink.user.entity.UserAuth;
import com.stylishlink.user.repository.UserAuthRepository;
import com.stylishlink.user.service.UserAuthService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.Optional;

/**
 * 用户认证服务实现
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class UserAuthServiceImpl implements UserAuthService {
    
    private final UserAuthRepository userAuthRepository;
    
    @Override
    @Transactional
    public UserAuth saveAuthInfo(String userId, String token, Date expiresAt) {
        log.info("保存用户认证信息: userId={}", userId);
        
        // 先删除该用户现有的认证信息（确保一个用户只有一个有效的认证记录）
        userAuthRepository.deleteByUserId(userId);
        
        // 创建新的认证记录（refresh_token字段留空）
        UserAuth userAuth = UserAuth.builder()
                .userId(userId)
                .token(token)
                .refreshToken(null)  // 不保存refresh_token
                .expiresAt(expiresAt)
                .createdAt(new Date())
                .build();
        
        userAuthRepository.insert(userAuth);
        log.info("用户认证信息保存成功: userId={}, id={}", userId, userAuth.getId());
        
        return userAuth;
    }
    
    @Override
    public Optional<UserAuth> findByToken(String token) {
        return userAuthRepository.findByToken(token);
    }
    
    @Override
    public Optional<UserAuth> findByRefreshToken(String refreshToken) {
        return userAuthRepository.findByRefreshToken(refreshToken);
    }
    
    @Override
    public Optional<UserAuth> findByUserId(String userId) {
        return userAuthRepository.findByUserId(userId);
    }
    
    @Override
    @Transactional
    public boolean removeByUserId(String userId) {
        log.info("删除用户认证信息: userId={}", userId);
        int deletedCount = userAuthRepository.deleteByUserId(userId);
        boolean success = deletedCount > 0;
        log.info("删除用户认证信息结果: userId={}, 删除记录数={}, 成功={}", userId, deletedCount, success);
        return success;
    }
    
    @Override
    @Transactional
    public boolean removeByToken(String token) {
        log.info("根据token删除认证信息");
        int deletedCount = userAuthRepository.deleteByToken(token);
        boolean success = deletedCount > 0;
        log.info("根据token删除认证信息结果: 删除记录数={}, 成功={}", deletedCount, success);
        return success;
    }
    
    @Override
    @Transactional
    public UserAuth updateAuthInfo(String userId, String newToken, Date newExpiresAt) {
        log.info("更新用户认证信息: userId={}", userId);
        
        // 查找现有的认证记录
        Optional<UserAuth> existingAuth = findByUserId(userId);
        
        if (existingAuth.isPresent()) {
            // 更新现有记录（refresh_token保持为空）
            UserAuth userAuth = existingAuth.get();
            userAuth.setToken(newToken);
            userAuth.setRefreshToken(null);  // 保持refresh_token为空
            userAuth.setExpiresAt(newExpiresAt);
            
            userAuthRepository.updateById(userAuth);
            log.info("用户认证信息更新成功: userId={}", userId);
            return userAuth;
        } else {
            // 如果不存在，则创建新记录
            log.info("未找到现有认证记录，创建新记录: userId={}", userId);
            return saveAuthInfo(userId, newToken, newExpiresAt);
        }
    }
} 