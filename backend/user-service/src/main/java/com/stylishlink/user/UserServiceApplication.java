package com.stylishlink.user;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.mongo.MongoDataAutoConfiguration;
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;

/**
 * 用户服务应用启动类
 */
@SpringBootApplication(
        scanBasePackages = {"com.stylishlink.user", "com.stylishlink.common"},
        exclude = {MongoAutoConfiguration.class, MongoDataAutoConfiguration.class}
)
@EnableDiscoveryClient
@EnableFeignClients
public class UserServiceApplication {
    
    public static void main(String[] args) {
        SpringApplication.run(UserServiceApplication.class, args);
    }
} 