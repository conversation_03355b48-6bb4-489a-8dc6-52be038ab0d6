package com.stylishlink.user.dto.response;

import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

/**
 * 今日能量简要信息响应DTO
 * 适用于首页展示或小卡片场景
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TodayEnergyBriefResponse {
    
    /**
     * 日期信息
     */
    private DateInfo dateInfo;
    
    /**
     * 今日总能量分数(0-100)
     */
    private Integer totalScore;
    
    /**
     * 超过的用户百分比
     */
    private Integer percentage;
    
    /**
     * 幸运元素简要
     */
    private LuckyElements luckyElements;
    
    /**
     * 日期信息
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class DateInfo {
        /**
         * 公历日期
         */
        private String gregorian;
        
        /**
         * 农历信息
         */
        private String lunar;
    }
    
    /**
     * 幸运元素简要
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class LuckyElements {
        /**
         * 服饰建议总结
         */
        private String clothingSummary;
        
        /**
         * 配饰建议总结
         */
        private String accessoriesSummary;
        
        /**
         * 妆容建议总结
         */
        private String makeupSummary;
    }
} 