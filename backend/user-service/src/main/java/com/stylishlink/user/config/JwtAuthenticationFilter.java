package com.stylishlink.user.config;

import com.stylishlink.user.service.JwtService;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.NonNull;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.util.Collections;

/**
 * JWT认证过滤器
 * 
 * 特点：
 * 1. 只负责解析JWT并设置SecurityContext，不做任何拦截
 * 2. 如果token无效或不存在，不返回错误，让请求正常通过
 * 3. 保持白名单逻辑不受影响
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class JwtAuthenticationFilter extends OncePerRequestFilter {
    
    private final JwtService jwtService;
    
    @Override
    protected void doFilterInternal(
            @NonNull HttpServletRequest request,
            @NonNull HttpServletResponse response,
            @NonNull FilterChain filterChain) throws ServletException, IOException {
        
        try {
            // 提取JWT token
            String token = extractTokenFromRequest(request);
            
            // 如果有token且SecurityContext中还没有认证信息
            if (StringUtils.hasText(token) && SecurityContextHolder.getContext().getAuthentication() == null) {
                
                // 验证token有效性
                if (jwtService.validateToken(token)) {
                    // 从token中提取用户ID
                    String userId = jwtService.extractUserId(token);
                    
                    if (StringUtils.hasText(userId)) {
                        // 创建认证对象
                        UsernamePasswordAuthenticationToken authentication = 
                            new UsernamePasswordAuthenticationToken(
                                userId,  // principal
                                null,    // credentials
                                Collections.emptyList()  // authorities
                            );
                        
                        // 设置请求详情
                        authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                        
                        // 设置到SecurityContext
                        SecurityContextHolder.getContext().setAuthentication(authentication);
                        
                        log.debug("JWT认证成功，用户ID: {}", userId);
                    }
                } else {
                    log.debug("JWT token验证失败，但允许请求继续（白名单逻辑）");
                }
            }
            
        } catch (Exception e) {
            // 捕获所有异常，不影响请求处理
            log.warn("JWT认证过程中发生异常，但允许请求继续: {}", e.getMessage());
        }
        
        // 无论认证是否成功，都继续执行过滤器链
        filterChain.doFilter(request, response);
    }
    
    /**
     * 从请求中提取JWT token
     */
    private String extractTokenFromRequest(HttpServletRequest request) {
        // 1. 优先从Authorization头部获取
        String authHeader = request.getHeader("Authorization");
        if (StringUtils.hasText(authHeader) && authHeader.startsWith("Bearer ")) {
            return authHeader.substring(7);
        }
        
        // 2. 从网关传递的用户ID头部判断是否需要创建token（可选）
        String gatewayUserId = request.getHeader("X-Authenticated-User-Id");
        if (StringUtils.hasText(gatewayUserId)) {
            // 如果网关已经认证了用户，直接使用网关的信息
            log.debug("检测到网关传递的用户ID: {}", gatewayUserId);
            // 这里可以选择不处理，让网关头部信息处理逻辑生效
        }
        
        return null;
    }
} 