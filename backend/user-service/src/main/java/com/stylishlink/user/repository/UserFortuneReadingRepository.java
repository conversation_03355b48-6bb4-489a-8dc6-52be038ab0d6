package com.stylishlink.user.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.stylishlink.user.entity.UserFortuneReading;
import org.apache.ibatis.annotations.Mapper;

import java.time.LocalDate;
import java.util.Optional;

/**
 * 用户完整运势解读仓库接口
 */
@Mapper
public interface UserFortuneReadingRepository extends BaseMapper<UserFortuneReading> {
    
    /**
     * 根据用户ID和日期查询运势解读
     * @param userId 用户ID
     * @param readingDate 解读日期
     * @return 运势解读信息
     */
    default Optional<UserFortuneReading> findByUserIdAndDate(String userId, LocalDate readingDate) {
        UserFortuneReading reading = selectOne(new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<UserFortuneReading>()
                .eq("user_id", userId)
                .eq("reading_date", readingDate));
        return Optional.ofNullable(reading);
    }
    
    /**
     * 检查用户某日期的运势解读是否存在
     * @param userId 用户ID
     * @param readingDate 解读日期
     * @return 是否存在
     */
    default boolean existsByUserIdAndDate(String userId, LocalDate readingDate) {
        Long count = selectCount(new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<UserFortuneReading>()
                .eq("user_id", userId)
                .eq("reading_date", readingDate));
        return count > 0;
    }
    
    /**
     * 根据用户ID和年月查询运势解读（查找当月的任意一条记录）
     * @param userId 用户ID
     * @param year 年份
     * @param month 月份
     * @return 运势解读信息
     */
    default Optional<UserFortuneReading> findByUserIdAndYearMonth(String userId, int year, int month) {
        LocalDate startOfMonth = LocalDate.of(year, month, 1);
        LocalDate endOfMonth = startOfMonth.withDayOfMonth(startOfMonth.lengthOfMonth());
        
        UserFortuneReading reading = selectOne(new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<UserFortuneReading>()
                .eq("user_id", userId)
                .ge("reading_date", startOfMonth)
                .le("reading_date", endOfMonth)
                .last("LIMIT 1"));
        return Optional.ofNullable(reading);
    }
    
    /**
     * 检查用户某年月的运势解读是否存在
     * @param userId 用户ID
     * @param year 年份
     * @param month 月份
     * @return 是否存在
     */
    default boolean existsByUserIdAndYearMonth(String userId, int year, int month) {
        LocalDate startOfMonth = LocalDate.of(year, month, 1);
        LocalDate endOfMonth = startOfMonth.withDayOfMonth(startOfMonth.lengthOfMonth());
        
        Long count = selectCount(new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<UserFortuneReading>()
                .eq("user_id", userId)
                .ge("reading_date", startOfMonth)
                .le("reading_date", endOfMonth));
        return count > 0;
    }
} 