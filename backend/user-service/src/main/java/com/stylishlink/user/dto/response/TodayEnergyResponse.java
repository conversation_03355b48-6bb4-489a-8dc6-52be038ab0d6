package com.stylishlink.user.dto.response;

import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * 今日能量响应DTO
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TodayEnergyResponse {
    
    /**
     * 日期信息
     */
    private DateInfo dateInfo;
    
    /**
     * 今日总能量分数(0-100)
     */
    private Integer totalScore;
    
    /**
     * 超过的用户百分比
     */
    private Integer percentage;
    
    /**
     * 能量高峰时段
     */
    private String peakTime;
    
    /**
     * 高峰时段描述
     */
    private String peakTimeDescription;
    
    /**
     * 今日能量总体描述
     */
    private String description;
    
    /**
     * 五维能量评分
     */
    private Dimensions dimensions;
    
    /**
     * 宜忌指南
     */
    private Advice advice;
    
    /**
     * 幸运元素
     */
    private LuckyElements luckyElements;
    
    /**
     * 日期信息
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class DateInfo {
        /**
         * 公历日期
         */
        private String gregorian;
        
        /**
         * 农历信息
         */
        private String lunar;
    }
    
    /**
     * 五维能量评分
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Dimensions {
        /**
         * 爱情运势(0-100)
         */
        private Integer love;
        
        /**
         * 事业运势(0-100)
         */
        private Integer career;
        
        /**
         * 财富运势(0-100)
         */
        private Integer wealth;
        
        /**
         * 健康运势(0-100)
         */
        private Integer health;
        
        /**
         * 人际运势(0-100)
         */
        private Integer relationship;
    }
    
    /**
     * 宜忌指南
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Advice {
        /**
         * 宜忌分类列表
         */
        private List<Category> categories;
        
        /**
         * 生活建议列表
         */
        private List<LifeSuggestion> lifeSuggestions;
    }
    
    /**
     * 宜忌分类
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Category {
        /**
         * 类型（suitable/avoid）
         */
        private String type;
        
        /**
         * 标签（宜做事项/忌做事项）
         */
        private String label;
        
        /**
         * 具体事项列表
         */
        private List<Item> items;
    }
    
    /**
     * 具体事项
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Item {
        /**
         * 事项ID
         */
        private String id;
        
        /**
         * 图标
         */
        private String icon;
        
        /**
         * 文本描述
         */
        private String text;
    }
    
    /**
     * 生活建议
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class LifeSuggestion {
        /**
         * 图标
         */
        private String icon;
        
        /**
         * 建议内容
         */
        private String content;
    }
    
    /**
     * 幸运元素
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class LuckyElements {
        /**
         * 幸运色列表
         */
        private List<ColorItem> colors;
        
        /**
         * 服饰建议列表
         */
        private List<String> clothing;
        
        /**
         * 配饰建议列表
         */
        private List<String> accessories;
        
        /**
         * 妆容建议列表
         */
        private List<String> makeup;
    }
    
    /**
     * 颜色项
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ColorItem {
        /**
         * 颜色值 (HTML颜色代码)
         */
        private String value;
        
        /**
         * 颜色名称 (中文名称)
         */
        private String name;
    }
} 