package com.stylishlink.user.client;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.Map;

/**
 * 文件服务客户端接口
 */
@FeignClient(name = "file-service", url = "${file-service.url:http://localhost:8082}")
public interface FileServiceClient {
    
    /**
     * 根据文件ID获取文件信息
     * @param fileId 文件ID
     * @return 文件信息（包含URL）
     */
    @GetMapping("/api/files/{fileId}")
    Map<String, Object> getFileInfo(@PathVariable("fileId") String fileId);
    
    /**
     * 根据文件ID获取文件URL
     * @param fileId 文件ID
     * @return 文件信息（包含URL）
     */
    @GetMapping("/api/files/{fileId}/url")
    Map<String, Object> getFileUrl(@PathVariable("fileId") String fileId);
} 