package com.stylishlink.user.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 五行命理响应实体
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WuxingResponse {
    
    /**
     * 操作是否成功
     */
    private Boolean success;
    
    /**
     * 响应消息
     */
    private String message;
    
    /**
     * 用户姓名
     */
    private String fullName;
    
    /**
     * 出生日期
     */
    private String birthDate;
    
    /**
     * 出生时间
     */
    private String birthTime;
    
    /**
     * 出生地点
     */
    private String birthPlace;
    
    /**
     * 八字结果
     */
    private BaziResult baziResult;
    
    /**
     * 五行分析
     */
    private WuxingAnalysis wuxingAnalysis;
    
    /**
     * 用户是否有五行信息
     */
    private Boolean hasWuxingInfo;
    
    /**
     * 八字结果
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BaziResult {
        private String yearPillar;     // 年柱
        private String monthPillar;    // 月柱
        private String dayPillar;      // 日柱
        private String hourPillar;     // 时柱
        private String dayMaster;      // 日主
        private List<String> elements; // 五行元素
    }
    
    /**
     * 五行分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class WuxingAnalysis {
        private String primaryElement;      // 主要五行
        private String secondaryElement;    // 次要五行
        private String luckyColor;          // 幸运色
        private String avoidColor;          // 避忌色
        private List<String> characteristics; // 特征描述
        private List<String> suggestions;   // 建议
    }
} 