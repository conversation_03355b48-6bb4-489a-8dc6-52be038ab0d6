package com.stylishlink.user.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 用户模式响应实体
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserModeResponse {
    
    /**
     * 操作是否成功
     */
    private Boolean success;
    
    /**
     * 响应消息
     */
    private String message;
    
    /**
     * 当前用户模式
     */
    private String mode;
    
    /**
     * 模式描述
     */
    private String modeDescription;
} 