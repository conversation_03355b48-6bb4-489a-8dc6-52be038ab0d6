package com.stylishlink.user.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 八字计算响应实体
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BaziResponse {
    
    /**
     * 操作是否成功
     */
    private Boolean success;
    
    /**
     * 响应消息
     */
    private String message;
    
    /**
     * 八字结果
     */
    private BaziResult baziResult;
    
    /**
     * 八字结果
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BaziResult {
        private String yearPillar;     // 年柱
        private String monthPillar;    // 月柱
        private String dayPillar;      // 日柱
        private String hourPillar;     // 时柱
        private String dayMaster;      // 日主
        private List<String> elements; // 五行元素
        
        // 新增：详细信息字段
        private DetailInfo detailInfo;
    }
    
    /**
     * 详细信息对象
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DetailInfo {
        private PillarDetail yearPillarDetail;    // 年柱详细信息
        private PillarDetail monthPillarDetail;   // 月柱详细信息
        private PillarDetail dayPillarDetail;     // 日柱详细信息
        private PillarDetail hourPillarDetail;    // 时柱详细信息
        private WuxingStatistics wuxingStatistics; // 五行统计信息
    }
    
    /**
     * 柱详细信息对象
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PillarDetail {
        private HeavenlyStemInfo heavenlyStem;    // 天干信息
        private EarthlyBranchInfo earthlyBranch;  // 地支信息
    }
    
    /**
     * 天干信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class HeavenlyStemInfo {
        private String character;  // 天干字符 (如"甲")
        private String element;    // 五行属性 (金木水火土)
    }
    
    /**
     * 地支信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class EarthlyBranchInfo {
        private String character;          // 地支字符 (如"子")
        private String element;            // 五行属性 (金木水火土)
        private List<HiddenStemInfo> hiddenStems; // 藏干信息列表
    }
    
    /**
     * 藏干信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class HiddenStemInfo {
        private String character;  // 藏干字符 (如"癸")
        private String element;    // 五行属性 (金木水火土)
        private String strength;   // 强度 (主气/中气/余气)
    }
    
    /**
     * 五行统计信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class WuxingStatistics {
        private Map<String, Integer> distribution; // 五行分布统计
        private String dominantElement;           // 主导五行
        private String weakestElement;            // 最弱五行
        private Integer totalCount;               // 总计数（含藏干）
    }
} 