package com.stylishlink.user.service.impl;

import com.stylishlink.user.entity.User;
import com.stylishlink.common.exception.BusinessException;
import com.stylishlink.user.dto.request.CompleteProfileRequest;
import com.stylishlink.user.dto.request.UpdateProfileRequest;
import com.stylishlink.user.dto.response.UserResponse;
import com.stylishlink.user.dto.response.BodyShapeAnalysisResponse;
import com.stylishlink.user.dto.response.ProfileResponse;
import com.stylishlink.user.dto.response.UserModeResponse;
import com.stylishlink.user.dto.response.BaziResponse;
import com.stylishlink.user.dto.response.TodayEnergyResponse;
import com.stylishlink.user.dto.response.TodayEnergyBriefResponse;
import com.stylishlink.user.entity.UserDailyEnergy;
import com.stylishlink.user.repository.UserRepository;
import com.stylishlink.user.service.UserDailyEnergyService;
import com.stylishlink.user.service.JwtService;
import com.stylishlink.user.service.UserService;
import com.stylishlink.user.service.WuxingService;
import com.stylishlink.user.client.AIServiceClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.List;
import java.util.ArrayList;

/**
 * 用户服务实现
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class UserServiceImpl implements UserService {
    
    private final UserRepository userRepository;
    private final JwtService jwtService;
    private final UserDailyEnergyService userDailyEnergyService;
    private final ApplicationContext applicationContext;
    
    @Autowired(required = false)
    private AIServiceClient aiServiceClient;
    
    @Autowired(required = false)
    private Executor baziGenerationExecutor;
    
    @Override
    public Optional<User> findById(String id) {
        try {
            Long userId = Long.parseLong(id);
            User user = userRepository.selectById(userId);
            return Optional.ofNullable(user);
        } catch (NumberFormatException e) {
            return Optional.empty();
        }
    }
    
    @Override
    public Optional<User> findByUserId(String userId) {
        return userRepository.findByUserId(userId);
    }
    
    @Override
    public UserResponse findUserResponseByUserId(String userId) {
        return findByUserId(userId)
                .map(this::toUserResponse)
                .orElseThrow(() -> new BusinessException("USER_NOT_FOUND", "用户不存在"));
    }
    
    @Override
    public Optional<User> findByOpenId(String openId) {
        return userRepository.findByOpenId(openId);
    }
    
    @Override
    public Optional<User> findByPhone(String phone) {
        return userRepository.findByPhone(phone);
    }
    
    @Override
    public Optional<User> findByEmail(String email) {
        return userRepository.findByEmail(email);
    }
    
    @Override
    public Optional<User> findByUsername(String username) {
        // 用户名可能是userId、手机号或邮箱
        Optional<User> user = findByUserId(username);
        if (user.isPresent()) {
            return user;
        }
        
        user = findByPhone(username);
        if (user.isPresent()) {
            return user;
        }
        
        return findByEmail(username);
    }
    
    @Override
    public User save(User user) {
        if (user.getId() == null) {
            userRepository.insert(user);
        } else {
            userRepository.updateById(user);
        }
        return user;
    }
    
    @Override
    public boolean existsByUserId(String userId) {
        return userRepository.existsByUserId(userId);
    }
    
    @Override
    public boolean existsByOpenId(String openId) {
        return userRepository.existsByOpenId(openId);
    }
    
    @Override
    public boolean existsByPhone(String phone) {
        return userRepository.existsByPhone(phone);
    }
    
    @Override
    public boolean existsByEmail(String email) {
        return userRepository.existsByEmail(email);
    }
    
    @Override
    public UserResponse toUserResponse(User user) {
        return UserResponse.builder()
                .id(user.getId())
                .userId(user.getUserId())
                .nickname(user.getNickname())
                .avatar(user.getAvatar())
                .gender(user.getGender())
                .phone(user.getPhone())
                .email(user.getEmail())
                .photoUrl(user.getPhotoUrl())
                .height(user.getHeight())
                .weight(user.getWeight())
                .bodyType(user.getBodyType())
                .skinTone(user.getSkinTone())
                .stylePreferences(user.getStylePreferences())
                .bodyShape(user.getBodyShape())
                .mode(user.getMode())
                .residenceName(user.getResidenceName())
                .residenceCode(user.getResidenceCode())
                .fullName(user.getFullName())
                .birthDate(user.getBirthDate())
                .birthTime(user.getBirthTime())
                .birthPlace(user.getBirthPlace())
                .createTime(user.getCreateTime())
                .updateTime(user.getUpdateTime())
                .build();
    }
    
    @Override
    public String generateTokenForUser(String userId) {
        return jwtService.generateToken(userId);
    }
    
    @Override
    public ProfileResponse completeProfile(String userId, CompleteProfileRequest request) {
        log.info("完善用户信息: {}", userId);
        
        try {
            User user = findByUserId(userId)
                    .orElseThrow(() -> new BusinessException("USER_NOT_FOUND", "用户不存在"));
            
            // 保存更新前的用户状态，用于检测字段变更
            User oldUser = User.builder()
                    .fullName(user.getFullName())
                    .birthDate(user.getBirthDate())
                    .birthTime(user.getBirthTime())
                    .birthPlace(user.getBirthPlace())
                    .gender(user.getGender())
                    .mode(user.getMode())
                    .build();
            
            // 更新用户信息
            updateUserFromRequest(user, request);
            user.setUpdateTime(new Date());
            
            // 保存用户
            user = save(user);
            
            // 检查是否需要异步生成八字（原有逻辑：首次生成）
            boolean shouldGenerate = shouldAsyncGenerateBazi(request, user);
            
            // 检查是否需要重新计算八字（新逻辑：字段变更）
            boolean shouldRecalculate = shouldRecalculateBazi(oldUser, request);
            
            if (shouldGenerate || shouldRecalculate) {
                String reason = shouldGenerate ? "首次生成" : "字段变更重新计算";
                log.info("满足异步八字{}条件，启动异步任务，用户: {}", reason, userId);
                
                // 使用 CompletableFuture 确保真正异步执行
                final String finalUserId = userId;
                final String finalFullName = user.getFullName();
                final String finalBirthDate = user.getBirthDate();
                final String finalBirthTime = user.getBirthTime();
                final String finalBirthPlace = user.getBirthPlace();
                final Integer finalGender = user.getGender();
                
                if (baziGenerationExecutor != null) {
                    CompletableFuture.runAsync(() -> {
                        asyncGenerateBazi(finalUserId, finalFullName, finalBirthDate, 
                                finalBirthTime, finalBirthPlace, finalGender);
                    }, baziGenerationExecutor);
                    log.info("异步八字{}任务已提交到专用线程池，用户: {}", reason, userId);
                } else {
                    // 备用方案：使用默认线程池
                    CompletableFuture.runAsync(() -> {
                        asyncGenerateBazi(finalUserId, finalFullName, finalBirthDate, 
                                finalBirthTime, finalBirthPlace, finalGender);
                    });
                    log.info("异步八字{}任务已提交到默认线程池，用户: {}", reason, userId);
                }
            }
            
            return ProfileResponse.builder()
                    .success(true)
                    .message("用户信息完善成功")
                    .hasBasicInfo(user.hasBasicInfo())
                    .hasWuxingInfo(user.hasWuxingInfo())
                    .userInfo(toUserResponse(user))
                    .build();
                    
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("完善用户信息失败: {}", userId, e);
            throw new BusinessException("PROFILE_UPDATE_FAILED", "用户信息完善失败: " + e.getMessage());
        }
    }
    
    /**
     * 判断是否需要异步生成八字
     */
    private boolean shouldAsyncGenerateBazi(CompleteProfileRequest request, User user) {
        // 1. 检查模式是否为 energy
        if (!"energy".equals(request.getMode())) {
            log.debug("用户模式不是energy，跳过八字生成，用户: {}", user.getUserId());
            return false;
        }
        
        // 2. 检查是否已有五行信息
        if (user.hasWuxingInfo()) {
            log.debug("用户已有五行信息，跳过八字生成，用户: {}", user.getUserId());
            return false;
        }
        
        // 3. 检查是否包含必要的出生信息
        if (user.getFullName() == null || user.getFullName().trim().isEmpty()) {
            log.debug("用户姓名为空，跳过八字生成，用户: {}", user.getUserId());
            return false;
        }
        
        if (user.getBirthDate() == null || user.getBirthDate().trim().isEmpty()) {
            log.debug("用户出生日期为空，跳过八字生成，用户: {}", user.getUserId());
            return false;
        }
        
        if (user.getBirthTime() == null || user.getBirthTime().trim().isEmpty()) {
            log.debug("用户出生时间为空，跳过八字生成，用户: {}", user.getUserId());
            return false;
        }
        
        if (user.getBirthPlace() == null || user.getBirthPlace().trim().isEmpty()) {
            log.debug("用户出生地点为空，跳过八字生成，用户: {}", user.getUserId());
            return false;
        }
        
        if (user.getGender() == null) {
            log.debug("用户性别为空，跳过八字生成，用户: {}", user.getUserId());
            return false;
        }
        
        log.info("满足异步八字生成条件，用户: {}，模式: {}，姓名: {}，出生日期: {}，出生时间: {}，出生地点: {}，性别: {}", 
                user.getUserId(), request.getMode(), user.getFullName(), user.getBirthDate(), 
                user.getBirthTime(), user.getBirthPlace(), user.getGender());
        return true;
    }
    
    @Override
    public ProfileResponse updateProfile(String userId, UpdateProfileRequest request) {
        log.info("更新用户档案: {}", userId);
        
        try {
            User user = findByUserId(userId)
                    .orElseThrow(() -> new BusinessException("USER_NOT_FOUND", "用户不存在"));
            
            // 更新用户信息
            updateUserFromUpdateRequest(user, request);
            user.setUpdateTime(new Date());
            
            // 保存用户
            user = save(user);
            
            return ProfileResponse.builder()
                    .success(true)
                    .message("用户档案更新成功")
                    .hasBasicInfo(user.hasBasicInfo())
                    .hasWuxingInfo(user.hasWuxingInfo())
                    .userInfo(toUserResponse(user))
                    .build();
                    
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("更新用户档案失败: {}", userId, e);
            throw new BusinessException("PROFILE_UPDATE_FAILED", "用户档案更新失败: " + e.getMessage());
        }
    }
    
    @Override
    public UserModeResponse switchUserMode(String userId, String mode) {
        log.info("切换用户模式: {} -> {}", userId, mode);
        
        try {
            User user = findByUserId(userId)
                    .orElseThrow(() -> new BusinessException("USER_NOT_FOUND", "用户不存在"));
            
            // 验证模式有效性
            if (!"natural".equals(mode) && !"energy".equals(mode)) {
                throw new BusinessException("INVALID_MODE", "无效的用户模式");
            }
            
            user.setMode(mode);
            user.setUpdateTime(new Date());
            save(user);
            
            String modeDescription = "natural".equals(mode) ? "自然风格模式" : "能量流转模式";
            
            return UserModeResponse.builder()
                    .success(true)
                    .message("用户模式切换成功")
                    .mode(mode)
                    .modeDescription(modeDescription)
                    .build();
                    
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("切换用户模式失败: {}", userId, e);
            throw new BusinessException("MODE_SWITCH_FAILED", "用户模式切换失败: " + e.getMessage());
        }
    }
    
    @Override
    public UserModeResponse getUserMode(String userId) {
        log.info("获取用户模式: {}", userId);
        
        try {
            User user = findByUserId(userId)
                    .orElseThrow(() -> new BusinessException("USER_NOT_FOUND", "用户不存在"));
            
            String mode = user.getMode() != null ? user.getMode() : "natural";
            String modeDescription = "natural".equals(mode) ? "自然风格模式" : "能量流转模式";
            
            return UserModeResponse.builder()
                    .success(true)
                    .message("获取用户模式成功")
                    .mode(mode)
                    .modeDescription(modeDescription)
                    .build();
                    
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取用户模式失败: {}", userId, e);
            throw new BusinessException("GET_MODE_FAILED", "获取用户模式失败: " + e.getMessage());
        }
    }
    
    /**
     * 检查用户的5个关键字段是否都有值
     * @param user 用户对象
     * @return 是否满足条件
     */
    private boolean hasRequiredWuxingFields(User user) {
        if (user == null) {
            return false;
        }
        
        return user.getFullName() != null && !user.getFullName().trim().isEmpty() &&
               user.getBirthDate() != null && !user.getBirthDate().trim().isEmpty() &&
               user.getBirthTime() != null && !user.getBirthTime().trim().isEmpty() &&
               user.getBirthPlace() != null && !user.getBirthPlace().trim().isEmpty() &&
               user.getGender() != null;
    }
    
    /**
     * 检测关键字段是否发生变更
     * @param oldUser 更新前的用户状态
     * @param request 更新请求
     * @return 是否有字段发生变更
     */
    private boolean hasWuxingFieldsChanged(User oldUser, CompleteProfileRequest request) {
        // 检查姓名是否变更
        if (request.getFullName() != null && 
            !Objects.equals(oldUser.getFullName(), request.getFullName())) {
            return true;
        }
        
        // 检查出生日期是否变更
        if (request.getBirthDate() != null && 
            !Objects.equals(oldUser.getBirthDate(), request.getBirthDate())) {
            return true;
        }
        
        // 检查出生时间是否变更
        if (request.getBirthTime() != null && 
            !Objects.equals(oldUser.getBirthTime(), request.getBirthTime())) {
            return true;
        }
        
        // 检查出生地点是否变更
        if (request.getBirthPlace() != null && 
            !Objects.equals(oldUser.getBirthPlace(), request.getBirthPlace())) {
            return true;
        }
        
        // 检查性别是否变更
        if (request.getGender() != null && 
            !Objects.equals(oldUser.getGender(), request.getGender())) {
            return true;
        }
        
        return false;
    }
    
    /**
     * 判断是否需要重新计算八字
     * @param oldUser 更新前的用户状态
     * @param request 更新请求
     * @return 是否需要重新计算八字
     */
    private boolean shouldRecalculateBazi(User oldUser, CompleteProfileRequest request) {
        // 1. 检查模式是否为 energy
        if (!"energy".equals(request.getMode()) && !"energy".equals(oldUser.getMode())) {
            return false;
        }
        
        // 2. 检查关键字段是否有变更
        return hasWuxingFieldsChanged(oldUser, request);
    }
    
    /**
     * 从CompleteProfileRequest更新用户信息
     */
    private void updateUserFromRequest(User user, CompleteProfileRequest request) {
        if (request.getNickname() != null) {
            user.setNickname(request.getNickname());
        }
        if (request.getAvatar() != null) {
            user.setAvatar(request.getAvatar());
        }
        if (request.getGender() != null) {
            user.setGender(request.getGender());
        }
        if (request.getPhotoUrl() != null) {
            user.setPhotoUrl(request.getPhotoUrl());
        }
        if (request.getHeight() != null) {
            user.setHeight(request.getHeight());
        }
        if (request.getWeight() != null) {
            user.setWeight(request.getWeight());
        }
        if (request.getBodyType() != null) {
            user.setBodyType(request.getBodyType());
        }
        if (request.getSkinTone() != null) {
            user.setSkinTone(request.getSkinTone());
        }
        if (request.getStylePreferences() != null) {
            user.setStylePreferences(request.getStylePreferences());
        }
        if (request.getBodyShape() != null) {
            user.setBodyShape(request.getBodyShape());
        }
        if (request.getMode() != null) {
            user.setMode(request.getMode());
        }
        if (request.getResidenceName() != null) {
            user.setResidenceName(request.getResidenceName());
        }
        if (request.getResidenceCode() != null) {
            user.setResidenceCode(request.getResidenceCode());
        }
        if (request.getFullName() != null) {
            user.setFullName(request.getFullName());
        }
        if (request.getBirthDate() != null) {
            user.setBirthDate(request.getBirthDate());
        }
        if (request.getBirthTime() != null) {
            user.setBirthTime(request.getBirthTime());
        }
        if (request.getBirthPlace() != null) {
            user.setBirthPlace(request.getBirthPlace());
        }
    }
    
    /**
     * 从UpdateProfileRequest更新用户信息
     */
    private void updateUserFromUpdateRequest(User user, UpdateProfileRequest request) {
        if (request.getNickname() != null) {
            user.setNickname(request.getNickname());
        }
        if (request.getAvatar() != null) {
            user.setAvatar(request.getAvatar());
        }
        if (request.getGender() != null) {
            user.setGender(request.getGender());
        }
        if (request.getPhotoUrl() != null) {
            user.setPhotoUrl(request.getPhotoUrl());
        }
        if (request.getHeight() != null) {
            user.setHeight(request.getHeight());
        }
        if (request.getWeight() != null) {
            user.setWeight(request.getWeight());
        }
        if (request.getBodyType() != null) {
            user.setBodyType(request.getBodyType());
        }
        if (request.getSkinTone() != null) {
            user.setSkinTone(request.getSkinTone());
        }
        if (request.getStylePreferences() != null) {
            user.setStylePreferences(request.getStylePreferences());
        }
        if (request.getBodyShape() != null) {
            user.setBodyShape(request.getBodyShape());
        }
        if (request.getMode() != null) {
            user.setMode(request.getMode());
        }
        if (request.getResidenceName() != null) {
            user.setResidenceName(request.getResidenceName());
        }
        if (request.getResidenceCode() != null) {
            user.setResidenceCode(request.getResidenceCode());
        }
        if (request.getFullName() != null) {
            user.setFullName(request.getFullName());
        }
        if (request.getBirthDate() != null) {
            user.setBirthDate(request.getBirthDate());
        }
        if (request.getBirthTime() != null) {
            user.setBirthTime(request.getBirthTime());
        }
        if (request.getBirthPlace() != null) {
            user.setBirthPlace(request.getBirthPlace());
        }
    }
    
    @Override
    public Map<String, Object> updateBodyShapeFromAnalysis(String userId, Map<String, Object> analysisResult) {
        log.info("根据身材分析结果更新用户信息: {}", userId);
        
        User user = findByUserId(userId)
                .orElseThrow(() -> new BusinessException("USER_NOT_FOUND", "用户不存在"));
        
        boolean updated = false;
        
        try {
            // 更新bodyType字段（如果AI分析中有推荐的体型）
            Object bodyType = analysisResult.get("bodyType");
            if (bodyType != null && !bodyType.toString().trim().isEmpty()) {
                user.setBodyType(bodyType.toString());
                updated = true;
                log.debug("更新用户体型: {}", bodyType);
            }
            
            // 更新bodyShape字段（详细的身材数据）
            Object bodyShape = analysisResult.get("bodyShape");
            if (bodyShape instanceof Map) {
                // 将bodyShape转换为Map<String, String>格式以匹配User实体的字段类型
                @SuppressWarnings("unchecked")
                Map<String, Object> bodyShapeMap = (Map<String, Object>) bodyShape;
                Map<String, String> bodyShapeStringMap = new HashMap<>();
                
                // 将body shape数据转换为字符串映射
                bodyShapeMap.forEach((key, value) -> {
                    if (value != null) {
                        bodyShapeStringMap.put(key, value.toString());
                    }
                });
                
                if (!bodyShapeStringMap.isEmpty()) {
                    user.setBodyShape(bodyShapeStringMap);
                    updated = true;
                    log.debug("更新用户身材细分数据: {}", bodyShapeStringMap.keySet());
                }
            }
            
            // 更新photoUrl字段（如果分析使用的图片URL可以作为用户的全身照）
            Object imageUrl = analysisResult.get("imageUrl");
            if (imageUrl != null && !imageUrl.toString().trim().isEmpty()) {
                // 只有当用户当前没有全身照或者明确要更新时才设置
                if (user.getPhotoUrl() == null || user.getPhotoUrl().trim().isEmpty()) {
                    user.setPhotoUrl(imageUrl.toString());
                    updated = true;
                    log.debug("更新用户全身照URL: {}", imageUrl);
                }
            }
            
            // 如果有任何更新，保存用户信息
            if (updated) {
                user.setUpdateTime(new Date());
                user = save(user);
                log.info("用户身材信息更新成功: {}", userId);
            } else {
                log.info("未检测到需要更新的身材信息: {}", userId);
            }
            
        } catch (Exception e) {
            log.error("更新用户身材信息失败: {}", userId, e);
            // 不抛出异常，避免影响主流程
        }
        
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("updated", updated);
        result.put("message", updated ? "用户身材信息已更新" : "无需更新用户信息");
        if (updated) {
            result.put("userInfo", toUserResponse(user));
        }
        
        return result;
    }
    
    @Override
    public BodyShapeAnalysisResponse analyzeAndUpdateBodyShape(String userId, String fileId) {
        log.info("开始身材分析业务流程，用户: {}, 文件ID: {}", userId, fileId);
        
        if (aiServiceClient == null) {
            log.warn("AI服务客户端未配置");
            throw new BusinessException("6001", "AI服务暂时不可用");
        }
        
        try {
            // 1. 调用AI服务进行身材识别
            Map<String, Object> aiResponse = aiServiceClient.analyzeBodyShapeByFileId(fileId, userId);
            log.debug("AI服务响应: {}", aiResponse);
            
            // 2. 解析AI服务响应
            Object codeObj = aiResponse.get("code");
            String code = codeObj != null ? codeObj.toString() : "unknown";
            
            if (!"200".equals(code)) {
                // AI分析失败
                String message = (String) aiResponse.get("message");
                log.warn("AI服务返回错误，用户: {}, code: {}, message: {}", userId, code, message);
                throw new BusinessException(code, message != null ? message : "身材识别失败");
            }
            
            // 3. AI分析成功，提取分析结果
            Object data = aiResponse.get("data");
            if (!(data instanceof Map)) {
                log.warn("AI服务返回的data字段格式异常: {}", data);
                throw new BusinessException("6002", "AI服务返回数据格式异常");
            }
            
            @SuppressWarnings("unchecked")
            Map<String, Object> analysisData = (Map<String, Object>) data;
            
            // 4. 转换为响应实体
            BodyShapeAnalysisResponse response = convertToBodyShapeResponse(analysisData);
            
            // 5. 更新用户身材信息到user_info表
            Map<String, Object> updateResult = updateBodyShapeFromAnalysis(userId, analysisData);
            response.setUserInfoUpdated((Boolean) updateResult.get("updated"));
            response.setUpdateMessage((String) updateResult.get("message"));
            
            // 6. 如果用户信息有更新，添加更新后的用户信息
            if (Boolean.TRUE.equals(updateResult.get("updated"))) {
                response.setUpdatedUserInfo((UserResponse) updateResult.get("userInfo"));
            }
            
            log.info("身材分析业务流程完成，用户: {}", userId);
            return response;
            
        } catch (BusinessException e) {
            // 重新抛出业务异常
            throw e;
        } catch (Exception e) {
            log.error("身材分析业务流程失败，用户: {}, 文件ID: {}", userId, fileId, e);
            throw new BusinessException("6001", "身材识别服务暂时不可用: " + e.getMessage());
        }
    }
    
    /**
     * 将AI返回的数据转换为BodyShapeAnalysisResponse
     */
    private BodyShapeAnalysisResponse convertToBodyShapeResponse(Map<String, Object> analysisData) {
        BodyShapeAnalysisResponse.BodyShapeAnalysisResponseBuilder builder = BodyShapeAnalysisResponse.builder();
        
        // 添加调试日志 - 记录原始数据
        log.debug("转换AI服务响应数据: {}", analysisData);
        Object rawIsFullBodyPhoto = analysisData.get("isFullBodyPhoto");
        log.debug("原始isFullBodyPhoto值: {}, 类型: {}", rawIsFullBodyPhoto, 
                rawIsFullBodyPhoto != null ? rawIsFullBodyPhoto.getClass().getSimpleName() : "null");
        
        // 基本字段
        Boolean isFullBodyPhoto = getBooleanValue(analysisData, "isFullBodyPhoto", false);
        log.debug("转换后isFullBodyPhoto值: {}", isFullBodyPhoto);
        builder.isFullBodyPhoto(isFullBodyPhoto);
        
        builder.confidence(getDoubleValue(analysisData, "confidence", 0.0));
        builder.bodyType(getStringValue(analysisData, "bodyType", null));
        builder.reason(getStringValue(analysisData, "reason", null));
        builder.imageUrl(getStringValue(analysisData, "imageUrl", null));
        
        // 身材数据
        Object bodyShape = analysisData.get("bodyShape");
        if (bodyShape instanceof Map) {
            @SuppressWarnings("unchecked")
            Map<String, Object> bodyShapeMap = (Map<String, Object>) bodyShape;
            builder.bodyShape(bodyShapeMap);
        }
        
        // 分析结果
        Object analysis = analysisData.get("analysis");
        if (analysis instanceof List) {
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> analysisList = (List<Map<String, Object>>) analysis;
            List<BodyShapeAnalysisResponse.BodyAnalysis> bodyAnalysisList = new ArrayList<>();
            
            for (Map<String, Object> item : analysisList) {
                BodyShapeAnalysisResponse.BodyAnalysis bodyAnalysis = BodyShapeAnalysisResponse.BodyAnalysis.builder()
                        .feature(getStringValue(item, "feature", ""))
                        .description(getStringValue(item, "description", ""))
                        .type(getStringValue(item, "type", "neutral"))
                        .build();
                bodyAnalysisList.add(bodyAnalysis);
            }
            builder.analysis(bodyAnalysisList);
        }
        
        // 穿搭建议
        Object suggestions = analysisData.get("suggestions");
        if (suggestions instanceof List) {
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> suggestionsList = (List<Map<String, Object>>) suggestions;
            List<BodyShapeAnalysisResponse.StyleSuggestion> styleSuggestionsList = new ArrayList<>();
            
            for (Map<String, Object> item : suggestionsList) {
                BodyShapeAnalysisResponse.StyleSuggestion styleSuggestion = BodyShapeAnalysisResponse.StyleSuggestion.builder()
                        .category(getStringValue(item, "category", ""))
                        .content(getStringValue(item, "content", ""))
                        .priority(getStringValue(item, "priority", "medium"))
                        .build();
                styleSuggestionsList.add(styleSuggestion);
            }
            builder.suggestions(styleSuggestionsList);
        }
        
        return builder.build();
    }
    
    /**
     * 辅助方法：获取字符串值
     */
    private String getStringValue(Map<String, Object> map, String key, String defaultValue) {
        Object value = map.get(key);
        return value != null ? value.toString() : defaultValue;
    }
    
    /**
     * 辅助方法：获取布尔值
     */
    private Boolean getBooleanValue(Map<String, Object> map, String key, Boolean defaultValue) {
        Object value = map.get(key);
        if (value instanceof Boolean) {
            return (Boolean) value;
        } else if (value instanceof String) {
            return "true".equalsIgnoreCase((String) value);
        }
        return defaultValue;
    }
    
    /**
     * 辅助方法：获取双精度值
     */
    private Double getDoubleValue(Map<String, Object> map, String key, Double defaultValue) {
        Object value = map.get(key);
        if (value instanceof Number) {
            return ((Number) value).doubleValue();
        } else if (value instanceof String) {
            try {
                return Double.parseDouble((String) value);
            } catch (NumberFormatException e) {
                log.warn("无法解析浮点数值: {}", value);
            }
        }
        return defaultValue;
    }
    
    @Override
    public boolean saveBaziResult(String userId, BaziResponse.BaziResult baziResult) {
        log.info("保存八字计算结果，用户: {}", userId);
        
        try {
            // 查找用户
            User user = findByUserId(userId)
                    .orElseThrow(() -> new BusinessException("USER_NOT_FOUND", "用户不存在"));
            
            // 获取当前的五行命理信息，如果不存在则创建新的
            User.WuxingProfile wuxingProfile = user.getWuxingProfile();
            if (wuxingProfile == null) {
                wuxingProfile = User.WuxingProfile.builder()
                        .updated(new Date())
                        .build();
            }
            
            // 创建八字详细信息
            User.WuxingProfile.BaziDetail baziDetail = User.WuxingProfile.BaziDetail.builder()
                    .yearPillar(baziResult.getYearPillar())
                    .monthPillar(baziResult.getMonthPillar())
                    .dayPillar(baziResult.getDayPillar())
                    .hourPillar(baziResult.getHourPillar())
                    .dayMaster(baziResult.getDayMaster())
                    .elements(baziResult.getElements())
                    .detailInfo(convertToUserDetailInfo(baziResult.getDetailInfo()))
                    .build();
            
            // 生成完整的八字字符串
            String eightCharString = String.format("%s %s %s %s", 
                    baziResult.getYearPillar(), 
                    baziResult.getMonthPillar(),
                    baziResult.getDayPillar(), 
                    baziResult.getHourPillar());
            
            // 更新八字信息
            wuxingProfile.setBaziDetail(baziDetail);
            wuxingProfile.setEightChar(eightCharString);
            wuxingProfile.setUpdated(new Date());
            
            // 保存到用户信息
            user.setWuxingProfile(wuxingProfile);
            user.setUpdateTime(new Date());
            save(user);
            
            log.info("八字结果保存成功，用户: {}, 八字: {}", userId, eightCharString);
            return true;
            
        } catch (Exception e) {
            log.error("保存八字结果失败，用户: {}", userId, e);
            return false;
        }
    }
    
    @Override
    public TodayEnergyResponse getTodayEnergy(String userId, String date) {
        log.info("获取今日能量信息，用户: {}, 日期: {}", userId, date);
        
        try {
            // 1. 查找用户并验证权限
            User user = findByUserId(userId)
                    .orElseThrow(() -> new BusinessException("2001", "用户不存在"));
            
            // 2. 验证用户模式（必须是能量模式）
            if (!"energy".equals(user.getMode())) {
                throw new BusinessException("3001", "用户未开启能量模式");
            }
            
            // 3. 验证八字信息完整性
            // 首先检查wuxingProfile是否存在
            if (user.getWuxingProfile() == null) {
                if (!user.hasWuxingInfo()) {
                    throw new BusinessException("3002", "八字信息不完整，请先完善个人基础信息（姓名、出生日期、出生时间）");
                } else {
                    throw new BusinessException("3002", "五行命理档案未创建，请先进行八字计算");
                }
            }
            
            // 再检查八字详细信息是否存在
            User.WuxingProfile.BaziDetail baziDetail = user.getWuxingProfile().getBaziDetail();
            if (baziDetail == null) {
                throw new BusinessException("3002", "八字详细信息不完整，请先进行八字计算");
            }
            
            // 最后检查八字详细信息的完整性
            if (baziDetail.getYearPillar() == null || baziDetail.getMonthPillar() == null ||
                baziDetail.getDayPillar() == null || baziDetail.getHourPillar() == null ||
                baziDetail.getDayMaster() == null || baziDetail.getElements() == null) {
                throw new BusinessException("3002", "八字详细信息不完整，请先进行八字计算");
            }
            
            // 4. 处理日期参数（默认今日）
            String targetDate = date;
            if (targetDate == null || targetDate.trim().isEmpty()) {
                targetDate = java.time.LocalDate.now().toString();
            }
            
            // 5. 验证历史查询限制（最多查询过去30天）
            java.time.LocalDate queryDate = java.time.LocalDate.parse(targetDate);
            java.time.LocalDate today = java.time.LocalDate.now();
            java.time.LocalDate thirtyDaysAgo = today.minusDays(30);
            
            if (queryDate.isBefore(thirtyDaysAgo)) {
                throw new BusinessException("1001", "最多只能查询过去30天的能量信息");
            }
            
            if (queryDate.isAfter(today)) {
                throw new BusinessException("1001", "不能查询未来日期的能量信息");
            }
            
            // 6. 优先从数据库查询缓存数据
            Optional<UserDailyEnergy> cachedEnergy = userDailyEnergyService.findByUserIdAndDate(userId, targetDate);
            if (cachedEnergy.isPresent()) {
                log.info("从数据库缓存获取能量信息，用户: {}, 日期: {}", userId, targetDate);
                TodayEnergyResponse response = userDailyEnergyService.convertToResponse(cachedEnergy.get());
                
                // TODO: 处理首次查看奖励（需要检查是否为今日首次查看）
                
                return response;
            }
            
            // 7. 数据库无缓存，调用AI服务计算能量
            log.info("数据库无缓存，调用AI服务计算能量，用户: {}, 日期: {}", userId, targetDate);
            
            if (aiServiceClient == null) {
                throw new BusinessException("3003", "能量计算服务暂时不可用");
            }
            
            Map<String, Object> request = buildEnergyRequest(user, targetDate);
            Map<String, Object> aiResponse = aiServiceClient.calculateTodayEnergy(request);
            
            // 8. 解析AI服务响应
            Object codeObj = aiResponse.get("code");
            int code = codeObj != null ? Integer.parseInt(codeObj.toString()) : 9999;
            
            if (code != 200) {
                String message = (String) aiResponse.get("message");
                log.warn("AI能量计算服务返回错误，用户: {}, code: {}, message: {}", userId, code, message);
                throw new BusinessException("3003", message != null ? message : "能量计算服务异常");
            }
            
            Object data = aiResponse.get("data");
            if (!(data instanceof Map)) {
                throw new BusinessException("3003", "能量计算服务返回数据格式异常");
            }
            
            @SuppressWarnings("unchecked")
            Map<String, Object> energyData = (Map<String, Object>) data;
            
            // 9. 转换为响应对象
            TodayEnergyResponse response = convertToTodayEnergyResponse(energyData);
            
            // 10. 保存到数据库缓存
            try {
                UserDailyEnergy dailyEnergy = userDailyEnergyService.createFromResponse(userId, targetDate, response);
                userDailyEnergyService.saveOrUpdate(dailyEnergy);
                log.info("能量信息已保存到数据库缓存，用户: {}, 日期: {}", userId, targetDate);
            } catch (Exception e) {
                log.warn("保存能量信息到数据库失败，但不影响响应: userId={}, date={}", userId, targetDate, e);
            }
            
            // TODO: 11. 处理首次查看奖励（需要调用运营服务）
            // 这里可以添加灵感值奖励逻辑
            
            log.info("今日能量信息获取成功，用户: {}, 日期: {}", userId, targetDate);
            return response;
            
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取今日能量信息失败，用户: {}, 日期: {}", userId, date, e);
            throw new BusinessException("3003", "能量计算服务异常: " + e.getMessage());
        }
    }
    
    /**
     * 构建能量计算请求
     */
    private Map<String, Object> buildEnergyRequest(User user, String date) {
        Map<String, Object> request = new HashMap<>();

        // 用户基本信息
        request.put("userId", user.getUserId());
        request.put("date", date);

        // 拼接用户基本信息和八字信息
        StringBuilder userInfoBuilder = new StringBuilder();
        userInfoBuilder.append("姓名: ").append(user.getFullName()).append(", ");
        userInfoBuilder.append("出生日期: ").append(user.getBirthDate()).append(", ");
        userInfoBuilder.append("出生时间: ").append(user.getBirthTime()).append(", ");
        userInfoBuilder.append("性别: ").append(user.getGender()).append(", ");
        
        // 八字信息 - 添加安全检查
        if (user.getWuxingProfile() == null || user.getWuxingProfile().getBaziDetail() == null) {
            throw new BusinessException("3002", "八字信息不完整，无法构建能量计算请求");
        }
        
        User.WuxingProfile.BaziDetail baziDetail = user.getWuxingProfile().getBaziDetail();
        userInfoBuilder.append("八字信息: 年柱=").append(baziDetail.getYearPillar())
                   .append(", 月柱=").append(baziDetail.getMonthPillar())
                   .append(", 日柱=").append(baziDetail.getDayPillar())
                   .append(", 时柱=").append(baziDetail.getHourPillar())
                   .append(", 日主=").append(baziDetail.getDayMaster())
                   .append(", 元素=").append(baziDetail.getElements());
        
        // 将拼接后的字符串添加到请求中
        request.put("userInfoString", userInfoBuilder.toString());

        // 其他请求参数保持不变
        Map<String, Object> baziInfo = new HashMap<>();
        baziInfo.put("yearPillar", baziDetail.getYearPillar());
        baziInfo.put("monthPillar", baziDetail.getMonthPillar());
        baziInfo.put("dayPillar", baziDetail.getDayPillar());
        baziInfo.put("hourPillar", baziDetail.getHourPillar());
        baziInfo.put("dayMaster", baziDetail.getDayMaster());
        baziInfo.put("elements", baziDetail.getElements());
        request.put("baziInfo", baziInfo);

        return request;
    }
    
    /**
     * 转换AI响应为TodayEnergyResponse对象
     */
    @SuppressWarnings("unchecked")
    private TodayEnergyResponse convertToTodayEnergyResponse(Map<String, Object> energyData) {
        TodayEnergyResponse.TodayEnergyResponseBuilder builder = TodayEnergyResponse.builder();
        
        // 日期信息
        Object dateInfoObj = energyData.get("dateInfo");
        if (dateInfoObj instanceof Map) {
            Map<String, Object> dateInfoMap = (Map<String, Object>) dateInfoObj;
            TodayEnergyResponse.DateInfo dateInfo = TodayEnergyResponse.DateInfo.builder()
                    .gregorian(getStringValue(dateInfoMap, "gregorian", ""))
                    .lunar(getStringValue(dateInfoMap, "lunar", ""))
                    .build();
            builder.dateInfo(dateInfo);
        }
        
        // 基本能量数据
        builder.totalScore(getIntegerValue(energyData, "totalScore", 0));
        builder.percentage(getIntegerValue(energyData, "percentage", 0));
        builder.peakTime(getStringValue(energyData, "peakTime", ""));
        builder.peakTimeDescription(getStringValue(energyData, "peakTimeDescription", ""));
        builder.description(getStringValue(energyData, "description", ""));
        
        // 五维能量评分
        Object dimensionsObj = energyData.get("dimensions");
        if (dimensionsObj instanceof Map) {
            Map<String, Object> dimensionsMap = (Map<String, Object>) dimensionsObj;
            TodayEnergyResponse.Dimensions dimensions = TodayEnergyResponse.Dimensions.builder()
                    .love(getIntegerValue(dimensionsMap, "love", 0))
                    .career(getIntegerValue(dimensionsMap, "career", 0))
                    .wealth(getIntegerValue(dimensionsMap, "wealth", 0))
                    .health(getIntegerValue(dimensionsMap, "health", 0))
                    .relationship(getIntegerValue(dimensionsMap, "relationship", 0))
                    .build();
            builder.dimensions(dimensions);
        }
        
        // 宜忌指南
        Object adviceObj = energyData.get("advice");
        if (adviceObj instanceof Map) {
            Map<String, Object> adviceMap = (Map<String, Object>) adviceObj;
            builder.advice(convertAdvice(adviceMap));
        }
        
        // 幸运元素
        Object luckyElementsObj = energyData.get("luckyElements");
        if (luckyElementsObj instanceof Map) {
            Map<String, Object> luckyElementsMap = (Map<String, Object>) luckyElementsObj;
            builder.luckyElements(convertLuckyElements(luckyElementsMap));
        }
        
        return builder.build();
    }
    
    /**
     * 转换宜忌指南
     */
    @SuppressWarnings("unchecked")
    private TodayEnergyResponse.Advice convertAdvice(Map<String, Object> adviceMap) {
        TodayEnergyResponse.Advice.AdviceBuilder builder = TodayEnergyResponse.Advice.builder();
        
        // 分类列表
        Object categoriesObj = adviceMap.get("categories");
        if (categoriesObj instanceof List) {
            List<Map<String, Object>> categoriesList = (List<Map<String, Object>>) categoriesObj;
            List<TodayEnergyResponse.Category> categories = new ArrayList<>();
            
            for (Map<String, Object> categoryMap : categoriesList) {
                TodayEnergyResponse.Category category = convertCategory(categoryMap);
                categories.add(category);
            }
            builder.categories(categories);
        }
        
        // 生活建议
        Object lifeSuggestionsObj = adviceMap.get("lifeSuggestions");
        if (lifeSuggestionsObj instanceof List) {
            List<Map<String, Object>> lifeSuggestionsList = (List<Map<String, Object>>) lifeSuggestionsObj;
            List<TodayEnergyResponse.LifeSuggestion> lifeSuggestions = new ArrayList<>();
            
            for (Map<String, Object> suggestionMap : lifeSuggestionsList) {
                TodayEnergyResponse.LifeSuggestion suggestion = TodayEnergyResponse.LifeSuggestion.builder()
                        .icon(getStringValue(suggestionMap, "icon", ""))
                        .content(getStringValue(suggestionMap, "content", ""))
                        .build();
                lifeSuggestions.add(suggestion);
            }
            builder.lifeSuggestions(lifeSuggestions);
        }
        
        return builder.build();
    }
    
    /**
     * 转换宜忌分类
     */
    @SuppressWarnings("unchecked")
    private TodayEnergyResponse.Category convertCategory(Map<String, Object> categoryMap) {
        TodayEnergyResponse.Category.CategoryBuilder builder = TodayEnergyResponse.Category.builder();
        
        builder.type(getStringValue(categoryMap, "type", ""));
        builder.label(getStringValue(categoryMap, "label", ""));
        
        Object itemsObj = categoryMap.get("items");
        if (itemsObj instanceof List) {
            List<Map<String, Object>> itemsList = (List<Map<String, Object>>) itemsObj;
            List<TodayEnergyResponse.Item> items = new ArrayList<>();
            
            for (Map<String, Object> itemMap : itemsList) {
                TodayEnergyResponse.Item item = TodayEnergyResponse.Item.builder()
                        .id(getStringValue(itemMap, "id", ""))
                        .icon(getStringValue(itemMap, "icon", ""))
                        .text(getStringValue(itemMap, "text", ""))
                        .build();
                items.add(item);
            }
            builder.items(items);
        }
        
        return builder.build();
    }
    
    /**
     * 转换幸运元素
     */
    @SuppressWarnings("unchecked")
    private TodayEnergyResponse.LuckyElements convertLuckyElements(Map<String, Object> luckyElementsMap) {
        TodayEnergyResponse.LuckyElements.LuckyElementsBuilder builder = TodayEnergyResponse.LuckyElements.builder();
        
        // 颜色列表
        Object colorsObj = luckyElementsMap.get("colors");
        if (colorsObj instanceof List) {
            List<?> colorsList = (List<?>) colorsObj;
            List<TodayEnergyResponse.ColorItem> colors = new ArrayList<>();
            
            for (Object colorObj : colorsList) {
                if (colorObj instanceof Map) {
                    // AI返回的对象格式
                    Map<String, Object> colorMap = (Map<String, Object>) colorObj;
                    colors.add(TodayEnergyResponse.ColorItem.builder()
                            .value(getStringValue(colorMap, "value", ""))
                            .name(getStringValue(colorMap, "name", ""))
                            .build());
                } else if (colorObj instanceof String) {
                    // 兼容旧的字符串格式
                    String colorStr = (String) colorObj;
                    colors.add(TodayEnergyResponse.ColorItem.builder()
                            .value(colorStr)
                            .name(colorStr)
                            .build());
                }
            }
            builder.colors(colors);
        }
        
        // 服饰建议
        Object clothingObj = luckyElementsMap.get("clothing");
        if (clothingObj instanceof List) {
            builder.clothing((List<String>) clothingObj);
        }
        
        // 配饰建议
        Object accessoriesObj = luckyElementsMap.get("accessories");
        if (accessoriesObj instanceof List) {
            builder.accessories((List<String>) accessoriesObj);
        }
        
        // 妆容建议
        Object makeupObj = luckyElementsMap.get("makeup");
        if (makeupObj instanceof List) {
            builder.makeup((List<String>) makeupObj);
        }
        
        return builder.build();
    }
    
    /**
     * 辅助方法：获取整数值
     */
    private Integer getIntegerValue(Map<String, Object> map, String key, Integer defaultValue) {
        Object value = map.get(key);
        if (value instanceof Number) {
            return ((Number) value).intValue();
        } else if (value instanceof String) {
            try {
                return Integer.parseInt((String) value);
            } catch (NumberFormatException e) {
                log.warn("无法解析整数值: {}", value);
            }
        }
        return defaultValue;
    }
    
    @Override
    public TodayEnergyBriefResponse getTodayEnergyBrief(String userId, String date) {
        log.info("获取今日能量简要信息，用户: {}, 日期: {}", userId, date);
        
        try {
            // 1. 查找用户并验证权限
            User user = findByUserId(userId)
                    .orElseThrow(() -> new BusinessException("2001", "用户不存在"));
            
            // 2. 验证用户模式（必须是能量模式）
            if (!"energy".equals(user.getMode())) {
                throw new BusinessException("3001", "用户未开启能量模式");
            }
            
            // 3. 验证八字信息完整性
            if (user.getWuxingProfile() == null || user.getWuxingProfile().getBaziDetail() == null) {
                throw new BusinessException("3002", "八字信息不完整");
            }
            
            // 4. 处理日期参数（默认今日）
            String targetDate = date;
            if (targetDate == null || targetDate.trim().isEmpty()) {
                targetDate = java.time.LocalDate.now().toString();
            }
            
            // 5. 验证历史查询限制（最多查询过去30天）
            java.time.LocalDate queryDate = java.time.LocalDate.parse(targetDate);
            java.time.LocalDate today = java.time.LocalDate.now();
            java.time.LocalDate thirtyDaysAgo = today.minusDays(30);
            
            if (queryDate.isBefore(thirtyDaysAgo)) {
                throw new BusinessException("1001", "最多只能查询过去30天的能量信息");
            }
            
            if (queryDate.isAfter(today)) {
                throw new BusinessException("1001", "不能查询未来日期的能量信息");
            }
            
            // 6. 优先从数据库查询缓存数据
            Optional<UserDailyEnergy> cachedEnergy = userDailyEnergyService.findByUserIdAndDate(userId, targetDate);
            if (cachedEnergy.isPresent()) {
                log.info("从数据库缓存获取能量简要信息，用户: {}, 日期: {}", userId, targetDate);
                return userDailyEnergyService.convertToBriefResponse(cachedEnergy.get());
            }
            
            // 7. 数据库无缓存，先调用完整的能量计算接口获取数据
            log.info("数据库无缓存，先获取完整能量信息，用户: {}, 日期: {}", userId, targetDate);
            TodayEnergyResponse fullResponse = getTodayEnergy(userId, targetDate);
            
            // 8. 重新查询数据库（getTodayEnergy应该已经保存了数据）
            Optional<UserDailyEnergy> newCachedEnergy = userDailyEnergyService.findByUserIdAndDate(userId, targetDate);
            if (newCachedEnergy.isPresent()) {
                return userDailyEnergyService.convertToBriefResponse(newCachedEnergy.get());
            }
            
            // 9. 如果数据库仍然没有数据，直接从完整响应转换（备用方案）
            log.warn("数据库仍无缓存数据，直接转换完整响应，用户: {}, 日期: {}", userId, targetDate);
            return convertFullResponseToBrief(fullResponse);
            
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取今日能量简要信息失败，用户: {}, 日期: {}", userId, date, e);
            throw new BusinessException("3003", "能量计算服务异常: " + e.getMessage());
        }
    }
    
    /**
     * 将完整的TodayEnergyResponse转换为TodayEnergyBriefResponse（备用方案）
     */
    private TodayEnergyBriefResponse convertFullResponseToBrief(TodayEnergyResponse fullResponse) {
        TodayEnergyBriefResponse.DateInfo dateInfo = null;
        if (fullResponse.getDateInfo() != null) {
            dateInfo = TodayEnergyBriefResponse.DateInfo.builder()
                    .gregorian(fullResponse.getDateInfo().getGregorian())
                    .lunar(fullResponse.getDateInfo().getLunar())
                    .build();
        }
        
        TodayEnergyBriefResponse.LuckyElements luckyElements = null;
        if (fullResponse.getLuckyElements() != null) {
            luckyElements = TodayEnergyBriefResponse.LuckyElements.builder()
                    .clothingSummary(generateSummaryFromList(fullResponse.getLuckyElements().getClothing(), "今日服饰搭配以舒适自然为主。"))
                    .accessoriesSummary(generateSummaryFromList(fullResponse.getLuckyElements().getAccessories(), "简约配饰最为适宜。"))
                    .makeupSummary(generateSummaryFromList(fullResponse.getLuckyElements().getMakeup(), "自然清透妆容为佳。"))
                    .build();
        }
        
        return TodayEnergyBriefResponse.builder()
                .dateInfo(dateInfo)
                .totalScore(fullResponse.getTotalScore())
                .percentage(fullResponse.getPercentage())
                .luckyElements(luckyElements)
                .build();
    }
    
    /**
     * 从列表生成简要总结
     */
    private String generateSummaryFromList(List<String> list, String defaultSummary) {
        if (list == null || list.isEmpty()) {
            return defaultSummary;
        }
        
        if (list.size() == 1) {
            return list.get(0);
        }
        
        // 组合前两条建议
        String firstSuggestion = list.get(0);
        String secondSuggestion = list.get(1);
        return firstSuggestion + " " + secondSuggestion;
    }
    
    /**
     * 异步生成八字信息
     */
    @Override
    public void asyncGenerateBazi(String userId, String fullName, String birthDate, String birthTime, String birthPlace, Integer gender) {
        long startTime = System.currentTimeMillis();
        log.info("开始异步生成八字信息，用户: {}, 姓名: {}", userId, fullName);
        
        try {
            // 从 ApplicationContext 中获取 WuxingService，避免循环依赖
            WuxingService wuxingService = null;
            try {
                wuxingService = applicationContext.getBean(WuxingService.class);
                log.info("WuxingService 获取完成，耗时: {}ms，用户: {}", 
                        System.currentTimeMillis() - startTime, userId);
            } catch (Exception e) {
                log.warn("WuxingService 不可用，跳过八字生成，用户: {}, 错误: {}", userId, e.getMessage());
                return;
            }
            
            // 构建八字计算请求
            com.stylishlink.user.dto.request.WuxingRequest wuxingRequest = new com.stylishlink.user.dto.request.WuxingRequest();
            wuxingRequest.setFullName(fullName);
            wuxingRequest.setBirthDate(birthDate);
            wuxingRequest.setBirthTime(birthTime);
            wuxingRequest.setBirthPlace(birthPlace);
            wuxingRequest.setGender(gender);
            
            log.info("开始调用八字计算服务，耗时: {}ms，用户: {}", 
                    System.currentTimeMillis() - startTime, userId);
            
            // 调用八字计算
            BaziResponse baziResponse = wuxingService.calculateBaZi(userId, wuxingRequest);
            
            log.info("八字计算服务调用完成，耗时: {}ms，用户: {}", 
                    System.currentTimeMillis() - startTime, userId);
            
            if (baziResponse != null && baziResponse.getBaziResult() != null) {
                // 保存八字结果
                boolean saveSuccess = saveBaziResult(userId, baziResponse.getBaziResult());
                if (saveSuccess) {
                    log.info("异步八字生成完成，总耗时: {}ms，用户: {}", 
                            System.currentTimeMillis() - startTime, userId);
                } else {
                    log.error("八字结果保存失败，用户: {}", userId);
                }
            } else {
                log.error("八字计算返回空结果，用户: {}", userId);
            }
            
        } catch (Exception e) {
            log.error("异步八字生成失败，耗时: {}ms，用户: {}, 错误: {}", 
                    System.currentTimeMillis() - startTime, userId, e.getMessage(), e);
            // 异步方法中的异常不会抛出，只记录日志
        }
    }
    
    /**
     * 将BaziResponse.DetailInfo转换为User.WuxingProfile.DetailInfo
     */
    private User.WuxingProfile.DetailInfo convertToUserDetailInfo(BaziResponse.DetailInfo responseDetailInfo) {
        if (responseDetailInfo == null) {
            return null;
        }
        
        return User.WuxingProfile.DetailInfo.builder()
                .yearPillarDetail(convertToUserPillarDetail(responseDetailInfo.getYearPillarDetail()))
                .monthPillarDetail(convertToUserPillarDetail(responseDetailInfo.getMonthPillarDetail()))
                .dayPillarDetail(convertToUserPillarDetail(responseDetailInfo.getDayPillarDetail()))
                .hourPillarDetail(convertToUserPillarDetail(responseDetailInfo.getHourPillarDetail()))
                .wuxingStatistics(convertToUserWuxingStatistics(responseDetailInfo.getWuxingStatistics()))
                .build();
    }
    
    /**
     * 将BaziResponse.PillarDetail转换为User.WuxingProfile.PillarDetail
     */
    private User.WuxingProfile.PillarDetail convertToUserPillarDetail(BaziResponse.PillarDetail responsePillarDetail) {
        if (responsePillarDetail == null) {
            return null;
        }
        
        return User.WuxingProfile.PillarDetail.builder()
                .heavenlyStem(convertToUserHeavenlyStemInfo(responsePillarDetail.getHeavenlyStem()))
                .earthlyBranch(convertToUserEarthlyBranchInfo(responsePillarDetail.getEarthlyBranch()))
                .build();
    }
    
    /**
     * 将BaziResponse.HeavenlyStemInfo转换为User.WuxingProfile.HeavenlyStemInfo
     */
    private User.WuxingProfile.HeavenlyStemInfo convertToUserHeavenlyStemInfo(BaziResponse.HeavenlyStemInfo responseHeavenlyStem) {
        if (responseHeavenlyStem == null) {
            return null;
        }
        
        return User.WuxingProfile.HeavenlyStemInfo.builder()
                .character(responseHeavenlyStem.getCharacter())
                .element(responseHeavenlyStem.getElement())
                .build();
    }
    
    /**
     * 将BaziResponse.EarthlyBranchInfo转换为User.WuxingProfile.EarthlyBranchInfo
     */
    private User.WuxingProfile.EarthlyBranchInfo convertToUserEarthlyBranchInfo(BaziResponse.EarthlyBranchInfo responseEarthlyBranch) {
        if (responseEarthlyBranch == null) {
            return null;
        }
        
        List<User.WuxingProfile.HiddenStemInfo> hiddenStems = null;
        if (responseEarthlyBranch.getHiddenStems() != null) {
            hiddenStems = responseEarthlyBranch.getHiddenStems().stream()
                    .map(this::convertToUserHiddenStemInfo)
                    .collect(java.util.stream.Collectors.toList());
        }
        
        return User.WuxingProfile.EarthlyBranchInfo.builder()
                .character(responseEarthlyBranch.getCharacter())
                .element(responseEarthlyBranch.getElement())
                .hiddenStems(hiddenStems)
                .build();
    }
    
    /**
     * 将BaziResponse.HiddenStemInfo转换为User.WuxingProfile.HiddenStemInfo
     */
    private User.WuxingProfile.HiddenStemInfo convertToUserHiddenStemInfo(BaziResponse.HiddenStemInfo responseHiddenStem) {
        if (responseHiddenStem == null) {
            return null;
        }
        
        return User.WuxingProfile.HiddenStemInfo.builder()
                .character(responseHiddenStem.getCharacter())
                .element(responseHiddenStem.getElement())
                .strength(responseHiddenStem.getStrength())
                .build();
    }
    
    /**
     * 将BaziResponse.WuxingStatistics转换为User.WuxingProfile.WuxingStatistics
     */
    private User.WuxingProfile.WuxingStatistics convertToUserWuxingStatistics(BaziResponse.WuxingStatistics responseWuxingStats) {
        if (responseWuxingStats == null) {
            return null;
        }
        
        return User.WuxingProfile.WuxingStatistics.builder()
                .distribution(responseWuxingStats.getDistribution())
                .dominantElement(responseWuxingStats.getDominantElement())
                .weakestElement(responseWuxingStats.getWeakestElement())
                .totalCount(responseWuxingStats.getTotalCount())
                .build();
    }
    
    /**
     * 更新用户居住地区信息
     */
    @Override
    public void updateUserResidence(String userId, String residenceName, String residenceCode) {
        log.info("更新用户居住地区信息: userId={}, residenceName={}, residenceCode={}", 
                userId, residenceName, residenceCode);
        
        try {
            User user = findByUserId(userId)
                    .orElseThrow(() -> new BusinessException("USER_NOT_FOUND", "用户不存在"));
            
            // 更新居住地区信息
            user.setResidenceName(residenceName);
            user.setResidenceCode(residenceCode);
            user.setUpdateTime(new Date());
            
            save(user);
            
            log.info("用户居住地区信息更新成功: userId={}, residenceName={}, residenceCode={}", 
                    userId, residenceName, residenceCode);
                    
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("更新用户居住地区信息失败: userId={}", userId, e);
            throw new BusinessException("RESIDENCE_UPDATE_FAILED", "居住地区信息更新失败: " + e.getMessage());
        }
    }
} 