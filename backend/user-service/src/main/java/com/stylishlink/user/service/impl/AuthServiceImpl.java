package com.stylishlink.user.service.impl;

import com.stylishlink.user.entity.User;
import com.stylishlink.user.entity.UserAuth;
import com.stylishlink.common.exception.BusinessException;
import com.stylishlink.user.dto.request.RefreshTokenRequest;
import com.stylishlink.user.dto.request.VerifyCodeLoginRequest;
import com.stylishlink.user.dto.response.LoginResponse;
import com.stylishlink.user.dto.response.RefreshTokenResponse;
import com.stylishlink.user.dto.response.SendCodeResponse;
import com.stylishlink.user.dto.response.UserResponse;
import com.stylishlink.user.service.AuthService;
import com.stylishlink.user.service.JwtService;
import com.stylishlink.user.service.UserService;
import com.stylishlink.user.service.VerificationCodeService;
import com.stylishlink.user.service.UserAuthService;
import com.stylishlink.user.config.JwtConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.Optional;
import java.util.UUID;

/**
 * 认证服务实现
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AuthServiceImpl implements AuthService {
    
    private final UserService userService;
    private final JwtService jwtService;
    private final VerificationCodeService verificationCodeService;
    private final UserAuthService userAuthService;
    private final JwtConfig jwtConfig;
    
    @Override
    public SendCodeResponse sendVerificationCode(String phone) {
        log.info("发送验证码: {}", phone);
        return verificationCodeService.sendCode(phone);
    }
    
    @Override
    @Transactional
    public LoginResponse loginByVerificationCode(VerifyCodeLoginRequest request) {
        log.info("验证码登录: {}", request.getPhone());
        
        // 验证验证码
        boolean isValidCode = verificationCodeService.verifyCode(
                request.getPhone(), request.getCode(), request.getCodeId());
        
        if (!isValidCode) {
            throw new BusinessException("2005", "验证码错误");
        }
        
        // 查找或创建用户
        Optional<User> userOpt = userService.findByPhone(request.getPhone());
        User user;
        boolean isNewUser = false;
        
        if (userOpt.isPresent()) {
            // 用户已存在，更新最后登录时间
            user = userOpt.get();
            user.setLastLoginDate(new Date());
            user = userService.save(user);
            log.info("用户登录: {}", user.getUserId());
        } else {
            // 用户不存在，自动注册
            isNewUser = true;
            user = createNewUser(request.getPhone());
            log.info("新用户自动注册: {}", user.getUserId());
        }
        
        // 生成JWT Token
        String token = jwtService.generateToken(user.getUserId());
        
        // 计算token过期时间（秒）
        int expiresIn = (int) (jwtConfig.getTokenValidityInMilliseconds() / 1000);
        
        // 计算过期时间Date对象
        Date expiresAt = new Date(System.currentTimeMillis() + jwtConfig.getTokenValidityInMilliseconds());
        
        // 保存认证信息到user_auth表（不保存refresh_token）
        userAuthService.saveAuthInfo(user.getUserId(), token, expiresAt);
        log.info("用户认证信息已保存: userId={}", user.getUserId());
        
        // 转换用户信息
        UserResponse userResponse = userService.toUserResponse(user);
        
        // 构建登录响应（不返回refreshToken）
        return LoginResponse.builder()
                .userId(user.getUserId())
                .userInfo(userResponse)
                .token(token)
                .expiresIn(expiresIn)
                .hasBasicInfo(user.hasBasicInfo())
                .hasWuxingInfo(user.hasWuxingInfo())
                .isNewUser(isNewUser)
                .build();
    }
    
    /**
     * 创建新用户
     */
    private User createNewUser(String phone) {
        Date now = new Date();
        String userId = "u" + UUID.randomUUID().toString().replace("-", "").substring(0, 8);
        
        // 确保userId唯一
        while (userService.existsByUserId(userId)) {
            userId = "u" + UUID.randomUUID().toString().replace("-", "").substring(0, 8);
        }
        
        User user = User.builder()
                .userId(userId)
                .phone(phone)
                .nickname("用户_" + phone.substring(phone.length() - 6)) // 用户_手机号后6位
                .gender(0) // 未知
                .mode("natural") // 默认自然模式
                .registerDate(now)
                .lastLoginDate(now)
                .createTime(now)
                .updateTime(now)
                .build();
        
        return userService.save(user);
    }
    
    @Override
    @Transactional
    public RefreshTokenResponse refreshToken(RefreshTokenRequest request) {
        log.info("刷新令牌");
        
        String refreshToken = request.getRefreshToken();
        
        // 验证refresh token的有效性（通过JWT解析）
        if (!jwtService.validateToken(refreshToken)) {
            log.warn("refresh token验证失败");
            throw new BusinessException("1002", "refresh token验证失败");
        }
        
        // 从refresh token中解析userId
        String userId;
        try {
            userId = jwtService.extractUserId(refreshToken);
            log.info("从refresh token解析用户ID: userId={}", userId);
        } catch (Exception e) {
            log.warn("refresh token解析失败", e);
            throw new BusinessException("1002", "refresh token无效");
        }
        
        // 查询用户当前的认证信息
        Optional<UserAuth> userAuthOpt = userAuthService.findByUserId(userId);
        if (userAuthOpt.isEmpty()) {
            log.warn("用户认证信息不存在: userId={}", userId);
            throw new BusinessException("1002", "用户认证信息不存在，请重新登录");
        }
        
        UserAuth userAuth = userAuthOpt.get();
        String originalToken = userAuth.getToken();
        
        // 检查当前token是否过期
        if (userAuth.getExpiresAt().before(new Date())) {
            log.warn("当前token已过期: userId={}", userId);
            // 删除过期的认证记录
            userAuthService.removeByUserId(userId);
            throw new BusinessException("1002", "token已过期，请重新登录");
        }
        
        // 生成新的access token
        String newToken = jwtService.generateToken(userId);
        
        // 计算token过期时间（秒）
        int expiresIn = (int) (jwtConfig.getTokenValidityInMilliseconds() / 1000);
        
        // 计算过期时间Date对象
        Date newExpiresAt = new Date(System.currentTimeMillis() + jwtConfig.getTokenValidityInMilliseconds());
        
        // 更新认证信息（只保存新token）
        userAuthService.updateAuthInfo(userId, newToken, newExpiresAt);
        log.info("用户认证信息已更新: userId={}", userId);
        
        // 构建刷新响应：token=原token，refreshToken=新token
        return RefreshTokenResponse.builder()
                .token(originalToken)        // 返回原来的token
                .expiresIn(expiresIn)
                .refreshToken(newToken)      // 返回新生成的token作为refreshToken
                .build();
    }
    
    @Override
    public void logout(String token) {
        // TODO: 实现退出登录逻辑（加入黑名单等）
        log.info("用户退出登录");
    }
} 