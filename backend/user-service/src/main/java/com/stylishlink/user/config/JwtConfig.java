package com.stylishlink.user.config;

import io.jsonwebtoken.JwtParser;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.io.Decoders;
import io.jsonwebtoken.security.Keys;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.crypto.SecretKey;

/**
 * JWT配置
 */
@Configuration
public class JwtConfig {
    
    @Value("${security.jwt.secret-key}")
    private String secretKey;
    
    @Value("${security.jwt.token-validity-in-seconds}")
    private long tokenValidityInSeconds;
    
    @Value("${security.jwt.token-validity-in-seconds-for-remember-me}")
    private long tokenValidityInSecondsForRememberMe;
    
    @Bean
    public SecretKey secretKey() {
        return Keys.hmacShaKeyFor(Decoders.BASE64.decode(secretKey));
    }
    
    @Bean
    public JwtParser jwtParser() {
        return Jwts.parser()
                .verifyWith(secretKey())
                .build();
    }
    
    public long getTokenValidityInMilliseconds() {
        return tokenValidityInSeconds * 1000;
    }
    
    public long getTokenValidityInMillisecondsForRememberMe() {
        return tokenValidityInSecondsForRememberMe * 1000;
    }
} 