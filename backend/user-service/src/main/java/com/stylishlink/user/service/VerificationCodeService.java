package com.stylishlink.user.service;

import com.stylishlink.user.dto.response.SendCodeResponse;

/**
 * 验证码服务接口
 */
public interface VerificationCodeService {
    
    /**
     * 发送验证码
     * @param phone 手机号
     * @return 发送结果
     */
    SendCodeResponse sendCode(String phone);
    
    /**
     * 验证验证码
     * @param phone 手机号
     * @param code 验证码
     * @param codeId 验证码标识
     * @return 验证是否成功
     */
    boolean verifyCode(String phone, String code, String codeId);
} 