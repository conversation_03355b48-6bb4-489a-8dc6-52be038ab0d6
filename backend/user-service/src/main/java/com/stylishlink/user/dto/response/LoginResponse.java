package com.stylishlink.user.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 登录响应
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LoginResponse {
    
    private String userId;          // 用户ID
    private UserResponse userInfo;  // 用户基础信息
    private String token;           // JWT访问令牌
    private Integer expiresIn;      // token过期时间（秒）
    private Boolean hasBasicInfo;   // 是否已完善基础信息
    private Boolean hasWuxingInfo;  // 是否已完善八字信息
    private Boolean isNewUser;      // 是否为新注册用户
} 