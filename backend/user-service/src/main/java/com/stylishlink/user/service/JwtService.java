package com.stylishlink.user.service;

import io.jsonwebtoken.Claims;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.Map;
import java.util.function.Function;

/**
 * JWT服务接口
 */
public interface JwtService {
    
    /**
     * 生成令牌
     * @param userId 用户ID
     * @return 令牌
     */
    String generateToken(String userId);
    
    /**
     * 生成令牌
     * @param userId 用户ID
     * @param claims 额外声明
     * @return 令牌
     */
    String generateToken(String userId, Map<String, Object> claims);
    
    /**
     * 生成记住我令牌
     * @param userId 用户ID
     * @return 令牌
     */
    String generateRememberMeToken(String userId);
    
    /**
     * 生成记住我令牌
     * @param userId 用户ID
     * @param claims 额外声明
     * @return 令牌
     */
    String generateRememberMeToken(String userId, Map<String, Object> claims);
    
    /**
     * 从令牌中获取用户ID
     * @param token 令牌
     * @return 用户ID
     */
    String extractUserId(String token);
    
    /**
     * 从令牌中获取过期时间
     * @param token 令牌
     * @return 过期时间
     */
    Date extractExpiration(String token);
    
    /**
     * 从令牌中获取声明
     * @param token 令牌
     * @return 声明
     */
    Claims extractAllClaims(String token);
    
    /**
     * 从令牌中获取指定声明
     * @param token 令牌
     * @param claimsResolver 声明解析器
     * @param <T> 声明类型
     * @return 声明值
     */
    <T> T extractClaim(String token, Function<Claims, T> claimsResolver);
    
    /**
     * 验证令牌
     * @param token 令牌
     * @return 是否有效
     */
    boolean validateToken(String token);
    
    /**
     * 验证令牌是否属于指定用户
     * @param token 令牌
     * @param userId 用户ID
     * @return 是否属于指定用户
     */
    boolean validateToken(String token, String userId);
    
    /**
     * 令牌是否过期
     * @param token 令牌
     * @return 是否过期
     */
    boolean isTokenExpired(String token);
} 