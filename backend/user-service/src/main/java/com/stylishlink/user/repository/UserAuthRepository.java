package com.stylishlink.user.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.stylishlink.user.entity.UserAuth;
import org.apache.ibatis.annotations.Mapper;

import java.util.Optional;

/**
 * 用户认证仓库接口
 */
@Mapper
public interface UserAuthRepository extends BaseMapper<UserAuth> {
    
    /**
     * 根据token查询认证信息
     * @param token 认证令牌
     * @return 认证信息
     */
    default Optional<UserAuth> findByToken(String token) {
        UserAuth userAuth = selectOne(new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<UserAuth>()
                .eq("token", token));
        return Optional.ofNullable(userAuth);
    }
    
    /**
     * 根据refreshToken查询认证信息
     * @param refreshToken 刷新令牌
     * @return 认证信息
     */
    default Optional<UserAuth> findByRefreshToken(String refreshToken) {
        UserAuth userAuth = selectOne(new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<UserAuth>()
                .eq("refresh_token", refreshToken));
        return Optional.ofNullable(userAuth);
    }
    
    /**
     * 根据userId查询认证信息
     * @param userId 用户ID
     * @return 认证信息
     */
    default Optional<UserAuth> findByUserId(String userId) {
        UserAuth userAuth = selectOne(new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<UserAuth>()
                .eq("user_id", userId));
        return Optional.ofNullable(userAuth);
    }
    
    /**
     * 根据userId删除认证信息
     * @param userId 用户ID
     * @return 删除的记录数
     */
    default int deleteByUserId(String userId) {
        return delete(new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<UserAuth>()
                .eq("user_id", userId));
    }
    
    /**
     * 根据token删除认证信息
     * @param token 认证令牌
     * @return 删除的记录数
     */
    default int deleteByToken(String token) {
        return delete(new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<UserAuth>()
                .eq("token", token));
    }
} 