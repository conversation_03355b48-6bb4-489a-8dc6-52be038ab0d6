package com.stylishlink.user.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.stylishlink.user.entity.User;
import org.apache.ibatis.annotations.Mapper;

import java.util.Optional;

/**
 * 用户仓库接口
 */
@Mapper
public interface UserRepository extends BaseMapper<User> {
    
    /**
     * 根据用户ID查询用户
     * @param userId 用户ID
     * @return 用户信息
     */
    default Optional<User> findByUserId(String userId) {
        User user = selectOne(new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<User>()
                .eq("user_id", userId));
        return Optional.ofNullable(user);
    }
    
    /**
     * 根据OpenID查询用户
     * @param openId 微信OpenID
     * @return 用户信息
     */
    default Optional<User> findByOpenId(String openId) {
        User user = selectOne(new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<User>()
                .eq("open_id", openId));
        return Optional.ofNullable(user);
    }
    
    /**
     * 根据手机号查询用户
     * @param phone 手机号
     * @return 用户信息
     */
    default Optional<User> findByPhone(String phone) {
        User user = selectOne(new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<User>()
                .eq("phone", phone));
        return Optional.ofNullable(user);
    }
    
    /**
     * 根据邮箱查询用户
     * @param email 邮箱
     * @return 用户信息
     */
    default Optional<User> findByEmail(String email) {
        User user = selectOne(new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<User>()
                .eq("email", email));
        return Optional.ofNullable(user);
    }
    
    /**
     * 检查用户ID是否存在
     * @param userId 用户ID
     * @return 是否存在
     */
    default boolean existsByUserId(String userId) {
        return selectCount(new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<User>()
                .eq("user_id", userId)) > 0;
    }
    
    /**
     * 检查OpenID是否存在
     * @param openId 微信OpenID
     * @return 是否存在
     */
    default boolean existsByOpenId(String openId) {
        return selectCount(new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<User>()
                .eq("open_id", openId)) > 0;
    }
    
    /**
     * 检查手机号是否存在
     * @param phone 手机号
     * @return 是否存在
     */
    default boolean existsByPhone(String phone) {
        return selectCount(new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<User>()
                .eq("phone", phone)) > 0;
    }
    
    /**
     * 检查邮箱是否存在
     * @param email 邮箱
     * @return 是否存在
     */
    default boolean existsByEmail(String email) {
        return selectCount(new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<User>()
                .eq("email", email)) > 0;
    }
} 