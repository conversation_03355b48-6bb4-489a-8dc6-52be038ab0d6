package com.stylishlink.user.controller;

import com.stylishlink.user.dto.response.ApiResponse;
import com.stylishlink.user.dto.response.UserResponse;
import com.stylishlink.user.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

/**
 * 用户控制器
 */
@RestController
@RequestMapping("/users")
@RequiredArgsConstructor
@Slf4j
public class UserController {
    
    private final UserService userService;
    
    /**
     * 获取用户信息
     * @param userId 用户ID
     * @return 用户信息
     */
    @GetMapping("/{userId}")
    public ApiResponse<UserResponse> getUserInfo(@PathVariable String userId) {
        log.info("获取用户信息: {}", userId);
        UserResponse response = userService.findUserResponseByUserId(userId);
        return ApiResponse.success(response);
    }
    
    /**
     * 获取当前用户信息
     * @return 用户信息
     */
    @GetMapping("/profile")
    public ApiResponse<UserResponse> getCurrentUserInfo() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String userId = authentication.getName();
        log.info("获取当前用户信息: {}", userId);
        UserResponse response = userService.findUserResponseByUserId(userId);
        return ApiResponse.success(response);
    }
    
    /**
     * 获取当前用户信息（别名）
     * @return 用户信息
     */
    @GetMapping("/me")
    public ApiResponse<UserResponse> getCurrentUserInfoAlias() {
        return getCurrentUserInfo();
    }
    
    /**
     * 检查用户名是否存在
     * @param username 用户名
     * @return 是否存在
     */
    @GetMapping("/check/username")
    public ApiResponse<Boolean> checkUsernameExists(@RequestParam String username) {
        log.info("检查用户名是否存在: {}", username);
        boolean exists = userService.existsByUserId(username);
        return ApiResponse.success(exists);
    }
    
    /**
     * 检查手机号是否存在
     * @param phone 手机号
     * @return 是否存在
     */
    @GetMapping("/check/phone")
    public ApiResponse<Boolean> checkPhoneExists(@RequestParam String phone) {
        log.info("检查手机号是否存在: {}", phone);
        boolean exists = userService.existsByPhone(phone);
        return ApiResponse.success(exists);
    }
    
    /**
     * 检查邮箱是否存在
     * @param email 邮箱
     * @return 是否存在
     */
    @GetMapping("/check/email")
    public ApiResponse<Boolean> checkEmailExists(@RequestParam String email) {
        log.info("检查邮箱是否存在: {}", email);
        boolean exists = userService.existsByEmail(email);
        return ApiResponse.success(exists);
    }
} 