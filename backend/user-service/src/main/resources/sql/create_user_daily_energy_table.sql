-- 创建用户每日能量表
CREATE TABLE IF NOT EXISTS `user_daily_energy` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` varchar(64) NOT NULL COMMENT '用户ID',
  `energy_date` varchar(10) NOT NULL COMMENT '能量日期(YYYY-MM-DD)',
  `total_score` int DEFAULT NULL COMMENT '今日总能量分数(0-100)',
  `percentage` int DEFAULT NULL COMMENT '超过的用户百分比',
  `peak_time` varchar(64) DEFAULT NULL COMMENT '能量高峰时段',
  `peak_time_description` text DEFAULT NULL COMMENT '高峰时段描述',
  `description` text DEFAULT NULL COMMENT '今日能量总体描述',
  `date_info` json DEFAULT NULL COMMENT '日期信息(JSON格式)',
  `dimensions` json DEFAULT NULL COMMENT '五维能量评分(JSON格式)',
  `advice` json DEFAULT NULL COMMENT '宜忌指南(JSON格式)',
  `lucky_elements` json DEFAULT NULL COMMENT '幸运元素(JSON格式)',
  `lucky_elements_summary` json DEFAULT NULL COMMENT '幸运元素简化总结(JSON格式)',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_date` (`user_id`, `energy_date`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_energy_date` (`energy_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户每日能量信息表'; 