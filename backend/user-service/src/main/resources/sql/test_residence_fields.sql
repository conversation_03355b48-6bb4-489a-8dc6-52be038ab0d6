-- 测试居住地点字段功能
-- 执行时间: 2025-06-05

-- 1. 插入一个测试用户并设置居住地点信息
INSERT INTO `user_info` (
    `user_id`, 
    `nickname`, 
    `phone`, 
    `residence_name`, 
    `residence_code`,
    `create_time`,
    `update_time`
) VALUES (
    'test_residence_001',
    '测试用户居住地',
    '13800000001',
    '上海市浦东新区',
    '310115',
    NOW(),
    NOW()
);

-- 2. 更新现有用户的居住地点信息
UPDATE `user_info` 
SET 
    `residence_name` = '北京市朝阳区',
    `residence_code` = '110105',
    `update_time` = NOW()
WHERE `user_id` = 'test_residence_001';

-- 3. 查询验证居住地点字段
SELECT 
    `user_id`,
    `nickname`,
    `residence_name`,
    `residence_code`,
    `create_time`,
    `update_time`
FROM `user_info` 
WHERE `user_id` = 'test_residence_001';

-- 4. 清理测试数据
-- DELETE FROM `user_info` WHERE `user_id` = 'test_residence_001'; 