-- 为user_daily_energy表添加幸运元素简化总结字段
-- 执行日期: 2025-01-24
-- 说明: 添加AI生成的幸运元素简化总结字段，用于优化今日能量简要信息接口性能

-- 检查字段是否已存在，如果不存在则添加
SET @sql = '';
SELECT 
    CASE 
        WHEN COUNT(*) = 0 THEN 
            'ALTER TABLE user_daily_energy ADD COLUMN lucky_elements_summary JSON DEFAULT NULL COMMENT ''幸运元素简化总结(JSON格式)'' AFTER lucky_elements;'
        ELSE 
            'SELECT ''字段 lucky_elements_summary 已存在'' AS message;'
    END INTO @sql
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'user_daily_energy' 
  AND COLUMN_NAME = 'lucky_elements_summary';

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt; 