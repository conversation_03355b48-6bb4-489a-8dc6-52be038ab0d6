-- 删除现有表
DROP TABLE IF EXISTS `user_info`;

-- 用户表 - 完整结构
CREATE TABLE `user_info` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` varchar(64) NOT NULL COMMENT '用户ID(用户名)',
  `open_id` varchar(64) DEFAULT NULL COMMENT '微信OpenID',
  `union_id` varchar(64) DEFAULT NULL COMMENT '微信UnionID',
  `nickname` varchar(64) DEFAULT NULL COMMENT '昵称',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像URL',
  `gender` tinyint DEFAULT '0' COMMENT '性别(0-未知,1-男,2-女)',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `email` varchar(64) DEFAULT NULL COMMENT '邮箱',
  `password_hash` varchar(255) DEFAULT NULL COMMENT '密码(加密存储)',
  `register_date` datetime DEFAULT NULL COMMENT '注册时间',
  `last_login_date` datetime DEFAULT NULL COMMENT '最后登录时间',
  
  -- 基础信息字段
  `photo_url` varchar(255) DEFAULT NULL COMMENT '全身照URL',
  `height` int DEFAULT NULL COMMENT '身高(cm)',
  `weight` int DEFAULT NULL COMMENT '体重(kg)',
  `body_type` varchar(32) DEFAULT NULL COMMENT '体型（标准/偏瘦/偏胖）',
  `skin_tone` varchar(32) DEFAULT NULL COMMENT '肤色',
  `style_preferences` json DEFAULT NULL COMMENT '风格偏好',
  `body_shape` json DEFAULT NULL COMMENT '体型细分',
  `mode` varchar(32) DEFAULT 'natural' COMMENT '用户模式（natural/energy）',
  
  -- 居住地点信息字段
  `residence_name` varchar(128) DEFAULT NULL COMMENT '居住地点中文名称',
  `residence_code` varchar(32) DEFAULT NULL COMMENT '居住地点地区代码',
  
  -- 五行命理信息字段
  `full_name` varchar(64) DEFAULT NULL COMMENT '姓名',
  `birth_date` varchar(32) DEFAULT NULL COMMENT '出生日期',
  `birth_time` varchar(32) DEFAULT NULL COMMENT '出生时间',
  `birth_place` varchar(64) DEFAULT NULL COMMENT '出生地点',
  
  -- JSON字段
  `preferences` json DEFAULT NULL COMMENT '用户偏好',
  `body_info` json DEFAULT NULL COMMENT '用户身体信息',
  `wuxing_profile` json DEFAULT NULL COMMENT '五行命理信息',
  `settings` json DEFAULT NULL COMMENT '用户设置',
  `stats` json DEFAULT NULL COMMENT '用户统计',
  `extensions` json DEFAULT NULL COMMENT '扩展字段',
  
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_id` (`user_id`),
  UNIQUE KEY `uk_phone` (`phone`),
  UNIQUE KEY `uk_email` (`email`),
  UNIQUE KEY `uk_open_id` (`open_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户信息表';

-- 认证表
CREATE TABLE IF NOT EXISTS `user_auth` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` varchar(64) NOT NULL COMMENT '用户ID',
  `token` varchar(255) NOT NULL COMMENT '认证令牌',
  `refresh_token` varchar(255) NOT NULL COMMENT '刷新令牌',
  `expires_at` datetime NOT NULL COMMENT '过期时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_token` (`token`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='认证表';

-- 用户完整运势解读表
CREATE TABLE IF NOT EXISTS `user_fortune_reading` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` varchar(64) NOT NULL COMMENT '用户ID',
  `reading_date` date NOT NULL COMMENT '解读日期',
  `bazi_combination` json NOT NULL COMMENT '八字组合',
  `wuxing_analysis` json NOT NULL COMMENT '五行分析',
  `overall_fortune` json NOT NULL COMMENT '整体运势',
  `lucky_advice` json NOT NULL COMMENT '吉运建议',
  `detailed_fortune` json NOT NULL COMMENT '详细运势解读',
  `ai_generated_at` datetime DEFAULT NULL COMMENT 'AI生成时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_date` (`user_id`, `reading_date`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_reading_date` (`reading_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户完整运势解读表';