# 本地开发环境配置
server:
  port: 8081

# JWT安全配置
security:
  jwt:
    secret-key: c3R5bGlzaGxpbmtfc2VjcmV0X2tleV9mb3Jfand0X3Rva2VuX2dlbmVyYXRpb25fYW5kX3ZhbGlkYXRpb25fc2VjdXJpdHk=
    token-validity-in-seconds: 86400  # 24小时
    token-validity-in-seconds-for-remember-me: 2592000  # 30天

# 数据库配置
spring:
  datasource:
    url: *****************************************************************************************************************************
    username: root
    password: root
    driver-class-name: com.mysql.cj.jdbc.Driver
  redis:
    host: localhost
    port: 6379
    database: 0

# AI服务配置
ai-service:
  enabled: true  # 启用AI服务
  url: http://localhost:8084  # AI服务地址（修正端口）

# 文件服务配置
file-service:
  url: http://localhost:8082  # 文件服务地址

# Feign全局配置
feign:
  client:
    config:
      default:
        connectTimeout: 5000
        readTimeout: 10000
        
# 日志配置
logging:
  level:
    com.stylishlink.user.client: DEBUG
    org.springframework.cloud.openfeign: DEBUG 