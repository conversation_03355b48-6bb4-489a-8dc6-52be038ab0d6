#!/bin/bash

# StylishLink Gateway Service 启动脚本
# 作者: StylishLink团队
# 版本: 1.0.0

# 服务配置
SERVICE_NAME="stylishlink-wardrobe-service"
VERSION="1.0.0-SNAPSHOT"
JAR_NAME="${SERVICE_NAME}-${VERSION}.jar"

# 路径配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
JAR_PATH="./${JAR_NAME}"
PID_FILE="${SCRIPT_DIR}/${SERVICE_NAME}.pid"
LOG_DIR="${SCRIPT_DIR}/logs"
LOG_FILE="${LOG_DIR}/${SERVICE_NAME}.log"

# JVM 配置
JAVA_OPTS="-Xms512m -Xmx768m"
JAVA_OPTS="${JAVA_OPTS} -Dfile.encoding=UTF-8"
JAVA_OPTS="${JAVA_OPTS} -Djava.awt.headless=true"
JAVA_OPTS="${JAVA_OPTS} -Djava.security.egd=file:/dev/./urandom"
JAVA_OPTS="${JAVA_OPTS} -XX:+UseG1GC"
JAVA_OPTS="${JAVA_OPTS} -XX:+HeapDumpOnOutOfMemoryError"
JAVA_OPTS="${JAVA_OPTS} -XX:HeapDumpPath=${LOG_DIR}/"

# Spring 配置
SPRING_PROFILES_ACTIVE="sit"
SPRING_OPTS="--spring.profiles.active=${SPRING_PROFILES_ACTIVE}"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 创建日志目录
mkdir -p "${LOG_DIR}"

# 获取进程ID
get_pid() {
    if [ -f "${PID_FILE}" ]; then
        echo $(cat "${PID_FILE}")
    else
        echo ""
    fi
}

# 检查服务是否运行
is_running() {
    local pid=$(get_pid)
    if [ -n "${pid}" ]; then
        # 在macOS中使用ps命令检查进程是否存在
        if ps -p "${pid}" > /dev/null 2>&1; then
            return 0
        else
            return 1
        fi
    else
        return 1
    fi
}

# 等待服务启动
wait_for_start() {
    local timeout=30
    local counter=0
    
    echo -n "等待服务启动"
    while [ ${counter} -lt ${timeout} ]; do
        if is_running; then
            echo -e "\n${GREEN}[SUCCESS]${NC} 服务启动成功"
            return 0
        fi
        echo -n "."
        sleep 1
        counter=$((counter + 1))
    done
    
    echo -e "\n${RED}[ERROR]${NC} 服务启动超时"
    return 1
}

# 等待服务停止
wait_for_stop() {
    local timeout=30
    local counter=0
    local pid=$(get_pid)
    
    if [ -z "${pid}" ]; then
        return 0
    fi
    
    echo -n "等待服务停止"
    while [ ${counter} -lt ${timeout} ]; do
        if ! is_running; then
            echo -e "\n${GREEN}[SUCCESS]${NC} 服务已停止"
            rm -f "${PID_FILE}"
            return 0
        fi
        echo -n "."
        sleep 1
        counter=$((counter + 1))
    done
    
    echo -e "\n${YELLOW}[WARNING]${NC} 服务停止超时，强制终止进程"
    kill -9 "${pid}" 2>/dev/null
    rm -f "${PID_FILE}"
    return 1
}

# 启动服务
start() {
    echo -e "${BLUE}[INFO]${NC} 启动 ${SERVICE_NAME}..."
    
    # 检查服务是否已经运行
    if is_running; then
        echo -e "${YELLOW}[WARNING]${NC} 服务已经在运行中 (PID: $(get_pid))"
        return 1
    fi
    
    # 检查JAR文件是否存在
    if [ ! -f "${JAR_PATH}" ]; then
        echo -e "${RED}[ERROR]${NC} JAR 文件不存在: ${JAR_PATH}"
        echo -e "${YELLOW}[HINT]${NC} 请先执行 'mvn clean package' 构建项目"
        return 1
    fi
    
    # 检查Java环境
    if ! command -v java &> /dev/null; then
        echo -e "${RED}[ERROR]${NC} Java 环境未找到，请安装 Java 17+"
        return 1
    fi
    
    # 显示启动信息
    echo -e "${BLUE}[INFO]${NC} JAR文件: ${JAR_PATH}"
    echo -e "${BLUE}[INFO]${NC} 日志文件: ${LOG_FILE}"
    echo -e "${BLUE}[INFO]${NC} Spring配置: ${SPRING_PROFILES_ACTIVE}"
    echo -e "${BLUE}[INFO]${NC} JVM参数: ${JAVA_OPTS}"
    
    # 启动服务
    nohup java ${JAVA_OPTS} -jar "${JAR_PATH}" ${SPRING_OPTS} > "${LOG_FILE}" 2>&1 &
    local pid=$!
    
    # 保存PID
    echo ${pid} > "${PID_FILE}"
    
    # 等待启动完成
    if wait_for_start; then
        echo -e "${GREEN}[SUCCESS]${NC} ${SERVICE_NAME} 启动成功 (PID: ${pid})"
        echo -e "${BLUE}[INFO]${NC} 日志: tail -f ${LOG_FILE}"
        return 0
    else
        echo -e "${RED}[ERROR]${NC} ${SERVICE_NAME} 启动失败"
        echo -e "${BLUE}[INFO]${NC} 查看日志: tail -f ${LOG_FILE}"
        rm -f "${PID_FILE}"
        return 1
    fi
}

# 停止服务
stop() {
    echo -e "${BLUE}[INFO]${NC} 停止 ${SERVICE_NAME}..."
    
    local pid=$(get_pid)
    if [ -z "${pid}" ]; then
        echo -e "${YELLOW}[WARNING]${NC} 服务未运行"
        return 0
    fi
    
    echo -e "${BLUE}[INFO]${NC} 终止进程 (PID: ${pid})"
    
    # 发送TERM信号
    kill -TERM "${pid}" 2>/dev/null
    
    # 等待服务停止
    if wait_for_stop; then
        echo -e "${GREEN}[SUCCESS]${NC} ${SERVICE_NAME} 已停止"
        return 0
    else
        echo -e "${RED}[ERROR]${NC} ${SERVICE_NAME} 停止失败"
        return 1
    fi
}

# 重启服务
restart() {
    echo -e "${BLUE}[INFO]${NC} 重启 ${SERVICE_NAME}..."
    stop
    sleep 2
    start
}

# 查看状态
status() {
    local pid=$(get_pid)
    
    echo -e "${BLUE}[INFO]${NC} ${SERVICE_NAME} 状态检查"
    echo "----------------------------------------"
    
    if is_running; then
        echo -e "状态: ${GREEN}运行中${NC}"
        echo "PID: ${pid}"
        echo "启动时间: $(ps -o lstart= -p ${pid} 2>/dev/null)"
        echo "内存使用: $(ps -o rss= -p ${pid} 2>/dev/null | awk '{printf "%.1f MB\n", $1/1024}')"
        echo "CPU使用: $(ps -o pcpu= -p ${pid} 2>/dev/null | awk '{print $1"%"}')"
    else
        echo -e "状态: ${RED}未运行${NC}"
        if [ -f "${PID_FILE}" ]; then
            echo -e "${YELLOW}[WARNING]${NC} PID文件存在但进程已停止，清理PID文件"
            rm -f "${PID_FILE}"
        fi
    fi
    
    echo "JAR文件: ${JAR_PATH}"
    echo "日志文件: ${LOG_FILE}"
    echo "PID文件: ${PID_FILE}"
}

# 查看日志
logs() {
    if [ -f "${LOG_FILE}" ]; then
        echo -e "${BLUE}[INFO]${NC} 显示最近100行日志:"
        echo "----------------------------------------"
        tail -n 100 "${LOG_FILE}"
    else
        echo -e "${YELLOW}[WARNING]${NC} 日志文件不存在: ${LOG_FILE}"
    fi
}

# 实时查看日志
tail_logs() {
    if [ -f "${LOG_FILE}" ]; then
        echo -e "${BLUE}[INFO]${NC} 实时查看日志 (Ctrl+C 退出):"
        echo "----------------------------------------"
        tail -f "${LOG_FILE}"
    else
        echo -e "${YELLOW}[WARNING]${NC} 日志文件不存在: ${LOG_FILE}"
    fi
}

# 显示帮助信息
usage() {
    echo "用法: $0 {start|stop|restart|status|logs|tail}"
    echo ""
    echo "命令说明:"
    echo "  start   - 启动服务"
    echo "  stop    - 停止服务"  
    echo "  restart - 重启服务"
    echo "  status  - 查看状态"
    echo "  logs    - 查看日志"
    echo "  tail    - 实时查看日志"
    echo ""
    echo "示例:"
    echo "  $0 start          # 启动服务"
    echo "  $0 status         # 查看状态"
    echo "  $0 tail           # 实时查看日志"
}

# 主函数
main() {
    case "$1" in
        start)
            start
            ;;
        stop)
            stop
            ;;
        restart)
            restart
            ;;
        status)
            status
            ;;
        logs)
            logs
            ;;
        tail)
            tail_logs
            ;;
        *)
            usage
            exit 1
            ;;
    esac
}

# 脚本入口
main "$@" 