-- =============================================
-- StylishLink衣橱服务数据库初始化脚本 (MySQL版本)
-- 创建时间: 2025-01-12
-- 描述: 初始化衣橱服务相关的所有数据表和基础数据
-- 符合微服务开发规范
-- =============================================

-- 1. 创建数据库
CREATE DATABASE IF NOT EXISTS `wardrobe` 
CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE `wardrobe`;

-- =============================================
-- 2. 衣物表 (wardrobe_clothing) - 与Clothing实体类完全匹配
-- =============================================
DROP TABLE IF EXISTS `wardrobe_clothing`;
CREATE TABLE `wardrobe_clothing` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '衣物ID',
    `user_id` BIGINT NOT NULL COMMENT '用户ID',
    `name` VARCHAR(255) NOT NULL COMMENT '衣物名称',
    `category` VARCHAR(50) NOT NULL COMMENT '衣物类别',
    `sub_category` VARCHAR(50) DEFAULT NULL COMMENT '衣物子类别',
    `brand` VARCHAR(100) DEFAULT NULL COMMENT '品牌',
    `description` TEXT COMMENT '描述',
    `colors` JSON COMMENT '颜色列表',
    `materials` JSON COMMENT '材质列表',
    `styles` JSON COMMENT '风格标签',
    `occasions` JSON COMMENT '适合场合',
    `seasons` JSON COMMENT '适合季节',
    `tags` JSON COMMENT '标签列表',
    `main_image_url` VARCHAR(500) COMMENT '主图URL',
    `image_urls` JSON COMMENT '其他图片URL列表',
    `wuxing_metal` INT DEFAULT 0 COMMENT '五行-金属性',
    `wuxing_wood` INT DEFAULT 0 COMMENT '五行-木属性',
    `wuxing_water` INT DEFAULT 0 COMMENT '五行-水属性',
    `wuxing_fire` INT DEFAULT 0 COMMENT '五行-火属性',
    `wuxing_earth` INT DEFAULT 0 COMMENT '五行-土属性',
    `purchase_date` DATE COMMENT '购买日期',
    `price` DECIMAL(10,2) COMMENT '价格',
    `wear_count` INT DEFAULT 0 COMMENT '穿着次数',
    `last_worn_at` TIMESTAMP NULL COMMENT '最后穿着时间',
    `status` TINYINT DEFAULT 1 COMMENT '状态(1:正常 0:禁用 -1:删除)',
    `created_by` BIGINT NOT NULL COMMENT '创建人ID',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_by` BIGINT NOT NULL COMMENT '更新人ID',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_user_category` (`user_id`, `category`),
    INDEX `idx_user_created` (`user_id`, `created_at`),
    INDEX `idx_wear_count` (`wear_count`),
    INDEX `idx_last_worn` (`last_worn_at`),
    INDEX `idx_status` (`status`),
    INDEX `idx_created_by` (`created_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='衣物表';

-- =============================================
-- 3. 饰品表 (wardrobe_accessories) - 与Accessory实体类匹配
-- =============================================
DROP TABLE IF EXISTS `wardrobe_accessories`;
CREATE TABLE `wardrobe_accessories` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '饰品ID',
    `user_id` BIGINT NOT NULL COMMENT '用户ID',
    `name` VARCHAR(255) NOT NULL COMMENT '饰品名称',
    `type` VARCHAR(50) NOT NULL COMMENT '饰品类型',
    `sub_type` VARCHAR(50) DEFAULT NULL COMMENT '饰品子类型',
    `brand` VARCHAR(100) DEFAULT NULL COMMENT '品牌',
    `description` TEXT COMMENT '描述',
    `colors` JSON COMMENT '颜色列表',
    `materials` JSON COMMENT '材质列表',
    `styles` JSON COMMENT '风格标签',
    `occasions` JSON COMMENT '适合场合',
    `seasons` JSON COMMENT '适合季节',
    `tags` JSON COMMENT '标签列表',
    `main_image_url` VARCHAR(500) COMMENT '主图URL',
    `image_urls` JSON COMMENT '其他图片URL列表',
    `wuxing_metal` INT DEFAULT 0 COMMENT '五行-金属性',
    `wuxing_wood` INT DEFAULT 0 COMMENT '五行-木属性',
    `wuxing_water` INT DEFAULT 0 COMMENT '五行-水属性',
    `wuxing_fire` INT DEFAULT 0 COMMENT '五行-火属性',
    `wuxing_earth` INT DEFAULT 0 COMMENT '五行-土属性',
    `purchase_date` DATE COMMENT '购买日期',
    `price` DECIMAL(10,2) COMMENT '价格',
    `wear_count` INT DEFAULT 0 COMMENT '穿着次数',
    `last_worn_at` TIMESTAMP NULL COMMENT '最后穿着时间',
    `status` TINYINT DEFAULT 1 COMMENT '状态(1:正常 0:禁用 -1:删除)',
    `created_by` BIGINT NOT NULL COMMENT '创建人ID',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_by` BIGINT NOT NULL COMMENT '更新人ID',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_user_type` (`user_id`, `type`),
    INDEX `idx_user_created` (`user_id`, `created_at`),
    INDEX `idx_wear_count` (`wear_count`),
    INDEX `idx_last_worn` (`last_worn_at`),
    INDEX `idx_status` (`status`),
    INDEX `idx_created_by` (`created_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='饰品表';

-- =============================================
-- 4. 搭配表 (wardrobe_outfits) - 与Outfit实体类匹配
-- =============================================
DROP TABLE IF EXISTS `wardrobe_outfits`;
CREATE TABLE `wardrobe_outfits` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '搭配ID',
    `user_id` BIGINT NOT NULL COMMENT '用户ID',
    `name` VARCHAR(255) NOT NULL COMMENT '搭配名称',
    `description` TEXT COMMENT '搭配描述',
    `clothing_ids` JSON COMMENT '衣物ID列表',
    `accessory_ids` JSON COMMENT '饰品ID列表',
    `occasions` JSON COMMENT '适合场合',
    `seasons` JSON COMMENT '适合季节',
    `styles` JSON COMMENT '风格标签',
    `images` JSON COMMENT '搭配效果图URL列表',
    `wuxing_metal` INT DEFAULT 0 COMMENT '五行-金属性',
    `wuxing_wood` INT DEFAULT 0 COMMENT '五行-木属性',
    `wuxing_water` INT DEFAULT 0 COMMENT '五行-水属性',
    `wuxing_fire` INT DEFAULT 0 COMMENT '五行-火属性',
    `wuxing_earth` INT DEFAULT 0 COMMENT '五行-土属性',
    `wear_count` INT DEFAULT 0 COMMENT '穿着次数',
    `is_user_created` BOOLEAN DEFAULT TRUE COMMENT '是否用户创建',
    `last_worn_at` TIMESTAMP NULL COMMENT '最后穿着时间',
    `status` TINYINT DEFAULT 1 COMMENT '状态(1:正常 0:禁用 -1:删除)',
    `created_by` BIGINT NOT NULL COMMENT '创建人ID',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_by` BIGINT NOT NULL COMMENT '更新人ID',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_user_created` (`user_id`, `created_at`),
    INDEX `idx_wear_count` (`wear_count`),
    INDEX `idx_is_user_created` (`is_user_created`),
    INDEX `idx_last_worn` (`last_worn_at`),
    INDEX `idx_status` (`status`),
    INDEX `idx_created_by` (`created_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='搭配表';

-- =============================================
-- 5. 穿着记录表 (wardrobe_wear_records) - 符合开发规范
-- =============================================
DROP TABLE IF EXISTS `wardrobe_wear_records`;
CREATE TABLE `wardrobe_wear_records` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '记录ID',
    `user_id` BIGINT NOT NULL COMMENT '用户ID',
    `item_id` BIGINT NOT NULL COMMENT '物品ID(衣物/搭配)',
    `item_type` VARCHAR(20) NOT NULL COMMENT '物品类型(clothing/outfit)',
    `weather` VARCHAR(100) COMMENT '天气情况',
    `temperature` DECIMAL(5,2) COMMENT '温度',
    `occasion` VARCHAR(100) COMMENT '场合',
    `location` VARCHAR(255) COMMENT '地点',
    `mood` VARCHAR(50) COMMENT '心情',
    `notes` TEXT COMMENT '备注',
    `wear_date` DATE NOT NULL COMMENT '穿着日期',
    `status` TINYINT DEFAULT 1 COMMENT '状态(1:正常 0:禁用 -1:删除)',
    `created_by` BIGINT NOT NULL COMMENT '创建人ID',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_by` BIGINT NOT NULL COMMENT '更新人ID',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_user_date` (`user_id`, `wear_date`),
    INDEX `idx_item` (`item_id`, `item_type`),
    INDEX `idx_wear_date` (`wear_date`),
    INDEX `idx_status` (`status`),
    INDEX `idx_created_by` (`created_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='穿着记录表';

-- =============================================
-- 6. 衣橱分析表 (wardrobe_analysis) - 符合开发规范
-- =============================================
DROP TABLE IF EXISTS `wardrobe_analysis`;
CREATE TABLE `wardrobe_analysis` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '分析ID',
    `user_id` BIGINT NOT NULL COMMENT '用户ID',
    `analysis_date` DATE NOT NULL COMMENT '分析日期',
    `style_analysis` JSON COMMENT '风格分析',
    `color_analysis` JSON COMMENT '色彩分析',
    `wuxing_balance` JSON COMMENT '五行平衡分析',
    `wear_frequency` JSON COMMENT '穿着频率分析',
    `category_distribution` JSON COMMENT '类别分布',
    `season_coverage` JSON COMMENT '季节覆盖率',
    `occasion_coverage` JSON COMMENT '场合覆盖率',
    `recommendations` JSON COMMENT '推荐建议',
    `total_items` INT DEFAULT 0 COMMENT '总单品数量',
    `total_outfits` INT DEFAULT 0 COMMENT '总搭配数量',
    `active_items` INT DEFAULT 0 COMMENT '活跃单品数量',
    `inactive_items` INT DEFAULT 0 COMMENT '闲置单品数量',
    `status` TINYINT DEFAULT 1 COMMENT '状态(1:正常 0:禁用 -1:删除)',
    `created_by` BIGINT NOT NULL COMMENT '创建人ID',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_by` BIGINT NOT NULL COMMENT '更新人ID',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE INDEX `uk_user_date` (`user_id`, `analysis_date`),
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_analysis_date` (`analysis_date`),
    INDEX `idx_status` (`status`),
    INDEX `idx_created_by` (`created_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='衣橱分析表';

SELECT 'MySQL数据库初始化完成！符合微服务开发规范' as message;
