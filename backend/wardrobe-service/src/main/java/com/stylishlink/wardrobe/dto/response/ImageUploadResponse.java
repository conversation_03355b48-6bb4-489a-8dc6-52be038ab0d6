package com.stylishlink.wardrobe.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 图片上传响应DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ImageUploadResponse {
    
    /**
     * 图片URL
     */
    private String url;
    
    /**
     * 图片类型
     */
    private String type;
    
    /**
     * 文件大小（字节）
     */
    private Long fileSize;
    
    /**
     * 图片宽度
     */
    private Integer width;
    
    /**
     * 图片高度
     */
    private Integer height;
    
    /**
     * 处理状态
     */
    private String processStatus;
    
    /**
     * 处理后的图片URL（如去背景等）
     */
    private String processedUrl;
    
    /**
     * AI识别结果
     */
    private Map<String, Object> aiRecognition;
    
    /**
     * 上传时间
     */
    private LocalDateTime uploadTime;
    
    /**
     * 处理时间
     */
    private LocalDateTime processTime;
} 