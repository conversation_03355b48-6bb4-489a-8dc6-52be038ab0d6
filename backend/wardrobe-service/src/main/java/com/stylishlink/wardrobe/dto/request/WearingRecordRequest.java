package com.stylishlink.wardrobe.dto.request;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 穿着记录请求DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WearingRecordRequest {
    
    /**
     * 物品ID（衣物ID或搭配ID）
     */
    @NotBlank(message = "物品ID不能为空")
    private String itemId;
    
    /**
     * 物品类型：clothing（衣物）或outfit（搭配）
     */
    @NotBlank(message = "物品类型不能为空")
    private String type;
    
    /**
     * 穿着日期
     */
    private LocalDateTime date;
    
    /**
     * 穿着场合
     */
    private String occasion;
    
    /**
     * 天气信息
     */
    private String weather;
    
    /**
     * 心情评分 (1-5)
     */
    private Integer moodRating;
    
    /**
     * 舒适度评分 (1-5)
     */
    private Integer comfortRating;
    
    /**
     * 备注
     */
    private String notes;
    
    /**
     * 照片URL
     */
    private String photoUrl;
} 