package com.stylishlink.wardrobe.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 衣物响应DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ClothingResponse {
    
    /**
     * ID
     */
    private String id;
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 名称
     */
    private String name;
    
    /**
     * 描述
     */
    private String description;
    
    /**
     * 类别
     */
    private String category;
    
    /**
     * 颜色
     */
    private String color;
    
    /**
     * 尺寸
     */
    private String size;
    
    /**
     * 品牌
     */
    private String brand;
    
    /**
     * 适合季节列表
     */
    private List<String> seasons;
    
    /**
     * 适合场合列表
     */
    private List<String> occasions;
    
    /**
     * 标签列表
     */
    private List<String> tags;
    
    /**
     * 购买日期
     */
    private String purchaseDate;
    
    /**
     * 购买价格
     */
    private Double price;
    
    /**
     * 图片URL
     */
    private String mainImageUrl;
    
    /**
     * 喜爱程度 (1-5)
     */
    private Integer favoriteLevel;
    
    /**
     * 是否归档
     */
    private Boolean isArchived;
    
    /**
     * 属性
     */
    private String properties;
    
    /**
     * 衣物材质
     */
    private String material;
    
    /**
     * 穿着次数
     */
    private Integer wearCount;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
} 