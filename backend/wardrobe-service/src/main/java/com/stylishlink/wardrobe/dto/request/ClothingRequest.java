package com.stylishlink.wardrobe.dto.request;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 衣物请求DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ClothingRequest {
    
    /**
     * 名称
     */
    @NotBlank(message = "名称不能为空")
    @Size(max = 50, message = "名称长度不能超过50个字符")
    private String name;
    
    /**
     * 描述
     */
    @Size(max = 500, message = "描述长度不能超过500个字符")
    private String description;
    
    /**
     * 类别
     */
    @NotBlank(message = "类别不能为空")
    private String category;
    
    /**
     * 颜色
     */
    @NotBlank(message = "颜色不能为空")
    private String color;
    
    /**
     * 尺寸
     */
    private String size;
    
    /**
     * 品牌
     */
    private String brand;
    
    /**
     * 适合季节列表
     */
    private List<String> seasons;
    
    /**
     * 适合场合列表
     */
    private List<String> occasions;
    
    /**
     * 标签列表
     */
    private List<String> tags;
    
    /**
     * 购买日期
     */
    private String purchaseDate;
    
    /**
     * 购买价格
     */
    @Min(value = 0, message = "购买价格不能为负数")
    private Double price;
    
    /**
     * 图片URL
     */
    private String mainImageUrl;
    
    /**
     * 喜爱程度 (1-5)
     */
    @Min(value = 1, message = "喜爱程度最小为1")
    @Max(value = 5, message = "喜爱程度最大为5")
    private Integer favoriteLevel;
    
    /**
     * 是否归档
     */
    private Boolean isArchived;
    
    /**
     * 属性
     */
    private String properties;
    
    /**
     * 衣物材质
     */
    private String material;
    
    /**
     * 穿着次数
     */
    @Min(value = 0, message = "穿着次数不能为负数")
    private Integer wearCount;
} 