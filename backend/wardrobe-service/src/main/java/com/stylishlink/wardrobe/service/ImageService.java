package com.stylishlink.wardrobe.service;

import com.stylishlink.wardrobe.dto.response.ImageUploadResponse;
import org.springframework.web.multipart.MultipartFile;

/**
 * 图片服务接口
 */
public interface ImageService {
    
    /**
     * 上传图片
     *
     * @param userId 用户ID
     * @param file 图片文件
     * @param type 图片类型
     * @return 上传结果
     */
    ImageUploadResponse uploadImage(String userId, MultipartFile file, String type);
    
    /**
     * 处理图片（去背景等）
     *
     * @param userId 用户ID
     * @param imageUrl 图片URL
     * @param processType 处理类型
     * @return 处理结果URL
     */
    String processImage(String userId, String imageUrl, String processType);
    
    /**
     * 删除图片
     *
     * @param userId 用户ID
     * @param imageUrl 图片URL
     */
    void deleteImage(String userId, String imageUrl);
} 