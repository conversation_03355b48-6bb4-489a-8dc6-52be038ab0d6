package com.stylishlink.wardrobe.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.stylishlink.wardrobe.entity.Clothing;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 衣物仓库接口
 */
@Mapper
public interface ClothingRepository extends BaseMapper<Clothing> {
    
    /**
     * 根据用户ID查询衣物列表
     * @param userId 用户ID
     * @return 衣物列表
     */
    @Select("SELECT * FROM clothing WHERE user_id = #{userId}")
    List<Clothing> selectByUserId(@Param("userId") String userId);
    
    /**
     * 根据用户ID分页查询衣物
     * @param page 分页参数
     * @param userId 用户ID
     * @return 分页衣物列表
     */
    @Select("SELECT * FROM clothing WHERE user_id = #{userId}")
    Page<Clothing> selectByUserId(Page<Clothing> page, @Param("userId") String userId);
    
    /**
     * 根据用户ID和类别查询衣物
     * @param userId 用户ID
     * @param category 类别
     * @return 衣物列表
     */
    @Select("SELECT * FROM clothing WHERE user_id = #{userId} AND category = #{category}")
    List<Clothing> selectByUserIdAndCategory(@Param("userId") String userId, @Param("category") String category);
    
    /**
     * 根据用户ID和类别分页查询衣物
     * @param page 分页参数
     * @param userId 用户ID
     * @param category 类别
     * @return 分页衣物列表
     */
    @Select("SELECT * FROM clothing WHERE user_id = #{userId} AND category = #{category}")
    Page<Clothing> selectByUserIdAndCategory(Page<Clothing> page, @Param("userId") String userId, @Param("category") String category);
    
    /**
     * 根据用户ID和季节查询衣物
     * @param userId 用户ID
     * @param season 季节
     * @return 衣物列表
     */
    @Select("SELECT * FROM clothing WHERE user_id = #{userId} AND JSON_CONTAINS(seasons, JSON_QUOTE(#{season}))")
    List<Clothing> selectByUserIdAndSeason(@Param("userId") String userId, @Param("season") String season);
    
    /**
     * 根据用户ID和场合查询衣物
     * @param userId 用户ID
     * @param occasion 场合
     * @return 衣物列表
     */
    @Select("SELECT * FROM clothing WHERE user_id = #{userId} AND JSON_CONTAINS(occasions, JSON_QUOTE(#{occasion}))")
    List<Clothing> selectByUserIdAndOccasion(@Param("userId") String userId, @Param("occasion") String occasion);
    
    /**
     * 根据用户ID和标签查询衣物
     * @param userId 用户ID
     * @param tag 标签
     * @return 衣物列表
     */
    @Select("SELECT * FROM clothing WHERE user_id = #{userId} AND JSON_CONTAINS(tags, JSON_QUOTE(#{tag}))")
    List<Clothing> selectByUserIdAndTag(@Param("userId") String userId, @Param("tag") String tag);
    
    /**
     * 根据用户ID和多个类别查询衣物
     * @param userId 用户ID
     * @param categories 类别列表
     * @return 衣物列表
     */
    @Select("<script>" +
            "SELECT * FROM clothing WHERE user_id = #{userId} AND category IN " +
            "<foreach collection='categories' item='category' open='(' separator=',' close=')'>" +
            "#{category}" +
            "</foreach>" +
            "</script>")
    List<Clothing> selectByUserIdAndCategoryIn(@Param("userId") String userId, @Param("categories") List<String> categories);
    
    /**
     * 检查衣物是否属于特定用户
     * @param id 衣物ID
     * @param userId 用户ID
     * @return 是否属于该用户
     */
    @Select("SELECT COUNT(*) > 0 FROM clothing WHERE id = #{id} AND user_id = #{userId}")
    boolean existsByIdAndUserId(@Param("id") String id, @Param("userId") String userId);
    
    /**
     * 根据用户ID统计衣物数量
     * @param userId 用户ID
     * @return 衣物数量
     */
    @Select("SELECT COUNT(*) FROM clothing WHERE user_id = #{userId}")
    int countByUserId(@Param("userId") String userId);
    
    /**
     * 根据用户ID和类别统计衣物数量
     * @param userId 用户ID
     * @param category 类别
     * @return 衣物数量
     */
    @Select("SELECT COUNT(*) FROM clothing WHERE user_id = #{userId} AND category = #{category}")
    int countByUserIdAndCategory(@Param("userId") String userId, @Param("category") String category);
} 