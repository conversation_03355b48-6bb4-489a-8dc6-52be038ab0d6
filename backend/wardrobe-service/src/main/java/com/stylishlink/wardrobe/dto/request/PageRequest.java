package com.stylishlink.wardrobe.dto.request;

import jakarta.validation.constraints.Min;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 分页请求DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PageRequest {
    
    /**
     * 页码
     */
    @Min(value = 1, message = "页码必须大于等于1")
    private Integer page;
    
    /**
     * 每页大小
     */
    @Min(value = 1, message = "每页大小必须大于等于1")
    private Integer size;
    
    /**
     * 排序字段
     */
    private String sort;
    
    /**
     * 排序方向，true为升序，false为降序
     */
    private Boolean asc;
    
    /**
     * 获取排序方向
     * @return 排序方向，"asc"或"desc"
     */
    public String getOrder() {
        return asc != null && asc ? "asc" : "desc";
    }
    
    /**
     * 将当前对象转换为Spring Data的Pageable对象
     * @return Pageable对象
     */
    public org.springframework.data.domain.PageRequest toPageable() {
        if (page == null || page < 1) {
            page = 1;
        }
        if (size == null || size < 1) {
            size = 10;
        }
        
        // 页码从0开始
        int pageIndex = page - 1;
        
        if (sort != null && !sort.isEmpty()) {
            org.springframework.data.domain.Sort.Direction direction = 
                    asc != null && asc ? org.springframework.data.domain.Sort.Direction.ASC : 
                            org.springframework.data.domain.Sort.Direction.DESC;
            return org.springframework.data.domain.PageRequest.of(
                    pageIndex, 
                    size, 
                    org.springframework.data.domain.Sort.by(direction, sort)
            );
        } else {
            return org.springframework.data.domain.PageRequest.of(pageIndex, size);
        }
    }
} 