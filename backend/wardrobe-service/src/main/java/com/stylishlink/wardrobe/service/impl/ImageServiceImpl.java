package com.stylishlink.wardrobe.service.impl;

import com.stylishlink.wardrobe.dto.response.ImageUploadResponse;
import com.stylishlink.wardrobe.service.ImageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.UUID;

/**
 * 图片服务实现类
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class ImageServiceImpl implements ImageService {
    
    @Value("${stylishlink.image.upload-path:/tmp/stylishlink/images}")
    private String uploadPath;
    
    @Value("${stylishlink.image.base-url:http://localhost:8083/images}")
    private String baseUrl;
    
    @Value("${stylishlink.image.max-size:10485760}") // 10MB
    private long maxSize;
    
    @Override
    public ImageUploadResponse uploadImage(String userId, MultipartFile file, String type) {
        try {
            // 验证文件
            validateFile(file);
            
            // 生成文件名
            String fileName = generateFileName(file.getOriginalFilename());
            
            // 创建上传目录
            Path uploadDir = Paths.get(uploadPath, type);
            Files.createDirectories(uploadDir);
            
            // 保存文件
            Path filePath = uploadDir.resolve(fileName);
            Files.copy(file.getInputStream(), filePath);
            
            // 构建访问URL
            String url = baseUrl + "/" + type + "/" + fileName;
            
            log.info("用户 {} 图片上传成功: {}", userId, url);
            
            return ImageUploadResponse.builder()
                    .url(url)
                    .type(type)
                    .fileSize(file.getSize())
                    .processStatus("uploaded")
                    .uploadTime(java.time.LocalDateTime.now())
                    .build();
                    
        } catch (IOException e) {
            log.error("用户 {} 图片上传失败", userId, e);
            throw new RuntimeException("图片上传失败: " + e.getMessage());
        }
    }
    
    @Override
    public void deleteImage(String userId, String imageUrl) {
        try {
            // 从URL中提取文件路径
            String relativePath = imageUrl.replace(baseUrl, "");
            Path filePath = Paths.get(uploadPath + relativePath);
            
            if (Files.exists(filePath)) {
                Files.delete(filePath);
                log.info("用户 {} 图片删除成功: {}", userId, imageUrl);
            } else {
                log.warn("用户 {} 要删除的图片不存在: {}", userId, imageUrl);
            }
            
        } catch (IOException e) {
            log.error("用户 {} 图片删除失败: {}", userId, imageUrl, e);
            throw new RuntimeException("图片删除失败: " + e.getMessage());
        }
    }
    
    @Override
    public String processImage(String userId, String imageUrl, String processType) {
        // TODO: 实现图片处理逻辑（如压缩、裁剪、去背景等）
        // 目前返回原图URL，后续可以集成AI图片处理服务
        log.info("用户 {} 图片处理请求: {} - {}", userId, imageUrl, processType);
        return imageUrl;
    }
    
    /**
     * 验证上传文件
     */
    private void validateFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new IllegalArgumentException("文件不能为空");
        }
        
        if (file.getSize() > maxSize) {
            throw new IllegalArgumentException("文件大小不能超过 " + (maxSize / 1024 / 1024) + "MB");
        }
        
        String contentType = file.getContentType();
        if (contentType == null || !isValidImageType(contentType)) {
            throw new IllegalArgumentException("不支持的图片格式，请上传 JPG、PNG、GIF 格式的图片");
        }
    }
    
    /**
     * 检查是否为有效的图片类型
     */
    private boolean isValidImageType(String contentType) {
        return contentType.equals("image/jpeg") ||
               contentType.equals("image/png") ||
               contentType.equals("image/gif") ||
               contentType.equals("image/webp");
    }
    
    /**
     * 生成文件名
     */
    private String generateFileName(String originalFilename) {
        String extension = "";
        if (originalFilename != null && originalFilename.contains(".")) {
            extension = originalFilename.substring(originalFilename.lastIndexOf("."));
        }
        return UUID.randomUUID().toString() + extension;
    }
} 