package com.stylishlink.wardrobe.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.stylishlink.common.dto.PageResponse;
import com.stylishlink.common.exception.BusinessException;
import com.stylishlink.common.exception.ResourceNotFoundException;
import com.stylishlink.wardrobe.dto.request.ClothingRequest;
import com.stylishlink.wardrobe.dto.response.ClothingResponse;
import com.stylishlink.wardrobe.entity.Clothing;
import com.stylishlink.wardrobe.repository.ClothingRepository;
import com.stylishlink.wardrobe.service.ClothingService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 衣物服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class ClothingServiceImpl implements ClothingService {
    
    private final ClothingRepository clothingRepository;
    
    @Override
    @Transactional
    public ClothingResponse createClothing(Long userId, ClothingRequest request) {
        log.info("创建衣物: userId={}, name={}", userId, request.getName());
        
        Clothing clothing = new Clothing();
        clothing.setUserId(userId);
        clothing.setName(request.getName());
        clothing.setCategory(request.getCategory());
        clothing.setBrand(request.getBrand());
        clothing.setDescription(request.getDescription());
        clothing.setOccasions(request.getOccasions());
        clothing.setSeasons(request.getSeasons());
        clothing.setTags(request.getTags());
        clothing.setMainImageUrl(request.getMainImageUrl());
        
        // 处理单个color转为colors列表
        if (request.getColor() != null) {
            clothing.setColors(List.of(request.getColor()));
        }
        
        // 处理单个material转为materials列表
        if (request.getMaterial() != null) {
            clothing.setMaterials(List.of(request.getMaterial()));
        }
        
        // 处理价格转换
        if (request.getPrice() != null) {
            clothing.setPrice(BigDecimal.valueOf(request.getPrice()));
        }
        
        // 处理购买日期转换
        if (request.getPurchaseDate() != null) {
            try {
                clothing.setPurchaseDate(LocalDate.parse(request.getPurchaseDate()));
            } catch (Exception e) {
                log.warn("购买日期格式错误: {}", request.getPurchaseDate());
            }
        }
        
        clothing.setWearCount(0);
        
        clothingRepository.insert(clothing);
        
        log.info("衣物创建成功: id={}", clothing.getId());
        return convertToResponse(clothing);
    }
    
    @Override
    public ClothingResponse getClothing(Long userId, Long id) {
        log.info("获取衣物: userId={}, id={}", userId, id);
        
        Clothing clothing = clothingRepository.selectById(id);
        if (clothing == null || !clothing.getUserId().equals(userId)) {
            throw new ResourceNotFoundException("衣物不存在或无权限访问");
        }
        
        return convertToResponse(clothing);
    }
    
    @Override
    @Transactional
    public ClothingResponse updateClothing(Long userId, Long id, ClothingRequest request) {
        log.info("更新衣物: userId={}, id={}", userId, id);
        
        Clothing clothing = clothingRepository.selectById(id);
        if (clothing == null || !clothing.getUserId().equals(userId)) {
            throw new ResourceNotFoundException("衣物不存在或无权限访问");
        }
        
        // 更新字段
        clothing.setName(request.getName());
        clothing.setCategory(request.getCategory());
        clothing.setBrand(request.getBrand());
        clothing.setDescription(request.getDescription());
        clothing.setOccasions(request.getOccasions());
        clothing.setSeasons(request.getSeasons());
        clothing.setTags(request.getTags());
        clothing.setMainImageUrl(request.getMainImageUrl());
        
        // 处理单个color转为colors列表
        if (request.getColor() != null) {
            clothing.setColors(List.of(request.getColor()));
        }
        
        // 处理单个material转为materials列表
        if (request.getMaterial() != null) {
            clothing.setMaterials(List.of(request.getMaterial()));
        }
        
        // 处理价格转换
        if (request.getPrice() != null) {
            clothing.setPrice(BigDecimal.valueOf(request.getPrice()));
        }
        
        // 处理购买日期转换
        if (request.getPurchaseDate() != null) {
            try {
                clothing.setPurchaseDate(LocalDate.parse(request.getPurchaseDate()));
            } catch (Exception e) {
                log.warn("购买日期格式错误: {}", request.getPurchaseDate());
            }
        }
        
        clothingRepository.updateById(clothing);
        
        log.info("衣物更新成功: id={}", id);
        return convertToResponse(clothing);
    }
    
    @Override
    @Transactional
    public void deleteClothing(Long userId, Long id) {
        log.info("删除衣物: userId={}, id={}", userId, id);
        
        Clothing clothing = clothingRepository.selectById(id);
        if (clothing == null || !clothing.getUserId().equals(userId)) {
            throw new ResourceNotFoundException("衣物不存在或无权限访问");
        }
        
        clothingRepository.deleteById(id);
        log.info("衣物删除成功: id={}", id);
    }
    
    @Override
    public List<ClothingResponse> getAllClothingByUserId(Long userId) {
        log.info("获取用户所有衣物: userId={}", userId);
        
        List<Clothing> clothingList = clothingRepository.selectByUserId(String.valueOf(userId));
        return clothingList.stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());
    }
    
    @Override
    public PageResponse<ClothingResponse> getClothingByUserIdPaged(Long userId, Integer page, Integer size) {
        log.info("分页获取用户衣物: userId={}, page={}, size={}", userId, page, size);
        
        // 简化实现 - 实际项目中需要真正的分页查询
        List<Clothing> allClothing = clothingRepository.selectByUserId(String.valueOf(userId));
        List<ClothingResponse> responseList = allClothing.stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());
        
        return PageResponse.<ClothingResponse>builder()
                .records(responseList)
                .total((long) responseList.size())
                .pages(1L)
                .current(1L)
                .size((long) responseList.size())
                .hasNext(false)
                .hasPrevious(false)
                .build();
    }
    
    @Override
    public List<ClothingResponse> getClothingByCategory(Long userId, String category) {
        log.info("根据类别获取衣物: userId={}, category={}", userId, category);
        
        List<Clothing> clothingList = clothingRepository.selectByUserIdAndCategory(String.valueOf(userId), category);
        return clothingList.stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<ClothingResponse> getClothingBySeason(Long userId, String season) {
        log.info("根据季节获取衣物: userId={}, season={}", userId, season);
        
        List<Clothing> clothingList = clothingRepository.selectByUserIdAndSeason(String.valueOf(userId), season);
        return clothingList.stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<ClothingResponse> getClothingByOccasion(Long userId, String occasion) {
        log.info("根据场合获取衣物: userId={}, occasion={}", userId, occasion);
        
        List<Clothing> clothingList = clothingRepository.selectByUserIdAndOccasion(String.valueOf(userId), occasion);
        return clothingList.stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());
    }
    
    @Override
    @Transactional
    public ClothingResponse incrementWearCount(Long userId, Long id) {
        log.info("增加穿着次数: userId={}, id={}", userId, id);
        
        Clothing clothing = clothingRepository.selectById(id);
        if (clothing == null || !clothing.getUserId().equals(userId)) {
            throw new ResourceNotFoundException("衣物不存在或无权限访问");
        }
        
        clothing.setWearCount(clothing.getWearCount() + 1);
        clothing.setLastWornAt(LocalDateTime.now());
        clothingRepository.updateById(clothing);
        
        log.info("穿着次数更新成功: id={}, count={}", id, clothing.getWearCount());
        return convertToResponse(clothing);
    }
    
    @Override
    @Transactional
    public ClothingResponse updateClothingImage(Long userId, Long id, String imageUrl) {
        log.info("更新衣物图片: userId={}, id={}, imageUrl={}", userId, id, imageUrl);
        
        Clothing clothing = clothingRepository.selectById(id);
        if (clothing == null || !clothing.getUserId().equals(userId)) {
            throw new ResourceNotFoundException("衣物不存在或无权限访问");
        }
        
        clothing.setMainImageUrl(imageUrl);
        clothingRepository.updateById(clothing);
        
        log.info("衣物图片更新成功: id={}", id);
        return convertToResponse(clothing);
    }
    
    /**
     * 转换为响应DTO
     */
    private ClothingResponse convertToResponse(Clothing clothing) {
        return ClothingResponse.builder()
                .id(String.valueOf(clothing.getId()))
                .userId(String.valueOf(clothing.getUserId()))
                .name(clothing.getName())
                .category(clothing.getCategory())
                .brand(clothing.getBrand())
                .description(clothing.getDescription())
                .occasions(clothing.getOccasions())
                .seasons(clothing.getSeasons())
                .tags(clothing.getTags())
                .mainImageUrl(clothing.getMainImageUrl())
                .wearCount(clothing.getWearCount())
                .createdAt(clothing.getCreatedAt())
                .updatedAt(clothing.getUpdatedAt())
                .build();
    }
} 