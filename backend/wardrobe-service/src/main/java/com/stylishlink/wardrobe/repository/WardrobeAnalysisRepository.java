package com.stylishlink.wardrobe.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.stylishlink.wardrobe.entity.WardrobeAnalysis;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Delete;

import java.time.LocalDate;

/**
 * 衣橱分析Repository
 */
@Mapper
public interface WardrobeAnalysisRepository extends BaseMapper<WardrobeAnalysis> {
    
    /**
     * 根据用户ID查询衣橱分析
     *
     * @param userId 用户ID
     * @return 衣橱分析
     */
    @Select("SELECT * FROM wardrobe_analysis WHERE user_id = #{userId} ORDER BY analysis_date DESC LIMIT 1")
    WardrobeAnalysis selectByUserId(@Param("userId") String userId);
    
    /**
     * 根据用户ID和分析日期查询衣橱分析
     *
     * @param userId 用户ID
     * @param analysisDate 分析日期
     * @return 衣橱分析记录
     */
    @Select("SELECT * FROM wardrobe_analysis WHERE user_id = #{userId} AND analysis_date = #{analysisDate}")
    WardrobeAnalysis selectByUserIdAndAnalysisDate(@Param("userId") String userId, @Param("analysisDate") LocalDate analysisDate);
    
    /**
     * 根据用户ID删除衣橱分析
     *
     * @param userId 用户ID
     */
    @Delete("DELETE FROM wardrobe_analysis WHERE user_id = #{userId}")
    void deleteByUserId(@Param("userId") String userId);
    
    /**
     * 检查用户是否有衣橱分析记录
     *
     * @param userId 用户ID
     * @return 是否存在
     */
    @Select("SELECT COUNT(*) > 0 FROM wardrobe_analysis WHERE user_id = #{userId}")
    boolean existsByUserId(@Param("userId") String userId);
} 