package com.stylishlink.wardrobe.controller;

import com.stylishlink.common.dto.ApiResponse;
import com.stylishlink.common.dto.PageResponse;
import com.stylishlink.wardrobe.dto.request.ClothingRequest;
import com.stylishlink.wardrobe.dto.response.ClothingResponse;
import com.stylishlink.wardrobe.service.ClothingService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 衣物控制器
 */
@Slf4j
@RestController
@RequestMapping("/clothing")
@RequiredArgsConstructor
@Tag(name = "衣物管理", description = "衣物相关的API接口")
public class ClothingController {
    
    private final ClothingService clothingService;
    
    /**
     * 创建衣物
     */
    @PostMapping
    @Operation(summary = "创建衣物", description = "添加新的衣物到衣橱")
    public ApiResponse<ClothingResponse> createClothing(
            @Parameter(description = "用户ID") @RequestParam Long userId,
            @Valid @RequestBody ClothingRequest request) {
        log.info("创建衣物，用户ID: {}, 衣物名称: {}", userId, request.getName());
        ClothingResponse response = clothingService.createClothing(userId, request);
        return ApiResponse.success(response);
    }
    
    /**
     * 更新衣物
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新衣物", description = "修改衣物信息")
    public ApiResponse<ClothingResponse> updateClothing(
            @Parameter(description = "用户ID") @RequestParam Long userId,
            @PathVariable Long id,
            @Valid @RequestBody ClothingRequest request) {
        log.info("更新衣物，用户ID: {}, 衣物ID: {}", userId, id);
        ClothingResponse response = clothingService.updateClothing(userId, id, request);
        return ApiResponse.success(response);
    }
    
    /**
     * 删除衣物
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除衣物", description = "删除指定的衣物")
    public ApiResponse<Void> deleteClothing(
            @Parameter(description = "用户ID") @RequestParam Long userId,
            @PathVariable Long id) {
        log.info("删除衣物，用户ID: {}, 衣物ID: {}", userId, id);
        clothingService.deleteClothing(userId, id);
        return ApiResponse.success();
    }
    
    /**
     * 获取衣物详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "获取衣物详情", description = "根据ID获取衣物详细信息")
    public ApiResponse<ClothingResponse> getClothing(
            @Parameter(description = "用户ID") @RequestParam Long userId,
            @PathVariable Long id) {
        log.info("获取衣物详情，用户ID: {}, 衣物ID: {}", userId, id);
        ClothingResponse response = clothingService.getClothing(userId, id);
        return ApiResponse.success(response);
    }
    
    /**
     * 获取用户的所有衣物
     */
    @GetMapping
    @Operation(summary = "获取衣物列表", description = "获取用户的所有衣物")
    public ApiResponse<List<ClothingResponse>> getAllClothing(
            @Parameter(description = "用户ID") @RequestParam Long userId) {
        log.info("获取用户的所有衣物，用户ID: {}", userId);
        List<ClothingResponse> responseList = clothingService.getAllClothingByUserId(userId);
        return ApiResponse.success(responseList);
    }
    
    /**
     * 分页获取用户的衣物
     */
    @GetMapping("/page")
    @Operation(summary = "分页获取衣物", description = "分页获取用户的衣物列表")
    public ApiResponse<PageResponse<ClothingResponse>> getClothingPaged(
            @Parameter(description = "用户ID") @RequestParam Long userId,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") Integer size) {
        log.info("分页获取用户的衣物，用户ID: {}, 页码: {}, 每页大小: {}", userId, page, size);
        PageResponse<ClothingResponse> pageResponse = clothingService.getClothingByUserIdPaged(userId, page, size);
        return ApiResponse.success(pageResponse);
    }
    
    /**
     * 根据类别获取用户的衣物
     */
    @GetMapping("/category/{category}")
    @Operation(summary = "按类别获取衣物", description = "根据类别获取用户的衣物")
    public ApiResponse<List<ClothingResponse>> getClothingByCategory(
            @Parameter(description = "用户ID") @RequestParam Long userId,
            @PathVariable String category) {
        log.info("根据类别获取用户的衣物，用户ID: {}, 类别: {}", userId, category);
        List<ClothingResponse> responseList = clothingService.getClothingByCategory(userId, category);
        return ApiResponse.success(responseList);
    }
    
    /**
     * 根据季节获取用户的衣物
     */
    @GetMapping("/season/{season}")
    @Operation(summary = "按季节获取衣物", description = "根据季节获取用户的衣物")
    public ApiResponse<List<ClothingResponse>> getClothingBySeason(
            @Parameter(description = "用户ID") @RequestParam Long userId,
            @PathVariable String season) {
        log.info("根据季节获取用户的衣物，用户ID: {}, 季节: {}", userId, season);
        List<ClothingResponse> responseList = clothingService.getClothingBySeason(userId, season);
        return ApiResponse.success(responseList);
    }
    
    /**
     * 根据场合获取用户的衣物
     */
    @GetMapping("/occasion/{occasion}")
    @Operation(summary = "按场合获取衣物", description = "根据场合获取用户的衣物")
    public ApiResponse<List<ClothingResponse>> getClothingByOccasion(
            @Parameter(description = "用户ID") @RequestParam Long userId,
            @PathVariable String occasion) {
        log.info("根据场合获取用户的衣物，用户ID: {}, 场合: {}", userId, occasion);
        List<ClothingResponse> responseList = clothingService.getClothingByOccasion(userId, occasion);
        return ApiResponse.success(responseList);
    }
    
    /**
     * 增加衣物穿着次数
     */
    @PutMapping("/{id}/wear")
    @Operation(summary = "记录穿着", description = "增加衣物穿着次数")
    public ApiResponse<ClothingResponse> incrementWearCount(
            @Parameter(description = "用户ID") @RequestParam Long userId,
            @PathVariable Long id) {
        log.info("增加衣物穿着次数，用户ID: {}, 衣物ID: {}", userId, id);
        ClothingResponse response = clothingService.incrementWearCount(userId, id);
        return ApiResponse.success(response);
    }
    
    /**
     * 更新衣物图片
     */
    @PutMapping("/{id}/image")
    @Operation(summary = "更新衣物图片", description = "更新衣物的主图")
    public ApiResponse<ClothingResponse> updateClothingImage(
            @Parameter(description = "用户ID") @RequestParam Long userId,
            @PathVariable Long id,
            @Parameter(description = "图片URL") @RequestParam String imageUrl) {
        log.info("更新衣物图片，用户ID: {}, 衣物ID: {}", userId, id);
        ClothingResponse response = clothingService.updateClothingImage(userId, id, imageUrl);
        return ApiResponse.success(response);
    }
} 