package com.stylishlink.wardrobe.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 衣橱分析实体类
 * 
 * <AUTHOR>
 * @since 2025-01-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("wardrobe_analysis")
public class WardrobeAnalysis {

    /**
     * 分析ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    @NotBlank(message = "用户ID不能为空")
    private String userId;

    /**
     * 分析日期
     */
    @TableField("analysis_date")
    @NotNull(message = "分析日期不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate analysisDate;

    /**
     * 风格分析 (存储为JSON)
     */
    @TableField(value = "style_analysis", typeHandler = com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler.class)
    @JsonProperty("styleAnalysis")
    private Map<String, Object> styleAnalysis;

    /**
     * 色彩分析 (存储为JSON)
     */
    @TableField(value = "color_analysis", typeHandler = com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler.class)
    @JsonProperty("colorAnalysis")
    private Map<String, Object> colorAnalysis;

    /**
     * 五行平衡 (存储为JSON)
     */
    @TableField(value = "wuxing_balance", typeHandler = com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler.class)
    @JsonProperty("wuxingBalance")
    private Map<String, Object> wuxingBalance;

    /**
     * 穿着频率 (存储为JSON)
     */
    @TableField(value = "wear_frequency", typeHandler = com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler.class)
    @JsonProperty("wearFrequency")
    private Map<String, Object> wearFrequency;

    /**
     * 总物品数
     */
    @TableField("total_items")
    @JsonProperty("totalItems")
    private Integer totalItems = 0;

    /**
     * 衣物数量
     */
    @TableField("clothing_count")
    @JsonProperty("clothingCount")
    private Integer clothingCount = 0;

    /**
     * 饰品数量
     */
    @TableField("accessory_count")
    @JsonProperty("accessoryCount")
    private Integer accessoryCount = 0;

    /**
     * 搭配数量
     */
    @TableField("outfit_count")
    @JsonProperty("outfitCount")
    private Integer outfitCount = 0;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonProperty("createdAt")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonProperty("updatedAt")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;
} 