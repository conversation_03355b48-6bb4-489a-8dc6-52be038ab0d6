package com.stylishlink.wardrobe.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import jakarta.validation.constraints.NotBlank;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 搭配实体类
 * 
 * <AUTHOR>
 * @since 2025-01-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("wardrobe_outfits")
public class Outfit {

    /**
     * 搭配ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    @NotBlank(message = "用户ID不能为空")
    private String userId;

    /**
     * 搭配名称
     */
    @TableField("name")
    @NotBlank(message = "搭配名称不能为空")
    private String name;

    /**
     * 搭配描述
     */
    @TableField("description")
    private String description;

    /**
     * 包含的衣物ID列表 (存储为JSON)
     */
    @TableField(value = "clothing_ids", typeHandler = com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler.class)
    @JsonProperty("clothingIds")
    private List<String> clothingIds;

    /**
     * 包含的饰品ID列表 (存储为JSON)
     */
    @TableField(value = "accessory_ids", typeHandler = com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler.class)
    @JsonProperty("accessoryIds")
    private List<String> accessoryIds;

    /**
     * 适合场合 (存储为JSON)
     */
    @TableField(value = "occasions", typeHandler = com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler.class)
    private List<String> occasions;

    /**
     * 适合季节 (存储为JSON)
     */
    @TableField(value = "seasons", typeHandler = com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler.class)
    private List<String> seasons;

    /**
     * 风格标签 (存储为JSON)
     */
    @TableField(value = "styles", typeHandler = com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler.class)
    private List<String> styles;

    /**
     * 标签 (存储为JSON)
     */
    @TableField(value = "tags", typeHandler = com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler.class)
    private List<String> tags;

    /**
     * 主图URL
     */
    @TableField("main_image_url")
    @JsonProperty("mainImageUrl")
    private String mainImageUrl;

    /**
     * 搭配效果图URL列表 (存储为JSON)
     */
    @TableField(value = "image_urls", typeHandler = com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler.class)
    @JsonProperty("imageUrls")
    private List<String> imageUrls;

    /**
     * 五行属性 - 金
     */
    @TableField("wuxing_metal")
    private Integer wuxingMetal = 0;

    /**
     * 五行属性 - 木
     */
    @TableField("wuxing_wood")
    private Integer wuxingWood = 0;

    /**
     * 五行属性 - 水
     */
    @TableField("wuxing_water")
    private Integer wuxingWater = 0;

    /**
     * 五行属性 - 火
     */
    @TableField("wuxing_fire")
    private Integer wuxingFire = 0;

    /**
     * 五行属性 - 土
     */
    @TableField("wuxing_earth")
    private Integer wuxingEarth = 0;

    /**
     * 是否用户创建
     */
    @TableField("is_user_created")
    @JsonProperty("isUserCreated")
    private Boolean isUserCreated = true;

    /**
     * 穿着次数
     */
    @TableField("wear_count")
    @JsonProperty("wearCount")
    private Integer wearCount = 0;

    /**
     * 最后穿着时间
     */
    @TableField("last_worn_at")
    @JsonProperty("lastWornAt")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastWornAt;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonProperty("createdAt")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonProperty("updatedAt")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    /**
     * 获取五行属性对象
     */
    public WuxingAttributes getWuxing() {
        WuxingAttributes wuxing = new WuxingAttributes();
        wuxing.setMetal(this.wuxingMetal != null ? this.wuxingMetal : 0);
        wuxing.setWood(this.wuxingWood != null ? this.wuxingWood : 0);
        wuxing.setWater(this.wuxingWater != null ? this.wuxingWater : 0);
        wuxing.setFire(this.wuxingFire != null ? this.wuxingFire : 0);
        wuxing.setEarth(this.wuxingEarth != null ? this.wuxingEarth : 0);
        return wuxing;
    }

    /**
     * 设置五行属性
     */
    public void setWuxing(WuxingAttributes wuxing) {
        if (wuxing != null) {
            this.wuxingMetal = wuxing.getMetal();
            this.wuxingWood = wuxing.getWood();
            this.wuxingWater = wuxing.getWater();
            this.wuxingFire = wuxing.getFire();
            this.wuxingEarth = wuxing.getEarth();
        }
    }
} 