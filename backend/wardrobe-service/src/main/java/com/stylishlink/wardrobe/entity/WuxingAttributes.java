package com.stylishlink.wardrobe.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 五行属性实体类
 * 
 * <AUTHOR>
 * @since 2025-01-12
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class WuxingAttributes {

    /**
     * 金属性值 (0-5)
     */
    @Builder.Default
    private Integer metal = 0;

    /**
     * 木属性值 (0-5)
     */
    @Builder.Default
    private Integer wood = 0;

    /**
     * 水属性值 (0-5)
     */
    @Builder.Default
    private Integer water = 0;

    /**
     * 火属性值 (0-5)
     */
    @Builder.Default
    private Integer fire = 0;

    /**
     * 土属性值 (0-5)
     */
    @Builder.Default
    private Integer earth = 0;

    /**
     * 计算总属性值
     */
    public Integer getTotalValue() {
        return metal + wood + water + fire + earth;
    }

    /**
     * 获取主导属性
     */
    public String getDominantElement() {
        int maxValue = Math.max(Math.max(metal, wood), Math.max(Math.max(water, fire), earth));
        
        if (metal == maxValue) return "金";
        if (wood == maxValue) return "木";
        if (water == maxValue) return "水";
        if (fire == maxValue) return "火";
        if (earth == maxValue) return "土";
        
        return "平衡";
    }
} 