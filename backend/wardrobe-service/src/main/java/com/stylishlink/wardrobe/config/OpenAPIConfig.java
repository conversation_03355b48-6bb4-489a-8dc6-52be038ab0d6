package com.stylishlink.wardrobe.config;

import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.servers.Server;
import io.swagger.v3.oas.models.tags.Tag;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * 衣橱服务OpenAPI配置
 */
@Configuration
public class OpenAPIConfig {

    @Value("${server.port:8082}")
    private String serverPort;

    @Bean
    public OpenAPI wardrobeServiceOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("衣橱服务API")
                        .description("StylishLink衣橱服务API文档，提供衣物管理、搭配管理、智能分类等功能")
                        .version("1.0.0")
                        .contact(new Contact()
                                .name("StylishLink团队")
                                .email("<EMAIL>")
                                .url("https://stylishlink.com"))
                        .license(new License()
                                .name("MIT")
                                .url("https://opensource.org/licenses/MIT")))
                .servers(List.of(
                        new Server()
                                .url("http://localhost:" + serverPort)
                                .description("本地开发环境"),
                        new Server()
                                .url("https://api.stylishlink.com")
                                .description("生产环境")
                ))
                .addSecurityItem(new SecurityRequirement()
                        .addList("Bearer Authentication"))
                .components(new Components()
                        .addSecuritySchemes("Bearer Authentication", 
                                new SecurityScheme()
                                        .type(SecurityScheme.Type.HTTP)
                                        .scheme("bearer")
                                        .bearerFormat("JWT")
                                        .description("请在请求头中添加 Authorization: Bearer {token}")))
                .tags(List.of(
                        new Tag()
                                .name("衣物管理")
                                .description("衣物增删改查、分类管理等接口"),
                        new Tag()
                                .name("搭配管理")
                                .description("搭配创建、编辑、推荐等接口"),
                        new Tag()
                                .name("配饰管理")
                                .description("配饰、鞋子、包包等管理接口"),
                        new Tag()
                                .name("智能分析")
                                .description("衣物智能分类、颜色识别等AI功能接口")
                ));
    }
} 