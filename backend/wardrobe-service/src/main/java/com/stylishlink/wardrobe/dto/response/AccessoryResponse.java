package com.stylishlink.wardrobe.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 配饰响应DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AccessoryResponse {
    
    /**
     * ID
     */
    private String id;
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 名称
     */
    private String name;
    
    /**
     * 描述
     */
    private String description;
    
    /**
     * 类型
     */
    private String type;
    
    /**
     * 颜色
     */
    private String color;
    
    /**
     * 品牌
     */
    private String brand;
    
    /**
     * 适合季节列表
     */
    private List<String> seasons;
    
    /**
     * 适合场合列表
     */
    private List<String> occasions;
    
    /**
     * 标签列表
     */
    private List<String> tags;
    
    /**
     * 购买日期
     */
    private String purchaseDate;
    
    /**
     * 购买价格
     */
    private Double price;
    
    /**
     * 图片URL
     */
    private String imageUrl;
    
    /**
     * 喜爱程度 (1-5)
     */
    private Integer favoriteLevel;
    
    /**
     * 是否归档
     */
    private Boolean isArchived;
    
    /**
     * 属性
     */
    private String properties;
    
    /**
     * 材质
     */
    private String material;
    
    /**
     * 使用次数
     */
    private Integer useCount;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
} 