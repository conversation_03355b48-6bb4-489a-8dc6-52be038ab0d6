package com.stylishlink.wardrobe.service;

import com.stylishlink.common.dto.PageResponse;
import com.stylishlink.wardrobe.dto.request.ClothingRequest;
import com.stylishlink.wardrobe.dto.response.ClothingResponse;

import java.util.List;

/**
 * 衣物服务接口
 */
public interface ClothingService {
    
    /**
     * 创建衣物
     * @param userId 用户ID
     * @param request 衣物请求
     * @return 衣物响应
     */
    ClothingResponse createClothing(Long userId, ClothingRequest request);
    
    /**
     * 更新衣物
     * @param userId 用户ID
     * @param id 衣物ID
     * @param request 衣物请求
     * @return 衣物响应
     */
    ClothingResponse updateClothing(Long userId, Long id, ClothingRequest request);
    
    /**
     * 删除衣物
     * @param userId 用户ID
     * @param id 衣物ID
     */
    void deleteClothing(Long userId, Long id);
    
    /**
     * 获取衣物详情
     * @param userId 用户ID
     * @param id 衣物ID
     * @return 衣物响应
     */
    ClothingResponse getClothing(Long userId, Long id);
    
    /**
     * 获取用户的所有衣物
     * @param userId 用户ID
     * @return 衣物响应列表
     */
    List<ClothingResponse> getAllClothingByUserId(Long userId);
    
    /**
     * 分页获取用户的衣物
     * @param userId 用户ID
     * @param page 页码
     * @param size 每页大小
     * @return 分页衣物响应
     */
    PageResponse<ClothingResponse> getClothingByUserIdPaged(Long userId, Integer page, Integer size);
    
    /**
     * 根据类别获取用户的衣物
     * @param userId 用户ID
     * @param category 类别
     * @return 衣物响应列表
     */
    List<ClothingResponse> getClothingByCategory(Long userId, String category);
    
    /**
     * 根据季节获取用户的衣物
     * @param userId 用户ID
     * @param season 季节
     * @return 衣物响应列表
     */
    List<ClothingResponse> getClothingBySeason(Long userId, String season);
    
    /**
     * 根据场合获取用户的衣物
     * @param userId 用户ID
     * @param occasion 场合
     * @return 衣物响应列表
     */
    List<ClothingResponse> getClothingByOccasion(Long userId, String occasion);
    
    /**
     * 增加衣物穿着次数
     * @param userId 用户ID
     * @param id 衣物ID
     * @return 衣物响应
     */
    ClothingResponse incrementWearCount(Long userId, Long id);
    
    /**
     * 更新衣物图片
     * @param userId 用户ID
     * @param id 衣物ID
     * @param imageUrl 图片URL
     * @return 衣物响应
     */
    ClothingResponse updateClothingImage(Long userId, Long id, String imageUrl);
} 