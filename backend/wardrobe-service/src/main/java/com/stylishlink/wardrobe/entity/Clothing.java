package com.stylishlink.wardrobe.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.stylishlink.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 衣物实体类
 * 
 * <AUTHOR>
 * @since 2025-01-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("wardrobe_clothing")
public class Clothing extends BaseEntity {

    /**
     * 用户ID
     */
    @TableField("user_id")
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    /**
     * 衣物名称
     */
    @TableField("name")
    @NotBlank(message = "衣物名称不能为空")
    private String name;

    /**
     * 衣物类别
     */
    @TableField("category")
    @NotBlank(message = "衣物类别不能为空")
    private String category;

    /**
     * 衣物子类别
     */
    @TableField("sub_category")
    @JsonProperty("subCategory")
    private String subCategory;

    /**
     * 品牌
     */
    @TableField("brand")
    private String brand;

    /**
     * 描述
     */
    @TableField("description")
    private String description;

    /**
     * 颜色列表 (存储为JSON)
     */
    @TableField(value = "colors", typeHandler = com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler.class)
    private List<String> colors;

    /**
     * 材质列表 (存储为JSON)
     */
    @TableField(value = "materials", typeHandler = com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler.class)
    private List<String> materials;

    /**
     * 风格标签 (存储为JSON)
     */
    @TableField(value = "styles", typeHandler = com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler.class)
    private List<String> styles;

    /**
     * 适合场合 (存储为JSON)
     */
    @TableField(value = "occasions", typeHandler = com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler.class)
    private List<String> occasions;

    /**
     * 适合季节 (存储为JSON)
     */
    @TableField(value = "seasons", typeHandler = com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler.class)
    private List<String> seasons;

    /**
     * 标签 (存储为JSON)
     */
    @TableField(value = "tags", typeHandler = com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler.class)
    private List<String> tags;

    /**
     * 主图URL
     */
    @TableField("main_image_url")
    @JsonProperty("mainImageUrl")
    private String mainImageUrl;

    /**
     * 其他图片URL列表 (存储为JSON)
     */
    @TableField(value = "image_urls", typeHandler = com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler.class)
    @JsonProperty("imageUrls")
    private List<String> imageUrls;

    /**
     * 五行属性 - 金
     */
    @TableField("wuxing_metal")
    private Integer wuxingMetal = 0;

    /**
     * 五行属性 - 木
     */
    @TableField("wuxing_wood")
    private Integer wuxingWood = 0;

    /**
     * 五行属性 - 水
     */
    @TableField("wuxing_water")
    private Integer wuxingWater = 0;

    /**
     * 五行属性 - 火
     */
    @TableField("wuxing_fire")
    private Integer wuxingFire = 0;

    /**
     * 五行属性 - 土
     */
    @TableField("wuxing_earth")
    private Integer wuxingEarth = 0;

    /**
     * 购买日期
     */
    @TableField("purchase_date")
    @JsonProperty("purchaseDate")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate purchaseDate;

    /**
     * 价格
     */
    @TableField("price")
    private BigDecimal price;

    /**
     * 穿着次数
     */
    @TableField("wear_count")
    @JsonProperty("wearCount")
    private Integer wearCount = 0;

    /**
     * 最后穿着时间
     */
    @TableField("last_worn_at")
    @JsonProperty("lastWornAt")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastWornAt;

    /**
     * 获取五行属性对象
     * @return 五行属性
     */
    public WuxingAttributes getWuxing() {
        return WuxingAttributes.builder()
                .metal(this.wuxingMetal)
                .wood(this.wuxingWood)
                .water(this.wuxingWater)
                .fire(this.wuxingFire)
                .earth(this.wuxingEarth)
                .build();
    }

    /**
     * 设置五行属性
     * @param wuxing 五行属性
     */
    public void setWuxing(WuxingAttributes wuxing) {
        if (wuxing != null) {
            this.wuxingMetal = wuxing.getMetal();
            this.wuxingWood = wuxing.getWood();
            this.wuxingWater = wuxing.getWater();
            this.wuxingFire = wuxing.getFire();
            this.wuxingEarth = wuxing.getEarth();
        }
    }
} 