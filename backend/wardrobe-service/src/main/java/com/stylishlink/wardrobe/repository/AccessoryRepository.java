package com.stylishlink.wardrobe.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.stylishlink.wardrobe.entity.Accessory;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 配饰仓库接口
 */
@Mapper
public interface AccessoryRepository extends BaseMapper<Accessory> {
    
    /**
     * 根据用户ID查询配饰列表
     * @param userId 用户ID
     * @return 配饰列表
     */
    @Select("SELECT * FROM accessories WHERE user_id = #{userId}")
    List<Accessory> selectByUserId(@Param("userId") String userId);
    
    /**
     * 根据用户ID分页查询配饰
     * @param page 分页参数
     * @param userId 用户ID
     * @return 分页配饰列表
     */
    @Select("SELECT * FROM accessories WHERE user_id = #{userId}")
    Page<Accessory> selectByUserId(Page<Accessory> page, @Param("userId") String userId);
    
    /**
     * 根据用户ID和类型查询配饰
     * @param userId 用户ID
     * @param type 类型
     * @return 配饰列表
     */
    @Select("SELECT * FROM accessories WHERE user_id = #{userId} AND type = #{type}")
    List<Accessory> selectByUserIdAndType(@Param("userId") String userId, @Param("type") String type);
    
    /**
     * 根据用户ID和类型分页查询配饰
     * @param page 分页参数
     * @param userId 用户ID
     * @param type 类型
     * @return 分页配饰列表
     */
    @Select("SELECT * FROM accessories WHERE user_id = #{userId} AND type = #{type}")
    Page<Accessory> selectByUserIdAndType(Page<Accessory> page, @Param("userId") String userId, @Param("type") String type);
    
    /**
     * 根据用户ID和季节查询配饰
     * @param userId 用户ID
     * @param season 季节
     * @return 配饰列表
     */
    @Select("SELECT * FROM accessories WHERE user_id = #{userId} AND JSON_CONTAINS(seasons, JSON_QUOTE(#{season}))")
    List<Accessory> selectByUserIdAndSeason(@Param("userId") String userId, @Param("season") String season);
    
    /**
     * 根据用户ID和颜色查询配饰
     * @param userId 用户ID
     * @param color 颜色
     * @return 配饰列表
     */
    @Select("SELECT * FROM accessories WHERE user_id = #{userId} AND JSON_CONTAINS(colors, JSON_QUOTE(#{color}))")
    List<Accessory> selectByUserIdAndColor(@Param("userId") String userId, @Param("color") String color);
    
    /**
     * 根据用户ID和场合查询配饰
     * @param userId 用户ID
     * @param occasion 场合
     * @return 配饰列表
     */
    @Select("SELECT * FROM accessories WHERE user_id = #{userId} AND JSON_CONTAINS(occasions, JSON_QUOTE(#{occasion}))")
    List<Accessory> selectByUserIdAndOccasion(@Param("userId") String userId, @Param("occasion") String occasion);
    
    /**
     * 根据用户ID和标签查询配饰
     * @param userId 用户ID
     * @param tag 标签
     * @return 配饰列表
     */
    @Select("SELECT * FROM accessories WHERE user_id = #{userId} AND JSON_CONTAINS(tags, JSON_QUOTE(#{tag}))")
    List<Accessory> selectByUserIdAndTag(@Param("userId") String userId, @Param("tag") String tag);
    
    /**
     * 根据用户ID和多个类型查询配饰
     * @param userId 用户ID
     * @param types 类型列表
     * @return 配饰列表
     */
    @Select("<script>" +
            "SELECT * FROM accessories WHERE user_id = #{userId} AND type IN " +
            "<foreach collection='types' item='type' open='(' separator=',' close=')'>" +
            "#{type}" +
            "</foreach>" +
            "</script>")
    List<Accessory> selectByUserIdAndTypeIn(@Param("userId") String userId, @Param("types") List<String> types);
    
    /**
     * 检查配饰是否属于特定用户
     * @param id 配饰ID
     * @param userId 用户ID
     * @return 是否属于该用户
     */
    @Select("SELECT COUNT(*) > 0 FROM accessories WHERE id = #{id} AND user_id = #{userId}")
    boolean existsByIdAndUserId(@Param("id") String id, @Param("userId") String userId);
    
    /**
     * 根据ID和用户ID查询配饰
     * @param id 配饰ID
     * @param userId 用户ID
     * @return 配饰
     */
    @Select("SELECT * FROM accessories WHERE id = #{id} AND user_id = #{userId}")
    Accessory selectByIdAndUserId(@Param("id") String id, @Param("userId") String userId);
    
    /**
     * 根据用户ID统计配饰数量
     * @param userId 用户ID
     * @return 配饰数量
     */
    @Select("SELECT COUNT(*) FROM accessories WHERE user_id = #{userId}")
    int countByUserId(@Param("userId") String userId);
    
    /**
     * 根据用户ID和类型统计配饰数量
     * @param userId 用户ID
     * @param type 类型
     * @return 配饰数量
     */
    @Select("SELECT COUNT(*) FROM accessories WHERE user_id = #{userId} AND type = #{type}")
    int countByUserIdAndType(@Param("userId") String userId, @Param("type") String type);
} 