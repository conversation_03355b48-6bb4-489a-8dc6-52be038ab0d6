package com.stylishlink.wardrobe.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 衣橱分析响应DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WardrobeAnalysisResponse {
    
    /**
     * 风格分析
     */
    private Map<String, Object> styleAnalysis;
    
    /**
     * 色彩分析
     */
    private Map<String, Object> colorAnalysis;
    
    /**
     * 五行平衡分析
     */
    private Map<String, Object> wuxingBalance;
    
    /**
     * 穿着频率分析
     */
    private Map<String, Object> wearFrequency;
    
    /**
     * 总体分析摘要
     */
    private String summary;
    
    /**
     * 建议
     */
    private Map<String, Object> recommendations;
    
    /**
     * 分析时间
     */
    private LocalDateTime analysisTime;
    
    /**
     * 数据版本
     */
    private String version;
} 