package com.stylishlink.wardrobe;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;


/**
 * 衣橱服务应用启动类
 */
@SpringBootApplication(scanBasePackages = {"com.stylishlink.wardrobe", "com.stylishlink.common"})
@EnableDiscoveryClient
@EnableFeignClients
public class WardrobeServiceApplication {
    
    public static void main(String[] args) {
        SpringApplication.run(WardrobeServiceApplication.class, args);
    }
} 