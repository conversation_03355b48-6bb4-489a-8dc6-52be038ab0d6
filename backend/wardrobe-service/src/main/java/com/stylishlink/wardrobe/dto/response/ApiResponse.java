package com.stylishlink.wardrobe.dto.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * API响应DTO
 * @param <T> 数据类型
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ApiResponse<T> {
    
    /**
     * 结果状态，SUCCESS或ERROR
     */
    private String result;
    
    /**
     * 响应消息
     */
    private String message;
    
    /**
     * 响应数据
     */
    private T data;
    
    /**
     * 创建成功响应
     * @param message 成功消息
     * @param data 响应数据
     * @param <T> 数据类型
     * @return 成功响应
     */
    public static <T> ApiResponse<T> success(String message, T data) {
        ApiResponse<T> response = new ApiResponse<>();
        response.setResult("SUCCESS");
        response.setMessage(message);
        response.setData(data);
        return response;
    }
    
    /**
     * 创建成功响应（无数据）
     * @param message 成功消息
     * @return 成功响应
     */
    public static <T> ApiResponse<T> success(String message) {
        ApiResponse<T> response = new ApiResponse<>();
        response.setResult("SUCCESS");
        response.setMessage(message);
        response.setData(null);
        return response;
    }
    
    /**
     * 创建错误响应
     * @param message 错误消息
     * @param <T> 数据类型
     * @return 错误响应
     */
    public static <T> ApiResponse<T> error(String message) {
        ApiResponse<T> response = new ApiResponse<>();
        response.setResult("ERROR");
        response.setMessage(message);
        response.setData(null);
        return response;
    }
    
    /**
     * 创建错误响应
     * @param message 错误消息
     * @param data 响应数据
     * @param <T> 数据类型
     * @return 错误响应
     */
    public static <T> ApiResponse<T> error(String message, T data) {
        ApiResponse<T> response = new ApiResponse<>();
        response.setResult("ERROR");
        response.setMessage(message);
        response.setData(data);
        return response;
    }
} 