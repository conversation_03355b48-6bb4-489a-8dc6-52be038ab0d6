package com.stylishlink.wardrobe.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 搭配响应DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OutfitResponse {
    
    /**
     * ID
     */
    private String id;
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 名称
     */
    private String name;
    
    /**
     * 描述
     */
    private String description;
    
    /**
     * 衣物ID列表
     */
    private List<String> clothingIds;
    
    /**
     * 配饰ID列表
     */
    private List<String> accessoryIds;
    
    /**
     * 衣物详情列表
     */
    private List<ClothingResponse> clothingList;
    
    /**
     * 配饰详情列表
     */
    private List<AccessoryResponse> accessoryList;
    
    /**
     * 适合季节列表
     */
    private List<String> seasons;
    
    /**
     * 适合场合列表
     */
    private List<String> occasions;
    
    /**
     * 标签列表
     */
    private List<String> tags;
    
    /**
     * 图片URL
     */
    private String imageUrl;
    
    /**
     * 搭配风格
     */
    private String style;
    
    /**
     * 喜爱程度 (1-5)
     */
    private Integer favoriteLevel;
    
    /**
     * 是否收藏
     */
    private Boolean isFavorite;
    
    /**
     * 是否AI生成
     */
    private Boolean isAiGenerated;
    
    /**
     * 穿着次数
     */
    private Integer wearCount;
    
    /**
     * 搭配备注
     */
    private String notes;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
} 