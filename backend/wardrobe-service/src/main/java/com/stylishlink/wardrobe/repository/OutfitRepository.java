package com.stylishlink.wardrobe.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.stylishlink.wardrobe.entity.Outfit;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 搭配仓库接口
 */
@Mapper
public interface OutfitRepository extends BaseMapper<Outfit> {
    
    /**
     * 根据用户ID查询搭配列表
     * @param userId 用户ID
     * @return 搭配列表
     */
    @Select("SELECT * FROM outfits WHERE user_id = #{userId}")
    List<Outfit> selectByUserId(@Param("userId") String userId);
    
    /**
     * 根据用户ID分页查询搭配
     * @param page 分页参数
     * @param userId 用户ID
     * @return 分页搭配列表
     */
    @Select("SELECT * FROM outfits WHERE user_id = #{userId}")
    Page<Outfit> selectByUserId(Page<Outfit> page, @Param("userId") String userId);
    
    /**
     * 根据用户ID和名称查询搭配
     * @param userId 用户ID
     * @param name 名称
     * @return 搭配列表
     */
    @Select("SELECT * FROM outfits WHERE user_id = #{userId} AND name LIKE CONCAT('%', #{name}, '%')")
    List<Outfit> selectByUserIdAndNameContaining(@Param("userId") String userId, @Param("name") String name);
    
    /**
     * 根据用户ID和季节查询搭配
     * @param userId 用户ID
     * @param season 季节
     * @return 搭配列表
     */
    @Select("SELECT * FROM outfits WHERE user_id = #{userId} AND JSON_CONTAINS(seasons, JSON_QUOTE(#{season}))")
    List<Outfit> selectByUserIdAndSeason(@Param("userId") String userId, @Param("season") String season);
    
    /**
     * 根据用户ID和场合查询搭配
     * @param userId 用户ID
     * @param occasion 场合
     * @return 搭配列表
     */
    @Select("SELECT * FROM outfits WHERE user_id = #{userId} AND JSON_CONTAINS(occasions, JSON_QUOTE(#{occasion}))")
    List<Outfit> selectByUserIdAndOccasion(@Param("userId") String userId, @Param("occasion") String occasion);
    
    /**
     * 根据用户ID和场合分页查询搭配
     * @param page 分页参数
     * @param userId 用户ID
     * @param occasion 场合
     * @return 分页搭配列表
     */
    @Select("SELECT * FROM outfits WHERE user_id = #{userId} AND JSON_CONTAINS(occasions, JSON_QUOTE(#{occasion}))")
    Page<Outfit> selectByUserIdAndOccasion(Page<Outfit> page, @Param("userId") String userId, @Param("occasion") String occasion);
    
    /**
     * 根据用户ID和季节分页查询搭配
     * @param page 分页参数
     * @param userId 用户ID
     * @param season 季节
     * @return 分页搭配列表
     */
    @Select("SELECT * FROM outfits WHERE user_id = #{userId} AND JSON_CONTAINS(seasons, JSON_QUOTE(#{season}))")
    Page<Outfit> selectByUserIdAndSeason(Page<Outfit> page, @Param("userId") String userId, @Param("season") String season);
    
    /**
     * 根据用户ID和风格分页查询搭配
     * @param page 分页参数
     * @param userId 用户ID
     * @param style 风格
     * @return 分页搭配列表
     */
    @Select("SELECT * FROM outfits WHERE user_id = #{userId} AND JSON_CONTAINS(styles, JSON_QUOTE(#{style}))")
    Page<Outfit> selectByUserIdAndStyle(Page<Outfit> page, @Param("userId") String userId, @Param("style") String style);
    
    /**
     * 根据ID和用户ID查询搭配
     * @param id 搭配ID
     * @param userId 用户ID
     * @return 搭配
     */
    @Select("SELECT * FROM outfits WHERE id = #{id} AND user_id = #{userId}")
    Outfit selectByIdAndUserId(@Param("id") String id, @Param("userId") String userId);
    
    /**
     * 根据用户ID和标签查询搭配
     * @param userId 用户ID
     * @param tag 标签
     * @return 搭配列表
     */
    @Select("SELECT * FROM outfits WHERE user_id = #{userId} AND JSON_CONTAINS(styles, JSON_QUOTE(#{tag}))")
    List<Outfit> selectByUserIdAndTag(@Param("userId") String userId, @Param("tag") String tag);
    
    /**
     * 检查搭配是否属于特定用户
     * @param id 搭配ID
     * @param userId 用户ID
     * @return 是否属于该用户
     */
    @Select("SELECT COUNT(*) > 0 FROM outfits WHERE id = #{id} AND user_id = #{userId}")
    boolean existsByIdAndUserId(@Param("id") String id, @Param("userId") String userId);
    
    /**
     * 根据用户ID和是否用户创建查询搭配
     * @param userId 用户ID
     * @param isUserCreated 是否用户创建
     * @return 搭配列表
     */
    @Select("SELECT * FROM outfits WHERE user_id = #{userId} AND is_user_created = #{isUserCreated}")
    List<Outfit> selectByUserIdAndIsUserCreated(@Param("userId") String userId, @Param("isUserCreated") Boolean isUserCreated);
    
    /**
     * 根据用户ID和衣物ID查询包含该衣物的搭配
     * @param userId 用户ID
     * @param clothingId 衣物ID
     * @return 搭配列表
     */
    @Select("SELECT * FROM outfits WHERE user_id = #{userId} AND JSON_CONTAINS(clothing_ids, JSON_QUOTE(#{clothingId}))")
    List<Outfit> selectByUserIdAndClothingId(@Param("userId") String userId, @Param("clothingId") String clothingId);
    
    /**
     * 根据用户ID和配饰ID查询包含该配饰的搭配
     * @param userId 用户ID
     * @param accessoryId 配饰ID
     * @return 搭配列表
     */
    @Select("SELECT * FROM outfits WHERE user_id = #{userId} AND JSON_CONTAINS(accessory_ids, JSON_QUOTE(#{accessoryId}))")
    List<Outfit> selectByUserIdAndAccessoryId(@Param("userId") String userId, @Param("accessoryId") String accessoryId);
    
    /**
     * 根据用户ID、场合和季节查询搭配
     * @param userId 用户ID
     * @param occasion 场合
     * @param season 季节
     * @return 搭配列表
     */
    @Select("SELECT * FROM outfits WHERE user_id = #{userId} AND JSON_CONTAINS(occasions, JSON_QUOTE(#{occasion})) AND JSON_CONTAINS(seasons, JSON_QUOTE(#{season}))")
    List<Outfit> selectByUserIdAndOccasionAndSeason(@Param("userId") String userId, @Param("occasion") String occasion, @Param("season") String season);
    
    /**
     * 根据用户ID查询搭配，按创建时间降序排序
     * @param page 分页参数
     * @param userId 用户ID
     * @return 分页搭配列表
     */
    @Select("SELECT * FROM outfits WHERE user_id = #{userId} ORDER BY created_at DESC")
    Page<Outfit> selectByUserIdOrderByCreatedAtDesc(Page<Outfit> page, @Param("userId") String userId);
    
    /**
     * 根据用户ID统计搭配数量
     * @param userId 用户ID
     * @return 搭配数量
     */
    @Select("SELECT COUNT(*) FROM outfits WHERE user_id = #{userId}")
    int countByUserId(@Param("userId") String userId);
    
    /**
     * 根据用户ID和季节统计搭配数量
     * @param userId 用户ID
     * @param season 季节
     * @return 搭配数量
     */
    @Select("SELECT COUNT(*) FROM outfits WHERE user_id = #{userId} AND JSON_CONTAINS(seasons, JSON_QUOTE(#{season}))")
    int countByUserIdAndSeason(@Param("userId") String userId, @Param("season") String season);
    
    /**
     * 根据用户ID和场合统计搭配数量
     * @param userId 用户ID
     * @param occasion 场合
     * @return 搭配数量
     */
    @Select("SELECT COUNT(*) FROM outfits WHERE user_id = #{userId} AND JSON_CONTAINS(occasions, JSON_QUOTE(#{occasion}))")
    int countByUserIdAndOccasion(@Param("userId") String userId, @Param("occasion") String occasion);
} 