package com.stylishlink.wardrobe.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.stylishlink.wardrobe.entity.WearRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Delete;

import java.time.LocalDate;
import java.util.List;

/**
 * 穿着记录Repository
 */
@Mapper
public interface WearRecordRepository extends BaseMapper<WearRecord> {
    
    /**
     * 根据用户ID分页查询穿着记录
     *
     * @param page 分页参数
     * @param userId 用户ID
     * @return 穿着记录分页结果
     */
    @Select("SELECT * FROM wear_records WHERE user_id = #{userId} ORDER BY wear_date DESC")
    Page<WearRecord> selectByUserIdOrderByDateDesc(Page<WearRecord> page, @Param("userId") String userId);
    
    /**
     * 根据用户ID和日期范围查询穿着记录
     *
     * @param page 分页参数
     * @param userId 用户ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 穿着记录分页结果
     */
    @Select("SELECT * FROM wear_records WHERE user_id = #{userId} AND wear_date BETWEEN #{startDate} AND #{endDate} ORDER BY wear_date DESC")
    Page<WearRecord> selectByUserIdAndDateBetweenOrderByDateDesc(
        Page<WearRecord> page, 
        @Param("userId") String userId, 
        @Param("startDate") LocalDate startDate, 
        @Param("endDate") LocalDate endDate);
    
    /**
     * 根据用户ID和物品ID查询穿着记录
     *
     * @param userId 用户ID
     * @param itemId 物品ID
     * @return 穿着记录列表
     */
    @Select("SELECT * FROM wear_records WHERE user_id = #{userId} AND item_id = #{itemId}")
    List<WearRecord> selectByUserIdAndItemId(@Param("userId") String userId, @Param("itemId") String itemId);
    
    /**
     * 根据用户ID和物品ID统计穿着次数
     *
     * @param userId 用户ID
     * @param itemId 物品ID
     * @return 穿着次数
     */
    @Select("SELECT COUNT(*) FROM wear_records WHERE user_id = #{userId} AND item_id = #{itemId}")
    int countByUserIdAndItemId(@Param("userId") String userId, @Param("itemId") String itemId);
    
    /**
     * 根据用户ID和物品类型查询穿着记录
     *
     * @param userId 用户ID
     * @param itemType 物品类型
     * @return 穿着记录列表
     */
    @Select("SELECT * FROM wear_records WHERE user_id = #{userId} AND item_type = #{itemType}")
    List<WearRecord> selectByUserIdAndItemType(@Param("userId") String userId, @Param("itemType") String itemType);
    
    /**
     * 根据用户ID查询最近的穿着记录
     *
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 最近的穿着记录
     */
    @Select("SELECT * FROM wear_records WHERE user_id = #{userId} ORDER BY wear_date DESC LIMIT #{limit}")
    List<WearRecord> selectRecentWearRecords(@Param("userId") String userId, @Param("limit") int limit);
    
    /**
     * 根据用户ID和日期查询穿着记录
     *
     * @param userId 用户ID
     * @param wearDate 穿着日期
     * @return 穿着记录列表
     */
    @Select("SELECT * FROM wear_records WHERE user_id = #{userId} AND wear_date = #{wearDate}")
    List<WearRecord> selectByUserIdAndWearDate(@Param("userId") String userId, @Param("wearDate") LocalDate wearDate);
    
    /**
     * 删除用户的穿着记录
     *
     * @param userId 用户ID
     */
    @Delete("DELETE FROM wear_records WHERE user_id = #{userId}")
    void deleteByUserId(@Param("userId") String userId);
    
    /**
     * 删除指定物品的穿着记录
     *
     * @param itemId 物品ID
     */
    @Delete("DELETE FROM wear_records WHERE item_id = #{itemId}")
    void deleteByItemId(@Param("itemId") String itemId);
} 