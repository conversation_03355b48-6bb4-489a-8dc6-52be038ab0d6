package com.stylishlink.wardrobe.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 穿着记录实体类
 * 
 * <AUTHOR>
 * @since 2025-01-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("wardrobe_wear_records")
public class WearRecord {

    /**
     * 记录ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    @NotBlank(message = "用户ID不能为空")
    private String userId;

    /**
     * 项目ID(衣物或搭配)
     */
    @TableField("item_id")
    @NotBlank(message = "项目ID不能为空")
    private String itemId;

    /**
     * 项目类型(clothing/accessory/outfit)
     */
    @TableField("item_type")
    @NotBlank(message = "项目类型不能为空")
    private String itemType;

    /**
     * 穿着日期
     */
    @TableField("wear_date")
    @NotNull(message = "穿着日期不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate wearDate;

    /**
     * 场合
     */
    @TableField("occasion")
    private String occasion;

    /**
     * 天气
     */
    @TableField("weather")
    private String weather;

    /**
     * 备注
     */
    @TableField("note")
    private String note;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonProperty("createdAt")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;
} 