# 推荐服务 (Recommendation Service)

## 概述

推荐服务是StylishLink时尚搭配助手的核心微服务，负责提供智能穿搭推荐、五行命理分析、天气数据处理等功能。

## 技术栈

- **框架**: Spring Boot 2.7.x
- **服务发现**: Nacos
- **数据库**: MySQL 8.0+
- **ORM**: MyBatis Plus
- **缓存**: Redis
- **API文档**: Swagger 3.0
- **构建工具**: Maven

## 数据库迁移说明

### 迁移原因

原设计使用MongoDB作为数据存储，现根据项目需求统一迁移至MySQL + MyBatis Plus方案。

### 迁移内容

1. **依赖更新**
   - 移除 `spring-boot-starter-data-mongodb`
   - 添加 `mysql-connector-java`
   - 添加 `mybatis-plus-boot-starter`

2. **实体类改造**
   - 移除 `@Document` 注解，添加 `@TableName`
   - 实体类继承 `BaseEntity`
   - 使用 `@TableField` 配置字段映射
   - 复杂字段使用 `JacksonTypeHandler` 处理JSON

3. **Repository层重构**
   - 接口继承 `BaseMapper<T>`
   - 使用 `@Mapper` 注解
   - 自定义查询方法使用 `@Select/@Update/@Insert/@Delete`

4. **服务层适配**
   - 更新数据库操作方法名
   - 适配MyBatis Plus的API调用方式

## 数据库表结构

### 核心表

1. **recommendation_daily_recommendations** - 每日推荐表
2. **recommendation_histories** - 推荐历史表
3. **recommendation_user_preferences** - 用户偏好表
4. **recommendation_feedbacks** - 推荐反馈表
5. **recommendation_algorithm_configs** - 算法配置表
6. **recommendation_weather_cache** - 天气数据缓存表

### 表设计特点

- 遵循微服务命名规范：`recommendation_{table_name}`
- 统一使用 `BIGINT AUTO_INCREMENT` 作为主键
- 包含完整的审计字段：`created_by`, `created_at`, `updated_by`, `updated_at`, `status`
- 复杂数据使用JSON字段存储，便于扩展
- 合理设置索引优化查询性能

## 部署说明

### 1. 数据库初始化

```bash
# 执行建表脚本
mysql -u root -p < src/main/resources/db/mysql-init.sql
```

### 2. 配置文件

在Nacos中配置以下内容（`recommendation-service.yaml`）：

```yaml
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ********************************************************************************************************
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:password}
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000

  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:}
    database: 0
    timeout: 10000
    lettuce:
      pool:
        max-active: 20
        max-idle: 10
        min-idle: 5
        max-wait: 10000

mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: true
    lazy-loading-enabled: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: auto
      logic-delete-value: -1
      logic-not-delete-value: 1
  mapper-locations: classpath*:mapper/**/*.xml
  type-aliases-package: com.stylishlink.recommendation.entity

logging:
  level:
    com.stylishlink.recommendation: DEBUG
    org.springframework.cache: DEBUG
    com.baomidou.mybatisplus: DEBUG

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
```

### 3. 启动服务

```bash
# 打包
mvn clean package -DskipTests

# 启动
java -jar target/stylishlink-recommendation-service-1.0.0-SNAPSHOT.jar
```

## API接口

### 主要端点

- `POST /api/recommendation/recommend/daily` - 获取每日推荐
- `POST /api/recommendation/recommend/feedback` - 提交反馈
- `GET /api/recommendation/recommend/history` - 获取推荐历史
- `GET /api/recommendation/recommend/stats` - 获取推荐统计
- `POST /api/recommendation/recommend/wuxing` - 五行命理分析
- `GET /api/recommendation/recommend/weather` - 获取天气信息

### 错误码

| 错误码 | 描述 |
|--------|------|
| 4001 | 推荐不存在 |
| 4002 | 推荐生成失败 |
| 4003 | 用户偏好异常 |
| 4004 | 天气数据异常 |
| 4005 | 场景不支持 |
| 4006 | 五行分析失败 |
| 4007 | 反馈提交失败 |
| 4008 | 推荐过期 |

## 开发说明

### 已实现功能

1. ✅ 每日推荐基础功能
2. ✅ 推荐反馈系统
3. ✅ 数据库表结构设计
4. ✅ 基础CRUD操作
5. ✅ 错误处理机制

### 待实现功能

1. ⏳ 推荐算法逻辑（五行分析、天气匹配、个性化推荐）
2. ⏳ 场合推荐、购物推荐、旅行推荐
3. ⏳ 推荐历史和统计功能
4. ⏳ 天气服务集成
5. ⏳ 缓存策略优化
6. ⏳ 异步处理机制

### 开发注意事项

1. 所有复杂对象使用JSON存储，通过 `JacksonTypeHandler` 自动序列化
2. 遵循BaseEntity的审计字段规范
3. 使用MyBatis Plus的条件构造器进行复杂查询
4. 合理使用缓存避免重复计算
5. 错误处理统一使用推荐服务专用错误码

## 性能优化

### 数据库优化

1. 针对常用查询字段建立索引
2. 使用JSON字段存储复杂数据，避免多表关联
3. 定期清理过期数据（天气缓存、历史记录等）
4. 读写分离（如需要）

### 缓存策略

1. 推荐结果缓存（Redis）
2. 用户偏好缓存
3. 天气数据缓存
4. 算法配置缓存

### 监控指标

1. 推荐生成耗时
2. 数据库连接池使用率
3. 缓存命中率
4. 错误率统计

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查数据库配置
   - 确认网络连通性
   - 验证用户权限

2. **JSON字段解析错误**
   - 检查数据格式
   - 确认TypeHandler配置
   - 验证字段映射

3. **缓存异常**
   - 检查Redis连接
   - 验证序列化配置
   - 确认缓存键命名

### 日志分析

- 应用日志：`logs/recommendation-service.log`
- 错误日志：`logs/error.log`
- 数据库日志：检查MySQL慢查询日志
- 缓存日志：Redis监控

## 版本历史

- **v1.0.0** - 初始版本，基础功能实现
- **v1.1.0** - 数据库迁移至MySQL + MyBatis Plus
- **v1.2.0** - 推荐算法优化（规划中）
- **v1.3.0** - 性能优化和扩展功能（规划中） 