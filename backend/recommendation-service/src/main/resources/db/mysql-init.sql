-- 推荐服务数据库初始化脚本
-- 创建recommendation数据库
CREATE DATABASE IF NOT EXISTS `recommendation` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE `recommendation`;

-- ====================================
-- 1. 每日推荐表
-- ====================================
CREATE TABLE `recommendation_daily_recommendations` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `user_id` BIGINT NOT NULL COMMENT '用户ID',
    `date` DATE NOT NULL COMMENT '推荐日期',
    `outfits` JSON COMMENT '推荐搭配列表（JSON格式）',
    `reason` TEXT COMMENT '推荐理由',
    `weather_info` JSON COMMENT '天气信息（JSON格式）',
    `wuxing_analysis` JSON COMMENT '五行分析（JSON格式）',
    `energy_advice` JSON COMMENT '能量建议列表（JSON格式）',
    `occasions` JSON COMMENT '适合场合列表（JSON格式）',
    `total_score` DECIMAL(3,2) DEFAULT 0.00 COMMENT '总评分（0-5）',
    `multi_score` JSON COMMENT '多维度评分（JSON格式）',
    `is_viewed` TINYINT(1) DEFAULT 0 COMMENT '是否已查看（0：未查看，1：已查看）',
    `feedback` JSON COMMENT '用户反馈（JSON格式）',
    `status` TINYINT DEFAULT 1 COMMENT '状态（1：正常，0：禁用，-1：删除）',
    `created_by` BIGINT NOT NULL COMMENT '创建人ID',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_by` BIGINT NOT NULL COMMENT '更新人ID', 
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_date` (`user_id`, `date`),
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_date` (`date`),
    INDEX `idx_status` (`status`),
    INDEX `idx_created_at` (`created_at`),
    INDEX `idx_is_viewed` (`is_viewed`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='每日推荐表';

-- ====================================
-- 2. 推荐历史表
-- ====================================
CREATE TABLE `recommendation_histories` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `user_id` BIGINT NOT NULL COMMENT '用户ID',
    `recommendation_type` VARCHAR(50) NOT NULL COMMENT '推荐类型（daily/occasion/shopping/travel）',
    `recommendation_id` VARCHAR(100) NOT NULL COMMENT '推荐记录ID',
    `date` DATE NOT NULL COMMENT '推荐日期',
    `interaction_count` INT DEFAULT 0 COMMENT '交互次数',
    `view_count` INT DEFAULT 0 COMMENT '查看次数',
    `like_count` INT DEFAULT 0 COMMENT '点赞次数',
    `share_count` INT DEFAULT 0 COMMENT '分享次数',
    `feedback_rating` DECIMAL(3,2) DEFAULT NULL COMMENT '反馈评分（0-5）',
    `feedback_tags` JSON COMMENT '反馈标签统计（JSON格式）',
    `last_interaction_at` TIMESTAMP NULL COMMENT '最后交互时间',
    `status` TINYINT DEFAULT 1 COMMENT '状态（1：正常，0：禁用，-1：删除）',
    `created_by` BIGINT NOT NULL COMMENT '创建人ID',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_by` BIGINT NOT NULL COMMENT '更新人ID',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_recommendation_type` (`recommendation_type`),
    INDEX `idx_recommendation_id` (`recommendation_id`),
    INDEX `idx_date` (`date`),
    INDEX `idx_status` (`status`),
    INDEX `idx_created_at` (`created_at`),
    INDEX `idx_last_interaction_at` (`last_interaction_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='推荐历史表';

-- ====================================
-- 3. 用户偏好表
-- ====================================
CREATE TABLE `recommendation_user_preferences` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `user_id` BIGINT NOT NULL COMMENT '用户ID',
    `style_preferences` JSON COMMENT '风格偏好列表（JSON格式）',
    `color_preferences` JSON COMMENT '颜色偏好列表（JSON格式）',
    `brand_preferences` JSON COMMENT '品牌偏好列表（JSON格式）',
    `occasion_preferences` JSON COMMENT '场合偏好列表（JSON格式）',
    `size_info` JSON COMMENT '尺码信息（JSON格式）',
    `height` INT DEFAULT NULL COMMENT '身高（cm）',
    `weight` INT DEFAULT NULL COMMENT '体重（kg）',
    `body_shape` VARCHAR(50) DEFAULT NULL COMMENT '体型',
    `skin_tone` VARCHAR(50) DEFAULT NULL COMMENT '肤色',
    `recommendation_mode` VARCHAR(20) DEFAULT 'natural' COMMENT '推荐模式（natural/energy）',
    `budget_min` INT DEFAULT 0 COMMENT '预算下限（分）',
    `budget_max` INT DEFAULT 999999 COMMENT '预算上限（分）',
    `disliked_styles` JSON COMMENT '不喜欢的风格列表（JSON格式）',
    `disliked_colors` JSON COMMENT '不喜欢的颜色列表（JSON格式）',
    `excluded_brands` JSON COMMENT '排除的品牌列表（JSON格式）',
    `is_energy_mode_enabled` TINYINT(1) DEFAULT 0 COMMENT '是否启用能量模式（0：否，1：是）',
    `wuxing_weights` JSON COMMENT '五行权重配置（JSON格式）',
    `status` TINYINT DEFAULT 1 COMMENT '状态（1：正常，0：禁用，-1：删除）',
    `created_by` BIGINT NOT NULL COMMENT '创建人ID',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_by` BIGINT NOT NULL COMMENT '更新人ID',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_id` (`user_id`),
    INDEX `idx_recommendation_mode` (`recommendation_mode`),
    INDEX `idx_body_shape` (`body_shape`),
    INDEX `idx_skin_tone` (`skin_tone`),
    INDEX `idx_height` (`height`),
    INDEX `idx_is_energy_mode_enabled` (`is_energy_mode_enabled`),
    INDEX `idx_status` (`status`),
    INDEX `idx_created_at` (`created_at`),
    INDEX `idx_updated_at` (`updated_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户偏好表';

-- ====================================
-- 4. 推荐反馈表（独立表，用于详细反馈分析）
-- ====================================
CREATE TABLE `recommendation_feedbacks` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `user_id` BIGINT NOT NULL COMMENT '用户ID',
    `recommendation_id` BIGINT NOT NULL COMMENT '推荐ID',
    `recommendation_type` VARCHAR(50) NOT NULL COMMENT '推荐类型（daily/occasion/shopping/travel）',
    `liked` TINYINT(1) DEFAULT NULL COMMENT '是否喜欢（0：不喜欢，1：喜欢）',
    `rating` DECIMAL(3,2) DEFAULT NULL COMMENT '评分（0-5）',
    `comment` TEXT COMMENT '评论内容',
    `feedback_tags` JSON COMMENT '反馈标签（JSON格式）',
    `outfit_items` JSON COMMENT '反馈的搭配单品（JSON格式）',
    `reason_analysis` JSON COMMENT '反馈原因分析（JSON格式）',
    `status` TINYINT DEFAULT 1 COMMENT '状态（1：正常，0：禁用，-1：删除）',
    `created_by` BIGINT NOT NULL COMMENT '创建人ID',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_by` BIGINT NOT NULL COMMENT '更新人ID',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_recommendation_id` (`recommendation_id`),
    INDEX `idx_recommendation_type` (`recommendation_type`),
    INDEX `idx_liked` (`liked`),
    INDEX `idx_rating` (`rating`),
    INDEX `idx_status` (`status`),
    INDEX `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='推荐反馈表';

-- ====================================
-- 5. 推荐算法配置表
-- ====================================
CREATE TABLE `recommendation_algorithm_configs` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `config_key` VARCHAR(100) NOT NULL COMMENT '配置键',
    `config_value` JSON NOT NULL COMMENT '配置值（JSON格式）',
    `config_type` VARCHAR(50) NOT NULL COMMENT '配置类型（wuxing/weather/style/algorithm）',
    `description` TEXT COMMENT '配置描述',
    `is_active` TINYINT(1) DEFAULT 1 COMMENT '是否启用（0：否，1：是）',
    `priority` INT DEFAULT 0 COMMENT '优先级',
    `valid_from` TIMESTAMP NULL COMMENT '生效开始时间',
    `valid_to` TIMESTAMP NULL COMMENT '生效结束时间',
    `status` TINYINT DEFAULT 1 COMMENT '状态（1：正常，0：禁用，-1：删除）',
    `created_by` BIGINT NOT NULL COMMENT '创建人ID',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_by` BIGINT NOT NULL COMMENT '更新人ID',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_config_key` (`config_key`),
    INDEX `idx_config_type` (`config_type`),
    INDEX `idx_is_active` (`is_active`),
    INDEX `idx_priority` (`priority`),
    INDEX `idx_valid_from` (`valid_from`),
    INDEX `idx_valid_to` (`valid_to`),
    INDEX `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='推荐算法配置表';

-- ====================================
-- 6. 天气数据缓存表
-- ====================================
CREATE TABLE `recommendation_weather_cache` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `location` VARCHAR(100) NOT NULL COMMENT '位置（城市名或坐标）',
    `location_hash` VARCHAR(64) NOT NULL COMMENT '位置哈希（用于快速查找）',
    `weather_date` DATE NOT NULL COMMENT '天气日期',
    `temperature` DECIMAL(5,2) COMMENT '温度（摄氏度）',
    `feels_like` DECIMAL(5,2) COMMENT '体感温度（摄氏度）',
    `humidity` INT COMMENT '湿度（百分比）',
    `weather_condition` VARCHAR(50) COMMENT '天气状况',
    `weather_code` VARCHAR(20) COMMENT '天气代码',
    `wind_speed` DECIMAL(5,2) COMMENT '风速（km/h）',
    `wind_direction` VARCHAR(10) COMMENT '风向',
    `rainfall` DECIMAL(5,2) DEFAULT 0.00 COMMENT '降雨量（mm）',
    `uv_index` INT COMMENT 'UV指数',
    `clothing_index` VARCHAR(20) COMMENT '穿衣指数',
    `air_quality` VARCHAR(20) COMMENT '空气质量',
    `dominant_element` VARCHAR(10) COMMENT '主导五行元素',
    `weather_data` JSON COMMENT '完整天气数据（JSON格式）',
    `data_source` VARCHAR(50) COMMENT '数据来源',
    `expires_at` TIMESTAMP NOT NULL COMMENT '过期时间',
    `status` TINYINT DEFAULT 1 COMMENT '状态（1：正常，0：禁用，-1：删除）',
    `created_by` BIGINT NOT NULL COMMENT '创建人ID',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_by` BIGINT NOT NULL COMMENT '更新人ID',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_location_date` (`location_hash`, `weather_date`),
    INDEX `idx_location` (`location`),
    INDEX `idx_weather_date` (`weather_date`),
    INDEX `idx_expires_at` (`expires_at`),
    INDEX `idx_clothing_index` (`clothing_index`),
    INDEX `idx_dominant_element` (`dominant_element`),
    INDEX `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='天气数据缓存表';

-- ====================================
-- 初始化配置数据
-- ====================================

-- 插入基本的五行配置
INSERT INTO `recommendation_algorithm_configs` 
(`config_key`, `config_value`, `config_type`, `description`, `created_by`, `updated_by`) 
VALUES 
('wuxing.color_mapping', '{"金":["白色","银色","灰色"],"木":["绿色","青色","蓝绿"],"水":["黑色","深蓝","藏青"],"火":["红色","橙色","粉色"],"土":["黄色","棕色","米色"]}', 'wuxing', '五行颜色映射配置', 1, 1),
('wuxing.style_mapping', '{"金":["简约","现代","商务"],"木":["自然","休闲","田园"],"水":["优雅","流动","知性"],"火":["热情","活力","个性"],"土":["稳重","传统","经典"]}', 'wuxing', '五行风格映射配置', 1, 1),
('weather.clothing_index', '{"炎热":{"temperature":">30","suggestion":"轻薄透气"},"温暖":{"temperature":"20-30","suggestion":"单衣舒适"},"凉爽":{"temperature":"10-20","suggestion":"薄外套"},"寒冷":{"temperature":"<10","suggestion":"厚外套保暖"}}', 'weather', '天气穿衣指数配置', 1, 1),
('algorithm.default_weights', '{"weather":0.3,"wuxing":0.25,"personal":0.25,"occasion":0.2}', 'algorithm', '推荐算法默认权重配置', 1, 1);

-- ====================================
-- 创建存储过程和函数
-- ====================================

DELIMITER ;;

-- 清理过期天气数据的存储过程
CREATE PROCEDURE `CleanExpiredWeatherData`()
BEGIN
    DECLARE affected_rows INT DEFAULT 0;
    
    -- 删除过期的天气数据
    DELETE FROM `recommendation_weather_cache` 
    WHERE `expires_at` < NOW() OR `status` = -1;
    
    SET affected_rows = ROW_COUNT();
    
    -- 记录清理结果（可以扩展为日志表）
    SELECT CONCAT('Cleaned ', affected_rows, ' expired weather records') AS result;
END ;;

-- 获取用户推荐统计的函数
CREATE FUNCTION `GetUserRecommendationCount`(userId BIGINT) 
RETURNS INT
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE rec_count INT DEFAULT 0;
    
    SELECT COUNT(*) INTO rec_count
    FROM `recommendation_daily_recommendations`
    WHERE `user_id` = userId AND `status` = 1;
    
    RETURN rec_count;
END ;;

DELIMITER ;

-- ====================================
-- 创建触发器
-- ====================================

-- 每日推荐表的触发器：自动更新推荐历史
DELIMITER ;;

CREATE TRIGGER `tr_daily_recommendation_after_insert`
AFTER INSERT ON `recommendation_daily_recommendations`
FOR EACH ROW
BEGIN
    -- 插入或更新推荐历史记录
    INSERT INTO `recommendation_histories` 
    (`user_id`, `recommendation_type`, `recommendation_id`, `date`, `view_count`, `created_by`, `updated_by`)
    VALUES 
    (NEW.user_id, 'daily', NEW.id, NEW.date, 0, NEW.created_by, NEW.updated_by)
    ON DUPLICATE KEY UPDATE
    `updated_at` = NOW(),
    `updated_by` = NEW.updated_by;
END ;;

CREATE TRIGGER `tr_daily_recommendation_after_update`
AFTER UPDATE ON `recommendation_daily_recommendations`
FOR EACH ROW
BEGIN
    -- 如果查看状态发生变化，更新历史记录
    IF NEW.is_viewed != OLD.is_viewed AND NEW.is_viewed = 1 THEN
        UPDATE `recommendation_histories` 
        SET `view_count` = `view_count` + 1,
            `last_interaction_at` = NOW(),
            `updated_at` = NOW(),
            `updated_by` = NEW.updated_by
        WHERE `recommendation_id` = NEW.id AND `recommendation_type` = 'daily';
    END IF;
END ;;

DELIMITER ;

-- ====================================
-- 创建视图
-- ====================================

-- 用户推荐概览视图
CREATE VIEW `v_user_recommendation_overview` AS
SELECT 
    u.user_id,
    COUNT(d.id) as total_recommendations,
    COUNT(CASE WHEN d.is_viewed = 1 THEN 1 END) as viewed_count,
    COUNT(CASE WHEN d.feedback IS NOT NULL THEN 1 END) as feedback_count,
    AVG(JSON_EXTRACT(d.feedback, '$.rating')) as avg_rating,
    MAX(d.created_at) as last_recommendation_at,
    up.recommendation_mode,
    up.is_energy_mode_enabled
FROM (SELECT DISTINCT user_id FROM recommendation_daily_recommendations WHERE status = 1) u
LEFT JOIN recommendation_daily_recommendations d ON u.user_id = d.user_id AND d.status = 1
LEFT JOIN recommendation_user_preferences up ON u.user_id = up.user_id AND up.status = 1
GROUP BY u.user_id, up.recommendation_mode, up.is_energy_mode_enabled;

-- 推荐效果统计视图
CREATE VIEW `v_recommendation_effectiveness` AS
SELECT 
    DATE(created_at) as recommendation_date,
    COUNT(*) as total_count,
    COUNT(CASE WHEN is_viewed = 1 THEN 1 END) as viewed_count,
    COUNT(CASE WHEN feedback IS NOT NULL THEN 1 END) as feedback_count,
    COUNT(CASE WHEN JSON_EXTRACT(feedback, '$.liked') = true THEN 1 END) as liked_count,
    AVG(total_score) as avg_score,
    AVG(JSON_EXTRACT(feedback, '$.rating')) as avg_user_rating
FROM recommendation_daily_recommendations
WHERE status = 1
GROUP BY DATE(created_at)
ORDER BY recommendation_date DESC; 