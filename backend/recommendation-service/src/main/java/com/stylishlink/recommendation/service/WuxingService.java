package com.stylishlink.recommendation.service;

import com.stylishlink.recommendation.dto.request.OutfitRecommendationRequest.WuxingProfile;

import java.util.Map;

/**
 * 五行命理服务接口
 */
public interface WuxingService {
    
    /**
     * 计算五行属性
     * @param birthYear 出生年
     * @param birthMonth 出生月
     * @param birthDay 出生日
     * @param birthHour 出生时
     * @return 五行属性得分
     */
    Map<String, Integer> calculateWuxingElements(String birthYear, String birthMonth, String birthDay, String birthHour);
    
    /**
     * 获取用户五行命理档案
     * @param userId 用户ID
     * @return 五行命理档案
     */
    WuxingProfile getUserWuxingProfile(String userId);
    
    /**
     * 获取适合的颜色
     * @param wuxingProfile 五行命理档案
     * @return 适合的颜色列表
     */
    String[] getSuitableColors(WuxingProfile wuxingProfile);
    
    /**
     * 获取适合的图案
     * @param wuxingProfile 五行命理档案
     * @return 适合的图案列表
     */
    String[] getSuitablePatterns(WuxingProfile wuxingProfile);
    
    /**
     * 计算衣物与用户五行的匹配度
     * @param clothingAttributes 衣物属性
     * @param wuxingProfile 五行命理档案
     * @return 匹配度得分
     */
    Double calculateMatchScore(Map<String, Object> clothingAttributes, WuxingProfile wuxingProfile);
} 