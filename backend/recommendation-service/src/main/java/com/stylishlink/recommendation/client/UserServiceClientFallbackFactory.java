package com.stylishlink.recommendation.client;

import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Map;

/**
 * 用户服务Feign客户端回退工厂类
 */
@Component
@Slf4j
public class UserServiceClientFallbackFactory implements FallbackFactory<UserServiceClient> {

    @Override
    public UserServiceClient create(Throwable cause) {
        log.error("用户服务调用失败", cause);
        
        return new UserServiceClient() {
            @Override
            public Map<String, Object> getUserInfo(String userId) {
                log.warn("获取用户信息失败，返回空对象，用户ID: {}", userId);
                return Collections.emptyMap();
            }

            @Override
            public Map<String, Object> getUserPreferences(String userId) {
                log.warn("获取用户偏好设置失败，返回空对象，用户ID: {}", userId);
                return Collections.emptyMap();
            }

            @Override
            public Map<String, Object> getUserWuxingInfo(String userId) {
                log.warn("获取用户五行命理信息失败，返回空对象，用户ID: {}", userId);
                return Collections.emptyMap();
            }

            @Override
            public Map<String, Object> updateUserPreferences(String userId, Map<String, Object> preferences) {
                log.warn("更新用户偏好设置失败，返回空对象，用户ID: {}", userId);
                return Collections.emptyMap();
            }

            @Override
            public boolean validateToken(String token) {
                log.warn("验证用户令牌失败，返回false");
                return false;
            }

            @Override
            public String extractUserIdFromToken(String token) {
                log.warn("从令牌中提取用户ID失败，返回null");
                return null;
            }
        };
    }
} 