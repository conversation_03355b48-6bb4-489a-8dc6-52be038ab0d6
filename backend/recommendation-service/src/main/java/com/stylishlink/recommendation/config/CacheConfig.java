package com.stylishlink.recommendation.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

/**
 * 缓存配置类
 */
@Configuration
@EnableCaching
public class CacheConfig {

    @Value("${recommendation.cache.ttl:3600}")
    private int defaultCacheTtl;

    /**
     * 配置缓存管理器
     */
    @Bean
    public CacheManager cacheManager(RedisConnectionFactory connectionFactory) {
        // 默认缓存配置
        RedisCacheConfiguration defaultConfig = RedisCacheConfiguration.defaultCacheConfig()
            .entryTtl(Duration.ofSeconds(defaultCacheTtl))
            .serializeKeysWith(RedisSerializationContext.SerializationPair.fromSerializer(new StringRedisSerializer()))
            .serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(new GenericJackson2JsonRedisSerializer()))
            .disableCachingNullValues();
        
        // 特定缓存配置
        Map<String, RedisCacheConfiguration> cacheConfigurations = new HashMap<>();
        
        // 天气缓存配置 - 1小时
        cacheConfigurations.put("currentWeather", defaultConfig.entryTtl(Duration.ofHours(1)));
        cacheConfigurations.put("forecastWeather", defaultConfig.entryTtl(Duration.ofHours(1)));
        
        // 用户信息缓存配置 - 1天
        cacheConfigurations.put("userInfo", defaultConfig.entryTtl(Duration.ofDays(1)));
        cacheConfigurations.put("userPreferences", defaultConfig.entryTtl(Duration.ofDays(1)));
        cacheConfigurations.put("userWuxingInfo", defaultConfig.entryTtl(Duration.ofDays(1)));
        
        // 衣物信息缓存配置 - 6小时
        cacheConfigurations.put("userClothing", defaultConfig.entryTtl(Duration.ofHours(6)));
        cacheConfigurations.put("userClothingByCategory", defaultConfig.entryTtl(Duration.ofHours(6)));
        cacheConfigurations.put("userClothingBySeason", defaultConfig.entryTtl(Duration.ofHours(6)));
        cacheConfigurations.put("userClothingByOccasion", defaultConfig.entryTtl(Duration.ofHours(6)));
        cacheConfigurations.put("userClothingByColor", defaultConfig.entryTtl(Duration.ofHours(6)));
        cacheConfigurations.put("clothingDetails", defaultConfig.entryTtl(Duration.ofHours(6)));
        
        // 搭配信息缓存配置 - 6小时
        cacheConfigurations.put("userOutfits", defaultConfig.entryTtl(Duration.ofHours(6)));
        cacheConfigurations.put("outfitDetails", defaultConfig.entryTtl(Duration.ofHours(6)));
        
        // 推荐缓存配置 - 1小时
        cacheConfigurations.put("outfitRecommendations", defaultConfig.entryTtl(Duration.ofHours(1)));
        
        // 五行命理缓存配置 - 30天
        cacheConfigurations.put("wuxingProfiles", defaultConfig.entryTtl(Duration.ofDays(30)));
        
        return RedisCacheManager.builder(connectionFactory)
            .cacheDefaults(defaultConfig)
            .withInitialCacheConfigurations(cacheConfigurations)
            .build();
    }
} 