package com.stylishlink.recommendation.client;

import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 衣橱服务Feign客户端回退工厂类
 */
@Component
@Slf4j
public class WardrobeServiceClientFallbackFactory implements FallbackFactory<WardrobeServiceClient> {

    @Override
    public WardrobeServiceClient create(Throwable cause) {
        log.error("衣橱服务调用失败", cause);
        
        return new WardrobeServiceClient() {
            @Override
            public List<Map<String, Object>> getUserClothing(String userId) {
                log.warn("获取用户衣物失败，返回空列表，用户ID: {}", userId);
                return Collections.emptyList();
            }

            @Override
            public List<Map<String, Object>> getUserClothingByCategory(String userId, String category) {
                log.warn("根据类别获取用户衣物失败，返回空列表，用户ID: {}, 类别: {}", userId, category);
                return Collections.emptyList();
            }

            @Override
            public List<Map<String, Object>> getUserClothingBySeason(String userId, String season) {
                log.warn("根据季节获取用户衣物失败，返回空列表，用户ID: {}, 季节: {}", userId, season);
                return Collections.emptyList();
            }

            @Override
            public List<Map<String, Object>> getUserClothingByOccasion(String userId, String occasion) {
                log.warn("根据场合获取用户衣物失败，返回空列表，用户ID: {}, 场合: {}", userId, occasion);
                return Collections.emptyList();
            }

            @Override
            public List<Map<String, Object>> getUserClothingByColor(String userId, String color) {
                log.warn("根据颜色获取用户衣物失败，返回空列表，用户ID: {}, 颜色: {}", userId, color);
                return Collections.emptyList();
            }

            @Override
            public Map<String, Object> getClothingDetails(String clothingId) {
                log.warn("获取衣物详情失败，返回空对象，衣物ID: {}", clothingId);
                return Collections.emptyMap();
            }

            @Override
            public List<Map<String, Object>> getUserOutfits(String userId) {
                log.warn("获取用户搭配失败，返回空列表，用户ID: {}", userId);
                return Collections.emptyList();
            }

            @Override
            public Map<String, Object> getOutfitDetails(String outfitId) {
                log.warn("获取搭配详情失败，返回空对象，搭配ID: {}", outfitId);
                return Collections.emptyMap();
            }
        };
    }
} 