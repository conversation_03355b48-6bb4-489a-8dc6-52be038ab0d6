package com.stylishlink.recommendation.exception;

import lombok.Getter;
import org.springframework.http.HttpStatus;

/**
 * 业务异常类
 */
@Getter
public class BusinessException extends RuntimeException {

    private final HttpStatus status;

    /**
     * 创建业务异常
     * @param message 异常消息
     */
    public BusinessException(String message) {
        this(message, HttpStatus.BAD_REQUEST);
    }

    /**
     * 创建业务异常
     * @param message 异常消息
     * @param status HTTP状态码
     */
    public BusinessException(String message, HttpStatus status) {
        super(message);
        this.status = status;
    }
    
    /**
     * 创建业务异常
     * @param message 异常消息
     * @param cause 原始异常
     */
    public BusinessException(String message, Throwable cause) {
        this(message, HttpStatus.BAD_REQUEST, cause);
    }
    
    /**
     * 创建业务异常
     * @param message 异常消息
     * @param status HTTP状态码
     * @param cause 原始异常
     */
    public BusinessException(String message, HttpStatus status, Throwable cause) {
        super(message, cause);
        this.status = status;
    }
} 