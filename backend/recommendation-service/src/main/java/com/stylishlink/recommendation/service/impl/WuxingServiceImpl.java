package com.stylishlink.recommendation.service.impl;

import com.stylishlink.recommendation.dto.request.OutfitRecommendationRequest.WuxingProfile;
import com.stylishlink.recommendation.service.UserService;
import com.stylishlink.recommendation.service.WuxingService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 五行命理服务实现类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class WuxingServiceImpl implements WuxingService {

    private final UserService userService;
    
    // 五行与颜色的映射关系
    private static final Map<String, String[]> WUXING_COLORS = Map.of(
        "金", new String[]{"白色", "金色", "银色", "米色", "浅黄色"},
        "木", new String[]{"绿色", "青色", "淡绿色", "橄榄色", "草绿色"},
        "水", new String[]{"蓝色", "黑色", "灰色", "深蓝色", "藏青色"},
        "火", new String[]{"红色", "紫色", "粉色", "橙色", "桃红色"},
        "土", new String[]{"黄色", "棕色", "咖啡色", "土黄色", "卡其色"}
    );
    
    // 五行与图案的映射关系
    private static final Map<String, String[]> WUXING_PATTERNS = Map.of(
        "金", new String[]{"几何图案", "圆形", "方形", "金属质感", "简约线条"},
        "木", new String[]{"树叶", "花卉", "自然纹理", "条纹", "植物图案"},
        "水", new String[]{"波浪", "水滴", "流线型", "抽象水纹", "云纹"},
        "火", new String[]{"火焰", "太阳", "星星", "三角形", "锯齿形"},
        "土", new String[]{"方格", "格子", "山形", "大地纹理", "复古图案"}
    );

    @Override
    public Map<String, Integer> calculateWuxingElements(String birthYear, String birthMonth, String birthDay, String birthHour) {
        log.debug("计算五行属性，出生年月日时: {}-{}-{} {}", birthYear, birthMonth, birthDay, birthHour);
        
        // 简化实现，实际应根据中国传统命理学进行计算
        // 这里仅作为示例，返回随机生成的五行得分
        Map<String, Integer> elements = new HashMap<>();
        elements.put("金", 70);
        elements.put("木", 60);
        elements.put("水", 80);
        elements.put("火", 50);
        elements.put("土", 65);
        
        return elements;
    }

    @Override
    @Cacheable(value = "wuxingProfiles", key = "#userId", unless = "#result == null")
    public WuxingProfile getUserWuxingProfile(String userId) {
        log.debug("获取用户五行命理档案，用户ID: {}", userId);
        
        try {
            // 从用户服务获取用户五行命理信息
            Map<String, Object> wuxingInfo = userService.getUserWuxingInfo(userId);
            
            if (wuxingInfo != null && !wuxingInfo.isEmpty()) {
                Map<String, Integer> elements = new HashMap<>();
                
                // 从用户服务返回的数据中提取五行得分
                if (wuxingInfo.containsKey("elements") && wuxingInfo.get("elements") instanceof Map) {
                    Map<String, Object> elementData = (Map<String, Object>) wuxingInfo.get("elements");
                    
                    for (Map.Entry<String, Object> entry : elementData.entrySet()) {
                        if (entry.getValue() instanceof Number) {
                            elements.put(entry.getKey(), ((Number) entry.getValue()).intValue());
                        }
                    }
                } else {
                    // 如果没有五行数据，使用默认值
                    elements.put("金", 60);
                    elements.put("木", 60);
                    elements.put("水", 60);
                    elements.put("火", 60);
                    elements.put("土", 60);
                }
                
                return WuxingProfile.builder()
                    .dominantElement(findDominantElement(elements))
                    .weakElement(findWeakElement(elements))
                    .elementScores(elements)
                    .build();
            }
        } catch (Exception e) {
            log.error("获取用户五行命理档案失败", e);
        }
        
        // 返回默认五行档案
        return getDefaultWuxingProfile();
    }

    @Override
    public String[] getSuitableColors(WuxingProfile wuxingProfile) {
        if (wuxingProfile == null || wuxingProfile.getDominantElement() == null) {
            return WUXING_COLORS.get("木"); // 默认返回木属性的颜色
        }
        
        // 获取主导五行和弱势五行
        String dominantElement = wuxingProfile.getDominantElement();
        String weakElement = wuxingProfile.getWeakElement();
        
        // 根据五行相生关系，推荐主导五行生的五行的颜色
        String generatedElement = getGeneratingElement(dominantElement);
        
        // 返回对应的颜色
        return WUXING_COLORS.getOrDefault(generatedElement, WUXING_COLORS.get("木"));
    }

    @Override
    public String[] getSuitablePatterns(WuxingProfile wuxingProfile) {
        if (wuxingProfile == null || wuxingProfile.getDominantElement() == null) {
            return WUXING_PATTERNS.get("木"); // 默认返回木属性的图案
        }
        
        // 获取主导五行
        String dominantElement = wuxingProfile.getDominantElement();
        
        // 返回对应的图案
        return WUXING_PATTERNS.getOrDefault(dominantElement, WUXING_PATTERNS.get("木"));
    }

    @Override
    public Double calculateMatchScore(Map<String, Object> clothingAttributes, WuxingProfile wuxingProfile) {
        if (clothingAttributes == null || wuxingProfile == null) {
            return 50.0; // 默认中等匹配度
        }
        
        double score = 70.0; // 基础分
        
        try {
            // 获取衣物的五行属性
            Map<String, Integer> clothingWuxing = null;
            if (clothingAttributes.containsKey("wuxingAttributes")) {
                clothingWuxing = (Map<String, Integer>) clothingAttributes.get("wuxingAttributes");
            }
            
            if (clothingWuxing == null || clothingWuxing.isEmpty()) {
                // 如果衣物没有五行属性，根据颜色和材质推断
                String color = (String) clothingAttributes.get("color");
                String material = (String) clothingAttributes.get("material");
                
                // 根据颜色调整分数
                if (color != null) {
                    for (Map.Entry<String, String[]> entry : WUXING_COLORS.entrySet()) {
                        String element = entry.getKey();
                        String[] colors = entry.getValue();
                        
                        for (String c : colors) {
                            if (color.contains(c)) {
                                // 如果颜色属于某个五行，检查与用户五行的关系
                                score += calculateElementRelationScore(element, wuxingProfile);
                                break;
                            }
                        }
                    }
                }
            } else {
                // 如果衣物有五行属性，计算与用户五行的匹配度
                Map<String, Integer> userElements = wuxingProfile.getElementScores();
                
                double totalScore = 0;
                int count = 0;
                
                for (Map.Entry<String, Integer> entry : clothingWuxing.entrySet()) {
                    String element = entry.getKey();
                    Integer strength = entry.getValue();
                    
                    if (strength > 0) {
                        // 计算此元素与用户五行的关系得分
                        double elementScore = calculateElementRelationScore(element, wuxingProfile);
                        totalScore += elementScore * (strength / 100.0); // 按元素强度加权
                        count++;
                    }
                }
                
                if (count > 0) {
                    score += totalScore / count;
                }
            }
        } catch (Exception e) {
            log.error("计算五行匹配度时发生错误", e);
        }
        
        // 确保分数在0-100范围内
        return Math.max(0, Math.min(100, score));
    }
    
    /**
     * 计算元素与用户五行的关系得分
     */
    private double calculateElementRelationScore(String element, WuxingProfile wuxingProfile) {
        String dominantElement = wuxingProfile.getDominantElement();
        String weakElement = wuxingProfile.getWeakElement();
        
        // 如果元素是用户的主导元素，加分
        if (element.equals(dominantElement)) {
            return 15.0;
        }
        
        // 如果元素是用户的弱势元素，减分
        if (element.equals(weakElement)) {
            return -5.0;
        }
        
        // 如果元素生用户的主导元素，加分
        if (element.equals(getGeneratingElement(dominantElement))) {
            return 10.0;
        }
        
        // 如果元素克制用户的弱势元素，加分
        if (element.equals(getControllingElement(weakElement))) {
            return 5.0;
        }
        
        // 如果元素被用户的主导元素克制，减分
        if (dominantElement.equals(getControllingElement(element))) {
            return -10.0;
        }
        
        return 0.0;
    }
    
    /**
     * 查找主导五行元素
     */
    private String findDominantElement(Map<String, Integer> elements) {
        return elements.entrySet().stream()
            .max(Map.Entry.comparingByValue())
            .map(Map.Entry::getKey)
            .orElse("木");
    }
    
    /**
     * 查找弱势五行元素
     */
    private String findWeakElement(Map<String, Integer> elements) {
        return elements.entrySet().stream()
            .min(Map.Entry.comparingByValue())
            .map(Map.Entry::getKey)
            .orElse("土");
    }
    
    /**
     * 获取生成指定元素的五行
     * 五行相生关系：木生火，火生土，土生金，金生水，水生木
     */
    private String getGeneratingElement(String element) {
        switch (element) {
            case "木": return "水";
            case "火": return "木";
            case "土": return "火";
            case "金": return "土";
            case "水": return "金";
            default: return "木";
        }
    }
    
    /**
     * 获取克制指定元素的五行
     * 五行相克关系：木克土，土克水，水克火，火克金，金克木
     */
    private String getControllingElement(String element) {
        switch (element) {
            case "木": return "金";
            case "火": return "水";
            case "土": return "木";
            case "金": return "火";
            case "水": return "土";
            default: return "金";
        }
    }
    
    /**
     * 获取默认五行档案
     */
    private WuxingProfile getDefaultWuxingProfile() {
        Map<String, Integer> elements = new HashMap<>();
        elements.put("金", 60);
        elements.put("木", 60);
        elements.put("水", 60);
        elements.put("火", 60);
        elements.put("土", 60);
        
        return WuxingProfile.builder()
            .dominantElement("木")
            .weakElement("土")
            .elementScores(elements)
            .build();
    }
} 