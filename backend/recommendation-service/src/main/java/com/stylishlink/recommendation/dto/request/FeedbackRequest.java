package com.stylishlink.recommendation.dto.request;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import io.swagger.v3.oas.annotations.media.Schema;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;

/**
 * 推荐反馈请求DTO
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "推荐反馈请求")
public class FeedbackRequest {

    @Schema(description = "推荐ID", example = "rec_123456", required = true)
    @NotBlank(message = "推荐ID不能为空")
    private String recommendId;

    @Schema(description = "评分", example = "4.5")
    @DecimalMin(value = "0.0", message = "评分不能小于0")
    @DecimalMax(value = "5.0", message = "评分不能大于5")
    private Double rating;

    @Schema(description = "反馈内容", example = "很喜欢这套搭配")
    private String feedback;

    @Schema(description = "是否喜欢", example = "true")
    private Boolean liked;
} 