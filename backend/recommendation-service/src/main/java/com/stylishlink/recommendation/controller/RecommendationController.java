package com.stylishlink.recommendation.controller;

import com.stylishlink.common.dto.ApiResponse;
import com.stylishlink.recommendation.dto.request.DailyRecommendRequest;
import com.stylishlink.recommendation.dto.request.FeedbackRequest;
import com.stylishlink.recommendation.dto.response.DailyRecommendationResponse;
import com.stylishlink.recommendation.service.RecommendationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * 推荐服务控制器
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@RestController
@RequestMapping("/recommend")
@RequiredArgsConstructor
@Slf4j
@Validated
@Tag(name = "推荐服务API", description = "穿搭推荐、五行命理分析、天气数据处理等功能")
public class RecommendationController {

    private final RecommendationService recommendationService;

    @PostMapping("/daily")
    @Operation(summary = "获取每日推荐", description = "根据用户偏好、天气和五行分析生成每日穿搭推荐")
    public ApiResponse<DailyRecommendationResponse> getDailyRecommendation(@Valid @RequestBody DailyRecommendRequest request) {
        log.info("获取每日推荐请求: {}", request);
        try {
            DailyRecommendationResponse response = recommendationService.getDailyRecommendation(request);
            return ApiResponse.success(response);
        } catch (RuntimeException e) {
            log.error("获取每日推荐失败", e);
            return ApiResponse.error(4002, "推荐生成失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("获取每日推荐异常", e);
            return ApiResponse.error(4002, "推荐生成失败");
        }
    }

    @GetMapping("/occasion")
    @Operation(summary = "获取场合推荐", description = "根据特定场合获取穿搭推荐")
    public ApiResponse<Object> getOccasionRecommendation() {
        log.warn("场合推荐功能待实现");
        return ApiResponse.error(4005, "场景不支持");
    }

    @GetMapping("/multi")
    @Operation(summary = "获取多场景推荐", description = "获取适用于多个场景的穿搭推荐")
    public ApiResponse<Object> getMultiOccasionRecommendation() {
        log.warn("多场景推荐功能待实现");
        return ApiResponse.error(4005, "场景不支持");
    }

    @GetMapping("/shopping")
    @Operation(summary = "获取购物推荐", description = "获取购物场景下的穿搭推荐")
    public ApiResponse<Object> getShoppingRecommendation() {
        log.warn("购物推荐功能待实现");
        return ApiResponse.error(4005, "场景不支持");
    }

    @GetMapping("/travel")
    @Operation(summary = "获取旅行推荐", description = "获取旅行场景下的穿搭推荐")
    public ApiResponse<Object> getTravelRecommendation() {
        log.warn("旅行推荐功能待实现");
        return ApiResponse.error(4005, "场景不支持");
    }

    @PostMapping("/wuxing")
    @Operation(summary = "五行命理分析", description = "根据用户生辰信息进行五行命理分析")
    public ApiResponse<Object> analyzeWuxing() {
        log.warn("五行分析功能待实现");
        return ApiResponse.error(4005, "场景不支持");
    }

    @GetMapping("/weather")
    @Operation(summary = "获取天气信息", description = "获取当前天气信息和穿衣建议")
    public ApiResponse<Object> getWeatherInfo() {
        log.warn("天气信息功能待实现");
        return ApiResponse.error(4004, "天气数据异常");
    }

    @GetMapping("/personal")
    @Operation(summary = "个性化推荐", description = "基于用户历史和偏好的个性化推荐")
    public ApiResponse<Object> getPersonalizedRecommendation() {
        log.warn("个性化推荐功能待实现");
        return ApiResponse.error(4005, "场景不支持");
    }

    @PostMapping("/feedback")
    @Operation(summary = "提交推荐反馈", description = "用户对推荐结果进行反馈评价")
    public ApiResponse<Void> submitFeedback(@Valid @RequestBody FeedbackRequest request) {
        log.info("提交推荐反馈: {}", request);
        try {
            recommendationService.submitFeedback(request);
            return ApiResponse.success(null);
        } catch (RuntimeException e) {
            log.error("提交推荐反馈失败", e);
            if (e.getMessage().contains("推荐记录不存在")) {
                return ApiResponse.error(4001, "推荐不存在");
            }
            return ApiResponse.error(4007, "反馈提交失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("提交推荐反馈异常", e);
            return ApiResponse.error(4007, "反馈提交失败");
        }
    }

    @GetMapping("/history")
    @Operation(summary = "获取推荐历史", description = "获取用户的推荐历史记录")
    public ApiResponse<Object> getRecommendationHistory() {
        log.warn("推荐历史功能待实现");
        return ApiResponse.error(4005, "场景不支持");
    }

    @GetMapping("/stats")
    @Operation(summary = "获取推荐统计", description = "获取用户的推荐统计信息")
    public ApiResponse<Object> getRecommendationStats() {
        log.warn("推荐统计功能待实现");
        return ApiResponse.error(4005, "场景不支持");
    }

    @GetMapping("/detail/{outfitId}")
    @Operation(summary = "获取搭配详情", description = "获取指定搭配的完整详情信息")
    public ApiResponse<Object> getOutfitDetail(@PathVariable String outfitId) {
        log.warn("搭配详情功能待实现: {}", outfitId);
        return ApiResponse.error(4001, "推荐不存在");
    }
} 