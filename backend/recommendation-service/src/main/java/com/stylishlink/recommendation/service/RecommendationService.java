package com.stylishlink.recommendation.service;

import com.stylishlink.recommendation.dto.request.DailyRecommendRequest;
import com.stylishlink.recommendation.dto.request.FeedbackRequest;
import com.stylishlink.recommendation.dto.response.DailyRecommendationResponse;

/**
 * 推荐服务接口
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public interface RecommendationService {

    /**
     * 获取每日推荐
     */
    DailyRecommendationResponse getDailyRecommendation(DailyRecommendRequest request);

    /**
     * 获取场合推荐
     */
    Object getOccasionRecommendation(Object request);

    /**
     * 获取多场景推荐
     */
    Object getMultiOccasionRecommendation(Object request);

    /**
     * 获取购物推荐
     */
    Object getShoppingRecommendation(Object request);

    /**
     * 获取旅行推荐
     */
    Object getTravelRecommendation(Object request);

    /**
     * 获取个性化推荐
     */
    Object getPersonalizedRecommendation(Object request);

    /**
     * 获取搭配详情
     */
    Object getOutfitDetail(String outfitId);

    /**
     * 提交推荐反馈
     */
    void submitFeedback(FeedbackRequest request);

    /**
     * 获取推荐历史
     */
    Object getRecommendationHistory(Object request);

    /**
     * 获取推荐统计
     */
    Object getRecommendationStats(Object request);
} 