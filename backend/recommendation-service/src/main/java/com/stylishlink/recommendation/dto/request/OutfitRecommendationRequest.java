package com.stylishlink.recommendation.dto.request;

import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 穿搭推荐请求DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OutfitRecommendationRequest {
    
    @NotBlank(message = "用户ID不能为空")
    private String userId;
    
    private String occasion;
    private List<String> preferredStyles;
    private List<String> preferredColors;
    
    private WeatherInfo weatherInfo;
    private WuxingProfile wuxingProfile;
    
    private Integer count;
    private Boolean useAI;
    
    /**
     * 获取是否使用AI
     */
    public Boolean getUseAI() {
        return useAI;
    }
    
    /**
     * 天气信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class WeatherInfo {
        private Double temperature;
        private String condition;
        private String city;
        private String season;
        private Integer humidity;
        private Double windSpeed;
        private Double precipitation;
    }
    
    /**
     * 五行命理档案
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class WuxingProfile {
        private String birthYear;
        private String birthMonth;
        private String birthDay;
        private String birthHour;
        private Map<String, Integer> elementScores;
        private String dominantElement;
        private String weakElement;
    }
} 