package com.stylishlink.recommendation.service.impl;

import com.stylishlink.recommendation.client.WardrobeServiceClient;
import com.stylishlink.recommendation.service.ClothingService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 衣物服务实现类 - 通过Feign客户端与衣橱服务交互
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ClothingServiceImpl implements ClothingService {

    private final WardrobeServiceClient wardrobeServiceClient;

    @Override
    @Cacheable(value = "userClothing", key = "#userId", unless = "#result == null || #result.isEmpty()")
    public List<Map<String, Object>> getUserClothing(String userId) {
        log.debug("获取用户所有衣物，用户ID: {}", userId);
        
        try {
            return wardrobeServiceClient.getUserClothing(userId);
        } catch (Exception e) {
            log.error("获取用户衣物失败", e);
            return Collections.emptyList();
        }
    }

    @Override
    @Cacheable(value = "userClothingByCategory", key = "#userId + '-' + #category", unless = "#result == null || #result.isEmpty()")
    public List<Map<String, Object>> getUserClothingByCategory(String userId, String category) {
        log.debug("根据类别获取用户衣物，用户ID: {}, 类别: {}", userId, category);
        
        try {
            return wardrobeServiceClient.getUserClothingByCategory(userId, category);
        } catch (Exception e) {
            log.error("根据类别获取用户衣物失败", e);
            return Collections.emptyList();
        }
    }

    @Override
    @Cacheable(value = "userClothingBySeason", key = "#userId + '-' + #season", unless = "#result == null || #result.isEmpty()")
    public List<Map<String, Object>> getUserClothingBySeason(String userId, String season) {
        log.debug("根据季节获取用户衣物，用户ID: {}, 季节: {}", userId, season);
        
        try {
            return wardrobeServiceClient.getUserClothingBySeason(userId, season);
        } catch (Exception e) {
            log.error("根据季节获取用户衣物失败", e);
            return Collections.emptyList();
        }
    }

    @Override
    @Cacheable(value = "userClothingByOccasion", key = "#userId + '-' + #occasion", unless = "#result == null || #result.isEmpty()")
    public List<Map<String, Object>> getUserClothingByOccasion(String userId, String occasion) {
        log.debug("根据场合获取用户衣物，用户ID: {}, 场合: {}", userId, occasion);
        
        try {
            return wardrobeServiceClient.getUserClothingByOccasion(userId, occasion);
        } catch (Exception e) {
            log.error("根据场合获取用户衣物失败", e);
            return Collections.emptyList();
        }
    }

    @Override
    @Cacheable(value = "userClothingByColor", key = "#userId + '-' + #color", unless = "#result == null || #result.isEmpty()")
    public List<Map<String, Object>> getUserClothingByColor(String userId, String color) {
        log.debug("根据颜色获取用户衣物，用户ID: {}, 颜色: {}", userId, color);
        
        try {
            return wardrobeServiceClient.getUserClothingByColor(userId, color);
        } catch (Exception e) {
            log.error("根据颜色获取用户衣物失败", e);
            return Collections.emptyList();
        }
    }

    @Override
    @Cacheable(value = "clothingDetails", key = "#clothingId", unless = "#result == null")
    public Map<String, Object> getClothingDetails(String clothingId) {
        log.debug("获取衣物详情，衣物ID: {}", clothingId);
        
        try {
            return wardrobeServiceClient.getClothingDetails(clothingId);
        } catch (Exception e) {
            log.error("获取衣物详情失败", e);
            return Collections.emptyMap();
        }
    }

    @Override
    @Cacheable(value = "userOutfits", key = "#userId", unless = "#result == null || #result.isEmpty()")
    public List<Map<String, Object>> getUserOutfits(String userId) {
        log.debug("获取用户所有搭配，用户ID: {}", userId);
        
        try {
            return wardrobeServiceClient.getUserOutfits(userId);
        } catch (Exception e) {
            log.error("获取用户搭配失败", e);
            return Collections.emptyList();
        }
    }

    @Override
    @Cacheable(value = "outfitDetails", key = "#outfitId", unless = "#result == null")
    public Map<String, Object> getOutfitDetails(String outfitId) {
        log.debug("获取搭配详情，搭配ID: {}", outfitId);
        
        try {
            return wardrobeServiceClient.getOutfitDetails(outfitId);
        } catch (Exception e) {
            log.error("获取搭配详情失败", e);
            return Collections.emptyMap();
        }
    }
} 