package com.stylishlink.recommendation.dto.response;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 每日推荐响应DTO
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "每日推荐响应")
public class DailyRecommendationResponse {

    @Schema(description = "推荐ID", example = "rec_123456")
    private String id;

    @Schema(description = "推荐日期", example = "2024-01-01")
    private LocalDate date;

    @Schema(description = "推荐搭配列表")
    private List<OutfitItemResponse> outfits;

    @Schema(description = "推荐理由", example = "今日天气晴朗，推荐清新风格搭配")
    private String reason;

    @Schema(description = "推荐场合")
    private List<String> occasions;

    @Schema(description = "总评分", example = "4.5")
    private Double totalScore;

    @Schema(description = "多维度评分")
    private Map<String, Double> multiScore;

    @Schema(description = "天气信息")
    private WeatherInfoResponse weatherInfo;

    @Schema(description = "五行分析")
    private WuxingAnalysisResponse wuxingAnalysis;

    @Schema(description = "能量建议")
    private List<EnergyAdviceResponse> energyAdvice;

    @Schema(description = "用户反馈")
    private FeedbackResponse feedback;

    @Schema(description = "创建时间")
    private LocalDateTime createdAt;

    /**
     * 搭配单品响应
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "搭配单品")
    public static class OutfitItemResponse {
        @Schema(description = "单品ID", example = "item_123")
        private String itemId;

        @Schema(description = "单品名称", example = "白色简约衬衫")
        private String itemName;

        @Schema(description = "单品图片", example = "https://example.com/image.jpg")
        private String itemImageUrl;

        @Schema(description = "单品类型", example = "shirt")
        private String itemType;

        @Schema(description = "五行属性", example = "金")
        private String wuxing;

        @Schema(description = "单品描述", example = "轻薄透气棉质面料")
        private String itemDescription;

        @Schema(description = "单品标签")
        private List<String> itemTags;

        @Schema(description = "来源", example = "wardrobe")
        private String source;

        @Schema(description = "推荐度", example = "95")
        private Integer score;
    }

    /**
     * 天气信息响应
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "天气信息")
    public static class WeatherInfoResponse {
        @Schema(description = "温度", example = "25.5")
        private Double temperature;

        @Schema(description = "天气状况", example = "晴")
        private String weatherCondition;

        @Schema(description = "湿度", example = "60")
        private Integer humidity;

        @Schema(description = "风速", example = "3.2")
        private Double windSpeed;

        @Schema(description = "紫外线指数", example = "5")
        private Integer uvIndex;

        @Schema(description = "穿衣指数", example = "适宜")
        private String clothingIndex;
    }

    /**
     * 五行分析响应
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "五行分析")
    public static class WuxingAnalysisResponse {
        @Schema(description = "金", example = "40")
        private Integer metal;

        @Schema(description = "木", example = "10")
        private Integer wood;

        @Schema(description = "水", example = "20")
        private Integer water;

        @Schema(description = "火", example = "0")
        private Integer fire;

        @Schema(description = "土", example = "30")
        private Integer earth;

        @Schema(description = "五行整体解读", example = "此搭配以金(白色系)为主...")
        private String summary;

        @Schema(description = "各元素解析")
        private List<WuxingDetailResponse> details;
    }

    /**
     * 五行详情响应
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "五行详情")
    public static class WuxingDetailResponse {
        @Schema(description = "五行元素", example = "金")
        private String element;

        @Schema(description = "分值", example = "40")
        private Integer value;

        @Schema(description = "解读文本", example = "白色调衬衫和裙子...")
        private String summary;
    }

    /**
     * 能量建议响应
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "能量建议")
    public static class EnergyAdviceResponse {
        @Schema(description = "五行元素", example = "木")
        private String element;

        @Schema(description = "建议文本", example = "搭配绿色或青色小饰品...")
        private String advice;
    }

    /**
     * 反馈响应
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "用户反馈")
    public static class FeedbackResponse {
        @Schema(description = "是否喜欢", example = "true")
        private Boolean liked;

        @Schema(description = "用户评分", example = "4.5")
        private Double rating;

        @Schema(description = "用户评论", example = "很喜欢这套搭配")
        private String comment;

        @Schema(description = "反馈时间")
        private LocalDateTime createdAt;
    }
} 