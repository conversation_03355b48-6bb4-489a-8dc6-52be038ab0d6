package com.stylishlink.recommendation.service;

import java.util.Map;

/**
 * 用户服务接口 - 用于与用户服务交互
 */
public interface UserService {
    
    /**
     * 获取用户信息
     * @param userId 用户ID
     * @return 用户信息
     */
    Map<String, Object> getUserInfo(String userId);
    
    /**
     * 获取用户偏好设置
     * @param userId 用户ID
     * @return 用户偏好设置
     */
    Map<String, Object> getUserPreferences(String userId);
    
    /**
     * 获取用户五行命理信息
     * @param userId 用户ID
     * @return 用户五行命理信息
     */
    Map<String, Object> getUserWuxingInfo(String userId);
    
    /**
     * 更新用户偏好设置
     * @param userId 用户ID
     * @param preferences 偏好设置
     * @return 更新后的偏好设置
     */
    Map<String, Object> updateUserPreferences(String userId, Map<String, Object> preferences);
    
    /**
     * 验证用户令牌
     * @param token 用户令牌
     * @return 验证结果
     */
    boolean validateToken(String token);
    
    /**
     * 从令牌中提取用户ID
     * @param token 用户令牌
     * @return 用户ID
     */
    String extractUserIdFromToken(String token);
} 