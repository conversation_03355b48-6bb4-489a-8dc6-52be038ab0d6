package com.stylishlink.recommendation.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.stylishlink.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 用户偏好实体
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "recommendation_user_preferences", autoResultMap = true)
public class UserPreference extends BaseEntity {

    @TableField("user_id")
    private Long userId;

    @TableField(value = "style_preferences", typeHandler = JacksonTypeHandler.class)
    private List<String> stylePreferences; // 风格偏好

    @TableField(value = "color_preferences", typeHandler = JacksonTypeHandler.class)
    private List<String> colorPreferences; // 颜色偏好

    @TableField(value = "brand_preferences", typeHandler = JacksonTypeHandler.class)
    private List<String> brandPreferences; // 品牌偏好

    @TableField(value = "occasion_preferences", typeHandler = JacksonTypeHandler.class)
    private List<String> occasionPreferences; // 场合偏好

    @TableField(value = "size_info", typeHandler = JacksonTypeHandler.class)
    private Map<String, String> sizeInfo; // 尺码信息

    @TableField("height")
    private Integer height; // 身高(cm)

    @TableField("weight")
    private Integer weight; // 体重(kg)

    @TableField("body_shape")
    private String bodyShape; // 体型

    @TableField("skin_tone")
    private String skinTone; // 肤色

    @TableField("recommendation_mode")
    private String recommendationMode; // 推荐模式: natural/energy

    @TableField("budget_min")
    private Integer budgetMin; // 预算下限

    @TableField("budget_max")
    private Integer budgetMax; // 预算上限

    @TableField(value = "disliked_styles", typeHandler = JacksonTypeHandler.class)
    private List<String> dislikedStyles; // 不喜欢的风格

    @TableField(value = "disliked_colors", typeHandler = JacksonTypeHandler.class)
    private List<String> dislikedColors; // 不喜欢的颜色

    @TableField(value = "excluded_brands", typeHandler = JacksonTypeHandler.class)
    private List<String> excludedBrands; // 排除的品牌

    @TableField("is_energy_mode_enabled")
    private Boolean isEnergyModeEnabled; // 是否启用能量模式

    @TableField(value = "wuxing_weights", typeHandler = JacksonTypeHandler.class)
    private Map<String, Double> wuxingWeights; // 五行权重配置
} 