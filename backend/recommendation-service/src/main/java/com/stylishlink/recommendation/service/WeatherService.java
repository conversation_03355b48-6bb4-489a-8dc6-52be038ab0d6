package com.stylishlink.recommendation.service;

import com.stylishlink.recommendation.dto.request.OutfitRecommendationRequest.WeatherInfo;

import java.util.Map;

/**
 * 天气服务接口
 */
public interface WeatherService {
    
    /**
     * 获取城市当前天气信息
     * @param city 城市名称
     * @return 天气信息
     */
    WeatherInfo getCurrentWeather(String city);
    
    /**
     * 获取城市未来天气预报
     * @param city 城市名称
     * @param days 天数
     * @return 天气信息
     */
    Map<String, WeatherInfo> getForecastWeather(String city, Integer days);
    
    /**
     * 根据温度获取适合的季节
     * @param temperature 温度
     * @return 季节
     */
    String getSeasonByTemperature(Double temperature);
    
    /**
     * 根据天气条件获取适合的衣物类型
     * @param weatherInfo 天气信息
     * @return 适合的衣物类型
     */
    String[] getSuitableClothingTypes(WeatherInfo weatherInfo);
} 