package com.stylishlink.recommendation.service.impl;

import com.stylishlink.recommendation.dto.request.OutfitRecommendationRequest.WeatherInfo;
import com.stylishlink.recommendation.service.WeatherService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * 天气服务实现类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class WeatherServiceImpl implements WeatherService {

    private final RestTemplate restTemplate;
    
    @Value("${weather.api.key:}")
    private String apiKey;
    
    @Value("${weather.api.url:https://api.weatherapi.com/v1}")
    private String apiUrl;

    @Override
    @Cacheable(value = "currentWeather", key = "#city", unless = "#result == null")
    public WeatherInfo getCurrentWeather(String city) {
        log.debug("获取当前天气，城市: {}", city);
        
        try {
            String url = String.format("%s/current.json?key=%s&q=%s&lang=zh", apiUrl, apiKey, city);
            ResponseEntity<Map> response = restTemplate.getForEntity(url, Map.class);
            
            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                Map<String, Object> data = response.getBody();
                Map<String, Object> current = (Map<String, Object>) data.get("current");
                Map<String, Object> condition = (Map<String, Object>) current.get("condition");
                
                return WeatherInfo.builder()
                    .city(city)
                    .temperature(((Number) current.get("temp_c")).doubleValue())
                    .condition((String) condition.get("text"))
                    .humidity(((Number) current.get("humidity")).intValue())
                    .windSpeed(((Number) current.get("wind_kph")).doubleValue())
                    .precipitation(((Number) current.get("precip_mm")).doubleValue())
                    .build();
            }
        } catch (Exception e) {
            log.error("获取天气信息失败", e);
            // 返回默认天气信息
            return getDefaultWeather(city);
        }
        
        return getDefaultWeather(city);
    }

    @Override
    @Cacheable(value = "forecastWeather", key = "#city + '-' + #days", unless = "#result == null")
    public Map<String, WeatherInfo> getForecastWeather(String city, Integer days) {
        log.debug("获取天气预报，城市: {}, 天数: {}", city, days);
        
        Map<String, WeatherInfo> forecast = new HashMap<>();
        
        try {
            String url = String.format("%s/forecast.json?key=%s&q=%s&days=%d&lang=zh", apiUrl, apiKey, city, days);
            ResponseEntity<Map> response = restTemplate.getForEntity(url, Map.class);
            
            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                Map<String, Object> data = response.getBody();
                Map<String, Object> forecastData = (Map<String, Object>) data.get("forecast");
                
                if (forecastData != null) {
                    Object[] forecastDays = (Object[]) forecastData.get("forecastday");
                    
                    for (Object dayObj : forecastDays) {
                        Map<String, Object> day = (Map<String, Object>) dayObj;
                        String date = (String) day.get("date");
                        Map<String, Object> dayData = (Map<String, Object>) day.get("day");
                        Map<String, Object> condition = (Map<String, Object>) dayData.get("condition");
                        
                        WeatherInfo weatherInfo = WeatherInfo.builder()
                            .city(city)
                            .temperature(((Number) dayData.get("avgtemp_c")).doubleValue())
                            .condition((String) condition.get("text"))
                            .humidity(0) // 预报通常不包含湿度
                            .windSpeed(((Number) dayData.get("maxwind_kph")).doubleValue())
                            .precipitation(((Number) dayData.get("totalprecip_mm")).doubleValue())
                            .build();
                        
                        forecast.put(date, weatherInfo);
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取天气预报失败", e);
        }
        
        // 如果获取失败，至少返回当天天气
        if (forecast.isEmpty()) {
            WeatherInfo currentWeather = getCurrentWeather(city);
            forecast.put("today", currentWeather);
        }
        
        return forecast;
    }

    @Override
    public String getSeasonByTemperature(Double temperature) {
        if (temperature == null) {
            return "春季"; // 默认季节
        }
        
        if (temperature < 5) {
            return "冬季";
        } else if (temperature < 15) {
            return "春季";
        } else if (temperature < 25) {
            return "秋季";
        } else {
            return "夏季";
        }
    }

    @Override
    public String[] getSuitableClothingTypes(WeatherInfo weatherInfo) {
        if (weatherInfo == null) {
            return new String[]{"上衣", "裤子", "鞋子"}; // 默认类别
        }
        
        Double temperature = weatherInfo.getTemperature();
        String condition = weatherInfo.getCondition();
        
        // 根据温度和天气状况推荐衣物类型
        if (temperature < 5) {
            // 寒冷天气
            return new String[]{"羽绒服", "毛衣", "长裤", "靴子", "围巾", "手套"};
        } else if (temperature < 15) {
            // 凉爽天气
            if (condition != null && (condition.contains("雨") || condition.contains("雪"))) {
                return new String[]{"风衣", "夹克", "长裤", "靴子", "雨伞"};
            } else {
                return new String[]{"夹克", "毛衣", "长裤", "休闲鞋"};
            }
        } else if (temperature < 25) {
            // 温和天气
            if (condition != null && condition.contains("雨")) {
                return new String[]{"轻薄外套", "长袖衬衫", "长裤", "休闲鞋", "雨伞"};
            } else {
                return new String[]{"长袖衬衫", "长裤", "休闲鞋"};
            }
        } else {
            // 炎热天气
            if (condition != null && condition.contains("雨")) {
                return new String[]{"短袖", "短裤", "凉鞋", "雨伞"};
            } else {
                return new String[]{"短袖", "短裤", "凉鞋", "太阳帽"};
            }
        }
    }
    
    /**
     * 获取默认天气信息
     */
    private WeatherInfo getDefaultWeather(String city) {
        return WeatherInfo.builder()
            .city(city)
            .temperature(20.0)
            .condition("晴朗")
            .humidity(50)
            .windSpeed(10.0)
            .precipitation(0.0)
            .build();
    }
} 