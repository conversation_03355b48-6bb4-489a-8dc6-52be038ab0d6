package com.stylishlink.recommendation.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.stylishlink.recommendation.entity.UserPreference;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户偏好Repository
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Mapper
public interface UserPreferenceRepository extends BaseMapper<UserPreference> {

    /**
     * 根据用户ID查询偏好
     */
    @Select("SELECT * FROM recommendation_user_preferences WHERE user_id = #{userId} AND status = 1")
    UserPreference selectByUserId(@Param("userId") Long userId);

    /**
     * 查询启用能量模式的用户
     */
    @Select("SELECT * FROM recommendation_user_preferences WHERE is_energy_mode_enabled = true AND status = 1")
    List<UserPreference> selectEnergyModeUsers();

    /**
     * 查询指定推荐模式的用户
     */
    @Select("SELECT * FROM recommendation_user_preferences WHERE recommendation_mode = #{mode} AND status = 1")
    List<UserPreference> selectByRecommendationMode(@Param("mode") String mode);

    /**
     * 统计用户偏好数量
     */
    @Select("SELECT COUNT(*) FROM recommendation_user_preferences WHERE status = 1")
    Long countTotal();

    /**
     * 查询最近更新的用户偏好
     */
    @Select("SELECT * FROM recommendation_user_preferences WHERE updated_at >= #{since} AND status = 1 ORDER BY updated_at DESC")
    List<UserPreference> selectRecentlyUpdated(@Param("since") LocalDateTime since);

    /**
     * 根据体型查询用户偏好
     */
    @Select("SELECT * FROM recommendation_user_preferences WHERE body_shape = #{bodyShape} AND status = 1")
    List<UserPreference> selectByBodyShape(@Param("bodyShape") String bodyShape);

    /**
     * 根据肤色查询用户偏好
     */
    @Select("SELECT * FROM recommendation_user_preferences WHERE skin_tone = #{skinTone} AND status = 1")
    List<UserPreference> selectBySkinTone(@Param("skinTone") String skinTone);

    /**
     * 查询指定身高范围的用户偏好
     */
    @Select("SELECT * FROM recommendation_user_preferences WHERE height BETWEEN #{minHeight} AND #{maxHeight} AND status = 1")
    List<UserPreference> selectByHeightRange(@Param("minHeight") Integer minHeight, @Param("maxHeight") Integer maxHeight);

    /**
     * 查询指定预算范围的用户偏好
     */
    @Select("SELECT * FROM recommendation_user_preferences WHERE budget_max >= #{minBudget} AND budget_min <= #{maxBudget} AND status = 1")
    List<UserPreference> selectByBudgetRange(@Param("minBudget") Integer minBudget, @Param("maxBudget") Integer maxBudget);

    /**
     * 检查用户偏好是否存在
     */
    @Select("SELECT COUNT(*) > 0 FROM recommendation_user_preferences WHERE user_id = #{userId} AND status = 1")
    boolean existsByUserId(@Param("userId") Long userId);
} 