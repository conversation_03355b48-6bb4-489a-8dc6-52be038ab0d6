package com.stylishlink.recommendation.service.impl;

import com.stylishlink.recommendation.client.UserServiceClient;
import com.stylishlink.recommendation.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Map;

/**
 * 用户服务实现类 - 通过Feign客户端与用户服务交互
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class UserServiceImpl implements UserService {

    private final UserServiceClient userServiceClient;

    @Override
    @Cacheable(value = "userInfo", key = "#userId", unless = "#result == null")
    public Map<String, Object> getUserInfo(String userId) {
        log.debug("获取用户信息，用户ID: {}", userId);
        
        try {
            return userServiceClient.getUserInfo(userId);
        } catch (Exception e) {
            log.error("获取用户信息失败", e);
            return Collections.emptyMap();
        }
    }

    @Override
    @Cacheable(value = "userPreferences", key = "#userId", unless = "#result == null")
    public Map<String, Object> getUserPreferences(String userId) {
        log.debug("获取用户偏好设置，用户ID: {}", userId);
        
        try {
            return userServiceClient.getUserPreferences(userId);
        } catch (Exception e) {
            log.error("获取用户偏好设置失败", e);
            return Collections.emptyMap();
        }
    }

    @Override
    @Cacheable(value = "userWuxingInfo", key = "#userId", unless = "#result == null")
    public Map<String, Object> getUserWuxingInfo(String userId) {
        log.debug("获取用户五行命理信息，用户ID: {}", userId);
        
        try {
            return userServiceClient.getUserWuxingInfo(userId);
        } catch (Exception e) {
            log.error("获取用户五行命理信息失败", e);
            return Collections.emptyMap();
        }
    }

    @Override
    @CacheEvict(value = "userPreferences", key = "#userId")
    public Map<String, Object> updateUserPreferences(String userId, Map<String, Object> preferences) {
        log.debug("更新用户偏好设置，用户ID: {}", userId);
        
        try {
            return userServiceClient.updateUserPreferences(userId, preferences);
        } catch (Exception e) {
            log.error("更新用户偏好设置失败", e);
            return Collections.emptyMap();
        }
    }

    @Override
    public boolean validateToken(String token) {
        log.debug("验证用户令牌");
        
        try {
            return userServiceClient.validateToken(token);
        } catch (Exception e) {
            log.error("验证用户令牌失败", e);
            return false;
        }
    }

    @Override
    public String extractUserIdFromToken(String token) {
        log.debug("从令牌中提取用户ID");
        
        try {
            return userServiceClient.extractUserIdFromToken(token);
        } catch (Exception e) {
            log.error("从令牌中提取用户ID失败", e);
            return null;
        }
    }
} 