package com.stylishlink.recommendation.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.stylishlink.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 每日推荐实体
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "recommendation_daily_recommendations", autoResultMap = true)
public class DailyRecommendation extends BaseEntity {

    @TableField("user_id")
    private Long userId;

    @TableField("date")
    private LocalDate date;

    @TableField(value = "outfits", typeHandler = JacksonTypeHandler.class)
    private List<OutfitItem> outfits;

    @TableField("reason")
    private String reason;

    @TableField(value = "weather_info", typeHandler = JacksonTypeHandler.class)
    private WeatherInfo weatherInfo;

    @TableField(value = "wuxing_analysis", typeHandler = JacksonTypeHandler.class)
    private WuxingAnalysis wuxingAnalysis;

    @TableField(value = "energy_advice", typeHandler = JacksonTypeHandler.class)
    private List<EnergyAdvice> energyAdvice;

    @TableField(value = "occasions", typeHandler = JacksonTypeHandler.class)
    private List<String> occasions;

    @TableField("total_score")
    private Double totalScore;

    @TableField(value = "multi_score", typeHandler = JacksonTypeHandler.class)
    private Map<String, Double> multiScore; // energy, occasion, style, temperament

    @TableField("is_viewed")
    private Boolean isViewed;

    @TableField(value = "feedback", typeHandler = JacksonTypeHandler.class)
    private RecommendationFeedback feedback;

    /**
     * 搭配单品
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OutfitItem {
        private String itemId;
        private String itemName;
        private String itemImageUrl;
        private String itemType;
        private String wuxing;
        private String itemDescription;
        private List<String> itemTags;
        private String source; // wardrobe/recommended
        private Integer score;
    }

    /**
     * 天气信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class WeatherInfo {
        private Double temperature;
        private String weatherCondition;
        private Integer humidity;
        private Double windSpeed;
        private Integer uvIndex;
        private String clothingIndex;
    }

    /**
     * 五行分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class WuxingAnalysis {
        private Integer metal;  // 金
        private Integer wood;   // 木
        private Integer water;  // 水
        private Integer fire;   // 火
        private Integer earth;  // 土
        private String summary; // 整体解读
        private List<WuxingDetail> details; // 各元素解析
    }

    /**
     * 五行详情
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class WuxingDetail {
        private String element; // 五行元素
        private Integer value;  // 分值
        private String summary; // 解读文本
    }

    /**
     * 能量建议
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class EnergyAdvice {
        private String element; // 五行元素
        private String advice;  // 建议文本
    }

    /**
     * 推荐反馈
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RecommendationFeedback {
        private Boolean liked;
        private Double rating;
        private String comment;
        private LocalDateTime createdAt;
    }
} 