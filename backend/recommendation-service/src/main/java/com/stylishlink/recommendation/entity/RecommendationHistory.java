package com.stylishlink.recommendation.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.stylishlink.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDate;
import java.util.Map;

/**
 * 推荐历史实体
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "recommendation_histories", autoResultMap = true)
public class RecommendationHistory extends BaseEntity {

    @TableField("user_id")
    private Long userId;

    @TableField("recommendation_type")
    private String recommendationType; // daily/occasion/shopping/travel

    @TableField("recommendation_id")
    private String recommendationId;

    @TableField("date")
    private LocalDate date;

    @TableField("interaction_count")
    private Integer interactionCount; // 交互次数

    @TableField("view_count")
    private Integer viewCount; // 查看次数

    @TableField("like_count")
    private Integer likeCount; // 点赞次数

    @TableField("share_count")
    private Integer shareCount; // 分享次数

    @TableField("feedback_rating")
    private Double feedbackRating; // 反馈评分

    @TableField(value = "feedback_tags", typeHandler = JacksonTypeHandler.class)
    private Map<String, Integer> feedbackTags; // 反馈标签统计

    @TableField("last_interaction_at")
    private java.time.LocalDateTime lastInteractionAt; // 最后交互时间
} 