package com.stylishlink.recommendation.client;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 * 衣橱服务Feign客户端接口
 */
@FeignClient(name = "wardrobe-service", path = "/api/wardrobe", fallbackFactory = WardrobeServiceClientFallbackFactory.class)
public interface WardrobeServiceClient {

    /**
     * 获取用户所有衣物
     * @param userId 用户ID
     * @return 衣物列表
     */
    @GetMapping("/clothing/user/{userId}")
    List<Map<String, Object>> getUserClothing(@PathVariable("userId") String userId);
    
    /**
     * 根据类别获取用户衣物
     * @param userId 用户ID
     * @param category 衣物类别
     * @return 衣物列表
     */
    @GetMapping("/clothing/user/{userId}/category")
    List<Map<String, Object>> getUserClothingByCategory(
        @PathVariable("userId") String userId,
        @RequestParam("category") String category
    );
    
    /**
     * 根据季节获取用户衣物
     * @param userId 用户ID
     * @param season 季节
     * @return 衣物列表
     */
    @GetMapping("/clothing/user/{userId}/season")
    List<Map<String, Object>> getUserClothingBySeason(
        @PathVariable("userId") String userId,
        @RequestParam("season") String season
    );
    
    /**
     * 根据场合获取用户衣物
     * @param userId 用户ID
     * @param occasion 场合
     * @return 衣物列表
     */
    @GetMapping("/clothing/user/{userId}/occasion")
    List<Map<String, Object>> getUserClothingByOccasion(
        @PathVariable("userId") String userId,
        @RequestParam("occasion") String occasion
    );
    
    /**
     * 根据颜色获取用户衣物
     * @param userId 用户ID
     * @param color 颜色
     * @return 衣物列表
     */
    @GetMapping("/clothing/user/{userId}/color")
    List<Map<String, Object>> getUserClothingByColor(
        @PathVariable("userId") String userId,
        @RequestParam("color") String color
    );
    
    /**
     * 获取衣物详情
     * @param clothingId 衣物ID
     * @return 衣物详情
     */
    @GetMapping("/clothing/{clothingId}")
    Map<String, Object> getClothingDetails(@PathVariable("clothingId") String clothingId);
    
    /**
     * 获取用户所有搭配
     * @param userId 用户ID
     * @return 搭配列表
     */
    @GetMapping("/outfit/user/{userId}")
    List<Map<String, Object>> getUserOutfits(@PathVariable("userId") String userId);
    
    /**
     * 获取搭配详情
     * @param outfitId 搭配ID
     * @return 搭配详情
     */
    @GetMapping("/outfit/{outfitId}")
    Map<String, Object> getOutfitDetails(@PathVariable("outfitId") String outfitId);
} 