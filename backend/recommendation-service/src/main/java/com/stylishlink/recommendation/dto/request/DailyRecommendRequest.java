package com.stylishlink.recommendation.dto.request;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import io.swagger.v3.oas.annotations.media.Schema;

import jakarta.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;

/**
 * 每日推荐请求DTO
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "每日推荐请求")
public class DailyRecommendRequest {

    @Schema(description = "推荐日期，不传则为当天", example = "2024-01-01")
    private LocalDate date;

    @Schema(description = "指定场景", example = "[\"商务会议\", \"日常通勤\"]")
    private List<String> occasions;

    @Schema(description = "天气信息（可选，不传则自动获取）")
    private WeatherRequest weather;

    @Schema(description = "推荐模式", example = "energy", allowableValues = {"natural", "energy"})
    private String mode;

    @Schema(description = "是否强制重新生成", example = "false")
    private Boolean forceRegenerate;

    /**
     * 天气信息请求
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "天气信息")
    public static class WeatherRequest {
        @Schema(description = "温度", example = "25.5")
        private Double temperature;

        @Schema(description = "天气状况", example = "晴")
        private String condition;

        @Schema(description = "湿度", example = "60")
        private Integer humidity;

        @Schema(description = "紫外线指数", example = "5")
        private Integer uvIndex;
    }
} 