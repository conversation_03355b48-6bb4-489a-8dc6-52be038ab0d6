package com.stylishlink.recommendation.service;

import java.util.List;
import java.util.Map;

/**
 * 衣物服务接口 - 用于与衣橱服务交互
 */
public interface ClothingService {
    
    /**
     * 获取用户所有衣物
     * @param userId 用户ID
     * @return 衣物列表
     */
    List<Map<String, Object>> getUserClothing(String userId);
    
    /**
     * 根据类别获取用户衣物
     * @param userId 用户ID
     * @param category 衣物类别
     * @return 衣物列表
     */
    List<Map<String, Object>> getUserClothingByCategory(String userId, String category);
    
    /**
     * 根据季节获取用户衣物
     * @param userId 用户ID
     * @param season 季节
     * @return 衣物列表
     */
    List<Map<String, Object>> getUserClothingBySeason(String userId, String season);
    
    /**
     * 根据场合获取用户衣物
     * @param userId 用户ID
     * @param occasion 场合
     * @return 衣物列表
     */
    List<Map<String, Object>> getUserClothingByOccasion(String userId, String occasion);
    
    /**
     * 根据颜色获取用户衣物
     * @param userId 用户ID
     * @param color 颜色
     * @return 衣物列表
     */
    List<Map<String, Object>> getUserClothingByColor(String userId, String color);
    
    /**
     * 获取衣物详情
     * @param clothingId 衣物ID
     * @return 衣物详情
     */
    Map<String, Object> getClothingDetails(String clothingId);
    
    /**
     * 获取用户所有搭配
     * @param userId 用户ID
     * @return 搭配列表
     */
    List<Map<String, Object>> getUserOutfits(String userId);
    
    /**
     * 获取搭配详情
     * @param outfitId 搭配ID
     * @return 搭配详情
     */
    Map<String, Object> getOutfitDetails(String outfitId);
} 