package com.stylishlink.recommendation.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 穿搭推荐响应DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OutfitRecommendationResponse {
    
    private String id;
    private String userId;
    private String name;
    private String description;
    
    private List<OutfitItem> items;
    private String occasion;
    private List<String> seasons;
    private List<String> styles;
    
    private Map<String, Double> wuxingScores;
    private Double totalScore;
    
    private String mainImageUrl;
    private List<String> imageUrls;
    
    private Boolean isAiGenerated;
    private String reasoningExplanation;
    
    private LocalDateTime createdAt;
    
    /**
     * 搭配项
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OutfitItem {
        private String type;      // clothing or accessory
        private String itemId;    // 衣物或饰品ID
        private String category;  // 类别（上衣、裤子等）
        private String name;      // 名称
        private String imageUrl;  // 图片URL
        private String color;     // 颜色
        private String material;  // 材质
        private String pattern;   // 图案
        private Map<String, Double> wuxingAttributes; // 五行属性得分
        
        /**
         * 获取类型
         */
        public String getType() {
            return type;
        }
        
        /**
         * 设置类型
         */
        public void setType(String type) {
            this.type = type;
        }
    }
} 