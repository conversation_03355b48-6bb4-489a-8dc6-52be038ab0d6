package com.stylishlink.recommendation.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.stylishlink.recommendation.entity.DailyRecommendation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 每日推荐Repository
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Mapper
public interface DailyRecommendationRepository extends BaseMapper<DailyRecommendation> {

    /**
     * 根据用户ID和日期查询推荐
     */
    @Select("SELECT * FROM recommendation_daily_recommendations WHERE user_id = #{userId} AND date = #{date} AND status = 1")
    DailyRecommendation selectByUserIdAndDate(@Param("userId") Long userId, @Param("date") LocalDate date);

    /**
     * 查询用户指定日期范围内的推荐
     */
    @Select("SELECT * FROM recommendation_daily_recommendations WHERE user_id = #{userId} AND date BETWEEN #{startDate} AND #{endDate} AND status = 1 ORDER BY date DESC")
    List<DailyRecommendation> selectByUserIdAndDateBetween(
        @Param("userId") Long userId, 
        @Param("startDate") LocalDate startDate, 
        @Param("endDate") LocalDate endDate
    );

    /**
     * 查询用户最近的推荐记录
     */
    @Select("SELECT * FROM recommendation_daily_recommendations WHERE user_id = #{userId} AND status = 1 ORDER BY date DESC LIMIT #{limit}")
    List<DailyRecommendation> selectRecentByUserId(@Param("userId") Long userId, @Param("limit") Integer limit);

    /**
     * 查询用户已查看的推荐数量
     */
    @Select("SELECT COUNT(*) FROM recommendation_daily_recommendations WHERE user_id = #{userId} AND is_viewed = true AND status = 1")
    Long countViewedByUserId(@Param("userId") Long userId);

    /**
     * 查询用户有反馈的推荐
     */
    @Select("SELECT * FROM recommendation_daily_recommendations WHERE user_id = #{userId} AND feedback IS NOT NULL AND status = 1")
    List<DailyRecommendation> selectByUserIdWithFeedback(@Param("userId") Long userId);

    /**
     * 查询指定时间范围内的推荐
     */
    @Select("SELECT * FROM recommendation_daily_recommendations WHERE created_at BETWEEN #{startTime} AND #{endTime} AND status = 1")
    List<DailyRecommendation> selectByCreatedAtBetween(
        @Param("startTime") LocalDateTime startTime, 
        @Param("endTime") LocalDateTime endTime
    );

    /**
     * 批量更新查看状态
     */
    @Update("UPDATE recommendation_daily_recommendations SET is_viewed = #{isViewed}, updated_at = NOW() WHERE id IN (${ids}) AND status = 1")
    int updateViewedStatusByIds(@Param("ids") String ids, @Param("isViewed") Boolean isViewed);

    /**
     * 分页查询用户推荐记录
     */
    @Select("SELECT * FROM recommendation_daily_recommendations WHERE user_id = #{userId} AND status = 1 ORDER BY created_at DESC")
    Page<DailyRecommendation> selectPageByUserId(Page<DailyRecommendation> page, @Param("userId") Long userId);

    /**
     * 统计用户推荐总数
     */
    @Select("SELECT COUNT(*) FROM recommendation_daily_recommendations WHERE user_id = #{userId} AND status = 1")
    Long countByUserId(@Param("userId") Long userId);

    /**
     * 查询用户最新推荐
     */
    @Select("SELECT * FROM recommendation_daily_recommendations WHERE user_id = #{userId} AND status = 1 ORDER BY created_at DESC LIMIT 1")
    DailyRecommendation selectLatestByUserId(@Param("userId") Long userId);
} 