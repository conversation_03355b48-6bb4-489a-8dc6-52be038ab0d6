package com.stylishlink.recommendation.client;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.Map;

/**
 * 用户服务Feign客户端接口
 */
@FeignClient(name = "user-service", path = "/api/user", fallbackFactory = UserServiceClientFallbackFactory.class)
public interface UserServiceClient {

    /**
     * 获取用户信息
     * @param userId 用户ID
     * @return 用户信息
     */
    @GetMapping("/{userId}")
    Map<String, Object> getUserInfo(@PathVariable("userId") String userId);
    
    /**
     * 获取用户偏好设置
     * @param userId 用户ID
     * @return 用户偏好设置
     */
    @GetMapping("/{userId}/preferences")
    Map<String, Object> getUserPreferences(@PathVariable("userId") String userId);
    
    /**
     * 获取用户五行命理信息
     * @param userId 用户ID
     * @return 用户五行命理信息
     */
    @GetMapping("/{userId}/wuxing")
    Map<String, Object> getUserWuxingInfo(@PathVariable("userId") String userId);
    
    /**
     * 更新用户偏好设置
     * @param userId 用户ID
     * @param preferences 偏好设置
     * @return 更新后的偏好设置
     */
    @PostMapping("/{userId}/preferences")
    Map<String, Object> updateUserPreferences(
        @PathVariable("userId") String userId,
        @RequestBody Map<String, Object> preferences
    );
    
    /**
     * 验证用户令牌
     * @param token 用户令牌
     * @return 验证结果
     */
    @GetMapping("/auth/validate")
    boolean validateToken(@RequestHeader("Authorization") String token);
    
    /**
     * 从令牌中提取用户ID
     * @param token 用户令牌
     * @return 用户ID
     */
    @GetMapping("/auth/extract-user-id")
    String extractUserIdFromToken(@RequestHeader("Authorization") String token);
} 