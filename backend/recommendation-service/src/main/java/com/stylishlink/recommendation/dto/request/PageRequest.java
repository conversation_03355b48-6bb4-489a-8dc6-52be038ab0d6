package com.stylishlink.recommendation.dto.request;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.domain.Sort;

/**
 * 分页请求DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PageRequest {
    
    @Min(value = 0, message = "页码不能小于0")
    private Integer page = 0;
    
    @Min(value = 1, message = "每页大小不能小于1")
    @Max(value = 100, message = "每页大小不能大于100")
    private Integer size = 10;
    
    private String sortBy;
    private String sortDirection;
    
    /**
     * 转换为Spring Data分页对象
     * @return Spring Data分页对象
     */
    public org.springframework.data.domain.PageRequest toPageable() {
        Sort sort = Sort.unsorted();
        
        if (sortBy != null && !sortBy.isEmpty()) {
            sort = Sort.Direction.DESC.name().equalsIgnoreCase(sortDirection)
                    ? Sort.by(sortBy).descending()
                    : Sort.by(sortBy).ascending();
        }
        
        return org.springframework.data.domain.PageRequest.of(page, size, sort);
    }
} 