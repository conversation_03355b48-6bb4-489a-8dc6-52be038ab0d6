-- 社交服务数据库初始化脚本
-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS stylishlink DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE stylishlink;

-- 分享记录表
CREATE TABLE IF NOT EXISTS social_share (
    id VARCHAR(64) NOT NULL COMMENT '主键ID',
    user_id VARCHAR(64) NOT NULL COMMENT '用户ID',
    content_type VARCHAR(32) NOT NULL COMMENT '内容类型（outfit-搭配, recommendation-推荐, activity-活动等）',
    content_id VARCHAR(64) NOT NULL COMMENT '内容ID',
    platform VARCHAR(32) NOT NULL COMMENT '分享平台（wechat-微信, weibo-微博, qq-QQ等）',
    share_url VARCHAR(512) COMMENT '分享URL',
    image_url VARCHAR(512) COMMENT '分享图片URL',
    description VARCHAR(500) COMMENT '分享描述',
    title VARCHAR(100) COMMENT '分享标题',
    view_count INT DEFAULT 0 COMMENT '查看次数',
    like_count INT DEFAULT 0 COMMENT '点赞次数',
    comment_count INT DEFAULT 0 COMMENT '评论次数',
    forward_count INT DEFAULT 0 COMMENT '转发次数',
    status INT DEFAULT 0 COMMENT '分享状态（0-正常, 1-已删除, 2-已屏蔽）',
    deleted INT DEFAULT 0 COMMENT '是否删除（逻辑删除）',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id),
    INDEX idx_user_id (user_id),
    INDEX idx_content (content_type, content_id),
    INDEX idx_platform (platform),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分享记录表';

-- 点赞记录表
CREATE TABLE IF NOT EXISTS social_like (
    id VARCHAR(64) NOT NULL COMMENT '主键ID',
    user_id VARCHAR(64) NOT NULL COMMENT '用户ID',
    content_type VARCHAR(32) NOT NULL COMMENT '内容类型（outfit-搭配, recommendation-推荐, share-分享, comment-评论等）',
    content_id VARCHAR(64) NOT NULL COMMENT '内容ID',
    content_owner_id VARCHAR(64) COMMENT '内容所有者ID',
    like_status INT DEFAULT 1 COMMENT '点赞状态（1-点赞, 0-取消点赞）',
    deleted INT DEFAULT 0 COMMENT '是否删除（逻辑删除）',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id),
    UNIQUE KEY uk_user_content (user_id, content_type, content_id),
    INDEX idx_content (content_type, content_id),
    INDEX idx_content_owner (content_owner_id),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='点赞记录表';

-- 收藏夹表
CREATE TABLE IF NOT EXISTS social_collection (
    id VARCHAR(64) NOT NULL COMMENT '主键ID',
    user_id VARCHAR(64) NOT NULL COMMENT '用户ID',
    name VARCHAR(50) NOT NULL COMMENT '收藏夹名称',
    description VARCHAR(200) COMMENT '收藏夹描述',
    cover_image_url VARCHAR(512) COMMENT '封面图片URL',
    is_public INT DEFAULT 0 COMMENT '是否公开（1-公开, 0-私有）',
    item_count INT DEFAULT 0 COMMENT '收藏项目数量',
    type VARCHAR(32) DEFAULT 'mixed' COMMENT '收藏夹类型（outfit-搭配, recommendation-推荐, mixed-混合等）',
    sort_order INT DEFAULT 0 COMMENT '排序权重',
    status INT DEFAULT 0 COMMENT '收藏夹状态（0-正常, 1-已删除）',
    deleted INT DEFAULT 0 COMMENT '是否删除（逻辑删除）',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id),
    INDEX idx_user_id (user_id),
    INDEX idx_type (type),
    INDEX idx_sort_order (sort_order),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='收藏夹表';

-- 收藏项目表
CREATE TABLE IF NOT EXISTS social_collection_item (
    id VARCHAR(64) NOT NULL COMMENT '主键ID',
    collection_id VARCHAR(64) NOT NULL COMMENT '收藏夹ID',
    user_id VARCHAR(64) NOT NULL COMMENT '用户ID',
    content_type VARCHAR(32) NOT NULL COMMENT '内容类型（outfit-搭配, recommendation-推荐, share-分享等）',
    content_id VARCHAR(64) NOT NULL COMMENT '内容ID',
    content_owner_id VARCHAR(64) COMMENT '内容所有者ID',
    note VARCHAR(200) COMMENT '收藏备注',
    tags VARCHAR(200) COMMENT '收藏标签（多个标签用逗号分隔）',
    sort_order INT DEFAULT 0 COMMENT '排序权重',
    status INT DEFAULT 0 COMMENT '收藏状态（0-正常, 1-已移除）',
    deleted INT DEFAULT 0 COMMENT '是否删除（逻辑删除）',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id),
    UNIQUE KEY uk_collection_content (collection_id, content_type, content_id),
    INDEX idx_user_id (user_id),
    INDEX idx_content (content_type, content_id),
    INDEX idx_content_owner (content_owner_id),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='收藏项目表';

-- 评论表
CREATE TABLE IF NOT EXISTS social_comment (
    id VARCHAR(64) NOT NULL COMMENT '主键ID',
    user_id VARCHAR(64) NOT NULL COMMENT '用户ID',
    content_type VARCHAR(32) NOT NULL COMMENT '内容类型（outfit-搭配, recommendation-推荐, share-分享等）',
    content_id VARCHAR(64) NOT NULL COMMENT '内容ID',
    content_owner_id VARCHAR(64) COMMENT '内容所有者ID',
    parent_id VARCHAR(64) COMMENT '父评论ID（用于回复评论）',
    root_id VARCHAR(64) COMMENT '根评论ID（用于多级评论）',
    reply_to_user_id VARCHAR(64) COMMENT '回复目标用户ID',
    content TEXT NOT NULL COMMENT '评论内容',
    images VARCHAR(1000) COMMENT '评论图片URL（多个用逗号分隔）',
    like_count INT DEFAULT 0 COMMENT '点赞数',
    reply_count INT DEFAULT 0 COMMENT '回复数',
    level INT DEFAULT 1 COMMENT '评论层级（1-一级评论, 2-二级评论等）',
    audit_status INT DEFAULT 1 COMMENT '审核状态（0-待审核, 1-审核通过, 2-审核拒绝）',
    audit_time DATETIME COMMENT '审核时间',
    audit_remark VARCHAR(200) COMMENT '审核备注',
    status INT DEFAULT 0 COMMENT '评论状态（0-正常, 1-已删除, 2-已屏蔽）',
    deleted INT DEFAULT 0 COMMENT '是否删除（逻辑删除）',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id),
    INDEX idx_user_id (user_id),
    INDEX idx_content (content_type, content_id),
    INDEX idx_parent_id (parent_id),
    INDEX idx_root_id (root_id),
    INDEX idx_content_owner (content_owner_id),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='评论表';

-- 用户互动统计表
CREATE TABLE IF NOT EXISTS social_user_interaction_stats (
    id VARCHAR(64) NOT NULL COMMENT '主键ID',
    user_id VARCHAR(64) NOT NULL COMMENT '用户ID',
    like_count INT DEFAULT 0 COMMENT '点赞数（用户发出的点赞）',
    liked_count INT DEFAULT 0 COMMENT '被点赞数（用户收到的点赞）',
    share_count INT DEFAULT 0 COMMENT '分享数（用户发出的分享）',
    shared_count INT DEFAULT 0 COMMENT '被分享数（用户内容被分享）',
    collection_count INT DEFAULT 0 COMMENT '收藏数（用户发出的收藏）',
    collected_count INT DEFAULT 0 COMMENT '被收藏数（用户内容被收藏）',
    comment_count INT DEFAULT 0 COMMENT '评论数（用户发出的评论）',
    commented_count INT DEFAULT 0 COMMENT '被评论数（用户内容被评论）',
    follow_count INT DEFAULT 0 COMMENT '关注数（用户关注的人数）',
    follower_count INT DEFAULT 0 COMMENT '粉丝数（关注用户的人数）',
    average_rating DECIMAL(3,2) DEFAULT 0.00 COMMENT '平均评分',
    total_interactions INT DEFAULT 0 COMMENT '总互动数（所有互动的总和）',
    activity_score DECIMAL(10,2) DEFAULT 0.00 COMMENT '活跃度评分',
    influence_score DECIMAL(10,2) DEFAULT 0.00 COMMENT '影响力评分',
    today_like_rewards INT DEFAULT 0 COMMENT '今日点赞奖励次数',
    today_comment_rewards INT DEFAULT 0 COMMENT '今日评论奖励次数',
    today_share_rewards INT DEFAULT 0 COMMENT '今日分享奖励次数',
    today_collection_rewards INT DEFAULT 0 COMMENT '今日收藏奖励次数',
    reward_reset_date DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '奖励重置日期',
    deleted INT DEFAULT 0 COMMENT '是否删除（逻辑删除）',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id),
    UNIQUE KEY uk_user_id (user_id),
    INDEX idx_activity_score (activity_score),
    INDEX idx_influence_score (influence_score),
    INDEX idx_reward_reset_date (reward_reset_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户互动统计表';

-- 插入默认数据
-- 为测试用户创建默认收藏夹
INSERT IGNORE INTO social_collection (id, user_id, name, description, type, is_public, created_at, updated_at) VALUES
('default_collection_001', 'test_user_001', '我的收藏', '默认收藏夹', 'mixed', 0, NOW(), NOW()),
('default_collection_002', 'test_user_002', '最爱搭配', '收藏喜欢的搭配', 'outfit', 1, NOW(), NOW());

-- 为测试用户初始化互动统计
INSERT IGNORE INTO social_user_interaction_stats (id, user_id, created_at, updated_at) VALUES
('stats_001', 'test_user_001', NOW(), NOW()),
('stats_002', 'test_user_002', NOW(), NOW()); 