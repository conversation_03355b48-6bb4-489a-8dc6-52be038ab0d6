package com.stylishlink.social.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 收藏夹实体
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("social_collection")
public class SocialCollection implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private String userId;

    /**
     * 收藏夹名称
     */
    @TableField("name")
    private String name;

    /**
     * 收藏夹描述
     */
    @TableField("description")
    private String description;

    /**
     * 收藏夹类型（outfit-搭配, recommendation-推荐, mixed-混合等）
     */
    @TableField("type")
    private String type;

    /**
     * 是否公开（0-私有, 1-公开）
     */
    @TableField("is_public")
    private Integer isPublic;

    /**
     * 收藏项目数量
     */
    @TableField("item_count")
    private Integer itemCount;

    /**
     * 排序顺序
     */
    @TableField("sort_order")
    private Integer sortOrder;

    /**
     * 收藏夹状态（0-正常, 1-已删除）
     */
    @TableField("status")
    private Integer status;

    /**
     * 是否删除（逻辑删除）
     */
    @TableLogic
    @TableField("deleted")
    private Integer deleted;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
} 