package com.stylishlink.social.dto.request;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 点赞请求DTO
 */
@Data
public class LikeRequest {

    /**
     * 内容类型（outfit-搭配, recommendation-推荐, share-分享, comment-评论等）
     */
    @NotBlank(message = "内容类型不能为空")
    private String contentType;

    /**
     * 内容ID
     */
    @NotBlank(message = "内容ID不能为空")
    private String contentId;

    /**
     * 点赞状态（1-点赞, 0-取消点赞）
     */
    @NotNull(message = "点赞状态不能为空")
    private Integer likeStatus;
} 