package com.stylishlink.social.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.SecurityFilterChain;

/**
 * 社交服务安全配置
 */
@Configuration
@EnableWebSecurity
public class SecurityConfig {

    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        http
            // 禁用CSRF
            .csrf(AbstractHttpConfigurer::disable)
            
            // 配置会话管理为无状态
            .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
            
            // 配置请求授权
            .authorizeHttpRequests(authz -> authz
                // SpringDoc相关路径允许访问
                .requestMatchers(
                    "/v3/api-docs/**",
                    "/swagger-ui/**",
                    "/swagger-ui.html",
                    "/swagger-config", // 兼容旧版或某些特定swagger客户端可能请求的路径
                    "/webjars/**"      // Swagger UI的静态资源
                ).permitAll()
                
                // Actuator健康检查等端点
                .requestMatchers("/actuator/**").permitAll()
                
                // 其他所有请求都需要认证 (可以根据实际业务需求调整，例如某些公开查询接口)
                .anyRequest().authenticated()
            )
            
            // 禁用HTTP Basic认证 (如果API主要通过JWT等Token机制保护)
            .httpBasic(AbstractHttpConfigurer::disable)
            
            // 禁用表单登录 (如果API服务不需要用户通过表单登录)
            .formLogin(AbstractHttpConfigurer::disable);

        return http.build();
    }
} 