package com.stylishlink.social.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 分享记录实体
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("social_share")
public class Share implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private String userId;

    /**
     * 内容类型（outfit-搭配, recommendation-推荐, activity-活动等）
     */
    @TableField("content_type")
    private String contentType;

    /**
     * 内容ID
     */
    @TableField("content_id")
    private String contentId;

    /**
     * 分享平台（wechat-微信, weibo-微博, qq-QQ等）
     */
    @TableField("platform")
    private String platform;

    /**
     * 分享URL
     */
    @TableField("share_url")
    private String shareUrl;

    /**
     * 分享图片URL
     */
    @TableField("image_url")
    private String imageUrl;

    /**
     * 分享描述
     */
    @TableField("description")
    private String description;

    /**
     * 分享标题
     */
    @TableField("title")
    private String title;

    /**
     * 查看次数
     */
    @TableField("view_count")
    private Integer viewCount;

    /**
     * 点赞次数
     */
    @TableField("like_count")
    private Integer likeCount;

    /**
     * 评论次数
     */
    @TableField("comment_count")
    private Integer commentCount;

    /**
     * 转发次数
     */
    @TableField("forward_count")
    private Integer forwardCount;

    /**
     * 分享状态（0-正常, 1-已删除, 2-已屏蔽）
     */
    @TableField("status")
    private Integer status;

    /**
     * 是否删除（逻辑删除）
     */
    @TableLogic
    @TableField("deleted")
    private Integer deleted;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
} 