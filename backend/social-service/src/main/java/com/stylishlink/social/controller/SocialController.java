package com.stylishlink.social.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.stylishlink.social.common.ApiResponse;
import com.stylishlink.social.dto.request.*;
import com.stylishlink.social.dto.response.*;
import com.stylishlink.social.service.SocialService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 社交功能控制器
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/social")
public class SocialController {

    private final SocialService socialService;

    // ==================== 分享相关 ====================

    /**
     * 创建分享记录
     */
    @PostMapping("/shares")
    public ApiResponse<ShareResponse> createShare(
            @RequestHeader("X-User-Id") String userId,
            @Valid @RequestBody ShareRequest request) {
        try {
            log.info("用户 {} 创建分享记录: {}", userId, request);
            ShareResponse response = socialService.createShare(userId, request);
            return ApiResponse.success(response);
        } catch (Exception e) {
            log.error("创建分享记录失败", e);
            return ApiResponse.error("创建分享记录失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户分享列表
     */
    @GetMapping("/shares")
    public ApiResponse<Page<ShareResponse>> getUserShares(
            @RequestHeader("X-User-Id") String userId,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size) {
        try {
            log.info("获取用户 {} 分享列表，页码: {}, 大小: {}", userId, page, size);
            Page<ShareResponse> response = socialService.getUserShares(userId, page, size);
            return ApiResponse.success(response);
        } catch (Exception e) {
            log.error("获取用户分享列表失败", e);
            return ApiResponse.error("获取分享列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取分享详情
     */
    @GetMapping("/shares/{shareId}")
    public ApiResponse<ShareResponse> getShareById(@PathVariable String shareId) {
        try {
            log.info("获取分享详情: {}", shareId);
            ShareResponse response = socialService.getShareById(shareId);
            return ApiResponse.success(response);
        } catch (Exception e) {
            log.error("获取分享详情失败", e);
            return ApiResponse.error("获取分享详情失败: " + e.getMessage());
        }
    }

    /**
     * 删除分享记录
     */
    @DeleteMapping("/shares/{shareId}")
    public ApiResponse<Boolean> deleteShare(
            @RequestHeader("X-User-Id") String userId,
            @PathVariable String shareId) {
        try {
            log.info("用户 {} 删除分享记录: {}", userId, shareId);
            boolean result = socialService.deleteShare(userId, shareId);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("删除分享记录失败", e);
            return ApiResponse.error("删除分享记录失败: " + e.getMessage());
        }
    }

    // ==================== 点赞相关 ====================

    /**
     * 点赞/取消点赞
     */
    @PostMapping("/likes")
    public ApiResponse<LikeResponse> toggleLike(
            @RequestHeader("X-User-Id") String userId,
            @Valid @RequestBody LikeRequest request) {
        try {
            log.info("用户 {} 点赞操作: {}", userId, request);
            LikeResponse response = socialService.toggleLike(userId, request);
            return ApiResponse.success(response);
        } catch (Exception e) {
            log.error("点赞操作失败", e);
            return ApiResponse.error("点赞操作失败: " + e.getMessage());
        }
    }

    /**
     * 获取内容点赞列表
     */
    @GetMapping("/likes/content")
    public ApiResponse<Page<LikeResponse>> getContentLikes(
            @RequestParam String contentType,
            @RequestParam String contentId,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size) {
        try {
            log.info("获取内容点赞列表: {} - {}", contentType, contentId);
            Page<LikeResponse> response = socialService.getContentLikes(contentType, contentId, page, size);
            return ApiResponse.success(response);
        } catch (Exception e) {
            log.error("获取内容点赞列表失败", e);
            return ApiResponse.error("获取点赞列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户点赞列表
     */
    @GetMapping("/likes")
    public ApiResponse<Page<LikeResponse>> getUserLikes(
            @RequestHeader("X-User-Id") String userId,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size) {
        try {
            log.info("获取用户 {} 点赞列表", userId);
            Page<LikeResponse> response = socialService.getUserLikes(userId, page, size);
            return ApiResponse.success(response);
        } catch (Exception e) {
            log.error("获取用户点赞列表失败", e);
            return ApiResponse.error("获取点赞列表失败: " + e.getMessage());
        }
    }

    /**
     * 检查用户是否已点赞
     */
    @GetMapping("/likes/check")
    public ApiResponse<Boolean> isUserLiked(
            @RequestHeader("X-User-Id") String userId,
            @RequestParam String contentType,
            @RequestParam String contentId) {
        try {
            log.info("检查用户 {} 是否已点赞: {} - {}", userId, contentType, contentId);
            boolean result = socialService.isUserLiked(userId, contentType, contentId);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("检查点赞状态失败", e);
            return ApiResponse.error("检查点赞状态失败: " + e.getMessage());
        }
    }

    // ==================== 收藏相关 ====================

    /**
     * 创建收藏夹
     */
    @PostMapping("/collections")
    public ApiResponse<CollectionResponse> createCollection(
            @RequestHeader("X-User-Id") String userId,
            @Valid @RequestBody CollectionRequest request) {
        try {
            log.info("用户 {} 创建收藏夹: {}", userId, request);
            CollectionResponse response = socialService.createCollection(userId, request);
            return ApiResponse.success(response);
        } catch (Exception e) {
            log.error("创建收藏夹失败", e);
            return ApiResponse.error("创建收藏夹失败: " + e.getMessage());
        }
    }

    /**
     * 更新收藏夹
     */
    @PutMapping("/collections/{collectionId}")
    public ApiResponse<CollectionResponse> updateCollection(
            @RequestHeader("X-User-Id") String userId,
            @PathVariable String collectionId,
            @Valid @RequestBody CollectionRequest request) {
        try {
            log.info("用户 {} 更新收藏夹 {}: {}", userId, collectionId, request);
            CollectionResponse response = socialService.updateCollection(userId, collectionId, request);
            return ApiResponse.success(response);
        } catch (Exception e) {
            log.error("更新收藏夹失败", e);
            return ApiResponse.error("更新收藏夹失败: " + e.getMessage());
        }
    }

    /**
     * 删除收藏夹
     */
    @DeleteMapping("/collections/{collectionId}")
    public ApiResponse<Boolean> deleteCollection(
            @RequestHeader("X-User-Id") String userId,
            @PathVariable String collectionId) {
        try {
            log.info("用户 {} 删除收藏夹: {}", userId, collectionId);
            boolean result = socialService.deleteCollection(userId, collectionId);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("删除收藏夹失败", e);
            return ApiResponse.error("删除收藏夹失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户收藏夹列表
     */
    @GetMapping("/collections")
    public ApiResponse<List<CollectionResponse>> getUserCollections(
            @RequestHeader("X-User-Id") String userId) {
        try {
            log.info("获取用户 {} 收藏夹列表", userId);
            List<CollectionResponse> response = socialService.getUserCollections(userId);
            return ApiResponse.success(response);
        } catch (Exception e) {
            log.error("获取收藏夹列表失败", e);
            return ApiResponse.error("获取收藏夹列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取收藏夹详情
     */
    @GetMapping("/collections/{collectionId}")
    public ApiResponse<CollectionResponse> getCollectionById(@PathVariable String collectionId) {
        try {
            log.info("获取收藏夹详情: {}", collectionId);
            CollectionResponse response = socialService.getCollectionById(collectionId);
            return ApiResponse.success(response);
        } catch (Exception e) {
            log.error("获取收藏夹详情失败", e);
            return ApiResponse.error("获取收藏夹详情失败: " + e.getMessage());
        }
    }

    /**
     * 添加收藏项目
     */
    @PostMapping("/collections/items")
    public ApiResponse<Boolean> addCollectionItem(
            @RequestHeader("X-User-Id") String userId,
            @Valid @RequestBody CollectionItemRequest request) {
        try {
            log.info("用户 {} 添加收藏项目: {}", userId, request);
            boolean result = socialService.addCollectionItem(userId, request);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("添加收藏项目失败", e);
            return ApiResponse.error("添加收藏项目失败: " + e.getMessage());
        }
    }

    /**
     * 移除收藏项目
     */
    @DeleteMapping("/collections/{collectionId}/items/{contentId}")
    public ApiResponse<Boolean> removeCollectionItem(
            @RequestHeader("X-User-Id") String userId,
            @PathVariable String collectionId,
            @PathVariable String contentId) {
        try {
            log.info("用户 {} 移除收藏项目: {} - {}", userId, collectionId, contentId);
            boolean result = socialService.removeCollectionItem(userId, collectionId, contentId);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("移除收藏项目失败", e);
            return ApiResponse.error("移除收藏项目失败: " + e.getMessage());
        }
    }

    /**
     * 获取收藏夹内容列表
     */
    @GetMapping("/collections/{collectionId}/items")
    public ApiResponse<Page<Object>> getCollectionItems(
            @PathVariable String collectionId,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size) {
        try {
            log.info("获取收藏夹 {} 内容列表", collectionId);
            Page<Object> response = socialService.getCollectionItems(collectionId, page, size);
            return ApiResponse.success(response);
        } catch (Exception e) {
            log.error("获取收藏夹内容失败", e);
            return ApiResponse.error("获取收藏夹内容失败: " + e.getMessage());
        }
    }

    /**
     * 检查内容是否已收藏
     */
    @GetMapping("/collections/check")
    public ApiResponse<Boolean> isContentCollected(
            @RequestHeader("X-User-Id") String userId,
            @RequestParam String contentType,
            @RequestParam String contentId) {
        try {
            log.info("检查用户 {} 是否已收藏: {} - {}", userId, contentType, contentId);
            boolean result = socialService.isContentCollected(userId, contentType, contentId);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("检查收藏状态失败", e);
            return ApiResponse.error("检查收藏状态失败: " + e.getMessage());
        }
    }

    // ==================== 评论相关 ====================

    /**
     * 发表评论
     */
    @PostMapping("/comments")
    public ApiResponse<CommentResponse> createComment(
            @RequestHeader("X-User-Id") String userId,
            @Valid @RequestBody CommentRequest request) {
        try {
            log.info("用户 {} 发表评论: {}", userId, request);
            CommentResponse response = socialService.createComment(userId, request);
            return ApiResponse.success(response);
        } catch (Exception e) {
            log.error("发表评论失败", e);
            return ApiResponse.error("发表评论失败: " + e.getMessage());
        }
    }

    /**
     * 删除评论
     */
    @DeleteMapping("/comments/{commentId}")
    public ApiResponse<Boolean> deleteComment(
            @RequestHeader("X-User-Id") String userId,
            @PathVariable String commentId) {
        try {
            log.info("用户 {} 删除评论: {}", userId, commentId);
            boolean result = socialService.deleteComment(userId, commentId);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("删除评论失败", e);
            return ApiResponse.error("删除评论失败: " + e.getMessage());
        }
    }

    /**
     * 获取内容评论列表
     */
    @GetMapping("/comments/content")
    public ApiResponse<Page<CommentResponse>> getContentComments(
            @RequestParam String contentType,
            @RequestParam String contentId,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size) {
        try {
            log.info("获取内容评论列表: {} - {}", contentType, contentId);
            Page<CommentResponse> response = socialService.getContentComments(contentType, contentId, page, size);
            return ApiResponse.success(response);
        } catch (Exception e) {
            log.error("获取内容评论列表失败", e);
            return ApiResponse.error("获取评论列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取评论回复列表
     */
    @GetMapping("/comments/{commentId}/replies")
    public ApiResponse<List<CommentResponse>> getCommentReplies(@PathVariable String commentId) {
        try {
            log.info("获取评论回复列表: {}", commentId);
            List<CommentResponse> response = socialService.getCommentReplies(commentId);
            return ApiResponse.success(response);
        } catch (Exception e) {
            log.error("获取评论回复列表失败", e);
            return ApiResponse.error("获取回复列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户评论列表
     */
    @GetMapping("/comments")
    public ApiResponse<Page<CommentResponse>> getUserComments(
            @RequestHeader("X-User-Id") String userId,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size) {
        try {
            log.info("获取用户 {} 评论列表", userId);
            Page<CommentResponse> response = socialService.getUserComments(userId, page, size);
            return ApiResponse.success(response);
        } catch (Exception e) {
            log.error("获取用户评论列表失败", e);
            return ApiResponse.error("获取评论列表失败: " + e.getMessage());
        }
    }

    // ==================== 统计相关 ====================

    /**
     * 获取用户互动统计
     */
    @GetMapping("/stats")
    public ApiResponse<UserInteractionStatsResponse> getUserInteractionStats(
            @RequestHeader("X-User-Id") String userId) {
        try {
            log.info("获取用户 {} 互动统计", userId);
            UserInteractionStatsResponse response = socialService.getUserInteractionStats(userId);
            return ApiResponse.success(response);
        } catch (Exception e) {
            log.error("获取用户互动统计失败", e);
            return ApiResponse.error("获取互动统计失败: " + e.getMessage());
        }
    }

    /**
     * 重置每日奖励
     */
    @PostMapping("/stats/reset-daily-rewards")
    public ApiResponse<Void> resetDailyRewards(@RequestHeader("X-User-Id") String userId) {
        try {
            log.info("重置用户 {} 每日奖励", userId);
            socialService.resetDailyRewards(userId);
            return ApiResponse.success();
        } catch (Exception e) {
            log.error("重置每日奖励失败", e);
            return ApiResponse.error("重置每日奖励失败: " + e.getMessage());
        }
    }

    /**
     * 检查奖励资格
     */
    @GetMapping("/stats/can-reward")
    public ApiResponse<Boolean> canGetReward(
            @RequestHeader("X-User-Id") String userId,
            @RequestParam String actionType) {
        try {
            log.info("检查用户 {} 奖励资格: {}", userId, actionType);
            boolean result = socialService.canGetReward(userId, actionType);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("检查奖励资格失败", e);
            return ApiResponse.error("检查奖励资格失败: " + e.getMessage());
        }
    }

    // ==================== 健康检查 ====================

    /**
     * 健康检查
     */
    @GetMapping("/health")
    public ApiResponse<Map<String, String>> health() {
        return ApiResponse.success("服务运行正常", Map.of(
                "service", "social-service",
                "status", "UP",
                "timestamp", String.valueOf(System.currentTimeMillis())
        ));
    }
} 