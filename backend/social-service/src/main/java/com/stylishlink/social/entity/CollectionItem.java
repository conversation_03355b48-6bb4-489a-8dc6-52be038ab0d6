package com.stylishlink.social.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 收藏项目实体
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("social_collection_item")
public class CollectionItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 收藏夹ID
     */
    @TableField("collection_id")
    private String collectionId;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private String userId;

    /**
     * 内容类型（outfit-搭配, recommendation-推荐, share-分享等）
     */
    @TableField("content_type")
    private String contentType;

    /**
     * 内容ID
     */
    @TableField("content_id")
    private String contentId;

    /**
     * 内容所有者ID
     */
    @TableField("content_owner_id")
    private String contentOwnerId;

    /**
     * 收藏备注
     */
    @TableField("note")
    private String note;

    /**
     * 收藏标签（多个标签用逗号分隔）
     */
    @TableField("tags")
    private String tags;

    /**
     * 排序权重
     */
    @TableField("sort_order")
    private Integer sortOrder;

    /**
     * 收藏状态（0-正常, 1-已移除）
     */
    @TableField("status")
    private Integer status;

    /**
     * 是否删除（逻辑删除）
     */
    @TableLogic
    @TableField("deleted")
    private Integer deleted;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
} 