package com.stylishlink.social.dto.request;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

/**
 * 分享请求DTO
 */
@Data
public class ShareRequest {

    /**
     * 内容类型（outfit-搭配, recommendation-推荐, activity-活动等）
     */
    @NotBlank(message = "内容类型不能为空")
    private String contentType;

    /**
     * 内容ID
     */
    @NotBlank(message = "内容ID不能为空")
    private String contentId;

    /**
     * 分享平台（wechat-微信, weibo-微博, qq-QQ等）
     */
    @NotBlank(message = "分享平台不能为空")
    private String platform;

    /**
     * 分享标题
     */
    @Size(max = 100, message = "分享标题不能超过100个字符")
    private String title;

    /**
     * 分享描述
     */
    @Size(max = 500, message = "分享描述不能超过500个字符")
    private String description;

    /**
     * 分享图片URL
     */
    private String imageUrl;

    /**
     * 分享链接
     */
    private String shareUrl;

    /**
     * 分享标签（多个标签用逗号分隔）
     */
    private String tags;

    /**
     * 分享备注
     */
    @Size(max = 200, message = "分享备注不能超过200个字符")
    private String remark;
} 