package com.stylishlink.social.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.stylishlink.social.dto.request.*;
import com.stylishlink.social.dto.response.*;
import com.stylishlink.social.entity.*;
import com.stylishlink.social.mapper.*;
import com.stylishlink.social.service.SocialService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 社交服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SocialServiceImpl implements SocialService {

    private final ShareMapper shareMapper;
    private final LikeMapper likeMapper;
    private final CollectionMapper collectionMapper;
    private final CollectionItemMapper collectionItemMapper;
    private final CommentMapper commentMapper;
    private final UserInteractionStatsMapper userInteractionStatsMapper;

    // ==================== 分享相关 ====================

    @Override
    @Transactional
    public ShareResponse createShare(String userId, ShareRequest request) {
        log.info("用户 {} 创建分享记录: {}", userId, request);
        
        Share share = new Share();
        BeanUtils.copyProperties(request, share);
        share.setUserId(userId);
        share.setStatus(0); // 正常状态
        
        shareMapper.insert(share);
        
        // 更新用户统计
        updateUserInteractionStats(userId, "share", null);
        
        return convertToShareResponse(share);
    }

    @Override
    public Page<ShareResponse> getUserShares(String userId, int page, int size) {
        log.info("获取用户 {} 的分享列表，页码: {}, 大小: {}", userId, page, size);
        
        Page<Share> sharePage = new Page<>(page, size);
        LambdaQueryWrapper<Share> wrapper = new LambdaQueryWrapper<Share>()
                .eq(Share::getUserId, userId)
                .eq(Share::getStatus, 0)
                .orderByDesc(Share::getCreatedAt);
        
        Page<Share> result = shareMapper.selectPage(sharePage, wrapper);
        
        Page<ShareResponse> responsePage = new Page<>();
        BeanUtils.copyProperties(result, responsePage);
        responsePage.setRecords(result.getRecords().stream()
                .map(this::convertToShareResponse)
                .collect(Collectors.toList()));
        
        return responsePage;
    }

    @Override
    public ShareResponse getShareById(String shareId) {
        log.info("获取分享详情: {}", shareId);
        
        Share share = shareMapper.selectById(shareId);
        if (share == null || share.getStatus() != 0) {
            throw new RuntimeException("分享记录不存在或已删除");
        }
        
        return convertToShareResponse(share);
    }

    @Override
    @Transactional
    public boolean deleteShare(String userId, String shareId) {
        log.info("用户 {} 删除分享记录: {}", userId, shareId);
        
        Share share = shareMapper.selectById(shareId);
        if (share == null || !share.getUserId().equals(userId)) {
            throw new RuntimeException("分享记录不存在或无权限删除");
        }
        
        // 逻辑删除
        LambdaUpdateWrapper<Share> wrapper = new LambdaUpdateWrapper<Share>()
                .eq(Share::getId, shareId)
                .set(Share::getStatus, 1);
        
        return shareMapper.update(null, wrapper) > 0;
    }

    // ==================== 点赞相关 ====================

    @Override
    @Transactional
    public LikeResponse toggleLike(String userId, LikeRequest request) {
        log.info("用户 {} 切换点赞状态: {}", userId, request);
        
        // 查询是否已存在点赞记录
        LambdaQueryWrapper<Like> wrapper = new LambdaQueryWrapper<Like>()
                .eq(Like::getUserId, userId)
                .eq(Like::getContentType, request.getContentType())
                .eq(Like::getContentId, request.getContentId());
        
        Like existingLike = likeMapper.selectOne(wrapper);
        
        if (existingLike != null) {
            // 更新点赞状态
            existingLike.setLikeStatus(request.getLikeStatus());
            likeMapper.updateById(existingLike);
            
            // 更新统计
            String actionType = request.getLikeStatus() == 1 ? "like" : "unlike";
            updateUserInteractionStats(userId, actionType, existingLike.getContentOwnerId());
            
            return convertToLikeResponse(existingLike);
        } else {
            // 创建新的点赞记录
            Like like = new Like();
            BeanUtils.copyProperties(request, like);
            like.setUserId(userId);
            // 这里需要根据contentType和contentId查询内容所有者，暂时设为null
            like.setContentOwnerId(null);
            
            likeMapper.insert(like);
            
            // 更新统计
            if (request.getLikeStatus() == 1) {
                updateUserInteractionStats(userId, "like", like.getContentOwnerId());
            }
            
            return convertToLikeResponse(like);
        }
    }

    @Override
    public Page<LikeResponse> getContentLikes(String contentType, String contentId, int page, int size) {
        log.info("获取内容点赞列表: {} - {}", contentType, contentId);
        
        Page<Like> likePage = new Page<>(page, size);
        LambdaQueryWrapper<Like> wrapper = new LambdaQueryWrapper<Like>()
                .eq(Like::getContentType, contentType)
                .eq(Like::getContentId, contentId)
                .eq(Like::getLikeStatus, 1)
                .orderByDesc(Like::getCreatedAt);
        
        Page<Like> result = likeMapper.selectPage(likePage, wrapper);
        
        Page<LikeResponse> responsePage = new Page<>();
        BeanUtils.copyProperties(result, responsePage);
        responsePage.setRecords(result.getRecords().stream()
                .map(this::convertToLikeResponse)
                .collect(Collectors.toList()));
        
        return responsePage;
    }

    @Override
    public Page<LikeResponse> getUserLikes(String userId, int page, int size) {
        log.info("获取用户 {} 的点赞列表", userId);
        
        Page<Like> likePage = new Page<>(page, size);
        LambdaQueryWrapper<Like> wrapper = new LambdaQueryWrapper<Like>()
                .eq(Like::getUserId, userId)
                .eq(Like::getLikeStatus, 1)
                .orderByDesc(Like::getCreatedAt);
        
        Page<Like> result = likeMapper.selectPage(likePage, wrapper);
        
        Page<LikeResponse> responsePage = new Page<>();
        BeanUtils.copyProperties(result, responsePage);
        responsePage.setRecords(result.getRecords().stream()
                .map(this::convertToLikeResponse)
                .collect(Collectors.toList()));
        
        return responsePage;
    }

    @Override
    public boolean isUserLiked(String userId, String contentType, String contentId) {
        LambdaQueryWrapper<Like> wrapper = new LambdaQueryWrapper<Like>()
                .eq(Like::getUserId, userId)
                .eq(Like::getContentType, contentType)
                .eq(Like::getContentId, contentId)
                .eq(Like::getLikeStatus, 1);
        
        return likeMapper.selectCount(wrapper) > 0;
    }

    // ==================== 收藏相关 ====================

    @Override
    @Transactional
    public CollectionResponse createCollection(String userId, CollectionRequest request) {
        log.info("用户 {} 创建收藏夹: {}", userId, request);
        
        SocialCollection collection = new SocialCollection();
        BeanUtils.copyProperties(request, collection);
        collection.setUserId(userId);
        collection.setItemCount(0);
        collection.setSortOrder(0);
        collection.setStatus(0);
        
        // 设置默认值
        if (collection.getIsPublic() == null) {
            collection.setIsPublic(0); // 默认私有
        }
        if (collection.getType() == null) {
            collection.setType("mixed"); // 默认混合类型
        }
        
        collectionMapper.insert(collection);
        
        return convertToCollectionResponse(collection);
    }

    @Override
    @Transactional
    public CollectionResponse updateCollection(String userId, String collectionId, CollectionRequest request) {
        log.info("用户 {} 更新收藏夹 {}: {}", userId, collectionId, request);
        
        SocialCollection collection = collectionMapper.selectById(collectionId);
        if (collection == null || !collection.getUserId().equals(userId)) {
            throw new RuntimeException("收藏夹不存在或无权限修改");
        }
        
        BeanUtils.copyProperties(request, collection);
        collection.setId(collectionId);
        
        collectionMapper.updateById(collection);
        
        return convertToCollectionResponse(collection);
    }

    @Override
    @Transactional
    public boolean deleteCollection(String userId, String collectionId) {
        log.info("用户 {} 删除收藏夹: {}", userId, collectionId);
        
        SocialCollection collection = collectionMapper.selectById(collectionId);
        if (collection == null || !collection.getUserId().equals(userId)) {
            throw new RuntimeException("收藏夹不存在或无权限删除");
        }
        
        // 删除收藏夹中的所有项目
        LambdaQueryWrapper<CollectionItem> itemWrapper = new LambdaQueryWrapper<CollectionItem>()
                .eq(CollectionItem::getCollectionId, collectionId);
        collectionItemMapper.delete(itemWrapper);
        
        // 删除收藏夹
        return collectionMapper.deleteById(collectionId) > 0;
    }

    @Override
    public List<CollectionResponse> getUserCollections(String userId) {
        log.info("获取用户 {} 的收藏夹列表", userId);
        
        LambdaQueryWrapper<SocialCollection> wrapper = new LambdaQueryWrapper<SocialCollection>()
                .eq(SocialCollection::getUserId, userId)
                .eq(SocialCollection::getStatus, 0)
                .orderByAsc(SocialCollection::getSortOrder)
                .orderByDesc(SocialCollection::getCreatedAt);
        
        List<SocialCollection> collections = collectionMapper.selectList(wrapper);
        
        return collections.stream()
                .map(this::convertToCollectionResponse)
                .collect(Collectors.toList());
    }

    @Override
    public CollectionResponse getCollectionById(String collectionId) {
        log.info("获取收藏夹详情: {}", collectionId);
        
        SocialCollection collection = collectionMapper.selectById(collectionId);
        if (collection == null || collection.getStatus() != 0) {
            throw new RuntimeException("收藏夹不存在或已删除");
        }
        
        return convertToCollectionResponse(collection);
    }

    @Override
    @Transactional
    public boolean addCollectionItem(String userId, CollectionItemRequest request) {
        log.info("用户 {} 添加收藏项目: {}", userId, request);
        
        // 验证收藏夹是否属于用户
        SocialCollection collection = collectionMapper.selectById(request.getCollectionId());
        if (collection == null || !collection.getUserId().equals(userId)) {
            throw new RuntimeException("收藏夹不存在或无权限操作");
        }
        
        // 检查是否已收藏
        LambdaQueryWrapper<CollectionItem> wrapper = new LambdaQueryWrapper<CollectionItem>()
                .eq(CollectionItem::getCollectionId, request.getCollectionId())
                .eq(CollectionItem::getContentType, request.getContentType())
                .eq(CollectionItem::getContentId, request.getContentId())
                .eq(CollectionItem::getStatus, 0);
        
        if (collectionItemMapper.selectCount(wrapper) > 0) {
            throw new RuntimeException("该内容已在收藏夹中");
        }
        
        // 添加收藏项目
        CollectionItem item = new CollectionItem();
        BeanUtils.copyProperties(request, item);
        item.setUserId(userId);
        // 这里需要根据contentType和contentId查询内容所有者，暂时设为null
        item.setContentOwnerId(null);
        item.setSortOrder(0);
        item.setStatus(0);
        
        collectionItemMapper.insert(item);
        
        // 更新收藏夹项目数量
        LambdaUpdateWrapper<SocialCollection> updateWrapper = new LambdaUpdateWrapper<SocialCollection>()
                .eq(SocialCollection::getId, request.getCollectionId())
                .setSql("item_count = item_count + 1");
        collectionMapper.update(null, updateWrapper);
        
        // 更新用户统计
        updateUserInteractionStats(userId, "collect", item.getContentOwnerId());
        
        return true;
    }

    @Override
    @Transactional
    public boolean removeCollectionItem(String userId, String collectionId, String contentId) {
        log.info("用户 {} 从收藏夹 {} 移除内容: {}", userId, collectionId, contentId);
        
        // 验证收藏夹是否属于用户
        SocialCollection collection = collectionMapper.selectById(collectionId);
        if (collection == null || !collection.getUserId().equals(userId)) {
            throw new RuntimeException("收藏夹不存在或无权限操作");
        }
        
        // 删除收藏项目
        LambdaQueryWrapper<CollectionItem> wrapper = new LambdaQueryWrapper<CollectionItem>()
                .eq(CollectionItem::getCollectionId, collectionId)
                .eq(CollectionItem::getContentId, contentId)
                .eq(CollectionItem::getUserId, userId);
        
        int deleted = collectionItemMapper.delete(wrapper);
        
        if (deleted > 0) {
            // 更新收藏夹项目数量
            LambdaUpdateWrapper<SocialCollection> updateWrapper = new LambdaUpdateWrapper<SocialCollection>()
                    .eq(SocialCollection::getId, collectionId)
                    .setSql("item_count = GREATEST(item_count - 1, 0)");
            collectionMapper.update(null, updateWrapper);
        }
        
        return deleted > 0;
    }

    @Override
    public Page<Object> getCollectionItems(String collectionId, int page, int size) {
        log.info("获取收藏夹 {} 的内容列表", collectionId);
        
        Page<CollectionItem> itemPage = new Page<>(page, size);
        LambdaQueryWrapper<CollectionItem> wrapper = new LambdaQueryWrapper<CollectionItem>()
                .eq(CollectionItem::getCollectionId, collectionId)
                .eq(CollectionItem::getStatus, 0)
                .orderByAsc(CollectionItem::getSortOrder)
                .orderByDesc(CollectionItem::getCreatedAt);
        
        Page<CollectionItem> result = collectionItemMapper.selectPage(itemPage, wrapper);
        
        // 这里需要根据contentType和contentId查询具体内容，暂时返回CollectionItem
        Page<Object> responsePage = new Page<>();
        BeanUtils.copyProperties(result, responsePage);
        responsePage.setRecords(result.getRecords().stream()
                .map(item -> (Object) item)
                .collect(Collectors.toList()));
        
        return responsePage;
    }

    @Override
    public boolean isContentCollected(String userId, String contentType, String contentId) {
        LambdaQueryWrapper<CollectionItem> wrapper = new LambdaQueryWrapper<CollectionItem>()
                .eq(CollectionItem::getUserId, userId)
                .eq(CollectionItem::getContentType, contentType)
                .eq(CollectionItem::getContentId, contentId)
                .eq(CollectionItem::getStatus, 0);
        
        return collectionItemMapper.selectCount(wrapper) > 0;
    }

    // ==================== 评论相关 ====================

    @Override
    @Transactional
    public CommentResponse createComment(String userId, CommentRequest request) {
        log.info("用户 {} 发表评论: {}", userId, request);
        
        Comment comment = new Comment();
        BeanUtils.copyProperties(request, comment);
        comment.setUserId(userId);
        comment.setLikeCount(0);
        comment.setReplyCount(0);
        comment.setLevel(1);
        comment.setAuditStatus(1); // 默认审核通过
        comment.setStatus(0);
        
        // 处理回复评论
        if (request.getParentId() != null) {
            Comment parentComment = commentMapper.selectById(request.getParentId());
            if (parentComment != null) {
                comment.setRootId(parentComment.getRootId() != null ? parentComment.getRootId() : parentComment.getId());
                comment.setLevel(parentComment.getLevel() + 1);
                
                // 更新父评论回复数
                LambdaUpdateWrapper<Comment> updateWrapper = new LambdaUpdateWrapper<Comment>()
                        .eq(Comment::getId, request.getParentId())
                        .setSql("reply_count = reply_count + 1");
                commentMapper.update(null, updateWrapper);
            }
        }
        
        commentMapper.insert(comment);
        
        // 更新用户统计
        updateUserInteractionStats(userId, "comment", comment.getContentOwnerId());
        
        return convertToCommentResponse(comment);
    }

    @Override
    @Transactional
    public boolean deleteComment(String userId, String commentId) {
        log.info("用户 {} 删除评论: {}", userId, commentId);
        
        Comment comment = commentMapper.selectById(commentId);
        if (comment == null || !comment.getUserId().equals(userId)) {
            throw new RuntimeException("评论不存在或无权限删除");
        }
        
        // 逻辑删除
        LambdaUpdateWrapper<Comment> wrapper = new LambdaUpdateWrapper<Comment>()
                .eq(Comment::getId, commentId)
                .set(Comment::getStatus, 1);
        
        int updated = commentMapper.update(null, wrapper);
        
        // 如果是回复评论，更新父评论回复数
        if (updated > 0 && comment.getParentId() != null) {
            LambdaUpdateWrapper<Comment> updateWrapper = new LambdaUpdateWrapper<Comment>()
                    .eq(Comment::getId, comment.getParentId())
                    .setSql("reply_count = GREATEST(reply_count - 1, 0)");
            commentMapper.update(null, updateWrapper);
        }
        
        return updated > 0;
    }

    @Override
    public Page<CommentResponse> getContentComments(String contentType, String contentId, int page, int size) {
        log.info("获取内容评论列表: {} - {}", contentType, contentId);
        
        Page<Comment> commentPage = new Page<>(page, size);
        LambdaQueryWrapper<Comment> wrapper = new LambdaQueryWrapper<Comment>()
                .eq(Comment::getContentType, contentType)
                .eq(Comment::getContentId, contentId)
                .eq(Comment::getStatus, 0)
                .eq(Comment::getLevel, 1) // 只查询一级评论
                .orderByDesc(Comment::getCreatedAt);
        
        Page<Comment> result = commentMapper.selectPage(commentPage, wrapper);
        
        Page<CommentResponse> responsePage = new Page<>();
        BeanUtils.copyProperties(result, responsePage);
        responsePage.setRecords(result.getRecords().stream()
                .map(this::convertToCommentResponse)
                .collect(Collectors.toList()));
        
        return responsePage;
    }

    @Override
    public List<CommentResponse> getCommentReplies(String commentId) {
        log.info("获取评论回复列表: {}", commentId);
        
        LambdaQueryWrapper<Comment> wrapper = new LambdaQueryWrapper<Comment>()
                .eq(Comment::getParentId, commentId)
                .eq(Comment::getStatus, 0)
                .orderByAsc(Comment::getCreatedAt);
        
        List<Comment> replies = commentMapper.selectList(wrapper);
        
        return replies.stream()
                .map(this::convertToCommentResponse)
                .collect(Collectors.toList());
    }

    @Override
    public Page<CommentResponse> getUserComments(String userId, int page, int size) {
        log.info("获取用户 {} 的评论列表", userId);
        
        Page<Comment> commentPage = new Page<>(page, size);
        LambdaQueryWrapper<Comment> wrapper = new LambdaQueryWrapper<Comment>()
                .eq(Comment::getUserId, userId)
                .eq(Comment::getStatus, 0)
                .orderByDesc(Comment::getCreatedAt);
        
        Page<Comment> result = commentMapper.selectPage(commentPage, wrapper);
        
        Page<CommentResponse> responsePage = new Page<>();
        BeanUtils.copyProperties(result, responsePage);
        responsePage.setRecords(result.getRecords().stream()
                .map(this::convertToCommentResponse)
                .collect(Collectors.toList()));
        
        return responsePage;
    }

    // ==================== 统计相关 ====================

    @Override
    public UserInteractionStatsResponse getUserInteractionStats(String userId) {
        log.info("获取用户 {} 的互动统计", userId);
        
        UserInteractionStats stats = userInteractionStatsMapper.selectOne(
                new LambdaQueryWrapper<UserInteractionStats>()
                        .eq(UserInteractionStats::getUserId, userId)
        );
        
        if (stats == null) {
            stats = initUserInteractionStats(userId);
        }
        
        return convertToUserInteractionStatsResponse(stats);
    }

    @Override
    @Transactional
    public void updateUserInteractionStats(String userId, String actionType, String targetUserId) {
        log.info("更新用户 {} 的互动统计，动作: {}", userId, actionType);
        
        UserInteractionStats stats = userInteractionStatsMapper.selectOne(
                new LambdaQueryWrapper<UserInteractionStats>()
                        .eq(UserInteractionStats::getUserId, userId)
        );
        
        if (stats == null) {
            stats = initUserInteractionStats(userId);
        }
        
        // 检查是否需要重置每日奖励
        if (stats.getRewardResetDate() == null || 
            !stats.getRewardResetDate().toLocalDate().equals(LocalDateTime.now().toLocalDate())) {
            resetDailyRewards(userId);
            stats.setRewardResetDate(LocalDateTime.now());
        }
        
        // 根据动作类型更新统计
        switch (actionType) {
            case "like":
                stats.setLikeCount(stats.getLikeCount() + 1);
                if (canGetReward(userId, "like")) {
                    stats.setTodayLikeRewards(stats.getTodayLikeRewards() + 1);
                }
                break;
            case "unlike":
                stats.setLikeCount(Math.max(0, stats.getLikeCount() - 1));
                break;
            case "share":
                stats.setShareCount(stats.getShareCount() + 1);
                if (canGetReward(userId, "share")) {
                    stats.setTodayShareRewards(stats.getTodayShareRewards() + 1);
                }
                break;
            case "collect":
                stats.setCollectionCount(stats.getCollectionCount() + 1);
                if (canGetReward(userId, "collect")) {
                    stats.setTodayCollectionRewards(stats.getTodayCollectionRewards() + 1);
                }
                break;
            case "comment":
                stats.setCommentCount(stats.getCommentCount() + 1);
                if (canGetReward(userId, "comment")) {
                    stats.setTodayCommentRewards(stats.getTodayCommentRewards() + 1);
                }
                break;
        }
        
        // 更新总互动数
        stats.setTotalInteractions(
                stats.getLikeCount() + stats.getShareCount() + 
                stats.getCollectionCount() + stats.getCommentCount()
        );
        
        // 计算活跃度评分（简单算法）
        stats.setActivityScore(BigDecimal.valueOf(stats.getTotalInteractions() * 0.1));
        
        userInteractionStatsMapper.updateById(stats);
        
        // 如果有目标用户，更新目标用户的被互动统计
        if (targetUserId != null && !targetUserId.equals(userId)) {
            updateTargetUserStats(targetUserId, actionType);
        }
    }

    @Override
    @Transactional
    public void resetDailyRewards(String userId) {
        log.info("重置用户 {} 的每日奖励计数", userId);
        
        LambdaUpdateWrapper<UserInteractionStats> wrapper = new LambdaUpdateWrapper<UserInteractionStats>()
                .eq(UserInteractionStats::getUserId, userId)
                .set(UserInteractionStats::getTodayLikeRewards, 0)
                .set(UserInteractionStats::getTodayCommentRewards, 0)
                .set(UserInteractionStats::getTodayShareRewards, 0)
                .set(UserInteractionStats::getTodayCollectionRewards, 0)
                .set(UserInteractionStats::getRewardResetDate, LocalDateTime.now());
        
        userInteractionStatsMapper.update(null, wrapper);
    }

    @Override
    public boolean canGetReward(String userId, String actionType) {
        UserInteractionStats stats = userInteractionStatsMapper.selectOne(
                new LambdaQueryWrapper<UserInteractionStats>()
                        .eq(UserInteractionStats::getUserId, userId)
        );
        
        if (stats == null) {
            return true; // 新用户可以获得奖励
        }
        
        // 检查每日奖励限制（每种动作每天最多奖励5次）
        int maxDailyRewards = 5;
        switch (actionType) {
            case "like":
                return stats.getTodayLikeRewards() < maxDailyRewards;
            case "comment":
                return stats.getTodayCommentRewards() < maxDailyRewards;
            case "share":
                return stats.getTodayShareRewards() < maxDailyRewards;
            case "collect":
                return stats.getTodayCollectionRewards() < maxDailyRewards;
            default:
                return false;
        }
    }

    // ==================== 私有方法 ====================

    private UserInteractionStats initUserInteractionStats(String userId) {
        UserInteractionStats stats = new UserInteractionStats();
        stats.setUserId(userId);
        stats.setLikeCount(0);
        stats.setLikedCount(0);
        stats.setShareCount(0);
        stats.setSharedCount(0);
        stats.setCollectionCount(0);
        stats.setCollectedCount(0);
        stats.setCommentCount(0);
        stats.setCommentedCount(0);
        stats.setFollowCount(0);
        stats.setFollowerCount(0);
        stats.setAverageRating(BigDecimal.ZERO);
        stats.setTotalInteractions(0);
        stats.setActivityScore(BigDecimal.ZERO);
        stats.setInfluenceScore(BigDecimal.ZERO);
        stats.setTodayLikeRewards(0);
        stats.setTodayCommentRewards(0);
        stats.setTodayShareRewards(0);
        stats.setTodayCollectionRewards(0);
        stats.setRewardResetDate(LocalDateTime.now());
        
        userInteractionStatsMapper.insert(stats);
        return stats;
    }

    private void updateTargetUserStats(String targetUserId, String actionType) {
        UserInteractionStats targetStats = userInteractionStatsMapper.selectOne(
                new LambdaQueryWrapper<UserInteractionStats>()
                        .eq(UserInteractionStats::getUserId, targetUserId)
        );
        
        if (targetStats == null) {
            targetStats = initUserInteractionStats(targetUserId);
        }
        
        // 更新被互动统计
        switch (actionType) {
            case "like":
                targetStats.setLikedCount(targetStats.getLikedCount() + 1);
                break;
            case "share":
                targetStats.setSharedCount(targetStats.getSharedCount() + 1);
                break;
            case "collect":
                targetStats.setCollectedCount(targetStats.getCollectedCount() + 1);
                break;
            case "comment":
                targetStats.setCommentedCount(targetStats.getCommentedCount() + 1);
                break;
        }
        
        // 计算影响力评分（简单算法）
        int totalReceived = targetStats.getLikedCount() + targetStats.getSharedCount() + 
                           targetStats.getCollectedCount() + targetStats.getCommentedCount();
        targetStats.setInfluenceScore(BigDecimal.valueOf(totalReceived * 0.2));
        
        userInteractionStatsMapper.updateById(targetStats);
    }

    // ==================== 转换方法 ====================

    private ShareResponse convertToShareResponse(Share share) {
        ShareResponse response = new ShareResponse();
        BeanUtils.copyProperties(share, response);
        return response;
    }

    private LikeResponse convertToLikeResponse(Like like) {
        LikeResponse response = new LikeResponse();
        BeanUtils.copyProperties(like, response);
        return response;
    }

    private CollectionResponse convertToCollectionResponse(SocialCollection collection) {
        CollectionResponse response = new CollectionResponse();
        BeanUtils.copyProperties(collection, response);
        return response;
    }

    private CommentResponse convertToCommentResponse(Comment comment) {
        CommentResponse response = new CommentResponse();
        BeanUtils.copyProperties(comment, response);
        return response;
    }

    private UserInteractionStatsResponse convertToUserInteractionStatsResponse(UserInteractionStats stats) {
        UserInteractionStatsResponse response = new UserInteractionStatsResponse();
        BeanUtils.copyProperties(stats, response);
        return response;
    }
} 