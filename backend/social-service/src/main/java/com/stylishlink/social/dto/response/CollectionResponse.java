package com.stylishlink.social.dto.response;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 收藏夹响应DTO
 */
@Data
public class CollectionResponse {

    /**
     * 收藏夹ID
     */
    private String id;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 收藏夹名称
     */
    private String name;

    /**
     * 收藏夹描述
     */
    private String description;

    /**
     * 封面图片URL
     */
    private String coverImageUrl;

    /**
     * 是否公开
     */
    private Integer isPublic;

    /**
     * 收藏项目数量
     */
    private Integer itemCount;

    /**
     * 收藏夹类型
     */
    private String type;

    /**
     * 排序权重
     */
    private Integer sortOrder;

    /**
     * 收藏夹状态
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
} 