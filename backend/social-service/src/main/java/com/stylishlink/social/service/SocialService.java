package com.stylishlink.social.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.stylishlink.social.dto.request.*;
import com.stylishlink.social.dto.response.*;

import java.util.List;

/**
 * 社交服务接口
 */
public interface SocialService {

    // ==================== 分享相关 ====================
    
    /**
     * 创建分享记录
     */
    ShareResponse createShare(String userId, ShareRequest request);
    
    /**
     * 获取用户分享列表
     */
    Page<ShareResponse> getUserShares(String userId, int page, int size);
    
    /**
     * 获取分享详情
     */
    ShareResponse getShareById(String shareId);
    
    /**
     * 删除分享记录
     */
    boolean deleteShare(String userId, String shareId);

    // ==================== 点赞相关 ====================
    
    /**
     * 点赞或取消点赞
     */
    LikeResponse toggleLike(String userId, LikeRequest request);
    
    /**
     * 获取内容点赞列表
     */
    Page<LikeResponse> getContentLikes(String contentType, String contentId, int page, int size);
    
    /**
     * 获取用户点赞列表
     */
    Page<LikeResponse> getUserLikes(String userId, int page, int size);
    
    /**
     * 检查用户是否已点赞
     */
    boolean isUserLiked(String userId, String contentType, String contentId);

    // ==================== 收藏相关 ====================
    
    /**
     * 创建收藏夹
     */
    CollectionResponse createCollection(String userId, CollectionRequest request);
    
    /**
     * 更新收藏夹
     */
    CollectionResponse updateCollection(String userId, String collectionId, CollectionRequest request);
    
    /**
     * 删除收藏夹
     */
    boolean deleteCollection(String userId, String collectionId);
    
    /**
     * 获取用户收藏夹列表
     */
    List<CollectionResponse> getUserCollections(String userId);
    
    /**
     * 获取收藏夹详情
     */
    CollectionResponse getCollectionById(String collectionId);
    
    /**
     * 添加收藏项目
     */
    boolean addCollectionItem(String userId, CollectionItemRequest request);
    
    /**
     * 移除收藏项目
     */
    boolean removeCollectionItem(String userId, String collectionId, String contentId);
    
    /**
     * 获取收藏夹内容列表
     */
    Page<Object> getCollectionItems(String collectionId, int page, int size);
    
    /**
     * 检查内容是否已收藏
     */
    boolean isContentCollected(String userId, String contentType, String contentId);

    // ==================== 评论相关 ====================
    
    /**
     * 发表评论
     */
    CommentResponse createComment(String userId, CommentRequest request);
    
    /**
     * 删除评论
     */
    boolean deleteComment(String userId, String commentId);
    
    /**
     * 获取内容评论列表
     */
    Page<CommentResponse> getContentComments(String contentType, String contentId, int page, int size);
    
    /**
     * 获取评论回复列表
     */
    List<CommentResponse> getCommentReplies(String commentId);
    
    /**
     * 获取用户评论列表
     */
    Page<CommentResponse> getUserComments(String userId, int page, int size);

    // ==================== 统计相关 ====================
    
    /**
     * 获取用户互动统计
     */
    UserInteractionStatsResponse getUserInteractionStats(String userId);
    
    /**
     * 更新用户互动统计
     */
    void updateUserInteractionStats(String userId, String actionType, String targetUserId);
    
    /**
     * 重置每日奖励计数
     */
    void resetDailyRewards(String userId);
    
    /**
     * 检查是否可以获得奖励
     */
    boolean canGetReward(String userId, String actionType);
} 