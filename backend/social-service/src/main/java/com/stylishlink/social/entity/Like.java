package com.stylishlink.social.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 点赞记录实体
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("social_like")
public class Like implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private String userId;

    /**
     * 内容类型（outfit-搭配, recommendation-推荐, share-分享, comment-评论等）
     */
    @TableField("content_type")
    private String contentType;

    /**
     * 内容ID
     */
    @TableField("content_id")
    private String contentId;

    /**
     * 内容所有者ID
     */
    @TableField("content_owner_id")
    private String contentOwnerId;

    /**
     * 点赞状态（1-点赞, 0-取消点赞）
     */
    @TableField("like_status")
    private Integer likeStatus;

    /**
     * 是否删除（逻辑删除）
     */
    @TableLogic
    @TableField("deleted")
    private Integer deleted;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
} 