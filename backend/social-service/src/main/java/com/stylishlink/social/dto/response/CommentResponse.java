package com.stylishlink.social.dto.response;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 评论响应DTO
 */
@Data
public class CommentResponse {

    /**
     * 评论ID
     */
    private String id;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 内容类型
     */
    private String contentType;

    /**
     * 内容ID
     */
    private String contentId;

    /**
     * 内容所有者ID
     */
    private String contentOwnerId;

    /**
     * 父评论ID
     */
    private String parentId;

    /**
     * 根评论ID
     */
    private String rootId;

    /**
     * 回复目标用户ID
     */
    private String replyToUserId;

    /**
     * 评论内容
     */
    private String content;

    /**
     * 评论图片URL
     */
    private String images;

    /**
     * 点赞数
     */
    private Integer likeCount;

    /**
     * 回复数
     */
    private Integer replyCount;

    /**
     * 评论层级
     */
    private Integer level;

    /**
     * 审核状态
     */
    private Integer auditStatus;

    /**
     * 评论状态
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 子评论列表（用于嵌套显示）
     */
    private List<CommentResponse> replies;
} 