package com.stylishlink.social.dto.response;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 点赞响应DTO
 */
@Data
public class LikeResponse {

    /**
     * 点赞ID
     */
    private String id;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 内容类型
     */
    private String contentType;

    /**
     * 内容ID
     */
    private String contentId;

    /**
     * 内容所有者ID
     */
    private String contentOwnerId;

    /**
     * 点赞状态
     */
    private Integer likeStatus;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
} 