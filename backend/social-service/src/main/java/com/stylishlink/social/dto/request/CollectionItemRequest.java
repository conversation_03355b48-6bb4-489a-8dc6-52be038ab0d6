package com.stylishlink.social.dto.request;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 收藏项目请求DTO
 */
@Data
public class CollectionItemRequest {

    /**
     * 收藏夹ID
     */
    @NotBlank(message = "收藏夹ID不能为空")
    private String collectionId;

    /**
     * 内容类型（outfit-搭配, recommendation-推荐, share-分享等）
     */
    @NotBlank(message = "内容类型不能为空")
    private String contentType;

    /**
     * 内容ID
     */
    @NotBlank(message = "内容ID不能为空")
    private String contentId;

    /**
     * 收藏备注
     */
    @Size(max = 200, message = "收藏备注不能超过200个字符")
    private String note;

    /**
     * 收藏标签（多个标签用逗号分隔）
     */
    private String tags;
} 