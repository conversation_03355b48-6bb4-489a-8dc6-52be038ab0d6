package com.stylishlink.social.dto.response;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 分享响应DTO
 */
@Data
public class ShareResponse {

    /**
     * 分享ID
     */
    private String id;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 内容类型
     */
    private String contentType;

    /**
     * 内容ID
     */
    private String contentId;

    /**
     * 分享平台
     */
    private String platform;

    /**
     * 分享标题
     */
    private String title;

    /**
     * 分享描述
     */
    private String description;

    /**
     * 分享图片URL
     */
    private String imageUrl;

    /**
     * 分享链接
     */
    private String shareUrl;

    /**
     * 分享标签
     */
    private String tags;

    /**
     * 分享备注
     */
    private String remark;

    /**
     * 分享状态
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
} 