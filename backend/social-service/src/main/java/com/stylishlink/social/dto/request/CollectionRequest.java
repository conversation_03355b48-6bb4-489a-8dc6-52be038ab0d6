package com.stylishlink.social.dto.request;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 收藏夹请求DTO
 */
@Data
public class CollectionRequest {

    /**
     * 收藏夹名称
     */
    @NotBlank(message = "收藏夹名称不能为空")
    @Size(max = 50, message = "收藏夹名称不能超过50个字符")
    private String name;

    /**
     * 收藏夹描述
     */
    @Size(max = 200, message = "收藏夹描述不能超过200个字符")
    private String description;

    /**
     * 封面图片URL
     */
    private String coverImageUrl;

    /**
     * 是否公开（1-公开, 0-私有）
     */
    private Integer isPublic;

    /**
     * 收藏夹类型（outfit-搭配, recommendation-推荐, mixed-混合等）
     */
    private String type;
} 