package com.stylishlink.social.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 评论实体
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("social_comment")
public class Comment implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private String userId;

    /**
     * 内容类型（outfit-搭配, recommendation-推荐, share-分享等）
     */
    @TableField("content_type")
    private String contentType;

    /**
     * 内容ID
     */
    @TableField("content_id")
    private String contentId;

    /**
     * 内容所有者ID
     */
    @TableField("content_owner_id")
    private String contentOwnerId;

    /**
     * 父评论ID（用于回复评论）
     */
    @TableField("parent_id")
    private String parentId;

    /**
     * 根评论ID（用于多级评论）
     */
    @TableField("root_id")
    private String rootId;

    /**
     * 回复目标用户ID
     */
    @TableField("reply_to_user_id")
    private String replyToUserId;

    /**
     * 评论内容
     */
    @TableField("content")
    private String content;

    /**
     * 评论图片URL（多个用逗号分隔）
     */
    @TableField("images")
    private String images;

    /**
     * 点赞数
     */
    @TableField("like_count")
    private Integer likeCount;

    /**
     * 回复数
     */
    @TableField("reply_count")
    private Integer replyCount;

    /**
     * 评论层级（1-一级评论, 2-二级评论等）
     */
    @TableField("level")
    private Integer level;

    /**
     * 审核状态（0-待审核, 1-审核通过, 2-审核拒绝）
     */
    @TableField("audit_status")
    private Integer auditStatus;

    /**
     * 审核时间
     */
    @TableField("audit_time")
    private LocalDateTime auditTime;

    /**
     * 审核备注
     */
    @TableField("audit_remark")
    private String auditRemark;

    /**
     * 评论状态（0-正常, 1-已删除, 2-已屏蔽）
     */
    @TableField("status")
    private Integer status;

    /**
     * 是否删除（逻辑删除）
     */
    @TableLogic
    @TableField("deleted")
    private Integer deleted;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
} 