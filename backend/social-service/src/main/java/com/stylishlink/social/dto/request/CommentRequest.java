package com.stylishlink.social.dto.request;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 评论请求DTO
 */
@Data
public class CommentRequest {

    /**
     * 内容类型（outfit-搭配, recommendation-推荐, share-分享等）
     */
    @NotBlank(message = "内容类型不能为空")
    private String contentType;

    /**
     * 内容ID
     */
    @NotBlank(message = "内容ID不能为空")
    private String contentId;

    /**
     * 父评论ID（用于回复评论）
     */
    private String parentId;

    /**
     * 回复目标用户ID
     */
    private String replyToUserId;

    /**
     * 评论内容
     */
    @NotBlank(message = "评论内容不能为空")
    @Size(max = 500, message = "评论内容不能超过500个字符")
    private String content;

    /**
     * 评论图片URL（多个用逗号分隔）
     */
    private String images;
} 