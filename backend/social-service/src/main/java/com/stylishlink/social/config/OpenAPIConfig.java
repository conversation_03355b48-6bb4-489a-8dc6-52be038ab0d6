package com.stylishlink.social.config;

import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.servers.Server;
import io.swagger.v3.oas.models.tags.Tag;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * 社交服务OpenAPI配置
 */
@Configuration
public class OpenAPIConfig {

    @Value("${server.port:8086}")
    private String serverPort;

    @Bean
    public OpenAPI socialServiceOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("社交服务API")
                        .description("StylishLink社交服务API文档，提供分享展示、点赞评论、关注互动、社区功能等")
                        .version("1.0.0")
                        .contact(new Contact()
                                .name("StylishLink团队")
                                .email("<EMAIL>")
                                .url("https://stylishlink.com"))
                        .license(new License()
                                .name("MIT")
                                .url("https://opensource.org/licenses/MIT")))
                .servers(List.of(
                        new Server()
                                .url("http://localhost:" + serverPort)
                                .description("本地开发环境"),
                        new Server()
                                .url("https://api.stylishlink.com")
                                .description("生产环境")
                ))
                .addSecurityItem(new SecurityRequirement()
                        .addList("Bearer Authentication"))
                .components(new Components()
                        .addSecuritySchemes("Bearer Authentication", 
                                new SecurityScheme()
                                        .type(SecurityScheme.Type.HTTP)
                                        .scheme("bearer")
                                        .bearerFormat("JWT")
                                        .description("请在请求头中添加 Authorization: Bearer {token}")))
                .tags(List.of(
                        new Tag()
                                .name("分享管理")
                                .description("穿搭分享创建、编辑、删除、查询等接口"),
                        new Tag()
                                .name("互动功能")
                                .description("点赞、收藏、评论、回复等用户互动接口"),
                        new Tag()
                                .name("关注系统")
                                .description("关注、取消关注、粉丝列表、关注列表等接口"),
                        new Tag()
                                .name("社区动态")
                                .description("动态广场、热门推荐、话题讨论等接口"),
                        new Tag()
                                .name("用户统计")
                                .description("互动统计、活跃度分析、社交数据等接口")
                ));
    }
} 