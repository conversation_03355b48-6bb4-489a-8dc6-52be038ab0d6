package com.stylishlink.social.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 用户互动统计实体
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("social_user_interaction_stats")
public class UserInteractionStats implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private String userId;

    /**
     * 点赞数（用户发出的点赞）
     */
    @TableField("like_count")
    private Integer likeCount;

    /**
     * 被点赞数（用户收到的点赞）
     */
    @TableField("liked_count")
    private Integer likedCount;

    /**
     * 分享数（用户发出的分享）
     */
    @TableField("share_count")
    private Integer shareCount;

    /**
     * 被分享数（用户内容被分享）
     */
    @TableField("shared_count")
    private Integer sharedCount;

    /**
     * 收藏数（用户发出的收藏）
     */
    @TableField("collection_count")
    private Integer collectionCount;

    /**
     * 被收藏数（用户内容被收藏）
     */
    @TableField("collected_count")
    private Integer collectedCount;

    /**
     * 评论数（用户发出的评论）
     */
    @TableField("comment_count")
    private Integer commentCount;

    /**
     * 被评论数（用户内容被评论）
     */
    @TableField("commented_count")
    private Integer commentedCount;

    /**
     * 关注数（用户关注的人数）
     */
    @TableField("follow_count")
    private Integer followCount;

    /**
     * 粉丝数（关注用户的人数）
     */
    @TableField("follower_count")
    private Integer followerCount;

    /**
     * 平均评分
     */
    @TableField("average_rating")
    private BigDecimal averageRating;

    /**
     * 总互动数（所有互动的总和）
     */
    @TableField("total_interactions")
    private Integer totalInteractions;

    /**
     * 活跃度评分
     */
    @TableField("activity_score")
    private BigDecimal activityScore;

    /**
     * 影响力评分
     */
    @TableField("influence_score")
    private BigDecimal influenceScore;

    /**
     * 今日点赞奖励次数
     */
    @TableField("today_like_rewards")
    private Integer todayLikeRewards;

    /**
     * 今日评论奖励次数
     */
    @TableField("today_comment_rewards")
    private Integer todayCommentRewards;

    /**
     * 今日分享奖励次数
     */
    @TableField("today_share_rewards")
    private Integer todayShareRewards;

    /**
     * 今日收藏奖励次数
     */
    @TableField("today_collection_rewards")
    private Integer todayCollectionRewards;

    /**
     * 奖励重置日期
     */
    @TableField("reward_reset_date")
    private LocalDateTime rewardResetDate;

    /**
     * 是否删除（逻辑删除）
     */
    @TableLogic
    @TableField("deleted")
    private Integer deleted;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
} 