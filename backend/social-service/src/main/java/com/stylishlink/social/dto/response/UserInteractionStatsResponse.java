package com.stylishlink.social.dto.response;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 用户互动统计响应DTO
 */
@Data
public class UserInteractionStatsResponse {

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 点赞数
     */
    private Integer likeCount;

    /**
     * 被点赞数
     */
    private Integer likedCount;

    /**
     * 分享数
     */
    private Integer shareCount;

    /**
     * 被分享数
     */
    private Integer sharedCount;

    /**
     * 收藏数
     */
    private Integer collectionCount;

    /**
     * 被收藏数
     */
    private Integer collectedCount;

    /**
     * 评论数
     */
    private Integer commentCount;

    /**
     * 被评论数
     */
    private Integer commentedCount;

    /**
     * 关注数
     */
    private Integer followCount;

    /**
     * 粉丝数
     */
    private Integer followerCount;

    /**
     * 平均评分
     */
    private BigDecimal averageRating;

    /**
     * 总互动数
     */
    private Integer totalInteractions;

    /**
     * 活跃度评分
     */
    private BigDecimal activityScore;

    /**
     * 影响力评分
     */
    private BigDecimal influenceScore;

    /**
     * 今日点赞奖励次数
     */
    private Integer todayLikeRewards;

    /**
     * 今日评论奖励次数
     */
    private Integer todayCommentRewards;

    /**
     * 今日分享奖励次数
     */
    private Integer todayShareRewards;

    /**
     * 今日收藏奖励次数
     */
    private Integer todayCollectionRewards;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
} 