# StylishLink Social Service

## 项目介绍

StylishLink社交服务是穿搭推荐小程序的核心微服务之一，负责处理用户间的社交互动功能，包括内容分享、点赞、评论、收藏、用户互动统计等功能。

## 功能特性

### 核心功能
- **内容分享**: 支持搭配、推荐等内容的多平台分享
- **点赞系统**: 支持对各类内容的点赞/取消点赞操作
- **收藏管理**: 支持创建收藏夹、管理收藏内容
- **评论系统**: 支持多级评论、回复功能
- **互动统计**: 用户互动数据统计与分析
- **奖励机制**: 基于互动行为的灵感值奖励系统

### 技术特性
- **微服务架构**: 基于Spring Cloud的微服务设计
- **服务发现**: 使用Nacos进行服务注册与发现
- **数据持久化**: 使用MySQL + MyBatis Plus
- **缓存支持**: 集成Redis缓存
- **监控健康**: 集成Actuator健康检查
- **参数验证**: 完整的请求参数验证
- **异常处理**: 全局异常处理机制

## 技术栈

| 技术 | 版本 | 说明 |
|------|------|------|
| Java | 17 | 开发语言 |
| Spring Boot | 3.2.x | 应用框架 |
| Spring Cloud | 2023.0.0 | 微服务框架 |
| Nacos | 2022.0.0.0 | 服务发现与配置中心 |
| MySQL | 8.0+ | 关系型数据库 |
| MyBatis Plus | 3.5.x | ORM框架 |
| Redis | 6.0+ | 缓存数据库 |
| Maven | 3.8+ | 构建工具 |

## 项目结构

```
social-service/
├── src/main/java/com/stylishlink/social/
│   ├── SocialServiceApplication.java          # 启动类
│   ├── controller/                            # 控制器层
│   │   └── SocialController.java             # 社交功能控制器
│   ├── service/                               # 服务层
│   │   ├── SocialService.java                # 服务接口
│   │   └── impl/SocialServiceImpl.java       # 服务实现
│   ├── entity/                                # 实体类
│   │   ├── Share.java                        # 分享记录
│   │   ├── Like.java                         # 点赞记录
│   │   ├── Collection.java                   # 收藏夹
│   │   ├── CollectionItem.java               # 收藏项目
│   │   ├── Comment.java                      # 评论
│   │   └── UserInteractionStats.java         # 用户互动统计
│   ├── mapper/                                # 数据访问层
│   ├── dto/                                   # 数据传输对象
│   │   ├── request/                          # 请求DTO
│   │   └── response/                         # 响应DTO
│   ├── config/                                # 配置类
│   │   └── MyBatisPlusConfig.java            # MyBatis Plus配置
│   └── exception/                             # 异常处理
│       └── GlobalExceptionHandler.java       # 全局异常处理器
├── src/main/resources/
│   ├── application.yml                        # 应用配置
│   └── sql/schema.sql                        # 数据库初始化脚本
└── pom.xml                                   # Maven配置
```

## 快速开始

### 环境要求
- JDK 17+
- Maven 3.8+
- MySQL 8.0+
- Redis 6.0+
- Nacos 2.x

### 1. 克隆项目
```bash
git clone <repository-url>
cd stylishlink/backend/social-service
```

### 2. 配置数据库
```bash
# 连接MySQL，执行初始化脚本
mysql -u root -p < src/main/resources/sql/schema.sql
```

### 3. 配置Nacos
确保Nacos服务已启动，默认地址：`http://************:8848`

### 4. 修改配置
编辑 `src/main/resources/application.yml`，根据实际环境修改：
- 数据库连接信息
- Redis连接信息
- Nacos服务地址

### 5. 启动服务
```bash
mvn spring-boot:run
```

服务启动后，默认端口为 `8088`

### 6. 健康检查
访问健康检查端点：
```bash
curl http://localhost:8088/actuator/health
```

## API文档

### 基础信息
- **Base URL**: `http://localhost:8088/api/social`
- **认证方式**: 请求头 `X-User-Id`
- **响应格式**: JSON

### 通用响应结构
```json
{
  "code": 0,
  "message": "success",
  "data": {},
  "timestamp": 1640995200000
}
```

### 主要接口

#### 1. 分享相关
- `POST /shares` - 创建分享记录
- `GET /shares` - 获取用户分享列表
- `GET /shares/{shareId}` - 获取分享详情
- `DELETE /shares/{shareId}` - 删除分享记录

#### 2. 点赞相关
- `POST /likes` - 点赞/取消点赞
- `GET /likes/content` - 获取内容点赞列表
- `GET /likes` - 获取用户点赞列表
- `GET /likes/check` - 检查点赞状态

#### 3. 收藏相关
- `POST /collections` - 创建收藏夹
- `PUT /collections/{collectionId}` - 更新收藏夹
- `DELETE /collections/{collectionId}` - 删除收藏夹
- `GET /collections` - 获取用户收藏夹列表
- `POST /collections/items` - 添加收藏项目
- `DELETE /collections/{collectionId}/items/{contentId}` - 移除收藏项目

#### 4. 评论相关
- `POST /comments` - 发表评论
- `DELETE /comments/{commentId}` - 删除评论
- `GET /comments/content` - 获取内容评论列表
- `GET /comments/{commentId}/replies` - 获取评论回复

#### 5. 统计相关
- `GET /stats` - 获取用户互动统计
- `POST /stats/reset-daily-rewards` - 重置每日奖励
- `GET /stats/can-reward` - 检查奖励资格

详细API文档请参考：[social-service-api.md](../../doc/api/social-service-api.md)

## 数据库设计

### 主要表结构

#### 1. social_share (分享记录表)
- 存储用户分享的内容记录
- 支持多平台分享统计

#### 2. social_like (点赞记录表)
- 存储用户点赞行为
- 支持点赞状态切换

#### 3. social_collection (收藏夹表)
- 存储用户创建的收藏夹
- 支持公开/私有设置

#### 4. social_collection_item (收藏项目表)
- 存储收藏夹中的具体内容
- 支持备注和标签

#### 5. social_comment (评论表)
- 存储用户评论内容
- 支持多级评论结构

#### 6. social_user_interaction_stats (用户互动统计表)
- 存储用户互动数据统计
- 支持奖励机制管理

## 配置说明

### 应用配置
```yaml
# 服务端口
server:
  port: 8088

# 数据源配置
spring:
  datasource:
    url: ******************************************
    username: root
    password: 123456

# Redis配置
spring:
  redis:
    host: ************
    port: 6379
    database: 4

# Nacos配置
spring:
  cloud:
    nacos:
      discovery:
        server-addr: ************:8848
```

### 业务配置
```yaml
# 互动奖励配置
social:
  reward:
    like-reward: 2          # 点赞奖励
    comment-reward: 5       # 评论奖励
    share-reward: 10        # 分享奖励
    collection-reward: 3    # 收藏奖励
    daily-like-limit: 30    # 每日点赞奖励上限
```

## 开发指南

### 代码规范
- 遵循阿里巴巴Java开发手册
- 使用Lombok简化代码
- 统一异常处理
- 完整的参数验证

### 测试
```bash
# 运行单元测试
mvn test

# 运行集成测试
mvn integration-test
```

### 部署
```bash
# 构建项目
mvn clean package

# 构建Docker镜像
docker build -t social-service:latest .

# 运行容器
docker run -p 8088:8088 social-service:latest
```

## 监控与运维

### 健康检查
- 端点：`/actuator/health`
- 检查数据库连接、Redis连接等

### 指标监控
- 端点：`/actuator/metrics`
- 支持Prometheus格式导出

### 日志管理
- 日志文件：`logs/social-service.log`
- 支持日志级别动态调整

## 常见问题

### Q1: 服务启动失败
**A**: 检查数据库连接配置和Nacos服务状态

### Q2: 接口返回401错误
**A**: 检查请求头是否包含 `X-User-Id`

### Q3: 数据库连接超时
**A**: 检查数据库服务状态和网络连接

### Q4: Redis连接失败
**A**: 检查Redis服务状态和配置信息

## 更新日志

### v1.0.0 (2025-01-12)
- 初始版本发布
- 实现基础社交功能
- 支持分享、点赞、收藏、评论
- 集成奖励机制
- 完善监控和健康检查

## 贡献指南

1. Fork 项目
2. 创建特性分支
3. 提交代码变更
4. 推送到分支
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 联系方式

- 项目维护者：StylishLink Team
- 邮箱：<EMAIL>
- 文档：[项目文档](../../doc/) 