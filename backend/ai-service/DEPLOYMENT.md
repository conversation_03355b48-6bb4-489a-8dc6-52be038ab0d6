# AI Service 部署指南

## 快速开始

### 1. 环境要求
- JDK 17+
- Maven 3.6+
- MySQL 8.0+
- Redis 6.0+
- Nacos 2.0+

### 2. 数据库准备
```sql
-- 创建数据库
CREATE DATABASE stylishlink CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE stylishlink;

-- 执行建表脚本
source src/main/resources/sql/ai_service_schema.sql;
```

### 3. 配置环境变量
```bash
# 数据库配置
export DB_HOST=************
export DB_PORT=3306
export DB_NAME=stylishlink
export DB_USERNAME=root
export DB_PASSWORD=123456

# Redis配置
export REDIS_HOST=************
export REDIS_PORT=6379
export REDIS_DATABASE=2

# AI服务配置
export AI_OPENAI_API_KEY=your_openai_api_key
export AI_OPENAI_BASE_URL=https://api.openai.com
export AI_OPENAI_MODEL=gpt-3.5-turbo

# Nacos配置
export NACOS_SERVER_ADDR=************:8848
```

### 4. 编译项目
```bash
mvn clean compile
```

### 5. 运行服务
```bash
# 开发环境
mvn spring-boot:run -Dspring-boot.run.profiles=dev

# 或者打包后运行
mvn clean package -DskipTests
java -jar target/ai-service-1.0.0-SNAPSHOT.jar --spring.profiles.active=dev
```

### 6. 验证服务
```bash
# 健康检查
curl http://localhost:8084/actuator/health

# 测试AI对话接口
curl -X POST http://localhost:8084/api/ai/chat \
  -H "Content-Type: application/json" \
  -d '{
    "message": "今天穿什么好？",
    "userId": "test_user"
  }'
```



### 日志配置
- 应用日志：`logs/ai-service.log`
- 错误日志：`logs/error.log`

## 故障排查

### 常见问题
1. **数据库连接失败**
   - 检查数据库配置
   - 确认数据库服务状态
   - 验证网络连接

2. **AI服务调用失败**
   - 检查API密钥配置
   - 确认网络连接
   - 查看错误日志

3. **Nacos注册失败**
   - 检查Nacos服务状态
   - 验证配置参数
   - 查看网络连接

### 日志查看
```bash
# 查看应用日志
tail -f logs/ai-service.log

# 查看错误日志
tail -f logs/error.log

# 查看JVM状态
jstat -gc [pid]
```

## 性能调优

### JVM 参数
```bash
java -Xms512m -Xmx2g -XX:+UseG1GC \
     -XX:MaxGCPauseMillis=200 \
     -jar ai-service-1.0.0-SNAPSHOT.jar
```

### 数据库连接池
```yaml
spring:
  datasource:
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
```

### Redis 连接池
```yaml
spring:
  redis:
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0
        max-wait: -1ms
``` 