-- AI服务数据库表结构

-- 图像识别结果表
CREATE TABLE `ai_recognition_result` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `user_id` VARCHAR(64) NOT NULL COMMENT '用户ID',
    `image_url` VARCHAR(500) NOT NULL COMMENT '图片URL',
    `recognition_type` VARCHAR(32) NOT NULL COMMENT '识别类型(clothing/accessory)',
    `category` VARCHAR(64) COMMENT '类别',
    `colors` JSON COMMENT '颜色列表',
    `style` VARCHAR(64) COMMENT '风格',
    `materials` JSON COMMENT '材质列表',
    `patterns` JSON COMMENT '图案列表',
    `confidence` DECIMAL(5,4) COMMENT '置信度',
    `raw_result` JSON COMMENT '原始识别结果',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_recognition_type` (`recognition_type`),
    INDEX `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='图像识别结果表';

-- 风格分析结果表
CREATE TABLE `ai_style_analysis` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `user_id` VARCHAR(64) NOT NULL COMMENT '用户ID',
    `source_type` VARCHAR(32) NOT NULL COMMENT '来源类型(image/outfit)',
    `source_id` VARCHAR(64) NOT NULL COMMENT '来源ID',
    `style_distribution` TEXT COMMENT '风格分布(JSON字符串)',
    `dominant_colors` TEXT COMMENT '主导颜色(JSON字符串)',
    `recommended_styles` TEXT COMMENT '推荐风格(JSON字符串)',
    `material_analysis` TEXT COMMENT '材质分析(JSON字符串)',
    `pattern_analysis` TEXT COMMENT '图案分析(JSON字符串)',
    `season_suitability` TEXT COMMENT '季节适应性(JSON字符串)',
    `occasion_suitability` TEXT COMMENT '场合适应性(JSON字符串)',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_source` (`source_type`, `source_id`),
    INDEX `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='风格分析结果表';

-- 色彩分析结果表
CREATE TABLE `ai_color_analysis_result` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `user_id` VARCHAR(64) NOT NULL COMMENT '用户ID',
    `image_url` VARCHAR(500) NOT NULL COMMENT '图片URL',
    `dominant_colors` JSON COMMENT '主色调',
    `color_harmony` DECIMAL(5,4) COMMENT '色彩和谐度',
    `color_suggestions` JSON COMMENT '配色建议',
    `confidence` DECIMAL(5,4) COMMENT '置信度',
    `raw_result` JSON COMMENT '原始分析结果',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='色彩分析结果表';

-- 身材识别分析结果表
CREATE TABLE `ai_body_shape_analysis_result` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `user_id` VARCHAR(64) NOT NULL COMMENT '用户ID',
    `image_url` VARCHAR(500) NOT NULL COMMENT '图片URL',
    `is_full_body_photo` BOOLEAN DEFAULT FALSE COMMENT '是否为正面全身照',
    `confidence` DECIMAL(5,2) COMMENT '识别置信度(0-100)',
    `body_type` VARCHAR(32) COMMENT '推荐体型分类',
    `body_shape` JSON COMMENT '详细身材数据',
    `analysis` JSON COMMENT '身材分析结果',
    `suggestions` JSON COMMENT '穿搭建议',
    `reason` VARCHAR(200) COMMENT '失败原因',
    `raw_result` TEXT COMMENT '原始AI响应结果',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_body_type` (`body_type`),
    INDEX `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='身材识别分析结果表';

-- 衣物识别结果表
CREATE TABLE `ai_clothing_recognition_result` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `recognition_id` VARCHAR(64) NOT NULL COMMENT '识别结果ID',
    `user_id` VARCHAR(64) COMMENT '用户ID',
    `file_id` VARCHAR(64) COMMENT '文件ID',
    `image_url` VARCHAR(500) COMMENT '图片URL',
    `name` VARCHAR(100) COMMENT '衣物名称',
    `category` VARCHAR(64) COMMENT '大类别',
    `sub_category` VARCHAR(64) COMMENT '子类别',
    `colors` JSON COMMENT '颜色列表',
    `materials` JSON COMMENT '材质列表',
    `seasons` JSON COMMENT '适合季节',
    `occasions` JSON COMMENT '适合场合',
    `wuxing_metal` INT DEFAULT 0 COMMENT '金属性评分(0-4)',
    `wuxing_wood` INT DEFAULT 0 COMMENT '木属性评分(0-4)',
    `wuxing_water` INT DEFAULT 0 COMMENT '水属性评分(0-4)',
    `wuxing_fire` INT DEFAULT 0 COMMENT '火属性评分(0-4)',
    `wuxing_earth` INT DEFAULT 0 COMMENT '土属性评分(0-4)',
    `wuxing_primary` VARCHAR(10) COMMENT '主要五行属性',
    `confidence` DECIMAL(5,4) COMMENT '识别置信度(0-1)',
    `suggestions` JSON COMMENT '穿搭建议',
    `raw_result` JSON COMMENT '原始AI响应结果',
    `processed_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '处理时间',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_recognition_id` (`recognition_id`),
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_file_id` (`file_id`),
    INDEX `idx_category` (`category`),
    INDEX `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='衣物识别结果表';

-- AI对话记录表
CREATE TABLE `ai_chat_record` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `user_id` VARCHAR(64) NOT NULL COMMENT '用户ID',
    `session_id` VARCHAR(64) NOT NULL COMMENT '会话ID',
    `message_type` VARCHAR(16) NOT NULL COMMENT '消息类型(user/assistant)',
    `message` TEXT NOT NULL COMMENT '消息内容',
    `context` JSON COMMENT '上下文信息',
    `ai_model` VARCHAR(64) COMMENT 'AI模型名称',
    `tokens_used` INT COMMENT '使用的token数量',
    `response_time` INT COMMENT '响应时间(毫秒)',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    INDEX `idx_user_session` (`user_id`, `session_id`),
    INDEX `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI对话记录表';

-- 视频模板表
CREATE TABLE `ai_video_template` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `name` VARCHAR(128) NOT NULL COMMENT '模板名称',
    `description` TEXT COMMENT '模板描述',
    `style` VARCHAR(64) COMMENT '视频风格',
    `occasions` JSON COMMENT '适合场合',
    `duration` INT NOT NULL COMMENT '视频时长(秒)',
    `preview_url` VARCHAR(500) COMMENT '预览视频URL',
    `thumbnail_url` VARCHAR(500) COMMENT '缩略图URL',
    `is_active` BOOLEAN DEFAULT TRUE COMMENT '是否可用',
    `parameters` JSON COMMENT '模板参数',
    `required_assets` JSON COMMENT '所需素材类型',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    INDEX `idx_style` (`style`),
    INDEX `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='视频模板表';

-- 视频生成任务表
CREATE TABLE `ai_video_generation_task` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `task_id` VARCHAR(64) NOT NULL UNIQUE COMMENT '任务ID',
    `user_id` VARCHAR(64) NOT NULL COMMENT '用户ID',
    `template_id` BIGINT COMMENT '模板ID',
    `status` VARCHAR(32) NOT NULL DEFAULT 'PENDING' COMMENT '任务状态',
    `outfit_id` VARCHAR(64) COMMENT '搭配ID',
    `asset_urls` JSON COMMENT '素材URL列表',
    `result_url` VARCHAR(500) COMMENT '结果视频URL',
    `error_message` TEXT COMMENT '错误信息',
    `progress` INT DEFAULT 0 COMMENT '生成进度(百分比)',
    `priority` INT DEFAULT 0 COMMENT '任务优先级',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `completed_at` TIMESTAMP NULL COMMENT '完成时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_task_id` (`task_id`),
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_status` (`status`),
    INDEX `idx_priority` (`priority`),
    INDEX `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='视频生成任务表';

-- 形象评估记录表
CREATE TABLE `ai_appearance_evaluation` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `user_id` VARCHAR(64) NOT NULL COMMENT '用户ID',
    `evaluation_type` VARCHAR(32) NOT NULL COMMENT '评估类型(appearance/outfit)',
    `source_id` VARCHAR(64) COMMENT '来源ID',
    `image_urls` JSON COMMENT '图片URL列表',
    `overall_score` DECIMAL(5,2) COMMENT '总体评分',
    `style_score` DECIMAL(5,2) COMMENT '风格评分',
    `color_score` DECIMAL(5,2) COMMENT '色彩评分',
    `fit_score` DECIMAL(5,2) COMMENT '合身度评分',
    `suggestion` TEXT COMMENT '建议',
    `detailed_analysis` JSON COMMENT '详细分析',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_evaluation_type` (`evaluation_type`),
    INDEX `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='形象评估记录表';

-- AI评分记录表
CREATE TABLE `ai_rating_record` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `user_id` VARCHAR(64) NOT NULL COMMENT '用户ID',
    `rating_type` VARCHAR(32) NOT NULL COMMENT '评分类型(outfit/recommendation)',
    `target_id` VARCHAR(64) NOT NULL COMMENT '目标ID',
    `ai_score` DECIMAL(5,2) COMMENT 'AI评分',
    `rule_score` DECIMAL(5,2) COMMENT '规则评分',
    `final_score` DECIMAL(5,2) NOT NULL COMMENT '最终评分',
    `score_breakdown` JSON COMMENT '评分明细',
    `improvement_suggestions` JSON COMMENT '改进建议',
    `ai_model` VARCHAR(64) COMMENT 'AI模型名称',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_rating_type` (`rating_type`),
    INDEX `idx_target_id` (`target_id`),
    INDEX `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI评分记录表';

-- 向量存储表
CREATE TABLE `ai_vector_storage` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `vector_id` VARCHAR(64) NOT NULL UNIQUE COMMENT '向量ID',
    `vector_type` VARCHAR(32) NOT NULL COMMENT '向量类型(image/style/text)',
    `source_id` VARCHAR(64) NOT NULL COMMENT '来源ID',
    `vector_data` JSON NOT NULL COMMENT '向量数据',
    `dimension` INT NOT NULL COMMENT '向量维度',
    `metadata` JSON COMMENT '元数据',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_vector_id` (`vector_id`),
    INDEX `idx_vector_type` (`vector_type`),
    INDEX `idx_source_id` (`source_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='向量存储表'; 