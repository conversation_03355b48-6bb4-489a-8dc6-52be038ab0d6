-- 身材识别分析结果表建表脚本

DROP TABLE IF EXISTS `ai_body_shape_analysis_result`;

CREATE TABLE `ai_body_shape_analysis_result` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `user_id` VARCHAR(64) NOT NULL COMMENT '用户ID',
    `image_url` VARCHAR(500) NOT NULL COMMENT '图片URL',
    `is_full_body_photo` BOOLEAN NOT NULL COMMENT '是否为正面全身照',
    `confidence` DECIMAL(5,2) COMMENT '识别置信度（0-100）',
    `body_type` VARCHAR(32) COMMENT '推荐体型分类（slim/normal/chubby）',
    `body_shape` JSON COMMENT '详细身材数据（JSON格式存储）',
    `analysis` JSON COMMENT '身材分析结果（JSON格式存储）',
    `suggestions` JSON COMMENT '穿搭建议（JSON格式存储）',
    `reason` TEXT COMMENT '失败原因（仅当isFullBodyPhoto为false时）',
    `raw_result` TEXT COMMENT '原始AI响应结果',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_is_full_body` (`is_full_body_photo`),
    INDEX `idx_body_type` (`body_type`),
    INDEX `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='身材识别分析结果表';

-- 字段说明：
-- body_shape字段存储的JSON格式示例：
-- {
--   "shoulderWidth": 3,
--   "waistShape": 3,
--   "belly": 1,
--   "hip": 3,
--   "hipShape": 3,
--   "armLength": 3,
--   "armCircum": 3,
--   "hipWidth": 3,
--   "thigh": 3,
--   "calf": 3,
--   "bodyFat": 3,
--   "bodyLength": 3
-- }

-- analysis字段存储的JSON格式示例：
-- [
--   {
--     "feature": "肩膀宽度",
--     "description": "肩膀宽度适中，比例协调",
--     "type": "positive"
--   }
-- ]

-- suggestions字段存储的JSON格式示例：
-- [
--   {
--     "category": "上装选择",
--     "content": "建议选择修身剪裁的上装",
--     "priority": "high"
--   }
-- ] 