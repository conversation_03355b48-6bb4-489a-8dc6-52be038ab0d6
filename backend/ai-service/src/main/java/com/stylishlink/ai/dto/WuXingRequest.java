package com.stylishlink.ai.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.util.Map;

/**
 * 五行分析请求DTO
 */
@Data
@Schema(description = "五行分析请求")
public class WuXingRequest {
    
    @NotBlank(message = "八字信息不能为空")
    @Schema(description = "八字信息", example = "庚午 戊子 甲寅 丙寅")
    private String baziInfo;
    
    @Schema(description = "五行属性", example = "{\"金\":2,\"木\":2,\"水\":1,\"火\":2,\"土\":1}")
    private Map<String, Integer> wuxingAttributes;
    
    @Schema(description = "分析类型", example = "personality", allowableValues = {"personality", "health", "career", "luck"})
    private String analysisType = "personality";
    
    @Schema(description = "用户ID（可选）", example = "user123")
    private String userId;
} 