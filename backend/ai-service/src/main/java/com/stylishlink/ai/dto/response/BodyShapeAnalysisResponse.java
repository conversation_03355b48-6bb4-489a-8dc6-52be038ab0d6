package com.stylishlink.ai.dto.response;

import lombok.Data;

import java.util.List;

/**
 * 身材识别响应DTO
 */
@Data
public class BodyShapeAnalysisResponse {

    /**
     * 是否为正面全身照
     */
    private Boolean isFullBodyPhoto;

    /**
     * 获取是否为正面全身照
     * @return 是否为正面全身照
     */
    public Boolean isFullBodyPhoto() {
        return isFullBodyPhoto;
    }

    /**
     * 设置是否为正面全身照
     * @param isFullBodyPhoto 是否为正面全身照
     */
    public void setFullBodyPhoto(Boolean isFullBodyPhoto) {
        this.isFullBodyPhoto = isFullBodyPhoto;
    }

    /**
     * 识别置信度（0-100）
     */
    private Double confidence;

    /**
     * 推荐体型分类（slim/normal/chubby）
     */
    private String bodyType;

    /**
     * 详细身材数据
     */
    private BodyShape bodyShape;

    /**
     * 身材分析结果
     */
    private List<BodyAnalysis> analysis;

    /**
     * 穿搭建议
     */
    private List<StyleSuggestion> suggestions;

    /**
     * 失败原因（仅当isFullBodyPhoto为false时）
     */
    private String reason;

    /**
     * 详细身材数据
     */
    @Data
    public static class BodyShape {
        /**
         * 肩膀宽度（1-5）
         * 1:窄, 2:偏窄, 3:正常, 4:偏宽, 5:宽
         */
        private Integer shoulderWidth;

        /**
         * 腰型（1-5）
         * 1:直筒, 2:略有曲线, 3:有曲线, 4:曲线较明显, 5:曲线明显
         */
        private Integer waistShape;

        /**
         * 肚腩（1-5）
         * 1:没有, 2:略有小肚腩, 3:小肚腩, 4:偏大肚腩, 5:大肚腩
         */
        private Integer belly;

        /**
         * 臀型（1-5）
         * 1:下榻, 2:略有上翘, 3:正常, 4:较上翘, 5:上翘
         */
        private Integer hip;

        /**
         * 胯型（1-5）
         * 1:直筒, 2:略有曲线, 3:有曲线, 4:曲线较明显, 5:曲线明显
         */
        private Integer hipShape;

        /**
         * 臂长（1-5）
         * 1:短, 2:偏短, 3:正常, 4:偏长, 5:长
         */
        private Integer armLength;

        /**
         * 臂围（1-5）
         * 1:细, 2:偏细, 3:正常, 4:偏粗, 5:粗
         */
        private Integer armCircum;

        /**
         * 胯部宽度（1-5）
         * 1:窄, 2:偏窄, 3:正常, 4:偏宽, 5:宽
         */
        private Integer hipWidth;

        /**
         * 大腿（1-5）
         * 1:细, 2:偏细, 3:正常, 4:偏粗, 5:粗
         */
        private Integer thigh;

        /**
         * 小腿（1-5）
         * 1:细, 2:偏细, 3:正常, 4:偏粗, 5:粗
         */
        private Integer calf;

        /**
         * 上下身粗细（1-5）
         * 1:上身粗, 2:偏上身粗, 3:匀称, 4:偏下身粗, 5:下身粗
         */
        private Integer bodyFat;

        /**
         * 上下身长短（1-5）
         * 1:上身长, 2:偏上身长, 3:匀称, 4:偏下身长, 5:下身长
         */
        private Integer bodyLength;
    }

    /**
     * 身材分析结果
     */
    @Data
    public static class BodyAnalysis {
        /**
         * 分析特征点
         */
        private String feature;

        /**
         * 特征描述
         */
        private String description;

        /**
         * 类型（positive/neutral）
         */
        private String type;
    }

    /**
     * 穿搭建议
     */
    @Data
    public static class StyleSuggestion {
        /**
         * 建议类别
         */
        private String category;

        /**
         * 建议内容
         */
        private String content;

        /**
         * 优先级（high/medium/low）
         */
        private String priority;
    }
} 