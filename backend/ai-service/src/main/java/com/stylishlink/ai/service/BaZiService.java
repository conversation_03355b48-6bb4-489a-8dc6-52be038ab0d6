package com.stylishlink.ai.service;

import com.stylishlink.ai.dto.BaZiRequest;

import java.util.Map;

/**
 * 八字分析服务接口
 */
public interface BaZiService {
    
    /**
     * 计算八字
     * @param request 八字计算请求
     * @return 八字计算结果
     */
    Map<String, Object> calculateBaZi(BaZiRequest request);
    
    /**
     * 调用AI进行文案简化
     * @param prompt AI提示词
     * @return AI响应结果
     */
    Map<String, Object> callAiForSummarize(String prompt);
} 