package com.stylishlink.ai.dto.request;

import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import jakarta.validation.constraints.NotNull;

/**
 * 图像识别请求DTO
 */
@Data
public class RecognizeRequest {

    /**
     * 图片文件
     */
    @NotNull(message = "图片文件不能为空")
    private MultipartFile file;

    /**
     * 识别类型（clothing/accessory）
     */
    private String type;

    /**
     * 用户ID
     */
    private String userId;
} 