package com.stylishlink.ai.dto.request;

import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import java.util.Map;

/**
 * 能量计算请求DTO
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EnergyCalculationRequest {
    
    /**
     * 用户ID
     */
    @NotBlank(message = "用户ID不能为空")
    private String userId;
    
    /**
     * 计算日期（格式：YYYY-MM-DD）
     */
    @NotBlank(message = "日期不能为空")
    @Pattern(regexp = "\\d{4}-\\d{2}-\\d{2}", message = "日期格式不正确，应为YYYY-MM-DD")
    private String date;
    
    /**
     * 八字信息
     */
    @Valid
    @NotNull(message = "八字信息不能为空")
    private BaziInfo baziInfo;
    
    /**
     * 用户基本信息
     */
    @Valid
    @NotNull(message = "用户信息不能为空")
    private UserInfo userInfo;
    
    /**
     * 八字信息
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class BaziInfo {
        /**
         * 年柱
         */
        private String yearPillar;
        
        /**
         * 月柱
         */
        private String monthPillar;
        
        /**
         * 日柱
         */
        private String dayPillar;
        
        /**
         * 时柱
         */
        private String hourPillar;
        
        /**
         * 日主
         */
        private String dayMaster;
        
        /**
         * 五行元素分布
         */
        private Map<String, Object> elements;
    }
    
    /**
     * 用户基本信息
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class UserInfo {
        /**
         * 性别（0未知/1男/2女）
         */
        private Integer gender;
        
        /**
         * 出生日期
         */
        private String birthDate;
        
        /**
         * 出生时间
         */
        private String birthTime;
        
        /**
         * 出生地点
         */
        private String birthPlace;
        
        /**
         * 姓名
         */
        private String fullName;
    }
} 