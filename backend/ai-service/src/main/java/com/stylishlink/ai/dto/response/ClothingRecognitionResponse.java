package com.stylishlink.ai.dto.response;

import lombok.Data;

import java.util.List;

/**
 * 衣物识别响应DTO（对应API文档3.2.1接口）
 */
@Data
public class ClothingRecognitionResponse {

    /**
     * 识别结果ID
     */
    private String recognitionId;

    /**
     * 文件ID（通过文件ID识别时返回）
     */
    private String fileId;

    /**
     * 衣物名称
     */
    private String name;

    /**
     * 大类别（上衣/下装/外套/裙装/鞋履/配饰等）
     */
    private String category;

    /**
     * 子类别（T恤/衬衫/牛仔裤等）
     */
    private String subCategory;

    /**
     * 颜色列表
     */
    private List<String> colors;

    /**
     * 材质列表
     */
    private List<String> materials;

    /**
     * 适合季节（春/夏/秋/冬）
     */
    private List<String> seasons;

    /**
     * 适合场合（日常休闲/职场商务/约会交际/派对活动/运动健身/正式场合）
     */
    private List<String> occasions;

    /**
     * 五行属性分析
     */
    private WuxingAttributes wuxing;

    /**
     * 识别置信度（0-1）
     */
    private Double confidence;

    /**
     * 穿搭建议
     */
    private List<String> suggestions;

    /**
     * 处理时间
     */
    private String processedAt;

    /**
     * 五行属性内部类
     */
    @Data
    public static class WuxingAttributes {
        /**
         * 金属性评分（0-4）
         */
        private Integer metal;

        /**
         * 木属性评分（0-4）
         */
        private Integer wood;

        /**
         * 水属性评分（0-4）
         */
        private Integer water;

        /**
         * 火属性评分（0-4）
         */
        private Integer fire;

        /**
         * 土属性评分（0-4）
         */
        private Integer earth;

        /**
         * 主要五行属性（金/木/水/火/土）
         */
        private String primary;
    }
} 