package com.stylishlink.ai.client;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 文件服务Feign客户端接口
 * 用于调用file-service的相关接口
 */
@FeignClient(name = "file-service", path = "/files", fallbackFactory = FileServiceClientFallbackFactory.class)
public interface FileServiceClient {

    /**
     * 上传文件到文件服务
     * 
     * @param file 要上传的文件
     * @param userId 用户ID
     * @param category 文件类别 (枚举值：CLOTHING_IMAGE等)
     * @param accessLevel 访问级别 (枚举值：PUBLIC等)
     * @return 文件上传响应
     */
    @PostMapping(value = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    FileUploadResponse uploadFile(@RequestPart("file") MultipartFile file,
                                  @RequestParam("userId") String userId,
                                  @RequestParam("category") String category,
                                  @RequestParam("accessLevel") String accessLevel);

    /**
     * 获取文件信息
     * 
     * @param fileId 文件ID
     * @return 文件信息响应
     */
    @GetMapping("/{fileId}")
    FileInfoResponse getFileInfo(@PathVariable("fileId") String fileId);

    /**
     * 获取文件下载链接
     * 
     * @param fileId 文件ID
     * @param expiresIn 过期时间(秒)
     * @return 下载链接响应
     */
    @GetMapping("/{fileId}/download-url")
    DownloadUrlResponse getDownloadUrl(@PathVariable("fileId") String fileId, 
                                       @RequestParam("expiresIn") int expiresIn);

    /**
     * 删除文件
     * 
     * @param fileId 文件ID
     * @param force 是否强制删除
     * @return 删除结果响应
     */
    @DeleteMapping("/{fileId}")
    DeleteFileResponse deleteFile(@PathVariable("fileId") String fileId, 
                                  @RequestParam("force") boolean force);

    // 响应DTO类
    class FileUploadResponse {
        private String code;
        private String message;
        private FileData data;

        // getters and setters
        public String getCode() { return code; }
        public void setCode(String code) { this.code = code; }
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        public FileData getData() { return data; }
        public void setData(FileData data) { this.data = data; }

        public static class FileData {
            private String fileId;
            private String url;
            private String fileName;
            private Long fileSize;

            // getters and setters
            public String getFileId() { return fileId; }
            public void setFileId(String fileId) { this.fileId = fileId; }
            public String getUrl() { return url; }
            public void setUrl(String url) { this.url = url; }
            public String getFileName() { return fileName; }
            public void setFileName(String fileName) { this.fileName = fileName; }
            public Long getFileSize() { return fileSize; }
            public void setFileSize(Long fileSize) { this.fileSize = fileSize; }
        }
    }

    class FileInfoResponse {
        private String code;
        private String message;
        private Object data;
        private String timestamp;

        public String getCode() { return code; }
        public void setCode(String code) { this.code = code; }
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        public Object getData() { return data; }
        public void setData(Object data) { this.data = data; }
        public String getTimestamp() { return timestamp; }
        public void setTimestamp(String timestamp) { this.timestamp = timestamp; }
    }

    class DownloadUrlResponse {
        private String code;
        private String message;
        private String data;

        public String getCode() { return code; }
        public void setCode(String code) { this.code = code; }
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        public String getData() { return data; }
        public void setData(String data) { this.data = data; }
    }

    class DeleteFileResponse {
        private String code;
        private String message;
        private Boolean data;

        public String getCode() { return code; }
        public void setCode(String code) { this.code = code; }
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        public Boolean getData() { return data; }
        public void setData(Boolean data) { this.data = data; }
    }
} 