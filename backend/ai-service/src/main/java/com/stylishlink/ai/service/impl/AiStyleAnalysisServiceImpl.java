package com.stylishlink.ai.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.stylishlink.ai.client.AiClient;
import com.stylishlink.ai.dto.request.StyleAnalysisRequest;
import com.stylishlink.ai.dto.response.StyleAnalysisResponse;
import com.stylishlink.ai.entity.AiStyleAnalysis;
import com.stylishlink.ai.mapper.AiStyleAnalysisMapper;
import com.stylishlink.ai.service.AiStyleAnalysisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 风格分析服务实现
 */
@Slf4j
@Service
public class AiStyleAnalysisServiceImpl implements AiStyleAnalysisService {

    @Autowired
    private AiClient aiClient;

    @Autowired
    private AiStyleAnalysisMapper styleAnalysisMapper;

    @Autowired
    private ObjectMapper objectMapper;

    @Override
    public StyleAnalysisResponse analyzeImageStyle(StyleAnalysisRequest request) {
        try {
            // 调用AI服务进行风格分析
            Map<String, Object> aiResult = aiClient.analyzeStyle(request.getImageUrl());

            // 构建响应对象
            StyleAnalysisResponse response = buildStyleAnalysisResponse(aiResult);

            // 保存分析结果到数据库
            saveStyleAnalysisResult(request, aiResult, response);

            log.info("图片风格分析完成，用户: {}, 来源: {}", request.getUserId(), request.getSourceId());
            return response;

        } catch (Exception e) {
            log.error("图片风格分析失败", e);
            throw new RuntimeException("图片风格分析失败: " + e.getMessage());
        }
    }

    @Override
    public StyleAnalysisResponse analyzeOutfitStyle(StyleAnalysisRequest request) {
        try {
            // 对于搭配风格分析，这里可以调用不同的AI接口或使用不同的分析逻辑
            // 暂时使用相同的分析方法
            Map<String, Object> aiResult = aiClient.analyzeStyle("outfit:" + request.getOutfitId());

            // 构建响应对象
            StyleAnalysisResponse response = buildStyleAnalysisResponse(aiResult);

            // 保存分析结果到数据库
            saveStyleAnalysisResult(request, aiResult, response);

            log.info("搭配风格分析完成，用户: {}, 搭配ID: {}", request.getUserId(), request.getOutfitId());
            return response;

        } catch (Exception e) {
            log.error("搭配风格分析失败", e);
            throw new RuntimeException("搭配风格分析失败: " + e.getMessage());
        }
    }

    @Override
    public StyleAnalysisResponse analyzeColor(StyleAnalysisRequest request) {
        try {
            // 调用AI服务进行色彩分析
            String imageUrl = "image".equals(request.getSourceType()) ? 
                request.getImageUrl() : "outfit:" + request.getOutfitId();
            Map<String, Object> aiResult = aiClient.analyzeColor(imageUrl);

            // 构建响应对象
            StyleAnalysisResponse response = buildColorAnalysisResponse(aiResult);

            // 保存分析结果到数据库
            saveStyleAnalysisResult(request, aiResult, response);

            log.info("色彩分析完成，用户: {}, 来源: {}", request.getUserId(), request.getSourceId());
            return response;

        } catch (Exception e) {
            log.error("色彩分析失败", e);
            throw new RuntimeException("色彩分析失败: " + e.getMessage());
        }
    }

    /**
     * 构建风格分析响应对象
     */
    private StyleAnalysisResponse buildStyleAnalysisResponse(Map<String, Object> aiResult) {
        StyleAnalysisResponse response = new StyleAnalysisResponse();
        
        if (aiResult.get("styleDistribution") instanceof Map) {
            response.setStyleDistribution((Map<String, Double>) aiResult.get("styleDistribution"));
        }
        
        if (aiResult.get("dominantColors") instanceof String[]) {
            response.setDominantColors(Arrays.asList((String[]) aiResult.get("dominantColors")));
        }
        
        if (aiResult.get("recommendedStyles") instanceof String[]) {
            response.setRecommendedStyles(Arrays.asList((String[]) aiResult.get("recommendedStyles")));
        }

        if (aiResult.get("materialAnalysis") instanceof Map) {
            response.setMaterialAnalysis((Map<String, Double>) aiResult.get("materialAnalysis"));
        }

        if (aiResult.get("patternAnalysis") instanceof String[]) {
            response.setPatternAnalysis(Arrays.asList((String[]) aiResult.get("patternAnalysis")));
        }

        if (aiResult.get("seasonSuitability") instanceof Map) {
            response.setSeasonSuitability((Map<String, Double>) aiResult.get("seasonSuitability"));
        }

        if (aiResult.get("occasionSuitability") instanceof Map) {
            response.setOccasionSuitability((Map<String, Double>) aiResult.get("occasionSuitability"));
        }

        return response;
    }

    /**
     * 构建色彩分析响应对象
     */
    private StyleAnalysisResponse buildColorAnalysisResponse(Map<String, Object> aiResult) {
        StyleAnalysisResponse response = new StyleAnalysisResponse();
        
        if (aiResult.get("dominantColors") instanceof String[]) {
            response.setDominantColors(Arrays.asList((String[]) aiResult.get("dominantColors")));
        }
        
        if (aiResult.get("seasonSuitability") instanceof Map) {
            response.setSeasonSuitability((Map<String, Double>) aiResult.get("seasonSuitability"));
        }

        return response;
    }

    /**
     * 保存风格分析结果到数据库
     */
    private void saveStyleAnalysisResult(StyleAnalysisRequest request, Map<String, Object> aiResult, StyleAnalysisResponse response) {
        try {
            AiStyleAnalysis entity = new AiStyleAnalysis();
            entity.setUserId(request.getUserId());
            entity.setSourceType(request.getSourceType());
            entity.setSourceId(request.getSourceId());
            
            // 将复杂对象转换为JSON字符串存储
            entity.setStyleDistribution(convertToJsonString(response.getStyleDistribution()));
            entity.setDominantColors(convertToJsonString(response.getDominantColors()));
            entity.setRecommendedStyles(convertToJsonString(response.getRecommendedStyles()));
            entity.setMaterialAnalysis(convertToJsonString(response.getMaterialAnalysis()));
            entity.setPatternAnalysis(convertToJsonString(response.getPatternAnalysis()));
            entity.setSeasonSuitability(convertToJsonString(response.getSeasonSuitability()));
            entity.setOccasionSuitability(convertToJsonString(response.getOccasionSuitability()));
            
            entity.setCreatedAt(LocalDateTime.now());
            entity.setUpdatedAt(LocalDateTime.now());

            styleAnalysisMapper.insert(entity);
            response.setAnalysisId(entity.getId());

        } catch (Exception e) {
            log.error("保存风格分析结果失败", e);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 将对象转换为JSON字符串
     */
    private String convertToJsonString(Object obj) {
        if (obj == null) {
            return null;
        }
        try {
            return objectMapper.writeValueAsString(obj);
        } catch (Exception e) {
            log.warn("对象转换为JSON字符串失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 将JSON字符串转换为指定类型的对象
     */
    private <T> T convertFromJsonString(String jsonStr, Class<T> clazz) {
        if (jsonStr == null || jsonStr.trim().isEmpty()) {
            return null;
        }
        try {
            return objectMapper.readValue(jsonStr, clazz);
        } catch (Exception e) {
            log.warn("JSON字符串转换为对象失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 从数据库实体转换为响应对象
     */
    private StyleAnalysisResponse convertEntityToResponse(AiStyleAnalysis entity) {
        StyleAnalysisResponse response = new StyleAnalysisResponse();
        response.setAnalysisId(entity.getId());
        
        // 将JSON字符串转换回复杂对象
        response.setStyleDistribution(convertFromJsonString(entity.getStyleDistribution(), Map.class));
        response.setDominantColors(convertFromJsonString(entity.getDominantColors(), List.class));
        response.setRecommendedStyles(convertFromJsonString(entity.getRecommendedStyles(), List.class));
        response.setMaterialAnalysis(convertFromJsonString(entity.getMaterialAnalysis(), Map.class));
        response.setPatternAnalysis(convertFromJsonString(entity.getPatternAnalysis(), List.class));
        response.setSeasonSuitability(convertFromJsonString(entity.getSeasonSuitability(), Map.class));
        response.setOccasionSuitability(convertFromJsonString(entity.getOccasionSuitability(), Map.class));
        
        return response;
    }

    @Override
    public StyleAnalysisResponse getAnalysisById(Long analysisId) {
        try {
            AiStyleAnalysis entity = styleAnalysisMapper.selectById(analysisId);
            if (entity == null) {
                throw new RuntimeException("风格分析记录不存在: " + analysisId);
            }
            return convertEntityToResponse(entity);
        } catch (Exception e) {
            log.error("获取风格分析结果失败", e);
            throw new RuntimeException("获取风格分析结果失败: " + e.getMessage());
        }
    }

    @Override
    public List<StyleAnalysisResponse> getAnalysisByUserId(String userId) {
        try {
            List<AiStyleAnalysis> entities = styleAnalysisMapper.selectByUserId(userId);
            return entities.stream()
                    .map(this::convertEntityToResponse)
                    .collect(java.util.stream.Collectors.toList());
        } catch (Exception e) {
            log.error("获取用户风格分析历史失败", e);
            throw new RuntimeException("获取用户风格分析历史失败: " + e.getMessage());
        }
    }
}