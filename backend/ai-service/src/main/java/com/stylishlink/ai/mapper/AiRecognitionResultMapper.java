package com.stylishlink.ai.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.stylishlink.ai.entity.AiRecognitionResult;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 图像识别结果Mapper接口
 */
@Mapper
public interface AiRecognitionResultMapper extends BaseMapper<AiRecognitionResult> {

    /**
     * 根据用户ID查询识别结果
     * @param userId 用户ID
     * @return 识别结果列表
     */
    List<AiRecognitionResult> selectByUserId(@Param("userId") String userId);

    /**
     * 根据识别类型查询结果
     * @param recognitionType 识别类型
     * @return 识别结果列表
     */
    List<AiRecognitionResult> selectByRecognitionType(@Param("recognitionType") String recognitionType);

    /**
     * 根据用户ID和识别类型查询结果
     * @param userId 用户ID
     * @param recognitionType 识别类型
     * @return 识别结果列表
     */
    List<AiRecognitionResult> selectByUserIdAndType(@Param("userId") String userId, 
                                                   @Param("recognitionType") String recognitionType);
} 