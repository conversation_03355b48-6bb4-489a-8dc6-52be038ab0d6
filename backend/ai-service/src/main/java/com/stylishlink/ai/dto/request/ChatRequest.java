package com.stylishlink.ai.dto.request;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * AI对话请求DTO
 */
@Data
public class ChatRequest {

    /**
     * 用户输入消息
     */
    @NotBlank(message = "消息内容不能为空")
    private String message;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 上下文信息
     */
    private String context;
} 