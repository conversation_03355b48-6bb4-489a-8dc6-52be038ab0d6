package com.stylishlink.ai.service.impl;

import com.stylishlink.ai.dto.request.WeatherRequest;
import com.stylishlink.ai.dto.response.WeatherResponse;
import com.stylishlink.ai.service.WeatherProvider;
import com.stylishlink.ai.service.WeatherService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Comparator;
import java.util.List;

/**
 * 天气服务实现类
 */
@Slf4j
@Service
public class WeatherServiceImpl implements WeatherService {

    private final List<WeatherProvider> weatherProviders;

    public WeatherServiceImpl(List<WeatherProvider> weatherProviders) {
        this.weatherProviders = weatherProviders;
        // 按优先级排序
        this.weatherProviders.sort(Comparator.comparingInt(WeatherProvider::getPriority));
    }

    @Override
    public WeatherResponse getWeather(WeatherRequest request) {
        if (request == null) {
            return buildErrorResponse("请求参数不能为空");
        }

        // 验证查询参数
        if (!StringUtils.hasText(request.getCityName()) && 
            (request.getLatitude() == null || request.getLongitude() == null)) {
            return buildErrorResponse("请提供城市名称或经纬度信息");
        }

        // 从认证上下文中获取用户ID
        try {
            String userId = SecurityContextHolder.getContext()
                .getAuthentication().getName();
            request.setUserId(userId);
            log.debug("设置查询用户ID: {}", userId);
        } catch (Exception e) {
            log.warn("无法获取当前用户ID: {}", e.getMessage());
        }

        // 按优先级尝试各个数据源（心知天气优先级最高）
        for (WeatherProvider provider : weatherProviders) {
            if (!provider.isAvailable()) {
                log.debug("天气数据源不可用: {}", provider.getProviderName());
                continue;
            }

            try {
                log.info("使用天气数据源: {}", provider.getProviderName());
                WeatherResponse response = provider.getWeather(request);
                
                if (response != null && Boolean.TRUE.equals(response.getSuccess())) {
                    log.info("天气数据获取成功，数据源: {}", provider.getProviderName());
                    return response;
                } else {
                    log.warn("天气数据源返回失败: {}, 错误: {}", 
                            provider.getProviderName(), 
                            response != null ? response.getErrorMessage() : "未知错误");
                }
            } catch (Exception e) {
                log.error("天气数据源调用异常: {}", provider.getProviderName(), e);
            }
        }

        return buildErrorResponse("天气查询失败，请稍后重试");
    }



    /**
     * 根据名称查找天气数据提供商
     */
    private WeatherProvider findProviderByName(String providerName) {
        return weatherProviders.stream()
                .filter(provider -> provider.getProviderName().equals(providerName))
                .findFirst()
                .orElse(null);
    }

    /**
     * 构建错误响应
     */
    private WeatherResponse buildErrorResponse(String errorMessage) {
        WeatherResponse response = new WeatherResponse();
        response.setSuccess(false);
        response.setErrorMessage(errorMessage);
        response.setTimestamp(System.currentTimeMillis());
        return response;
    }
} 