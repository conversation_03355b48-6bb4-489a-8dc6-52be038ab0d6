package com.stylishlink.ai.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.stylishlink.ai.client.AiClient;
import com.stylishlink.ai.client.AiClientFactory;
import com.stylishlink.ai.dto.request.ClothingRecognitionByFileIdRequest;
import com.stylishlink.ai.dto.request.RecognizeRequest;
import com.stylishlink.ai.dto.response.ClothingItem;
import com.stylishlink.ai.dto.response.ClothingRecognitionResponse;
import com.stylishlink.ai.dto.response.RecognitionResponse;
import com.stylishlink.ai.entity.AiRecognitionResult;
import com.stylishlink.ai.entity.ClothingRecognitionResult;
import com.stylishlink.ai.mapper.AiRecognitionResultMapper;
import com.stylishlink.ai.mapper.ClothingRecognitionResultMapper;
import com.stylishlink.ai.service.AiRecognitionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 图像识别服务实现
 */
@Slf4j
@Service
public class AiRecognitionServiceImpl implements AiRecognitionService {

    @Autowired
    private AiClientFactory aiClientFactory;

    @Autowired
    private AiRecognitionResultMapper recognitionResultMapper;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private ClothingRecognitionResultMapper clothingRecognitionResultMapper;

    @Autowired
    private com.stylishlink.ai.service.FileUploadService fileUploadService;

    @Override
    public RecognitionResponse recognizeClothing(RecognizeRequest request) {
        request.setType("clothing");
        return recognize(request);
    }

    @Override
    public RecognitionResponse recognizeAccessory(RecognizeRequest request) {
        request.setType("accessory");
        return recognize(request);
    }

    @Override
    public RecognitionResponse recognize(RecognizeRequest request) {
        try {
            // 获取视觉专用AI客户端（豆包）进行图像识别
            AiClient visionClient = aiClientFactory.getVisionClient();
            log.info("使用视觉AI客户端进行图像识别，用户: {}", request.getUserId());

            // 调用AI服务进行识别
            String recognitionType = StringUtils.hasText(request.getType()) ? request.getType() : "clothing";
            Map<String, Object> aiResult = visionClient.recognizeImage(request.getFile(), recognitionType);
            
            log.info("AI服务返回结果: {}", aiResult);

            // 先解析aiResponse字符串
            Map<String, Object> parsedAiResponse = parseAiResponse(aiResult);
            log.debug("解析后的AI响应数据: {}", parsedAiResponse);

            // 构建响应对象
            RecognitionResponse response = new RecognitionResponse();
            
            // 尝试解析新格式的多物件识别结果
            if (parseMultiItemResponse(parsedAiResponse, response)) {
                log.info("成功解析新格式的多物件识别结果");
            } else {
                // 回退到兼容性模式，解析旧格式
                log.info("回退到兼容性模式，解析旧格式结果");
                parseLegacyResponse(parsedAiResponse, response);
            }

            // 保存识别结果到数据库
            try {
                log.debug("=== 开始保存识别结果到数据库 ===");
                
                AiRecognitionResult entity = new AiRecognitionResult();
                entity.setUserId(request.getUserId());
                // 直接使用豆包上传后返回的真实图片URL
                String imageUrl = (String) aiResult.get("imageUrl");
                entity.setImageUrl(imageUrl);
                entity.setRecognitionType(recognitionType);
                
                // 保存新格式的多物件识别结果，使用第一个物件的信息作为主记录
                if (response.isHasClothingOrAccessory() && response.getItems() != null && !response.getItems().isEmpty()) {
                    ClothingItem firstItem = response.getItems().get(0);
                    entity.setCategory(firstItem.getCategory());
                    entity.setColors(firstItem.getColors());
                    entity.setStyle(firstItem.getStyle());
                    entity.setMaterials(firstItem.getMaterials());
                    entity.setPatterns(firstItem.getPatterns());
                    entity.setConfidence(firstItem.getConfidence());
                }
                
                entity.setRawResult(objectMapper.writeValueAsString(aiResult));
                entity.setCreatedAt(LocalDateTime.now());
                entity.setUpdatedAt(LocalDateTime.now());

                log.debug("准备保存的实体数据: {}", entity);
                
                log.debug("调用 recognitionResultMapper.insert() 方法");
                int insertResult = recognitionResultMapper.insert(entity);
                log.debug("数据库插入结果: {}, 生成的ID: {}", insertResult, entity.getId());
                
                log.info("图像识别完成，用户: {}, 类型: {}, 结果ID: {}, 识别到物件数: {}", 
                        request.getUserId(), recognitionType, entity.getId(), response.getItemCount());
                        
            } catch (Exception dbException) {
                log.error("=== 保存识别结果到数据库失败 ===");
                log.error("异常类型: {}", dbException.getClass().getSimpleName());
                log.error("异常信息: {}", dbException.getMessage());
                log.error("详细堆栈:", dbException);
                
                log.warn("保存识别结果到数据库失败，但识别成功: {}", dbException.getMessage());
                // 数据库失败不影响识别结果返回
            }

            return response;

        } catch (Exception e) {
            log.error("图像识别失败，用户: {}, 类型: {}", request.getUserId(), request.getType(), e);
            throw new RuntimeException("图像识别失败: " + e.getMessage());
        }
    }

    /**
     * 解析新格式的多物件识别响应
     * @param parsedAiResponse 解析后的AI响应
     * @param response 要填充的响应对象
     * @return 是否成功解析新格式
     */
    private boolean parseMultiItemResponse(Map<String, Object> parsedAiResponse, RecognitionResponse response) {
        if (parsedAiResponse == null || parsedAiResponse.isEmpty()) {
            return false;
        }
        
        try {
            // 检查是否包含新格式的必需字段
            if (!parsedAiResponse.containsKey("hasClothingOrAccessory")) {
                return false;
            }
            
            // 解析是否识别到衣物配饰
            Object hasClothingObj = parsedAiResponse.get("hasClothingOrAccessory");
            boolean hasClothing = false;
            if (hasClothingObj instanceof Boolean) {
                hasClothing = (Boolean) hasClothingObj;
            } else if (hasClothingObj instanceof String) {
                hasClothing = "true".equalsIgnoreCase((String) hasClothingObj);
            }
            
            response.setHasClothingOrAccessory(hasClothing);
            
            if (!hasClothing) {
                // 没有识别到衣物配饰
                response.setItemCount(0);
                response.setItems(new ArrayList<>());
                log.info("AI响应：未识别到衣物或配饰");
                return true;
            }
            
            // 解析物件数量
            Object itemCountObj = parsedAiResponse.get("itemCount");
            int itemCount = 0;
            if (itemCountObj instanceof Number) {
                itemCount = ((Number) itemCountObj).intValue();
            } else if (itemCountObj instanceof String) {
                try {
                    itemCount = Integer.parseInt((String) itemCountObj);
                } catch (NumberFormatException e) {
                    log.warn("无法解析itemCount: {}", itemCountObj);
                }
            }
            response.setItemCount(itemCount);
            
            // 解析物件列表
            List<ClothingItem> items = parseClothingItems(parsedAiResponse);
            response.setItems(items);
            
            log.debug("解析物件列表完成，共{}件物品", items.size());
            for (int i = 0; i < items.size(); i++) {
                ClothingItem item = items.get(i);
                log.debug("物件[{}]: 类型={}, 类别={}, 颜色={}, 材质={}, 图案={}", 
                        i, item.getType(), item.getCategory(), 
                        item.getColors(), item.getMaterials(), item.getPatterns());
            }
            
            log.info("成功解析新格式响应，识别到{}件物品", items.size());
            return true;
            
        } catch (Exception e) {
            log.error("解析新格式响应失败: {}", e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 解析衣物物件列表
     */
    private List<ClothingItem> parseClothingItems(Map<String, Object> parsedAiResponse) {
        List<ClothingItem> items = new ArrayList<>();
        
        Object itemsObj = parsedAiResponse.get("items");
        if (!(itemsObj instanceof List)) {
            log.warn("items字段不是列表类型: {}", itemsObj);
            return items;
        }
        
        List<?> itemsList = (List<?>) itemsObj;
        for (Object itemObj : itemsList) {
            if (!(itemObj instanceof Map)) {
                log.warn("物件项不是Map类型: {}", itemObj);
                continue;
            }
            
            Map<String, Object> itemMap = (Map<String, Object>) itemObj;
            ClothingItem item = new ClothingItem();
            
            item.setType(extractStringValue(itemMap, "type", "未知类型"));
            item.setCategory(extractStringValue(itemMap, "category", "未知类别"));
            item.setStyle(extractStringValue(itemMap, "style", "通用风格"));
            item.setColors(extractStringList(itemMap, "colors", Arrays.asList("未知颜色")));
            item.setMaterials(extractStringList(itemMap, "materials", Arrays.asList("未知材质")));
            item.setPatterns(extractStringList(itemMap, "patterns", Arrays.asList("无图案")));
            item.setConfidence(extractConfidenceValue(itemMap));
            
            items.add(item);
            log.debug("解析到物件: 类型={}, 类别={}, 置信度={}, 材质数量={}, 图案数量={}", 
                    item.getType(), item.getCategory(), item.getConfidence(), 
                    item.getMaterials().size(), item.getPatterns().size());
        }
        
        return items;
    }
    
    /**
     * 解析旧格式响应（向后兼容）
     */
    private void parseLegacyResponse(Map<String, Object> parsedAiResponse, RecognitionResponse response) {
        // 设置新格式字段的默认值
        response.setHasClothingOrAccessory(true); // 假设旧格式都是识别到了物件
        response.setItemCount(1); // 旧格式默认为1个物件
        
        // 构建兼容性物件
        ClothingItem legacyItem = new ClothingItem();
        legacyItem.setType("衣物"); // 默认类型
        legacyItem.setCategory(extractStringValue(parsedAiResponse, "category", "未知类别"));
        legacyItem.setStyle(extractStringValue(parsedAiResponse, "style", "通用风格"));
        legacyItem.setColors(extractStringList(parsedAiResponse, "colors", Arrays.asList("未知颜色")));
        legacyItem.setMaterials(extractStringList(parsedAiResponse, "materials", Arrays.asList("未知材质")));
        legacyItem.setPatterns(extractStringList(parsedAiResponse, "patterns", Arrays.asList("无图案")));
        legacyItem.setConfidence(extractConfidenceValue(parsedAiResponse));
        
        response.setItems(Arrays.asList(legacyItem));
        
        log.info("使用兼容模式解析旧格式响应");
    }

    /**
     * 解析AI响应字符串
     */
    private Map<String, Object> parseAiResponse(Map<String, Object> aiResult) {
        if (aiResult == null || !aiResult.containsKey("aiResponse")) {
            log.warn("AI结果中不包含aiResponse字段");
            return Collections.emptyMap();
        }
        
        String aiResponseStr = (String) aiResult.get("aiResponse");
        if (aiResponseStr == null || aiResponseStr.trim().isEmpty()) {
            log.warn("AI响应内容为空");
            return Collections.emptyMap();
        }
        
        try {
            log.debug("正在解析AI响应字符串: {}", aiResponseStr);
            return objectMapper.readValue(aiResponseStr, Map.class);
        } catch (JsonProcessingException e) {
            log.error("解析AI响应字符串失败: {}", e.getMessage());
            log.debug("原始AI响应字符串: {}", aiResponseStr);
            return Collections.emptyMap();
        }
    }

    /**
     * 安全提取字符串值
     */
    private String extractStringValue(Map<String, Object> map, String key, String defaultValue) {
        if (map == null || !map.containsKey(key)) {
            return defaultValue;
        }
        Object value = map.get(key);
        return value != null ? value.toString() : defaultValue;
    }

    /**
     * 安全提取置信度值
     */
    private BigDecimal extractConfidenceValue(Map<String, Object> map) {
        if (map == null || !map.containsKey("confidence")) {
            return new BigDecimal("0.5"); // 默认置信度50%
        }
        Object value = map.get("confidence");
        if (value == null) {
            return new BigDecimal("0.5");
        }
        try {
            if (value instanceof Number) {
                return BigDecimal.valueOf(((Number) value).doubleValue());
            }
            return new BigDecimal(value.toString());
        } catch (NumberFormatException e) {
            log.warn("无法解析置信度值: {}, 使用默认值", value);
            return new BigDecimal("0.5");
        }
    }

    /**
     * 安全提取字符串列表
     * 区分空数组和缺失字段：
     * - 如果字段不存在或为null，使用默认值
     * - 如果字段存在但为空数组，保持空数组
     */
    private List<String> extractStringList(Map<String, Object> map, String key, List<String> defaultValue) {
        if (map == null || !map.containsKey(key)) {
            // 字段不存在，使用默认值
            return defaultValue;
        }
        Object value = map.get(key);
        if (value == null) {
            // 字段存在但为null，使用默认值
            return defaultValue;
        }
        
        try {
            if (value instanceof String[]) {
                return Arrays.asList((String[]) value);
            } else if (value instanceof List) {
                List<?> list = (List<?>) value;
                // 如果是空List，保持空List（不使用默认值）
                if (list.isEmpty()) {
                    return new ArrayList<>();
                }
                List<String> stringList = new ArrayList<>();
                for (Object item : list) {
                    if (item != null) {
                        stringList.add(item.toString());
                    }
                }
                return stringList;
            } else if (value instanceof String) {
                // 尝试解析逗号分隔的字符串
                String str = value.toString().trim();
                if (str.isEmpty()) {
                    // 空字符串保持为空数组
                    return new ArrayList<>();
                }
                return Arrays.asList(str.split(","));
            }
        } catch (Exception e) {
            log.warn("无法解析{}字段: {}, 使用默认值", key, value);
        }
        
        // 无法解析的情况，使用默认值
        return defaultValue;
    }

    @Override
    public ClothingRecognitionResponse recognizeClothingByFileId(ClothingRecognitionByFileIdRequest request) {
        try {
            log.info("开始衣物识别分析（文件ID方式），文件ID: {}, 用户: {}", request.getFileId(), request.getUserId());

            // 1. 根据文件ID获取图片URL
            String imageUrl = fileUploadService.getImageUrlById(request.getFileId());
            log.info("从文件ID {} 获取到图片URL: {}", request.getFileId(), imageUrl);

            // 2. 调用AI客户端进行衣物识别
            AiClient visionClient = aiClientFactory.getVisionClient();
            Map<String, Object> aiResult = visionClient.recognizeImageByUrl(imageUrl, "clothing");
            
            log.info("AI衣物识别完成（文件ID方式）: {}", aiResult);

            // 3. 解析AI响应为符合API文档的格式
            ClothingRecognitionResponse response = parseClothingRecognitionResponse(aiResult, request.getFileId());
            
            // 4. 保存识别结果到数据库
            saveClothingRecognitionResult(request, response, aiResult, imageUrl);

            log.info("衣物识别完成（文件ID方式），用户: {}, 识别ID: {}", 
                    request.getUserId(), response.getRecognitionId());
            
            return response;
            
        } catch (Exception e) {
            log.error("衣物识别失败（文件ID方式）", e);
            throw new RuntimeException("衣物识别失败: " + e.getMessage());
        }
    }

    /**
     * 解析AI响应为衣物识别响应格式
     */
    private ClothingRecognitionResponse parseClothingRecognitionResponse(Map<String, Object> aiResult, String fileId) {
        ClothingRecognitionResponse response = new ClothingRecognitionResponse();
        
        // 生成识别结果ID
        response.setRecognitionId("rec_" + System.currentTimeMillis());
        response.setFileId(fileId);
        
        // 解析AI响应字符串
        Map<String, Object> parsedAiResponse = parseAiResponse(aiResult);
        if (parsedAiResponse.isEmpty()) {
            throw new RuntimeException("AI响应解析失败，无法获取有效数据");
        }
        
        // 检查是否识别到衣物
        Boolean hasClothingOrAccessory = (Boolean) parsedAiResponse.get("hasClothingOrAccessory");
        if (hasClothingOrAccessory == null || !hasClothingOrAccessory) {
            String errorMessage = (String) parsedAiResponse.get("errorMessage");
            throw new RuntimeException("AI未识别到衣物或配饰: " + (errorMessage != null ? errorMessage : "未知原因"));
        }
        
        // 获取items数组
        List<?> itemsList = (List<?>) parsedAiResponse.get("items");
        if (itemsList == null || itemsList.isEmpty()) {
            throw new RuntimeException("AI响应中未包含具体的衣物信息");
        }
        
        // 取第一个识别结果（目前只处理单个衣物）
        Map<String, Object> firstItem = (Map<String, Object>) itemsList.get(0);
        if (firstItem == null) {
            throw new RuntimeException("AI响应中的衣物信息为空");
        }
        
        // 从AI返回的数据中提取信息，不使用默认值
        String aiType = (String) firstItem.get("type");
        String aiCategory = (String) firstItem.get("category");
        List<String> aiColors = (List<String>) firstItem.get("colors");
        String aiStyle = (String) firstItem.get("style");
        List<String> aiMaterials = (List<String>) firstItem.get("materials");
        List<String> aiPatterns = (List<String>) firstItem.get("patterns");
        Double aiConfidence = extractDoubleFromObject(firstItem.get("confidence"));
        
        // 验证必需字段
        if (aiType == null || aiType.trim().isEmpty()) {
            throw new RuntimeException("AI未返回衣物类型信息");
        }
        if (aiCategory == null || aiCategory.trim().isEmpty()) {
            throw new RuntimeException("AI未返回衣物类别信息");
        }
        if (aiColors == null || aiColors.isEmpty()) {
            throw new RuntimeException("AI未返回颜色信息");
        }
        if (aiConfidence == null || aiConfidence <= 0) {
            throw new RuntimeException("AI未返回有效的置信度信息");
        }
        
        // 设置基本信息（使用AI返回的真实数据）
        response.setName(generateClothingName(aiType, aiCategory, aiColors, aiStyle));
        response.setCategory(aiType);  // 使用AI返回的type作为category
        response.setSubCategory(aiCategory);  // 使用AI返回的category作为subCategory
        
        // 设置颜色（使用AI返回的真实数据）
        response.setColors(aiColors);
        
        // 设置材质（使用AI返回的数据，空数组也保持空数组）
        response.setMaterials(aiMaterials != null ? aiMaterials : new ArrayList<>());
        
        // 根据AI返回的信息推断适合季节和场合
        response.setSeasons(inferSeasonsFromClothing(aiType, aiCategory, aiStyle));
        response.setOccasions(inferOccasionsFromClothing(aiType, aiCategory, aiStyle));
        
        // 根据颜色和材质推断五行属性
        ClothingRecognitionResponse.WuxingAttributes wuxing = inferWuxingFromClothing(aiColors, aiMaterials, aiType);
        response.setWuxing(wuxing);
        
        // 设置置信度（使用AI返回的真实数据）
        response.setConfidence(aiConfidence);
        
        // 生成建议
        response.setSuggestions(generateSuggestions(aiType, aiCategory, aiColors, aiStyle));
        
        // 设置处理时间
        response.setProcessedAt(java.time.LocalDateTime.now().toString());
        
        return response;
    }

    /**
     * 保存衣物识别结果到数据库
     */
    private void saveClothingRecognitionResult(ClothingRecognitionByFileIdRequest request, 
                                             ClothingRecognitionResponse response, 
                                             Map<String, Object> aiResult,
                                             String imageUrl) {
        try {
            ClothingRecognitionResult entity = new ClothingRecognitionResult();
            entity.setRecognitionId(response.getRecognitionId());
            entity.setUserId(request.getUserId());
            entity.setFileId(request.getFileId());
            entity.setImageUrl(imageUrl);
            entity.setName(response.getName());
            entity.setCategory(response.getCategory());
            entity.setSubCategory(response.getSubCategory());
            entity.setColors(response.getColors());
            entity.setMaterials(response.getMaterials());
            entity.setSeasons(response.getSeasons());
            entity.setOccasions(response.getOccasions());
            
            // 五行属性
            ClothingRecognitionResponse.WuxingAttributes wuxing = response.getWuxing();
            if (wuxing != null) {
                entity.setWuxingMetal(wuxing.getMetal());
                entity.setWuxingWood(wuxing.getWood());
                entity.setWuxingWater(wuxing.getWater());
                entity.setWuxingFire(wuxing.getFire());
                entity.setWuxingEarth(wuxing.getEarth());
                entity.setWuxingPrimary(wuxing.getPrimary());
            }
            
            entity.setConfidence(java.math.BigDecimal.valueOf(response.getConfidence()));
            entity.setSuggestions(response.getSuggestions());
            
            // 保存原始AI响应
            try {
                entity.setRawResult(objectMapper.writeValueAsString(aiResult));
            } catch (Exception e) {
                log.warn("序列化AI响应结果失败", e);
                entity.setRawResult(aiResult.toString());
            }
            
            entity.setProcessedAt(java.time.LocalDateTime.now());
            entity.setCreatedAt(java.time.LocalDateTime.now());
            entity.setUpdatedAt(java.time.LocalDateTime.now());
            
            clothingRecognitionResultMapper.insert(entity);
            log.info("衣物识别结果已保存到数据库，ID: {}", entity.getId());
            
        } catch (Exception e) {
            log.error("保存衣物识别结果到数据库失败", e);
            // 数据库失败不影响识别结果返回
        }
    }
    
    /**
     * 安全提取整数值
     */
    private Integer extractIntValue(Map<String, Object> map, String key, Integer defaultValue) {
        if (map == null || !map.containsKey(key)) {
            return defaultValue;
        }
        Object value = map.get(key);
        if (value == null) {
            return defaultValue;
        }
        try {
            if (value instanceof Number) {
                return ((Number) value).intValue();
            }
            return Integer.parseInt(value.toString());
        } catch (NumberFormatException e) {
            log.warn("无法解析整数值: {}, 使用默认值", value);
            return defaultValue;
        }
    }
    
    /**
     * 安全提取双精度值
     */
    private Double extractDoubleValue(Map<String, Object> map, String key, Double defaultValue) {
        if (map == null || !map.containsKey(key)) {
            return defaultValue;
        }
        Object value = map.get(key);
        if (value == null) {
            return defaultValue;
        }
        try {
            if (value instanceof Number) {
                return ((Number) value).doubleValue();
            }
            return Double.parseDouble(value.toString());
        } catch (NumberFormatException e) {
            log.warn("无法解析双精度值: {}, 使用默认值", value);
            return defaultValue;
        }
    }

    /**
     * 从Object中安全提取Double值，不使用默认值
     */
    private Double extractDoubleFromObject(Object value) {
        if (value == null) {
            return null;
        }
        try {
            if (value instanceof Number) {
                return ((Number) value).doubleValue();
            }
            return Double.parseDouble(value.toString());
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * 生成衣物名称
     */
    private String generateClothingName(String type, String category, List<String> colors, String style) {
        StringBuilder name = new StringBuilder();
        
        // 添加颜色
        if (colors != null && !colors.isEmpty()) {
            name.append(colors.get(0));
        }
        
        // 添加风格（如果有）
        if (style != null && !style.trim().isEmpty()) {
            if (name.length() > 0) name.append("");
            name.append(style);
        }
        
        // 添加类别
        if (category != null && !category.trim().isEmpty()) {
            if (name.length() > 0) name.append("");
            name.append(category);
        } else if (type != null && !type.trim().isEmpty()) {
            if (name.length() > 0) name.append("");
            name.append(type);
        }
        
        return name.length() > 0 ? name.toString() : "识别衣物";
    }

    /**
     * 根据衣物信息推断适合季节
     */
    private List<String> inferSeasonsFromClothing(String type, String category, String style) {
        List<String> seasons = new ArrayList<>();
        
        // 根据类别推断季节
        if (category != null) {
            String cat = category.toLowerCase();
            if (cat.contains("t恤") || cat.contains("短袖") || cat.contains("背心") || cat.contains("短裤")) {
                seasons.addAll(List.of("春", "夏"));
            } else if (cat.contains("外套") || cat.contains("大衣") || cat.contains("毛衣") || cat.contains("长袖")) {
                seasons.addAll(List.of("秋", "冬"));
            } else if (cat.contains("裙子") || cat.contains("连衣裙")) {
                seasons.addAll(List.of("春", "夏", "秋"));
            } else {
                // 默认适合春夏
                seasons.addAll(List.of("春", "夏", "秋"));
            }
        } else {
            // 默认适合春夏秋
            seasons.addAll(List.of("春", "夏", "秋"));
        }
        
        return seasons;
    }

    /**
     * 根据衣物信息推断适合场合
     */
    private List<String> inferOccasionsFromClothing(String type, String category, String style) {
        List<String> occasions = new ArrayList<>();
        
        // 根据风格推断场合
        if (style != null) {
            String s = style.toLowerCase();
            if (s.contains("运动")) {
                occasions.add("运动健身");
            } else if (s.contains("商务") || s.contains("正式")) {
                occasions.addAll(List.of("职场商务", "正式场合"));
            } else if (s.contains("休闲")) {
                occasions.add("日常休闲");
            } else if (s.contains("甜美") || s.contains("约会")) {
                occasions.add("约会交际");
            } else if (s.contains("派对") || s.contains("聚会")) {
                occasions.add("派对活动");
            }
        }
        
        // 如果没有推断出场合，根据类别推断
        if (occasions.isEmpty() && category != null) {
            String cat = category.toLowerCase();
            if (cat.contains("t恤") || cat.contains("牛仔") || cat.contains("休闲")) {
                occasions.add("日常休闲");
            } else if (cat.contains("西装") || cat.contains("衬衫")) {
                occasions.add("职场商务");
            } else if (cat.contains("连衣裙")) {
                occasions.addAll(List.of("日常休闲", "约会交际"));
            } else {
                occasions.add("日常休闲");
            }
        }
        
        // 确保至少有一个场合
        if (occasions.isEmpty()) {
            occasions.add("日常休闲");
        }
        
        return occasions;
    }

    /**
     * 根据颜色和材质推断五行属性
     */
    private ClothingRecognitionResponse.WuxingAttributes inferWuxingFromClothing(List<String> colors, List<String> materials, String type) {
        ClothingRecognitionResponse.WuxingAttributes wuxing = new ClothingRecognitionResponse.WuxingAttributes();
        
        // 初始化分数
        int metal = 0, wood = 0, water = 0, fire = 0, earth = 0;
        
        // 根据颜色推断五行
        if (colors != null) {
            for (String color : colors) {
                String c = color.toLowerCase();
                if (c.contains("白") || c.contains("银") || c.contains("灰")) {
                    metal += 2;
                } else if (c.contains("绿") || c.contains("青")) {
                    wood += 2;
                } else if (c.contains("黑") || c.contains("蓝") || c.contains("深蓝")) {
                    water += 2;
                } else if (c.contains("红") || c.contains("橙") || c.contains("粉")) {
                    fire += 2;
                } else if (c.contains("黄") || c.contains("棕") || c.contains("褐")) {
                    earth += 2;
                }
            }
        }
        
        // 根据材质推断五行
        if (materials != null) {
            for (String material : materials) {
                String m = material.toLowerCase();
                if (m.contains("金") || m.contains("银") || m.contains("铁")) {
                    metal += 1;
                } else if (m.contains("棉") || m.contains("麻") || m.contains("竹")) {
                    wood += 1;
                } else if (m.contains("丝") || m.contains("人造")) {
                    water += 1;
                } else if (m.contains("皮") || m.contains("毛")) {
                    fire += 1;
                } else if (m.contains("土") || m.contains("陶")) {
                    earth += 1;
                }
            }
        }
        
        // 确保分数在0-4范围内
        metal = Math.min(4, Math.max(0, metal));
        wood = Math.min(4, Math.max(0, wood));
        water = Math.min(4, Math.max(0, water));
        fire = Math.min(4, Math.max(0, fire));
        earth = Math.min(4, Math.max(0, earth));
        
        // 如果所有分数都是0，给一个基础分数
        if (metal == 0 && wood == 0 && water == 0 && fire == 0 && earth == 0) {
            // 根据类型给基础分数
            if (type != null && type.contains("上衣")) {
                metal = 1; wood = 1; water = 1; fire = 1; earth = 1;
            } else {
                metal = 1; wood = 1; water = 1; fire = 1; earth = 1;
            }
        }
        
        wuxing.setMetal(metal);
        wuxing.setWood(wood);
        wuxing.setWater(water);
        wuxing.setFire(fire);
        wuxing.setEarth(earth);
        
        // 确定主要属性
        String primary = "金";
        int maxScore = metal;
        if (wood > maxScore) { maxScore = wood; primary = "木"; }
        if (water > maxScore) { maxScore = water; primary = "水"; }
        if (fire > maxScore) { maxScore = fire; primary = "火"; }
        if (earth > maxScore) { primary = "土"; }
        
        wuxing.setPrimary(primary);
        
        return wuxing;
    }

    /**
     * 生成穿搭建议
     */
    private List<String> generateSuggestions(String type, String category, List<String> colors, String style) {
        List<String> suggestions = new ArrayList<>();
        
        // 基于类别的建议
        if (category != null) {
            String cat = category.toLowerCase();
            if (cat.contains("外套")) {
                suggestions.add("外套适合叠穿搭配，建议内搭简洁款式");
            } else if (cat.contains("t恤")) {
                suggestions.add("T恤百搭实用，适合多种搭配风格");
            } else if (cat.contains("衬衫")) {
                suggestions.add("衬衫可正式可休闲，建议根据场合选择搭配");
            }
        }
        
        // 基于颜色的建议
        if (colors != null && !colors.isEmpty()) {
            String color = colors.get(0).toLowerCase();
            if (color.contains("白")) {
                suggestions.add("白色经典百搭，可与任何颜色组合");
            } else if (color.contains("黑")) {
                suggestions.add("黑色优雅百搭，适合正式和休闲场合");
            } else if (color.contains("红")) {
                suggestions.add("红色醒目活力，建议搭配中性色平衡");
            }
        }
        
        // 基于风格的建议
        if (style != null) {
            String s = style.toLowerCase();
            if (s.contains("运动")) {
                suggestions.add("运动风格建议搭配运动鞋和休闲配饰");
            } else if (s.contains("休闲")) {
                suggestions.add("休闲风格适合日常穿着，舒适自然");
            }
        }
        
        // 确保至少有一个建议
        if (suggestions.isEmpty()) {
            suggestions.add("建议根据个人喜好和场合需求进行搭配");
        }
        
        return suggestions;
    }

} 