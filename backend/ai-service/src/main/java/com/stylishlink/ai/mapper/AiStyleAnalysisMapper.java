package com.stylishlink.ai.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.stylishlink.ai.entity.AiStyleAnalysis;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 风格分析Mapper接口
 */
@Mapper
public interface AiStyleAnalysisMapper extends BaseMapper<AiStyleAnalysis> {

    /**
     * 根据用户ID查询风格分析记录
     * @param userId 用户ID
     * @return 风格分析记录列表
     */
    List<AiStyleAnalysis> selectByUserId(@Param("userId") String userId);

    /**
     * 根据来源类型查询风格分析记录
     * @param sourceType 来源类型
     * @return 风格分析记录列表
     */
    List<AiStyleAnalysis> selectBySourceType(@Param("sourceType") String sourceType);

    /**
     * 根据用户ID和来源类型查询风格分析记录
     * @param userId 用户ID
     * @param sourceType 来源类型
     * @return 风格分析记录列表
     */
    List<AiStyleAnalysis> selectByUserIdAndSourceType(@Param("userId") String userId, 
                                                     @Param("sourceType") String sourceType);

    /**
     * 根据来源ID查询风格分析记录
     * @param sourceId 来源ID
     * @return 风格分析记录
     */
    AiStyleAnalysis selectBySourceId(@Param("sourceId") String sourceId);
} 