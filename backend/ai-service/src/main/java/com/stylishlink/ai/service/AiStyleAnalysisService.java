package com.stylishlink.ai.service;

import com.stylishlink.ai.dto.request.StyleAnalysisRequest;
import com.stylishlink.ai.dto.response.StyleAnalysisResponse;

import java.util.List;

/**
 * 风格分析服务接口
 */
public interface AiStyleAnalysisService {

    /**
     * 分析图片风格
     * @param request 风格分析请求
     * @return 风格分析结果
     */
    StyleAnalysisResponse analyzeImageStyle(StyleAnalysisRequest request);

    /**
     * 分析搭配风格
     * @param request 风格分析请求
     * @return 风格分析结果
     */
    StyleAnalysisResponse analyzeOutfitStyle(StyleAnalysisRequest request);

    /**
     * 色彩分析
     * @param request 色彩分析请求
     * @return 色彩分析结果
     */
    StyleAnalysisResponse analyzeColor(StyleAnalysisRequest request);

    /**
     * 根据分析ID获取风格分析结果
     * @param analysisId 分析ID
     * @return 风格分析结果
     */
    StyleAnalysisResponse getAnalysisById(Long analysisId);

    /**
     * 根据用户ID获取风格分析历史
     * @param userId 用户ID
     * @return 风格分析结果列表
     */
    List<StyleAnalysisResponse> getAnalysisByUserId(String userId);
} 