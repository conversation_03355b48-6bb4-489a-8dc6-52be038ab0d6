package com.stylishlink.ai.controller;

import com.stylishlink.ai.dto.request.ClothingRecognitionByFileIdRequest;
import com.stylishlink.ai.dto.request.RecognizeRequest;
import com.stylishlink.ai.dto.response.ClothingRecognitionResponse;
import com.stylishlink.ai.dto.response.RecognitionResponse;
import com.stylishlink.ai.service.AiRecognitionService;
import com.stylishlink.common.dto.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.validation.Valid;

/**
 * 图像识别控制器
 */
@Slf4j
@RestController
@RequestMapping("/recognition")
public class AiRecognitionController {

    @Autowired
    private AiRecognitionService recognitionService;

    /**
     * 图像识别接口
     */
    @PostMapping
    public ApiResponse<RecognitionResponse> recognize(
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "type", required = false) String type,
            @RequestParam(value = "userId", required = false) String userId) {
        
        try {
            RecognizeRequest request = new RecognizeRequest();
            request.setFile(file);
            request.setType(type);
            request.setUserId(userId);

            RecognitionResponse response = recognitionService.recognize(request);
            
            // 检查是否识别到衣物或配饰
            if (!response.isHasClothingOrAccessory()) {
                log.warn("图像识别未找到衣物配饰，用户: {}", userId);
                return ApiResponse.error(6003, "未识别到衣服配饰信息");
            }
            
            log.info("图像识别成功，用户: {}, 识别到{}件物品", userId, response.getItemCount());
            return ApiResponse.success("图像识别成功", response);
            
        } catch (Exception e) {
            log.error("图像识别失败", e);
            return ApiResponse.error(6002, "图像识别失败: " + e.getMessage());
        }
    }

    /**
     * 衣物识别接口
     */
    @PostMapping("/clothing")
    public ApiResponse<RecognitionResponse> recognizeClothing(
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "userId", required = false) String userId) {
        
        try {
            RecognizeRequest request = new RecognizeRequest();
            request.setFile(file);
            request.setType("clothing");
            request.setUserId(userId);

            RecognitionResponse response = recognitionService.recognizeClothing(request);
            
            // 检查是否识别到衣物
            if (!response.isHasClothingOrAccessory()) {
                log.warn("衣物识别未找到衣服，用户: {}", userId);
                return ApiResponse.error(6003, "未识别到衣服信息");
            }
            
            log.info("衣物识别成功，用户: {}, 识别到{}件衣物", userId, response.getItemCount());
            return ApiResponse.success("衣物识别成功", response);
            
        } catch (Exception e) {
            log.error("衣物识别失败", e);
            return ApiResponse.error(6002, "衣物识别失败: " + e.getMessage());
        }
    }

    /**
     * 衣物识别接口（通过文件ID）- API 3.2.1
     * 接口路径优化为: /api/ai/recognition/clothing/recognize-by-file-id
     */
    @PostMapping("/clothing/recognize-by-file-id")
    public ApiResponse<ClothingRecognitionResponse> recognizeClothingByFileId(
            @Valid @RequestBody ClothingRecognitionByFileIdRequest request) {
        
        try {
            log.info("开始通过文件ID进行衣物识别，用户: {}, 文件ID: {}", request.getUserId(), request.getFileId());
            
            ClothingRecognitionResponse response = recognitionService.recognizeClothingByFileId(request);
            
            log.info("衣物识别成功，用户: {}, 文件ID: {}, 识别结果ID: {}", 
                    request.getUserId(), request.getFileId(), response.getRecognitionId());
            
            return ApiResponse.success("衣物识别成功", response);
            
        } catch (IllegalArgumentException e) {
            log.error("衣物识别参数错误", e);
            return ApiResponse.error(1001, "参数错误: " + e.getMessage());
        } catch (Exception e) {
            log.error("衣物识别失败", e);
            return ApiResponse.error(6001, "衣物识别失败: " + e.getMessage());
        }
    }

    /**
     * 配饰识别接口
     */
    @PostMapping("/accessory")
    public ApiResponse<RecognitionResponse> recognizeAccessory(
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "userId", required = false) String userId) {
        
        try {
            RecognizeRequest request = new RecognizeRequest();
            request.setFile(file);
            request.setType("accessory");
            request.setUserId(userId);

            RecognitionResponse response = recognitionService.recognizeAccessory(request);
            
            // 检查是否识别到配饰
            if (!response.isHasClothingOrAccessory()) {
                log.warn("配饰识别未找到配饰，用户: {}", userId);
                return ApiResponse.error(6003, "未识别到配饰信息");
            }
            
            log.info("配饰识别成功，用户: {}, 识别到{}件配饰", userId, response.getItemCount());
            return ApiResponse.success("配饰识别成功", response);
            
        } catch (Exception e) {
            log.error("配饰识别失败", e);
            return ApiResponse.error(6002, "配饰识别失败: " + e.getMessage());
        }
    }
}