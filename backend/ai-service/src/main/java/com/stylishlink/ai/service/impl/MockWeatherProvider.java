package com.stylishlink.ai.service.impl;

import com.stylishlink.ai.dto.request.WeatherRequest;
import com.stylishlink.ai.dto.response.WeatherData;
import com.stylishlink.ai.dto.response.WeatherLocation;
import com.stylishlink.ai.dto.response.WeatherResponse;
import com.stylishlink.ai.service.WeatherProvider;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 模拟天气数据提供商
 * 用于测试和开发环境，无需API密钥
 */
@Slf4j
@Service
public class MockWeatherProvider implements WeatherProvider {

    // 城市数据映射
    private static final Map<String, CityInfo> CITY_MAP = new HashMap<>();
    
    static {
        CITY_MAP.put("北京", new CityInfo("北京", "北京市", "110000", "中国"));
        CITY_MAP.put("上海", new CityInfo("上海", "上海市", "310000", "中国"));
        CITY_MAP.put("深圳", new CityInfo("深圳", "广东省", "440300", "中国"));
        CITY_MAP.put("广州", new CityInfo("广州", "广东省", "440100", "中国"));
        CITY_MAP.put("杭州", new CityInfo("杭州", "浙江省", "330100", "中国"));
        CITY_MAP.put("成都", new CityInfo("成都", "四川省", "510100", "中国"));
        CITY_MAP.put("西安", new CityInfo("西安", "陕西省", "610100", "中国"));
    }

    @Override
    public String getProviderName() {
        return "mock";
    }

    @Override
    public WeatherResponse getWeather(WeatherRequest request) {
        try {
            log.info("使用模拟天气数据提供商: city={}, longitude={}, latitude={}", 
                    request.getCityName(), request.getLongitude(), request.getLatitude());

            String cityName = extractCityName(request);
            if (!StringUtils.hasText(cityName)) {
                return buildErrorResponse("请提供城市名称或有效的经纬度");
            }

            return buildMockWeatherResponse(cityName);

        } catch (Exception e) {
            log.error("模拟天气数据生成失败", e);
            return buildErrorResponse("天气数据生成失败: " + e.getMessage());
        }
    }

    @Override
    public boolean isAvailable() {
        return true; // 模拟数据总是可用
    }

    @Override
    public int getPriority() {
        return 99; // 最低优先级，仅当其他数据源不可用时使用
    }

    /**
     * 提取城市名称
     */
    private String extractCityName(WeatherRequest request) {
        if (StringUtils.hasText(request.getCityName())) {
            return request.getCityName();
        }
        
        // 基于经纬度模拟城市（简单实现）
        if (request.getLatitude() != null && request.getLongitude() != null) {
            // 这里可以根据经纬度范围返回对应城市，简化处理返回北京
            return "北京";
        }
        
        return null;
    }

    /**
     * 构建模拟天气响应
     */
    private WeatherResponse buildMockWeatherResponse(String cityName) {
        WeatherResponse response = new WeatherResponse();
        response.setSuccess(true);
        response.setDataSource("模拟天气数据（仅供测试）");
        response.setTimestamp(System.currentTimeMillis());
        response.setLastUpdate(java.time.LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

        // 设置位置信息
        CityInfo cityInfo = CITY_MAP.getOrDefault(cityName, new CityInfo(cityName, "未知省份", "000000", "中国"));
        WeatherLocation location = new WeatherLocation();
        location.setName(cityInfo.name);
        location.setProvince(cityInfo.province);
        location.setAdcode(cityInfo.adcode);
        location.setCountry(cityInfo.country);
        location.setPath(String.format("%s,%s,%s", cityInfo.name, cityInfo.province, cityInfo.country));
        response.setLocation(location);

        // 设置当前天气（模拟数据）
        WeatherData current = new WeatherData();
        current.setText("晴");
        current.setCode("0");
        current.setTemperature("22");
        current.setHumidity("45");
        current.setWindDirection("东南风");
        current.setWindPower("3级");
        current.setWindSpeed("15");
        current.setPressure("1013");
        current.setVisibility("10");
        current.setUvIndex("5");
        response.setCurrent(current);

        // 设置未来几天预报（模拟数据）
        List<WeatherData> forecast = new ArrayList<>();
        for (int i = 1; i <= 3; i++) {
            WeatherData forecastDay = new WeatherData();
            LocalDate futureDate = LocalDate.now().plusDays(i);
            forecastDay.setDate(futureDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            forecastDay.setWeek(getWeekday(futureDate.getDayOfWeek().getValue()));
            
            // 模拟不同的天气
            switch (i) {
                case 1:
                    forecastDay.setDayWeather("多云");
                    forecastDay.setNightWeather("阴");
                    forecastDay.setDayTemperature("25");
                    forecastDay.setNightTemperature("18");
                    break;
                case 2:
                    forecastDay.setDayWeather("小雨");
                    forecastDay.setNightWeather("小雨");
                    forecastDay.setDayTemperature("20");
                    forecastDay.setNightTemperature("15");
                    break;
                case 3:
                    forecastDay.setDayWeather("晴");
                    forecastDay.setNightWeather("晴");
                    forecastDay.setDayTemperature("28");
                    forecastDay.setNightTemperature("20");
                    break;
            }
            
            forecastDay.setWindDirection("南风");
            forecastDay.setWindPower("2级");
            forecast.add(forecastDay);
        }
        response.setForecast(forecast);

        return response;
    }

    /**
     * 获取星期几的中文表示
     */
    private String getWeekday(int dayOfWeek) {
        String[] weekdays = {"", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六", "星期日"};
        return weekdays[dayOfWeek];
    }

    /**
     * 构建错误响应
     */
    private WeatherResponse buildErrorResponse(String errorMessage) {
        WeatherResponse response = new WeatherResponse();
        response.setSuccess(false);
        response.setErrorMessage(errorMessage);
        response.setTimestamp(System.currentTimeMillis());
        response.setDataSource("模拟天气数据");
        return response;
    }

    /**
     * 城市信息内部类
     */
    private static class CityInfo {
        String name;
        String province;
        String adcode;
        String country;

        public CityInfo(String name, String province, String adcode, String country) {
            this.name = name;
            this.province = province;
            this.adcode = adcode;
            this.country = country;
        }
    }
} 