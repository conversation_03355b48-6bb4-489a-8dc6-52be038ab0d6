package com.stylishlink.ai.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;

/**
 * 运势分析请求DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "运势分析请求")
public class FortuneAnalysisRequest {
    
    @NotNull(message = "用户ID不能为空")
    @Schema(description = "用户ID", example = "u123456")
    private String userId;
    
    @Schema(description = "查询日期", example = "2024-01-15")
    private LocalDate date;
    
    @NotNull(message = "八字详情不能为空")
    @Schema(description = "八字详情")
    private BaziDetail baziDetail;
    
    @NotNull(message = "用户信息不能为空")
    @Schema(description = "用户信息")
    private UserInfo userInfo;
    
    /**
     * 八字详情
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "八字详情")
    public static class BaziDetail {
        
        @Schema(description = "年柱", example = "甲子")
        private String yearPillar;
        
        @Schema(description = "月柱", example = "乙丑")
        private String monthPillar;
        
        @Schema(description = "日柱", example = "丙寅")
        private String dayPillar;
        
        @Schema(description = "时柱", example = "丁卯")
        private String hourPillar;
        
        @Schema(description = "五行元素列表")
        private List<String> elements;
        
        @Schema(description = "五行分布统计")
        private WuxingDistribution wuxingDistribution;
        
        @Data
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        @Schema(description = "五行分布")
        public static class WuxingDistribution {
            
            @Schema(description = "金的数量", example = "2")
            private Integer metal;
            
            @Schema(description = "木的数量", example = "3")
            private Integer wood;
            
            @Schema(description = "水的数量", example = "1")
            private Integer water;
            
            @Schema(description = "火的数量", example = "2")
            private Integer fire;
            
            @Schema(description = "土的数量", example = "0")
            private Integer earth;
        }
    }
    
    /**
     * 用户信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "用户信息")
    public static class UserInfo {
        
        @Schema(description = "姓名", example = "张三")
        private String fullName;
        
        @Schema(description = "性别", example = "1", allowableValues = {"1", "2"})
        private Integer gender;
        
        @Schema(description = "出生日期", example = "1995-05-01")
        private LocalDate birthDate;
        
        @Schema(description = "出生时间", example = "08:30")
        private String birthTime;
        
        @Schema(description = "出生地点", example = "上海")
        private String birthPlace;
    }
} 