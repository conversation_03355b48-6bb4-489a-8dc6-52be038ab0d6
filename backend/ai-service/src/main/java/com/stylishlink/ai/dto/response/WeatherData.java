package com.stylishlink.ai.dto.response;

import lombok.Data;
import java.util.List;

/**
 * 天气数据DTO
 */
@Data
public class WeatherData {

    /**
     * 天气现象描述（如：晴、多云、小雨）
     */
    private String text;

    /**
     * 天气现象代码
     */
    private String code;

    /**
     * 温度（摄氏度）
     */
    private String temperature;

    /**
     * 最高温度
     */
    private String highTemperature;

    /**
     * 最低温度
     */
    private String lowTemperature;

    /**
     * 风向
     */
    private String windDirection;

    /**
     * 风力等级
     */
    private String windPower;

    /**
     * 风速
     */
    private String windSpeed;

    /**
     * 湿度（百分比）
     */
    private String humidity;

    /**
     * 气压
     */
    private String pressure;

    /**
     * 能见度
     */
    private String visibility;

    /**
     * 紫外线指数
     */
    private String uvIndex;

    /**
     * 降水量
     */
    private String precipitation;

    /**
     * 日期（用于预报天气）
     */
    private String date;

    /**
     * 星期
     */
    private String week;

    /**
     * 白天天气（用于预报）
     */
    private String dayWeather;

    /**
     * 夜间天气（用于预报）
     */
    private String nightWeather;

    /**
     * 白天温度
     */
    private String dayTemperature;

    /**
     * 夜间温度
     */
    private String nightTemperature;
} 