package com.stylishlink.ai.controller;

import com.stylishlink.ai.dto.request.ChatRequest;
import com.stylishlink.ai.dto.response.ChatResponse;
import com.stylishlink.ai.service.AiChatService;
import com.stylishlink.common.dto.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * AI对话控制器
 */
@Slf4j
@RestController
@RequestMapping("/chat")
public class AiChatController {

    @Autowired
    private AiChatService chatService;

    /**
     * AI对话接口
     */
    @PostMapping("/chat")
    public ApiResponse<ChatResponse> chat(@Valid @RequestBody ChatRequest request) {
        try {
            ChatResponse response = chatService.chat(request);
            return ApiResponse.success("对话成功", response);
        } catch (Exception e) {
            log.error("AI对话失败", e);
            return ApiResponse.error(6001, "AI对话失败: " + e.getMessage());
        }
    }

    /**
     * 获取时尚建议接口
     */
    @PostMapping("/fashion-advice")
    public ApiResponse<ChatResponse> getFashionAdvice(@Valid @RequestBody ChatRequest request) {
        try {
            ChatResponse response = chatService.getFashionAdvice(request);
            return ApiResponse.success("获取时尚建议成功", response);
        } catch (Exception e) {
            log.error("获取时尚建议失败", e);
            return ApiResponse.error(6001, "获取时尚建议失败: " + e.getMessage());
        }
    }

    /**
     * 造型师咨询接口
     */
    @PostMapping("/stylist-consultation")
    public ApiResponse<ChatResponse> getStylistConsultation(@Valid @RequestBody ChatRequest request) {
        try {
            ChatResponse response = chatService.getStylistConsultation(request);
            return ApiResponse.success("造型师咨询成功", response);
        } catch (Exception e) {
            log.error("造型师咨询失败", e);
            return ApiResponse.error(6001, "造型师咨询失败: " + e.getMessage());
        }
    }
} 