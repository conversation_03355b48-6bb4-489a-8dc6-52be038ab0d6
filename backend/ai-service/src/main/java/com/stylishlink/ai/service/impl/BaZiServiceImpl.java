package com.stylishlink.ai.service.impl;

import com.stylishlink.ai.client.AiClient;
import com.stylishlink.ai.dto.BaZiRequest;
import com.stylishlink.ai.service.BaZiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * 八字分析服务实现类
 */
@Slf4j
@Service
public class BaZiServiceImpl implements BaZiService {
    
    private final AiClient aiClient;
    
    @Autowired
    public BaZiServiceImpl(AiClient aiClient) {
        this.aiClient = aiClient;
    }
    
    @Override
    public Map<String, Object> calculateBaZi(BaZiRequest request) {
        try {
            log.info("开始计算八字，出生时间: {}, 性别: {}", request.getBirthDateTime(), request.getGender());
            
            // 构建八字计算的提示词
            String prompt = buildBaZiPrompt(request);
            
            // 调用AI客户端进行八字计算
            Map<String, Object> aiResponse = aiClient.chat(prompt, "八字计算");
            
            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("birthDate", request.getBirthDate());
            result.put("birthTime", request.getBirthTime());
            result.put("birthDateTime", request.getBirthDateTime());
            result.put("gender", request.getGender());
            result.put("birthPlace", request.getBirthPlace());
            
            // 从AI响应中提取八字信息（这里是示例，实际需要解析AI返回的内容）
            result.put("baziData", extractBaZiData(aiResponse));
            result.put("aiAnalysis", aiResponse.get("response"));
            result.put("timestamp", System.currentTimeMillis());
            
            log.info("八字计算完成，用户: {}", request.getUserId());
            return result;
            
        } catch (Exception e) {
            log.error("八字计算失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("error", "八字计算失败: " + e.getMessage());
            result.put("success", false);
            return result;
        }
    }
    
    /**
     * 构建八字计算的提示词
     */
    private String buildBaZiPrompt(BaZiRequest request) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy年MM月dd日 HH时mm分");
        String birthTimeStr = request.getBirthDateTime().format(formatter);
        
        return String.format(
            "请根据以下信息计算八字命理：\n" +
            "出生时间：%s\n" +
            "性别：%s\n" +
            "出生地点：%s\n\n" +
            "请提供详细的八字分析，包括：\n" +
            "1. 年柱、月柱、日柱、时柱的天干地支\n" +
            "2. 五行分布情况（金、木、水、火、土的数量）\n" +
            "3. 命格特点和性格分析\n" +
            "4. 运势概况\n" +
            "5. 喜用神和忌神\n\n" +
            "请以JSON格式返回结果，包含以上各项分析内容。",
            birthTimeStr,
            request.getGender(),
            request.getBirthPlace() != null ? request.getBirthPlace() : "未提供"
        );
    }
    
    /**
     * 从AI响应中提取八字数据
     */
    private Map<String, Object> extractBaZiData(Map<String, Object> aiResponse) {
        Map<String, Object> baziData = new HashMap<>();
        
        // 示例八字数据（实际应该从AI响应中解析）
        baziData.put("yearPillar", "庚子"); // 年柱
        baziData.put("monthPillar", "戊子"); // 月柱  
        baziData.put("dayPillar", "甲寅"); // 日柱
        baziData.put("hourPillar", "丙寅"); // 时柱
        
        // 日主（从日柱天干获取）
        baziData.put("dayMaster", "甲"); // 日主
        
        // 五行元素列表
        java.util.List<String> elements = new java.util.ArrayList<>();
        elements.add("木");
        elements.add("金");
        elements.add("土");
        baziData.put("elements", elements);
        
        // 五行分布
        Map<String, Integer> wuxing = new HashMap<>();
        wuxing.put("金", 1);
        wuxing.put("木", 2);
        wuxing.put("水", 2);
        wuxing.put("火", 1);
        wuxing.put("土", 2);
        baziData.put("wuxingDistribution", wuxing);
        
        // 命格信息
        baziData.put("mingge", "正财格");
        baziData.put("xiyongshen", "木火");
        baziData.put("jishen", "金水");
        
        return baziData;
    }
    
    @Override
    public Map<String, Object> callAiForSummarize(String prompt) {
        try {
            log.info("调用AI进行文案简化，提示词长度: {}", prompt.length());
            
            // 调用AI客户端进行文案简化
            Map<String, Object> aiResponse = aiClient.chat(prompt, "文案简化");
            
            log.info("AI文案简化完成");
            return aiResponse;
            
        } catch (Exception e) {
            log.error("AI文案简化失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("error", "AI文案简化失败: " + e.getMessage());
            result.put("success", false);
            return result;
        }
    }
} 