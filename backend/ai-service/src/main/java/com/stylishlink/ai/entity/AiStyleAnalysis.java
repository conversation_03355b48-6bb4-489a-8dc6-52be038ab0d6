package com.stylishlink.ai.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 风格分析结果实体
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ai_style_analysis")
public class AiStyleAnalysis {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private String userId;

    /**
     * 来源类型(image/outfit)
     */
    @TableField("source_type")
    private String sourceType;

    /**
     * 来源ID
     */
    @TableField("source_id")
    private String sourceId;

    /**
     * 风格分布(JSON字符串)
     */
    @TableField("style_distribution")
    private String styleDistribution;

    /**
     * 主导颜色(JSON字符串)
     */
    @TableField("dominant_colors")
    private String dominantColors;

    /**
     * 推荐风格(JSON字符串)
     */
    @TableField("recommended_styles")
    private String recommendedStyles;

    /**
     * 材质分析(JSON字符串)
     */
    @TableField("material_analysis")
    private String materialAnalysis;

    /**
     * 图案分析(JSON字符串)
     */
    @TableField("pattern_analysis")
    private String patternAnalysis;

    /**
     * 季节适应性(JSON字符串)
     */
    @TableField("season_suitability")
    private String seasonSuitability;

    /**
     * 场合适应性(JSON字符串)
     */
    @TableField("occasion_suitability")
    private String occasionSuitability;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
}