package com.stylishlink.ai.controller;

import com.stylishlink.ai.dto.BaZiRequest;
import com.stylishlink.ai.dto.WuXingRequest;
import com.stylishlink.ai.dto.request.EnergyCalculationRequest;
import com.stylishlink.ai.dto.request.FortuneAnalysisRequest;
import com.stylishlink.ai.dto.response.EnergyCalculationResponse;
import com.stylishlink.ai.service.BaZiService;
import com.stylishlink.ai.service.WuXingService;
import com.stylishlink.ai.service.EnergyService;
import com.stylishlink.common.dto.ApiResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 八字和五行分析控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/ai")
@Tag(name = "八字五行分析", description = "八字计算和五行分析相关接口")
public class BaZiController {
    
    private final BaZiService baZiService;
    private final WuXingService wuXingService;
    private final EnergyService energyService;
    
    @Autowired
    public BaZiController(BaZiService baZiService, WuXingService wuXingService, 
                         EnergyService energyService) {
        this.baZiService = baZiService;
        this.wuXingService = wuXingService;
        this.energyService = energyService;
    }
    
    /**
     * 计算八字
     */
    @PostMapping("/bazi/calculate")
    @Operation(summary = "计算八字", description = "根据出生时间、性别等信息计算八字命理")
    public ApiResponse<Map<String, Object>> calculateBaZi(@Valid @RequestBody BaZiRequest request) {
        try {
            log.info("收到八字计算请求，出生时间: {}", request.getBirthTime());
            
            Map<String, Object> result = baZiService.calculateBaZi(request);
            
            if (result.containsKey("error")) {
                return ApiResponse.error((String) result.get("error"));
            }
            
            return ApiResponse.success(result);
            
        } catch (Exception e) {
            log.error("八字计算接口异常", e);
            return ApiResponse.error("八字计算失败: " + e.getMessage());
        }
    }
    
    /**
     * 分析五行
     */
    @PostMapping("/wuxing/analyze")
    @Operation(summary = "分析五行", description = "根据八字信息进行五行分析，包括性格、健康、事业、运势等")
    public ApiResponse<Map<String, Object>> analyzeWuxing(@Valid @RequestBody WuXingRequest request) {
        try {
            log.info("收到五行分析请求，八字: {}, 分析类型: {}", request.getBaziInfo(), request.getAnalysisType());
            
            Map<String, Object> result = wuXingService.analyzeWuxing(request);
            
            if (result.containsKey("error")) {
                return ApiResponse.error((String) result.get("error"));
            }
            
            return ApiResponse.success(result);
            
        } catch (Exception e) {
            log.error("五行分析接口异常", e);
            return ApiResponse.error("五行分析失败: " + e.getMessage());
        }
    }
    
    /**
     * 简化总结幸运元素文案
     */
    @PostMapping("/text/summarize-lucky-elements")
    @Operation(summary = "简化总结幸运元素文案", description = "使用AI简化总结服饰、配饰、妆容建议文案，生成简洁易懂的总结")
    public Map<String, Object> summarizeLuckyElements(@Valid @RequestBody Map<String, Object> request) {
        try {
            log.info("收到幸运元素文案简化请求: {}", request);
            
            // 提取请求参数
            @SuppressWarnings("unchecked")
            List<String> clothing = (List<String>) request.get("clothing");
            @SuppressWarnings("unchecked")
            List<String> accessories = (List<String>) request.get("accessories");
            @SuppressWarnings("unchecked")
            List<String> makeup = (List<String>) request.get("makeup");
            
            // 构建AI提示词
            String prompt = buildSummarizePrompt(clothing, accessories, makeup);
            
            // 调用AI进行文案简化
            Map<String, Object> aiResponse = baZiService.callAiForSummarize(prompt);
            
            // 解析AI响应
            Map<String, Object> summaryData = parseAiSummaryResponse(aiResponse, clothing, accessories, makeup);
            
            // 构建标准响应格式
            Map<String, Object> result = new HashMap<>();
            result.put("code", 200);
            result.put("message", "文案简化成功");
            result.put("data", summaryData);
            
            log.info("幸运元素文案简化完成");
            return result;
            
        } catch (Exception e) {
            log.error("幸运元素文案简化失败", e);
            
            // 返回错误响应
            Map<String, Object> result = new HashMap<>();
            result.put("code", 500);
            result.put("message", "文案简化失败: " + e.getMessage());
            result.put("data", createDefaultSummaryData(request));
            
            return result;
        }
    }

    /**
     * 生成完整运势解读
     */
    @PostMapping("/fortune/complete-analysis")
    @Operation(summary = "生成完整运势解读", description = "基于用户八字信息生成包含整体运势、吉运建议、详细运势的完整分析")
    public Map<String, Object> generateCompleteFortuneReading(@Valid @RequestBody Map<String, Object> request) {
        try {
            log.info("收到运势分析请求: userId={}, date={}", request.get("userId"), request.get("date"));
            
            // 转换请求数据为FortuneAnalysisRequest对象
            FortuneAnalysisRequest fortuneRequest = mapToFortuneRequest(request);
            
            // 调用五行服务生成运势解读
            Map<String, Object> fortuneData = wuXingService.generateCompleteFortuneReading(fortuneRequest);
            
            // 构建标准响应格式
            Map<String, Object> result = new HashMap<>();
            result.put("code", 200);
            result.put("message", "运势解读生成成功");
            result.put("data", fortuneData);
            
            log.info("运势解读生成完成: userId={}", fortuneRequest.getUserId());
            return result;
            
        } catch (Exception e) {
            log.error("运势解读生成失败", e);
            
            // 返回错误响应
            Map<String, Object> result = new HashMap<>();
            result.put("code", 500);
            result.put("message", "运势解读生成失败: " + e.getMessage());
            result.put("data", new HashMap<>());
            
            return result;
        }
    }

    /**
     * 计算今日能量信息
     */
    @PostMapping("/energy/today")
    @Operation(summary = "计算今日能量信息", description = "根据用户八字信息计算指定日期的能量运势")
    public Map<String, Object> calculateTodayEnergy(@Valid @RequestBody Map<String, Object> request) {
        try {
            log.info("收到能量计算请求: userId={}, date={}", request.get("userId"), request.get("date"));
            
            // 将Map转换为EnergyCalculationRequest对象
            EnergyCalculationRequest energyRequest = mapToEnergyRequest(request);
            
            // 调用服务计算能量
            EnergyCalculationResponse response = energyService.calculateTodayEnergy(energyRequest);
            
            // 构建符合用户服务期望的响应格式
            Map<String, Object> result = new HashMap<>();
            result.put("code", 200);
            result.put("message", "能量计算成功");
            result.put("data", response);
            
            log.info("能量计算完成: userId={}, totalScore={}", 
                    energyRequest.getUserId(), response.getTotalScore());
            
            return result;
            
        } catch (Exception e) {
            log.error("能量计算失败", e);
            
            // 返回错误响应
            Map<String, Object> result = new HashMap<>();
            result.put("code", 500);
            result.put("message", "能量计算失败: " + e.getMessage());
            result.put("data", createDefaultEnergyResponse(request));
            
            return result;
        }
    }
    
    /**
     * 将Map请求数据转换为EnergyCalculationRequest对象
     */
    @SuppressWarnings("unchecked")
    private EnergyCalculationRequest mapToEnergyRequest(Map<String, Object> request) {
        EnergyCalculationRequest.EnergyCalculationRequestBuilder builder = 
                EnergyCalculationRequest.builder();
        
        // 基本信息
        builder.userId(getStringValue(request, "userId"));
        builder.date(getStringValue(request, "date"));
        
        // 八字信息
        Object baziInfoObj = request.get("baziInfo");
        if (baziInfoObj instanceof Map) {
            Map<String, Object> baziInfoMap = (Map<String, Object>) baziInfoObj;
            
            // 修复elements字段的处理 - 支持List类型
            Map<String, Object> elementsMap = new HashMap<>();
            Object elementsObj = baziInfoMap.get("elements");
            if (elementsObj instanceof List) {
                // 如果是List类型，转换为Map格式
                List<?> elementsList = (List<?>) elementsObj;
                for (int i = 0; i < elementsList.size(); i++) {
                    elementsMap.put("element" + i, elementsList.get(i));
                }
            } else if (elementsObj instanceof Map) {
                // 如果是Map类型，直接使用
                elementsMap = (Map<String, Object>) elementsObj;
            }
            
            EnergyCalculationRequest.BaziInfo baziInfo = EnergyCalculationRequest.BaziInfo.builder()
                    .yearPillar(getStringValue(baziInfoMap, "yearPillar"))
                    .monthPillar(getStringValue(baziInfoMap, "monthPillar"))
                    .dayPillar(getStringValue(baziInfoMap, "dayPillar"))
                    .hourPillar(getStringValue(baziInfoMap, "hourPillar"))
                    .dayMaster(getStringValue(baziInfoMap, "dayMaster"))
                    .elements(elementsMap)
                    .build();
            builder.baziInfo(baziInfo);
        }
        
        // 用户信息
        Object userInfoObj = request.get("userInfo");
        if (userInfoObj instanceof Map) {
            Map<String, Object> userInfoMap = (Map<String, Object>) userInfoObj;
            EnergyCalculationRequest.UserInfo userInfo = EnergyCalculationRequest.UserInfo.builder()
                    .gender(getIntegerValue(userInfoMap, "gender"))
                    .birthDate(getStringValue(userInfoMap, "birthDate"))
                    .birthTime(getStringValue(userInfoMap, "birthTime"))
                    .birthPlace(getStringValue(userInfoMap, "birthPlace"))
                    .fullName(getStringValue(userInfoMap, "fullName"))
                    .build();
            builder.userInfo(userInfo);
        } else if (request.containsKey("userInfoString")) {
            // 如果userInfo不是Map，但有userInfoString，可以解析基本信息
            String userInfoString = getStringValue(request, "userInfoString");
            // 这里可以根据需要解析userInfoString，暂时使用空的UserInfo
            EnergyCalculationRequest.UserInfo userInfo = EnergyCalculationRequest.UserInfo.builder()
                    .build();
            builder.userInfo(userInfo);
        }
        
        return builder.build();
    }
    
    /**
     * 创建默认能量响应
     */
    private EnergyCalculationResponse createDefaultEnergyResponse(Map<String, Object> request) {
        return EnergyCalculationResponse.builder()
                .dateInfo(EnergyCalculationResponse.DateInfo.builder()
                        .gregorian(getStringValue(request, "date"))
                        .lunar("农历信息")
                        .build())
                .totalScore(75)
                .percentage(60)
                .peakTime("上午8-10点")
                .peakTimeDescription("能量充沛的时段")
                .description("今日能量平衡，适合进行常规活动")
                .dimensions(EnergyCalculationResponse.Dimensions.builder()
                        .love(75)
                        .career(80)
                        .wealth(70)
                        .health(85)
                        .relationship(75)
                        .build())
                .advice(EnergyCalculationResponse.Advice.builder()
                        .categories(java.util.Arrays.asList())
                        .lifeSuggestions(java.util.Arrays.asList())
                        .build())
                .luckyElements(EnergyCalculationResponse.LuckyElements.builder()
                        .colors(java.util.Arrays.asList(
                                EnergyCalculationResponse.ColorItem.builder()
                                        .value("#FF6B6B")
                                        .name("珊瑚红")
                                        .build(),
                                EnergyCalculationResponse.ColorItem.builder()
                                        .value("#4ECDC4")
                                        .name("青绿色")
                                        .build()))
                        .clothing(java.util.Arrays.asList("推荐穿着舒适的休闲服装"))
                        .accessories(java.util.Arrays.asList("建议佩戴简约的首饰"))
                        .makeup(java.util.Arrays.asList("清淡自然的妆容"))
                        .build())
                .build();
    }
    
    /**
     * 安全获取字符串值
     */
    private String getStringValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        return value != null ? value.toString() : "";
    }
    
    /**
     * 安全获取整数值
     */
    private Integer getIntegerValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        if (value instanceof String) {
            try {
                return Integer.parseInt((String) value);
            } catch (NumberFormatException e) {
                return null;
            }
        }
        return null;
    }
    
    /**
     * 构建AI文案简化提示词
     */
    private String buildSummarizePrompt(List<String> clothing, List<String> accessories, List<String> makeup) {
        StringBuilder prompt = new StringBuilder();
        prompt.append("请将以下幸运元素建议简化为简洁易懂的总结文案，每类最多一句话：\n\n");
        
        if (clothing != null && !clothing.isEmpty()) {
            prompt.append("服饰建议：\n");
            for (String item : clothing) {
                prompt.append("- ").append(item).append("\n");
            }
            prompt.append("\n");
        }
        
        if (accessories != null && !accessories.isEmpty()) {
            prompt.append("配饰建议：\n");
            for (String item : accessories) {
                prompt.append("- ").append(item).append("\n");
            }
            prompt.append("\n");
        }
        
        if (makeup != null && !makeup.isEmpty()) {
            prompt.append("妆容建议：\n");
            for (String item : makeup) {
                prompt.append("- ").append(item).append("\n");
            }
            prompt.append("\n");
        }
        
        prompt.append("请用简洁的语言总结以上建议，要求：\n");
        prompt.append("1. 每类建议（服饰、配饰、妆容）各用一句话总结\n");
        prompt.append("2. 保留关键的风格和颜色信息\n");
        prompt.append("3. 语言自然流畅，符合中文表达习惯\n");
        prompt.append("4. 返回JSON格式：{\"clothingSummary\": \"...\", \"accessoriesSummary\": \"...\", \"makeupSummary\": \"...\"}\n");
        
        return prompt.toString();
    }
    
    /**
     * 解析AI简化总结响应
     */
    private Map<String, Object> parseAiSummaryResponse(Map<String, Object> aiResponse, 
                                                      List<String> clothing, 
                                                      List<String> accessories, 
                                                      List<String> makeup) {
        Map<String, Object> summaryData = new HashMap<>();
        
        try {
            // 尝试从AI响应中提取总结
            Object reply = aiResponse.get("reply");
            if (reply != null) {
                String responseText = reply.toString();
                
                // 尝试解析JSON格式的响应
                Map<String, Object> parsedJson = parseJsonFromResponse(responseText);
                if (parsedJson != null) {
                    summaryData.put("clothingSummary", parsedJson.get("clothingSummary"));
                    summaryData.put("accessoriesSummary", parsedJson.get("accessoriesSummary"));
                    summaryData.put("makeupSummary", parsedJson.get("makeupSummary"));
                } else {
                    // 如果无法解析JSON，使用默认总结
                    summaryData = generateLocalSummary(clothing, accessories, makeup);
                }
            } else {
                // 如果没有AI回复，使用默认总结
                summaryData = generateLocalSummary(clothing, accessories, makeup);
            }
        } catch (Exception e) {
            log.warn("解析AI总结响应失败，使用默认总结: {}", e.getMessage());
            summaryData = generateLocalSummary(clothing, accessories, makeup);
        }
        
        return summaryData;
    }
    
    /**
     * 从响应文本中解析JSON
     */
    private Map<String, Object> parseJsonFromResponse(String responseText) {
        try {
            // 查找JSON部分
            int jsonStart = responseText.indexOf("{");
            int jsonEnd = responseText.lastIndexOf("}");
            
            if (jsonStart >= 0 && jsonEnd > jsonStart) {
                String jsonStr = responseText.substring(jsonStart, jsonEnd + 1);
                
                // 简单的JSON解析（这里可以使用ObjectMapper，但为了简单起见用字符串处理）
                Map<String, Object> result = new HashMap<>();
                
                // 解析clothingSummary
                String clothingSummary = extractValueFromJson(jsonStr, "clothingSummary");
                if (clothingSummary != null) {
                    result.put("clothingSummary", clothingSummary);
                }
                
                // 解析accessoriesSummary
                String accessoriesSummary = extractValueFromJson(jsonStr, "accessoriesSummary");
                if (accessoriesSummary != null) {
                    result.put("accessoriesSummary", accessoriesSummary);
                }
                
                // 解析makeupSummary
                String makeupSummary = extractValueFromJson(jsonStr, "makeupSummary");
                if (makeupSummary != null) {
                    result.put("makeupSummary", makeupSummary);
                }
                
                return result;
            }
        } catch (Exception e) {
            log.warn("JSON解析失败: {}", e.getMessage());
        }
        
        return null;
    }
    
    /**
     * 从JSON字符串中提取指定字段的值
     */
    private String extractValueFromJson(String jsonStr, String key) {
        try {
            String pattern = "\"" + key + "\"\\s*:\\s*\"([^\"]+)\"";
            java.util.regex.Pattern p = java.util.regex.Pattern.compile(pattern);
            java.util.regex.Matcher m = p.matcher(jsonStr);
            if (m.find()) {
                return m.group(1);
            }
        } catch (Exception e) {
            log.warn("提取JSON字段 {} 失败: {}", key, e.getMessage());
        }
        return null;
    }
    
    /**
     * 生成本地简化总结（备用方案）
     */
    private Map<String, Object> generateLocalSummary(List<String> clothing, 
                                                    List<String> accessories, 
                                                    List<String> makeup) {
        Map<String, Object> summaryData = new HashMap<>();
        
        // 服饰总结
        String clothingSummary = generateSummaryFromList(clothing, "今日服饰搭配以舒适自然为主。");
        summaryData.put("clothingSummary", clothingSummary);
        
        // 配饰总结
        String accessoriesSummary = generateSummaryFromList(accessories, "简约配饰最为适宜。");
        summaryData.put("accessoriesSummary", accessoriesSummary);
        
        // 妆容总结
        String makeupSummary = generateSummaryFromList(makeup, "自然清透妆容为佳。");
        summaryData.put("makeupSummary", makeupSummary);
        
        return summaryData;
    }
    
    /**
     * 从列表生成简要总结
     */
    private String generateSummaryFromList(List<String> list, String defaultSummary) {
        if (list == null || list.isEmpty()) {
            return defaultSummary;
        }
        
        if (list.size() == 1) {
            return list.get(0);
        }
        
        // 组合前两条建议
        String firstSuggestion = list.get(0);
        String secondSuggestion = list.get(1);
        return firstSuggestion + " " + secondSuggestion;
    }
    
    /**
     * 创建默认总结数据
     */
    private Map<String, Object> createDefaultSummaryData(Map<String, Object> request) {
        @SuppressWarnings("unchecked")
        List<String> clothing = (List<String>) request.get("clothing");
        @SuppressWarnings("unchecked")
        List<String> accessories = (List<String>) request.get("accessories");
        @SuppressWarnings("unchecked")
        List<String> makeup = (List<String>) request.get("makeup");
        
        return generateLocalSummary(clothing, accessories, makeup);
    }

    /**
     * 将Map请求数据转换为FortuneAnalysisRequest对象
     */
    @SuppressWarnings("unchecked")
    private FortuneAnalysisRequest mapToFortuneRequest(Map<String, Object> request) {
        FortuneAnalysisRequest.FortuneAnalysisRequestBuilder builder = 
                FortuneAnalysisRequest.builder();
        
        // 基本信息
        builder.userId(getStringValue(request, "userId"));
        
        // 处理日期
        String dateStr = getStringValue(request, "date");
        if (dateStr != null && !dateStr.isEmpty()) {
            try {
                builder.date(java.time.LocalDate.parse(dateStr));
            } catch (Exception e) {
                log.warn("日期解析失败，使用当前日期: {}", dateStr);
                builder.date(java.time.LocalDate.now());
            }
        } else {
            builder.date(java.time.LocalDate.now());
        }
        
        // 八字详情
        Object baziDetailObj = request.get("baziDetail");
        if (baziDetailObj instanceof Map) {
            Map<String, Object> baziDetailMap = (Map<String, Object>) baziDetailObj;
            FortuneAnalysisRequest.BaziDetail.BaziDetailBuilder baziBuilder = 
                    FortuneAnalysisRequest.BaziDetail.builder();
            
            baziBuilder.yearPillar(getStringValue(baziDetailMap, "yearPillar"));
            baziBuilder.monthPillar(getStringValue(baziDetailMap, "monthPillar"));
            baziBuilder.dayPillar(getStringValue(baziDetailMap, "dayPillar"));
            baziBuilder.hourPillar(getStringValue(baziDetailMap, "hourPillar"));
            
            // 五行分布
            Object wuxingDistObj = baziDetailMap.get("wuxingDistribution");
            if (wuxingDistObj instanceof Map) {
                Map<String, Object> wuxingMap = (Map<String, Object>) wuxingDistObj;
                FortuneAnalysisRequest.BaziDetail.WuxingDistribution.WuxingDistributionBuilder wuxingBuilder =
                        FortuneAnalysisRequest.BaziDetail.WuxingDistribution.builder();
                
                wuxingBuilder.metal(getIntegerValue(wuxingMap, "metal"));
                wuxingBuilder.wood(getIntegerValue(wuxingMap, "wood"));
                wuxingBuilder.water(getIntegerValue(wuxingMap, "water"));
                wuxingBuilder.fire(getIntegerValue(wuxingMap, "fire"));
                wuxingBuilder.earth(getIntegerValue(wuxingMap, "earth"));
                
                baziBuilder.wuxingDistribution(wuxingBuilder.build());
            }
            
            builder.baziDetail(baziBuilder.build());
        }
        
        // 用户信息
        Object userInfoObj = request.get("userInfo");
        if (userInfoObj instanceof Map) {
            Map<String, Object> userInfoMap = (Map<String, Object>) userInfoObj;
            FortuneAnalysisRequest.UserInfo.UserInfoBuilder userBuilder = 
                    FortuneAnalysisRequest.UserInfo.builder();
            
            userBuilder.fullName(getStringValue(userInfoMap, "fullName"));
            userBuilder.gender(getIntegerValue(userInfoMap, "gender"));
            userBuilder.birthTime(getStringValue(userInfoMap, "birthTime"));
            userBuilder.birthPlace(getStringValue(userInfoMap, "birthPlace"));
            
            // 处理出生日期
            String birthDateStr = getStringValue(userInfoMap, "birthDate");
            if (birthDateStr != null && !birthDateStr.isEmpty()) {
                try {
                    userBuilder.birthDate(java.time.LocalDate.parse(birthDateStr));
                } catch (Exception e) {
                    log.warn("出生日期解析失败: {}", birthDateStr);
                }
            }
            
            builder.userInfo(userBuilder.build());
        }
        
        return builder.build();
    }
} 