package com.stylishlink.ai.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 衣物识别结果实体
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ai_clothing_recognition_result")
public class ClothingRecognitionResult {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 识别结果ID
     */
    @TableField("recognition_id")
    private String recognitionId;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private String userId;

    /**
     * 文件ID
     */
    @TableField("file_id")
    private String fileId;

    /**
     * 图片URL
     */
    @TableField("image_url")
    private String imageUrl;

    /**
     * 衣物名称
     */
    @TableField("name")
    private String name;

    /**
     * 大类别（上衣/下装/外套/裙装/鞋履/配饰等）
     */
    @TableField("category")
    private String category;

    /**
     * 子类别（T恤/衬衫/牛仔裤等）
     */
    @TableField("sub_category")
    private String subCategory;

    /**
     * 颜色列表
     */
    @TableField(value = "colors", typeHandler = JacksonTypeHandler.class)
    private List<String> colors;

    /**
     * 材质列表
     */
    @TableField(value = "materials", typeHandler = JacksonTypeHandler.class)
    private List<String> materials;

    /**
     * 适合季节（春/夏/秋/冬）
     */
    @TableField(value = "seasons", typeHandler = JacksonTypeHandler.class)
    private List<String> seasons;

    /**
     * 适合场合（日常休闲/职场商务/约会交际/派对活动/运动健身/正式场合）
     */
    @TableField(value = "occasions", typeHandler = JacksonTypeHandler.class)
    private List<String> occasions;

    /**
     * 金属性评分（0-4）
     */
    @TableField("wuxing_metal")
    private Integer wuxingMetal;

    /**
     * 木属性评分（0-4）
     */
    @TableField("wuxing_wood")
    private Integer wuxingWood;

    /**
     * 水属性评分（0-4）
     */
    @TableField("wuxing_water")
    private Integer wuxingWater;

    /**
     * 火属性评分（0-4）
     */
    @TableField("wuxing_fire")
    private Integer wuxingFire;

    /**
     * 土属性评分（0-4）
     */
    @TableField("wuxing_earth")
    private Integer wuxingEarth;

    /**
     * 主要五行属性（金/木/水/火/土）
     */
    @TableField("wuxing_primary")
    private String wuxingPrimary;

    /**
     * 识别置信度（0-1）
     */
    @TableField("confidence")
    private BigDecimal confidence;

    /**
     * 穿搭建议
     */
    @TableField(value = "suggestions", typeHandler = JacksonTypeHandler.class)
    private List<String> suggestions;

    /**
     * 原始AI响应结果
     */
    @TableField("raw_result")
    private String rawResult;

    /**
     * 处理时间
     */
    @TableField("processed_at")
    private LocalDateTime processedAt;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
} 