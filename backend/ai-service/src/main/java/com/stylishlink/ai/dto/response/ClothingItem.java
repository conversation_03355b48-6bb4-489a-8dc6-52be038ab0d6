package com.stylishlink.ai.dto.response;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 衣物配饰物件DTO
 * 表示图像识别中的单个衣物或配饰项目
 */
@Data
public class ClothingItem {

    /**
     * 物件类型（如：上衣、裤子、鞋子、包包、帽子等）
     */
    private String type;

    /**
     * 类别（如：T恤、牛仔裤、运动鞋等具体分类）
     */
    private String category;

    /**
     * 颜色列表
     */
    private List<String> colors;

    /**
     * 风格（如：休闲、商务、运动、甜美等）
     */
    private String style;

    /**
     * 材质列表（如：棉质、聚酯纤维、真皮等）
     */
    private List<String> materials;

    /**
     * 图案列表（如：纯色、条纹、格子、印花等）
     */
    private List<String> patterns;

    /**
     * 置信度（0-1之间的浮点数，表示识别的准确性）
     */
    private BigDecimal confidence;
} 