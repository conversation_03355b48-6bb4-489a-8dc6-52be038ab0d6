package com.stylishlink.ai.dto.request;

import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import jakarta.validation.constraints.NotNull;

/**
 * 身材识别分析请求
 */
@Data
public class BodyShapeAnalysisRequest {
    
    /**
     * 图片文件（用于文件上传方式）
     */
    private MultipartFile file;
    
    /**
     * 图片URL（用于URL方式）
     */
    private String imageUrl;
    
    /**
     * 用户ID（可选）
     */
    private String userId;
    
    /**
     * 文件ID（用于URL方式时记录原文件）
     */
    private String fileId;
} 