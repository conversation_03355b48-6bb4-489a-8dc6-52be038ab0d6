package com.stylishlink.ai.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.stylishlink.ai.entity.BodyShapeAnalysisResult;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 身材识别结果Mapper接口
 */
@Mapper
public interface BodyShapeAnalysisResultMapper extends BaseMapper<BodyShapeAnalysisResult> {

    /**
     * 根据用户ID查询身材识别结果
     * @param userId 用户ID
     * @return 身材识别结果列表
     */
    List<BodyShapeAnalysisResult> selectByUserId(@Param("userId") String userId);

    /**
     * 根据用户ID查询最新的身材识别结果
     * @param userId 用户ID
     * @return 最新的身材识别结果
     */
    BodyShapeAnalysisResult selectLatestByUserId(@Param("userId") String userId);

    /**
     * 根据用户ID查询成功的身材识别结果
     * @param userId 用户ID
     * @return 成功的身材识别结果列表
     */
    List<BodyShapeAnalysisResult> selectSuccessfulByUserId(@Param("userId") String userId);

    /**
     * 根据体型分类查询结果
     * @param bodyType 体型分类
     * @return 身材识别结果列表
     */
    List<BodyShapeAnalysisResult> selectByBodyType(@Param("bodyType") String bodyType);
} 