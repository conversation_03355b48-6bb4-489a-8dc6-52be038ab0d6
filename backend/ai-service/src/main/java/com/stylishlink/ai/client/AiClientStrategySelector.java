package com.stylishlink.ai.client;

import com.stylishlink.ai.client.impl.DeepSeekClientImpl;
import com.stylishlink.ai.client.impl.DoubaoVisionClientImpl;
import com.stylishlink.ai.client.impl.OpenAiClientImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * AI客户端策略选择器
 * 实现智能的AI客户端选择逻辑
 */
@Slf4j
@Component
public class AiClientStrategySelector implements AiClientFactory {
    
    private final Map<AiClientType, AiClient> clientMap;
    private final Map<String, AiClientType> scenarioMapping;
    
    @Value("${ai.default.client:deepseek}")
    private String defaultClientType;
    
    @Value("${ai.vision.client:doubao}")
    private String visionClientType;
    
    public AiClientStrategySelector(
            @Autowired(required = false) DeepSeekClientImpl deepSeekClient,
            @Autowired(required = false) OpenAiClientImpl openAiClient,
            @Autowired(required = false) DoubaoVisionClientImpl doubaoVisionClient) {
        
        // 初始化客户端映射
        this.clientMap = new HashMap<>();
        if (deepSeekClient != null) {
            this.clientMap.put(AiClientType.DEEPSEEK, deepSeekClient);
        }
        if (openAiClient != null) {
            this.clientMap.put(AiClientType.OPENAI, openAiClient);
        }
        if (doubaoVisionClient != null) {
            this.clientMap.put(AiClientType.DOUBAO_VISION, doubaoVisionClient);
        }
        
        // 初始化场景映射
        this.scenarioMapping = new HashMap<>();
        initScenarioMapping();
        
        log.info("AI客户端策略选择器初始化完成，支持的客户端类型: {}", clientMap.keySet());
        
        if (clientMap.isEmpty()) {
            log.warn("没有可用的AI客户端实现，请检查配置");
        }
    }
    
    /**
     * 初始化场景映射配置
     */
    private void initScenarioMapping() {
        // 文本对话场景
        scenarioMapping.put("chat", AiClientType.DEEPSEEK);
        scenarioMapping.put("general", AiClientType.DEEPSEEK);
        scenarioMapping.put("fashion_advice", AiClientType.DEEPSEEK);
        
        // 图像分析场景
        scenarioMapping.put("vision", AiClientType.DOUBAO_VISION);
        scenarioMapping.put("image_recognition", AiClientType.DOUBAO_VISION);
        scenarioMapping.put("style_analysis", AiClientType.DOUBAO_VISION);
        scenarioMapping.put("color_analysis", AiClientType.DOUBAO_VISION);
        
        // 通用场景备选
        scenarioMapping.put("fallback", AiClientType.OPENAI);
    }
    
    @Override
    public AiClient getDefaultClient() {
        try {
            AiClientType type = AiClientType.fromCode(defaultClientType);
            AiClient client = clientMap.get(type);
            if (client != null) {
                log.debug("使用默认AI客户端: {}", type.getDescription());
                return client;
            }
        } catch (Exception e) {
            log.warn("获取默认AI客户端失败: {}", e.getMessage());
        }
        
        // 降级到最高优先级的可用客户端
        return getAvailableClient();
    }
    
    @Override
    public AiClient getClient(AiClientType clientType) {
        AiClient client = clientMap.get(clientType);
        if (client == null) {
            throw new IllegalArgumentException("不支持的AI客户端类型: " + clientType);
        }
        log.debug("获取指定AI客户端: {}", clientType.getDescription());
        return client;
    }
    
    @Override
    public AiClient getClientByScenario(String scenario) {
        AiClientType clientType = scenarioMapping.get(scenario);
        if (clientType != null && isClientAvailable(clientType)) {
            log.debug("根据场景 '{}' 选择AI客户端: {}", scenario, clientType.getDescription());
            return getClient(clientType);
        }
        
        // 场景未配置或客户端不可用时，使用默认客户端
        log.debug("场景 '{}' 未配置或客户端不可用，使用默认客户端", scenario);
        return getDefaultClient();
    }
    
    @Override
    public AiClient getAvailableClient() {
        // 按优先级顺序尝试获取可用客户端
        for (AiClientType type : AiClientType.values()) {
            if (isClientAvailable(type)) {
                log.debug("选择可用AI客户端: {}", type.getDescription());
                return getClient(type);
            }
        }
        
        throw new RuntimeException("没有可用的AI客户端");
    }
    
    @Override
    public AiClient getVisionClient() {
        try {
            AiClientType type = AiClientType.fromCode(visionClientType);
            if (isClientAvailable(type)) {
                log.debug("使用视觉分析AI客户端: {}", type.getDescription());
                return getClient(type);
            }
        } catch (Exception e) {
            log.warn("获取视觉分析AI客户端失败: {}", e.getMessage());
        }
        
        // 降级到豆包视觉客户端
        if (isClientAvailable(AiClientType.DOUBAO_VISION)) {
            return getClient(AiClientType.DOUBAO_VISION);
        }
        
        throw new RuntimeException("没有可用的视觉分析AI客户端");
    }
    
    @Override
    public boolean isClientAvailable(AiClientType clientType) {
        AiClient client = clientMap.get(clientType);
        if (client == null) {
            return false;
        }
        
        // TODO: 可以添加更复杂的健康检查逻辑
        // 例如：ping测试、最近错误率检查等
        
        return true;
    }
    
    @Override
    public List<AiClientType> getAvailableClientTypes() {
        return clientMap.entrySet().stream()
                .filter(entry -> isClientAvailable(entry.getKey()))
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
    }
    
    /**
     * 动态更新场景映射
     */
    public void updateScenarioMapping(String scenario, AiClientType clientType) {
        scenarioMapping.put(scenario, clientType);
        log.info("更新场景映射: {} -> {}", scenario, clientType.getDescription());
    }
    
    /**
     * 获取当前场景映射配置
     */
    public Map<String, AiClientType> getScenarioMappings() {
        return new HashMap<>(scenarioMapping);
    }
} 