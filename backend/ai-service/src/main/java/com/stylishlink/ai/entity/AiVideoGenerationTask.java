package com.stylishlink.ai.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 视频生成任务实体
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ai_video_generation_task")
public class AiVideoGenerationTask {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 任务ID
     */
    @TableField("task_id")
    private String taskId;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private String userId;

    /**
     * 模板ID
     */
    @TableField("template_id")
    private Long templateId;

    /**
     * 任务状态
     */
    @TableField("status")
    private String status;

    /**
     * 搭配ID
     */
    @TableField("outfit_id")
    private String outfitId;

    /**
     * 素材URL列表
     */
    @TableField("asset_urls")
    private List<String> assetUrls;

    /**
     * 结果视频URL
     */
    @TableField("result_url")
    private String resultUrl;

    /**
     * 错误信息
     */
    @TableField("error_message")
    private String errorMessage;

    /**
     * 生成进度(百分比)
     */
    @TableField("progress")
    private Integer progress;

    /**
     * 任务优先级
     */
    @TableField("priority")
    private Integer priority;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 完成时间
     */
    @TableField("completed_at")
    private LocalDateTime completedAt;

    /**
     * 视频生成状态枚举
     */
    public enum Status {
        PENDING("PENDING", "待处理"),
        PROCESSING("PROCESSING", "处理中"),
        COMPLETED("COMPLETED", "已完成"),
        FAILED("FAILED", "失败");

        private final String code;
        private final String description;

        Status(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }
} 