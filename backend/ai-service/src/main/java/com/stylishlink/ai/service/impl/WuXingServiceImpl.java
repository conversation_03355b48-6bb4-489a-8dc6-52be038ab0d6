package com.stylishlink.ai.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.stylishlink.ai.client.AiClient;
import com.stylishlink.ai.client.AiClientFactory;
import com.stylishlink.ai.dto.WuXingRequest;
import com.stylishlink.ai.dto.request.FortuneAnalysisRequest;
import com.stylishlink.ai.service.WuXingService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.Period;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;

/**
 * 五行分析服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WuXingServiceImpl implements WuXingService {
    
    private final AiClient aiClient;
    private final AiClientFactory aiClientFactory;
    private final ObjectMapper objectMapper;
    
    @Override
    public Map<String, Object> analyzeWuxing(WuXingRequest request) {
        try {
            log.info("开始五行分析，八字: {}, 分析类型: {}", request.getBaziInfo(), request.getAnalysisType());
            
            // 构建五行分析的提示词
            String prompt = buildWuXingPrompt(request);
            
            // 调用AI客户端进行五行分析
            Map<String, Object> aiResponse = aiClient.chat(prompt, "五行分析");
            
            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("baziInfo", request.getBaziInfo());
            result.put("analysisType", request.getAnalysisType());
            result.put("wuxingAttributes", request.getWuxingAttributes());
            
            // 五行分析结果
            result.put("wuxingAnalysis", extractWuXingAnalysis(request, aiResponse));
            result.put("aiAnalysis", aiResponse.get("response"));
            result.put("timestamp", System.currentTimeMillis());
            
            log.info("五行分析完成，用户: {}", request.getUserId());
            return result;
            
        } catch (Exception e) {
            log.error("五行分析失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("error", "五行分析失败: " + e.getMessage());
            result.put("success", false);
            return result;
        }
    }
    
    /**
     * 构建五行分析的提示词
     */
    private String buildWuXingPrompt(WuXingRequest request) {
        StringBuilder prompt = new StringBuilder();
        prompt.append("请根据以下八字和五行信息进行详细分析：\n");
        prompt.append("八字：").append(request.getBaziInfo()).append("\n");
        
        if (request.getWuxingAttributes() != null && !request.getWuxingAttributes().isEmpty()) {
            prompt.append("五行分布：");
            request.getWuxingAttributes().forEach((element, count) -> 
                prompt.append(element).append(":").append(count).append("个 ")
            );
            prompt.append("\n");
        }
        
        prompt.append("分析类型：").append(getAnalysisTypeDescription(request.getAnalysisType())).append("\n\n");
        
        prompt.append("请提供详细的五行分析，包括：\n");
        prompt.append("1. 五行平衡状况分析\n");
        prompt.append("2. 优势五行和缺失五行\n");
        prompt.append("3. 五行对应的性格特点\n");
        
        switch (request.getAnalysisType()) {
            case "personality" -> prompt.append("4. 性格分析和人格特质\n5. 行为倾向和思维模式\n");
            case "health" -> prompt.append("4. 健康运势和体质分析\n5. 易患疾病和保健建议\n");
            case "career" -> prompt.append("4. 事业运势和职业方向\n5. 适合的工作类型和发展建议\n");
            case "luck" -> prompt.append("4. 整体运势分析\n5. 财运、感情运、健康运等\n");
            default -> prompt.append("4. 综合分析和建议\n");
        }
        
        prompt.append("\n请以JSON格式返回结果，包含以上各项分析内容。");
        
        return prompt.toString();
    }
    
    /**
     * 获取分析类型描述
     */
    private String getAnalysisTypeDescription(String analysisType) {
        return switch (analysisType) {
            case "personality" -> "性格分析";
            case "health" -> "健康分析";
            case "career" -> "事业分析";
            case "luck" -> "运势分析";
            default -> "综合分析";
        };
    }
    
    /**
     * 从AI响应中提取五行分析数据
     */
    private Map<String, Object> extractWuXingAnalysis(WuXingRequest request, Map<String, Object> aiResponse) {
        Map<String, Object> analysis = new HashMap<>();
        
        // 五行平衡分析
        Map<String, Object> balance = new HashMap<>();
        balance.put("strongElements", new String[]{"木", "火"});
        balance.put("weakElements", new String[]{"金"});
        balance.put("missingElements", new String[]{});
        balance.put("balanceScore", 75);
        analysis.put("wuxingBalance", balance);
        
        // 性格特点分析
        Map<String, Object> personality = new HashMap<>();
        personality.put("mainTraits", new String[]{"积极向上", "富有创造力", "行动力强"});
        personality.put("strengths", new String[]{"领导能力", "决策果断", "适应性强"});
        personality.put("weaknesses", new String[]{"有时过于冲动", "缺乏耐心"});
        analysis.put("personalityAnalysis", personality);
        
        // 根据分析类型添加特定内容
        switch (request.getAnalysisType()) {
            case "health" -> {
                Map<String, Object> health = new HashMap<>();
                health.put("bodyType", "阳性体质");
                health.put("healthTrends", new String[]{"心血管需注意", "消化系统较好"});
                health.put("suggestions", new String[]{"多运动", "清淡饮食", "规律作息"});
                analysis.put("healthAnalysis", health);
            }
            case "career" -> {
                Map<String, Object> career = new HashMap<>();
                career.put("suitableFields", new String[]{"管理", "销售", "教育", "创意行业"});
                career.put("careerTrends", "事业运势上升期");
                career.put("suggestions", new String[]{"把握机会", "发挥领导才能", "注重团队合作"});
                analysis.put("careerAnalysis", career);
            }
            case "luck" -> {
                Map<String, Object> luck = new HashMap<>();
                luck.put("overallLuck", "中上");
                luck.put("wealthLuck", "较好");
                luck.put("relationshipLuck", "稳定");
                luck.put("healthLuck", "良好");
                analysis.put("luckAnalysis", luck);
            }
        }
        
        // 建议和改善方法
        Map<String, Object> suggestions = new HashMap<>();
        suggestions.put("colorSuggestions", new String[]{"绿色", "红色", "黄色"});
        suggestions.put("directionSuggestions", new String[]{"东方", "南方"});
        suggestions.put("lifestyleSuggestions", new String[]{"多接触自然", "保持乐观心态", "规律运动"});
        analysis.put("improvementSuggestions", suggestions);
        
        return analysis;
    }

    @Override
    public Map<String, Object> generateCompleteFortuneReading(FortuneAnalysisRequest request) {
        log.info("开始生成完整运势解读: userId={}, date={}", request.getUserId(), request.getDate());
        
        try {
            // 构建AI提示词
            String prompt = buildFortuneAnalysisPrompt(request);
            
            // 调用AI生成运势解读
            Map<String, Object> aiResponseMap = aiClientFactory.getDefaultClient().chat(prompt, "运势解读分析");
            
            // 正确提取AI响应内容
            String aiResponse;
            if (aiResponseMap.containsKey("reply")) {
                aiResponse = (String) aiResponseMap.get("reply");
            } else if (aiResponseMap.containsKey("content")) {
                aiResponse = (String) aiResponseMap.get("content");
            } else {
                // 如果都没有，查找可能的文本响应
                aiResponse = aiResponseMap.values().stream()
                    .filter(v -> v instanceof String)
                    .map(String.class::cast)
                    .findFirst()
                    .orElseThrow(() -> new RuntimeException("AI响应中未找到有效内容"));
            }
            
            log.info("AI原始响应长度: {}", aiResponse.length());
            
            // 解析AI响应
            Map<String, Object> fortuneData = parseAiResponse(aiResponse, request);
            
            log.info("运势解读生成成功: userId={}", request.getUserId());
            return fortuneData;
            
        } catch (Exception e) {
            log.error("生成运势解读失败: userId={}", request.getUserId(), e);
            throw new RuntimeException("AI运势解读生成失败: " + e.getMessage(), e);
        }
    }

    /**
     * 构建运势分析AI提示词
     */
    private String buildFortuneAnalysisPrompt(FortuneAnalysisRequest request) {
        StringBuilder prompt = new StringBuilder();
        
        // 计算用户当前年龄和出生年份
        int currentAge = calculateAge(request.getUserInfo().getBirthDate(), request.getDate());
        int birthYear = request.getUserInfo().getBirthDate().getYear();
        
        prompt.append("你是一位专业的命理大师，请根据以下信息为用户生成运势解读分析：\n\n");
        
        // 用户基本信息
        prompt.append("## 用户信息\n");
        prompt.append("姓名：").append(request.getUserInfo().getFullName()).append("\n");
        prompt.append("性别：").append(request.getUserInfo().getGender() == 1 ? "男" : "女").append("\n");
        prompt.append("出生日期：").append(request.getUserInfo().getBirthDate()).append("\n");
        prompt.append("出生时间：").append(request.getUserInfo().getBirthTime()).append("\n");
        prompt.append("出生地点：").append(request.getUserInfo().getBirthPlace()).append("\n");
        prompt.append("当前年龄：").append(currentAge).append("岁\n");
        prompt.append("查询日期：").append(request.getDate()).append("\n\n");
        
        // 八字信息
        prompt.append("## 八字信息\n");
        prompt.append("年柱：").append(request.getBaziDetail().getYearPillar()).append("\n");
        prompt.append("月柱：").append(request.getBaziDetail().getMonthPillar()).append("\n");
        prompt.append("日柱：").append(request.getBaziDetail().getDayPillar()).append("\n");
        prompt.append("时柱：").append(request.getBaziDetail().getHourPillar()).append("\n\n");
        
        // 五行分布
        prompt.append("## 五行分布\n");
        FortuneAnalysisRequest.BaziDetail.WuxingDistribution wuxing = request.getBaziDetail().getWuxingDistribution();
        if (wuxing != null) {
            prompt.append("金：").append(wuxing.getMetal() != null ? wuxing.getMetal() : 0).append("个\n");
            prompt.append("木：").append(wuxing.getWood() != null ? wuxing.getWood() : 0).append("个\n");
            prompt.append("水：").append(wuxing.getWater() != null ? wuxing.getWater() : 0).append("个\n");
            prompt.append("火：").append(wuxing.getFire() != null ? wuxing.getFire() : 0).append("个\n");
            prompt.append("土：").append(wuxing.getEarth() != null ? wuxing.getEarth() : 0).append("个\n\n");
        } else {
            prompt.append("金：2个\n");
            prompt.append("木：2个\n");
            prompt.append("水：2个\n");
            prompt.append("火：1个\n");
            prompt.append("土：1个\n\n");
        }
        
        // 分析要求
        prompt.append("## 分析要求\n");
        prompt.append("请严格按照以下JSON格式返回运势分析结果（注意：不需要生成八字组合和五行分析，只需要运势分析部分），不要添加任何其他文字说明：\n\n");
        prompt.append("```json\n");
        prompt.append("{\n");
        prompt.append("  \"wuxingSummary\": {\n");
        prompt.append("    \"favorableElements\": [\"木\", \"水\"],\n");
        prompt.append("    \"unfavorableElements\": [\"火\", \"土\"],\n");
        prompt.append("    \"favorableColors\": [\"绿色\", \"蓝色\", \"青色\"],\n");
        prompt.append("    \"unfavorableColors\": [\"红色\", \"黄色\"],\n");
        prompt.append("    \"summary\": \"喜用木、水，宜着绿色、蓝色，忌着红色、黄色\"\n");
        prompt.append("  },\n");
        prompt.append("  \"overallFortune\": {\n");
        prompt.append("    \"lifetimeOverview\": \"综合八字分析，您的一生运势整体呈波浪式上升趋势，中年期达到巅峰，晚年平稳安康。重要转折点出现在30-40岁期间，需把握机遇。\",\n");
        prompt.append("    \"decadeFortunes\": [\n");
        prompt.append("      {\n");
        prompt.append("        \"decade\": 1,\n");
        prompt.append("        \"ageRange\": \"0-9岁\",\n");
        prompt.append("        \"yearRange\": \"").append(birthYear).append("-").append(birthYear + 9).append("\",\n");
        prompt.append("        \"score\": 75,\n");
        prompt.append("        \"theme\": \"成长奠基\",\n");
        prompt.append("        \"description\": \"童年时期，身体健康，家庭和睦，为未来发展奠定良好基础。\",\n");
        prompt.append("        \"keyEvents\": [\"启蒙教育\", \"性格养成\"]\n");
        prompt.append("      },\n");
        prompt.append("      {\n");
        prompt.append("        \"decade\": 2,\n");
        prompt.append("        \"ageRange\": \"10-19岁\",\n");
        prompt.append("        \"yearRange\": \"").append(birthYear + 10).append("-").append(birthYear + 19).append("\",\n");
        prompt.append("        \"score\": 80,\n");
        prompt.append("        \"theme\": \"求学成长\",\n");
        prompt.append("        \"description\": \"学业运势良好，智慧开启，有贵人相助。建议专注学习，培养兴趣。\",\n");
        prompt.append("        \"keyEvents\": [\"学业成就\", \"才能显现\"]\n");
        prompt.append("      }\n");
        prompt.append("      // ... 请生成完整的10个decade数据\n");
        prompt.append("    ],\n");
        prompt.append("    \"currentDecade\": ").append((currentAge / 10) + 1).append(",\n");
        prompt.append("    \"currentAge\": ").append(currentAge).append(",\n");
        prompt.append("    \"lifePhase\": \"").append(getLifePhase(currentAge)).append("\"\n");
        prompt.append("  },\n");
        prompt.append("  \"luckyAdvice\": {\n");
        prompt.append("    \"clothing\": {\n");
        prompt.append("      \"title\": \"服装选择\",\n");
        prompt.append("      \"items\": [\n");
        prompt.append("        {\"label\": \"颜色搭配\", \"advice\": \"多穿绿色、青色系服装，有助于增强木的力量\", \"type\": \"recommend\"},\n");
        prompt.append("        {\"label\": \"面料选择\", \"advice\": \"选择棉麻等天然面料，避免过多化纤材质\", \"type\": \"recommend\"}\n");
        prompt.append("      ]\n");
        prompt.append("    },\n");
        prompt.append("    \"jewelry\": {\n");
        prompt.append("      \"title\": \"首饰佩戴\",\n");
        prompt.append("      \"items\": [\n");
        prompt.append("        {\"label\": \"材质推荐\", \"advice\": \"木质、绿色宝石首饰最为适宜\", \"type\": \"recommend\"}\n");
        prompt.append("      ]\n");
        prompt.append("    },\n");
        prompt.append("    \"fengshui\": {\n");
        prompt.append("      \"title\": \"居家风水\",\n");
        prompt.append("      \"items\": [\n");
        prompt.append("        {\"label\": \"植物摆放\", \"advice\": \"在家中东方位多放置绿植，增强木气\", \"type\": \"recommend\"},\n");
        prompt.append("        {\"label\": \"金属装饰\", \"advice\": \"避免在卧室放置过多金属装饰品\", \"type\": \"avoid\"}\n");
        prompt.append("      ]\n");
        prompt.append("    }\n");
        prompt.append("  },\n");
        prompt.append("  \"detailedFortune\": {\n");
        prompt.append("    \"monthly\": {\n");
        prompt.append("      \"career\": {\"status\": \"稳步上升\", \"highlight\": \"贵人运佳\", \"color\": \"#4CAF50\", \"content\": [\"本月工作运势整体向好\", \"有机会接触重要项目\"]},\n");
        prompt.append("      \"wealth\": {\"status\": \"财运亨通\", \"highlight\": \"投资有利\", \"color\": \"#FF9800\", \"content\": \"正财收入稳定，偏财运也不错\"},\n");
        prompt.append("      \"health\": {\"status\": \"精神良好\", \"highlight\": \"注意休息\", \"color\": \"#8BC34A\", \"content\": [\"身体状况总体良好\", \"注意不要过度劳累\"]},\n");
        prompt.append("      \"marriage\": {\"status\": \"感情和睦\", \"highlight\": \"沟通顺畅\", \"color\": \"#E91E63\", \"content\": \"单身者有望遇到心仪对象\"},\n");
        prompt.append("      \"children\": {\"status\": \"亲子关系良好\", \"highlight\": \"教育有方\", \"color\": \"#9C27B0\", \"content\": [\"与子女关系融洽\", \"在教育方面要注意方式方法\"]}\n");
        prompt.append("    },\n");
        prompt.append("    \"yearly\": {\n");
        prompt.append("      \"career\": {\"status\": \"突破发展\", \"highlight\": \"机遇众多\", \"color\": \"#2196F3\", \"content\": [\"今年是事业发展的关键年\", \"会有多个重要机会出现\"]},\n");
        prompt.append("      \"wealth\": {\"status\": \"财源广进\", \"highlight\": \"多元收入\", \"color\": \"#4CAF50\", \"content\": \"收入来源将更加多元化\"},\n");
        prompt.append("      \"health\": {\"status\": \"健康平稳\", \"highlight\": \"预防为主\", \"color\": \"#009688\", \"content\": [\"整体健康状况良好\", \"建议定期体检\"]},\n");
        prompt.append("      \"marriage\": {\"status\": \"感情深化\", \"highlight\": \"家庭美满\", \"color\": \"#F44336\", \"content\": \"感情生活将更加丰富多彩\"},\n");
        prompt.append("      \"children\": {\"status\": \"成长顺利\", \"highlight\": \"天赋展现\", \"color\": \"#673AB7\", \"content\": [\"子女在学习和成长方面表现出色\", \"要给予更多关爱和支持\"]}\n");
        prompt.append("    }\n");
        prompt.append("  }\n");
        prompt.append("}\n");
        prompt.append("```\n\n");
        
        prompt.append("注意事项：\n");
        prompt.append("1. 必须严格按照上述完整JSON格式返回，包含所有字段\n");
        prompt.append("2. wuxingSummary要基于用户八字五行分布进行准确分析\n");
        prompt.append("3. decadeFortunes需要生成完整的10个decade数据，每个decade代表10年\n");
        prompt.append("4. lifetimeOverview要提供对一生整体运势的综合预测\n");
        prompt.append("5. luckyAdvice和detailedFortune必须填写完整，不能为空\n");
        prompt.append("6. 运势分数范围为0-100，要符合命理学规律\n");
        prompt.append("7. 运势分析要基于提供的八字信息\n");
        prompt.append("8. 建议要具体实用，避免空泛的表述\n");
        prompt.append("9. 严格按照JSON格式返回，不要有格式错误\n");
        prompt.append("10. 重要：不要生成baziCombination和wuxingAnalysis部分，这些由其他服务负责\n");
        
        return prompt.toString();
    }

    /**
     * 解析AI响应
     */
    private Map<String, Object> parseAiResponse(String aiResponse, FortuneAnalysisRequest request) {
        try {
            // 提取JSON部分
            String jsonContent = extractJsonFromResponse(aiResponse);
            
            // 解析JSON
            Map<String, Object> fortuneData = objectMapper.readValue(jsonContent, Map.class);
            
            // 验证和补充数据
            validateAndEnhanceFortuneData(fortuneData, request);
            
            return fortuneData;
            
        } catch (Exception e) {
            log.error("解析AI响应失败，使用默认数据: {}", e.getMessage());
            return generateDefaultFortuneData(request);
        }
    }

    /**
     * 从AI响应中提取JSON内容
     */
    private String extractJsonFromResponse(String response) {
        log.info("开始解析AI响应，响应长度: {}", response.length());
        
        // 查找JSON代码块
        int jsonStart = response.indexOf("```json");
        int jsonEnd = response.lastIndexOf("```");
        
        if (jsonStart != -1 && jsonEnd != -1 && jsonEnd > jsonStart) {
            String jsonContent = response.substring(jsonStart + 7, jsonEnd).trim();
            log.info("成功提取JSON代码块，内容长度: {}", jsonContent.length());
            return jsonContent;
        }
        
        // 如果没有代码块，尝试查找大括号
        int braceStart = response.indexOf("{");
        int braceEnd = response.lastIndexOf("}");
        
        if (braceStart != -1 && braceEnd != -1 && braceEnd > braceStart) {
            String jsonContent = response.substring(braceStart, braceEnd + 1);
            log.info("从响应中提取JSON大括号内容，内容长度: {}", jsonContent.length());
            return jsonContent;
        }
        
        log.error("无法从AI响应中提取JSON内容，响应前100字符: {}", 
                  response.length() > 100 ? response.substring(0, 100) : response);
        throw new RuntimeException("无法从AI响应中提取JSON内容");
    }

    /**
     * 验证和增强运势数据
     */
    @SuppressWarnings("unchecked")
    private void validateAndEnhanceFortuneData(Map<String, Object> fortuneData, FortuneAnalysisRequest request) {
        // 确保decadeFortunes有完整的10个decade数据
        Map<String, Object> overallFortune = (Map<String, Object>) fortuneData.get("overallFortune");
        if (overallFortune != null) {
            List<Map<String, Object>> decadeFortunes = (List<Map<String, Object>>) overallFortune.get("decadeFortunes");
            if (decadeFortunes == null || decadeFortunes.size() < 10) {
                // 生成完整的10个decade运势数据
                decadeFortunes = generateDecadeFortuneData(request);
                overallFortune.put("decadeFortunes", decadeFortunes);
            }
            
            // 确保有lifetimeOverview
            if (!overallFortune.containsKey("lifetimeOverview")) {
                overallFortune.put("lifetimeOverview", generateLifetimeOverview(request));
            }
        }
        
        // 不再补充缺失字段，要求AI按照完整格式返回
        
        // AI服务只负责运势分析，不处理五行分析数据
    }

    /**
     * 生成10个decade运势数据
     */
    private List<Map<String, Object>> generateDecadeFortuneData(FortuneAnalysisRequest request) {
        List<Map<String, Object>> decadeFortunes = new ArrayList<>();
        int birthYear = request.getUserInfo().getBirthDate().getYear();
        
        // 生成10个decade数据
        for (int decade = 1; decade <= 10; decade++) {
            int startAge = (decade - 1) * 10;
            int endAge = decade * 10 - 1;
            int startYear = birthYear + startAge;
            int endYear = birthYear + endAge;
            
            Map<String, Object> decadeData = new HashMap<>();
            decadeData.put("decade", decade);
            decadeData.put("ageRange", startAge + "-" + endAge + "岁");
            decadeData.put("yearRange", startYear + "-" + endYear);
            decadeData.put("score", calculateDecadeScore(decade, request));
            decadeData.put("theme", getDecadeTheme(decade));
            decadeData.put("description", getDecadeDescription(decade, request));
            decadeData.put("keyEvents", getDecadeKeyEvents(decade));
            
            decadeFortunes.add(decadeData);
        }
        
        return decadeFortunes;
    }

    /**
     * 生成一生整体运势概述
     */
    private String generateLifetimeOverview(FortuneAnalysisRequest request) {
        StringBuilder overview = new StringBuilder();
        overview.append("综合八字分析，您的一生运势");
        
        // 根据五行分布分析整体趋势
        FortuneAnalysisRequest.BaziDetail.WuxingDistribution wuxing = request.getBaziDetail().getWuxingDistribution();
        int totalElements = wuxing.getMetal() + wuxing.getWood() + wuxing.getWater() + wuxing.getFire() + wuxing.getEarth();
        
        if (totalElements > 6) {
            overview.append("整体呈稳步上升趋势，");
        } else {
            overview.append("呈波浪式发展趋势，");
        }
        
        overview.append("中年期达到人生巅峰，晚年平稳安康。");
        overview.append("重要转折点出现在30-40岁期间，需把握机遇。");
        
        return overview.toString();
    }

    // 五行元素分析已移至用户服务，AI服务不再处理

    /**
     * 计算decade运势分数
     */
    private int calculateDecadeScore(int decade, FortuneAnalysisRequest request) {
        // 基础分数
        int baseScore = 70;
        
        // decade因素：早期较低，中期最高，后期逐渐下降
        if (decade <= 2) {
            baseScore += 5; // 童年青少年期
        } else if (decade >= 3 && decade <= 6) {
            baseScore += 15; // 青壮年期
        } else if (decade >= 7 && decade <= 8) {
            baseScore += 10; // 中年期
        } else {
            baseScore -= (decade - 8) * 5; // 老年期逐渐下降
        }
        
        // 添加随机波动
        Random random = new Random(request.getUserId().hashCode() + decade);
        int fluctuation = random.nextInt(21) - 10; // -10到+10的随机波动
        
        int finalScore = baseScore + fluctuation;
        return Math.max(0, Math.min(100, finalScore)); // 确保在0-100范围内
    }

    /**
     * 获取decade主题
     */
    private String getDecadeTheme(int decade) {
        switch (decade) {
            case 1: return "成长奠基";
            case 2: return "求学成长";
            case 3: return "事业起步";
            case 4: return "发展建设";
            case 5: return "事业巅峰";
            case 6: return "成熟稳定";
            case 7: return "收获智慧";
            case 8: return "传承经验";
            case 9: return "享受人生";
            case 10: return "圆满安康";
            default: return "人生历程";
        }
    }

    /**
     * 获取decade描述
     */
    private String getDecadeDescription(int decade, FortuneAnalysisRequest request) {
        switch (decade) {
            case 1: return "童年时期，身体健康，家庭和睦，为未来发展奠定良好基础。";
            case 2: return "学业运势良好，智慧开启，有贵人相助。建议专注学习，培养兴趣。";
            case 3: return "踏入社会，事业起步，虽有挑战但机遇并存。需积极进取，把握机会。";
            case 4: return "事业发展期，运势上升，有重要突破。感情生活也将有所收获。";
            case 5: return "人生巅峰期，事业财运双收，家庭美满。是人生最辉煌的十年。";
            case 6: return "成熟稳定期，事业稳固，财富积累。注重健康，享受家庭生活。";
            case 7: return "智慧收获期，经验丰富，德高望重。可考虑传承知识，培养后进。";
            case 8: return "传承经验期，享受天伦之乐，关注健康养生。运势平稳安康。";
            case 9: return "享受人生期，儿孙满堂，生活安逸。运势平和，健康是重点。";
            case 10: return "圆满安康期，人生圆满，长寿安康。晚年生活幸福美满。";
            default: return "人生各个阶段都有其独特的意义和价值。";
        }
    }

    /**
     * 获取decade关键事件
     */
    private List<String> getDecadeKeyEvents(int decade) {
        List<String> events = new ArrayList<>();
        switch (decade) {
            case 1:
                events.add("启蒙教育");
                events.add("性格养成");
                break;
            case 2:
                events.add("学业成就");
                events.add("才能显现");
                break;
            case 3:
                events.add("职场初入");
                events.add("人际拓展");
                break;
            case 4:
                events.add("事业突破");
                events.add("感情收获");
                break;
            case 5:
                events.add("事业巅峰");
                events.add("财富积累");
                break;
            case 6:
                events.add("家庭稳定");
                events.add("事业成熟");
                break;
            case 7:
                events.add("智慧传承");
                events.add("声望提升");
                break;
            case 8:
                events.add("天伦之乐");
                events.add("健康养生");
                break;
            case 9:
                events.add("安享晚年");
                events.add("儿孙满堂");
                break;
            case 10:
                events.add("人生圆满");
                events.add("长寿安康");
                break;
            default:
                events.add("人生历程");
                break;
        }
        return events;
    }

    /**
     * 根据年龄获取人生阶段
     */
    private String getPeriodByAge(int age) {
        if (age < 18) return "少年期";
        if (age < 30) return "青年期";
        if (age < 50) return "中年期";
        if (age < 70) return "成熟期";
        return "老年期";
    }

    /**
     * 根据年龄获取当前生命阶段
     */
    private String getLifePhase(int age) {
        if (age < 20) return "成长期";
        if (age < 30) return "探索期";
        if (age < 50) return "建设期";
        if (age < 70) return "成熟期";
        return "收获期";
    }

    /**
     * 生成默认吉运建议
     */
    private Map<String, Object> generateDefaultLuckyAdvice(FortuneAnalysisRequest request) {
        Map<String, Object> luckyAdvice = new HashMap<>();
        
        // 服装建议
        Map<String, Object> clothing = new HashMap<>();
        clothing.put("title", "服装选择");
        List<Map<String, Object>> clothingItems = new ArrayList<>();
        clothingItems.add(createAdviceItem("颜色搭配", "多选择温和色调，避免过于鲜艳的颜色", "recommend"));
        clothingItems.add(createAdviceItem("面料选择", "选择天然面料，如棉麻，有助于运势提升", "recommend"));
        clothing.put("items", clothingItems);
        luckyAdvice.put("clothing", clothing);
        
        // 首饰建议
        Map<String, Object> jewelry = new HashMap<>();
        jewelry.put("title", "首饰佩戴");
        List<Map<String, Object>> jewelryItems = new ArrayList<>();
        jewelryItems.add(createAdviceItem("材质推荐", "佩戴温润的玉石或木质饰品", "recommend"));
        jewelry.put("items", jewelryItems);
        luckyAdvice.put("jewelry", jewelry);
        
        // 风水建议
        Map<String, Object> fengshui = new HashMap<>();
        fengshui.put("title", "居家风水");
        List<Map<String, Object>> fengshuiItems = new ArrayList<>();
        fengshuiItems.add(createAdviceItem("植物摆放", "在家中摆放绿色植物，增强生气", "recommend"));
        fengshuiItems.add(createAdviceItem("方位调整", "注意睡床方向，有助于运势调和", "recommend"));
        fengshui.put("items", fengshuiItems);
        luckyAdvice.put("fengshui", fengshui);
        
        return luckyAdvice;
    }

    /**
     * 生成默认详细运势
     */
    private Map<String, Object> generateDefaultDetailedFortune(FortuneAnalysisRequest request) {
        Map<String, Object> detailedFortune = new HashMap<>();
        
        // 月度运势
        Map<String, Object> monthly = new HashMap<>();
        List<String> careerContent = new ArrayList<>();
        careerContent.add("工作运势整体向好");
        careerContent.add("有新的机会出现");
        monthly.put("career", createFortuneItem("稳步发展", "贵人相助", "#4CAF50", careerContent));
        monthly.put("wealth", createFortuneItem("财运平稳", "量入为出", "#FF9800", "收入稳定，理财有道"));
        List<String> healthContent = new ArrayList<>();
        healthContent.add("身体状况良好");
        healthContent.add("保持规律作息");
        monthly.put("health", createFortuneItem("身体健康", "注意休息", "#8BC34A", healthContent));
        monthly.put("marriage", createFortuneItem("感情和睦", "沟通顺畅", "#E91E63", "感情生活温馨美满"));
        List<String> childrenContent = new ArrayList<>();
        childrenContent.add("与子女关系良好");
        childrenContent.add("注重品格培养");
        monthly.put("children", createFortuneItem("亲子融洽", "教育有方", "#9C27B0", childrenContent));
        detailedFortune.put("monthly", monthly);
        
        // 年度运势
        Map<String, Object> yearly = new HashMap<>();
        List<String> yearlyCareerContent = new ArrayList<>();
        yearlyCareerContent.add("年度事业运势良好");
        yearlyCareerContent.add("把握发展机会");
        yearly.put("career", createFortuneItem("事业进展", "机遇显现", "#2196F3", yearlyCareerContent));
        yearly.put("wealth", createFortuneItem("财源广进", "理财有道", "#4CAF50", "收入来源多样化"));
        List<String> yearlyHealthContent = new ArrayList<>();
        yearlyHealthContent.add("整体健康良好");
        yearlyHealthContent.add("注意定期检查");
        yearly.put("health", createFortuneItem("健康平稳", "预防为主", "#009688", yearlyHealthContent));
        yearly.put("marriage", createFortuneItem("感情深化", "家庭美满", "#F44336", "感情生活更加充实"));
        List<String> yearlyChildrenContent = new ArrayList<>();
        yearlyChildrenContent.add("子女发展良好");
        yearlyChildrenContent.add("给予适当引导");
        yearly.put("children", createFortuneItem("成长顺利", "天赋发展", "#673AB7", yearlyChildrenContent));
        detailedFortune.put("yearly", yearly);
        
        return detailedFortune;
    }

    /**
     * 创建建议项
     */
    private Map<String, Object> createAdviceItem(String label, String advice, String type) {
        Map<String, Object> item = new HashMap<>();
        item.put("label", label);
        item.put("advice", advice);
        item.put("type", type);
        return item;
    }

    /**
     * 创建运势项
     */
    private Map<String, Object> createFortuneItem(String status, String highlight, String color, Object content) {
        Map<String, Object> item = new HashMap<>();
        item.put("status", status);
        item.put("highlight", highlight);
        item.put("color", color);
        item.put("content", content);
        return item;
    }

    /**
     * 生成喜用五行
     */
    private List<String> generateFavorableElements(FortuneAnalysisRequest request) {
        List<String> favorable = new ArrayList<>();
        FortuneAnalysisRequest.BaziDetail.WuxingDistribution wuxing = request.getBaziDetail().getWuxingDistribution();
        
        // 简化逻辑：缺少的五行为喜用
        if (wuxing.getWood() == 0) favorable.add("木");
        if (wuxing.getFire() == 0) favorable.add("火");
        if (wuxing.getEarth() == 0) favorable.add("土");
        if (wuxing.getMetal() == 0) favorable.add("金");
        if (wuxing.getWater() == 0) favorable.add("水");
        
        // 如果没有缺失的，选择最少的两个
        if (favorable.isEmpty()) {
            favorable.add("木");
            favorable.add("水");
        }
        
        return favorable;
    }

    /**
     * 生成忌用五行
     */
    private List<String> generateUnfavorableElements(FortuneAnalysisRequest request) {
        List<String> unfavorable = new ArrayList<>();
        FortuneAnalysisRequest.BaziDetail.WuxingDistribution wuxing = request.getBaziDetail().getWuxingDistribution();
        
        // 简化逻辑：最多的五行为忌用
        int maxCount = Math.max(Math.max(wuxing.getWood(), wuxing.getFire()),
                               Math.max(Math.max(wuxing.getEarth(), wuxing.getMetal()), wuxing.getWater()));
        
        if (wuxing.getWood() == maxCount) unfavorable.add("木");
        if (wuxing.getFire() == maxCount) unfavorable.add("火");
        if (wuxing.getEarth() == maxCount) unfavorable.add("土");
        if (wuxing.getMetal() == maxCount) unfavorable.add("金");
        if (wuxing.getWater() == maxCount) unfavorable.add("水");
        
        return unfavorable;
    }

    /**
     * 生成宜用颜色
     */
    private List<String> generateFavorableColors(FortuneAnalysisRequest request) {
        List<String> colors = new ArrayList<>();
        List<String> favorableElements = generateFavorableElements(request);
        
        for (String element : favorableElements) {
            switch (element) {
                case "木" -> {
                    colors.add("绿色");
                    colors.add("青色");
                }
                case "火" -> {
                    colors.add("红色");
                    colors.add("紫色");
                }
                case "土" -> {
                    colors.add("黄色");
                    colors.add("棕色");
                }
                case "金" -> {
                    colors.add("白色");
                    colors.add("银色");
                }
                case "水" -> {
                    colors.add("蓝色");
                    colors.add("黑色");
                }
            }
        }
        
        return colors;
    }

    /**
     * 生成忌用颜色
     */
    private List<String> generateUnfavorableColors(FortuneAnalysisRequest request) {
        List<String> colors = new ArrayList<>();
        List<String> unfavorableElements = generateUnfavorableElements(request);
        
        for (String element : unfavorableElements) {
            switch (element) {
                case "木" -> {
                    colors.add("绿色");
                    colors.add("青色");
                }
                case "火" -> {
                    colors.add("红色");
                    colors.add("紫色");
                }
                case "土" -> {
                    colors.add("黄色");
                    colors.add("棕色");
                }
                case "金" -> {
                    colors.add("白色");
                    colors.add("银色");
                }
                case "水" -> {
                    colors.add("蓝色");
                    colors.add("黑色");
                }
            }
        }
        
        return colors;
    }

    /**
     * 生成五行总结文字
     */
    private String generateWuxingSummaryText(FortuneAnalysisRequest request) {
        List<String> favorable = generateFavorableElements(request);
        List<String> favorableColors = generateFavorableColors(request);
        List<String> unfavorable = generateUnfavorableElements(request);
        List<String> unfavorableColors = generateUnfavorableColors(request);
        
        StringBuilder summary = new StringBuilder();
        summary.append("喜用").append(String.join("、", favorable));
        summary.append("，宜着").append(String.join("、", favorableColors));
        
        if (!unfavorable.isEmpty() && !unfavorableColors.isEmpty()) {
            summary.append("，忌用").append(String.join("、", unfavorable));
            summary.append("，忌着").append(String.join("、", unfavorableColors));
        }
        
        return summary.toString();
    }

    /**
     * 计算年龄
     */
    private int calculateAge(LocalDate birthDate, LocalDate currentDate) {
        return Period.between(birthDate, currentDate).getYears();
    }

    /**
     * 生成默认运势数据（当AI解析失败时使用）
     */
    private Map<String, Object> generateDefaultFortuneData(FortuneAnalysisRequest request) {
        Map<String, Object> defaultData = new HashMap<>();
        
        // AI服务不生成五行分析，只生成运势数据
        
        // 默认五行总结
        Map<String, Object> wuxingSummary = new HashMap<>();
        wuxingSummary.put("favorableElements", generateFavorableElements(request));
        wuxingSummary.put("unfavorableElements", generateUnfavorableElements(request));
        wuxingSummary.put("favorableColors", generateFavorableColors(request));
        wuxingSummary.put("unfavorableColors", generateUnfavorableColors(request));
        wuxingSummary.put("summary", generateWuxingSummaryText(request));
        defaultData.put("wuxingSummary", wuxingSummary);
        
        // 默认整体运势
        Map<String, Object> overallFortune = new HashMap<>();
        overallFortune.put("lifetimeOverview", generateLifetimeOverview(request));
        overallFortune.put("decadeFortunes", generateDecadeFortuneData(request));
        overallFortune.put("currentDecade", (calculateAge(request.getUserInfo().getBirthDate(), request.getDate()) / 10) + 1);
        overallFortune.put("currentAge", calculateAge(request.getUserInfo().getBirthDate(), request.getDate()));
        overallFortune.put("lifePhase", getLifePhase(calculateAge(request.getUserInfo().getBirthDate(), request.getDate())));
        defaultData.put("overallFortune", overallFortune);
        
        // 默认吉运建议 - 如果AI完全失败才使用
        defaultData.put("luckyAdvice", generateDefaultLuckyAdvice(request));
        
        // 默认详细运势 - 如果AI完全失败才使用
        defaultData.put("detailedFortune", generateDefaultDetailedFortune(request));
        
        return defaultData;
    }
} 