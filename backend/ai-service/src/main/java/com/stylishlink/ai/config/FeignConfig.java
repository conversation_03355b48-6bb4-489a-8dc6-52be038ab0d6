package com.stylishlink.ai.config;

import feign.Logger;
import feign.Request;
import feign.Retryer;
import feign.codec.Encoder;
import feign.form.spring.SpringFormEncoder;
import org.springframework.beans.factory.ObjectFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.http.HttpMessageConverters;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.cloud.openfeign.support.SpringEncoder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.util.concurrent.TimeUnit;

/**
 * Feign配置类 - 支持文件上传
 */
@Configuration
@EnableFeignClients(basePackages = "com.stylishlink.ai.client")
public class FeignConfig {

    @Value("${feign.client.config.default.connectTimeout:10000}")
    private int connectTimeout;
    
    @Value("${feign.client.config.default.readTimeout:30000}")
    private int readTimeout;
    
    @Value("${feign.client.config.default.loggerLevel:BASIC}")
    private String loggerLevel;

    /**
     * 配置Feign日志级别
     */
    @Bean
    public Logger.Level feignLoggerLevel() {
        return Logger.Level.valueOf(loggerLevel.toUpperCase());
    }
    
    /**
     * 配置Feign请求超时 - 文件上传需要更长的超时时间
     */
    @Bean
    public Request.Options options() {
        return new Request.Options(
            connectTimeout, TimeUnit.MILLISECONDS,
            readTimeout, TimeUnit.MILLISECONDS,
            true
        );
    }
    
    /**
     * 配置Feign重试策略
     */
    @Bean
    public Retryer feignRetryer() {
        // 初始间隔100ms，最大间隔1s，最多重试2次 (文件上传重试次数适当减少)
        return new Retryer.Default(100, TimeUnit.SECONDS.toMillis(1), 2);
    }
    
    /**
     * 支持文件上传的Encoder
     */
    @Bean
    @Primary
    public Encoder feignFormEncoder(ObjectFactory<HttpMessageConverters> messageConverters) {
        return new SpringFormEncoder(new SpringEncoder(messageConverters));
    }
} 