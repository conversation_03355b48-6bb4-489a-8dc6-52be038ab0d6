package com.stylishlink.ai.client;

import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

/**
 * 文件服务Feign客户端回退工厂类
 * 当文件服务不可用时提供降级处理
 */
@Slf4j
@Component
public class FileServiceClientFallbackFactory implements FallbackFactory<FileServiceClient> {

    @Override
    public FileServiceClient create(Throwable cause) {
        log.error("文件服务调用失败，启用降级处理", cause);
        
        return new FileServiceClient() {
            @Override
            public FileUploadResponse uploadFile(MultipartFile file, String userId, String category, String accessLevel) {
                log.error("文件服务不可用，无法上传文件: {}", file.getOriginalFilename());
                
                // 创建明确的失败响应，不提供虚假URL
                FileUploadResponse response = new FileUploadResponse();
                response.setCode("503"); // 服务不可用
                response.setMessage("文件服务暂时不可用，无法上传文件");
                
                // 不设置data，表示上传失败
                response.setData(null);
                
                return response;
            }

            @Override
            public FileInfoResponse getFileInfo(String fileId) {
                log.warn("文件服务不可用，无法获取文件信息: {}", fileId);
                FileInfoResponse response = new FileInfoResponse();
                response.setCode("500");
                response.setMessage("文件服务暂时不可用");
                return response;
            }

            @Override
            public DownloadUrlResponse getDownloadUrl(String fileId, int expiresIn) {
                log.warn("文件服务不可用，无法获取下载链接: {}", fileId);
                DownloadUrlResponse response = new DownloadUrlResponse();
                response.setCode("500");
                response.setMessage("文件服务暂时不可用");
                return response;
            }

            @Override
            public DeleteFileResponse deleteFile(String fileId, boolean force) {
                log.warn("文件服务不可用，无法删除文件: {}", fileId);
                DeleteFileResponse response = new DeleteFileResponse();
                response.setCode("500");
                response.setMessage("文件服务暂时不可用");
                response.setData(false);
                return response;
            }
        };
    }
} 