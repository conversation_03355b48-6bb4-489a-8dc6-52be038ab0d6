package com.stylishlink.ai.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * AI对话记录实体
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ai_chat_record")
public class AiChatRecord {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private String userId;

    /**
     * 会话ID
     */
    @TableField("session_id")
    private String sessionId;

    /**
     * 消息类型(user/assistant)
     */
    @TableField("message_type")
    private String messageType;

    /**
     * 消息内容
     */
    @TableField("message")
    private String message;

    /**
     * 上下文信息
     */
    @TableField("context")
    private String context;

    /**
     * AI模型名称
     */
    @TableField("ai_model")
    private String aiModel;

    /**
     * 使用的token数量
     */
    @TableField("tokens_used")
    private Integer tokensUsed;

    /**
     * 响应时间(毫秒)
     */
    @TableField("response_time")
    private Integer responseTime;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;
} 