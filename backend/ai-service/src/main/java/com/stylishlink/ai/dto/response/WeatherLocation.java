package com.stylishlink.ai.dto.response;

import lombok.Data;

/**
 * 天气查询位置信息DTO
 */
@Data
public class WeatherLocation {

    /**
     * 位置ID
     */
    private String id;

    /**
     * 城市名称
     */
    private String name;

    /**
     * 国家代码
     */
    private String country;

    /**
     * 完整路径（如：深圳,深圳,广东,中国）
     */
    private String path;

    /**
     * 时区
     */
    private String timezone;

    /**
     * 时区偏移
     */
    private String timezoneOffset;

    /**
     * 省份
     */
    private String province;

    /**
     * 区域编码
     */
    private String adcode;
} 