package com.stylishlink.ai.client;

/**
 * AI客户端工厂接口
 * 提供统一的AI客户端获取方法，支持多种选择策略
 */
public interface AiClientFactory {
    
    /**
     * 获取默认AI客户端
     * 根据配置的默认策略选择
     */
    AiClient getDefaultClient();
    
    /**
     * 根据AI客户端类型获取客户端
     */
    AiClient getClient(AiClientType clientType);
    
    /**
     * 根据场景获取最适合的AI客户端
     * @param scenario 使用场景（如：chat, vision, style_analysis等）
     */
    AiClient getClientByScenario(String scenario);
    
    /**
     * 获取可用的AI客户端（带故障转移）
     * 如果首选客户端不可用，自动切换到备选客户端
     */
    AiClient getAvailableClient();
    
    /**
     * 获取视觉分析专用客户端
     */
    AiClient getVisionClient();
    
    /**
     * 检查指定类型的AI客户端是否可用
     */
    boolean isClientAvailable(AiClientType clientType);
    
    /**
     * 获取所有可用的AI客户端类型
     */
    java.util.List<AiClientType> getAvailableClientTypes();
} 