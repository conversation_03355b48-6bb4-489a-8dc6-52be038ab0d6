package com.stylishlink.ai.dto.request;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import java.util.Map;

/**
 * 衣物识别请求DTO（通过文件ID方式）
 */
@Data
public class ClothingRecognitionByFileIdRequest {

    /**
     * 文件ID
     */
    @NotBlank(message = "文件ID不能为空")
    private String fileId;

    /**
     * 用户ID（可选）
     */
    private String userId;

    /**
     * 元数据信息（可选）
     */
    private Map<String, Object> metadata;
} 