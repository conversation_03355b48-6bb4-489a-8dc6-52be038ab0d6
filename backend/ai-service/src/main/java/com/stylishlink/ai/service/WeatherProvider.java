package com.stylishlink.ai.service;

import com.stylishlink.ai.dto.request.WeatherRequest;
import com.stylishlink.ai.dto.response.WeatherResponse;

/**
 * 天气数据提供商策略接口
 */
public interface WeatherProvider {

    /**
     * 获取提供商名称
     * @return 提供商名称
     */
    String getProviderName();

    /**
     * 查询天气信息
     * @param request 天气查询请求
     * @return 天气响应
     */
    WeatherResponse getWeather(WeatherRequest request);

    /**
     * 检查提供商是否可用
     * @return 是否可用
     */
    boolean isAvailable();

    /**
     * 获取提供商优先级（数字越小优先级越高）
     * @return 优先级
     */
    int getPriority();
} 