package com.stylishlink.ai.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.stylishlink.ai.entity.AiChatRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * AI对话记录Mapper接口
 */
@Mapper
public interface AiChatRecordMapper extends BaseMapper<AiChatRecord> {

    /**
     * 根据用户ID和会话ID查询对话记录
     * @param userId 用户ID
     * @param sessionId 会话ID
     * @return 对话记录列表
     */
    List<AiChatRecord> selectByUserIdAndSessionId(@Param("userId") String userId, 
                                                 @Param("sessionId") String sessionId);

    /**
     * 根据用户ID查询对话记录
     * @param userId 用户ID
     * @return 对话记录列表
     */
    List<AiChatRecord> selectByUserId(@Param("userId") String userId);

    /**
     * 根据会话ID查询对话记录
     * @param sessionId 会话ID
     * @return 对话记录列表
     */
    List<AiChatRecord> selectBySessionId(@Param("sessionId") String sessionId);
} 