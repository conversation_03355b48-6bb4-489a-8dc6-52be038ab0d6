package com.stylishlink.ai.service;

import com.stylishlink.ai.dto.request.ChatRequest;
import com.stylishlink.ai.dto.response.ChatResponse;

/**
 * AI对话服务接口
 */
public interface AiChatService {

    /**
     * AI对话
     * @param request 对话请求
     * @return 对话响应
     */
    ChatResponse chat(ChatRequest request);

    /**
     * 获取时尚建议
     * @param request 对话请求
     * @return 时尚建议响应
     */
    ChatResponse getFashionAdvice(ChatRequest request);

    /**
     * 造型师咨询
     * @param request 对话请求
     * @return 咨询响应
     */
    ChatResponse getStylistConsultation(ChatRequest request);
} 