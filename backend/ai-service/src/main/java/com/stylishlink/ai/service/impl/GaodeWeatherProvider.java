package com.stylishlink.ai.service.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.stylishlink.ai.dto.request.WeatherRequest;
import com.stylishlink.ai.dto.response.WeatherData;
import com.stylishlink.ai.dto.response.WeatherLocation;
import com.stylishlink.ai.dto.response.WeatherResponse;
import com.stylishlink.ai.service.WeatherProvider;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.List;

/**
 * 高德天气数据提供商实现
 */
@Slf4j
@Service
public class GaodeWeatherProvider implements WeatherProvider {

    @Value("${weather.gaode.api-key:}")
    private String apiKey;

    @Value("${weather.gaode.base-url:https://restapi.amap.com/v3/weather}")
    private String baseUrl;

    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;

    public GaodeWeatherProvider(RestTemplate restTemplate, ObjectMapper objectMapper) {
        this.restTemplate = restTemplate;
        this.objectMapper = objectMapper;
    }

    @Override
    public String getProviderName() {
        return "gaode";
    }

    @Override
    public WeatherResponse getWeather(WeatherRequest request) {
        try {
            String city = buildCityParam(request);
            if (!StringUtils.hasText(city)) {
                return buildErrorResponse("城市参数不能为空");
            }

            String url = buildApiUrl(city, "forecast"); // 默认使用预报天气
            log.info("调用高德天气API: {}", url);

            String response = restTemplate.getForObject(url, String.class);
            return parseResponse(response);

        } catch (Exception e) {
            log.error("高德天气API调用失败", e);
            return buildErrorResponse("天气数据获取失败: " + e.getMessage());
        }
    }

    @Override
    public boolean isAvailable() {
        return StringUtils.hasText(apiKey);
    }

    @Override
    public int getPriority() {
        return 2; // 高德天气优先级较低
    }

    /**
     * 构建城市参数
     */
    private String buildCityParam(WeatherRequest request) {
        if (StringUtils.hasText(request.getCityName())) {
            return request.getCityName();
        }
        return null;
    }

    /**
     * 构建API URL
     */
    private String buildApiUrl(String city, String weatherType) {
        String extensions = "base".equals(weatherType) ? "base" : "all";
        return String.format("%s/weatherInfo?key=%s&city=%s&extensions=%s&output=JSON",
                baseUrl, apiKey, city, extensions);
    }

    /**
     * 解析高德天气API响应
     */
    private WeatherResponse parseResponse(String responseStr) {
        try {
            JsonNode root = objectMapper.readTree(responseStr);
            
            // 检查API调用是否成功
            String status = root.path("status").asText();
            if (!"1".equals(status)) {
                String info = root.path("info").asText();
                return buildErrorResponse("高德天气API调用失败: " + info);
            }

            JsonNode lives = root.get("lives");
            JsonNode forecasts = root.get("forecasts");

            WeatherResponse response = new WeatherResponse();
            response.setSuccess(true);
            response.setDataSource("高德天气");
            response.setTimestamp(System.currentTimeMillis());

            // 解析实况天气
            if (lives != null && lives.isArray() && lives.size() > 0) {
                JsonNode live = lives.get(0);
                
                // 位置信息
                WeatherLocation location = new WeatherLocation();
                location.setName(live.path("city").asText());
                location.setProvince(live.path("province").asText());
                location.setAdcode(live.path("adcode").asText());
                response.setLocation(location);

                // 当前天气
                WeatherData current = new WeatherData();
                current.setText(live.path("weather").asText());
                current.setTemperature(live.path("temperature").asText());
                current.setWindDirection(live.path("winddirection").asText());
                current.setWindPower(live.path("windpower").asText());
                current.setHumidity(live.path("humidity").asText());
                response.setCurrent(current);

                response.setLastUpdate(live.path("reporttime").asText());
            }

            // 解析预报天气
            if (forecasts != null && forecasts.isArray() && forecasts.size() > 0) {
                JsonNode forecast = forecasts.get(0);
                
                // 如果没有位置信息，从预报中获取
                if (response.getLocation() == null) {
                    WeatherLocation location = new WeatherLocation();
                    location.setName(forecast.path("city").asText());
                    location.setProvince(forecast.path("province").asText());
                    location.setAdcode(forecast.path("adcode").asText());
                    response.setLocation(location);
                }

                JsonNode casts = forecast.get("casts");
                if (casts != null && casts.isArray()) {
                    List<WeatherData> forecastList = new ArrayList<>();
                    for (JsonNode cast : casts) {
                        WeatherData weatherData = new WeatherData();
                        weatherData.setDate(cast.path("date").asText());
                        weatherData.setWeek(cast.path("week").asText());
                        weatherData.setDayWeather(cast.path("dayweather").asText());
                        weatherData.setNightWeather(cast.path("nightweather").asText());
                        weatherData.setDayTemperature(cast.path("daytemp").asText());
                        weatherData.setNightTemperature(cast.path("nighttemp").asText());
                        weatherData.setWindDirection(cast.path("daywind").asText());
                        weatherData.setWindPower(cast.path("daypower").asText());
                        forecastList.add(weatherData);
                    }
                    response.setForecast(forecastList);
                }

                response.setLastUpdate(forecast.path("reporttime").asText());
            }

            return response;

        } catch (Exception e) {
            log.error("解析高德天气API响应失败", e);
            return buildErrorResponse("天气数据解析失败: " + e.getMessage());
        }
    }

    /**
     * 构建错误响应
     */
    private WeatherResponse buildErrorResponse(String errorMessage) {
        WeatherResponse response = new WeatherResponse();
        response.setSuccess(false);
        response.setErrorMessage(errorMessage);
        response.setDataSource("高德天气");
        response.setTimestamp(System.currentTimeMillis());
        return response;
    }
} 