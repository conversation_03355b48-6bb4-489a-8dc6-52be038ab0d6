package com.stylishlink.ai.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.stylishlink.ai.client.AiClient;
import com.stylishlink.ai.client.AiClientFactory;
import com.stylishlink.ai.dto.request.BodyShapeAnalysisRequest;
import com.stylishlink.ai.dto.response.BodyShapeAnalysisResponse;
import com.stylishlink.ai.entity.BodyShapeAnalysisResult;
import com.stylishlink.ai.mapper.BodyShapeAnalysisResultMapper;
import com.stylishlink.ai.service.BodyShapeAnalysisService;
import com.stylishlink.ai.service.FileUploadService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 身材识别服务实现
 */
@Slf4j
@Service
public class BodyShapeAnalysisServiceImpl implements BodyShapeAnalysisService {

    @Autowired
    private AiClientFactory aiClientFactory;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private BodyShapeAnalysisResultMapper bodyShapeAnalysisResultMapper;

    @Autowired
    private FileUploadService fileUploadService;

    /**
     * AI响应兼容性数据提取器
     * 处理各种可能的AI输出格式，提供统一的数据提取接口
     */
    public static class CompatibleDataExtractor {
        
        /**
         * 提取身材数据，兼容嵌套和扁平化结构
         */
        public static BodyShapeAnalysisResponse.BodyShape extractBodyShape(Map<String, Object> parsedResponse) {
            log.debug("开始提取身材数据");
            
            // 方案1：标准嵌套结构 - bodyShape对象
            Map<String, Object> bodyShapeMap = (Map<String, Object>) parsedResponse.get("bodyShape");
            if (bodyShapeMap != null && !bodyShapeMap.isEmpty()) {
                log.debug("使用标准嵌套bodyShape结构");
                return buildBodyShapeFromMap(bodyShapeMap);
            }
            
            // 方案2：扁平化结构 - 直接从根级别读取
            if (hasBodyShapeFields(parsedResponse)) {
                log.debug("使用扁平化身材数据结构");
                return buildBodyShapeFromMap(parsedResponse);
            }
            
            log.warn("未找到有效的身材数据结构");
            return createDefaultBodyShape();
        }
        
        /**
         * 提取身材分析结果，兼容数组和对象格式
         */
        public static List<BodyShapeAnalysisResponse.BodyAnalysis> extractAnalysis(Map<String, Object> parsedResponse) {
            log.debug("开始提取身材分析结果");
            List<BodyShapeAnalysisResponse.BodyAnalysis> result = new ArrayList<>();
            
            // 方案1：标准数组格式 - analysis数组
            List<Map<String, Object>> analysisList = (List<Map<String, Object>>) parsedResponse.get("analysis");
            if (analysisList != null && !analysisList.isEmpty()) {
                log.debug("找到标准格式的analysis数组，包含{}项", analysisList.size());
                for (Map<String, Object> analysisMap : analysisList) {
                    BodyShapeAnalysisResponse.BodyAnalysis analysis = new BodyShapeAnalysisResponse.BodyAnalysis();
                    analysis.setFeature(getStringValue(analysisMap, "feature", ""));
                    analysis.setDescription(getStringValue(analysisMap, "description", ""));
                    analysis.setType(getStringValue(analysisMap, "type", "neutral"));
                    result.add(analysis);
                }
                return result;
            }
            
            // 方案2：从suggestions对象中提取分析内容
            Map<String, Object> suggestionsMap = (Map<String, Object>) parsedResponse.get("suggestions");
            if (suggestionsMap != null) {
                log.debug("suggestions Map包含的字段: {}", suggestionsMap.keySet());
                
                // 检查各种可能的字段名
                String analysisContent = "";
                if (suggestionsMap.containsKey("analysis")) {
                    analysisContent = getStringValue(suggestionsMap, "analysis", "");
                    log.debug("从suggestions.analysis字段获取内容，长度: {}", analysisContent.length());
                } else if (suggestionsMap.containsKey("bodyAnalysis")) {
                    analysisContent = getStringValue(suggestionsMap, "bodyAnalysis", "");
                    log.debug("从suggestions.bodyAnalysis字段获取内容，长度: {}", analysisContent.length());
                }
                
                if (!analysisContent.isEmpty()) {
                    BodyShapeAnalysisResponse.BodyAnalysis analysis = new BodyShapeAnalysisResponse.BodyAnalysis();
                    analysis.setFeature("整体身材");
                    analysis.setDescription(analysisContent);
                    analysis.setType("positive");
                    result.add(analysis);
                    log.debug("成功从suggestions字段创建身材分析");
                }
            }
            
            log.debug("解析身材分析结果完成，共{}项", result.size());
            return result;
        }
        
        /**
         * 提取穿搭建议，兼容数组和对象格式
         */
        public static List<BodyShapeAnalysisResponse.StyleSuggestion> extractSuggestions(Map<String, Object> parsedResponse) {
            log.debug("开始提取穿搭建议");
            List<BodyShapeAnalysisResponse.StyleSuggestion> result = new ArrayList<>();
            
            Object suggestionsObj = parsedResponse.get("suggestions");
            if (suggestionsObj == null) {
                log.debug("未找到suggestions字段");
                return result;
            }
            
            try {
                // 方案1：标准数组格式 - suggestions数组
                if (suggestionsObj instanceof List) {
                    List<Map<String, Object>> suggestionsList = (List<Map<String, Object>>) suggestionsObj;
                    for (Map<String, Object> suggestionMap : suggestionsList) {
                        BodyShapeAnalysisResponse.StyleSuggestion suggestion = new BodyShapeAnalysisResponse.StyleSuggestion();
                        suggestion.setCategory(getStringValue(suggestionMap, "category", ""));
                        suggestion.setContent(getStringValue(suggestionMap, "content", ""));
                        suggestion.setPriority(getStringValue(suggestionMap, "priority", "medium"));
                        result.add(suggestion);
                    }
                    log.debug("成功解析List格式suggestions，包含{}条建议", result.size());
                    return result;
                }
                
                // 方案2：对象格式 - suggestions对象，包含多个字段
                if (suggestionsObj instanceof Map) {
                    Map<String, Object> suggestionsMap = (Map<String, Object>) suggestionsObj;
                    log.debug("suggestions Map包含的字段: {}", suggestionsMap.keySet());
                    
                    // 定义多组可能的字段名组合
                    String[][] fieldCombinations = {
                        {"analysis", "dressing"},           // 最新格式
                        {"bodyAnalysis", "dressSuggestions"}, // 备用格式1
                        {"bodyAnalysis", "dressing"},       // 混合格式1
                        {"analysis", "dressSuggestions"}    // 混合格式2
                    };
                    
                    for (String[] combination : fieldCombinations) {
                        String analysisField = combination[0];
                        String dressingField = combination[1];
                        
                        String analysisContent = getStringValue(suggestionsMap, analysisField, "");
                        String dressingContent = getStringValue(suggestionsMap, dressingField, "");
                        
                        if (!analysisContent.isEmpty() || !dressingContent.isEmpty()) {
                            log.debug("使用字段组合: {} + {}", analysisField, dressingField);
                            
                            // 添加身材分析建议
                            if (!analysisContent.isEmpty()) {
                                BodyShapeAnalysisResponse.StyleSuggestion analysisSuggestion = new BodyShapeAnalysisResponse.StyleSuggestion();
                                analysisSuggestion.setCategory("身材分析");
                                analysisSuggestion.setContent(analysisContent);
                                analysisSuggestion.setPriority("high");
                                result.add(analysisSuggestion);
                                log.debug("添加身材分析建议: {}", analysisContent.substring(0, Math.min(50, analysisContent.length())));
                            }
                            
                            // 添加穿搭建议
                            if (!dressingContent.isEmpty()) {
                                BodyShapeAnalysisResponse.StyleSuggestion dressSuggestion = new BodyShapeAnalysisResponse.StyleSuggestion();
                                dressSuggestion.setCategory("穿搭建议");
                                dressSuggestion.setContent(dressingContent);
                                dressSuggestion.setPriority("high");
                                result.add(dressSuggestion);
                                log.debug("添加穿搭建议: {}", dressingContent.substring(0, Math.min(50, dressingContent.length())));
                            }
                            
                            break; // 找到有效组合后退出循环
                        }
                    }
                    
                    log.debug("成功解析Map格式suggestions，包含{}条建议", result.size());
                    return result;
                }
                
            } catch (Exception e) {
                log.warn("解析suggestions时发生异常: {}", e.getMessage(), e);
            }
            
            // 方案3：从根级别获取通用建议
            String generalSuggestion = getStringValue(parsedResponse, "suggestion", "");
            if (!generalSuggestion.isEmpty()) {
                BodyShapeAnalysisResponse.StyleSuggestion suggestion = new BodyShapeAnalysisResponse.StyleSuggestion();
                suggestion.setCategory("通用建议");
                suggestion.setContent(generalSuggestion);
                suggestion.setPriority("medium");
                result.add(suggestion);
                log.debug("使用通用建议字段");
            }
            
            log.debug("解析穿搭建议完成，共{}条", result.size());
            return result;
        }
        
        // 辅助方法
        private static boolean hasBodyShapeFields(Map<String, Object> map) {
            String[] bodyShapeFields = {
                "shoulderWidth", "waistShape", "belly", "hip", "hipShape", 
                "armLength", "armCircum", "hipWidth", "thigh", "calf", "bodyFat", "bodyLength"
            };
            
            for (String field : bodyShapeFields) {
                if (map.containsKey(field)) {
                    return true;
                }
            }
            return false;
        }
        
        private static BodyShapeAnalysisResponse.BodyShape buildBodyShapeFromMap(Map<String, Object> map) {
            BodyShapeAnalysisResponse.BodyShape bodyShape = new BodyShapeAnalysisResponse.BodyShape();
            bodyShape.setShoulderWidth(getIntegerValue(map, "shoulderWidth", 3));
            bodyShape.setWaistShape(getIntegerValue(map, "waistShape", 3));
            bodyShape.setBelly(getIntegerValue(map, "belly", 1));
            bodyShape.setHip(getIntegerValue(map, "hip", 3));
            bodyShape.setHipShape(getIntegerValue(map, "hipShape", 3));
            bodyShape.setArmLength(getIntegerValue(map, "armLength", 3));
            bodyShape.setArmCircum(getIntegerValue(map, "armCircum", 3));
            bodyShape.setHipWidth(getIntegerValue(map, "hipWidth", 3));
            bodyShape.setThigh(getIntegerValue(map, "thigh", 3));
            bodyShape.setCalf(getIntegerValue(map, "calf", 3));
            bodyShape.setBodyFat(getIntegerValue(map, "bodyFat", 3));
            bodyShape.setBodyLength(getIntegerValue(map, "bodyLength", 3));
            return bodyShape;
        }
        
        private static BodyShapeAnalysisResponse.BodyShape createDefaultBodyShape() {
            BodyShapeAnalysisResponse.BodyShape bodyShape = new BodyShapeAnalysisResponse.BodyShape();
            bodyShape.setShoulderWidth(3);
            bodyShape.setWaistShape(3);
            bodyShape.setBelly(1);
            bodyShape.setHip(3);
            bodyShape.setHipShape(3);
            bodyShape.setArmLength(3);
            bodyShape.setArmCircum(3);
            bodyShape.setHipWidth(3);
            bodyShape.setThigh(3);
            bodyShape.setCalf(3);
            bodyShape.setBodyFat(3);
            bodyShape.setBodyLength(3);
            return bodyShape;
        }
        
        private static String getStringValue(Map<String, Object> map, String key, String defaultValue) {
            Object value = map.get(key);
            return value != null ? value.toString() : defaultValue;
        }
        
        private static Integer getIntegerValue(Map<String, Object> map, String key, Integer defaultValue) {
            Object value = map.get(key);
            if (value instanceof Number) {
                return ((Number) value).intValue();
            } else if (value instanceof String) {
                try {
                    return Integer.parseInt((String) value);
                } catch (NumberFormatException e) {
                    log.warn("无法解析整数值: {}", value);
                }
            }
            return defaultValue;
        }
    }

    @Override
    public BodyShapeAnalysisResponse analyzeBodyShape(BodyShapeAnalysisRequest request) {
        try {
            log.info("开始身材识别，用户: {}", request.getUserId());

            // 获取视觉专用AI客户端（豆包）进行身材识别
            AiClient visionClient = aiClientFactory.getVisionClient();
            
            // 调用AI服务进行身材识别
            Map<String, Object> aiResult = visionClient.analyzeBodyShape(request.getFile());
            
            log.info("AI身材识别服务返回结果: {}", aiResult);

            // 解析AI响应
            BodyShapeAnalysisResponse response = parseAiResponse(aiResult);
            
            // 保存识别结果到数据库
            try {
                saveAnalysisResult(request, response, aiResult);
                log.info("身材识别结果已保存到数据库");
            } catch (Exception dbException) {
                log.error("保存身材识别结果到数据库失败", dbException);
                // 数据库失败不影响识别结果返回
            }
            
            log.info("身材识别完成，用户: {}, 是否全身照: {}", 
                    request.getUserId(), response.isFullBodyPhoto());
            
            return response;

        } catch (Exception e) {
            log.error("身材识别失败，用户: {}", request.getUserId(), e);
            throw new RuntimeException("身材识别失败: " + e.getMessage());
        }
    }

    @Override
    public BodyShapeAnalysisResponse analyzeBodyShapeByUrl(BodyShapeAnalysisRequest request) {
        try {
            log.info("开始身材识别分析（URL方式），图片URL: {}, 用户: {}", request.getImageUrl(), request.getUserId());

            // 调用AI客户端进行身材识别（URL方式）
            Map<String, Object> aiResult = aiClientFactory.getVisionClient().analyzeBodyShapeByUrl(request.getImageUrl());
            log.info("AI身材识别完成（URL方式）: {}", aiResult);

            // 解析AI响应
            BodyShapeAnalysisResponse response = parseAiResponse(aiResult);
            
            // 如果识别成功且为正面全身照，保存结果到数据库
            if (response.isFullBodyPhoto() && request.getUserId() != null) {
                saveAnalysisResult(request, response, aiResult);
            }

            return response;
            
        } catch (Exception e) {
            log.error("身材识别失败（URL方式）", e);
            throw new RuntimeException("身材识别失败: " + e.getMessage());
        }
    }

    @Override
    public BodyShapeAnalysisResponse analyzeBodyShapeByFileId(BodyShapeAnalysisRequest request) {
        try {
            log.info("开始身材识别分析（文件ID方式），文件ID: {}, 用户: {}", request.getFileId(), request.getUserId());

            // 1. 根据文件ID获取图片URL
            String imageUrl = fileUploadService.getImageUrlById(request.getFileId());
            log.info("从文件ID {} 获取到图片URL: {}", request.getFileId(), imageUrl);

            // 2. 设置图片URL到请求对象中（用于后续保存数据库）
            request.setImageUrl(imageUrl);

            // 3. 调用AI客户端进行身材识别
            Map<String, Object> aiResult = aiClientFactory.getVisionClient().analyzeBodyShapeByUrl(imageUrl);
            log.info("AI身材识别完成（文件ID方式）: {}", aiResult);

            // 4. 解析AI响应
            BodyShapeAnalysisResponse response = parseAiResponse(aiResult);
            
            // 5. 如果识别成功且为正面全身照，保存结果到数据库
            if (response.isFullBodyPhoto() && request.getUserId() != null) {
                saveAnalysisResult(request, response, aiResult);
            }

            return response;
            
        } catch (Exception e) {
            log.error("身材识别失败（文件ID方式）", e);
            throw new RuntimeException("身材识别失败: " + e.getMessage());
        }
    }

    /**
     * 保存识别结果到数据库
     */
    private void saveAnalysisResult(BodyShapeAnalysisRequest request, 
                                   BodyShapeAnalysisResponse response, 
                                   Map<String, Object> aiResult) {
        try {
            BodyShapeAnalysisResult entity = new BodyShapeAnalysisResult();
            entity.setUserId(request.getUserId());
            
            // 获取图片URL - 优先使用请求中的URL（用于URL方式），其次使用AI结果中的URL（用于文件上传方式）
            String imageUrl = request.getImageUrl() != null ? 
                request.getImageUrl() : (String) aiResult.get("imageUrl");
            entity.setImageUrl(imageUrl);
            
            // 设置基本字段
            entity.setIsFullBodyPhoto(response.isFullBodyPhoto());
            entity.setConfidence(response.getConfidence());
            entity.setBodyType(response.getBodyType());
            entity.setReason(response.getReason());
            
            // 转换复杂对象为Map格式存储
            if (response.getBodyShape() != null) {
                entity.setBodyShape(convertBodyShapeToMap(response.getBodyShape()));
            }
            
            if (response.getAnalysis() != null) {
                entity.setAnalysis(convertAnalysisToMapList(response.getAnalysis()));
            }
            
            if (response.getSuggestions() != null) {
                entity.setSuggestions(convertSuggestionsToMapList(response.getSuggestions()));
            }
            
            // 保存原始AI响应
            try {
                entity.setRawResult(objectMapper.writeValueAsString(aiResult));
            } catch (JsonProcessingException e) {
                log.warn("序列化AI响应结果失败", e);
                entity.setRawResult(aiResult.toString());
            }
            
            entity.setCreatedAt(LocalDateTime.now());
            entity.setUpdatedAt(LocalDateTime.now());
            
            int insertResult = bodyShapeAnalysisResultMapper.insert(entity);
            log.debug("数据库插入结果: {}, 生成的ID: {}", insertResult, entity.getId());
            
        } catch (Exception e) {
            log.error("保存身材识别结果失败", e);
            throw e;
        }
    }

    /**
     * 转换BodyShape为Map
     */
    private Map<String, Object> convertBodyShapeToMap(BodyShapeAnalysisResponse.BodyShape bodyShape) {
        Map<String, Object> map = new HashMap<>();
        map.put("shoulderWidth", bodyShape.getShoulderWidth());
        map.put("waistShape", bodyShape.getWaistShape());
        map.put("belly", bodyShape.getBelly());
        map.put("hip", bodyShape.getHip());
        map.put("hipShape", bodyShape.getHipShape());
        map.put("armLength", bodyShape.getArmLength());
        map.put("armCircum", bodyShape.getArmCircum());
        map.put("hipWidth", bodyShape.getHipWidth());
        map.put("thigh", bodyShape.getThigh());
        map.put("calf", bodyShape.getCalf());
        map.put("bodyFat", bodyShape.getBodyFat());
        map.put("bodyLength", bodyShape.getBodyLength());
        return map;
    }

    /**
     * 转换Analysis列表为Map列表
     */
    private List<Map<String, Object>> convertAnalysisToMapList(List<BodyShapeAnalysisResponse.BodyAnalysis> analysisList) {
        List<Map<String, Object>> mapList = new ArrayList<>();
        for (BodyShapeAnalysisResponse.BodyAnalysis analysis : analysisList) {
            Map<String, Object> map = new HashMap<>();
            map.put("feature", analysis.getFeature());
            map.put("description", analysis.getDescription());
            map.put("type", analysis.getType());
            mapList.add(map);
        }
        return mapList;
    }

    /**
     * 转换Suggestions列表为Map列表
     */
    private List<Map<String, Object>> convertSuggestionsToMapList(List<BodyShapeAnalysisResponse.StyleSuggestion> suggestionsList) {
        List<Map<String, Object>> mapList = new ArrayList<>();
        for (BodyShapeAnalysisResponse.StyleSuggestion suggestion : suggestionsList) {
            Map<String, Object> map = new HashMap<>();
            map.put("category", suggestion.getCategory());
            map.put("content", suggestion.getContent());
            map.put("priority", suggestion.getPriority());
            mapList.add(map);
        }
        return mapList;
    }

    /**
     * 解析AI响应结果
     */
    private BodyShapeAnalysisResponse parseAiResponse(Map<String, Object> aiResult) {
        try {
            // 获取AI响应内容
            String aiResponseContent = (String) aiResult.get("aiResponse");
            if (aiResponseContent == null || aiResponseContent.trim().isEmpty()) {
                throw new RuntimeException("AI响应内容为空");
            }

            log.debug("解析AI响应内容: {}", aiResponseContent);

            // 尝试从AI响应中提取JSON
            String jsonContent = extractJsonFromResponse(aiResponseContent);
            
            // 解析JSON为Map
            Map<String, Object> parsedResponse = objectMapper.readValue(jsonContent, Map.class);
            
            // 构建响应对象
            BodyShapeAnalysisResponse response = new BodyShapeAnalysisResponse();
            
            // 添加调试日志 - 记录AI原始返回值
            Object rawIsFullBodyPhoto = parsedResponse.get("isFullBodyPhoto");
            Object rawIsFullBodyFrontal = parsedResponse.get("isFullBodyFrontal");
            log.debug("AI原始返回 - isFullBodyPhoto: {}, isFullBodyFrontal: {}", 
                    rawIsFullBodyPhoto, rawIsFullBodyFrontal);
            
            // 解析基本字段 - 适配AI实际返回的字段名
            // AI返回 "isFullBodyFrontal" 而不是 "isFullBodyPhoto"
            boolean isFullBodyPhoto = getBooleanValue(parsedResponse, "isFullBodyFrontal", false) || 
                                     getBooleanValue(parsedResponse, "isFullBodyPhoto", false);
            log.debug("解析后isFullBodyPhoto: {}", isFullBodyPhoto);
            response.setFullBodyPhoto(isFullBodyPhoto);
            
            response.setConfidence(getDoubleValue(parsedResponse, "confidence", 0.0));
            response.setBodyType(getStringValue(parsedResponse, "bodyType", null));
            response.setReason(getStringValue(parsedResponse, "reason", null));
            
            // 如果是全身照，解析详细身材数据
            if (response.isFullBodyPhoto()) {
                response.setBodyShape(CompatibleDataExtractor.extractBodyShape(parsedResponse));
                response.setAnalysis(CompatibleDataExtractor.extractAnalysis(parsedResponse));
                response.setSuggestions(CompatibleDataExtractor.extractSuggestions(parsedResponse));
            }
            
            return response;

        } catch (Exception e) {
            log.error("解析AI响应失败", e);
            throw new RuntimeException("解析AI响应失败: " + e.getMessage());
        }
    }

    /**
     * 从AI响应中提取JSON内容
     */
    private String extractJsonFromResponse(String aiResponse) {
        // 查找JSON开始和结束位置
        int jsonStart = aiResponse.indexOf("{");
        int jsonEnd = aiResponse.lastIndexOf("}");
        
        if (jsonStart == -1 || jsonEnd == -1 || jsonStart >= jsonEnd) {
            throw new RuntimeException("AI响应中未找到有效的JSON格式");
        }
        
        return aiResponse.substring(jsonStart, jsonEnd + 1);
    }

    // 辅助方法
    private String getStringValue(Map<String, Object> map, String key, String defaultValue) {
        Object value = map.get(key);
        return value != null ? value.toString() : defaultValue;
    }

    private Integer getIntegerValue(Map<String, Object> map, String key, Integer defaultValue) {
        Object value = map.get(key);
        if (value instanceof Number) {
            return ((Number) value).intValue();
        } else if (value instanceof String) {
            try {
                return Integer.parseInt((String) value);
            } catch (NumberFormatException e) {
                log.warn("无法解析整数值: {}", value);
            }
        }
        return defaultValue;
    }

    private Double getDoubleValue(Map<String, Object> map, String key, Double defaultValue) {
        Object value = map.get(key);
        if (value instanceof Number) {
            return ((Number) value).doubleValue();
        } else if (value instanceof String) {
            try {
                return Double.parseDouble((String) value);
            } catch (NumberFormatException e) {
                log.warn("无法解析浮点数值: {}", value);
            }
        }
        return defaultValue;
    }

    private Boolean getBooleanValue(Map<String, Object> map, String key, Boolean defaultValue) {
        Object value = map.get(key);
        if (value instanceof Boolean) {
            return (Boolean) value;
        } else if (value instanceof String) {
            return "true".equalsIgnoreCase((String) value);
        }
        return defaultValue;
    }
} 