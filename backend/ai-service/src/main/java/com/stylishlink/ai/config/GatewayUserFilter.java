package com.stylishlink.ai.config;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.NonNull;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.util.Collections;

/**
 * 网关用户信息过滤器
 * 
 * 从网关传递的header中读取用户信息，设置到SecurityContext中
 */
@Component
@Slf4j
public class GatewayUserFilter extends OncePerRequestFilter {
    
    private static final String USER_ID_HEADER = "X-User-Id";
    private static final String USER_NICKNAME_HEADER = "X-User-Nickname";
    private static final String USER_PHONE_HEADER = "X-User-Phone";
    private static final String USER_ROLES_HEADER = "X-User-Roles";
    
    @Override
    protected void doFilterInternal(
            @NonNull HttpServletRequest request,
            @NonNull HttpServletResponse response,
            @NonNull FilterChain filterChain) throws ServletException, IOException {
        
        // 从网关传递的header中获取用户信息
        String userId = request.getHeader(USER_ID_HEADER);
        String userNickname = request.getHeader(USER_NICKNAME_HEADER);
        String userPhone = request.getHeader(USER_PHONE_HEADER);
        String userRoles = request.getHeader(USER_ROLES_HEADER);
        
        log.debug("处理请求: {} {}, 网关用户信息 - User-Id: {}, Nickname: {}, Phone: {}, Roles: {}", 
                request.getMethod(), request.getRequestURI(), userId, userNickname, userPhone, userRoles);
        
        try {
            // 如果有用户ID且SecurityContext中还没有认证信息
            if (StringUtils.hasText(userId) && SecurityContextHolder.getContext().getAuthentication() == null) {
                
                // 创建认证对象
                UsernamePasswordAuthenticationToken authentication = 
                    new UsernamePasswordAuthenticationToken(
                        userId,  // principal - 使用用户ID作为主体
                        null,    // credentials - 网关已验证，无需密码
                        Collections.emptyList()  // authorities - 可以根据需要添加角色权限
                    );
                
                // 设置请求详情
                authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                
                // 设置到SecurityContext
                SecurityContextHolder.getContext().setAuthentication(authentication);
                
                log.debug("网关用户认证成功，用户ID: {}, 昵称: {}, 角色: {}", userId, userNickname, userRoles);
            } else if (!StringUtils.hasText(userId)) {
                log.debug("请求缺少网关用户认证header，路径: {}", request.getRequestURI());
            }
            
        } catch (Exception e) {
            // 捕获所有异常，不影响请求处理
            log.warn("网关用户信息处理过程中发生异常: {}", e.getMessage(), e);
        }
        
        // 继续执行过滤器链
        filterChain.doFilter(request, response);
    }
} 