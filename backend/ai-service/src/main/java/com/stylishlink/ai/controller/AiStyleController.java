package com.stylishlink.ai.controller;

import com.stylishlink.ai.dto.request.StyleAnalysisRequest;
import com.stylishlink.ai.dto.response.StyleAnalysisResponse;
import com.stylishlink.ai.service.AiStyleAnalysisService;
import com.stylishlink.common.dto.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 风格分析控制器
 */
@Slf4j
@RestController
@RequestMapping("/style")
public class AiStyleController {

    @Autowired
    private AiStyleAnalysisService styleAnalysisService;

    /**
     * 风格分析接口
     */
    @PostMapping
    public ApiResponse<StyleAnalysisResponse> analyzeStyle(@Valid @RequestBody StyleAnalysisRequest request) {
        try {
            StyleAnalysisResponse response;
            if ("image".equals(request.getSourceType())) {
                response = styleAnalysisService.analyzeImageStyle(request);
            } else if ("outfit".equals(request.getSourceType())) {
                response = styleAnalysisService.analyzeOutfitStyle(request);
            } else {
                return ApiResponse.error(1001, "不支持的来源类型: " + request.getSourceType());
            }
            return ApiResponse.success("风格分析成功", response);
        } catch (Exception e) {
            log.error("风格分析失败", e);
            return ApiResponse.error(6003, "风格分析失败: " + e.getMessage());
        }
    }

    /**
     * 色彩分析接口
     */
    @PostMapping("/color")
    public ApiResponse<StyleAnalysisResponse> analyzeColor(@Valid @RequestBody StyleAnalysisRequest request) {
        try {
            StyleAnalysisResponse response = styleAnalysisService.analyzeColor(request);
            return ApiResponse.success("色彩分析成功", response);
        } catch (Exception e) {
            log.error("色彩分析失败", e);
            return ApiResponse.error(6003, "色彩分析失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID获取风格分析结果
     */
    @GetMapping("/{analysisId}")
    public ApiResponse<StyleAnalysisResponse> getAnalysisById(@PathVariable Long analysisId) {
        try {
            StyleAnalysisResponse response = styleAnalysisService.getAnalysisById(analysisId);
            return ApiResponse.success("获取风格分析结果成功", response);
        } catch (Exception e) {
            log.error("获取风格分析结果失败", e);
            return ApiResponse.error(6003, "获取风格分析结果失败: " + e.getMessage());
        }
    }

    /**
     * 根据用户ID获取风格分析历史
     */
    @GetMapping("/user/{userId}")
    public ApiResponse<List<StyleAnalysisResponse>> getAnalysisByUserId(@PathVariable String userId) {
        try {
            List<StyleAnalysisResponse> responses = styleAnalysisService.getAnalysisByUserId(userId);
            return ApiResponse.success("获取用户风格分析历史成功", responses);
        } catch (Exception e) {
            log.error("获取用户风格分析历史失败", e);
            return ApiResponse.error(6003, "获取用户风格分析历史失败: " + e.getMessage());
        }
    }
} 