package com.stylishlink.ai.service;

import com.stylishlink.ai.dto.request.ClothingRecognitionByFileIdRequest;
import com.stylishlink.ai.dto.request.RecognizeRequest;
import com.stylishlink.ai.dto.response.ClothingRecognitionResponse;
import com.stylishlink.ai.dto.response.RecognitionResponse;

/**
 * 图像识别服务接口
 */
public interface AiRecognitionService {

    /**
     * 识别衣物
     * @param request 识别请求
     * @return 识别结果
     */
    RecognitionResponse recognizeClothing(RecognizeRequest request);

    /**
     * 识别饰品
     * @param request 识别请求
     * @return 识别结果
     */
    RecognitionResponse recognizeAccessory(RecognizeRequest request);

    /**
     * 通用识别方法
     * @param request 识别请求
     * @return 识别结果
     */
    RecognitionResponse recognize(RecognizeRequest request);

    /**
     * 通过文件ID识别衣物（对应API文档3.2.1）
     * @param request 衣物识别请求
     * @return 衣物识别结果
     */
    ClothingRecognitionResponse recognizeClothingByFileId(ClothingRecognitionByFileIdRequest request);
} 