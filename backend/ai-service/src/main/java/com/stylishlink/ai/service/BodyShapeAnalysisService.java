package com.stylishlink.ai.service;

import com.stylishlink.ai.dto.request.BodyShapeAnalysisRequest;
import com.stylishlink.ai.dto.response.BodyShapeAnalysisResponse;

/**
 * 身材识别分析服务接口
 */
public interface BodyShapeAnalysisService {

    /**
     * 身材识别分析（文件上传方式）
     * @param request 身材识别分析请求
     * @return 身材识别分析结果
     */
    BodyShapeAnalysisResponse analyzeBodyShape(BodyShapeAnalysisRequest request);

    /**
     * 身材识别分析（图片URL方式）
     * @param request 身材识别分析请求（包含图片URL）
     * @return 身材识别分析结果
     */
    BodyShapeAnalysisResponse analyzeBodyShapeByUrl(BodyShapeAnalysisRequest request);

    /**
     * 身材识别分析（文件ID方式）
     * @param request 身材识别分析请求（包含文件ID）
     * @return 身材识别分析结果
     */
    BodyShapeAnalysisResponse analyzeBodyShapeByFileId(BodyShapeAnalysisRequest request);
} 