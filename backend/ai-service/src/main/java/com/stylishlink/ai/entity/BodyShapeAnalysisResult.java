package com.stylishlink.ai.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 身材识别结果实体
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ai_body_shape_analysis_result")
public class BodyShapeAnalysisResult {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private String userId;

    /**
     * 图片URL
     */
    @TableField("image_url")
    private String imageUrl;

    /**
     * 是否为正面全身照
     */
    @TableField("is_full_body_photo")
    private Boolean isFullBodyPhoto;

    /**
     * 识别置信度（0-100）
     */
    @TableField("confidence")
    private Double confidence;

    /**
     * 推荐体型分类（slim/normal/chubby）
     */
    @TableField("body_type")
    private String bodyType;

    /**
     * 详细身材数据（JSON格式存储）
     */
    @TableField(value = "body_shape", typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> bodyShape;

    /**
     * 身材分析结果（JSON格式存储）
     */
    @TableField(value = "analysis", typeHandler = JacksonTypeHandler.class)
    private List<Map<String, Object>> analysis;

    /**
     * 穿搭建议（JSON格式存储）
     */
    @TableField(value = "suggestions", typeHandler = JacksonTypeHandler.class)
    private List<Map<String, Object>> suggestions;

    /**
     * 失败原因（仅当isFullBodyPhoto为false时）
     */
    @TableField("reason")
    private String reason;

    /**
     * 原始AI响应结果
     */
    @TableField("raw_result")
    private String rawResult;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
} 