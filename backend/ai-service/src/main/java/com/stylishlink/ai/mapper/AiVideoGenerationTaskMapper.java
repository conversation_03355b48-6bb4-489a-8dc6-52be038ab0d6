package com.stylishlink.ai.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.stylishlink.ai.entity.AiVideoGenerationTask;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 视频生成任务Mapper接口
 */
@Mapper
public interface AiVideoGenerationTaskMapper extends BaseMapper<AiVideoGenerationTask> {

    /**
     * 根据用户ID查询任务列表
     * @param userId 用户ID
     * @return 任务列表
     */
    List<AiVideoGenerationTask> selectByUserId(@Param("userId") String userId);

    /**
     * 根据状态查询任务列表
     * @param status 任务状态
     * @return 任务列表
     */
    List<AiVideoGenerationTask> selectByStatus(@Param("status") String status);

    /**
     * 根据用户ID和状态查询任务列表
     * @param userId 用户ID
     * @param status 任务状态
     * @return 任务列表
     */
    List<AiVideoGenerationTask> selectByUserIdAndStatus(@Param("userId") String userId, 
                                                       @Param("status") String status);

    /**
     * 根据任务ID查询任务
     * @param taskId 任务ID
     * @return 任务信息
     */
    AiVideoGenerationTask selectByTaskId(@Param("taskId") String taskId);

    /**
     * 查询待处理的任务（按优先级排序）
     * @param limit 限制数量
     * @return 任务列表
     */
    List<AiVideoGenerationTask> selectPendingTasksByPriority(@Param("limit") Integer limit);
} 