package com.stylishlink.ai.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.stylishlink.ai.client.AiClient;
import com.stylishlink.ai.client.AiClientFactory;
import com.stylishlink.ai.dto.request.EnergyCalculationRequest;
import com.stylishlink.ai.dto.response.EnergyCalculationResponse;
import com.stylishlink.ai.exception.AiServiceException;
import com.stylishlink.ai.service.EnergyService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.TextStyle;
import java.util.*;

/**
 * 能量计算服务实现
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class EnergyServiceImpl implements EnergyService {
    
    private final AiClientFactory aiClientFactory;
    private final ObjectMapper objectMapper;
    
    @Override
    public EnergyCalculationResponse calculateTodayEnergy(EnergyCalculationRequest request) {
        log.info("开始计算用户今日能量信息: userId={}, date={}", request.getUserId(), request.getDate());
        
        try {
            // 获取AI客户端
            AiClient aiClient = aiClientFactory.getDefaultClient();
            
            // 构建能量计算请求
            Map<String, Object> aiRequest = buildEnergyRequest(request);
            
            // 调用AI服务进行能量计算
            Map<String, Object> aiResponse = aiClient.calculateEnergyInfo(aiRequest);
            
            // 解析AI响应
            EnergyCalculationResponse response = parseAiResponse(aiResponse);
            
            log.info("用户今日能量信息计算完成: userId={}, totalScore={}", 
                    request.getUserId(), response.getTotalScore());
            
            return response;
            
        } catch (Exception e) {
            log.error("计算用户今日能量信息失败: userId={}, date={}", 
                    request.getUserId(), request.getDate(), e);
            throw new AiServiceException("能量计算失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 构建发送给AI的能量计算请求
     */
    private Map<String, Object> buildEnergyRequest(EnergyCalculationRequest request) {
        Map<String, Object> aiRequest = new HashMap<>();
        
        // 基本信息
        aiRequest.put("userId", request.getUserId());
        aiRequest.put("date", request.getDate());
        
        // 日期分析信息
        aiRequest.put("dateAnalysis", analyzeDateInfo(request.getDate()));
        
        // 八字信息
        Map<String, Object> baziInfo = new HashMap<>();
        EnergyCalculationRequest.BaziInfo bazi = request.getBaziInfo();
        baziInfo.put("yearPillar", bazi.getYearPillar());
        baziInfo.put("monthPillar", bazi.getMonthPillar());
        baziInfo.put("dayPillar", bazi.getDayPillar());
        baziInfo.put("hourPillar", bazi.getHourPillar());
        baziInfo.put("dayMaster", bazi.getDayMaster());
        baziInfo.put("elements", bazi.getElements());
        aiRequest.put("baziInfo", baziInfo);
        
        // 用户信息
        Map<String, Object> userInfo = new HashMap<>();
        EnergyCalculationRequest.UserInfo user = request.getUserInfo();
        userInfo.put("gender", user.getGender());
        userInfo.put("birthDate", user.getBirthDate());
        userInfo.put("birthTime", user.getBirthTime());
        userInfo.put("birthPlace", user.getBirthPlace());
        userInfo.put("fullName", user.getFullName());
        aiRequest.put("userInfo", userInfo);
        
        return aiRequest;
    }
    
    /**
     * 分析日期信息
     */
    private Map<String, Object> analyzeDateInfo(String date) {
        LocalDate localDate = LocalDate.parse(date);
        
        Map<String, Object> dateAnalysis = new HashMap<>();
        dateAnalysis.put("year", localDate.getYear());
        dateAnalysis.put("month", localDate.getMonthValue());
        dateAnalysis.put("day", localDate.getDayOfMonth());
        dateAnalysis.put("dayOfWeek", localDate.getDayOfWeek().getDisplayName(TextStyle.FULL, Locale.CHINESE));
        dateAnalysis.put("season", getSeason(localDate.getMonthValue()));
        
        return dateAnalysis;
    }
    
    /**
     * 获取季节
     */
    private String getSeason(int month) {
        if (month >= 3 && month <= 5) return "春";
        if (month >= 6 && month <= 8) return "夏";
        if (month >= 9 && month <= 11) return "秋";
        return "冬";
    }
    
    /**
     * 解析AI响应为能量计算结果
     */
    @SuppressWarnings("unchecked")
    private EnergyCalculationResponse parseAiResponse(Map<String, Object> aiResponse) {
        try {
            // 直接使用AI返回的结构化数据
            Object data = aiResponse.get("data");
            if (data instanceof Map) {
                Map<String, Object> energyData = (Map<String, Object>) data;
                return convertToEnergyResponse(energyData);
            }
            
            // 如果没有结构化数据，返回默认响应
            log.warn("AI响应中没有找到结构化数据，返回默认能量信息");
            return createDefaultEnergyResponse();
            
        } catch (Exception e) {
            log.error("解析AI能量计算响应失败", e);
            return createDefaultEnergyResponse();
        }
    }
    
    /**
     * 转换AI响应数据为能量响应对象
     */
    @SuppressWarnings("unchecked")
    private EnergyCalculationResponse convertToEnergyResponse(Map<String, Object> energyData) {
        EnergyCalculationResponse.EnergyCalculationResponseBuilder builder = 
                EnergyCalculationResponse.builder();
        
        // 日期信息
        Object dateInfoObj = energyData.get("dateInfo");
        if (dateInfoObj instanceof Map) {
            Map<String, Object> dateInfoMap = (Map<String, Object>) dateInfoObj;
            EnergyCalculationResponse.DateInfo dateInfo = EnergyCalculationResponse.DateInfo.builder()
                    .gregorian(getStringValue(dateInfoMap, "gregorian", ""))
                    .lunar(getStringValue(dateInfoMap, "lunar", ""))
                    .build();
            builder.dateInfo(dateInfo);
        }
        
        // 基本能量数据
        builder.totalScore(getIntegerValue(energyData, "totalScore", 75));
        builder.percentage(getIntegerValue(energyData, "percentage", 60));
        builder.peakTime(getStringValue(energyData, "peakTime", "上午8-10点"));
        builder.peakTimeDescription(getStringValue(energyData, "peakTimeDescription", "能量充沛的时段"));
        builder.description(getStringValue(energyData, "description", "今日能量平衡"));
        
        // 五维能量评分
        Object dimensionsObj = energyData.get("dimensions");
        if (dimensionsObj instanceof Map) {
            Map<String, Object> dimensionsMap = (Map<String, Object>) dimensionsObj;
            EnergyCalculationResponse.Dimensions dimensions = EnergyCalculationResponse.Dimensions.builder()
                    .love(getIntegerValue(dimensionsMap, "love", 75))
                    .career(getIntegerValue(dimensionsMap, "career", 80))
                    .wealth(getIntegerValue(dimensionsMap, "wealth", 70))
                    .health(getIntegerValue(dimensionsMap, "health", 85))
                    .relationship(getIntegerValue(dimensionsMap, "relationship", 75))
                    .build();
            builder.dimensions(dimensions);
        }
        
        // 宜忌指南
        Object adviceObj = energyData.get("advice");
        if (adviceObj instanceof Map) {
            Map<String, Object> adviceMap = (Map<String, Object>) adviceObj;
            builder.advice(convertAdvice(adviceMap));
        }
        
        // 幸运元素
        Object luckyElementsObj = energyData.get("luckyElements");
        if (luckyElementsObj instanceof Map) {
            Map<String, Object> luckyElementsMap = (Map<String, Object>) luckyElementsObj;
            builder.luckyElements(convertLuckyElements(luckyElementsMap));
        }
        
        return builder.build();
    }
    
    /**
     * 转换宜忌指南
     */
    @SuppressWarnings("unchecked")
    private EnergyCalculationResponse.Advice convertAdvice(Map<String, Object> adviceMap) {
        EnergyCalculationResponse.Advice.AdviceBuilder builder = 
                EnergyCalculationResponse.Advice.builder();
        
        // 分类列表
        Object categoriesObj = adviceMap.get("categories");
        if (categoriesObj instanceof List) {
            List<Map<String, Object>> categoriesList = (List<Map<String, Object>>) categoriesObj;
            List<EnergyCalculationResponse.Category> categories = new ArrayList<>();
            
            for (Map<String, Object> categoryMap : categoriesList) {
                EnergyCalculationResponse.Category category = convertCategory(categoryMap);
                categories.add(category);
            }
            builder.categories(categories);
        }
        
        // 生活建议
        Object lifeSuggestionsObj = adviceMap.get("lifeSuggestions");
        if (lifeSuggestionsObj instanceof List) {
            List<Map<String, Object>> lifeSuggestionsList = (List<Map<String, Object>>) lifeSuggestionsObj;
            List<EnergyCalculationResponse.LifeSuggestion> lifeSuggestions = new ArrayList<>();
            
            for (Map<String, Object> suggestionMap : lifeSuggestionsList) {
                EnergyCalculationResponse.LifeSuggestion suggestion = 
                        EnergyCalculationResponse.LifeSuggestion.builder()
                                .icon(getStringValue(suggestionMap, "icon", ""))
                                .content(getStringValue(suggestionMap, "content", ""))
                                .build();
                lifeSuggestions.add(suggestion);
            }
            builder.lifeSuggestions(lifeSuggestions);
        }
        
        return builder.build();
    }
    
    /**
     * 转换宜忌分类
     */
    @SuppressWarnings("unchecked")
    private EnergyCalculationResponse.Category convertCategory(Map<String, Object> categoryMap) {
        EnergyCalculationResponse.Category.CategoryBuilder builder = 
                EnergyCalculationResponse.Category.builder();
        
        builder.type(getStringValue(categoryMap, "type", ""));
        builder.label(getStringValue(categoryMap, "label", ""));
        
        Object itemsObj = categoryMap.get("items");
        if (itemsObj instanceof List) {
            List<Map<String, Object>> itemsList = (List<Map<String, Object>>) itemsObj;
            List<EnergyCalculationResponse.Item> items = new ArrayList<>();
            
            for (Map<String, Object> itemMap : itemsList) {
                EnergyCalculationResponse.Item item = EnergyCalculationResponse.Item.builder()
                        .id(getStringValue(itemMap, "id", ""))
                        .icon(getStringValue(itemMap, "icon", ""))
                        .text(getStringValue(itemMap, "text", ""))
                        .build();
                items.add(item);
            }
            builder.items(items);
        }
        
        return builder.build();
    }
    
    /**
     * 转换幸运元素
     */
    @SuppressWarnings("unchecked")
    private EnergyCalculationResponse.LuckyElements convertLuckyElements(Map<String, Object> luckyElementsMap) {
        EnergyCalculationResponse.LuckyElements.LuckyElementsBuilder builder = 
                EnergyCalculationResponse.LuckyElements.builder();
        
        // 幸运色
        Object colorsObj = luckyElementsMap.get("colors");
        if (colorsObj instanceof List) {
            List<?> colorsList = (List<?>) colorsObj;
            List<EnergyCalculationResponse.ColorItem> colors = new ArrayList<>();
            
            for (Object colorObj : colorsList) {
                if (colorObj instanceof Map) {
                    // AI返回的对象格式
                    Map<String, Object> colorMap = (Map<String, Object>) colorObj;
                    colors.add(EnergyCalculationResponse.ColorItem.builder()
                            .value(getStringValue(colorMap, "value", ""))
                            .name(getStringValue(colorMap, "name", ""))
                            .build());
                } else if (colorObj instanceof String) {
                    // 兼容旧的字符串格式
                    String colorStr = (String) colorObj;
                    colors.add(EnergyCalculationResponse.ColorItem.builder()
                            .value(colorStr)
                            .name(colorStr)
                            .build());
                }
            }
            builder.colors(colors);
        }
        
        // 服饰建议
        Object clothingObj = luckyElementsMap.get("clothing");
        if (clothingObj instanceof List) {
            builder.clothing((List<String>) clothingObj);
        }
        
        // 配饰建议
        Object accessoriesObj = luckyElementsMap.get("accessories");
        if (accessoriesObj instanceof List) {
            builder.accessories((List<String>) accessoriesObj);
        }
        
        // 妆容建议
        Object makeupObj = luckyElementsMap.get("makeup");
        if (makeupObj instanceof List) {
            builder.makeup((List<String>) makeupObj);
        }
        
        return builder.build();
    }
    
    /**
     * 创建默认能量响应
     */
    private EnergyCalculationResponse createDefaultEnergyResponse() {
        return EnergyCalculationResponse.builder()
                .dateInfo(EnergyCalculationResponse.DateInfo.builder()
                        .gregorian(LocalDate.now().toString())
                        .lunar("农历信息")
                        .build())
                .totalScore(75)
                .percentage(60)
                .peakTime("上午8-10点")
                .peakTimeDescription("能量充沛的时段")
                .description("今日能量平衡，适合进行常规活动")
                .dimensions(EnergyCalculationResponse.Dimensions.builder()
                        .love(75)
                        .career(80)
                        .wealth(70)
                        .health(85)
                        .relationship(75)
                        .build())
                .advice(EnergyCalculationResponse.Advice.builder()
                        .categories(Arrays.asList())
                        .lifeSuggestions(Arrays.asList())
                        .build())
                .luckyElements(EnergyCalculationResponse.LuckyElements.builder()
                        .colors(Arrays.asList(
                                EnergyCalculationResponse.ColorItem.builder()
                                        .value("#FF6B6B")
                                        .name("珊瑚红")
                                        .build(),
                                EnergyCalculationResponse.ColorItem.builder()
                                        .value("#4ECDC4")
                                        .name("青绿色")
                                        .build()))
                        .clothing(Arrays.asList("推荐穿着舒适的休闲服装"))
                        .accessories(Arrays.asList("建议佩戴简约的首饰"))
                        .makeup(Arrays.asList("清淡自然的妆容"))
                        .build())
                .build();
    }
    
    /**
     * 安全获取字符串值
     */
    private String getStringValue(Map<String, Object> map, String key, String defaultValue) {
        Object value = map.get(key);
        return value != null ? value.toString() : defaultValue;
    }
    
    /**
     * 安全获取整数值
     */
    private Integer getIntegerValue(Map<String, Object> map, String key, Integer defaultValue) {
        Object value = map.get(key);
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        if (value instanceof String) {
            try {
                return Integer.parseInt((String) value);
            } catch (NumberFormatException e) {
                return defaultValue;
            }
        }
        return defaultValue;
    }
} 