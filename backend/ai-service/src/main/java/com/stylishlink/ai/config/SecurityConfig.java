package com.stylishlink.ai.config;

import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

/**
 * AI服务安全配置
 */
@Configuration
@EnableWebSecurity
@RequiredArgsConstructor
public class SecurityConfig {

    private final GatewayUserFilter gatewayUserFilter;

    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        return http
            // 禁用CSRF
            .csrf(AbstractHttpConfigurer::disable)
            
            // 配置会话管理为无状态
            .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
            
            // 配置请求授权
            .authorizeHttpRequests(authz -> authz
                // SpringDoc相关路径允许访问
                .requestMatchers(
                    "/v3/api-docs/**",
                    "/swagger-ui/**",
                    "/swagger-ui.html",
                    "/swagger-config", 
                    "/webjars/**"
                ).permitAll()
                
                // Actuator健康检查等端点
                .requestMatchers("/actuator/**").permitAll()
                
                // 错误页面
                .requestMatchers("/error").permitAll()
                
                // AI识别相关接口允许访问（可能需要匿名访问）
                .requestMatchers("/recognition/**").permitAll()

                // AI API接口允许直接访问，不需要认证
                .requestMatchers("/api/ai/**").permitAll()
                // 其他所有请求都需要网关认证（通过header验证）
                .anyRequest().authenticated()
            )
            
            // 禁用HTTP Basic认证
            .httpBasic(AbstractHttpConfigurer::disable)
            
            // 禁用表单登录
            .formLogin(AbstractHttpConfigurer::disable)
            
            // 添加网关用户信息过滤器
            .addFilterBefore(gatewayUserFilter, UsernamePasswordAuthenticationFilter.class)
            
            .build();
    }
} 