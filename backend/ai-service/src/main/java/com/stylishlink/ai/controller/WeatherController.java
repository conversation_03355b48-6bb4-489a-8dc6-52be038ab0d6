package com.stylishlink.ai.controller;

import com.stylishlink.ai.dto.request.WeatherRequest;
import com.stylishlink.ai.dto.response.WeatherResponse;
import com.stylishlink.ai.service.WeatherService;
import com.stylishlink.common.dto.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 天气查询控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/ai/weather")
public class WeatherController {

    @Autowired
    private WeatherService weatherService;

    /**
     * 天气查询接口
     * 支持城市名称或经纬度查询，默认使用心知天气
     * 
     * @param city 城市名称（如：北京、上海）
     * @param longitude 经度（可选）
     * @param latitude 纬度（可选）
     * @return 天气信息
     */
    @GetMapping
    public ApiResponse<WeatherResponse> getWeather(
            @RequestParam(value = "city", required = false) String city,
            @RequestParam(value = "longitude", required = false) Double longitude,
            @RequestParam(value = "latitude", required = false) Double latitude) {
        try {
            // 构建请求对象
            WeatherRequest request = new WeatherRequest();
            request.setCityName(city);
            request.setLongitude(longitude);
            request.setLatitude(latitude);

            log.info("天气查询请求: city={}, longitude={}, latitude={}", city, longitude, latitude);
            
            WeatherResponse response = weatherService.getWeather(request);
            
            if (Boolean.TRUE.equals(response.getSuccess())) {
                return ApiResponse.success("天气查询成功", response);
            } else {
                return ApiResponse.error(5001, response.getErrorMessage());
            }
        } catch (Exception e) {
            log.error("天气查询失败", e);
            return ApiResponse.error(5000, "天气查询失败: " + e.getMessage());
        }
    }
} 