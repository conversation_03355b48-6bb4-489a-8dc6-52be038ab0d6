package com.stylishlink.ai.dto.response;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 风格分析响应DTO
 */
@Data
public class StyleAnalysisResponse {

    /**
     * 风格分布
     */
    private Map<String, Double> styleDistribution;

    /**
     * 主导颜色
     */
    private List<String> dominantColors;

    /**
     * 推荐风格
     */
    private List<String> recommendedStyles;

    /**
     * 材质分析
     */
    private Map<String, Double> materialAnalysis;

    /**
     * 图案分析
     */
    private List<String> patternAnalysis;

    /**
     * 季节适应性
     */
    private Map<String, Double> seasonSuitability;

    /**
     * 场合适应性
     */
    private Map<String, Double> occasionSuitability;

    /**
     * 分析ID（用于后续查询）
     */
    private Long analysisId;
} 