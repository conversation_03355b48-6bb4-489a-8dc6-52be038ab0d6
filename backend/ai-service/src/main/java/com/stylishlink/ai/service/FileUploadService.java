package com.stylishlink.ai.service;

import com.stylishlink.ai.client.FileServiceClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

/**
 * 文件上传服务
 * 封装Feign客户端调用，提供便捷的文件上传方法
 */
@Slf4j
@Service
public class FileUploadService {

    @Autowired
    private FileServiceClient fileServiceClient;

    /**
     * 上传图片文件 (便捷方法)
     * 
     * @param imageFile 图片文件
     * @param userId 用户ID
     * @return 图片访问URL
     */
    public String uploadImage(MultipartFile imageFile, String userId) {
        return uploadFile(imageFile, userId, "TEMP_FILE", "PUBLIC");
    }

    /**
     * 上传文件
     * 
     * @param file 文件
     * @param userId 用户ID
     * @param category 文件类别
     * @param accessLevel 访问级别
     * @return 文件访问URL
     */
    public String uploadFile(MultipartFile file, String userId, String category, String accessLevel) {
        try {
            log.info("上传文件: 文件名={}, 用户ID={}, 类别={}, 访问级别={}", 
                    file.getOriginalFilename(), userId, category, accessLevel);

            FileServiceClient.FileUploadResponse response = fileServiceClient.uploadFile(file, userId, category, accessLevel);
            
            log.info("文件服务响应: code={}, message={}", response.getCode(), response.getMessage());
            
            // 只接受正常的成功响应
            if ("200".equals(response.getCode()) && response.getData() != null) {
                String fileUrl = response.getData().getUrl();
                if (fileUrl != null && !fileUrl.isEmpty()) {
                    log.info("文件上传成功，获取到URL: {}", fileUrl);
                    return fileUrl;
                }
            }
            
            // 任何非成功的情况都直接抛出异常，不进行任何降级处理
            throw new RuntimeException("文件上传失败: " + response.getMessage());
            
        } catch (Exception e) {
            log.error("文件上传失败: {}", e.getMessage());
            // 直接重新抛出异常，不做任何降级处理，让调用方知道真实的失败情况
            throw new RuntimeException("文件上传失败: " + e.getMessage(), e);
        }
    }

    /**
     * 根据文件ID获取图片URL
     * @param fileId 文件ID
     * @return 图片URL
     */
    public String getImageUrlById(String fileId) {
        try {
            log.info("根据文件ID获取图片URL: {}", fileId);
            
            // 调用文件服务获取文件信息
            Object fileInfo = getFileInfo(fileId);
            
            // 从文件信息中提取URL
            if (fileInfo instanceof Map) {
                Map<String, Object> fileMap = (Map<String, Object>) fileInfo;
                String imageUrl = (String) fileMap.get("url");
                
                if (imageUrl != null && !imageUrl.isEmpty()) {
                    log.info("成功获取到图片URL: {}", imageUrl);
                    return imageUrl;
                } else {
                    throw new RuntimeException("文件信息中未找到URL字段");
                }
            } else {
                throw new RuntimeException("无效的文件信息格式");
            }
            
        } catch (Exception e) {
            log.error("根据文件ID获取图片URL失败: {}", e.getMessage());
            throw new RuntimeException("获取图片URL失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取文件信息
     */
    public Object getFileInfo(String fileId) {
        try {
            FileServiceClient.FileInfoResponse response = fileServiceClient.getFileInfo(fileId);
            if ("200".equals(response.getCode())) {
                return response.getData();
            }
            throw new RuntimeException("获取文件信息失败: " + response.getMessage());
        } catch (Exception e) {
            log.error("获取文件信息失败: {}", e.getMessage());
            throw new RuntimeException("获取文件信息失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取下载链接
     */
    public String getDownloadUrl(String fileId, int expiresIn) {
        try {
            FileServiceClient.DownloadUrlResponse response = fileServiceClient.getDownloadUrl(fileId, expiresIn);
            if ("200".equals(response.getCode())) {
                return response.getData();
            }
            throw new RuntimeException("获取下载链接失败: " + response.getMessage());
        } catch (Exception e) {
            log.error("获取下载链接失败: {}", e.getMessage());
            throw new RuntimeException("获取下载链接失败: " + e.getMessage(), e);
        }
    }

    /**
     * 删除文件
     */
    public boolean deleteFile(String fileId, boolean force) {
        try {
            FileServiceClient.DeleteFileResponse response = fileServiceClient.deleteFile(fileId, force);
            if ("200".equals(response.getCode())) {
                return response.getData() != null ? response.getData() : false;
            }
            return false;
        } catch (Exception e) {
            log.error("删除文件失败: {}", e.getMessage());
            return false;
        }
    }
} 