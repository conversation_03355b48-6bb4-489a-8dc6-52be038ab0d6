package com.stylishlink.ai.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.stylishlink.ai.client.AiClient;
import com.stylishlink.ai.client.AiClientFactory;
import com.stylishlink.ai.dto.request.ChatRequest;
import com.stylishlink.ai.dto.response.ChatResponse;
import com.stylishlink.ai.entity.AiChatRecord;
import com.stylishlink.ai.mapper.AiChatRecordMapper;
import com.stylishlink.ai.service.AiChatService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.UUID;

/**
 * AI对话服务实现
 */
@Slf4j
@Service
public class AiChatServiceImpl implements AiChatService {

    @Autowired
    private AiClientFactory aiClientFactory;

    @Autowired
    private AiChatRecordMapper chatRecordMapper;

    @Autowired
    private ObjectMapper objectMapper;

    @Override
    public ChatResponse chat(ChatRequest request) {
        return processChat(request, "general");
    }

    @Override
    public ChatResponse getFashionAdvice(ChatRequest request) {
        // 为时尚建议添加特定的上下文
        String fashionContext = "请作为专业的时尚顾问，为用户提供专业的穿搭建议和时尚指导。";
        request.setContext(fashionContext);
        return processChat(request, "fashion_advice");
    }

    @Override
    public ChatResponse getStylistConsultation(ChatRequest request) {
        // 为造型师咨询添加特定的上下文
        String stylistContext = "请作为专业的造型师，根据用户的需求提供个性化的造型建议，包括服装搭配、色彩选择、风格定位等。";
        request.setContext(stylistContext);
        return processChat(request, "stylist_consultation");
    }

    /**
     * 处理对话的通用方法
     */
    private ChatResponse processChat(ChatRequest request, String chatType) {
        long startTime = System.currentTimeMillis();
        
        try {
            // 生成会话ID（如果没有提供）
            String sessionId = StringUtils.hasText(request.getSessionId()) ? 
                request.getSessionId() : UUID.randomUUID().toString();

            // 保存用户消息
            saveUserMessage(request, sessionId);

            // 根据聊天类型获取合适的AI客户端
            AiClient aiClient = aiClientFactory.getClientByScenario(chatType);
            
            // 调用AI服务
            Map<String, Object> aiResult = aiClient.chat(request.getMessage(), request.getContext());

            // 构建响应
            ChatResponse response = new ChatResponse();
            response.setReply((String) aiResult.get("reply"));
            response.setSessionId(sessionId);
            response.setAiModel((String) aiResult.get("model"));
            
            if (aiResult.containsKey("tokensUsed")) {
                response.setTokensUsed((Integer) aiResult.get("tokensUsed"));
            }
            
            long endTime = System.currentTimeMillis();
            response.setResponseTime((int) (endTime - startTime));

            // 保存AI回复
            saveAssistantMessage(response, request.getUserId(), sessionId);

            log.info("AI对话完成，用户: {}, 会话: {}, 类型: {}, AI客户端: {}, 耗时: {}ms", 
                    request.getUserId(), sessionId, chatType, aiClient.getClass().getSimpleName(), response.getResponseTime());

            return response;

        } catch (Exception e) {
            log.error("AI对话失败", e);
            
            // 返回默认响应
            ChatResponse response = new ChatResponse();
            response.setReply("抱歉，我现在无法为您提供回复，请稍后再试。");
            response.setSessionId(request.getSessionId());
            response.setResponseTime((int) (System.currentTimeMillis() - startTime));
            
            return response;
        }
    }

    /**
     * 保存用户消息
     */
    private void saveUserMessage(ChatRequest request, String sessionId) {
        try {
            AiChatRecord record = new AiChatRecord();
            record.setUserId(request.getUserId());
            record.setSessionId(sessionId);
            record.setMessageType("user");
            record.setMessage(request.getMessage());
            record.setContext(request.getContext());
            record.setCreatedAt(LocalDateTime.now());

            chatRecordMapper.insert(record);
        } catch (Exception e) {
            log.error("保存用户消息失败", e);
        }
    }

    /**
     * 保存AI回复消息
     */
    private void saveAssistantMessage(ChatResponse response, String userId, String sessionId) {
        try {
            AiChatRecord record = new AiChatRecord();
            record.setUserId(userId);
            record.setSessionId(sessionId);
            record.setMessageType("assistant");
            record.setMessage(response.getReply());
            record.setAiModel(response.getAiModel());
            record.setTokensUsed(response.getTokensUsed());
            record.setResponseTime(response.getResponseTime());
            record.setCreatedAt(LocalDateTime.now());

            chatRecordMapper.insert(record);
        } catch (Exception e) {
            log.error("保存AI回复失败", e);
        }
    }
} 