package com.stylishlink.ai.dto.request;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * 风格分析请求DTO
 */
@Data
public class StyleAnalysisRequest {

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 来源类型(image/outfit)
     */
    @NotBlank(message = "来源类型不能为空")
    private String sourceType;

    /**
     * 来源ID
     */
    @NotBlank(message = "来源ID不能为空")
    private String sourceId;

    /**
     * 图片URL（当sourceType为image时使用）
     */
    private String imageUrl;

    /**
     * 搭配ID（当sourceType为outfit时使用）
     */
    private String outfitId;
} 