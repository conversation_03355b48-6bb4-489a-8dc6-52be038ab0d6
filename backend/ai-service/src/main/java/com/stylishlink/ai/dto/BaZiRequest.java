package com.stylishlink.ai.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;

/**
 * 八字计算请求DTO
 */
@Data
@Schema(description = "八字计算请求")
public class BaZiRequest {
    
    @Schema(description = "出生日期", example = "1990-01-01")
    private String birthDate;
    
    @Schema(description = "出生时间", example = "08:30")
    private String birthTime;
    
    @NotBlank(message = "性别不能为空")
    @Schema(description = "性别", example = "男", allowableValues = {"男", "女"})
    private String gender;
    
    @Schema(description = "出生地点（可选）", example = "北京市")
    private String birthPlace;
    
    @Schema(description = "姓名（可选）", example = "张三")
    private String fullName;
    
    @Schema(description = "用户ID（可选）", example = "user123")
    private String userId;
    
    /**
     * 获取完整的出生日期时间
     * 如果只提供了时间，则使用默认日期（当前日期）
     */
    public LocalDateTime getBirthDateTime() {
        try {
            LocalDate date;
            LocalTime time;
            
            // 解析日期
            if (birthDate != null && !birthDate.trim().isEmpty()) {
                date = LocalDate.parse(birthDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            } else {
                date = LocalDate.now(); // 默认使用当前日期
            }
            
            // 解析时间
            if (birthTime != null && !birthTime.trim().isEmpty()) {
                // 支持多种时间格式
                if (birthTime.contains(":")) {
                    time = LocalTime.parse(birthTime, DateTimeFormatter.ofPattern("HH:mm"));
                } else if (birthTime.length() == 4) {
                    // 支持HHMM格式，如0830
                    time = LocalTime.parse(birthTime, DateTimeFormatter.ofPattern("HHmm"));
                } else {
                    time = LocalTime.parse(birthTime + ":00", DateTimeFormatter.ofPattern("HH:mm"));
                }
            } else {
                time = LocalTime.of(12, 0); // 默认中午12点
            }
            
            return LocalDateTime.of(date, time);
        } catch (Exception e) {
            // 如果解析失败，返回当前时间
            return LocalDateTime.now();
        }
    }
} 