package com.stylishlink.ai.service.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.stylishlink.ai.dto.request.WeatherRequest;
import com.stylishlink.ai.dto.response.WeatherData;
import com.stylishlink.ai.dto.response.WeatherLocation;
import com.stylishlink.ai.dto.response.WeatherResponse;
import com.stylishlink.ai.service.WeatherProvider;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

/**
 * 心知天气数据提供商实现
 */
@Slf4j
@Service
public class XinZhiWeatherProvider implements WeatherProvider {

    @Value("${weather.xinzhi.api-key:}")
    private String apiKey;

    @Value("${weather.xinzhi.base-url:https://api.seniverse.com/v3/weather}")
    private String baseUrl;

    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;

    public XinZhiWeatherProvider(RestTemplate restTemplate, ObjectMapper objectMapper) {
        this.restTemplate = restTemplate;
        this.objectMapper = objectMapper;
    }

    @Override
    public String getProviderName() {
        return "xinzhi";
    }

    @Override
    public WeatherResponse getWeather(WeatherRequest request) {
        try {
            String location = buildLocationParam(request);
            if (!StringUtils.hasText(location)) {
                return buildErrorResponse("位置参数不能为空");
            }

            String url = buildApiUrl(location, "base"); // 使用实况天气
            log.info("调用心知天气API: {}", url);

            String response = restTemplate.getForObject(url, String.class);
            return parseResponse(response);

        } catch (Exception e) {
            log.error("心知天气API调用失败", e);
            return buildErrorResponse("天气数据获取失败: " + e.getMessage());
        }
    }

    @Override
    public boolean isAvailable() {
        return StringUtils.hasText(apiKey);
    }

    @Override
    public int getPriority() {
        return 1; // 心知天气优先级较高
    }

    /**
     * 构建位置参数
     */
    private String buildLocationParam(WeatherRequest request) {
        if (StringUtils.hasText(request.getCityName())) {
            return request.getCityName();
        }
        if (request.getLatitude() != null && request.getLongitude() != null) {
            return request.getLatitude() + ":" + request.getLongitude();
        }
        return null;
    }

    /**
     * 构建API URL
     */
    private String buildApiUrl(String location, String weatherType) {
        String endpoint = "base".equals(weatherType) ? "now" : "daily";
        return String.format("%s/%s.json?key=%s&location=%s&language=zh-Hans&unit=c",
                baseUrl, endpoint, apiKey, location);
    }

    /**
     * 解析心知天气API响应
     */
    private WeatherResponse parseResponse(String responseStr) {
        try {
            log.info("心知天气API原始响应: {}", responseStr);
            
            JsonNode root = objectMapper.readTree(responseStr);
            JsonNode results = root.get("results");
            
            if (results == null || !results.isArray() || results.size() == 0) {
                log.error("心知天气API返回数据格式错误: {}", responseStr);
                return buildErrorResponse("心知天气API返回数据格式错误");
            }

            JsonNode result = results.get(0);
            WeatherResponse response = new WeatherResponse();
            response.setSuccess(true);
            response.setDataSource("心知天气");
            response.setTimestamp(System.currentTimeMillis());

            // 解析位置信息
            JsonNode locationNode = result.get("location");
            if (locationNode != null) {
                WeatherLocation location = new WeatherLocation();
                location.setId(locationNode.path("id").asText());
                location.setName(locationNode.path("name").asText());
                location.setCountry(locationNode.path("country").asText());
                location.setPath(locationNode.path("path").asText());
                location.setTimezone(locationNode.path("timezone").asText());
                location.setTimezoneOffset(locationNode.path("timezone_offset").asText());
                response.setLocation(location);
            }

            // 解析实况天气数据
            JsonNode nowNode = result.get("now");
            if (nowNode != null) {
                log.info("找到now节点，开始解析天气数据");
                WeatherData current = new WeatherData();
                current.setText(nowNode.path("text").asText());
                current.setCode(nowNode.path("code").asText());
                current.setTemperature(nowNode.path("temperature").asText());
                current.setHumidity(nowNode.path("humidity").asText());
                current.setVisibility(nowNode.path("visibility").asText());
                current.setPressure(nowNode.path("pressure").asText());
                current.setWindDirection(nowNode.path("wind_direction").asText());
                current.setWindSpeed(nowNode.path("wind_speed").asText());
                current.setWindPower(nowNode.path("wind_scale").asText());
                current.setUvIndex(nowNode.path("clouds").asText()); // 暂时用clouds字段存储
                response.setCurrent(current);
                log.info("天气数据解析完成: {}", current);
            } else {
                log.warn("未找到now节点，检查API响应结构");
            }

            // 解析预报天气数据
            JsonNode dailyNode = result.get("daily");
            if (dailyNode != null && dailyNode.isArray()) {
                log.info("找到daily节点，包含{}天预报", dailyNode.size());
                java.util.List<WeatherData> forecastList = new java.util.ArrayList<>();
                for (JsonNode dayNode : dailyNode) {
                    WeatherData forecast = new WeatherData();
                    forecast.setDate(dayNode.path("date").asText());
                    forecast.setDayWeather(dayNode.path("text_day").asText());
                    forecast.setNightWeather(dayNode.path("text_night").asText());
                    forecast.setCode(dayNode.path("code_day").asText());
                    forecast.setHighTemperature(dayNode.path("high").asText());
                    forecast.setLowTemperature(dayNode.path("low").asText());
                    forecast.setPrecipitation(dayNode.path("rainfall").asText());
                    forecast.setWindDirection(dayNode.path("wind_direction").asText());
                    forecast.setWindPower(dayNode.path("wind_scale").asText());
                    forecastList.add(forecast);
                }
                response.setForecast(forecastList);
            }

            // 解析更新时间
            String lastUpdate = result.path("last_update").asText();
            response.setLastUpdate(lastUpdate);

            return response;

        } catch (Exception e) {
            log.error("解析心知天气API响应失败", e);
            return buildErrorResponse("天气数据解析失败: " + e.getMessage());
        }
    }

    /**
     * 构建错误响应
     */
    private WeatherResponse buildErrorResponse(String errorMessage) {
        WeatherResponse response = new WeatherResponse();
        response.setSuccess(false);
        response.setErrorMessage(errorMessage);
        response.setDataSource("心知天气");
        response.setTimestamp(System.currentTimeMillis());
        return response;
    }
} 