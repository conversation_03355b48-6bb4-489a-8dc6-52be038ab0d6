package com.stylishlink.ai.client;

/**
 * AI客户端类型枚举
 * 定义支持的AI服务提供商类型
 */
public enum AiClientType {
    
    /**
     * DeepSeek AI服务
     * 主要用于文本对话和代码生成
     */
    DEEPSEEK("deepseek", "DeepSeek AI服务", 1),
    
    /**
     * OpenAI GPT服务
     * 通用AI对话服务
     */
    OPENAI("openai", "OpenAI GPT服务", 2),
    
    /**
     * 豆包视觉服务
     * 专门用于图像识别和视觉分析
     */
    DOUBAO_VISION("doubao", "豆包视觉服务", 3);
    
    private final String code;
    private final String description;
    private final int priority;
    
    AiClientType(String code, String description, int priority) {
        this.code = code;
        this.description = description;
        this.priority = priority;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    public int getPriority() {
        return priority;
    }
    
    /**
     * 根据代码获取AI客户端类型
     */
    public static AiClientType fromCode(String code) {
        for (AiClientType type : values()) {
            if (type.code.equalsIgnoreCase(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("未知的AI客户端类型: " + code);
    }
    
    /**
     * 获取默认AI客户端类型（优先级最高的）
     */
    public static AiClientType getDefault() {
        return DEEPSEEK;
    }
} 