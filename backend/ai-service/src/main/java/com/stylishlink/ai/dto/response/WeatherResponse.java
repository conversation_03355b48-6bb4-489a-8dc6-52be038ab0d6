package com.stylishlink.ai.dto.response;

import lombok.Data;
import java.util.List;

/**
 * 天气查询响应DTO
 */
@Data
public class WeatherResponse {

    /**
     * 位置信息
     */
    private WeatherLocation location;

    /**
     * 当前天气（实况天气）
     */
    private WeatherData current;

    /**
     * 预报天气列表（未来几天）
     */
    private List<WeatherData> forecast;

    /**
     * 数据更新时间
     */
    private String lastUpdate;

    /**
     * 数据来源
     */
    private String dataSource;

    /**
     * 查询时间戳
     */
    private Long timestamp;

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 错误信息（如果有）
     */
    private String errorMessage;
} 