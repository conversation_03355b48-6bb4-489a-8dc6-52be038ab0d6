package com.stylishlink.ai.config;

import com.stylishlink.ai.client.AiClient;
import com.stylishlink.ai.client.AiClientFactory;
import com.stylishlink.ai.client.AiClientStrategySelector;
import com.stylishlink.ai.client.impl.DeepSeekClientImpl;
import com.stylishlink.ai.client.impl.DoubaoVisionClientImpl;
import com.stylishlink.ai.client.impl.OpenAiClientImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * AI客户端配置类
 * 集成策略选择器，支持灵活的AI客户端选择
 */
@Slf4j
@Configuration
public class AiClientConfig {

    /**
     * AI客户端工厂 - 主要接口
     * 提供统一的AI客户端获取方法
     */
    @Bean
    @Primary
    public AiClientFactory aiClientFactory(
            @Autowired(required = false) DeepSeekClientImpl deepSeekClient,
            @Autowired(required = false) OpenAiClientImpl openAiClient,
            @Autowired(required = false) DoubaoVisionClientImpl doubaoVisionClient) {
        log.info("配置AI客户端工厂，支持智能策略选择");
        log.info("可用的AI客户端: DeepSeek={}, OpenAI={}, DoubaoVision={}", 
                deepSeekClient != null, openAiClient != null, doubaoVisionClient != null);
        return new AiClientStrategySelector(deepSeekClient, openAiClient, doubaoVisionClient);
    }

    /**
     * 主要AI客户端 - 使用工厂获取默认客户端
     * 保持向后兼容性
     */
    @Bean
    @Qualifier("primaryAiClient")
    public AiClient primaryAiClient(AiClientFactory aiClientFactory) {
        log.info("配置主要AI客户端（通过工厂获取）");
        return aiClientFactory.getDefaultClient();
    }

    /**
     * 备选AI客户端 - 使用工厂获取可用客户端
     * 保持向后兼容性
     */
    @Bean
    @Qualifier("fallbackAiClient")
    public AiClient fallbackAiClient(AiClientFactory aiClientFactory) {
        log.info("配置备选AI客户端（通过工厂获取）");
        return aiClientFactory.getAvailableClient();
    }

    /**
     * 视觉分析AI客户端 - 使用工厂获取视觉专用客户端
     * 保持向后兼容性
     */
    @Bean
    @Qualifier("visionAiClient")
    public AiClient visionAiClient(AiClientFactory aiClientFactory) {
        log.info("配置视觉分析AI客户端（通过工厂获取）");
        return aiClientFactory.getVisionClient();
    }

    /**
     * 兼容性：保留原有的AI客户端选择器
     * @deprecated 推荐使用 AiClientFactory
     */
    @Deprecated
    @Bean
    @Qualifier("aiClientSelector")
    public AiClientSelector aiClientSelector(AiClientFactory aiClientFactory) {
        log.warn("使用已弃用的AiClientSelector，建议迁移到AiClientFactory");
        return new AiClientSelector(
                aiClientFactory.getDefaultClient(),
                aiClientFactory.getAvailableClient(),
                aiClientFactory.getVisionClient()
        );
    }

    /**
     * AI客户端选择器实现 - 保持向后兼容
     * @deprecated 推荐使用 AiClientFactory
     */
    @Deprecated
    public static class AiClientSelector {
        private final AiClient primaryClient;
        private final AiClient fallbackClient;
        private final AiClient visionClient;

        public AiClientSelector(AiClient primaryClient, AiClient fallbackClient, AiClient visionClient) {
            this.primaryClient = primaryClient;
            this.fallbackClient = fallbackClient;
            this.visionClient = visionClient;
        }

        public AiClient getAvailableClient() {
            return primaryClient != null ? primaryClient : fallbackClient;
        }

        public AiClient getVisionClient() {
            return visionClient;
        }

        public AiClient getPrimaryClient() {
            return primaryClient;
        }

        public AiClient getFallbackClient() {
            return fallbackClient;
        }
    }
} 