package com.stylishlink.ai.controller;

import com.stylishlink.ai.dto.request.BodyShapeAnalysisRequest;
import com.stylishlink.ai.dto.response.BodyShapeAnalysisResponse;
import com.stylishlink.ai.service.BodyShapeAnalysisService;
import com.stylishlink.common.dto.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.validation.Valid;

/**
 * 身材识别控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/ai")
public class BodyShapeController {

    @Autowired
    private BodyShapeAnalysisService bodyShapeAnalysisService;

    /**
     * 全身照身材识别接口
     */
    @PostMapping("/analyze-body-shape")
    public ApiResponse<BodyShapeAnalysisResponse> analyzeBodyShape(
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "userId", required = false) String userId) {
        
        try {
            // 构建请求对象
            BodyShapeAnalysisRequest request = new BodyShapeAnalysisRequest();
            request.setFile(file);
            request.setUserId(userId);

            // 调用服务进行身材识别
            BodyShapeAnalysisResponse response = bodyShapeAnalysisService.analyzeBodyShape(request);
            
            // 检查是否为正面全身照
            if (!response.isFullBodyPhoto()) {
                log.warn("身材识别失败：非正面全身照，用户: {}, 原因: {}", userId, response.getReason());
                return ApiResponse.error(6003, "非正面全身照: " + response.getReason());
            }
            
            log.info("身材识别成功，用户: {}, 体型: {}, 置信度: {}", 
                    userId, response.getBodyType(), response.getConfidence());
            return ApiResponse.success("身材识别成功", response);
            
        } catch (Exception e) {
            log.error("身材识别失败", e);
            return ApiResponse.error(6001, "身材识别失败: " + e.getMessage());
        }
    }

    /**
     * 基于图片URL的身材识别接口
     */
    @PostMapping("/analyze-body-shape-by-url")
    public ApiResponse<BodyShapeAnalysisResponse> analyzeBodyShapeByUrl(
            @RequestParam("imageUrl") String imageUrl,
            @RequestParam(value = "userId", required = false) String userId,
            @RequestParam(value = "fileId", required = false) String fileId) {
        
        try {
            // 构建请求对象
            BodyShapeAnalysisRequest request = new BodyShapeAnalysisRequest();
            request.setImageUrl(imageUrl);
            request.setUserId(userId);
            request.setFileId(fileId);

            // 调用服务进行身材识别
            BodyShapeAnalysisResponse response = bodyShapeAnalysisService.analyzeBodyShapeByUrl(request);
            
            // 检查是否为正面全身照
            if (!response.isFullBodyPhoto()) {
                log.warn("身材识别失败：非正面全身照，用户: {}, 原因: {}", userId, response.getReason());
                return ApiResponse.error(6003, "非正面全身照: " + response.getReason());
            }
            
            log.info("身材识别成功（URL方式），用户: {}, 体型: {}, 置信度: {}", 
                    userId, response.getBodyType(), response.getConfidence());
            return ApiResponse.success("身材识别成功", response);
            
        } catch (Exception e) {
            log.error("身材识别失败（URL方式）", e);
            return ApiResponse.error(6001, "身材识别失败: " + e.getMessage());
        }
    }

    /**
     * 基于文件ID的身材识别接口
     */
    @PostMapping("/analyze-body-shape-by-file-id")
    public ApiResponse<BodyShapeAnalysisResponse> analyzeBodyShapeByFileId(
            @RequestParam("fileId") String fileId,
            @RequestParam(value = "userId", required = false) String userId) {
        
        try {
            // 构建请求对象
            BodyShapeAnalysisRequest request = new BodyShapeAnalysisRequest();
            request.setFileId(fileId);
            request.setUserId(userId);

            // 调用服务进行身材识别
            BodyShapeAnalysisResponse response = bodyShapeAnalysisService.analyzeBodyShapeByFileId(request);
            
            // 检查是否为正面全身照
            if (!response.isFullBodyPhoto()) {
                log.warn("身材识别失败：非正面全身照，用户: {}, 原因: {}", userId, response.getReason());
                return ApiResponse.error(6003, "非正面全身照: " + response.getReason());
            }
            
            log.info("身材识别成功（文件ID方式），用户: {}, 体型: {}, 置信度: {}", 
                    userId, response.getBodyType(), response.getConfidence());
            return ApiResponse.success("身材识别成功", response);
            
        } catch (Exception e) {
            log.error("身材识别失败（文件ID方式）", e);
            return ApiResponse.error(6001, "身材识别失败: " + e.getMessage());
        }
    }
} 