package com.stylishlink.ai.service;

import com.stylishlink.ai.dto.WuXingRequest;
import com.stylishlink.ai.dto.request.FortuneAnalysisRequest;

import java.util.Map;

/**
 * 五行分析服务接口
 */
public interface WuXingService {
    
    /**
     * 分析五行
     * @param request 五行分析请求
     * @return 五行分析结果
     */
    Map<String, Object> analyzeWuxing(WuXingRequest request);
    
    /**
     * 生成完整运势解读
     * @param request 运势分析请求
     * @return 运势解读结果
     */
    Map<String, Object> generateCompleteFortuneReading(FortuneAnalysisRequest request);
} 