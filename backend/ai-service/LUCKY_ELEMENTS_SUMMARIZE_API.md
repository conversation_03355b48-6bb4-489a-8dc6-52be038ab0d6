# 幸运元素文案简化接口

## 接口概述

**接口路径**: `/api/ai/text/summarize-lucky-elements`  
**请求方法**: POST  
**功能描述**: 使用DeepSeek AI简化总结服饰、配饰、妆容建议文案，生成简洁易懂的总结

## 技术实现

### 核心特性
- 基于Spring AI + DeepSeek Chat模型
- 智能文案简化和总结
- 支持中文语义理解和自然表达
- 包含完整的错误处理和回退机制

### AI模型配置
- 模型：DeepSeek Chat
- Temperature: 0.7（平衡创造性和准确性）
- 最大Token: 500（足够处理文案简化任务）

## 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| clothing | List<String> | 否 | 服饰建议列表 |
| accessories | List<String> | 否 | 配饰建议列表 |
| makeup | List<String> | 否 | 妆容建议列表 |

## 请求示例

```json
{
  "clothing": [
    "推荐丝绸、雪纺类柔软面料。",
    "上装可选丝质衬衫或针织款。",
    "颜色以淡雅色系为主，避免过于鲜艳的色彩。"
  ],
  "accessories": [
    "首饰宜选银色、白金系。",
    "建议佩戴简约的水滴造型耳环或项链。",
    "手表选择金属表带款式。"
  ],
  "makeup": [
    "今日妆容以清透水润为主。",
    "眼部可点缀淡蓝或淡紫色眼影。",
    "唇色选择自然粉色系。"
  ]
}
```

## 响应参数

| 字段名 | 类型 | 说明 |
|--------|------|------|
| code | Integer | 状态码，200表示成功 |
| message | String | 响应消息 |
| data | Object | 总结数据 |
| data.clothingSummary | String | 服饰建议总结 |
| data.accessoriesSummary | String | 配饰建议总结 |
| data.makeupSummary | String | 妆容建议总结 |

## 响应示例

### 成功响应
```json
{
  "code": 200,
  "message": "文案简化成功",
  "data": {
    "clothingSummary": "今日宜选择丝质面料，上装推荐衬衫或针织款，柔软质感有助提升运势。",
    "accessoriesSummary": "银色系首饰为佳，简约水滴造型设计能增强能量流动。",
    "makeupSummary": "清透水润妆感最适宜，眼部可点缀淡雅蓝紫色调。"
  }
}
```

### 错误响应
```json
{
  "code": 500,
  "message": "文案简化失败: AI服务暂时不可用",
  "data": {
    "clothingSummary": "今日服饰搭配以舒适自然为主。",
    "accessoriesSummary": "简约配饰最为适宜。",
    "makeupSummary": "自然清透妆容为佳。"
  }
}
```

## AI提示词策略

### 提示词构建
```
请将以下幸运元素建议简化为简洁易懂的总结文案，每类最多一句话：

服饰建议：
- 推荐丝绸、雪纺类柔软面料。
- 上装可选丝质衬衫或针织款。
- 颜色以淡雅色系为主，避免过于鲜艳的色彩。

配饰建议：
- 首饰宜选银色、白金系。
- 建议佩戴简约的水滴造型耳环或项链。
- 手表选择金属表带款式。

妆容建议：
- 今日妆容以清透水润为主。
- 眼部可点缀淡蓝或淡紫色眼影。
- 唇色选择自然粉色系。

请用简洁的语言总结以上建议，要求：
1. 每类建议（服饰、配饰、妆容）各用一句话总结
2. 保留关键的风格和颜色信息
3. 语言自然流畅，符合中文表达习惯
4. 返回JSON格式：{"clothingSummary": "...", "accessoriesSummary": "...", "makeupSummary": "..."}
```

### AI优化策略
1. **语义压缩**: 提取多条建议中的核心信息
2. **风格统一**: 保持总结文案的语言风格一致
3. **关键信息保留**: 确保重要的颜色、材质、风格信息不丢失
4. **自然表达**: 生成符合中文表达习惯的流畅文案

## 错误处理机制

### 多层级回退策略
1. **AI解析失败**: 如果无法解析AI返回的JSON，使用本地算法生成总结
2. **AI服务不可用**: 如果AI服务调用失败，直接使用本地算法
3. **输入为空**: 对于空的输入列表，提供预设的默认总结

### 本地算法备用方案
```java
// 本地简化逻辑
private String generateSummaryFromList(List<String> list, String defaultSummary) {
    if (list == null || list.isEmpty()) {
        return defaultSummary;
    }
    
    if (list.size() == 1) {
        return list.get(0);
    }
    
    // 组合前两条建议
    return list.get(0) + " " + list.get(1);
}
```

## 性能考虑

### Token使用优化
- 限制单次请求的建议条数，避免超长提示词
- 使用合适的temperature参数平衡创造性和准确性
- 设置合理的maxTokens限制，控制响应长度

### 缓存策略
- 对于相同的输入建议列表，可以考虑添加缓存机制
- 减少重复的AI调用，提升响应速度

### 监控指标
- AI调用成功率
- 响应时间分布
- 本地回退使用频率
- 用户满意度反馈

## 使用场景

### 主要用途
1. **用户服务集成**: 在今日能量信息保存时调用此接口
2. **实时总结**: 为前端提供简洁的穿搭建议展示
3. **内容优化**: 将冗长的AI生成建议优化为用户友好的格式

### 集成示例
```java
// 在UserDailyEnergyService中的使用
Map<String, Object> request = new HashMap<>();
request.put("clothing", luckyElements.getClothing());
request.put("accessories", luckyElements.getAccessories());
request.put("makeup", luckyElements.getMakeup());

Map<String, Object> response = aiServiceClient.summarizeLuckyElements(request);
if ((Integer) response.get("code") == 200) {
    Map<String, Object> data = (Map<String, Object>) response.get("data");
    // 保存总结数据到数据库
}
```

## 部署配置

### 必需配置
```yaml
spring:
  ai:
    deepseek:
      chat:
        enabled: true
        api-key: ${DEEPSEEK_API_KEY}
        base-url: https://api.deepseek.com
```

### 环境变量
- `DEEPSEEK_API_KEY`: DeepSeek API密钥

---

**创建时间**: 2025-01-24  
**相关模块**: ai-service, user-service  
**依赖服务**: Spring AI, DeepSeek API 