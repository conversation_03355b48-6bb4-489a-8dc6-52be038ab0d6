# AI服务 - Feign客户端迁移文档

## 概述

将AI服务中的文件上传功能从直接使用RestTemplate改为使用Spring Cloud OpenFeign，提升代码的可维护性和可测试性。

## 迁移内容

### 1. 添加依赖

在 `pom.xml` 中添加了以下依赖：

```xml
<!-- Spring Cloud OpenFeign -->
<dependency>
    <groupId>org.springframework.cloud</groupId>
    <artifactId>spring-cloud-starter-openfeign</artifactId>
</dependency>

<!-- Feign Form Support (文件上传支持) -->
<dependency>
    <groupId>io.github.openfeign.form</groupId>
    <artifactId>feign-form</artifactId>
    <version>3.8.0</version>
</dependency>
<dependency>
    <groupId>io.github.openfeign.form</groupId>
    <artifactId>feign-form-spring</artifactId>
    <version>3.8.0</version>
</dependency>
```

### 2. 创建Feign配置

创建了 `FeignConfig.java` 配置类：

- 支持文件上传的 `SpringFormEncoder`
- 适当的超时配置（文件上传需要更长时间）
- 重试策略配置
- 日志级别配置

### 3. 重构客户端接口

将 `FileServiceClient` 从普通接口改为Feign客户端：

- 使用 `@FeignClient` 注解
- 支持 `multipart/form-data` 文件上传
- 定义了完整的响应DTO类
- 配置了降级工厂类

### 4. 创建降级处理

创建了 `FileServiceClientFallbackFactory`：

- 当文件服务不可用时提供降级响应
- 生成临时URL作为备用方案
- 确保服务的高可用性

### 5. 服务层封装

创建了 `FileUploadService`：

- 封装Feign客户端调用
- 提供便捷的 `uploadImage()` 方法
- 统一错误处理和日志记录

### 6. 配置文件优化

在 `application.yml` 中添加了Feign配置：

```yaml
feign:
  client:
    config:
      default:
        connectTimeout: 10000
        readTimeout: 30000
        loggerLevel: BASIC
  httpclient:
    enabled: true
  compression:
    request:
      enabled: true
    response:
      enabled: true
```

## 架构优势

### 1. 代码简化
- 从手动构建HTTP请求简化为接口调用
- 自动序列化/反序列化
- 减少样板代码

### 2. 服务发现集成
- 自动与Nacos服务发现集成
- 负载均衡支持
- 服务健康检查

### 3. 降级处理
- 内置的回退机制
- 服务不可用时的优雅降级
- 提高系统稳定性

### 4. 配置管理
- 统一的超时配置
- 重试策略配置
- 压缩支持

### 5. 监控和日志
- 更好的调用链追踪
- 统一的错误处理
- 详细的请求/响应日志

## 使用方式

### 在DoubaoVisionClientImpl中的使用：

```java
// 注入FileUploadService
@Autowired
private FileUploadService fileUploadService;

// 使用便捷方法上传图片
String imageUrl = fileUploadService.uploadImage(imageFile, "ai-service");
```

### 支持的文件操作：

1. **文件上传**: `uploadFile(file, userId, category, accessLevel)`
2. **图片上传**: `uploadImage(imageFile, userId)` (便捷方法)
3. **获取文件信息**: `getFileInfo(fileId)`
4. **获取下载链接**: `getDownloadUrl(fileId, expiresIn)`
5. **删除文件**: `deleteFile(fileId, force)`

## 降级策略

**重要更新：移除虚假降级URL**

为确保数据真实性，已移除所有虚假URL生成逻辑：

### 失败处理原则：
1. **真实失败就是失败** - 不捏造任何虚假数据
2. **文件上传失败时直接抛出异常** - 终止后续处理流程
3. **明确的错误信息** - 让调用方了解真实的失败原因

### 当前处理流程：

1. **正常情况**: 文件服务可用 → 上传成功 → 返回真实URL
2. **文件服务403/权限错误**: 直接抛出异常，终止流程
3. **文件服务不可用**: FallbackFactory返回明确的503错误，不提供虚假URL

### 错误处理层级：

1. **FileServiceClient**: 使用Feign调用file-service
2. **FallbackFactory**: 当file-service完全不可用时，返回503错误（不提供虚假URL）
3. **FileUploadService**: 只接受真实的成功响应，其他情况直接抛出异常
4. **DoubaoVisionClientImpl**: 文件上传失败时，直接让异常向上抛出

**移除的问题逻辑**：
- ❌ 生成临时降级URL
- ❌ 使用emergency_fallback_xxx格式的虚假URL
- ❌ 在文件上传失败后继续执行AI识别

**正确的行为**：
- ✅ 文件上传失败时直接返回错误
- ✅ 明确的错误信息，不误导用户
- ✅ 保证数据的真实性和一致性

## 测试验证

已通过Maven编译验证，所有依赖和配置正确无误。建议进行以下测试：

1. 正常文件上传流程测试
2. 文件服务不可用时的降级测试
3. 大文件上传的超时测试
4. 并发上传的性能测试

## 注意事项

1. 确保file-service已正确注册到Nacos
2. 文件上传接口路径为 `/files/upload`
3. 支持的文件格式: jpeg, jpg, png, gif, webp
4. 最大文件大小: 10MB
5. 降级URL仅用于应急场景，需要实际文件存储支持 