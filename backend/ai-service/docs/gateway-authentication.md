# 微服务网关认证机制

## 概述

本项目采用网关统一认证的微服务架构模式：
- **网关层**：验证JWT token，提取用户信息
- **服务层**：信任网关传递的header，无需重复验证JWT

## 网关传递Header规范

网关验证JWT token成功后，向下游服务传递以下header：

```
X-User-Id: {用户ID}
X-User-Nickname: {用户昵称}（可选）
X-User-Phone: {用户电话}（可选）
X-User-Roles: {用户角色，逗号分隔}（可选）
```

## 服务端配置

### 1. 安全配置 (SecurityConfig)

```java
@Configuration
@EnableWebSecurity
public class SecurityConfig {
    
    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        return http
            .csrf(AbstractHttpConfigurer::disable)
            .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
            .authorizeHttpRequests(authz -> authz
                // 公开端点
                .requestMatchers("/actuator/**", "/swagger-ui/**", "/v3/api-docs/**").permitAll()
                // 其他端点需要网关认证
                .anyRequest().authenticated()
            )
            .addFilterBefore(gatewayUserFilter, UsernamePasswordAuthenticationFilter.class)
            .build();
    }
}
```

### 2. 网关用户过滤器 (GatewayUserFilter)

- 读取 `X-User-Id` header
- 创建Spring Security认证对象
- 设置到SecurityContext供业务代码使用

### 3. 业务代码获取当前用户

```java
@RestController
public class WeatherController {
    
    @PostMapping("/weather")
    public ApiResponse<WeatherResponse> getWeather(@RequestBody WeatherRequest request) {
        // 获取当前认证用户ID
        String userId = SecurityContextHolder.getContext()
            .getAuthentication()
            .getName();
        
        // 业务逻辑处理
        WeatherResponse response = weatherService.getWeather(request, userId);
        return ApiResponse.success(response);
    }
}
```

## 网关端实施建议

### 1. JWT验证成功后提取用户信息

```java
// 网关端伪代码
public class GatewayAuthFilter {
    
    public void doFilter(ServerHttpRequest request, ServerHttpResponse response) {
        String token = extractToken(request);
        
        if (isValidToken(token)) {
            UserInfo userInfo = parseToken(token);
            
                         // 向下游服务传递用户信息
             ServerHttpRequest mutatedRequest = request.mutate()
                 .header("X-User-Id", userInfo.getUserId())
                 .header("X-User-Nickname", userInfo.getNickname())
                 .header("X-User-Phone", userInfo.getPhone())
                 .header("X-User-Roles", String.join(",", userInfo.getRoles()))
                 .build();
                
            // 继续转发请求
            chain.filter(mutatedRequest, response);
        } else {
            // 返回401未授权
            response.setStatusCode(HttpStatus.UNAUTHORIZED);
        }
    }
}
```

### 2. 路由配置示例

```yaml
spring:
  cloud:
    gateway:
      routes:
        - id: ai-service
          uri: http://ai-service:8084
          predicates:
            - Path=/api/ai/**
          filters:
            - StripPrefix=2  # 去掉 /api/ai 前缀
            - name: AuthenticationFilter  # 添加认证过滤器
```

## 安全注意事项

1. **内网通信**：确保网关与下游服务之间是内网通信，防止header被伪造
2. **Header验证**：可以考虑添加签名验证header的完整性
3. **日志记录**：记录认证相关的关键操作日志
4. **异常处理**：网关认证失败时返回统一的错误响应

## 测试

### 带认证Header的请求
```bash
curl -H "X-User-Id: user123" \
     -H "X-User-Nickname: testuser" \
     -H "X-User-Roles: user,member" \
     "http://localhost:8084/weather?city=北京"
```

### 不带认证Header的请求（应返回403）
```bash
curl "http://localhost:8084/weather?city=北京"
``` 