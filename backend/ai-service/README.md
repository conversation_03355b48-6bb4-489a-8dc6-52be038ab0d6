# AI Service - StylishLink AI服务

## 项目简介

AI服务是StylishLink项目的核心AI能力模块，提供图像识别、风格分析、色彩分析、AI对话、视频生成、形象评估、AI评分等智能功能。

## 技术栈

- **框架**: Spring Boot 2.7.x + Spring Cloud 2022.0.0
- **数据库**: MySQL 8.0 + MyBatis Plus 3.5.3
- **缓存**: Redis
- **服务发现**: Nacos
- **AI接口**: OpenAI API (可扩展其他AI服务)
- **构建工具**: Maven

## 功能模块

### 1. 图像识别 (Image Recognition)
- 衣物识别：识别服装类别、颜色、风格、材质等
- 饰品识别：识别配饰类型、风格、材质等
- 支持多种图片格式，最大10MB

### 2. 风格分析 (Style Analysis)
- 图片风格分析：分析图片中的时尚风格分布
- 搭配风格分析：分析整体搭配的风格协调性
- 色彩分析：主色调提取、色彩搭配建议

### 3. AI对话 (AI Chat)
- 通用对话：基础AI对话功能
- 时尚建议：专业时尚搭配建议
- 造型师咨询：个性化造型指导

### 4. 视频生成 (Video Generation)
- 根据搭配生成展示视频
- 多种模板选择
- 异步任务处理

### 5. 形象评估 (Appearance Evaluation)
- 整体形象评分
- 分项评估（风格、色彩、合身度等）
- 改进建议

### 6. AI评分 (AI Rating)
- 搭配评分：对用户搭配进行智能评分
- 推荐评分：对推荐结果进行质量评估

## API接口

### 图像识别
```
POST /api/ai/recognize
POST /api/ai/recognize/clothing
POST /api/ai/recognize/accessory
```

### AI对话
```
POST /api/ai/chat
POST /api/ai/fashion-advice
POST /api/ai/stylist-consultation
```

### 风格分析
```
POST /api/ai/style
POST /api/ai/color
```

### 形象评估
```
POST /api/ai/appearance
POST /api/ai/rate
```

### 视频生成
```
POST /api/ai/video
GET /api/ai/video/{taskId}
```

## 数据库设计

### 核心表结构
- `ai_recognition_result`: 图像识别结果
- `ai_style_analysis`: 风格分析结果
- `ai_chat_record`: AI对话记录
- `ai_video_generation_task`: 视频生成任务
- `ai_appearance_evaluation`: 形象评估记录
- `ai_rating_record`: AI评分记录

## 配置说明

### 环境变量
```bash
# OpenAI配置
AI_OPENAI_API_KEY=your_openai_api_key
AI_OPENAI_BASE_URL=https://api.openai.com
AI_OPENAI_MODEL=gpt-3.5-turbo

# 数据库配置
DB_HOST=************
DB_PORT=3306
DB_NAME=stylishlink_ai
DB_USERNAME=root
DB_PASSWORD=123456

# Redis配置
REDIS_HOST=************
REDIS_PORT=6379
REDIS_DATABASE=2
```

### Nacos配置
服务会自动从Nacos配置中心获取配置，配置文件：
- `ai-service-dev.yaml`: 开发环境配置
- `common-dev.yaml`: 公共配置

## 部署说明

### 1. 环境准备
- JDK 17+
- MySQL 8.0+
- Redis 6.0+
- Nacos 2.0+

### 2. 数据库初始化
```sql
-- 创建数据库
CREATE DATABASE stylishlink CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 执行建表脚本
source src/main/resources/sql/ai_service_schema.sql
```

## 开发指南

### 1. 项目结构
```
src/main/java/com/stylishlink/ai/
├── controller/          # 控制器层
├── service/            # 服务层
├── mapper/             # 数据访问层
├── entity/             # 实体类
├── dto/                # 数据传输对象
├── client/             # 外部服务客户端
├── config/             # 配置类
└── exception/          # 异常处理
```

### 2. 开发规范
- 遵循RESTful API设计原则
- 使用统一的响应格式 `ApiResponse<T>`
- 完善的异常处理和日志记录
- 参数验证使用JSR-303注解
- 数据库操作使用MyBatis Plus

### 3. 测试
```bash
# 运行单元测试
mvn test

# 运行集成测试
mvn verify
```

## 监控与运维

### 1. 健康检查
- `/actuator/health`: 服务健康状态
- `/actuator/info`: 服务信息
- `/actuator/metrics`: 性能指标

### 2. 日志
- 应用日志：`logs/ai-service.log`
- 错误日志：`logs/error.log`
- 访问日志：通过网关统一记录

### 3. 性能监控
- JVM指标监控
- 数据库连接池监控
- Redis连接监控
- AI服务调用监控

## 扩展说明

### 1. 新增AI服务提供商
实现 `AiClient` 接口，添加新的AI服务客户端：
```java
@Component
public class CustomAiClientImpl implements AiClient {
    // 实现接口方法
}
```

### 2. 新增识别类型
在 `AiRecognitionService` 中添加新的识别方法，更新数据库表结构。

### 3. 新增评估维度
扩展 `ai_appearance_evaluation` 表结构，添加新的评估字段。

## 常见问题

### 1. AI服务调用失败
- 检查API密钥配置
- 确认网络连接
- 查看错误日志

### 2. 数据库连接问题
- 检查数据库配置
- 确认数据库服务状态
- 验证连接参数

### 3. 文件上传失败
- 检查文件大小限制
- 确认文件格式支持
- 查看磁盘空间

## 版本历史

- v1.0.0: 初始版本，包含基础AI功能
- 后续版本将持续优化和扩展功能

## 联系方式

如有问题请联系开发团队或提交Issue。 