# Maven 构建指南

## 常用Maven命令

### 开发环境推荐命令

#### 1. 清理并安装到本地仓库（推荐）
```bash
mvn clean install
```
**用途**：
- 清理target目录
- 编译源代码
- 运行测试
- 打包JAR文件
- 安装到本地Maven仓库

#### 2. 仅编译（快速验证）
```bash
mvn compile
```

#### 3. 运行测试
```bash
mvn test
```

#### 4. 打包但不安装
```bash
mvn package
```

### 避免使用的命令

#### ❌ mvn clean deploy
**问题**：尝试部署到远程仓库，但项目没有配置`distributionManagement`

**错误信息**：
```
repository element was not specified in the POM inside distributionManagement element
```

**解决方案**：使用`mvn clean install`替代

## Maven生命周期说明

### 主要阶段顺序
1. **validate** - 验证项目结构
2. **compile** - 编译源代码
3. **test** - 运行单元测试
4. **package** - 打包成JAR/WAR
5. **verify** - 运行集成测试
6. **install** - 安装到本地仓库
7. **deploy** - 部署到远程仓库

### 阶段执行规则
- 执行某个阶段会自动执行之前的所有阶段
- `mvn install` = validate + compile + test + package + install
- `mvn deploy` = validate + compile + test + package + install + deploy

## 项目特定配置

### AI服务构建
```bash
cd backend/ai-service
mvn clean install
```

### 所有服务批量构建
```bash
cd backend
mvn clean install
```

### 跳过测试（开发阶段）
```bash
mvn clean install -DskipTests
```

## 如果需要Deploy功能

如果您确实需要部署到远程仓库，需要在POM文件中添加：

```xml
<distributionManagement>
    <repository>
        <id>releases</id>
        <name>Release Repository</name>
        <url>http://your-nexus-server/repository/maven-releases/</url>
    </repository>
    <snapshotRepository>
        <id>snapshots</id>
        <name>Snapshot Repository</name>
        <url>http://your-nexus-server/repository/maven-snapshots/</url>
    </snapshotRepository>
</distributionManagement>
```

并在`~/.m2/settings.xml`中配置对应的认证信息：

```xml
<servers>
    <server>
        <id>releases</id>
        <username>your-username</username>
        <password>your-password</password>
    </server>
    <server>
        <id>snapshots</id>
        <username>your-username</username>
        <password>your-password</password>
    </server>
</servers>
```

## 注意事项

1. **开发环境建议**：使用`mvn clean install`
2. **CI/CD环境**：根据需要使用`mvn clean deploy`
3. **快速构建**：添加`-DskipTests`跳过测试
4. **并行构建**：添加`-T 4`使用4个线程并行构建

## 警告信息处理

### unchecked warnings
如果看到以下警告：
```
某些输入文件使用了未经检查或不安全的操作。
有关详细信息, 请使用 -Xlint:unchecked 重新编译。
```

可以添加编译参数查看详细信息：
```bash
mvn clean install -Dmaven.compiler.showWarnings=true -Dmaven.compiler.showDeprecation=true
``` 