# Gateway HTTPS 配置使用说明

## 配置模式

### 模式1：仅HTTP模式（默认）
```yaml
server:
  port: 8080
# SSL配置不存在或disabled
```

**特点**：
- 只使用HTTP协议
- 端口：8080
- 不启动重定向服务器

### 模式2：HTTP+HTTPS双端口模式
```yaml
server:
  port: 8443  # HTTPS主端口
  http:
    port: 8080  # HTTP重定向端口
  ssl:
    enabled: true
    # ... SSL配置
```

**特点**：
- HTTPS主端口：8443
- HTTP重定向端口：8080
- HTTP请求自动重定向到HTTPS

## 切换到HTTPS模式

### 方法1：修改application-local.yml
取消注释HTTPS配置部分：

```yaml
server:
  port: 8443  # HTTPS主端口
  http:
    port: 8080  # HTTP重定向端口
  ssl:
    enabled: true
    key-store: classpath:keystore.p12
    key-store-password: changeit
    key-store-type: PKCS12
    key-alias: gateway
```

### 方法2：使用HTTPS配置文件
启动时添加profiles参数：
```bash
java -jar gateway-service.jar --spring.profiles.active=local,https
```

## SSL证书

### 自签名证书（开发环境）
项目已包含自签名证书：`keystore.p12`
- 密码：changeit
- 别名：gateway
- 主机名：localhost

### 生产环境证书
替换为正式SSL证书：
```yaml
server:
  ssl:
    key-store: /path/to/your/certificate.p12
    key-store-password: your-password
    key-alias: your-alias
```

## 测试验证

### HTTP模式测试
```bash
curl http://localhost:8080/api/user/fortune/complete-reading?date=2025-06-01
```

### HTTPS模式测试
```bash
# HTTPS访问
curl -k https://localhost:8443/api/user/fortune/complete-reading?date=2025-06-01

# HTTP访问（应该被重定向）
curl -v http://localhost:8080/api/user/fortune/complete-reading?date=2025-06-01
```

## 故障排除

### 常见问题

1. **SSL握手失败**
   - 检查SSL配置是否正确
   - 确认证书文件存在
   - 验证密码是否正确

2. **重定向循环**
   - 检查HTTP和HTTPS端口配置
   - 确认SslConfig只在SSL启用时运行

3. **证书不信任**
   - 开发环境：使用`-k`参数忽略证书验证
   - 生产环境：使用正式CA颁发的证书

### 日志检查
```yaml
logging:
  level:
    com.stylishlink.gateway.config.SslConfig: DEBUG
    org.springframework.boot.web.embedded.netty: DEBUG
```

## 当前状态

默认配置为**仅HTTP模式**，如需HTTPS支持请按上述方法切换配置。 