# Gateway Service 启动脚本使用说明

## 脚本功能

`start-gateway.sh` 是 StylishLink Gateway Service 的 Linux/macOS 启动管理脚本，提供完整的服务生命周期管理功能。

## 使用方法

### 基本命令

```bash
# 启动服务
./start-gateway.sh start

# 停止服务
./start-gateway.sh stop

# 重启服务
./start-gateway.sh restart

# 查看状态
./start-gateway.sh status

# 查看最近100行日志
./start-gateway.sh logs

# 实时查看日志 (Ctrl+C 退出)
./start-gateway.sh tail
```

### 使用示例

```bash
# 1. 启动服务
$ ./start-gateway.sh start
[INFO] 启动 gateway-service...
[INFO] JAR文件: /path/to/target/gateway-service-1.0.0-SNAPSHOT.jar
[INFO] 日志文件: /path/to/logs/gateway-service.log
[INFO] Spring配置: local
[SUCCESS] gateway-service 启动成功 (PID: 12345)

# 2. 查看状态
$ ./start-gateway.sh status
[INFO] gateway-service 状态检查
----------------------------------------
状态: 运行中
PID: 12345
启动时间: Mon Jun  9 10:47:37 2025
内存使用: 589.2 MB
CPU使用: 0.0%

# 3. 实时查看日志
$ ./start-gateway.sh tail
[INFO] 实时查看日志 (Ctrl+C 退出):
----------------------------------------
10:47:51.815 [main] INFO  c.s.g.GatewayServiceApplication - Started GatewayServiceApplication...

# 4. 停止服务
$ ./start-gateway.sh stop
[INFO] 停止 gateway-service...
[SUCCESS] gateway-service 已停止
```

## 配置说明

### 默认配置

- **Java 内存**: 初始512MB，最大1024MB
- **GC策略**: G1垃圾收集器
- **Spring Profile**: local
- **日志目录**: `./logs/`
- **PID文件**: `./gateway-service.pid`

### 自定义配置

修改脚本中的配置变量：

```bash
# JVM 配置
JAVA_OPTS="-Xms512m -Xmx1024m"

# Spring 配置
SPRING_PROFILES_ACTIVE="local"  # 可改为 dev, test, prod
```

## 目录结构

```
gateway-service/
├── start-gateway.sh                        # 启动脚本
├── gateway-service-1.0.0-SNAPSHOT.jar     # 可执行JAR文件
├── target/                                 # Maven构建目录
│   └── gateway-service-1.0.0-SNAPSHOT.jar # 构建生成的JAR文件
├── logs/                                   # 日志目录（自动创建）
│   ├── gateway-service.log                 # 应用日志
│   └── *.hprof                            # 内存转储文件（如果发生OOM）
└── gateway-service.pid                     # 进程ID文件
```

## 常见问题

### 1. 启动失败

**现象**: `[ERROR] 服务启动超时`

**解决方案**:
```bash
# 查看详细日志
./start-gateway.sh logs

# 常见原因:
# - Nacos 连接失败 (检查 ************:8848 是否可访问)
# - 端口冲突 (8080端口被占用)
# - Java 版本不兼容 (需要 Java 17+)
```

### 2. JAR 文件不存在

**现象**: `[ERROR] JAR 文件不存在`

**解决方案**:
```bash
# 重新构建项目
mvn clean package -DskipTests

# 将JAR文件复制到当前目录
cp target/gateway-service-1.0.0-SNAPSHOT.jar .
```

### 3. 权限问题

**现象**: `Permission denied`

**解决方案**:
```bash
# 添加执行权限
chmod +x start-gateway.sh
```

## 生产环境部署

### 1. 系统服务配置

创建 systemd 服务文件 `/etc/systemd/system/gateway-service.service`:

```ini
[Unit]
Description=StylishLink Gateway Service
After=network.target

[Service]
Type=forking
User=stylishlink
WorkingDirectory=/opt/stylishlink/gateway-service
ExecStart=/opt/stylishlink/gateway-service/start-gateway.sh start
ExecStop=/opt/stylishlink/gateway-service/start-gateway.sh stop
ExecReload=/opt/stylishlink/gateway-service/start-gateway.sh restart
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

### 2. 启用系统服务

```bash
# 重载系统配置
sudo systemctl daemon-reload

# 启用开机自启
sudo systemctl enable gateway-service

# 启动服务
sudo systemctl start gateway-service

# 查看状态
sudo systemctl status gateway-service
```

### 3. 日志轮转配置

创建 `/etc/logrotate.d/gateway-service`:

```
/opt/stylishlink/gateway-service/logs/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 stylishlink stylishlink
    postrotate
        /opt/stylishlink/gateway-service/start-gateway.sh restart > /dev/null 2>&1 || true
    endscript
}
```

## 监控建议

### 1. 健康检查

```bash
# 定期检查服务状态
*/5 * * * * /opt/stylishlink/gateway-service/start-gateway.sh status > /dev/null || /opt/stylishlink/gateway-service/start-gateway.sh start
```

### 2. 性能监控

- 内存使用率监控
- CPU使用率监控
- 响应时间监控
- 错误率监控

推荐使用 Prometheus + Grafana 进行可视化监控。

## 技术支持

如遇到问题，请检查：

1. 日志文件：`./logs/gateway-service.log`
2. JVM堆转储：`./logs/*.hprof`
3. 系统资源：内存、CPU、磁盘空间
4. 网络连接：Nacos、数据库、Redis等依赖服务

---

**版本**: 1.0.0  
**更新日期**: 2025-06-09  
**维护团队**: StylishLink后端团队 