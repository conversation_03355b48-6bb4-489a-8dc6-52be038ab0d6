package com.stylishlink.gateway.filter;

import com.stylishlink.common.util.JwtUtil;
import com.stylishlink.gateway.config.SecurityWhitelistProperties;
import io.jsonwebtoken.Claims;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.StringUtils;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.util.Arrays; 
import java.util.List;
import java.util.stream.Collectors;

@Component
public class AuthenticationFilter implements GlobalFilter, Ordered {

    private static final Logger logger = LoggerFactory.getLogger(AuthenticationFilter.class);

    private final JwtUtil jwtUtil;
    private final SecurityWhitelistProperties whitelistProperties;
    private final AntPathMatcher pathMatcher;

    // HTTP Header names for passing user info
    // Token相关常量 (从用户输入整合)
    private static final String AUTHORIZATION_HEADER = "Authorization"; // HttpHeaders.AUTHORIZATION 已经使用
    private static final String BEARER_PREFIX = "Bearer "; // 已在代码中使用 "Bearer ".substring(7)
    private static final String HEADER_USER_ID = "X-User-Id";
    private static final String HEADER_USER_ROLES = "X-User-Roles";
    private static final String USER_PHONE_HEADER = "X-User-Phone";
    private static final String USER_NICKNAME_HEADER = "X-User-Nickname";


    // JWT Claim name for roles (需要与 user-service 生成JWT时保持一致)
    private static final String CLAIM_ROLES_NAME = "roles";
    // JWT Claim names for additional info (需要与 user-service 生成JWT时保持一致 - 请用户确认!)
    private static final String CLAIM_PHONE_KEY = "phone"; 
    private static final String CLAIM_NICKNAME_KEY = "nickname";

    @Autowired
    public AuthenticationFilter(JwtUtil jwtUtil, SecurityWhitelistProperties whitelistProperties) {
        this.jwtUtil = jwtUtil;
        this.whitelistProperties = whitelistProperties;
        this.pathMatcher = new AntPathMatcher();
    }

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        String path = request.getURI().getPath();

        // 检查是否是公共路径
        if (isPublicEndpoint(path)) {
            logger.debug("Public path [{}], skipping authentication.", path);
            return chain.filter(exchange);
        }
        logger.info("Protected path [{}], performing authentication.", path);

        String authHeader = request.getHeaders().getFirst(HttpHeaders.AUTHORIZATION);

        if (!StringUtils.hasText(authHeader) || !authHeader.startsWith("Bearer ")) {
            logger.warn("Missing or invalid Authorization header for path: {}", path);
            return unauthorizedResponse(exchange, "缺少或无效的Authorization请求头");
        }

        String token = authHeader.substring(7); // 移除 "Bearer " 前缀

        try {
            if (!jwtUtil.validateToken(token)) {
                logger.warn("JWT token validation failed for path: {}", path);
                return unauthorizedResponse(exchange, "无效或已过期的JWT令牌");
            }

            String userId = jwtUtil.getSubjectFromToken(token);
            List<String> roles = null;
            // New variables for phone and nickname
            String userPhone = null;
            String userNickname = null;

            try {
                Claims claims = jwtUtil.getAllClaimsFromToken(token);
                Object rolesClaim = claims.get(CLAIM_ROLES_NAME);
                if (rolesClaim instanceof List) {
                    roles = ((List<?>) rolesClaim).stream()
                            .filter(obj -> obj instanceof String)
                            .map(obj -> (String) obj)
                            .collect(Collectors.toList());
                } else if (rolesClaim instanceof String && StringUtils.hasText((String) rolesClaim)) {
                    roles = Arrays.asList(((String) rolesClaim).split(","));
                } else if (rolesClaim != null) {
                    logger.warn("Roles claim '{}' is present but is not a List or a String for user '{}'. Type: {}", CLAIM_ROLES_NAME, userId, rolesClaim.getClass().getName());
                }

                // Extract phone
                Object phoneClaim = claims.get(CLAIM_PHONE_KEY);
                if (phoneClaim instanceof String) {
                    userPhone = (String) phoneClaim;
                } else if (phoneClaim != null) {
                    logger.warn("Phone claim '{}' is present but is not a String for user '{}'. Type: {}", CLAIM_PHONE_KEY, userId, phoneClaim.getClass().getName());
                }

                // Extract nickname
                Object nicknameClaim = claims.get(CLAIM_NICKNAME_KEY);
                if (nicknameClaim instanceof String) {
                    userNickname = (String) nicknameClaim;
                } else if (nicknameClaim != null) {
                    logger.warn("Nickname claim '{}' is present but is not a String for user '{}'. Type: {}", CLAIM_NICKNAME_KEY, userId, nicknameClaim.getClass().getName());
                }

            } catch (Exception e) {
                logger.warn("Could not extract roles or other claims (phone, nickname) from token for user '{}', proceeding with available info. Error: {}", userId, e.getMessage());
            }

            logger.info("Token validated for user: {}, roles: {}, phone: {}, nickname: {} for path: {}", userId, roles, userPhone, userNickname, path);

            ServerHttpRequest.Builder requestBuilder = request.mutate();
            requestBuilder.header(HEADER_USER_ID, userId);

            if (roles != null && !roles.isEmpty()) {
                requestBuilder.header(HEADER_USER_ROLES, String.join(",", roles));
            }

            // Add new headers for phone and nickname
            if (StringUtils.hasText(userPhone)) {
                requestBuilder.header(USER_PHONE_HEADER, userPhone);
            }

            if (StringUtils.hasText(userNickname)) {
                requestBuilder.header(USER_NICKNAME_HEADER, userNickname);
            }

            ServerHttpRequest modifiedRequest = requestBuilder.build();
            return chain.filter(exchange.mutate().request(modifiedRequest).build());

        } catch (Exception e) {
            logger.error("Unexpected error during JWT processing for path: {}: {}", path, e.getMessage(), e);
            return unauthorizedResponse(exchange, "JWT处理异常");
        }
    }

    private boolean isPublicEndpoint(String path) {
        List<String> allowedPaths = whitelistProperties.getAllowedPaths();
        if (allowedPaths == null || allowedPaths.isEmpty()) {
            return false; // No whitelist configured, all paths protected by default
        }
        return allowedPaths.stream().anyMatch(pattern -> pathMatcher.match(pattern, path));
    }

    private Mono<Void> unauthorizedResponse(ServerWebExchange exchange, String message) {
        ServerHttpResponse response = exchange.getResponse();
        response.setStatusCode(HttpStatus.UNAUTHORIZED);
        // For a more detailed JSON response, you can uncomment and adapt the following:
        // response.getHeaders().setContentType(MediaType.APPLICATION_JSON);
        // String jsonPayload = String.format("{"timestamp": "%s", "status": 401, "error": "Unauthorized", "message": "%s", "path": "%s"}",
        //        java.time.LocalDateTime.now(), message, exchange.getRequest().getURI().getPath());
        // reactor.core.publisher.DataBuffer buffer = response.bufferFactory().wrap(jsonPayload.getBytes(java.nio.charset.StandardCharsets.UTF_8));
        // return response.writeWith(Mono.just(buffer));
        return response.setComplete();
    }

    @Override
    public int getOrder() {
        // Ensures this filter runs before routing filters but after some other high-priority filters if any.
        return -1; // A common value for authentication/authorization filters.
    }
} 