package com.stylishlink.gateway.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.servers.Server;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.core.models.GroupedOpenApi;
import org.springdoc.core.properties.AbstractSwaggerUiConfigProperties;
import org.springdoc.core.properties.SwaggerUiConfigProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.gateway.route.RouteDefinition;
import org.springframework.cloud.gateway.route.RouteDefinitionLocator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.function.client.WebClient;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * Swagger聚合配置
 * 从Gateway路由动态发现和聚合后端服务的Swagger API文档
 */
@Slf4j
@Configuration
public class SwaggerConfig {

    private final RouteDefinitionLocator routeDefinitionLocator;
    @Value("${server.port:8080}")
    private String serverPort;
    
    @Value("${spring.application.name}")
    private String applicationName;

    @Value("${springdoc.api-docs.path:/v3/api-docs/gateway}")
    private String gatewayApiDocsPath;

    @Autowired
    public SwaggerConfig(RouteDefinitionLocator routeDefinitionLocator) {
        this.routeDefinitionLocator = routeDefinitionLocator;
        log.info("SwaggerConfig initialized with RouteDefinitionLocator.");
    }

    /**
     * 配置聚合的OpenAPI信息
     */
    @Bean
    public OpenAPI customOpenAPI() {
        log.info("Creating customOpenAPI bean...");
        return new OpenAPI()
                .info(new Info()
                        .title("StylishLink 微服务API文档")
                        .description("StylishLink时尚搭配助手微信小程序后端API文档聚合")
                        .version("1.0.0")
                        .contact(new Contact()
                                .name("StylishLink团队")
                                .email("<EMAIL>"))
                        .license(new License()
                                .name("MIT")
                                .url("https://opensource.org/licenses/MIT")))
                .addServersItem(new Server()
                        .url("http://localhost:" + serverPort)
                        .description("本地开发环境"))
                .addServersItem(new Server()
                        .url("https://api.stylishlink.com")
                        .description("生产环境"));
    }

    /**
     * 配置Gateway自身的API分组
     */
    @Bean
    public GroupedOpenApi gatewayApi() {
        log.info("Creating gatewayApi bean for Gateway itself...");
        // This GroupedOpenApi is for the Gateway's own endpoints, if any are exposed via SpringDoc.
        // The actual API docs for the gateway are typically at `gatewayApiDocsPath` (e.g., /v3/api-docs/gateway)
        // which is configured in application.yml and referred to by SwaggerUrl for the UI dropdown.
        return GroupedOpenApi.builder()
                .group(applicationName) // Use application name for gateway's group, or a fixed "gateway" string
                .displayName("网关服务 (Gateway)")
                .pathsToMatch("/api/gateway/**") // Example: if gateway has its own /api/gateway/... endpoints
                .build();
    }

    /**
     * 动态创建GroupedOpenApi Beans for each routed service 
     * AND configures SwaggerUiConfigProperties to include all services in the UI dropdown.
     */
    @Bean
    public List<GroupedOpenApi> apis(SwaggerUiConfigProperties swaggerUiConfigProperties) {
        log.info("--- Executing apis() method to discover services, create GroupedOpenApi list, and configure Swagger UI URLs ---");
        
        List<GroupedOpenApi> groups = new ArrayList<>();
        Set<AbstractSwaggerUiConfigProperties.SwaggerUrl> finalUrls = new HashSet<>();

        // 1. Add Gateway's own API to the UI dropdown
        // The name here ("gateway" or applicationName) should match a GroupedOpenApi group if you want to link them,
        // or it can be a unique name for the dropdown entry.
        finalUrls.add(new AbstractSwaggerUiConfigProperties.SwaggerUrl(applicationName, gatewayApiDocsPath, "网关服务 (Gateway)"));
        log.info("apis() - Added Gateway's own API to Swagger UI list: Name: {}, URL: {}, DisplayName: 网关服务 (Gateway)", applicationName, gatewayApiDocsPath);

        // 2. Discover and process routed services
        List<RouteDefinition> definitions = routeDefinitionLocator.getRouteDefinitions().collectList().block();

        if (definitions != null && !definitions.isEmpty()) {
            log.info("apis() - Found {} route definitions from RouteDefinitionLocator.", definitions.size());
            int servicesAddedCount = 0;
            for (RouteDefinition definition : definitions) {
                String routeId = definition.getId(); // Example: "user-service" or "ReactiveCompositeDiscoveryClient_user-service"
                String serviceName = routeId.startsWith("ReactiveCompositeDiscoveryClient_")
                        ? routeId.substring("ReactiveCompositeDiscoveryClient_".length())
                        : routeId;

                // Ensure it's a service we want to include (e.g., ends with -service and is not the gateway itself)
                if (serviceName.endsWith("-service") && !serviceName.equalsIgnoreCase(applicationName)) {
                    log.info("apis() - Processing route definition for service: {}", serviceName);
                    String servicePathPrefix = getServicePathPrefix(serviceName);
                    String apiUrl = "/api/" + servicePathPrefix + "/v3/api-docs"; // Path for the service's OpenAPI docs via gateway
                    String displayName = getServiceDisplayName(serviceName) + " (" + servicePathPrefix + ")";

                    // Create GroupedOpenApi for the service
                    // The group name (serviceName) is important as SpringDoc might use it to find the API definition.
                    groups.add(GroupedOpenApi.builder()
                            .group(serviceName) // Group name, e.g., "user-service"
                            .displayName(displayName) // Display name for this group if shown elsewhere
                            .pathsToMatch("/api/" + servicePathPrefix + "/**") // Paths this group covers through the gateway
                            .build());
                    log.info("apis() - Created GroupedOpenApi for: Group={}, DisplayName={}, Paths=/api/{}/ **", serviceName, displayName, servicePathPrefix);

                    // Create SwaggerUrl for the UI dropdown
                    // The name (serviceName) is used as the key in the dropdown.
                    // The url (apiUrl) is where the UI will fetch the OpenAPI spec from.
                    // The displayName (displayName) is what the user sees in the dropdown.
                    finalUrls.add(new AbstractSwaggerUiConfigProperties.SwaggerUrl(serviceName, apiUrl, displayName));
                    servicesAddedCount++;
                    log.info("apis() - Added to Swagger UI list: Service/Name={}, URL={}, DisplayName={}", serviceName, apiUrl, displayName);
                } else {
                    log.debug("apis() - Skipping route definition: ID='{}', Extracted ServiceName='{}' (not a target service or is gateway itself).", routeId, serviceName);
                }
            }
            if (servicesAddedCount > 0) {
                log.info("apis() - Successfully processed and added {} service(s) to GroupedOpenApi list and Swagger UI URL list.", servicesAddedCount);
            } else {
                log.warn("apis() - No additional services (matching criteria) were found in route definitions to add.");
            }
        } else {
            log.warn("apis() - No route definitions found from RouteDefinitionLocator.");
        }

        // 3. Set the final complete list of URLs for Swagger UI
        if (finalUrls.isEmpty()) {
            log.warn("apis() - Final URL list for Swagger UI is empty. UI dropdown might be empty or show defaults.");
        }
        swaggerUiConfigProperties.setUrls(finalUrls);
        log.info("apis() - Final and complete Swagger UI URLs set in SwaggerUiConfigProperties: {}", finalUrls);
        
        return groups;
    }

    /**
     * 获取服务显示名称 (Helper method)
     */
    private String getServiceDisplayName(String serviceName) {
        return switch (serviceName) {
            case "user-service" -> "用户服务";
            case "wardrobe-service" -> "衣橱服务";
            case "recommendation-service" -> "推荐服务";
            case "ai-service" -> "AI服务";
            case "operation-service" -> "运营服务";
            case "social-service" -> "社交服务";
            case "payment-service" -> "支付服务";
            case "file-service" -> "文件服务";
            default -> serviceName;
        };
    }

    /**
     * 获取服务路径前缀 (Helper method)
     */
    private String getServicePathPrefix(String serviceName) {
        return switch (serviceName) {
            case "user-service" -> "user";
            case "wardrobe-service" -> "wardrobe";
            case "recommendation-service" -> "recommendation";
            case "ai-service" -> "ai";
            case "operation-service" -> "operation";
            case "social-service" -> "social";
            case "payment-service" -> "payment";
            case "file-service" -> "file";
            default -> serviceName.replace("-service", "");
        };
    }

    /**
     * 配置WebClient用于获取远程服务的OpenAPI文档 (if needed for advanced scenarios, currently not used for simple URL forwarding)
     */
    @Bean
    public WebClient webClient() {
        log.info("Creating webClient bean...");
        return WebClient.builder()
                .codecs(configurer -> configurer.defaultCodecs().maxInMemorySize(1024 * 1024)) // Example: Increase buffer size if fetching large specs
                .build();
    }
} 