package com.stylishlink.gateway.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import reactor.core.publisher.Mono;
import reactor.netty.DisposableServer;
import reactor.netty.http.server.HttpServer;

/**
 * SSL配置类 - 支持HTTP和HTTPS双端口
 * HTTPS端口：8443 (主端口，通过application.yml配置SSL)
 * HTTP端口：8080 (HTTP重定向服务器)
 */
@Slf4j
@Configuration
public class SslConfig {

    @Value("${server.http.port:8080}")
    private int httpPort;

    @Value("${server.port:8443}")
    private int httpsPort;

    @Value("${server.ssl.enabled:false}")
    private boolean sslEnabled;

    /**
     * 启动HTTP重定向服务器
     * 将HTTP请求重定向到HTTPS
     * 
     * 注意：只有当SSL启用时才会启动重定向服务器
     */
    @Bean
    public CommandLineRunner httpRedirectServer() {
        return args -> {
            // 检查SSL是否启用
            if (!sslEnabled) {
                log.info("SSL未启用，跳过HTTP重定向服务器启动");
                return;
            }
            
            log.info("启动HTTP重定向服务器，端口: {}", httpPort);
            
            DisposableServer httpServer = HttpServer.create()
                .host("0.0.0.0")
                .port(httpPort)
                .route(routes -> routes
                    .get("/**", (request, response) -> {
                        String httpsUrl = buildHttpsUrl(request.requestHeaders().get("Host"), request.uri());
                        log.debug("重定向HTTP请求到HTTPS: {} -> {}", request.uri(), httpsUrl);
                        
                        return response.status(301)
                            .header("Location", httpsUrl)
                            .sendString(Mono.just("Redirecting to HTTPS..."));
                    })
                    .post("/**", (request, response) -> {
                        String httpsUrl = buildHttpsUrl(request.requestHeaders().get("Host"), request.uri());
                        log.debug("重定向HTTP请求到HTTPS: {} -> {}", request.uri(), httpsUrl);
                        
                        return response.status(301)
                            .header("Location", httpsUrl)
                            .sendString(Mono.just("Redirecting to HTTPS..."));
                    })
                    .route(req -> true, (request, response) -> {
                        String httpsUrl = buildHttpsUrl(request.requestHeaders().get("Host"), request.uri());
                        log.debug("重定向HTTP请求到HTTPS: {} -> {}", request.uri(), httpsUrl);
                        
                        return response.status(301)
                            .header("Location", httpsUrl)
                            .sendString(Mono.just("Redirecting to HTTPS..."));
                    })
                )
                .bindNow();
            
            log.info("HTTP重定向服务器已启动，监听端口: {}, 重定向至HTTPS端口: {}", httpPort, httpsPort);
            
            // 注册关闭钩子
            Runtime.getRuntime().addShutdownHook(new Thread(() -> {
                log.info("关闭HTTP重定向服务器...");
                httpServer.disposeNow();
            }));
        };
    }

    /**
     * 构建HTTPS重定向URL
     * 修复Host header包含端口时的双端口号问题
     * 
     * @param host Host header值，可能包含端口
     * @param uri 请求URI
     * @return 正确的HTTPS URL
     */
    private String buildHttpsUrl(String host, String uri) {
        if (host == null || host.trim().isEmpty()) {
            // 如果Host为空，使用localhost作为默认值
            host = "localhost";
            log.warn("Host header为空，使用默认值: localhost");
        }
        
        // 从Host header中提取主机名（去掉端口部分）
        String hostname = extractHostname(host);
        
        // 构建完整的HTTPS URL
        return String.format("https://%s:%d%s", hostname, httpsPort, uri);
    }

    /**
     * 从Host header中提取主机名
     * 处理可能包含端口号的情况
     * 
     * @param host Host header值（如：localhost:8080 或 *************:8080）
     * @return 纯主机名（如：localhost 或 *************）
     */
    private String extractHostname(String host) {
        if (host == null || host.trim().isEmpty()) {
            return "localhost";
        }
        
        // 去除空格
        host = host.trim();
        
        // 检查是否包含端口号（格式：host:port）
        int portIndex = host.lastIndexOf(':');
        if (portIndex > 0) {
            // 确保不是IPv6地址（IPv6地址包含多个冒号）
            // 简单检查：如果包含多个冒号且以[开头，则可能是IPv6
            if (host.startsWith("[") && host.contains("]:")) {
                // IPv6格式：[::1]:8080
                int bracketIndex = host.lastIndexOf("]:");
                if (bracketIndex > 0) {
                    return host.substring(0, bracketIndex + 1);
                }
            } else {
                // IPv4或域名格式：*************:8080 或 localhost:8080
                return host.substring(0, portIndex);
            }
        }
        
        return host;
    }
} 