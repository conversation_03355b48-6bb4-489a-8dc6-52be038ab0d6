package com.stylishlink.gateway.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 安全白名单配置属性
 */
@Component
@ConfigurationProperties(prefix = "security.whitelist")
public class SecurityWhitelistProperties {

    private List<String> publicApis = new ArrayList<>();
    private List<String> swaggerPaths = new ArrayList<>();

    public List<String> getPublicApis() {
        return publicApis;
    }

    public void setPublicApis(List<String> publicApis) {
        this.publicApis = publicApis;
    }

    public List<String> getSwaggerPaths() {
        return swaggerPaths;
    }

    public void setSwaggerPaths(List<String> swaggerPaths) {
        this.swaggerPaths = swaggerPaths;
    }

    public List<String> getAllowedPaths() {
        // Filter out "/**" if it exists, as per user confirmation
        List<String> filteredPublicApis = publicApis.stream()
                .filter(path -> !"/**".equals(path))
                .collect(Collectors.toList());

        return Stream.concat(filteredPublicApis.stream(), swaggerPaths.stream())
                .distinct()
                .collect(Collectors.toList());
    }
} 