package com.stylishlink.gateway.config;

import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.reactive.EnableWebFluxSecurity;
import org.springframework.security.config.web.server.ServerHttpSecurity;
import org.springframework.security.web.server.SecurityWebFilterChain;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.reactive.CorsConfigurationSource;
import org.springframework.web.cors.reactive.UrlBasedCorsConfigurationSource;

import java.util.Arrays;
import java.util.List;

/**
 * 网关安全配置
 */
@Configuration
@EnableWebFluxSecurity
@RequiredArgsConstructor
public class SecurityConfig {
    private static final Logger log = LoggerFactory.getLogger(SecurityConfig.class);
    
    private final SecurityWhitelistProperties whitelistProperties;

    @Bean
    public SecurityWebFilterChain securityWebFilterChain(ServerHttpSecurity http) {
        log.debug("SecurityConfig: Initializing SecurityWebFilterChain.");
        
        // 从配置文件获取所有白名单路径 (用于日志记录，实际认证由AuthenticationFilter处理)
        List<String> allowedPathList = whitelistProperties.getAllowedPaths();
        String[] whitelistPathsForLogging = allowedPathList.toArray(new String[0]);
        log.info("SecurityConfig: Whitelist paths (from SecurityWhitelistProperties for potential use/logging): {}", Arrays.toString(whitelistPathsForLogging));

        http
            .cors(corsSpec -> corsSpec.configurationSource(corsConfigurationSource()))
            .csrf(ServerHttpSecurity.CsrfSpec::disable) // 禁用CSRF
            .authorizeExchange(exchange -> exchange
                // .pathMatchers(whitelistPaths).permitAll() // OLD - Let AuthenticationFilter handle path access
                // .anyExchange().authenticated() // OLD - Let AuthenticationFilter handle path access
                .anyExchange().permitAll() // NEW - Rely on AuthenticationFilter for actual auth checks
            )
            .httpBasic(ServerHttpSecurity.HttpBasicSpec::disable) // 明确禁用HTTP Basic认证
            .formLogin(ServerHttpSecurity.FormLoginSpec::disable); // 明确禁用表单登录
            // Our custom AuthenticationFilter (GlobalFilter) will handle JWT authentication
            // and enforce access based on its whitelist logic before requests reach this point significantly.

        return http.build();
    }

    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();
        // TODO:生产环境中应配置具体的允许来源，而不是"*"
        configuration.setAllowedOrigins(Arrays.asList("*")); 
        configuration.setAllowedMethods(Arrays.asList("GET", "POST", "PUT", "DELETE", "OPTIONS"));
        configuration.setAllowedHeaders(Arrays.asList("Authorization", "Content-Type", "X-Requested-With", "X-Forwarded-For"));
        configuration.setExposedHeaders(Arrays.asList("Authorization"));
        configuration.setAllowCredentials(true); 
        configuration.setMaxAge(3600L);
        
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);
        return source;
    }
} 