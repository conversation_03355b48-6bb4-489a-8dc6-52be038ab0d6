# StylishLink Gateway Service

## 概述

StylishLink Gateway Service 是基于 Spring Cloud Gateway 实现的独立微服务网关，提供统一的API入口、JWT权限验证、路由转发等核心功能。

## 核心功能

### 1. JWT Token验证
- **统一认证**: 所有请求统一验证JWT Token
- **白名单机制**: 登录、注册等接口无需认证
- **用户信息传递**: 解析Token并将用户信息传递给下游服务

### 2. 路由管理
- **动态路由**: 支持基于路径的动态路由配置
- **负载均衡**: 集成Spring Cloud LoadBalancer
- **服务发现**: 通过Eureka自动发现服务实例

### 3. 安全防护
- **跨域支持**: 全局CORS配置
- **请求过滤**: 恶意请求拦截
- **错误处理**: 统一错误响应格式

## 项目结构

```
gateway-service/
├── src/main/java/com/stylishlink/gateway/
│   ├── filter/
│   │   └── AuthGatewayFilter.java          # JWT认证过滤器
│   ├── config/
│   │   └── AuthProperties.java             # 认证配置类
│   └── GatewayApplication.java             # 启动类
├── src/main/resources/
│   └── application.yml                     # 配置文件
├── pom.xml                                 # Maven依赖
└── README.md                               # 说明文档
```

## 依赖说明

Gateway服务是独立的Spring Boot应用，主要依赖：
- **Spring Boot Starter Parent**: 基础Spring Boot框架
- **Spring Cloud Gateway**: 网关核心功能
- **Spring Cloud LoadBalancer**: 负载均衡
- **Eureka Client**: 服务发现
- **JJWT**: JWT Token解析库

## 快速开始

### 1. 启动服务

```bash
# 进入Gateway服务目录
cd backend/gateway-service

# 启动服务
mvn spring-boot:run
```

### 2. 验证启动

访问健康检查接口：
```bash
curl http://localhost:8080/actuator/health
```

## 配置说明

### JWT配置
```yaml
jwt:
  secret: stylishlink-secret-key-2025-gateway  # JWT密钥
  expiration: 7200                             # 过期时间(秒)
```

### 认证白名单配置
```yaml
auth:
  white-list:
    - /api/user/send-code    # 发送验证码
    - /api/user/login        # 用户登录
    - /api/user/refresh-token # 刷新Token
    - /actuator/**           # 监控端点
    - /health               # 健康检查
```

### 路由配置
```yaml
spring:
  cloud:
    gateway:
      routes:
        - id: user-service
          uri: lb://user-service     # 负载均衡到user-service
          predicates:
            - Path=/api/user/**      # 路径匹配
```

## API使用示例

### 1. 需要认证的接口调用

```bash
# 请求头中携带JWT Token
curl -X GET http://localhost:8080/api/user/profile \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
```

### 2. 白名单接口调用

```bash
# 无需Token的接口
curl -X POST http://localhost:8080/api/user/login \
  -H "Content-Type: application/json" \
  -d '{"phone":"13800138000","code":"123456","codeId":"code_123456"}'
```

## JWT Token格式

Gateway期望的JWT Token应包含以下Claims：

```json
{
  "sub": "u123456",           // 用户ID
  "phone": "13800138000",     // 手机号
  "nickname": "小明",         // 昵称
  "roles": "USER,VIP",        // 用户角色
  "exp": **********,          // 过期时间戳
  "iat": 1640988000           // 签发时间戳
}
```

## 传递给下游服务的Header

Gateway验证Token成功后，会在请求头中添加以下用户信息：

| Header名称 | 说明 | 示例值 |
|------------|------|--------|
| X-User-Id | 用户ID | u123456 |
| X-User-Phone | 手机号 | 13800138000 |
| X-User-Nickname | 昵称 | 小明 |
| X-User-Roles | 用户角色 | USER,VIP |

下游服务可以直接从这些Header中获取用户信息，无需再次解析JWT。

## 错误响应格式

### 认证失败响应
```json
{
  "code": 1002,
  "msg": "缺少认证token",
  "data": null
}
```

### Token无效响应
```json
{
  "code": 1002,
  "msg": "无效的认证token", 
  "data": null
}
```

## 监控和运维

### 健康检查
```bash
curl http://localhost:8080/actuator/health
```

### 路由信息查看
```bash
curl http://localhost:8080/actuator/gateway/routes
```

### 日志级别调整
```yaml
logging:
  level:
    com.stylishlink.gateway: DEBUG
    org.springframework.cloud.gateway: DEBUG
```

## 注意事项

### 1. JWT密钥安全
- 生产环境必须使用强密钥
- 定期轮换JWT密钥
- 密钥不要提交到代码仓库

### 2. Token过期处理
- 前端需要处理Token过期响应
- 实现自动Token刷新机制
- 合理设置Token过期时间

### 3. 性能优化
- 合理配置连接池大小
- 启用Gateway缓存机制
- 监控Gateway性能指标

### 4. 独立部署
- 无需依赖其他内部模块
- 可独立构建和部署
- JWT处理逻辑完全自包含

## 扩展功能

### 1. 限流控制
可以集成Redis实现基于用户的限流：

```yaml
spring:
  cloud:
    gateway:
      routes:
        - filters:
            - name: RequestRateLimiter
              args:
                redis-rate-limiter.replenishRate: 10
                redis-rate-limiter.burstCapacity: 20
```

### 2. 请求日志
可以添加全局过滤器记录请求日志：

```java
@Component
public class LoggingFilter implements GlobalFilter {
    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        // 记录请求日志
        return chain.filter(exchange);
    }
}
```

## 常见问题

### Q: Gateway启动失败，提示依赖问题
A: 确保使用的是标准的Spring Boot Parent，不依赖自定义的parent POM。

### Q: JWT Token解析失败
A: 检查JWT密钥配置是否与生成Token的服务一致。

### Q: 下游服务接收不到用户信息Header
A: 确认Gateway过滤器正常工作，检查路由配置是否正确。

### Q: 跨域请求被拒绝
A: 检查CORS配置，确保allowedOriginPatterns设置正确。

## 部署建议

### 1. Docker化部署
```dockerfile
FROM openjdk:17-jre-slim
COPY target/gateway-service-1.0.0.jar app.jar
EXPOSE 8080
ENTRYPOINT ["java", "-jar", "/app.jar"]
```

### 2. 环境变量配置
```bash
export JWT_SECRET=your-production-secret
export EUREKA_SERVER=http://eureka-server:8761/eureka/
```

## 技术支持

如有问题，请提交Issue或联系开发团队。

---

**版本**: 1.0.0  
**最后更新**: 2025-01-20 