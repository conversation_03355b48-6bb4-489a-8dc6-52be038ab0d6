# Operation Service - 运营服务

## 概述

运营服务是StylishLink微信小程序的核心业务模块之一，负责用户激励体系、任务系统、成就系统、灵感值管理等运营相关功能。

## 功能特性

### 1. 灵感值管理
- 灵感值账户管理
- 灵感值增加/消费
- 交易记录查询
- 多来源灵感值统计（充值、活动、赠送等）

### 2. 任务系统
- 日常任务管理
- 任务进度跟踪
- 任务完成奖励发放
- 每日签到功能

### 3. 成就系统
- 成就定义与管理
- 用户成就进度跟踪
- 成就解锁与奖励

### 4. 用户激励
- 等级系统（基于累计消费灵感值）
- 特权管理
- 活动参与

## 技术架构

### 技术栈
- **框架**: Spring Boot 3.x + Spring Cloud
- **数据库**: MySQL 8.0
- **ORM**: MyBatis Plus
- **缓存**: Redis
- **服务发现**: Nacos
- **消息队列**: RabbitMQ（可选）

### 项目结构
```
src/main/java/com/stylishlink/operation/
├── OperationServiceApplication.java    # 启动类
├── controller/                         # 控制器层
│   └── OperationController.java
├── service/                           # 服务层
│   ├── OperationService.java
│   └── impl/
│       └── OperationServiceImpl.java
├── entity/                            # 实体类
│   ├── InspirationAccount.java
│   ├── InspirationTransaction.java
│   ├── Task.java
│   ├── UserTaskProgress.java
│   ├── Achievement.java
│   └── UserAchievement.java
├── mapper/                            # 数据访问层
│   ├── InspirationAccountMapper.java
│   ├── InspirationTransactionMapper.java
│   ├── TaskMapper.java
│   ├── UserTaskProgressMapper.java
│   ├── AchievementMapper.java
│   └── UserAchievementMapper.java
├── dto/                               # 数据传输对象
│   ├── request/
│   └── response/
├── config/                            # 配置类
│   └── MyBatisPlusConfig.java
└── exception/                         # 异常处理
    └── GlobalExceptionHandler.java
```

## API接口

### 灵感值管理
- `GET /api/operation/inspiration/balance` - 获取灵感值余额
- `POST /api/operation/inspiration/add` - 增加灵感值
- `POST /api/operation/inspiration/consume` - 消费灵感值
- `GET /api/operation/inspiration/history` - 获取交易历史

### 任务系统
- `GET /api/operation/task/available` - 获取可用任务
- `POST /api/operation/task/complete` - 完成任务
- `GET /api/operation/task/progress` - 获取任务进度
- `POST /api/operation/task/signin` - 每日签到

### 成就系统
- `GET /api/operation/achievement/user` - 获取用户成就
- `POST /api/operation/achievement/unlock` - 解锁成就
- `GET /api/operation/achievement/available` - 获取可用成就

## 数据库设计

### 核心表结构
1. **inspiration_account** - 灵感值账户表
2. **inspiration_transaction** - 灵感值交易记录表
3. **task** - 任务表
4. **user_task_progress** - 用户任务进度表
5. **achievement** - 成就表
6. **user_achievement** - 用户成就表

详细表结构请参考 `src/main/resources/sql/schema.sql`

## 部署说明

### 环境要求
- JDK 17+
- MySQL 8.0+
- Redis 6.0+
- Nacos 2.0+

### 配置说明
1. 在Nacos中配置数据源信息
2. 配置Redis连接信息
3. 执行数据库初始化脚本

### 启动步骤
1. 确保Nacos服务正常运行
2. 确保MySQL和Redis服务正常运行
3. 执行数据库初始化脚本
4. 启动应用：`java -jar operation-service.jar`

## 开发指南

### 本地开发
1. 克隆项目代码
2. 配置本地数据库连接
3. 启动Nacos（可使用Docker）
4. 运行 `OperationServiceApplication.main()`

### 测试
```bash
# 运行单元测试
mvn test

# 运行集成测试
mvn integration-test
```

## 监控与运维

### 健康检查
- 应用健康检查：`/actuator/health`
- 数据库连接检查：`/actuator/health/db`
- Redis连接检查：`/actuator/health/redis`

### 日志
- 应用日志级别：INFO
- 错误日志会记录详细堆栈信息
- 关键业务操作会记录审计日志

## 错误码说明

| 错误码 | 含义 | 说明 |
|--------|------|------|
| 0 | 成功 | 操作成功 |
| 1001 | 参数错误 | 请求参数验证失败 |
| 7001 | 灵感值异常 | 灵感值相关操作异常 |
| 7002 | 任务异常 | 任务系统相关异常 |
| 7003 | 成就异常 | 成就系统相关异常 |
| 9001 | 系统异常 | 系统内部异常 |

## 版本历史

### v1.0.0 (2025-01-12)
- 初始版本发布
- 实现灵感值管理功能
- 实现任务系统功能
- 实现成就系统功能
- 支持Nacos服务发现
- 支持MySQL数据存储

## 联系方式

如有问题或建议，请联系开发团队。 