-- 运营服务数据库表结构

-- 灵感值账户表
CREATE TABLE IF NOT EXISTS `inspiration_account` (
    `user_id` VARCHAR(64) NOT NULL COMMENT '用户ID',
    `total_inspiration` BIGINT NOT NULL DEFAULT 0 COMMENT '总灵感值',
    `charged_inspiration` BIGINT NOT NULL DEFAULT 0 COMMENT '充值获得灵感值(可赠送)',
    `activity_inspiration` BIGINT NOT NULL DEFAULT 0 COMMENT '活动获得灵感值',
    `received_inspiration` BIGINT NOT NULL DEFAULT 0 COMMENT '接收赠送灵感值',
    `total_consumed` BIGINT NOT NULL DEFAULT 0 COMMENT '历史累计消耗灵感值',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`user_id`),
    INDEX `idx_total_inspiration` (`total_inspiration`),
    INDEX `idx_updated_at` (`updated_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='灵感值账户表';

-- 灵感值交易记录表
CREATE TABLE IF NOT EXISTS `inspiration_transaction` (
    `id` VARCHAR(64) NOT NULL COMMENT '交易ID',
    `user_id` VARCHAR(64) NOT NULL COMMENT '用户ID',
    `type` VARCHAR(32) NOT NULL COMMENT '交易类型(ADD/CONSUME)',
    `amount` BIGINT NOT NULL COMMENT '数量(正负表示增减)',
    `source` VARCHAR(64) NOT NULL COMMENT '来源',
    `description` VARCHAR(255) DEFAULT NULL COMMENT '描述',
    `is_counted_for_level` BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否计入等级计算',
    `balance_after` BIGINT NOT NULL COMMENT '交易后余额',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_type` (`type`),
    INDEX `idx_source` (`source`),
    INDEX `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='灵感值交易记录表';

-- 任务表
CREATE TABLE IF NOT EXISTS `task` (
    `id` VARCHAR(64) NOT NULL COMMENT '任务ID',
    `title` VARCHAR(255) NOT NULL COMMENT '任务标题',
    `description` TEXT COMMENT '任务描述',
    `type` VARCHAR(32) NOT NULL COMMENT '任务类型',
    `frequency` VARCHAR(32) NOT NULL COMMENT '任务频率',
    `reward_amount` INT NOT NULL DEFAULT 0 COMMENT '奖励灵感值',
    `conditions` JSON COMMENT '完成条件(JSON格式)',
    `is_active` BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否激活',
    `valid_from` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '有效期开始',
    `valid_to` DATETIME NOT NULL DEFAULT '2099-12-31 23:59:59' COMMENT '有效期结束',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    INDEX `idx_type` (`type`),
    INDEX `idx_frequency` (`frequency`),
    INDEX `idx_is_active` (`is_active`),
    INDEX `idx_valid_period` (`valid_from`, `valid_to`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='任务表';

-- 用户任务进度表
CREATE TABLE IF NOT EXISTS `user_task_progress` (
    `id` VARCHAR(64) NOT NULL COMMENT '主键ID',
    `user_id` VARCHAR(64) NOT NULL COMMENT '用户ID',
    `task_id` VARCHAR(64) NOT NULL COMMENT '任务ID',
    `status` VARCHAR(32) NOT NULL DEFAULT 'NOT_STARTED' COMMENT '任务状态',
    `progress` INT NOT NULL DEFAULT 0 COMMENT '进度',
    `target` INT NOT NULL DEFAULT 1 COMMENT '目标',
    `completed_times` INT NOT NULL DEFAULT 0 COMMENT '完成次数',
    `last_completed_at` DATETIME DEFAULT NULL COMMENT '最后完成时间',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_task` (`user_id`, `task_id`),
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_task_id` (`task_id`),
    INDEX `idx_status` (`status`),
    INDEX `idx_updated_at` (`updated_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户任务进度表';

-- 成就表
CREATE TABLE IF NOT EXISTS `achievement` (
    `id` VARCHAR(64) NOT NULL COMMENT '成就ID',
    `name` VARCHAR(255) NOT NULL COMMENT '成就名称',
    `description` TEXT COMMENT '成就描述',
    `icon` VARCHAR(255) DEFAULT NULL COMMENT '成就图标',
    `category` VARCHAR(64) NOT NULL COMMENT '成就类别',
    `level` INT NOT NULL DEFAULT 1 COMMENT '成就等级',
    `reward_amount` INT NOT NULL DEFAULT 0 COMMENT '奖励灵感值',
    `conditions` JSON COMMENT '完成条件(JSON格式)',
    `is_active` BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否激活',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    INDEX `idx_category` (`category`),
    INDEX `idx_level` (`level`),
    INDEX `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='成就表';

-- 用户成就表
CREATE TABLE IF NOT EXISTS `user_achievement` (
    `id` VARCHAR(64) NOT NULL COMMENT '主键ID',
    `user_id` VARCHAR(64) NOT NULL COMMENT '用户ID',
    `achievement_id` VARCHAR(64) NOT NULL COMMENT '成就ID',
    `is_unlocked` BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否解锁',
    `progress` INT NOT NULL DEFAULT 0 COMMENT '进度',
    `target` INT NOT NULL DEFAULT 1 COMMENT '目标',
    `unlocked_at` DATETIME DEFAULT NULL COMMENT '解锁时间',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_achievement` (`user_id`, `achievement_id`),
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_achievement_id` (`achievement_id`),
    INDEX `idx_is_unlocked` (`is_unlocked`),
    INDEX `idx_unlocked_at` (`unlocked_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户成就表'; 