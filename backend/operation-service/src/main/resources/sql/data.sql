-- 运营服务初始数据

-- 插入基础任务数据
INSERT INTO `task` (`id`, `title`, `description`, `type`, `frequency`, `reward_amount`, `conditions`, `is_active`, `valid_from`, `valid_to`) VALUES
('DAILY_SIGN_IN', '每日签到', '每天签到获得灵感值奖励', 'DAILY', 'DAILY', 10, '{"type": "signin", "target": 1}', TRUE, '2025-01-01 00:00:00', '2099-12-31 23:59:59'),
('UPLOAD_CLOTHING', '上传衣物', '上传一件衣物到衣橱', 'UPLOAD', 'UNLIMITED', 5, '{"type": "upload_clothing", "target": 1}', TRUE, '2025-01-01 00:00:00', '2099-12-31 23:59:59'),
('CREATE_OUTFIT', '创建搭配', '创建一个新的搭配方案', 'CREATE', 'UNLIMITED', 8, '{"type": "create_outfit", "target": 1}', TRUE, '2025-01-01 00:00:00', '2099-12-31 23:59:59'),
('SHARE_OUTFIT', '分享搭配', '分享搭配到社交平台', 'SHARE', 'UNLIMITED', 15, '{"type": "share_outfit", "target": 1}', TRUE, '2025-01-01 00:00:00', '2099-12-31 23:59:59'),
('WEEKLY_ACTIVE', '周活跃', '一周内完成5次穿搭推荐', 'WEEKLY', 'WEEKLY', 50, '{"type": "recommendation", "target": 5, "period": "week"}', TRUE, '2025-01-01 00:00:00', '2099-12-31 23:59:59');

-- 插入基础成就数据
INSERT INTO `achievement` (`id`, `name`, `description`, `icon`, `category`, `level`, `reward_amount`, `conditions`, `is_active`) VALUES
('FIRST_UPLOAD', '初次上传', '上传第一件衣物', 'icon_first_upload.png', 'BEGINNER', 1, 20, '{"type": "upload_clothing", "target": 1}', TRUE),
('WARDROBE_MASTER', '衣橱达人', '衣橱中拥有50件衣物', 'icon_wardrobe_master.png', 'COLLECTION', 2, 100, '{"type": "wardrobe_count", "target": 50}', TRUE),
('STYLE_CREATOR', '风格创造者', '创建10个不同的搭配', 'icon_style_creator.png', 'CREATIVITY', 2, 80, '{"type": "outfit_count", "target": 10}', TRUE),
('SOCIAL_BUTTERFLY', '社交达人', '分享搭配获得100个点赞', 'icon_social_butterfly.png', 'SOCIAL', 3, 200, '{"type": "likes_received", "target": 100}', TRUE),
('SIGN_IN_STREAK', '签到达人', '连续签到30天', 'icon_sign_in_streak.png', 'PERSISTENCE', 3, 300, '{"type": "signin_streak", "target": 30}', TRUE),
('INSPIRATION_COLLECTOR', '灵感收集者', '累计获得1000灵感值', 'icon_inspiration_collector.png', 'ACHIEVEMENT', 4, 500, '{"type": "total_inspiration", "target": 1000}', TRUE);

-- 插入测试用户灵感值账户（可选，用于测试）
-- INSERT INTO `inspiration_account` (`user_id`, `total_inspiration`, `charged_inspiration`, `activity_inspiration`, `received_inspiration`, `total_consumed`) VALUES
-- ('test_user_001', 100, 0, 100, 0, 0),
-- ('test_user_002', 50, 50, 0, 0, 0); 