package com.stylishlink.operation.dto.response;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 灵感值余额响应DTO
 */
@Data
public class InspirationBalanceResponse {

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 总灵感值
     */
    private Long totalInspiration;

    /**
     * 充值获得灵感值(可赠送)
     */
    private Long chargedInspiration;

    /**
     * 活动获得灵感值
     */
    private Long activityInspiration;

    /**
     * 接收赠送灵感值
     */
    private Long receivedInspiration;

    /**
     * 历史累计消耗灵感值
     */
    private Long totalConsumed;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
} 