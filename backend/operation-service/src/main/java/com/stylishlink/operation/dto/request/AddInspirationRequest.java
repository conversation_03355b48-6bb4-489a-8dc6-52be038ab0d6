package com.stylishlink.operation.dto.request;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;

/**
 * 增加灵感值请求DTO
 */
@Data
public class AddInspirationRequest {

    /**
     * 用户ID
     */
    @NotBlank(message = "用户ID不能为空")
    private String userId;

    /**
     * 增加数量
     */
    @NotNull(message = "增加数量不能为空")
    @Positive(message = "增加数量必须为正数")
    private Long amount;

    /**
     * 来源
     */
    @NotBlank(message = "来源不能为空")
    private String source;

    /**
     * 描述
     */
    private String description;

    /**
     * 是否计入等级计算
     */
    private Boolean isCountedForLevel = true;
} 