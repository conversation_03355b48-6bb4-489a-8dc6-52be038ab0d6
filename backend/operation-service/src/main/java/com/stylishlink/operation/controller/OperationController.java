package com.stylishlink.operation.controller;

import com.stylishlink.common.dto.ApiResponse;
import com.stylishlink.operation.dto.request.*;
import com.stylishlink.operation.dto.response.*;
import com.stylishlink.operation.service.OperationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 运营服务控制器
 */
@Slf4j
@RestController
@RequestMapping("/operation")
public class OperationController {

    @Autowired
    private OperationService operationService;

    /**
     * 获取灵感值余额
     */
    @GetMapping("/inspiration/balance")
    public ApiResponse<InspirationBalanceResponse> getInspirationBalance(@RequestParam String userId) {
        try {
            BalanceRequest request = new BalanceRequest();
            request.setUserId(userId);
            InspirationBalanceResponse response = operationService.getInspirationBalance(request);
            return ApiResponse.success("获取灵感值余额成功", response);
        } catch (Exception e) {
            log.error("获取灵感值余额失败", e);
            return ApiResponse.error(7001, "获取灵感值余额失败: " + e.getMessage());
        }
    }

    /**
     * 增加灵感值
     */
    @PostMapping("/inspiration/add")
    public ApiResponse<InspirationTransactionResponse> addInspiration(@Valid @RequestBody AddInspirationRequest request) {
        try {
            InspirationTransactionResponse response = operationService.addInspiration(request);
            return ApiResponse.success("增加灵感值成功", response);
        } catch (Exception e) {
            log.error("增加灵感值失败", e);
            return ApiResponse.error(7001, "增加灵感值失败: " + e.getMessage());
        }
    }

    /**
     * 消费灵感值
     */
    @PostMapping("/inspiration/consume")
    public ApiResponse<InspirationTransactionResponse> consumeInspiration(@Valid @RequestBody ConsumeInspirationRequest request) {
        try {
            InspirationTransactionResponse response = operationService.consumeInspiration(request);
            return ApiResponse.success("消费灵感值成功", response);
        } catch (Exception e) {
            log.error("消费灵感值失败", e);
            return ApiResponse.error(7001, "消费灵感值失败: " + e.getMessage());
        }
    }

    /**
     * 获取灵感值交易历史
     */
    @GetMapping("/inspiration/history")
    public ApiResponse<List<InspirationTransactionResponse>> getInspirationHistory(
            @RequestParam String userId,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size) {
        try {
            List<InspirationTransactionResponse> response = operationService.getInspirationHistory(userId, page, size);
            return ApiResponse.success("获取灵感值交易历史成功", response);
        } catch (Exception e) {
            log.error("获取灵感值交易历史失败", e);
            return ApiResponse.error(7001, "获取灵感值交易历史失败: " + e.getMessage());
        }
    }

    /**
     * 获取可用任务列表
     */
    @GetMapping("/task/available")
    public ApiResponse<List<TaskResponse>> getAvailableTasks(@RequestParam String userId) {
        try {
            List<TaskResponse> response = operationService.getAvailableTasks(userId);
            return ApiResponse.success("获取可用任务列表成功", response);
        } catch (Exception e) {
            log.error("获取可用任务列表失败", e);
            return ApiResponse.error(7002, "获取可用任务列表失败: " + e.getMessage());
        }
    }

    /**
     * 完成任务
     */
    @PostMapping("/task/complete")
    public ApiResponse<TaskResponse> completeTask(@Valid @RequestBody CompleteTaskRequest request) {
        try {
            TaskResponse response = operationService.completeTask(request);
            return ApiResponse.success("完成任务成功", response);
        } catch (Exception e) {
            log.error("完成任务失败", e);
            return ApiResponse.error(7002, "完成任务失败: " + e.getMessage());
        }
    }

    /**
     * 获取任务进度
     */
    @GetMapping("/task/progress")
    public ApiResponse<List<TaskResponse>> getTaskProgress(@RequestParam String userId) {
        try {
            List<TaskResponse> response = operationService.getTaskProgress(userId);
            return ApiResponse.success("获取任务进度成功", response);
        } catch (Exception e) {
            log.error("获取任务进度失败", e);
            return ApiResponse.error(7002, "获取任务进度失败: " + e.getMessage());
        }
    }

    /**
     * 每日签到
     */
    @PostMapping("/task/signin")
    public ApiResponse<TaskResponse> signIn(@RequestParam String userId) {
        try {
            TaskResponse response = operationService.signIn(userId);
            return ApiResponse.success("签到成功", response);
        } catch (Exception e) {
            log.error("签到失败", e);
            return ApiResponse.error(7002, "签到失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户成就
     */
    @GetMapping("/achievement/user")
    public ApiResponse<List<TaskResponse>> getUserAchievements(@RequestParam String userId) {
        try {
            List<TaskResponse> response = operationService.getUserAchievements(userId);
            return ApiResponse.success("获取用户成就成功", response);
        } catch (Exception e) {
            log.error("获取用户成就失败", e);
            return ApiResponse.error(7003, "获取用户成就失败: " + e.getMessage());
        }
    }

    /**
     * 解锁成就
     */
    @PostMapping("/achievement/unlock")
    public ApiResponse<TaskResponse> unlockAchievement(@RequestParam String userId, @RequestParam String achievementId) {
        try {
            TaskResponse response = operationService.unlockAchievement(userId, achievementId);
            return ApiResponse.success("解锁成就成功", response);
        } catch (Exception e) {
            log.error("解锁成就失败", e);
            return ApiResponse.error(7003, "解锁成就失败: " + e.getMessage());
        }
    }

    /**
     * 获取可用成就列表
     */
    @GetMapping("/achievement/available")
    public ApiResponse<List<TaskResponse>> getAvailableAchievements(@RequestParam String userId) {
        try {
            List<TaskResponse> response = operationService.getAvailableAchievements(userId);
            return ApiResponse.success("获取可用成就列表成功", response);
        } catch (Exception e) {
            log.error("获取可用成就列表失败", e);
            return ApiResponse.error(7003, "获取可用成就列表失败: " + e.getMessage());
        }
    }
} 