package com.stylishlink.operation.dto.response;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 灵感值交易响应DTO
 */
@Data
public class InspirationTransactionResponse {

    /**
     * 交易ID
     */
    private String id;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 交易类型
     */
    private String type;

    /**
     * 数量(正负表示增减)
     */
    private Long amount;

    /**
     * 来源
     */
    private String source;

    /**
     * 描述
     */
    private String description;

    /**
     * 是否计入等级计算
     */
    private Boolean isCountedForLevel;

    /**
     * 交易后余额
     */
    private Long balanceAfter;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
} 