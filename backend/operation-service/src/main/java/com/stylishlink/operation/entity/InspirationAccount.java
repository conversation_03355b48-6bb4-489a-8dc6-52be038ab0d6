package com.stylishlink.operation.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 灵感值账户实体
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("inspiration_account")
public class InspirationAccount {

    /**
     * 用户ID
     */
    @TableId(type = IdType.INPUT)
    private String userId;

    /**
     * 总灵感值
     */
    private Long totalInspiration;

    /**
     * 充值获得灵感值(可赠送)
     */
    private Long chargedInspiration;

    /**
     * 活动获得灵感值
     */
    private Long activityInspiration;

    /**
     * 接收赠送灵感值
     */
    private Long receivedInspiration;

    /**
     * 历史累计消耗灵感值
     */
    private Long totalConsumed;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
} 