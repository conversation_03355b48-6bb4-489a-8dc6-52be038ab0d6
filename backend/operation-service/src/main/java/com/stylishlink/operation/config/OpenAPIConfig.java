package com.stylishlink.operation.config;

import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.servers.Server;
import io.swagger.v3.oas.models.tags.Tag;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * 运营服务OpenAPI配置
 */
@Configuration
public class OpenAPIConfig {

    @Value("${server.port:8085}")
    private String serverPort;

    @Bean
    public OpenAPI operationServiceOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("运营服务API")
                        .description("StylishLink运营服务API文档，提供灵感值管理、任务系统、成就系统、会员特权等功能")
                        .version("1.0.0")
                        .contact(new Contact()
                                .name("StylishLink团队")
                                .email("<EMAIL>")
                                .url("https://stylishlink.com"))
                        .license(new License()
                                .name("MIT")
                                .url("https://opensource.org/licenses/MIT")))
                .servers(List.of(
                        new Server()
                                .url("http://localhost:" + serverPort)
                                .description("本地开发环境"),
                        new Server()
                                .url("https://api.stylishlink.com")
                                .description("生产环境")
                ))
                .addSecurityItem(new SecurityRequirement()
                        .addList("Bearer Authentication"))
                .components(new Components()
                        .addSecuritySchemes("Bearer Authentication", 
                                new SecurityScheme()
                                        .type(SecurityScheme.Type.HTTP)
                                        .scheme("bearer")
                                        .bearerFormat("JWT")
                                        .description("请在请求头中添加 Authorization: Bearer {token}")))
                .tags(List.of(
                        new Tag()
                                .name("灵感值管理")
                                .description("灵感值查询、增加、消费、交易记录等接口"),
                        new Tag()
                                .name("任务系统")
                                .description("每日任务、活动任务、任务完成等接口"),
                        new Tag()
                                .name("成就系统")
                                .description("成就解锁、进度查询、奖励领取等接口"),
                        new Tag()
                                .name("会员特权")
                                .description("会员等级、特权权益、升级条件等接口"),
                        new Tag()
                                .name("活动运营")
                                .description("促销活动、限时活动、节日活动等接口")
                ));
    }
} 