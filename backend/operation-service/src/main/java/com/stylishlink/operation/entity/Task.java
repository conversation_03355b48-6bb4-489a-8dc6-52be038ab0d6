package com.stylishlink.operation.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 任务实体
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("task")
public class Task {

    /**
     * 任务ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 任务标题
     */
    private String title;

    /**
     * 任务描述
     */
    private String description;

    /**
     * 任务类型
     */
    private String type;

    /**
     * 任务频率
     */
    private String frequency;

    /**
     * 奖励灵感值
     */
    private Integer rewardAmount;

    /**
     * 完成条件(JSON格式)
     */
    private String conditions;

    /**
     * 是否激活
     */
    private Boolean isActive;

    /**
     * 有效期开始
     */
    private LocalDateTime validFrom;

    /**
     * 有效期结束
     */
    private LocalDateTime validTo;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
} 