package com.stylishlink.operation.dto.response;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 任务响应DTO
 */
@Data
public class TaskResponse {

    /**
     * 任务ID
     */
    private String id;

    /**
     * 任务标题
     */
    private String title;

    /**
     * 任务描述
     */
    private String description;

    /**
     * 任务类型
     */
    private String type;

    /**
     * 任务频率
     */
    private String frequency;

    /**
     * 奖励灵感值
     */
    private Integer rewardAmount;

    /**
     * 完成条件
     */
    private String conditions;

    /**
     * 是否激活
     */
    private Boolean isActive;

    /**
     * 有效期开始
     */
    private LocalDateTime validFrom;

    /**
     * 有效期结束
     */
    private LocalDateTime validTo;

    /**
     * 用户进度
     */
    private Integer progress;

    /**
     * 目标
     */
    private Integer target;

    /**
     * 任务状态
     */
    private String status;
} 