package com.stylishlink.operation.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 成就实体
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("achievement")
public class Achievement {

    /**
     * 成就ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 成就名称
     */
    private String name;

    /**
     * 成就描述
     */
    private String description;

    /**
     * 成就图标
     */
    private String icon;

    /**
     * 成就类别
     */
    private String category;

    /**
     * 成就等级
     */
    private Integer level;

    /**
     * 奖励灵感值
     */
    private Integer rewardAmount;

    /**
     * 完成条件(JSON格式)
     */
    private String conditions;

    /**
     * 是否激活
     */
    private Boolean isActive;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
} 