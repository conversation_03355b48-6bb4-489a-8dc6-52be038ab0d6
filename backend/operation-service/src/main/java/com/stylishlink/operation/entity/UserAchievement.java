package com.stylishlink.operation.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 用户成就实体
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("user_achievement")
public class UserAchievement {

    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 成就ID
     */
    private String achievementId;

    /**
     * 是否解锁
     */
    private Boolean isUnlocked;

    /**
     * 进度
     */
    private Integer progress;

    /**
     * 目标
     */
    private Integer target;

    /**
     * 解锁时间
     */
    private LocalDateTime unlockedAt;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
} 