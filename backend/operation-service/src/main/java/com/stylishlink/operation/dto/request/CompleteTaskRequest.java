package com.stylishlink.operation.dto.request;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * 完成任务请求DTO
 */
@Data
public class CompleteTaskRequest {

    /**
     * 用户ID
     */
    @NotBlank(message = "用户ID不能为空")
    private String userId;

    /**
     * 任务ID
     */
    @NotBlank(message = "任务ID不能为空")
    private String taskId;

    /**
     * 完成数据(JSON格式)
     */
    private String completionData;
} 