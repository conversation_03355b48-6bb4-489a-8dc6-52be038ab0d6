package com.stylishlink.operation.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 用户任务进度实体
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("user_task_progress")
public class UserTaskProgress {

    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 任务状态
     */
    private String status;

    /**
     * 进度
     */
    private Integer progress;

    /**
     * 目标
     */
    private Integer target;

    /**
     * 完成次数
     */
    private Integer completedTimes;

    /**
     * 最后完成时间
     */
    private LocalDateTime lastCompletedAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
} 