package com.stylishlink.operation.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 灵感值交易记录实体
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("inspiration_transaction")
public class InspirationTransaction {

    /**
     * 交易ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 交易类型
     */
    private String type;

    /**
     * 数量(正负表示增减)
     */
    private Long amount;

    /**
     * 来源
     */
    private String source;

    /**
     * 描述
     */
    private String description;

    /**
     * 是否计入等级计算
     */
    private Boolean isCountedForLevel;

    /**
     * 交易后余额
     */
    private Long balanceAfter;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
} 