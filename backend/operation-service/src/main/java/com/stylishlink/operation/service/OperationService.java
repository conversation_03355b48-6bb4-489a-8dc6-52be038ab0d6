package com.stylishlink.operation.service;

import com.stylishlink.operation.dto.request.*;
import com.stylishlink.operation.dto.response.*;

import java.util.List;

/**
 * 运营服务接口
 */
public interface OperationService {

    // 灵感值管理
    InspirationBalanceResponse getInspirationBalance(BalanceRequest request);
    InspirationTransactionResponse addInspiration(AddInspirationRequest request);
    InspirationTransactionResponse consumeInspiration(ConsumeInspirationRequest request);
    List<InspirationTransactionResponse> getInspirationHistory(String userId, int page, int size);

    // 任务系统
    List<TaskResponse> getAvailableTasks(String userId);
    TaskResponse completeTask(CompleteTaskRequest request);
    List<TaskResponse> getTaskProgress(String userId);
    TaskResponse signIn(String userId);

    // 成就系统
    List<TaskResponse> getUserAchievements(String userId);
    TaskResponse unlockAchievement(String userId, String achievementId);
    List<TaskResponse> getAvailableAchievements(String userId);
} 