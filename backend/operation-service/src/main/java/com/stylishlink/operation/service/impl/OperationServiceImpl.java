package com.stylishlink.operation.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.stylishlink.operation.dto.request.*;
import com.stylishlink.operation.dto.response.*;
import com.stylishlink.operation.entity.*;
import com.stylishlink.operation.mapper.*;
import com.stylishlink.operation.service.OperationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 运营服务实现类
 */
@Slf4j
@Service
public class OperationServiceImpl implements OperationService {

    @Autowired
    private InspirationAccountMapper inspirationAccountMapper;

    @Autowired
    private InspirationTransactionMapper inspirationTransactionMapper;

    @Autowired
    private TaskMapper taskMapper;

    @Autowired
    private UserTaskProgressMapper userTaskProgressMapper;

    @Autowired
    private AchievementMapper achievementMapper;

    @Autowired
    private UserAchievementMapper userAchievementMapper;

    @Override
    public InspirationBalanceResponse getInspirationBalance(BalanceRequest request) {
        InspirationAccount account = inspirationAccountMapper.selectById(request.getUserId());
        if (account == null) {
            // 创建新账户
            account = new InspirationAccount();
            account.setUserId(request.getUserId());
            account.setTotalInspiration(0L);
            account.setChargedInspiration(0L);
            account.setActivityInspiration(0L);
            account.setReceivedInspiration(0L);
            account.setTotalConsumed(0L);
            account.setCreatedAt(LocalDateTime.now());
            account.setUpdatedAt(LocalDateTime.now());
            inspirationAccountMapper.insert(account);
        }

        InspirationBalanceResponse response = new InspirationBalanceResponse();
        BeanUtils.copyProperties(account, response);
        return response;
    }

    @Override
    @Transactional
    public InspirationTransactionResponse addInspiration(AddInspirationRequest request) {
        // 获取或创建账户
        InspirationAccount account = inspirationAccountMapper.selectById(request.getUserId());
        if (account == null) {
            account = new InspirationAccount();
            account.setUserId(request.getUserId());
            account.setTotalInspiration(0L);
            account.setChargedInspiration(0L);
            account.setActivityInspiration(0L);
            account.setReceivedInspiration(0L);
            account.setTotalConsumed(0L);
            account.setCreatedAt(LocalDateTime.now());
            account.setUpdatedAt(LocalDateTime.now());
            inspirationAccountMapper.insert(account);
        }

        // 更新账户余额
        account.setTotalInspiration(account.getTotalInspiration() + request.getAmount());
        
        // 根据来源更新对应字段
        if ("charge".equals(request.getSource())) {
            account.setChargedInspiration(account.getChargedInspiration() + request.getAmount());
        } else if ("activity".equals(request.getSource())) {
            account.setActivityInspiration(account.getActivityInspiration() + request.getAmount());
        } else if ("gift".equals(request.getSource())) {
            account.setReceivedInspiration(account.getReceivedInspiration() + request.getAmount());
        }
        
        account.setUpdatedAt(LocalDateTime.now());
        inspirationAccountMapper.updateById(account);

        // 创建交易记录
        InspirationTransaction transaction = new InspirationTransaction();
        transaction.setUserId(request.getUserId());
        transaction.setType("ADD");
        transaction.setAmount(request.getAmount());
        transaction.setSource(request.getSource());
        transaction.setDescription(request.getDescription());
        transaction.setIsCountedForLevel(request.getIsCountedForLevel());
        transaction.setBalanceAfter(account.getTotalInspiration());
        transaction.setCreatedAt(LocalDateTime.now());
        inspirationTransactionMapper.insert(transaction);

        InspirationTransactionResponse response = new InspirationTransactionResponse();
        BeanUtils.copyProperties(transaction, response);
        return response;
    }

    @Override
    @Transactional
    public InspirationTransactionResponse consumeInspiration(ConsumeInspirationRequest request) {
        // 获取账户
        InspirationAccount account = inspirationAccountMapper.selectById(request.getUserId());
        if (account == null || account.getTotalInspiration() < request.getAmount()) {
            throw new RuntimeException("灵感值余额不足");
        }

        // 更新账户余额
        account.setTotalInspiration(account.getTotalInspiration() - request.getAmount());
        account.setTotalConsumed(account.getTotalConsumed() + request.getAmount());
        account.setUpdatedAt(LocalDateTime.now());
        inspirationAccountMapper.updateById(account);

        // 创建交易记录
        InspirationTransaction transaction = new InspirationTransaction();
        transaction.setUserId(request.getUserId());
        transaction.setType("CONSUME");
        transaction.setAmount(-request.getAmount());
        transaction.setSource(request.getSource());
        transaction.setDescription(request.getDescription());
        transaction.setIsCountedForLevel(false);
        transaction.setBalanceAfter(account.getTotalInspiration());
        transaction.setCreatedAt(LocalDateTime.now());
        inspirationTransactionMapper.insert(transaction);

        InspirationTransactionResponse response = new InspirationTransactionResponse();
        BeanUtils.copyProperties(transaction, response);
        return response;
    }

    @Override
    public List<InspirationTransactionResponse> getInspirationHistory(String userId, int page, int size) {
        Page<InspirationTransaction> pageParam = new Page<>(page, size);
        QueryWrapper<InspirationTransaction> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        queryWrapper.orderByDesc("created_at");
        
        IPage<InspirationTransaction> result = inspirationTransactionMapper.selectPage(pageParam, queryWrapper);
        
        return result.getRecords().stream()
                .map(transaction -> {
                    InspirationTransactionResponse response = new InspirationTransactionResponse();
                    BeanUtils.copyProperties(transaction, response);
                    return response;
                })
                .collect(Collectors.toList());
    }

    @Override
    public List<TaskResponse> getAvailableTasks(String userId) {
        QueryWrapper<Task> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_active", true);
        queryWrapper.le("valid_from", LocalDateTime.now());
        queryWrapper.ge("valid_to", LocalDateTime.now());
        
        List<Task> tasks = taskMapper.selectList(queryWrapper);
        
        return tasks.stream()
                .map(task -> {
                    TaskResponse response = new TaskResponse();
                    BeanUtils.copyProperties(task, response);
                    
                    // 获取用户任务进度
                    QueryWrapper<UserTaskProgress> progressQuery = new QueryWrapper<>();
                    progressQuery.eq("user_id", userId);
                    progressQuery.eq("task_id", task.getId());
                    UserTaskProgress progress = userTaskProgressMapper.selectOne(progressQuery);
                    
                    if (progress != null) {
                        response.setProgress(progress.getProgress());
                        response.setTarget(progress.getTarget());
                        response.setStatus(progress.getStatus());
                    } else {
                        response.setProgress(0);
                        response.setTarget(1);
                        response.setStatus("NOT_STARTED");
                    }
                    
                    return response;
                })
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public TaskResponse completeTask(CompleteTaskRequest request) {
        // 获取任务信息
        Task task = taskMapper.selectById(request.getTaskId());
        if (task == null || !task.getIsActive()) {
            throw new RuntimeException("任务不存在或已失效");
        }

        // 获取或创建用户任务进度
        QueryWrapper<UserTaskProgress> progressQuery = new QueryWrapper<>();
        progressQuery.eq("user_id", request.getUserId());
        progressQuery.eq("task_id", request.getTaskId());
        UserTaskProgress progress = userTaskProgressMapper.selectOne(progressQuery);
        
        if (progress == null) {
            progress = new UserTaskProgress();
            progress.setUserId(request.getUserId());
            progress.setTaskId(request.getTaskId());
            progress.setStatus("IN_PROGRESS");
            progress.setProgress(0);
            progress.setTarget(1);
            progress.setCompletedTimes(0);
            progress.setCreatedAt(LocalDateTime.now());
            progress.setUpdatedAt(LocalDateTime.now());
            userTaskProgressMapper.insert(progress);
        }

        // 更新进度
        progress.setProgress(progress.getProgress() + 1);
        progress.setCompletedTimes(progress.getCompletedTimes() + 1);
        progress.setLastCompletedAt(LocalDateTime.now());
        progress.setUpdatedAt(LocalDateTime.now());
        
        if (progress.getProgress() >= progress.getTarget()) {
            progress.setStatus("COMPLETED");
            
            // 发放奖励
            AddInspirationRequest addRequest = new AddInspirationRequest();
            addRequest.setUserId(request.getUserId());
            addRequest.setAmount(task.getRewardAmount().longValue());
            addRequest.setSource("task");
            addRequest.setDescription("完成任务：" + task.getTitle());
            addRequest.setIsCountedForLevel(true);
            addInspiration(addRequest);
        }
        
        userTaskProgressMapper.updateById(progress);

        TaskResponse response = new TaskResponse();
        BeanUtils.copyProperties(task, response);
        response.setProgress(progress.getProgress());
        response.setTarget(progress.getTarget());
        response.setStatus(progress.getStatus());
        
        return response;
    }

    @Override
    public List<TaskResponse> getTaskProgress(String userId) {
        QueryWrapper<UserTaskProgress> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        queryWrapper.orderByDesc("updated_at");
        
        List<UserTaskProgress> progressList = userTaskProgressMapper.selectList(queryWrapper);
        
        return progressList.stream()
                .map(progress -> {
                    Task task = taskMapper.selectById(progress.getTaskId());
                    TaskResponse response = new TaskResponse();
                    if (task != null) {
                        BeanUtils.copyProperties(task, response);
                    }
                    response.setProgress(progress.getProgress());
                    response.setTarget(progress.getTarget());
                    response.setStatus(progress.getStatus());
                    return response;
                })
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public TaskResponse signIn(String userId) {
        // 创建签到任务完成请求
        CompleteTaskRequest request = new CompleteTaskRequest();
        request.setUserId(userId);
        request.setTaskId("DAILY_SIGN_IN"); // 假设有一个固定的签到任务ID
        request.setCompletionData("{\"signInDate\":\"" + LocalDateTime.now() + "\"}");
        
        return completeTask(request);
    }

    @Override
    public List<TaskResponse> getUserAchievements(String userId) {
        QueryWrapper<UserAchievement> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        queryWrapper.eq("is_unlocked", true);
        queryWrapper.orderByDesc("unlocked_at");
        
        List<UserAchievement> userAchievements = userAchievementMapper.selectList(queryWrapper);
        
        return userAchievements.stream()
                .map(userAchievement -> {
                    Achievement achievement = achievementMapper.selectById(userAchievement.getAchievementId());
                    TaskResponse response = new TaskResponse();
                    if (achievement != null) {
                        response.setId(achievement.getId());
                        response.setTitle(achievement.getName());
                        response.setDescription(achievement.getDescription());
                        response.setRewardAmount(achievement.getRewardAmount());
                    }
                    response.setProgress(userAchievement.getProgress());
                    response.setTarget(userAchievement.getTarget());
                    response.setStatus("COMPLETED");
                    return response;
                })
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public TaskResponse unlockAchievement(String userId, String achievementId) {
        Achievement achievement = achievementMapper.selectById(achievementId);
        if (achievement == null || !achievement.getIsActive()) {
            throw new RuntimeException("成就不存在或已失效");
        }

        // 检查是否已解锁
        QueryWrapper<UserAchievement> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        queryWrapper.eq("achievement_id", achievementId);
        UserAchievement userAchievement = userAchievementMapper.selectOne(queryWrapper);
        
        if (userAchievement != null && userAchievement.getIsUnlocked()) {
            throw new RuntimeException("成就已解锁");
        }

        if (userAchievement == null) {
            userAchievement = new UserAchievement();
            userAchievement.setUserId(userId);
            userAchievement.setAchievementId(achievementId);
            userAchievement.setProgress(1);
            userAchievement.setTarget(1);
            userAchievement.setCreatedAt(LocalDateTime.now());
            userAchievement.setUpdatedAt(LocalDateTime.now());
        }

        userAchievement.setIsUnlocked(true);
        userAchievement.setUnlockedAt(LocalDateTime.now());
        userAchievement.setUpdatedAt(LocalDateTime.now());
        
        if (userAchievement.getId() == null) {
            userAchievementMapper.insert(userAchievement);
        } else {
            userAchievementMapper.updateById(userAchievement);
        }

        // 发放奖励
        AddInspirationRequest addRequest = new AddInspirationRequest();
        addRequest.setUserId(userId);
        addRequest.setAmount(achievement.getRewardAmount().longValue());
        addRequest.setSource("achievement");
        addRequest.setDescription("解锁成就：" + achievement.getName());
        addRequest.setIsCountedForLevel(true);
        addInspiration(addRequest);

        TaskResponse response = new TaskResponse();
        response.setId(achievement.getId());
        response.setTitle(achievement.getName());
        response.setDescription(achievement.getDescription());
        response.setRewardAmount(achievement.getRewardAmount());
        response.setProgress(userAchievement.getProgress());
        response.setTarget(userAchievement.getTarget());
        response.setStatus("COMPLETED");
        
        return response;
    }

    @Override
    public List<TaskResponse> getAvailableAchievements(String userId) {
        QueryWrapper<Achievement> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_active", true);
        
        List<Achievement> achievements = achievementMapper.selectList(queryWrapper);
        
        return achievements.stream()
                .map(achievement -> {
                    TaskResponse response = new TaskResponse();
                    response.setId(achievement.getId());
                    response.setTitle(achievement.getName());
                    response.setDescription(achievement.getDescription());
                    response.setRewardAmount(achievement.getRewardAmount());
                    
                    // 获取用户成就进度
                    QueryWrapper<UserAchievement> userQuery = new QueryWrapper<>();
                    userQuery.eq("user_id", userId);
                    userQuery.eq("achievement_id", achievement.getId());
                    UserAchievement userAchievement = userAchievementMapper.selectOne(userQuery);
                    
                    if (userAchievement != null) {
                        response.setProgress(userAchievement.getProgress());
                        response.setTarget(userAchievement.getTarget());
                        response.setStatus(userAchievement.getIsUnlocked() ? "COMPLETED" : "IN_PROGRESS");
                    } else {
                        response.setProgress(0);
                        response.setTarget(1);
                        response.setStatus("NOT_STARTED");
                    }
                    
                    return response;
                })
                .collect(Collectors.toList());
    }
} 