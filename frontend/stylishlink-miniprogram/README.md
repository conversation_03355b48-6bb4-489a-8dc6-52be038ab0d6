<p align="center">
  <img src="https://github.com/uni-helper/vitesse-uni-app/raw/main/.github/images/preview.png" width="300"/>
</p>

<h2 align="center">
Vitesse for uni-app
</h2>
<p align="center">
  <a href="https://vitesse-uni-app.netlify.app/">📱 在线预览</a>
  <a href="https://vitesse-docs.netlify.app/">📖 阅读文档</a>
</p>

## 特性

- ⚡️ [Vue 3](https://github.com/vuejs/core), [Vite](https://github.com/vitejs/vite), [pnpm](https://pnpm.io/), [esbuild](https://github.com/evanw/esbuild) - 就是快！

- 🗂 [基于文件的路由](./src/pages)

- 📦 [组件自动化加载](./src/components)

- 📑 [布局系统](./src/layouts)

- 🎨 [UnoCSS](https://github.com/unocss/unocss) - 高性能且极具灵活性的即时原子化 CSS 引擎

- 😃 [各种图标集为你所用](https://github.com/antfu/unocss/tree/main/packages/preset-icons)

- 🔥 使用 [新的 `<script setup>` 语法](https://github.com/vuejs/rfcs/pull/227)

- 📥 [API 自动加载](https://github.com/antfu/unplugin-auto-import) - 直接使用 Composition API 无需引入

- 🦾 [TypeScript](https://www.typescriptlang.org/) & [ESLint](https://eslint.org/) - 保证代码质量

## 微信小程序打包与运行指南

### 1. 打包为微信小程序

在项目根目录下执行：
```bash
pnpm build:mp-weixin
```
- 编译产物输出到 `dist/build/mp-weixin` 目录。

### 2. 用微信开发者工具打开
1. 打开微信开发者工具，选择"打开项目"。
2. 选择 `dist/build/mp-weixin` 目录作为项目根目录。
3. 填写你的 AppID（如无可用测试号）。
4. 点击"编译"，即可在模拟器/真机预览小程序效果。

### 3. 本地开发与实时预览
- 本地开发（H5模式）：
  ```bash
  pnpm dev
  ```
- 微信小程序开发模式（自动同步到dist/dev/mp-weixin）：
  ```bash
  pnpm dev:mp-weixin
  ```

### 4. 常见问题与注意事项
- 样式单位建议全部使用 `rpx`，适配微信小程序屏幕。
- 优先使用 `uni.xxx` 跨端API，特殊场景可用 `wx.xxx`。
- 仅支持官方白名单文件类型，详见[官方文档](https://developers.weixin.qq.com/miniprogram/dev/framework/structure.html)。
- 如遇依赖或构建工具有新版本，建议定期执行：
  ```bash
  npx @dcloudio/uvm@latest
  ```
- 分包、云开发、体验版上传等功能可在微信开发者工具中进一步配置。

---
