# API接口联调配置说明

## 环境配置

项目支持三个环境的API接口配置：

### 开发环境 (development)
- **API域名**: `http://************:8080`
- **用途**: 本地开发联调
- **Swagger文档**: `http://************:8080/webjars/swagger-ui/index.html?urls.primaryName=%E7%94%A8%E6%88%B7%E6%9C%8D%E5%8A%A1%20(user)`

### 测试环境 (test)
- **API域名**: `https://api-test.stylishlink.com`
- **用途**: 测试环境联调

### 正式环境 (production)
- **API域名**: `https://api.stylishlink.com`
- **用途**: 生产环境

## 环境切换方法

在 `src/config/env.ts` 文件中修改 `getCurrentEnv()` 函数的返回值：

```typescript
export function getCurrentEnv(): Environment {
  // 开发阶段使用开发环境
  return 'development'

  // 切换到测试环境
  // return 'test'

  // 切换到正式环境
  // return 'production'
}
```

## 已配置的用户服务接口

### 1. 发送验证码
- **路径**: `/api/user/send-code`
- **方法**: POST
- **参数**:
  ```typescript
  {
    phone: string
    type?: 'login' | 'register' // 默认为 'login'
  }
  ```

### 2. 用户登录
- **路径**: `/api/user/login`
- **方法**: POST
- **参数**:
  ```typescript
  {
    phone: string
    code: string
    codeId: string
  }
  ```
- **响应**: JWT token在响应体的data.token字段中返回

### 3. 完善用户信息
- **路径**: `/api/user/complete-profile`
- **方法**: POST

### 4. 获取用户信息
- **路径**: `/api/user/info`
- **方法**: GET

### 5. 更新用户信息
- **路径**: `/api/user/info`
- **方法**: POST

### 6. 用户登出
- **路径**: `/api/user/logout`
- **方法**: POST

## JWT认证机制

- **登录成功**: JWT token通过响应体的data.token字段返回
- **后续请求**: 自动在请求header中携带 `Authorization: Bearer {token}`
- **自动处理**: `utils/request.ts` 已配置自动token提取和注入
- **过期管理**: 响应中包含expiresIn字段，用于客户端token过期管理
- **自动续期**:
  - 在token即将过期前5分钟自动刷新
  - 收到1002认证错误时自动刷新并重试请求
  - 使用refresh token无感知换取新的access token
- **安全保障**: refresh token与access token分离存储，刷新失败时自动清除认证信息

## 文件结构

```
src/
├── config/
│   ├── env.ts          # 环境配置
│   └── api.ts          # API端点配置
├── api/
│   └── user.ts         # 用户服务API
├── utils/
│   └── request.ts      # 请求工具（已支持环境配置）
├── types/
│   └── user.ts         # 用户相关类型定义
└── store/
    └── user.ts         # 用户状态管理（已使用真实API）
```

## 使用示例

```typescript
// 在组件中使用
import * as userApi from '@/api/user'

// 发送验证码
const sendCodeResponse = await userApi.sendCode({
  phone: '13800138000',
  type: 'login'
})

// 用户登录
const loginResponse = await userApi.login({
  phone: '13800138000',
  code: '123456',
  codeId: sendCodeResponse.codeId
})
```

## 注意事项

1. **环境切换**: 修改 `getCurrentEnv()` 函数后重新编译项目
2. **网络请求**: 所有API调用已自动处理JWT认证
3. **错误处理**: API调用失败时会自动显示错误提示
4. **类型安全**: 所有API调用都有完整的TypeScript类型定义

## 调试建议

1. 打开浏览器开发者工具查看网络请求
2. 检查请求URL是否正确拼接
3. 检查请求header中的Authorization字段
4. 查看响应header中的JWT token

## 下一步扩展

可以按需添加其他微服务的API配置：
- 衣橱服务 (wardrobe-service)
- 推荐服务 (recommendation-service)
- AI服务 (ai-service)
- 社交服务 (social-service)
- 等等...
