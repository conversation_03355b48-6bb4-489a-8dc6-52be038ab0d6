{"name": "stylishlink-miniprogram", "version": "0.0.0", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "stylishlink-miniprogram", "version": "0.0.0", "hasInstallScript": true, "license": "MIT", "dependencies": {"@dcloudio/uni-app": "3.0.0-4060620250520001", "@dcloudio/uni-app-harmony": "3.0.0-4060620250520001", "@dcloudio/uni-app-plus": "3.0.0-4060620250520001", "@dcloudio/uni-components": "3.0.0-4060620250520001", "@dcloudio/uni-h5": "3.0.0-4060620250520001", "@dcloudio/uni-mp-alipay": "3.0.0-4060620250520001", "@dcloudio/uni-mp-baidu": "3.0.0-4060620250520001", "@dcloudio/uni-mp-harmony": "3.0.0-4060620250520001", "@dcloudio/uni-mp-jd": "3.0.0-4060620250520001", "@dcloudio/uni-mp-kuaishou": "3.0.0-4060620250520001", "@dcloudio/uni-mp-lark": "3.0.0-4060620250520001", "@dcloudio/uni-mp-qq": "3.0.0-4060620250520001", "@dcloudio/uni-mp-toutiao": "3.0.0-4060620250520001", "@dcloudio/uni-mp-weixin": "3.0.0-4060620250520001", "@dcloudio/uni-mp-xhs": "3.0.0-4060620250520001", "@dcloudio/uni-quickapp-webview": "3.0.0-4060620250520001", "@dcloudio/uni-ui": "^1.5.7", "@vueuse/core": "^12.4.0", "pinia": "2.1.7", "vue": "~3.5.14", "vue-i18n": "^9.14.4"}, "devDependencies": {"@dcloudio/types": "^3.4.15", "@dcloudio/uni-automator": "3.0.0-4060620250520001", "@dcloudio/uni-cli-shared": "3.0.0-4060620250520001", "@dcloudio/uni-stacktracey": "3.0.0-4060620250520001", "@dcloudio/uni-vue-devtools": "3.0.0-4040520250104002", "@dcloudio/vite-plugin-uni": "3.0.0-4060620250520001", "@iconify-json/carbon": "^1.2.5", "@mini-types/alipay": "^3.0.14", "@types/node": "^22.10.6", "@uni-helper/eslint-config": "^0.4.0", "@uni-helper/uni-env": "^0.1.7", "@uni-helper/uni-types": "^1.0.0-alpha.6", "@uni-helper/unocss-preset-uni": "^0.2.11", "@uni-helper/vite-plugin-uni-components": "^0.2.0", "@uni-helper/vite-plugin-uni-layouts": "^0.1.10", "@uni-helper/vite-plugin-uni-manifest": "^0.2.7", "@uni-helper/vite-plugin-uni-pages": "^0.2.28", "@uni-helper/volar-service-uni-pages": "^0.2.28", "@unocss/eslint-config": "^65.4.0", "@vue/runtime-core": "^3.5.14", "@vue/tsconfig": "^0.7.0", "eslint": "^9.27.0", "lint-staged": "^15.4.0", "miniprogram-api-typings": "^4.0.4", "msw": "^2.0.0", "sass": "^1.89.0", "simple-git-hooks": "^2.11.1", "typescript": "~5.7.3", "unocss": "^65.4.0", "unplugin-auto-import": "^19.0.0", "vite": "^5.2.8", "vite-plugin-static-copy": "^3.0.0", "vite-plugin-uni-polyfill": "^0.1.0", "vue-tsc": "^2.2.0"}}, "../../node_modules/.pnpm/@dcloudio+types@3.4.15/node_modules/@dcloudio/types": {"version": "3.4.15", "dev": true, "license": "Apache-2.0", "devDependencies": {"@definitelytyped/dtslint": "^0.0.115", "miniprogram-api-typings": "3.7.1", "ts-morph": "^17.0.1", "tslint": "^5.14.0", "typescript": "5.0.4", "vue": "2.6"}}, "../../node_modules/.pnpm/@dcloudio+uni-app-harmony@3_f87dd63c3be5ef0b6158397a6f953bf5/node_modules/@dcloudio/uni-app-harmony": {"version": "3.0.0-4060620250520001", "license": "Apache-2.0", "dependencies": {"@dcloudio/uni-app-uts": "3.0.0-4060620250520001", "@dcloudio/uni-app-vite": "3.0.0-4060620250520001", "debug": "^4.3.3", "fs-extra": "^10.0.0", "licia": "^1.29.0", "postcss-selector-parser": "^6.0.6"}, "devDependencies": {"@amap/amap-jsapi-types": "^0.0.8", "@dcloudio/uni-app-plus": "3.0.0-4060620250520001", "@dcloudio/uni-cli-shared": "3.0.0-4060620250520001", "@dcloudio/uni-components": "3.0.0-4060620250520001", "@dcloudio/uni-i18n": "3.0.0-4060620250520001", "@dcloudio/uni-shared": "3.0.0-4060620250520001", "@types/google.maps": "^3.45.6", "@types/pako": "1.0.2", "@vue/compiler-sfc": "3.4.21", "autoprefixer": "^10.4.18", "pako": "^1.0.11", "postcss": "^8.4.21", "vue": "3.4.21"}}, "../../node_modules/.pnpm/@dcloudio+uni-app-plus@3.0._68cdccc633bee2044e2786a8a3411435/node_modules/@dcloudio/uni-app-plus": {"version": "3.0.0-4060620250520001", "license": "Apache-2.0", "dependencies": {"@dcloudio/uni-app-uts": "3.0.0-4060620250520001", "@dcloudio/uni-app-vite": "3.0.0-4060620250520001", "@dcloudio/uni-app-vue": "3.0.0-4060620250520001", "debug": "^4.3.3", "fs-extra": "^10.0.0", "licia": "^1.29.0", "postcss-selector-parser": "^6.0.6"}, "devDependencies": {"@amap/amap-jsapi-types": "^0.0.8", "@dcloudio/uni-cli-shared": "3.0.0-4060620250520001", "@dcloudio/uni-components": "3.0.0-4060620250520001", "@dcloudio/uni-h5": "3.0.0-4060620250520001", "@dcloudio/uni-i18n": "3.0.0-4060620250520001", "@dcloudio/uni-shared": "3.0.0-4060620250520001", "@types/google.maps": "^3.45.6", "@types/pako": "1.0.2", "@vue/compiler-sfc": "3.4.21", "autoprefixer": "^10.4.19", "pako": "^1.0.11", "postcss": "^8.4.21", "vue": "3.4.21"}}, "../../node_modules/.pnpm/@dcloudio+uni-app@3.0.0-406_dc7f530ee4a09974f2fe8bcae6ac6638/node_modules/@dcloudio/uni-app": {"version": "3.0.0-4060620250520001", "license": "Apache-2.0", "dependencies": {"@dcloudio/uni-cloud": "3.0.0-4060620250520001", "@dcloudio/uni-components": "3.0.0-4060620250520001", "@dcloudio/uni-console": "3.0.0-4060620250520001", "@dcloudio/uni-i18n": "3.0.0-4060620250520001", "@dcloudio/uni-push": "3.0.0-4060620250520001", "@dcloudio/uni-shared": "3.0.0-4060620250520001", "@dcloudio/uni-stat": "3.0.0-4060620250520001", "@vue/shared": "3.4.21"}, "peerDependencies": {"@dcloudio/types": "^3.4.14"}}, "../../node_modules/.pnpm/@dcloudio+uni-automator@3.0_77d8d6e1345cbb647eede5fc1685786a/node_modules/@dcloudio/uni-automator": {"version": "3.0.0-4060620250520001", "dev": true, "license": "Apache-2.0", "dependencies": {"@dcloudio/uni-cli-shared": "3.0.0-4060620250520001", "address": "^1.1.2", "cross-env": "^7.0.3", "debug": "^4.3.3", "default-gateway": "^6.0.3", "fs-extra": "^10.0.0", "jsonc-parser": "^3.2.0", "licia": "^1.29.0", "merge": "^2.1.1", "qrcode-reader": "^1.0.4", "qrcode-terminal": "^0.12.0", "ws": "^8.4.2"}, "devDependencies": {"@types/debug": "^4.1.7", "@types/fs-extra": "^9.0.13"}, "peerDependencies": {"jest": "27.0.4", "jest-environment-node": "27.5.1"}}, "../../node_modules/.pnpm/@dcloudio+uni-cli-shared@3._4097a85dbd46caa37941b1ccd89c1442/node_modules/@dcloudio/uni-cli-shared": {"version": "3.0.0-4060620250520001", "dev": true, "license": "Apache-2.0", "dependencies": {"@ampproject/remapping": "^2.1.2", "@babel/code-frame": "^7.23.5", "@babel/core": "^7.23.3", "@babel/parser": "^7.23.9", "@babel/types": "^7.20.7", "@dcloudio/uni-i18n": "3.0.0-4060620250520001", "@dcloudio/uni-shared": "3.0.0-4060620250520001", "@intlify/core-base": "9.1.9", "@intlify/shared": "9.1.9", "@intlify/vue-devtools": "9.1.9", "@rollup/pluginutils": "^5.0.5", "@vue/compiler-core": "3.4.21", "@vue/compiler-dom": "3.4.21", "@vue/compiler-sfc": "3.4.21", "@vue/compiler-ssr": "3.4.21", "@vue/server-renderer": "3.4.21", "@vue/shared": "3.4.21", "adm-zip": "^0.5.12", "autoprefixer": "^10.4.19", "base64url": "^3.0.1", "chokidar": "^3.5.3", "compare-versions": "^3.6.0", "debug": "^4.3.3", "es-module-lexer": "^1.2.1", "esbuild": "^0.20.1", "estree-walker": "^2.0.2", "fast-glob": "^3.2.11", "fs-extra": "^10.0.0", "hash-sum": "^2.0.0", "isbinaryfile": "^5.0.2", "jsonc-parser": "^3.2.0", "lines-and-columns": "^2.0.4", "magic-string": "^0.30.7", "merge": "^2.1.1", "mime": "^3.0.0", "module-alias": "^2.2.2", "os-locale-s-fix": "^1.0.8-fix-1", "picocolors": "^1.0.0", "postcss-import": "^14.0.2", "postcss-load-config": "^3.1.1", "postcss-modules": "^4.3.0", "postcss-selector-parser": "^6.0.6", "resolve": "^1.22.1", "source-map-js": "^1.0.2", "tapable": "^2.2.0", "unimport": "4.1.1", "unplugin-auto-import": "19.1.0", "xregexp": "3.1.0"}, "devDependencies": {"@dcloudio/uni-uts-v1": "3.0.0-4060620250520001", "@types/adm-zip": "^0.5.5", "@types/babel__code-frame": "^7.0.6", "@types/babel__core": "^7.1.19", "@types/debug": "^4.1.7", "@types/estree": "^1.0.5", "@types/fs-extra": "^9.0.13", "@types/hash-sum": "^1.0.0", "@types/less": "^3.0.3", "@types/mime": "^2.0.3", "@types/module-alias": "^2.0.4", "@types/resolve": "^1.20.2", "@types/sass": "^1.43.1", "@types/stylus": "^0.48.36", "code-frame": "link:@types/@babel/code-frame", "postcss": "^8.4.21", "vue": "3.4.21"}, "engines": {"node": "^14.18.0 || >=16.0.0"}}, "../../node_modules/.pnpm/@dcloudio+uni-components@3._498e74fd67354e3e43fa07d4a6e29ef4/node_modules/@dcloudio/uni-components": {"version": "3.0.0-4060620250520001", "license": "Apache-2.0", "dependencies": {"@dcloudio/uni-cloud": "3.0.0-4060620250520001", "@dcloudio/uni-h5": "3.0.0-4060620250520001", "@dcloudio/uni-i18n": "3.0.0-4060620250520001"}, "devDependencies": {"@dcloudio/uni-shared": "3.0.0-4060620250520001", "@types/quill": "1.3.10"}}, "../../node_modules/.pnpm/@dcloudio+uni-h5@3.0.0-4060_8ac9860a8ff4cbf52b7676e9229f4271/node_modules/@dcloudio/uni-h5": {"version": "3.0.0-4060620250520001", "license": "Apache-2.0", "dependencies": {"@dcloudio/uni-h5-vite": "3.0.0-4060620250520001", "@dcloudio/uni-h5-vue": "3.0.0-4060620250520001", "@dcloudio/uni-i18n": "3.0.0-4060620250520001", "@dcloudio/uni-shared": "3.0.0-4060620250520001", "@vue/server-renderer": "3.4.21", "@vue/shared": "3.4.21", "debug": "^4.3.3", "localstorage-polyfill": "^1.0.1", "postcss-selector-parser": "^6.0.6", "safe-area-insets": "^1.4.1", "vue-router": "^4.3.0", "xmlhttprequest": "^1.8.0"}, "devDependencies": {"@amap/amap-jsapi-types": "^0.0.8", "@dcloudio/uni-cli-shared": "3.0.0-4060620250520001", "@types/estree": "^1.0.5", "@types/google.maps": "^3.45.6", "acorn-loose": "^8.2.1", "acorn-walk": "^8.2.0", "estree-walker": "^2.0.2", "fast-glob": "^3.2.11", "fs-extra": "^10.0.0", "unplugin-auto-import": "19.1.0", "vue": "3.4.21"}}, "../../node_modules/.pnpm/@dcloudio+uni-mp-alipay@3.0_2c71c92bad11835bcb64bea696fc1074/node_modules/@dcloudio/uni-mp-alipay": {"version": "3.0.0-4060620250520001", "license": "Apache-2.0", "dependencies": {"@dcloudio/uni-cli-shared": "3.0.0-4060620250520001", "@dcloudio/uni-mp-vite": "3.0.0-4060620250520001", "@dcloudio/uni-mp-vue": "3.0.0-4060620250520001", "@dcloudio/uni-shared": "3.0.0-4060620250520001", "@vue/compiler-core": "3.4.21", "@vue/shared": "3.4.21"}, "devDependencies": {"mini-types": "^0.1.7"}}, "../../node_modules/.pnpm/@dcloudio+uni-mp-baidu@3.0._2b3ee215a7e617b87f37e5ccf9d32305/node_modules/@dcloudio/uni-mp-baidu": {"version": "3.0.0-4060620250520001", "license": "Apache-2.0", "dependencies": {"@dcloudio/uni-app": "3.0.0-4060620250520001", "@dcloudio/uni-cli-shared": "3.0.0-4060620250520001", "@dcloudio/uni-mp-compiler": "3.0.0-4060620250520001", "@dcloudio/uni-mp-vite": "3.0.0-4060620250520001", "@dcloudio/uni-mp-vue": "3.0.0-4060620250520001", "@dcloudio/uni-mp-weixin": "3.0.0-4060620250520001", "@dcloudio/uni-shared": "3.0.0-4060620250520001", "@vue/compiler-core": "3.4.21", "@vue/shared": "3.4.21", "jimp": "^0.10.1", "licia": "^1.29.0", "qrcode-reader": "^1.0.4", "qrcode-terminal": "^0.12.0", "ws": "^8.4.2"}}, "../../node_modules/.pnpm/@dcloudio+uni-mp-harmony@3._3aeff2273578545649f50e60cfa44e96/node_modules/@dcloudio/uni-mp-harmony": {"version": "3.0.0-4060620250520001", "license": "Apache-2.0", "dependencies": {"@dcloudio/uni-cli-shared": "3.0.0-4060620250520001", "@dcloudio/uni-mp-toutiao": "3.0.0-4060620250520001", "@dcloudio/uni-mp-vite": "3.0.0-4060620250520001", "@dcloudio/uni-mp-vue": "3.0.0-4060620250520001", "@dcloudio/uni-quickapp-webview": "3.0.0-4060620250520001", "@dcloudio/uni-shared": "3.0.0-4060620250520001", "@vue/shared": "3.4.21"}, "devDependencies": {"@dcloudio/uni-mp-compiler": "3.0.0-4060620250520001"}}, "../../node_modules/.pnpm/@dcloudio+uni-mp-jd@3.0.0-4_fd3228ffbbbc07528edd2750e9215427/node_modules/@dcloudio/uni-mp-jd": {"version": "3.0.0-4060620250520001", "license": "Apache-2.0", "dependencies": {"@dcloudio/uni-cli-shared": "3.0.0-4060620250520001", "@dcloudio/uni-mp-compiler": "3.0.0-4060620250520001", "@dcloudio/uni-mp-vite": "3.0.0-4060620250520001", "@dcloudio/uni-mp-vue": "3.0.0-4060620250520001", "@dcloudio/uni-shared": "3.0.0-4060620250520001", "@vue/shared": "3.4.21"}, "devDependencies": {"@dcloudio/uni-mp-weixin": "3.0.0-4060620250520001", "@vue/compiler-core": "3.4.21"}}, "../../node_modules/.pnpm/@dcloudio+uni-mp-kuaishou@3_b389214eb426e1a0f50416c947361b7c/node_modules/@dcloudio/uni-mp-kuaishou": {"version": "3.0.0-4060620250520001", "license": "Apache-2.0", "dependencies": {"@dcloudio/uni-cli-shared": "3.0.0-4060620250520001", "@dcloudio/uni-mp-compiler": "3.0.0-4060620250520001", "@dcloudio/uni-mp-vite": "3.0.0-4060620250520001", "@dcloudio/uni-mp-vue": "3.0.0-4060620250520001", "@dcloudio/uni-mp-weixin": "3.0.0-4060620250520001", "@dcloudio/uni-shared": "3.0.0-4060620250520001", "@vue/compiler-core": "3.4.21", "@vue/shared": "3.4.21"}}, "../../node_modules/.pnpm/@dcloudio+uni-mp-lark@3.0.0_1a029320eb3e37c14d81f8c64c2710aa/node_modules/@dcloudio/uni-mp-lark": {"version": "3.0.0-4060620250520001", "license": "Apache-2.0", "dependencies": {"@dcloudio/uni-cli-shared": "3.0.0-4060620250520001", "@dcloudio/uni-mp-compiler": "3.0.0-4060620250520001", "@dcloudio/uni-mp-toutiao": "3.0.0-4060620250520001", "@dcloudio/uni-mp-vite": "3.0.0-4060620250520001", "@dcloudio/uni-mp-vue": "3.0.0-4060620250520001", "@dcloudio/uni-shared": "3.0.0-4060620250520001", "@vue/compiler-core": "3.4.21", "@vue/shared": "3.4.21"}}, "../../node_modules/.pnpm/@dcloudio+uni-mp-qq@3.0.0-4_8a3c2941c92d2789a904b03547582a1b/node_modules/@dcloudio/uni-mp-qq": {"version": "3.0.0-4060620250520001", "license": "Apache-2.0", "dependencies": {"@dcloudio/uni-cli-shared": "3.0.0-4060620250520001", "@dcloudio/uni-mp-vite": "3.0.0-4060620250520001", "@dcloudio/uni-mp-vue": "3.0.0-4060620250520001", "@dcloudio/uni-shared": "3.0.0-4060620250520001", "@vue/shared": "3.4.21", "fs-extra": "^10.0.0"}, "devDependencies": {"@dcloudio/uni-mp-weixin": "3.0.0-4060620250520001", "@types/fs-extra": "^9.0.13", "@vue/compiler-core": "3.4.21"}}, "../../node_modules/.pnpm/@dcloudio+uni-mp-toutiao@3._29a31844a62f50f0b279589bc9c84ff2/node_modules/@dcloudio/uni-mp-toutiao": {"version": "3.0.0-4060620250520001", "license": "Apache-2.0", "dependencies": {"@dcloudio/uni-cli-shared": "3.0.0-4060620250520001", "@dcloudio/uni-mp-compiler": "3.0.0-4060620250520001", "@dcloudio/uni-mp-vite": "3.0.0-4060620250520001", "@dcloudio/uni-mp-vue": "3.0.0-4060620250520001", "@dcloudio/uni-shared": "3.0.0-4060620250520001", "@vue/compiler-core": "3.4.21", "@vue/shared": "3.4.21"}}, "../../node_modules/.pnpm/@dcloudio+uni-mp-weixin@3.0_1f7daaddf5caaf0de42754bd26d8d3c6/node_modules/@dcloudio/uni-mp-weixin": {"version": "3.0.0-4060620250520001", "license": "Apache-2.0", "dependencies": {"@dcloudio/uni-cli-shared": "3.0.0-4060620250520001", "@dcloudio/uni-mp-vite": "3.0.0-4060620250520001", "@dcloudio/uni-mp-vue": "3.0.0-4060620250520001", "@dcloudio/uni-shared": "3.0.0-4060620250520001", "@vue/shared": "3.4.21", "jimp": "^0.10.1", "licia": "^1.29.0", "qrcode-reader": "^1.0.4", "qrcode-terminal": "^0.12.0", "ws": "^8.4.2"}, "devDependencies": {"@vue/compiler-core": "3.4.21"}}, "../../node_modules/.pnpm/@dcloudio+uni-mp-xhs@3.0.0-_3e5ed1dfd0b4ab4f5ee5c0a2f14a3b25/node_modules/@dcloudio/uni-mp-xhs": {"version": "3.0.0-4060620250520001", "license": "Apache-2.0", "dependencies": {"@dcloudio/uni-cli-shared": "3.0.0-4060620250520001", "@dcloudio/uni-mp-compiler": "3.0.0-4060620250520001", "@dcloudio/uni-mp-vite": "3.0.0-4060620250520001", "@dcloudio/uni-mp-vue": "3.0.0-4060620250520001", "@dcloudio/uni-shared": "3.0.0-4060620250520001", "@vue/shared": "3.4.21"}, "devDependencies": {"@dcloudio/uni-mp-alipay": "3.0.0-4060620250520001", "@dcloudio/uni-mp-weixin": "3.0.0-4060620250520001", "@vue/compiler-core": "3.4.21"}}, "../../node_modules/.pnpm/@dcloudio+uni-quickapp-webv_c0c9edf7f86b8e55ab581f6f99d6325c/node_modules/@dcloudio/uni-quickapp-webview": {"version": "3.0.0-4060620250520001", "license": "Apache-2.0", "dependencies": {"@dcloudio/uni-cli-shared": "3.0.0-4060620250520001", "@dcloudio/uni-mp-vite": "3.0.0-4060620250520001", "@dcloudio/uni-mp-vue": "3.0.0-4060620250520001", "@dcloudio/uni-shared": "3.0.0-4060620250520001", "@vue/shared": "3.4.21"}, "devDependencies": {"@dcloudio/uni-mp-compiler": "3.0.0-4060620250520001"}}, "../../node_modules/.pnpm/@dcloudio+uni-stacktracey@3.0.0-4060620250520001/node_modules/@dcloudio/uni-stacktracey": {"version": "3.0.0-4060620250520001", "dev": true, "license": "Apache-2.0", "devDependencies": {"@dcloudio/types": "^2.6.12"}}, "../../node_modules/.pnpm/@dcloudio+uni-vue-devtools@_c5efa83d6d74d57482a82cac21afce5b/node_modules/@dcloudio/uni-vue-devtools": {"version": "3.0.0-4040520250104002", "dev": true, "license": "Apache-2.0", "dependencies": {"@dcloudio/uni-cli-shared": "3.0.0-4040520250104002", "detect-port": "^1.5.1", "express": "^4.17.1", "open": "^8.4.0", "socket.io": "^4.4.0"}}, "../../node_modules/.pnpm/@dcloudio+vite-plugin-uni@3_f8784e14707f7079b74a16940e8946b1/node_modules/@dcloudio/vite-plugin-uni": {"version": "3.0.0-4060620250520001", "dev": true, "license": "Apache-2.0", "dependencies": {"@babel/core": "^7.23.3", "@babel/plugin-syntax-import-meta": "^7.10.4", "@babel/plugin-transform-typescript": "^7.23.3", "@dcloudio/uni-cli-shared": "3.0.0-4060620250520001", "@dcloudio/uni-shared": "3.0.0-4060620250520001", "@rollup/pluginutils": "^5.0.5", "@vitejs/plugin-legacy": "5.3.2", "@vitejs/plugin-vue": "5.1.0", "@vitejs/plugin-vue-jsx": "3.1.0", "@vue/compiler-core": "3.4.21", "@vue/compiler-dom": "3.4.21", "@vue/compiler-sfc": "3.4.21", "@vue/shared": "3.4.21", "cac": "6.7.9", "debug": "^4.3.3", "estree-walker": "^2.0.2", "express": "^4.17.1", "fast-glob": "^3.2.11", "fs-extra": "^10.0.0", "hash-sum": "^2.0.0", "jsonc-parser": "^3.2.0", "magic-string": "^0.30.7", "picocolors": "^1.0.0", "terser": "^5.4.0", "unplugin-auto-import": "19.1.0"}, "bin": {"uni": "bin/uni.js"}, "devDependencies": {"@types/debug": "^4.1.7", "@types/estree": "^1.0.5", "@types/express": "^4.17.12", "@types/fs-extra": "^9.0.13", "@types/sass": "^1.16.0", "@vue/babel-plugin-jsx": "^1.1.1", "chokidar": "^3.5.3", "vite": "^5.2.8", "vue": "3.4.21"}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "peerDependencies": {"vite": "^5.2.8"}}, "../../node_modules/.pnpm/@iconify-json+carbon@1.2.8/node_modules/@iconify-json/carbon": {"version": "1.2.8", "dev": true, "license": "Apache-2.0", "dependencies": {"@iconify/types": "*"}}, "../../node_modules/.pnpm/@mini-types+alipay@3.0.14/node_modules/@mini-types/alipay": {"version": "3.0.14", "dev": true, "license": "MIT", "dependencies": {"@mini-types/global": "3.0.14", "@mini-types/my": "3.0.14"}}, "../../node_modules/.pnpm/@types+node@22.15.21/node_modules/@types/node": {"version": "22.15.21", "dev": true, "license": "MIT", "dependencies": {"undici-types": "~6.21.0"}}, "../../node_modules/.pnpm/@uni-helper+eslint-config@0_8124ceba1018c5be68dc4c38952b76eb/node_modules/@uni-helper/eslint-config": {"version": "0.4.0", "dev": true, "license": "MIT", "dependencies": {"@antfu/eslint-config": "^4.1.1", "@eslint/eslintrc": "^3.2.0", "eslint-flat-config-utils": "^2.0.1", "local-pkg": "^1.0.0"}, "devDependencies": {"@types/fs-extra": "^11.0.4", "@types/node": "^22.13.1", "bumpp": "^10.0.2", "eslint": "^9.20.0", "esno": "^4.8.0", "execa": "^9.5.2", "fast-glob": "^3.3.3", "fs-extra": "^11.3.0", "typescript": "^5.7.3", "unbuild": "^3.3.1", "vitest": "^3.0.5"}, "peerDependencies": {"@antfu/eslint-config": "^4.0.1", "eslint": "^9.10.0"}}, "../../node_modules/.pnpm/@uni-helper+uni-env@0.1.7/node_modules/@uni-helper/uni-env": {"version": "0.1.7", "dev": true, "license": "MIT", "dependencies": {"std-env": "^3.7.0"}, "devDependencies": {"@antfu/eslint-config": "^3.8.0", "@types/node": "^22.9.0", "bumpp": "^9.8.1", "eslint": "^9.14.0", "esno": "^4.8.0", "typescript": "^5.6.3", "unbuild": "^2.0.0", "vitest": "^2.1.4"}}, "../../node_modules/.pnpm/@uni-helper+uni-types@1.0.0_eb87bc98a68aec82f20c25707e94a24e/node_modules/@uni-helper/uni-types": {"version": "1.0.0-alpha.6", "dev": true, "license": "MIT", "dependencies": {"@uni-helper/uni-app-types": "1.0.0-alpha.6", "@uni-helper/uni-cloud-types": "1.0.0-alpha.6", "@uni-helper/uni-ui-types": "1.0.0-alpha.6"}, "devDependencies": {"vue": "3.4.38"}, "engines": {"node": ">=14.18"}, "funding": {"url": "https://github.com/ModyQyW/sponsors"}, "peerDependencies": {"@uni-helper/uni-app-types": "1.0.0-alpha.6", "@uni-helper/uni-cloud-types": "1.0.0-alpha.6", "@uni-helper/uni-ui-types": "1.0.0-alpha.6", "typescript": "^5.0.0", "vue": "^3.0.0"}}, "../../node_modules/.pnpm/@uni-helper+unocss-preset-u_4e02bdcd79028e29c3bb8545abad9fc2/node_modules/@uni-helper/unocss-preset-uni": {"version": "0.2.11", "dev": true, "hasInstallScript": true, "license": "MIT", "dependencies": {"@uni-helper/uni-env": "^0.1.7"}, "devDependencies": {"@types/node": "^20.17.12", "@uni-helper/eslint-config": "^0.2.2", "@unocss/preset-legacy-compat": "^0.65.4", "@unocss/preset-mini": "^0.65.4", "@unocss/rule-utils": "^0.65.4", "@unocss/vite": "^0.65.4", "bumpp": "^9.10.0", "eslint": "^9.18.0", "esno": "^4.8.0", "typescript": "^5.7.3", "unbuild": "^3.3.1", "unocss": "^0.65.4", "unocss-applet": "^0.9.0", "vite": "^5.4.11", "vitest": "^2.1.8"}, "peerDependencies": {"@unocss/preset-legacy-compat": ">=0.58", "@unocss/preset-mini": ">=0.58", "@unocss/rule-utils": ">=0.58", "@unocss/vite": ">=0.58", "unocss": ">=0.58", "unocss-applet": ">=0.7"}, "peerDependenciesMeta": {"@unocss/preset-mini": {"optional": true}, "@unocss/vite": {"optional": true}}}, "../../node_modules/.pnpm/@uni-helper+vite-plugin-uni_ba374fd7657ff93ddb52135e1a206148/node_modules/@uni-helper/vite-plugin-uni-pages": {"version": "0.2.28", "dev": true, "funding": ["https://afdian.net/a/kejun", "https://github.com/ModyQyW/sponsors"], "license": "MIT", "dependencies": {"@uni-helper/uni-env": "^0.1.4", "@vue/compiler-sfc": "^3.4.38", "chokidar": "^3.6.0", "debug": "^4.3.6", "detect-indent": "^6.1.0", "detect-newline": "^3.1.0", "fast-glob": "^3.3.2", "json5": "^2.2.3", "lodash.groupby": "^4.6.0", "magic-string": "^0.30.11", "unconfig": "^0.5.5", "yaml": "^2.5.0"}, "devDependencies": {"@antfu/utils": "^0.7.10", "@types/debug": "^4.1.12", "@types/lodash.groupby": "^4.6.9", "@types/node": "^20.15.0", "vite": "^5.4.1"}, "peerDependencies": {"vite": "^5.0.0"}}, "../../node_modules/.pnpm/@uni-helper+vite-plugin-uni_d25b230c3403b55398ac0deb7144e79a/node_modules/@uni-helper/vite-plugin-uni-manifest": {"version": "0.2.8", "dev": true, "license": "MIT", "dependencies": {"c12": "^2.0.4"}, "devDependencies": {"vite": "^5.4.14"}, "peerDependencies": {"vite": "^4.0.0 || ^5.0.0 || ^6.0.0"}}, "../../node_modules/.pnpm/@uni-helper+vite-plugin-uni-components@0.2.0_rollup@4.41.0/node_modules/@uni-helper/vite-plugin-uni-components": {"version": "0.2.0", "dev": true, "license": "MIT", "dependencies": {"@antfu/utils": "^0.7.2", "@rollup/pluginutils": "^5.0.2", "chokidar": "^3.5.3", "debug": "^4.3.4", "fast-glob": "^3.2.12", "local-pkg": "^0.4.3", "magic-string": "^0.30.0", "minimatch": "^8.0.3", "resolve": "^1.22.2"}, "devDependencies": {"@babel/parser": "^7.21.4", "@babel/types": "^7.21.4", "@types/debug": "^4.1.7", "@types/minimatch": "^5.1.2", "@types/node": "^18.15.11", "@types/resolve": "^1.20.2", "bumpp": "^9.1.0", "esno": "^0.16.3", "estree-walker": "^3.0.3", "pathe": "^1.1.0", "rollup": "^3.20.2"}}, "../../node_modules/.pnpm/@uni-helper+vite-plugin-uni-layouts@0.1.10_rollup@4.41.0/node_modules/@uni-helper/vite-plugin-uni-layouts": {"version": "0.1.10", "dev": true, "license": "MIT", "dependencies": {"@babel/types": "^7.23.5", "@uni-helper/uni-env": "^0.1.1", "@vue/compiler-core": "^3.3.11", "@vue/compiler-sfc": "^3.3.11", "ast-kit": "^0.11.3", "c12": "^1.5.1", "chokidar": "^3.5.3", "fast-glob": "^3.3.2", "jsonc-parser": "^3.2.0", "magic-string": "^0.30.5", "scule": "^1.1.1"}, "devDependencies": {"@antfu/eslint-config": "1.0.0-beta.26", "@types/node": "^20.10.4", "bumpp": "^9.2.1", "eslint": "^8.55.0", "typescript": "^5.3.3", "unbuild": "^2.0.0", "vite": "^4.4.12", "vitest": "^1.0.4"}}, "../../node_modules/.pnpm/@uni-helper+volar-service-uni-pages@0.2.28/node_modules/@uni-helper/volar-service-uni-pages": {"version": "0.2.28", "dev": true, "funding": ["https://afdian.net/a/kejun", "https://github.com/ModyQyW/sponsors"], "license": "MIT", "dependencies": {"@uni-helper/pages-json-schema": "^0.2.28", "vscode-json-languageservice": "^5.4.0", "vscode-languageserver-textdocument": "^1.0.12", "yaml-language-server": "^1.15.0"}, "devDependencies": {"@types/json-schema": "^7.0.15", "@volar/language-service": "^1.11.1"}, "peerDependencies": {"@volar/language-service": "^1.11.1"}, "peerDependenciesMeta": {"@volar/language-service": {"optional": true}}}, "../../node_modules/.pnpm/@unocss+eslint-config@65.5._4b002e10349c1fcb4a5d8a8f41d5b2c9/node_modules/@unocss/eslint-config": {"version": "65.5.0", "dev": true, "license": "MIT", "dependencies": {"@unocss/eslint-plugin": "65.5.0"}, "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/antfu"}}, "../../node_modules/.pnpm/@vue+runtime-core@3.5.14/node_modules/@vue/runtime-core": {"version": "3.5.14", "dev": true, "license": "MIT", "dependencies": {"@vue/reactivity": "3.5.14", "@vue/shared": "3.5.14"}}, "../../node_modules/.pnpm/@vue+tsconfig@0.7.0_typescr_e5665ec57b157b2a4709f899de9cfcc9/node_modules/@vue/tsconfig": {"version": "0.7.0", "dev": true, "license": "MIT", "peerDependencies": {"typescript": "5.x", "vue": "^3.4.0"}, "peerDependenciesMeta": {"typescript": {"optional": true}, "vue": {"optional": true}}}, "../../node_modules/.pnpm/@vueuse+core@12.8.2_typescript@5.7.3/node_modules/@vueuse/core": {"version": "12.8.2", "license": "MIT", "dependencies": {"@types/web-bluetooth": "^0.0.21", "@vueuse/metadata": "12.8.2", "@vueuse/shared": "12.8.2", "vue": "^3.5.13"}, "funding": {"url": "https://github.com/sponsors/antfu"}}, "../../node_modules/.pnpm/eslint@9.27.0_jiti@2.4.2/node_modules/eslint": {"version": "9.27.0", "dev": true, "license": "MIT", "dependencies": {"@eslint-community/eslint-utils": "^4.2.0", "@eslint-community/regexpp": "^4.12.1", "@eslint/config-array": "^0.20.0", "@eslint/config-helpers": "^0.2.1", "@eslint/core": "^0.14.0", "@eslint/eslintrc": "^3.3.1", "@eslint/js": "9.27.0", "@eslint/plugin-kit": "^0.3.1", "@humanfs/node": "^0.16.6", "@humanwhocodes/module-importer": "^1.0.1", "@humanwhocodes/retry": "^0.4.2", "@types/estree": "^1.0.6", "@types/json-schema": "^7.0.15", "ajv": "^6.12.4", "chalk": "^4.0.0", "cross-spawn": "^7.0.6", "debug": "^4.3.2", "escape-string-regexp": "^4.0.0", "eslint-scope": "^8.3.0", "eslint-visitor-keys": "^4.2.0", "espree": "^10.3.0", "esquery": "^1.5.0", "esutils": "^2.0.2", "fast-deep-equal": "^3.1.3", "file-entry-cache": "^8.0.0", "find-up": "^5.0.0", "glob-parent": "^6.0.2", "ignore": "^5.2.0", "imurmurhash": "^0.1.4", "is-glob": "^4.0.0", "json-stable-stringify-without-jsonify": "^1.0.1", "lodash.merge": "^4.6.2", "minimatch": "^3.1.2", "natural-compare": "^1.4.0", "optionator": "^0.9.3"}, "bin": {"eslint": "bin/eslint.js"}, "devDependencies": {"@arethetypeswrong/cli": "^0.18.0", "@babel/core": "^7.4.3", "@babel/preset-env": "^7.4.3", "@cypress/webpack-preprocessor": "^6.0.2", "@eslint/json": "^0.12.0", "@trunkio/launcher": "^1.3.4", "@types/esquery": "^1.5.4", "@types/node": "^22.13.14", "@typescript-eslint/parser": "^8.4.0", "babel-loader": "^8.0.5", "c8": "^7.12.0", "chai": "^4.0.1", "cheerio": "^0.22.0", "common-tags": "^1.8.0", "core-js": "^3.1.3", "cypress": "^14.1.0", "ejs": "^3.0.2", "eslint": "file:.", "eslint-config-eslint": "file:packages/eslint-config-eslint", "eslint-plugin-eslint-plugin": "^6.0.0", "eslint-plugin-expect-type": "^0.6.0", "eslint-plugin-yml": "^1.14.0", "eslint-release": "^3.3.0", "eslint-rule-composer": "^0.3.0", "eslump": "^3.0.0", "esprima": "^4.0.1", "fast-glob": "^3.2.11", "fs-teardown": "^0.1.3", "glob": "^10.0.0", "globals": "^15.0.0", "got": "^11.8.3", "gray-matter": "^4.0.3", "jiti": "^2.1.0", "knip": "^5.32.0", "lint-staged": "^11.0.0", "load-perf": "^0.2.0", "markdown-it": "^12.2.0", "markdown-it-container": "^3.0.0", "marked": "^4.0.8", "metascraper": "^5.25.7", "metascraper-description": "^5.25.7", "metascraper-image": "^5.29.3", "metascraper-logo": "^5.25.7", "metascraper-logo-favicon": "^5.25.7", "metascraper-title": "^5.25.7", "mocha": "^10.7.3", "node-polyfill-webpack-plugin": "^1.0.3", "npm-license": "^0.3.3", "pirates": "^4.0.5", "progress": "^2.0.3", "proxyquire": "^2.0.1", "recast": "^0.23.0", "regenerator-runtime": "^0.14.0", "semver": "^7.5.3", "shelljs": "^0.9.0", "sinon": "^11.0.0", "typescript": "^5.3.3", "webpack": "^5.23.0", "webpack-cli": "^4.5.0", "yorkie": "^2.0.0"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"url": "https://eslint.org/donate"}, "peerDependencies": {"jiti": "*"}, "peerDependenciesMeta": {"jiti": {"optional": true}}}, "../../node_modules/.pnpm/lint-staged@15.5.2/node_modules/lint-staged": {"version": "15.5.2", "dev": true, "license": "MIT", "dependencies": {"chalk": "^5.4.1", "commander": "^13.1.0", "debug": "^4.4.0", "execa": "^8.0.1", "lilconfig": "^3.1.3", "listr2": "^8.2.5", "micromatch": "^4.0.8", "pidtree": "^0.6.0", "string-argv": "^0.3.2", "yaml": "^2.7.0"}, "bin": {"lint-staged": "bin/lint-staged.js"}, "devDependencies": {"@changesets/changelog-github": "0.5.1", "@changesets/cli": "2.28.1", "@commitlint/cli": "19.8.0", "@commitlint/config-conventional": "19.8.0", "@eslint/js": "9.22.0", "consolemock": "1.1.0", "cross-env": "7.0.3", "eslint": "9.22.0", "eslint-config-prettier": "10.1.1", "eslint-plugin-jest": "28.11.0", "eslint-plugin-n": "17.16.2", "eslint-plugin-prettier": "5.2.3", "eslint-plugin-simple-import-sort": "12.1.1", "husky": "9.1.7", "jest": "29.7.0", "jest-snapshot-serializer-ansi": "2.2.1", "mock-stdin": "1.0.0", "prettier": "3.5.3", "semver": "7.7.1", "typescript": "5.8.2"}, "engines": {"node": ">=18.12.0"}, "funding": {"url": "https://opencollective.com/lint-staged"}}, "../../node_modules/.pnpm/miniprogram-api-typings@4.0.7/node_modules/miniprogram-api-typings": {"version": "4.0.7", "dev": true, "license": "MIT", "devDependencies": {"@typescript-eslint/eslint-plugin": "^5.46.0", "@typescript-eslint/parser": "^5.46.0", "eslint": "^8.29.0", "tsd": "^0.25.0", "typescript": "^4.9.4"}}, "../../node_modules/.pnpm/msw@2.8.4_@types+node@22.15.21_typescript@5.7.3/node_modules/msw": {"version": "2.8.4", "dev": true, "hasInstallScript": true, "license": "MIT", "dependencies": {"@bundled-es-modules/cookie": "^2.0.1", "@bundled-es-modules/statuses": "^1.0.1", "@bundled-es-modules/tough-cookie": "^0.1.6", "@inquirer/confirm": "^5.0.0", "@mswjs/interceptors": "^0.37.0", "@open-draft/deferred-promise": "^2.2.0", "@open-draft/until": "^2.1.0", "@types/cookie": "^0.6.0", "@types/statuses": "^2.0.4", "graphql": "^16.8.1", "headers-polyfill": "^4.0.2", "is-node-process": "^1.2.0", "outvariant": "^1.4.3", "path-to-regexp": "^6.3.0", "picocolors": "^1.1.1", "strict-event-emitter": "^0.5.1", "type-fest": "^4.26.1", "yargs": "^17.7.2"}, "bin": {"msw": "cli/index.js"}, "devDependencies": {"@commitlint/cli": "^18.4.4", "@commitlint/config-conventional": "^18.4.4", "@fastify/websocket": "^8.3.1", "@open-draft/test-server": "^0.4.2", "@ossjs/release": "^0.8.1", "@playwright/test": "^1.48.0", "@types/express": "^4.17.21", "@types/json-bigint": "^1.0.4", "@types/node": "~18.19.28", "@typescript-eslint/eslint-plugin": "^8.8.1", "@typescript-eslint/parser": "^8.8.1", "@web/dev-server": "^0.4.6", "axios": "^1.7.7", "babel-minify": "^0.5.1", "commitizen": "^4.3.1", "cross-env": "^7.0.3", "cross-fetch": "^4.0.0", "cz-conventional-changelog": "3.3.0", "esbuild": "^0.25.3", "esbuild-loader": "^4.2.2", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "express": "^5.0.0", "fastify": "^4.26.0", "fs-teardown": "^0.3.0", "glob": "^11.0.0", "jsdom": "^25.0.1", "json-bigint": "^1.0.0", "knip": "^5.51.1", "lint-staged": "^15.2.10", "msw": "2.8.4", "page-with": "^0.6.1", "prettier": "^3.4.2", "publint": "^0.3.12", "regenerator-runtime": "^0.14.1", "rimraf": "^6.0.1", "simple-git-hooks": "^2.9.0", "tsup": "^8.4.0", "typescript": "^5.5.2", "undici": "^6.20.0", "url-loader": "^4.1.1", "vitest": "^3.1.3", "vitest-environment-miniflare": "^2.14.4", "webpack": "^5.95.0", "webpack-http-server": "^0.5.0"}, "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/mswjs"}, "peerDependencies": {"typescript": ">= 4.8.x"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "../../node_modules/.pnpm/pinia@2.1.7_typescript@5.7.3_vue@3.5.14_typescript@5.7.3_/node_modules/pinia": {"version": "2.1.7", "license": "MIT", "dependencies": {"@vue/devtools-api": "^6.5.0", "vue-demi": ">=0.14.5"}, "devDependencies": {"@microsoft/api-extractor": "7.34.4", "@vue/test-utils": "^2.4.0"}, "funding": {"url": "https://github.com/sponsors/posva"}, "peerDependencies": {"@vue/composition-api": "^1.4.0", "typescript": ">=4.4.4", "vue": "^2.6.14 || ^3.3.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}, "typescript": {"optional": true}}}, "../../node_modules/.pnpm/sass@1.89.0/node_modules/sass": {"version": "1.89.0", "dev": true, "license": "MIT", "dependencies": {"chokidar": "^4.0.0", "immutable": "^5.0.2", "source-map-js": ">=0.6.2 <2.0.0"}, "bin": {"sass": "sass.js"}, "engines": {"node": ">=14.0.0"}, "optionalDependencies": {"@parcel/watcher": "^2.4.1"}}, "../../node_modules/.pnpm/simple-git-hooks@2.13.0/node_modules/simple-git-hooks": {"version": "2.13.0", "dev": true, "hasInstallScript": true, "license": "MIT", "bin": {"simple-git-hooks": "cli.js"}}, "../../node_modules/.pnpm/typescript@5.7.3/node_modules/typescript": {"version": "5.7.3", "dev": true, "license": "Apache-2.0", "bin": {"tsc": "bin/tsc", "tsserver": "bin/tsserver"}, "devDependencies": {"@dprint/formatter": "^0.4.1", "@dprint/typescript": "0.93.0", "@esfx/canceltoken": "^1.0.0", "@eslint/js": "^9.11.1", "@octokit/rest": "^21.0.2", "@types/chai": "^4.3.20", "@types/diff": "^5.2.2", "@types/minimist": "^1.2.5", "@types/mocha": "^10.0.8", "@types/ms": "^0.7.34", "@types/node": "latest", "@types/source-map-support": "^0.5.10", "@types/which": "^3.0.4", "@typescript-eslint/rule-tester": "^8.8.0", "@typescript-eslint/type-utils": "^8.8.0", "@typescript-eslint/utils": "^8.8.0", "azure-devops-node-api": "^14.1.0", "c8": "^10.1.2", "chai": "^4.5.0", "chalk": "^4.1.2", "chokidar": "^3.6.0", "diff": "^5.2.0", "dprint": "^0.47.2", "esbuild": "^0.24.0", "eslint": "^9.11.1", "eslint-formatter-autolinkable-stylish": "^1.4.0", "eslint-plugin-regexp": "^2.6.0", "fast-xml-parser": "^4.5.0", "glob": "^10.4.5", "globals": "^15.9.0", "hereby": "^1.10.0", "jsonc-parser": "^3.3.1", "knip": "^5.30.6", "minimist": "^1.2.8", "mocha": "^10.7.3", "mocha-fivemat-progress-reporter": "^0.1.0", "monocart-coverage-reports": "^2.11.0", "ms": "^2.1.3", "playwright": "^1.47.2", "source-map-support": "^0.5.21", "tslib": "^2.7.0", "typescript": "^5.6.2", "typescript-eslint": "^8.8.0", "which": "^3.0.1"}, "engines": {"node": ">=14.17"}}, "../../node_modules/.pnpm/unocss@65.5.0_postcss@8.5.3_c0db6dee1c1a4f91178b90407f669f27/node_modules/unocss": {"version": "65.5.0", "dev": true, "license": "MIT", "dependencies": {"@unocss/astro": "65.5.0", "@unocss/cli": "65.5.0", "@unocss/core": "65.5.0", "@unocss/postcss": "65.5.0", "@unocss/preset-attributify": "65.5.0", "@unocss/preset-icons": "65.5.0", "@unocss/preset-mini": "65.5.0", "@unocss/preset-tagify": "65.5.0", "@unocss/preset-typography": "65.5.0", "@unocss/preset-uno": "65.5.0", "@unocss/preset-web-fonts": "65.5.0", "@unocss/preset-wind": "65.5.0", "@unocss/transformer-attributify-jsx": "65.5.0", "@unocss/transformer-compile-class": "65.5.0", "@unocss/transformer-directives": "65.5.0", "@unocss/transformer-variant-group": "65.5.0", "@unocss/vite": "65.5.0"}, "devDependencies": {"@unocss/webpack": "65.5.0", "vite": "^6.1.0"}, "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/antfu"}, "peerDependencies": {"@unocss/webpack": "65.5.0", "vite": "^2.9.0 || ^3.0.0-0 || ^4.0.0 || ^5.0.0-0 || ^6.0.0-0"}, "peerDependenciesMeta": {"@unocss/webpack": {"optional": true}, "vite": {"optional": true}}}, "../../node_modules/.pnpm/unplugin-auto-import@19.2.0_1dc9ae0740bcbae0a5e93f7a761ffc94/node_modules/unplugin-auto-import": {"version": "19.2.0", "dev": true, "license": "MIT", "dependencies": {"local-pkg": "^1.1.1", "magic-string": "^0.30.17", "picomatch": "^4.0.2", "unimport": "^4.2.0", "unplugin": "^2.3.2", "unplugin-utils": "^0.2.4"}, "devDependencies": {"@antfu/eslint-config": "^4.12.1", "@antfu/ni": "^24.3.0", "@antfu/utils": "^9.2.0", "@nuxt/kit": "^3.17.2", "@nuxt/schema": "^3.17.2", "@svgr/plugin-jsx": "^8.1.0", "@types/node": "^22.15.12", "@types/picomatch": "^3.0.2", "@types/resolve": "^1.20.6", "@vueuse/metadata": "^13.1.0", "bumpp": "^10.1.0", "eslint": "^9.26.0", "esno": "^4.8.0", "fast-glob": "^3.3.3", "publint": "^0.3.12", "rollup": "^4.40.2", "tsup": "^8.4.0", "typescript": "^5.8.3", "vite": "^6.3.5", "vitest": "^3.1.3", "webpack": "^5.99.7"}, "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/antfu"}, "peerDependencies": {"@nuxt/kit": "^3.2.2", "@vueuse/core": "*"}, "peerDependenciesMeta": {"@nuxt/kit": {"optional": true}, "@vueuse/core": {"optional": true}}}, "../../node_modules/.pnpm/vite-plugin-static-copy@3.0_4973c6403afb40bbce525cdb993b6b5d/node_modules/vite-plugin-static-copy": {"version": "3.0.0", "dev": true, "license": "MIT", "dependencies": {"chokidar": "^3.5.3", "fs-extra": "^11.3.0", "p-map": "^7.0.3", "picocolors": "^1.1.1", "tinyglobby": "^0.2.13"}, "devDependencies": {"@changesets/changelog-github": "^0.5.1", "@changesets/cli": "^2.29.2", "@eslint/js": "^9.26.0", "@polka/url": "^1.0.0-next.29", "@types/eslint-config-prettier": "^6.11.3", "@types/fs-extra": "^11.0.4", "@types/node": "^22.15.3", "@types/throttle-debounce": "^5.0.2", "@vitest/eslint-plugin": "^1.1.44", "eslint": "^9.26.0", "eslint-config-prettier": "^10.1.2", "mrmime": "^2.0.1", "node-fetch": "^3.3.2", "prettier": "^3.5.3", "throttle-debounce": "^5.0.2", "tsdown": "^0.11.2", "typescript": "^5.8.3", "typescript-eslint": "^8.31.1", "vite": "^6.3.4", "vitest": "^3.1.2"}, "engines": {"node": "^18.0.0 || >=20.0.0"}, "peerDependencies": {"vite": "^5.0.0 || ^6.0.0"}}, "../../node_modules/.pnpm/vite-plugin-uni-polyfill@0.1.0/node_modules/vite-plugin-uni-polyfill": {"version": "0.1.0", "dev": true, "license": "MIT", "devDependencies": {"@antfu/eslint-config": "^2.23.0", "@antfu/ni": "^0.22.0", "@antfu/utils": "^0.7.10", "@types/node": "^20.14.11", "bumpp": "^9.4.1", "eslint": "^9.7.0", "esno": "^4.7.0", "lint-staged": "^15.2.7", "pnpm": "^9.5.0", "simple-git-hooks": "^2.11.1", "typescript": "^5.5.3", "unbuild": "^2.0.0", "vite": "^5.3.4", "vitest": "^2.0.3"}, "funding": {"url": "https://github.com/sponsors/Ares-Chang"}}, "../../node_modules/.pnpm/vite@5.2.8_@types+node@22.15.21_sass@1.89.0_terser@5.39.2/node_modules/vite": {"version": "5.2.8", "dev": true, "license": "MIT", "dependencies": {"esbuild": "^0.20.1", "postcss": "^8.4.38", "rollup": "^4.13.0"}, "bin": {"vite": "bin/vite.js"}, "devDependencies": {"@ampproject/remapping": "^2.3.0", "@babel/parser": "^7.24.1", "@jridgewell/trace-mapping": "^0.3.25", "@polka/compression": "^1.0.0-next.25", "@rollup/plugin-alias": "^5.1.0", "@rollup/plugin-commonjs": "^25.0.7", "@rollup/plugin-dynamic-import-vars": "^2.1.2", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "15.2.3", "@rollup/plugin-typescript": "^11.1.6", "@rollup/pluginutils": "^5.1.0", "@types/escape-html": "^1.0.4", "@types/pnpapi": "^0.0.5", "acorn": "^8.11.3", "acorn-walk": "^8.3.2", "artichokie": "^0.2.0", "cac": "^6.7.14", "chokidar": "^3.6.0", "connect": "^3.7.0", "convert-source-map": "^2.0.0", "cors": "^2.8.5", "cross-spawn": "^7.0.3", "debug": "^4.3.4", "dep-types": "link:./src/types", "dotenv": "^16.4.5", "dotenv-expand": "^11.0.6", "es-module-lexer": "^1.5.0", "escape-html": "^1.0.3", "estree-walker": "^3.0.3", "etag": "^1.8.1", "fast-glob": "^3.3.2", "http-proxy": "^1.18.1", "launch-editor-middleware": "^2.6.1", "lightningcss": "^1.24.1", "magic-string": "^0.30.8", "micromatch": "^4.0.5", "mlly": "^1.6.1", "mrmime": "^2.0.0", "open": "^8.4.2", "parse5": "^7.1.2", "pathe": "^1.1.2", "periscopic": "^4.0.2", "picocolors": "^1.0.0", "picomatch": "^2.3.1", "postcss-import": "^16.1.0", "postcss-load-config": "^4.0.2", "postcss-modules": "^6.0.0", "resolve.exports": "^2.0.2", "rollup-plugin-dts": "^6.1.0", "rollup-plugin-esbuild": "^6.1.1", "rollup-plugin-license": "^3.3.1", "sass": "^1.72.0", "sirv": "^2.0.4", "source-map-support": "^0.5.21", "strip-ansi": "^7.1.0", "strip-literal": "^2.1.0", "tsconfck": "^3.0.3", "tslib": "^2.6.2", "types": "link:./types", "ufo": "^1.5.3", "ws": "^8.16.0"}, "engines": {"node": "^18.0.0 || >=20.0.0"}, "funding": {"url": "https://github.com/vitejs/vite?sponsor=1"}, "optionalDependencies": {"fsevents": "~2.3.3"}, "peerDependencies": {"@types/node": "^18.0.0 || >=20.0.0", "less": "*", "lightningcss": "^1.21.0", "sass": "*", "stylus": "*", "sugarss": "*", "terser": "^5.4.0"}, "peerDependenciesMeta": {"@types/node": {"optional": true}, "less": {"optional": true}, "lightningcss": {"optional": true}, "sass": {"optional": true}, "stylus": {"optional": true}, "sugarss": {"optional": true}, "terser": {"optional": true}}}, "../../node_modules/.pnpm/vue-i18n@9.14.4_vue@3.5.14_typescript@5.7.3_/node_modules/vue-i18n": {"version": "9.14.4", "license": "MIT", "dependencies": {"@intlify/core-base": "9.14.4", "@intlify/shared": "9.14.4", "@vue/devtools-api": "^6.5.0"}, "devDependencies": {"@intlify/devtools-if": "9.14.4", "@intlify/vue-devtools": "9.14.4"}, "engines": {"node": ">= 16"}, "funding": {"url": "https://github.com/sponsors/kazupon"}, "peerDependencies": {"vue": "^3.0.0"}}, "../../node_modules/.pnpm/vue-tsc@2.2.10_typescript@5.7.3/node_modules/vue-tsc": {"version": "2.2.10", "dev": true, "license": "MIT", "dependencies": {"@volar/typescript": "~2.4.11", "@vue/language-core": "2.2.10"}, "bin": {"vue-tsc": "bin/vue-tsc.js"}, "devDependencies": {"@types/node": "^22.10.4"}, "peerDependencies": {"typescript": ">=5.0.0"}}, "../../node_modules/.pnpm/vue@3.5.14_typescript@5.7.3/node_modules/vue": {"version": "3.5.14", "license": "MIT", "dependencies": {"@vue/compiler-dom": "3.5.14", "@vue/compiler-sfc": "3.5.14", "@vue/runtime-dom": "3.5.14", "@vue/server-renderer": "3.5.14", "@vue/shared": "3.5.14"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/@dcloudio/types": {"resolved": "../../node_modules/.pnpm/@dcloudio+types@3.4.15/node_modules/@dcloudio/types", "link": true}, "node_modules/@dcloudio/uni-app": {"resolved": "../../node_modules/.pnpm/@dcloudio+uni-app@3.0.0-406_dc7f530ee4a09974f2fe8bcae6ac6638/node_modules/@dcloudio/uni-app", "link": true}, "node_modules/@dcloudio/uni-app-harmony": {"resolved": "../../node_modules/.pnpm/@dcloudio+uni-app-harmony@3_f87dd63c3be5ef0b6158397a6f953bf5/node_modules/@dcloudio/uni-app-harmony", "link": true}, "node_modules/@dcloudio/uni-app-plus": {"resolved": "../../node_modules/.pnpm/@dcloudio+uni-app-plus@3.0._68cdccc633bee2044e2786a8a3411435/node_modules/@dcloudio/uni-app-plus", "link": true}, "node_modules/@dcloudio/uni-automator": {"resolved": "../../node_modules/.pnpm/@dcloudio+uni-automator@3.0_77d8d6e1345cbb647eede5fc1685786a/node_modules/@dcloudio/uni-automator", "link": true}, "node_modules/@dcloudio/uni-cli-shared": {"resolved": "../../node_modules/.pnpm/@dcloudio+uni-cli-shared@3._4097a85dbd46caa37941b1ccd89c1442/node_modules/@dcloudio/uni-cli-shared", "link": true}, "node_modules/@dcloudio/uni-components": {"resolved": "../../node_modules/.pnpm/@dcloudio+uni-components@3._498e74fd67354e3e43fa07d4a6e29ef4/node_modules/@dcloudio/uni-components", "link": true}, "node_modules/@dcloudio/uni-h5": {"resolved": "../../node_modules/.pnpm/@dcloudio+uni-h5@3.0.0-4060_8ac9860a8ff4cbf52b7676e9229f4271/node_modules/@dcloudio/uni-h5", "link": true}, "node_modules/@dcloudio/uni-mp-alipay": {"resolved": "../../node_modules/.pnpm/@dcloudio+uni-mp-alipay@3.0_2c71c92bad11835bcb64bea696fc1074/node_modules/@dcloudio/uni-mp-alipay", "link": true}, "node_modules/@dcloudio/uni-mp-baidu": {"resolved": "../../node_modules/.pnpm/@dcloudio+uni-mp-baidu@3.0._2b3ee215a7e617b87f37e5ccf9d32305/node_modules/@dcloudio/uni-mp-baidu", "link": true}, "node_modules/@dcloudio/uni-mp-harmony": {"resolved": "../../node_modules/.pnpm/@dcloudio+uni-mp-harmony@3._3aeff2273578545649f50e60cfa44e96/node_modules/@dcloudio/uni-mp-harmony", "link": true}, "node_modules/@dcloudio/uni-mp-jd": {"resolved": "../../node_modules/.pnpm/@dcloudio+uni-mp-jd@3.0.0-4_fd3228ffbbbc07528edd2750e9215427/node_modules/@dcloudio/uni-mp-jd", "link": true}, "node_modules/@dcloudio/uni-mp-kuaishou": {"resolved": "../../node_modules/.pnpm/@dcloudio+uni-mp-kuaishou@3_b389214eb426e1a0f50416c947361b7c/node_modules/@dcloudio/uni-mp-kuaishou", "link": true}, "node_modules/@dcloudio/uni-mp-lark": {"resolved": "../../node_modules/.pnpm/@dcloudio+uni-mp-lark@3.0.0_1a029320eb3e37c14d81f8c64c2710aa/node_modules/@dcloudio/uni-mp-lark", "link": true}, "node_modules/@dcloudio/uni-mp-qq": {"resolved": "../../node_modules/.pnpm/@dcloudio+uni-mp-qq@3.0.0-4_8a3c2941c92d2789a904b03547582a1b/node_modules/@dcloudio/uni-mp-qq", "link": true}, "node_modules/@dcloudio/uni-mp-toutiao": {"resolved": "../../node_modules/.pnpm/@dcloudio+uni-mp-toutiao@3._29a31844a62f50f0b279589bc9c84ff2/node_modules/@dcloudio/uni-mp-toutiao", "link": true}, "node_modules/@dcloudio/uni-mp-weixin": {"resolved": "../../node_modules/.pnpm/@dcloudio+uni-mp-weixin@3.0_1f7daaddf5caaf0de42754bd26d8d3c6/node_modules/@dcloudio/uni-mp-weixin", "link": true}, "node_modules/@dcloudio/uni-mp-xhs": {"resolved": "../../node_modules/.pnpm/@dcloudio+uni-mp-xhs@3.0.0-_3e5ed1dfd0b4ab4f5ee5c0a2f14a3b25/node_modules/@dcloudio/uni-mp-xhs", "link": true}, "node_modules/@dcloudio/uni-quickapp-webview": {"resolved": "../../node_modules/.pnpm/@dcloudio+uni-quickapp-webv_c0c9edf7f86b8e55ab581f6f99d6325c/node_modules/@dcloudio/uni-quickapp-webview", "link": true}, "node_modules/@dcloudio/uni-stacktracey": {"resolved": "../../node_modules/.pnpm/@dcloudio+uni-stacktracey@3.0.0-4060620250520001/node_modules/@dcloudio/uni-stacktracey", "link": true}, "node_modules/@dcloudio/uni-ui": {"version": "1.5.7", "resolved": "https://registry.npmjs.org/@dcloudio/uni-ui/-/uni-ui-1.5.7.tgz", "integrity": "sha512-DugxSIrQrze1FLdUOj9a+JEQ0bHGjnJTcGUK1mN/MivKg7nuKJBRWk5Ipa9sUdoBznX6ndz5h2e7Uao6x1CdCw==", "license": "Apache-2.0"}, "node_modules/@dcloudio/uni-vue-devtools": {"resolved": "../../node_modules/.pnpm/@dcloudio+uni-vue-devtools@_c5efa83d6d74d57482a82cac21afce5b/node_modules/@dcloudio/uni-vue-devtools", "link": true}, "node_modules/@dcloudio/vite-plugin-uni": {"resolved": "../../node_modules/.pnpm/@dcloudio+vite-plugin-uni@3_f8784e14707f7079b74a16940e8946b1/node_modules/@dcloudio/vite-plugin-uni", "link": true}, "node_modules/@iconify-json/carbon": {"resolved": "../../node_modules/.pnpm/@iconify-json+carbon@1.2.8/node_modules/@iconify-json/carbon", "link": true}, "node_modules/@mini-types/alipay": {"resolved": "../../node_modules/.pnpm/@mini-types+alipay@3.0.14/node_modules/@mini-types/alipay", "link": true}, "node_modules/@types/node": {"resolved": "../../node_modules/.pnpm/@types+node@22.15.21/node_modules/@types/node", "link": true}, "node_modules/@uni-helper/eslint-config": {"resolved": "../../node_modules/.pnpm/@uni-helper+eslint-config@0_8124ceba1018c5be68dc4c38952b76eb/node_modules/@uni-helper/eslint-config", "link": true}, "node_modules/@uni-helper/uni-env": {"resolved": "../../node_modules/.pnpm/@uni-helper+uni-env@0.1.7/node_modules/@uni-helper/uni-env", "link": true}, "node_modules/@uni-helper/uni-types": {"resolved": "../../node_modules/.pnpm/@uni-helper+uni-types@1.0.0_eb87bc98a68aec82f20c25707e94a24e/node_modules/@uni-helper/uni-types", "link": true}, "node_modules/@uni-helper/unocss-preset-uni": {"resolved": "../../node_modules/.pnpm/@uni-helper+unocss-preset-u_4e02bdcd79028e29c3bb8545abad9fc2/node_modules/@uni-helper/unocss-preset-uni", "link": true}, "node_modules/@uni-helper/vite-plugin-uni-components": {"resolved": "../../node_modules/.pnpm/@uni-helper+vite-plugin-uni-components@0.2.0_rollup@4.41.0/node_modules/@uni-helper/vite-plugin-uni-components", "link": true}, "node_modules/@uni-helper/vite-plugin-uni-layouts": {"resolved": "../../node_modules/.pnpm/@uni-helper+vite-plugin-uni-layouts@0.1.10_rollup@4.41.0/node_modules/@uni-helper/vite-plugin-uni-layouts", "link": true}, "node_modules/@uni-helper/vite-plugin-uni-manifest": {"resolved": "../../node_modules/.pnpm/@uni-helper+vite-plugin-uni_d25b230c3403b55398ac0deb7144e79a/node_modules/@uni-helper/vite-plugin-uni-manifest", "link": true}, "node_modules/@uni-helper/vite-plugin-uni-pages": {"resolved": "../../node_modules/.pnpm/@uni-helper+vite-plugin-uni_ba374fd7657ff93ddb52135e1a206148/node_modules/@uni-helper/vite-plugin-uni-pages", "link": true}, "node_modules/@uni-helper/volar-service-uni-pages": {"resolved": "../../node_modules/.pnpm/@uni-helper+volar-service-uni-pages@0.2.28/node_modules/@uni-helper/volar-service-uni-pages", "link": true}, "node_modules/@unocss/eslint-config": {"resolved": "../../node_modules/.pnpm/@unocss+eslint-config@65.5._4b002e10349c1fcb4a5d8a8f41d5b2c9/node_modules/@unocss/eslint-config", "link": true}, "node_modules/@vue/runtime-core": {"resolved": "../../node_modules/.pnpm/@vue+runtime-core@3.5.14/node_modules/@vue/runtime-core", "link": true}, "node_modules/@vue/tsconfig": {"resolved": "../../node_modules/.pnpm/@vue+tsconfig@0.7.0_typescr_e5665ec57b157b2a4709f899de9cfcc9/node_modules/@vue/tsconfig", "link": true}, "node_modules/@vueuse/core": {"resolved": "../../node_modules/.pnpm/@vueuse+core@12.8.2_typescript@5.7.3/node_modules/@vueuse/core", "link": true}, "node_modules/eslint": {"resolved": "../../node_modules/.pnpm/eslint@9.27.0_jiti@2.4.2/node_modules/eslint", "link": true}, "node_modules/lint-staged": {"resolved": "../../node_modules/.pnpm/lint-staged@15.5.2/node_modules/lint-staged", "link": true}, "node_modules/miniprogram-api-typings": {"resolved": "../../node_modules/.pnpm/miniprogram-api-typings@4.0.7/node_modules/miniprogram-api-typings", "link": true}, "node_modules/msw": {"resolved": "../../node_modules/.pnpm/msw@2.8.4_@types+node@22.15.21_typescript@5.7.3/node_modules/msw", "link": true}, "node_modules/pinia": {"resolved": "../../node_modules/.pnpm/pinia@2.1.7_typescript@5.7.3_vue@3.5.14_typescript@5.7.3_/node_modules/pinia", "link": true}, "node_modules/sass": {"resolved": "../../node_modules/.pnpm/sass@1.89.0/node_modules/sass", "link": true}, "node_modules/simple-git-hooks": {"resolved": "../../node_modules/.pnpm/simple-git-hooks@2.13.0/node_modules/simple-git-hooks", "link": true}, "node_modules/typescript": {"resolved": "../../node_modules/.pnpm/typescript@5.7.3/node_modules/typescript", "link": true}, "node_modules/unocss": {"resolved": "../../node_modules/.pnpm/unocss@65.5.0_postcss@8.5.3_c0db6dee1c1a4f91178b90407f669f27/node_modules/unocss", "link": true}, "node_modules/unplugin-auto-import": {"resolved": "../../node_modules/.pnpm/unplugin-auto-import@19.2.0_1dc9ae0740bcbae0a5e93f7a761ffc94/node_modules/unplugin-auto-import", "link": true}, "node_modules/vite": {"resolved": "../../node_modules/.pnpm/vite@5.2.8_@types+node@22.15.21_sass@1.89.0_terser@5.39.2/node_modules/vite", "link": true}, "node_modules/vite-plugin-static-copy": {"resolved": "../../node_modules/.pnpm/vite-plugin-static-copy@3.0_4973c6403afb40bbce525cdb993b6b5d/node_modules/vite-plugin-static-copy", "link": true}, "node_modules/vite-plugin-uni-polyfill": {"resolved": "../../node_modules/.pnpm/vite-plugin-uni-polyfill@0.1.0/node_modules/vite-plugin-uni-polyfill", "link": true}, "node_modules/vue": {"resolved": "../../node_modules/.pnpm/vue@3.5.14_typescript@5.7.3/node_modules/vue", "link": true}, "node_modules/vue-i18n": {"resolved": "../../node_modules/.pnpm/vue-i18n@9.14.4_vue@3.5.14_typescript@5.7.3_/node_modules/vue-i18n", "link": true}, "node_modules/vue-tsc": {"resolved": "../../node_modules/.pnpm/vue-tsc@2.2.10_typescript@5.7.3/node_modules/vue-tsc", "link": true}}}