import uni from '@uni-helper/eslint-config'

export default uni({
  unocss: true,
  ignores: [
    // 第三方库文件
    'src/static/iconfont/iconfont.js',
    'src/static/iconfont/iconfont.css',
    'src/static/iconfont/demo.css',
    'src/static/iconfont/demo_index.html',

    // 自动生成的文件
    'src/auto-imports.d.ts',
    'src/components.d.ts',
    'src/manifest.json',
    'src/pages.json',
    'src/uni.min.css',

    // Mock Service Worker
    'public/mockServiceWorker.js',

    // 构建输出
    'dist/',
    'node_modules/',

    // CSS 文件
    'src/uni.css',
    'src/uni.scss',
    'src/static/styles/',

    // 配置文件
    '.cursorrules',

    // Markdown文档文件
    '*.md',
    '**/*.md',
  ],
})
