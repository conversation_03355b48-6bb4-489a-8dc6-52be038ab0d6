import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useThemeStore = defineStore('theme', () => {
  // 是否暗色模式
  const isDark = ref(false)

  // 切换主题
  function toggleTheme() {
    isDark.value = !isDark.value
  }

  // 可选：根据系统主题初始化
  function initTheme() {
    // #ifdef H5
    // 可根据 prefers-color-scheme 初始化
    // #endif
    // #ifdef MP-WEIXIN
    // 微信小程序可用 uni.getSystemInfo
    // #endif
  }

  return { isDark, toggleTheme, initTheme }
})
