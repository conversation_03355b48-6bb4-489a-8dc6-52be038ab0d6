/**
 * 今日能量数据管理 Composable
 * 负责获取、转换和管理能量相关数据
 */

import type { TodayEnergyBriefResponse } from '@/api/user'
import type { AdviceItem, EnergyData, LocationInfo, LunarInfo, WeatherData } from '@/types/business'
import { computed, ref } from 'vue'
import { getTodayEnergyBrief } from '@/api/user'
import { useUserStore } from '@/store/user'

// 日期信息接口
interface DateInfo {
  gregorian: string // 公历日期
  lunar: string // 农历信息
}

interface UseEnergyDataOptions {
  /**
   * 是否自动加载数据
   * @default true
   */
  autoLoad?: boolean

  /**
   * 指定日期，格式YYYY-MM-DD
   * @default undefined (今日)
   */
  date?: string
}

export function useEnergyData(options: UseEnergyDataOptions = {}) {
  const { autoLoad = true, date } = options

  // 状态管理
  const loading = ref(false)
  const error = ref<Error | null>(null)
  const energyData = ref<EnergyData>({ score: 0, percentage: 0 })
  const adviceList = ref<AdviceItem[]>([])

  // 日期信息（优先使用API数据）
  const dateInfo = ref<DateInfo>({
    gregorian: new Date().toLocaleDateString('zh-CN', { year: 'numeric', month: 'long', day: 'numeric' }),
    lunar: '农历信息加载中...',
  })

  // 农历和位置信息（将来可能从其他接口获取，目前使用模拟数据）
  const lunarInfo = ref<LunarInfo>({
    month: '二月',
    day: '十九',
    fullDate: '二月十九 己巳卯 丙成',
  })

  const locationInfo = ref<LocationInfo>({
    city: '北京市',
  })

  // 天气数据（将来可能从天气接口获取，目前使用模拟数据）
  const weatherData = ref<WeatherData>({
    temperature: 22,
    condition: '晴',
    icon: 'icon-taiyang',
  })

  const userStore = useUserStore()

  /**
   * 将API响应转换为建议列表格式
   * 包含安全的空值处理，确保数据为null时不影响解析
   */
  function transformToAdviceList(luckyElements: TodayEnergyBriefResponse['luckyElements'] | null): AdviceItem[] {
    // 当luckyElements为null时，提供默认建议内容
    const defaultAdvice = {
      clothingSummary: '今日适合选择舒适自然的服饰搭配',
      accessoriesSummary: '建议选择简约经典的配饰',
      makeupSummary: '清透自然的妆容最适合今天',
    }

    // 安全地获取建议内容，优先使用API数据，回退到默认内容
    const advice = luckyElements
      ? {
          clothingSummary: luckyElements.clothingSummary || defaultAdvice.clothingSummary,
          accessoriesSummary: luckyElements.accessoriesSummary || defaultAdvice.accessoriesSummary,
          makeupSummary: luckyElements.makeupSummary || defaultAdvice.makeupSummary,
        }
      : defaultAdvice

    return [
      {
        id: 'clothing',
        type: 'fashion',
        title: '服饰建议',
        description: advice.clothingSummary,
        icon: 'icon-tixu',
        backgroundColor: 'rgba(147, 51, 234, 0.3)',
      },
      {
        id: 'accessories',
        type: 'accessory',
        title: '配饰建议',
        description: advice.accessoriesSummary,
        icon: 'icon-zhubaopeishi',
        backgroundColor: 'rgba(59, 130, 246, 0.3)',
      },
      {
        id: 'makeup',
        type: 'makeup',
        title: '妆容建议',
        description: advice.makeupSummary,
        icon: 'icon-huazhuangpin',
        backgroundColor: 'rgba(236, 72, 153, 0.3)',
      },
    ]
  }

  /**
   * 获取今日能量数据
   */
  async function fetchEnergyData(queryDate?: string) {
    // 检查用户状态：只有登录且开启能量模式的用户才能调用API
    if (!userStore.isLoggedIn || !userStore.shouldShowEnergySection) {
      console.warn('用户未登录或未开启能量模式，跳过能量数据获取')
      return
    }

    loading.value = true
    error.value = null

    try {
      const response = await getTodayEnergyBrief(queryDate || date)

      // 安全地更新能量数据 - 确保数值有效
      energyData.value = {
        score: typeof response.totalScore === 'number' ? response.totalScore : 0,
        percentage: typeof response.percentage === 'number' ? response.percentage : 0,
      }

      // 转换建议列表 - 即使luckyElements为null也能正常处理
      adviceList.value = transformToAdviceList(response.luckyElements)

      // 安全地更新日期信息（优先使用API数据）
      if (response.dateInfo) {
        dateInfo.value = {
          gregorian: response.dateInfo.gregorian || dateInfo.value.gregorian,
          lunar: response.dateInfo.lunar || dateInfo.value.lunar,
        }

        // 同时更新农历信息以保持向后兼容
        if (response.dateInfo.lunar) {
          lunarInfo.value = {
            month: '',
            day: '',
            fullDate: response.dateInfo.lunar,
          }
        }
      }

      console.warn('今日能量数据获取成功:', response)

      // 记录数据质量信息（便于调试）
      if (!response.luckyElements) {
        console.warn('API返回的luckyElements为null，已使用默认建议内容')
      }
    }
    catch (err) {
      error.value = err as Error
      console.error('获取今日能量数据失败:', err)

      // 错误处理：使用默认数据
      energyData.value = { score: 0, percentage: 0 }
      adviceList.value = []

      // 显示错误提示
      uni.showToast({
        title: '能量数据加载失败',
        icon: 'none',
        duration: 2000,
      })
    }
    finally {
      loading.value = false
    }
  }

  /**
   * 刷新数据
   */
  async function refresh() {
    await fetchEnergyData()
  }

  // 计算属性：是否有数据
  const hasData = computed(() => {
    return energyData.value.score > 0 || adviceList.value.length > 0
  })

  // 计算属性：是否应该显示能量区域
  const shouldShowEnergySection = computed(() => {
    return userStore.shouldShowEnergySection && !error.value
  })

  // 自动加载数据
  if (autoLoad && userStore.isLoggedIn) {
    fetchEnergyData()
  }

  return {
    // 状态
    loading,
    error,
    hasData,
    shouldShowEnergySection,

    // 数据
    energyData,
    adviceList,
    dateInfo, // 新增：API返回的日期信息
    weatherData,
    locationInfo,
    lunarInfo,

    // 方法
    fetchEnergyData,
    refresh,
  }
}
