import { computed, onMounted, ref } from 'vue'

/**
 * 微信小程序胶囊按钮适配 Composable
 * 提供动态计算的PageHeader高度和内容位置，确保与胶囊按钮完美对齐
 */
export function useMenuButton() {
  // 胶囊按钮信息
  const menuButtonInfo = ref<any>(null)

  // 获取胶囊按钮位置信息
  function getMenuButtonInfo() {
    try {
      const info = uni.getMenuButtonBoundingClientRect()
      if (info && info.top !== undefined && info.height) {
        menuButtonInfo.value = info
      }
    }
    catch (error) {
      console.warn('获取胶囊按钮信息失败，使用默认值', error)
    }
  }

  // 计算PageHeader高度
  const headerHeight = computed(() => {
    if (menuButtonInfo.value) {
      // 胶囊按钮底部 + 8px 间距
      return `${menuButtonInfo.value.top + menuButtonInfo.value.height + 8}px`
    }
    // 默认值：使用安全区域 + 内容高度
    return 'max(calc(28px + env(safe-area-inset-top)), 100px)'
  })

  // 计算内容位置 - 与胶囊按钮垂直居中对齐
  const contentTop = computed(() => {
    if (menuButtonInfo.value) {
      // 使内容与胶囊按钮垂直居中对齐
      const centerY = menuButtonInfo.value.top + menuButtonInfo.value.height / 2
      return `${centerY - 14}px` // 14px是内容高度的一半(28px/2)
    }
    // 默认值
    return 'max(calc(env(safe-area-inset-top) + 12px), 56px)'
  })

  // 计算天气信息位置 - 与胶囊按钮顶部对齐
  const weatherAlignTop = computed(() => {
    if (menuButtonInfo.value) {
      return `${menuButtonInfo.value.top}px`
    }
    // 默认值
    return 'calc(env(safe-area-inset-top) + 6px)'
  })

  // 计算内容区域偏移12px - 胶囊按钮底部+12px
  const contentOffset12px = computed(() => {
    if (menuButtonInfo.value) {
      return `${menuButtonInfo.value.top + menuButtonInfo.value.height + 12}px`
    }
    // 默认值
    return 'calc(44px + env(safe-area-inset-top))'
  })

  // 计算主内容区域的padding-top（用于scroll-view等）
  const mainContentPaddingTop = computed(() => {
    if (menuButtonInfo.value) {
      // 胶囊按钮底部 + 8px 间距 + 16px 内容间距
      return `${menuButtonInfo.value.top + menuButtonInfo.value.height + 24}px`
    }
    // 默认值：44px内容高度 + 安全区域
    return 'calc(44px + env(safe-area-inset-top))'
  })

  // 计算scroll-view容器的高度
  const scrollViewHeight = computed(() => {
    const bottomNavHeight = 'var(--bottom-nav-height, 68px)'
    if (menuButtonInfo.value) {
      const headerHeight = menuButtonInfo.value.top + menuButtonInfo.value.height + 8
      return `calc(100vh - ${headerHeight}px - ${bottomNavHeight})`
    }
    // 默认值
    return `calc(100vh - max(calc(28px + env(safe-area-inset-top)), 100px) - ${bottomNavHeight})`
  })

  onMounted(() => {
    getMenuButtonInfo()
  })

  return {
    menuButtonInfo,
    headerHeight,
    contentTop,
    weatherAlignTop,
    contentOffset12px,
    mainContentPaddingTop,
    scrollViewHeight,
    getMenuButtonInfo,
  }
}
