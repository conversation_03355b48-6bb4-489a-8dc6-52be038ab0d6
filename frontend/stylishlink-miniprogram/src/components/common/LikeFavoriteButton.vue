<!-- 点赞收藏按钮组件 -->
<script setup lang="ts">
import { computed, nextTick, ref, watch } from 'vue'

// 按钮类型
export type ActionType = 'like' | 'favorite' | 'share'

// Props 定义
interface Props {
  type: ActionType // 按钮类型
  count?: number // 数字计数
  text?: string // 文字内容
  active?: boolean // 是否激活状态
  size?: 'small' | 'medium' // 尺寸
  disabled?: boolean // 是否禁用
}

const props = withDefaults(defineProps<Props>(), {
  count: 0,
  text: '',
  active: false,
  size: 'medium',
  disabled: false,
})

// Events 定义
const emit = defineEmits<{
  click: [type: ActionType, newActive: boolean, newCount: number]
}>()

// 内部状态管理
const isAnimating = ref(false)
const internalActive = ref(props.active)
const internalCount = ref(props.count || 0)

// 同步外部状态
function syncExternalState() {
  internalActive.value = props.active
  internalCount.value = props.count || 0
}

// 监听外部状态变化
watch(() => [props.active, props.count], () => {
  if (!isAnimating.value) {
    syncExternalState()
  }
}, { immediate: true })

// 处理点击事件
async function handleClick() {
  if (props.disabled || isAnimating.value)
    return

  // 开始动画
  isAnimating.value = true

  // 立即更新内部状态以触发动画
  const newActive = !internalActive.value
  const newCount = newActive ? internalCount.value + 1 : internalCount.value - 1

  internalActive.value = newActive
  internalCount.value = newCount

  // 发射事件给父组件
  emit('click', props.type, newActive, newCount)

  // 等待动画完成后恢复状态同步
  await nextTick()
  setTimeout(() => {
    isAnimating.value = false
    syncExternalState() // 同步外部的真实状态
  }, 600) // 动画持续时间
}

// 计算普通状态图标类名
const normalIconClass = computed(() => {
  const icons = {
    like: 'iconfont icon-aixin like-normal',
    favorite: 'iconfont icon-shoucang favorite-normal',
    share: 'iconfont icon-fenxiang share-icon',
  }
  return icons[props.type]
})

// 计算激活状态图标类名
const filledIconClass = computed(() => {
  const icons = {
    like: 'iconfont icon-aixin_shixin like-filled',
    favorite: 'iconfont icon-shoucang1 favorite-filled',
    share: 'iconfont icon-fenxiang share-icon',
  }
  return icons[props.type]
})

// 计算显示内容
const displayContent = computed(() => {
  if (props.text) {
    return props.text
  }
  return internalCount.value?.toString() || '0'
})

// 计算按钮样式
const buttonClass = computed(() => {
  return [
    'like-favorite-button',
    `like-favorite-button--${props.size}`,
    {
      'like-favorite-button--active': internalActive.value,
      'like-favorite-button--disabled': props.disabled,
      'like-favorite-button--animating': isAnimating.value,
    },
  ]
})
</script>

<template>
  <view
    :class="buttonClass"
    @tap="handleClick"
  >
    <view class="icon-svg-container">
      <text
        v-if="!internalActive"
        :key="`${props.type}-normal-${internalCount}`"
        :class="normalIconClass"
        class="button-icon"
      />
      <text
        v-else
        :key="`${props.type}-filled-${internalCount}`"
        :class="filledIconClass"
        class="button-icon"
      />
    </view>
    <text v-if="displayContent" class="button-content">
      {{ displayContent }}
    </text>
  </view>
</template>

<style scoped lang="scss">
.like-favorite-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.5);
  border-radius: 50px;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;

  // 基础尺寸
  &--small {
    padding: 1px 6px;
    min-width: 36px;
    height: 18px;
    gap: 2px;
  }

  &--medium {
    padding: 4px 12px;
    min-width: 52px;
    height: 28px;
    gap: 4px;
  }

  // 激活状态 - 由图标自身的CSS类处理动画

  // 禁用状态
  &--disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  // 悬停效果
  &:not(.like-favorite-button--disabled):active {
    background: rgba(255, 255, 255, 0.6);
    transform: scale(0.95);
  }
}

.icon-svg-container {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.button-icon {
  font-size: 12px;
  line-height: 1;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  transform-origin: center;
}

.button-content {
  font-size: 12px;
  font-weight: 500;
  color: var(--color-text-primary, #1f2937);
  white-space: nowrap;
  margin-left: 4px;
}

// 点赞图标颜色和动效
.like-normal {
  color: #ff6b6b !important;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.like-filled {
  color: #ff6b6b !important;
  animation: likeAnimation 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

// 收藏图标颜色和动效
.favorite-normal {
  color: #ffc107 !important;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.favorite-filled {
  color: #ffc107 !important;
  animation: favoriteAnimation 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

// 分享图标颜色
.share-icon {
  color: #8b5cf6 !important;
  transition: all 0.2s ease;
}

// 点赞动画
@keyframes likeAnimation {
  0% {
    transform: scale(1);
  }
  15% {
    transform: scale(1.2);
  }
  30% {
    transform: scale(0.95);
  }
  45% {
    transform: scale(1.1);
  }
  60% {
    transform: scale(0.98);
  }
  100% {
    transform: scale(1);
  }
}

// 收藏动画
@keyframes favoriteAnimation {
  0% {
    transform: scale(1) rotate(0deg);
  }
  25% {
    transform: scale(1.1) rotate(-5deg);
  }
  50% {
    transform: scale(1.2) rotate(0deg);
  }
  75% {
    transform: scale(1.1) rotate(5deg);
  }
  100% {
    transform: scale(1) rotate(0deg);
  }
}
</style>
