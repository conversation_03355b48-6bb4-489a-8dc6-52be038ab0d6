<!-- 图标按钮组件 -->
<script setup lang="ts">
import { computed } from 'vue'

// 按钮类型
export type ButtonType = 'primary' | 'secondary' | 'success' | 'warning' | 'danger' | 'ghost'

// 按钮尺寸
export type ButtonSize = 'small' | 'medium' | 'large'

// Props 定义
interface Props {
  icon?: string // 图标类名 - 改为可选
  type?: ButtonType // 按钮类型
  size?: ButtonSize // 按钮尺寸
  disabled?: boolean // 是否禁用
  loading?: boolean // 是否加载中
  round?: boolean // 是否圆形
  text?: string // 按钮文字（可选）
  backgroundColor?: string // 自定义背景色
  color?: string // 自定义文字颜色
  block?: boolean // 是否块级按钮
}

const props = withDefaults(defineProps<Props>(), {
  icon: 'icon-default',
  type: 'primary',
  size: 'medium',
  disabled: false,
  loading: false,
  round: false,
  text: '',
  backgroundColor: '',
  color: '',
  block: false,
})

// Events 定义
const emit = defineEmits<{
  click: [event: Event]
}>()

// 处理点击事件
function handleClick(event: Event) {
  if (!props.disabled && !props.loading) {
    emit('click', event)
  }
}

// 计算按钮样式类
const buttonClass = computed(() => {
  return [
    'icon-button',
    `icon-button--${props.type}`,
    `icon-button--${props.size}`,
    {
      'icon-button--disabled': props.disabled,
      'icon-button--loading': props.loading,
      'icon-button--round': props.round,
      'icon-button--block': props.block,
      'icon-button--text': props.text,
    },
  ]
})

// 计算自定义样式
const customStyle = computed(() => {
  const style: Record<string, string> = {}

  if (props.backgroundColor) {
    style.backgroundColor = props.backgroundColor
  }

  if (props.color) {
    style.color = props.color
  }

  return style
})

// 计算图标样式
const iconStyle = computed(() => {
  const sizes = {
    small: '14px',
    medium: '16px',
    large: '18px',
  }

  return {
    fontSize: sizes[props.size],
  }
})
</script>

<template>
  <view
    :class="buttonClass"
    :style="customStyle"
    @tap="handleClick"
  >
    <!-- 加载图标 -->
    <text
      v-if="loading"
      class="iconfont icon-loading loading-icon"
      :style="iconStyle"
    />

    <!-- 正常图标 -->
    <text
      v-else
      class="iconfont button-icon"
      :class="icon"
      :style="iconStyle"
    />

    <!-- 按钮文字 -->
    <text v-if="text" class="button-text">
      {{ text }}
    </text>
  </view>
</template>

<style scoped lang="scss">
.icon-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  // 基础尺寸
  &--small {
    padding: 6px 12px;
    min-height: 32px;
  }

  &--medium {
    padding: 8px 16px;
    min-height: 40px;
  }

  &--large {
    padding: 12px 20px;
    min-height: 48px;
  }

  // 按钮类型样式
  &--primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;

    &:active {
      background: linear-gradient(135deg, #5a6fd8 0%, #694389 100%);
    }
  }

  &--secondary {
    background: rgba(255, 255, 255, 0.25);
    color: var(--color-text-primary, #1f2937);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);

    &:active {
      background: rgba(255, 255, 255, 0.35);
    }
  }

  &--success {
    background: linear-gradient(135deg, #34d399 0%, #10b981 100%);
    color: white;

    &:active {
      background: linear-gradient(135deg, #22c55e 0%, #059669 100%);
    }
  }

  &--warning {
    background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
    color: white;

    &:active {
      background: linear-gradient(135deg, #f97316 0%, #ea580c 100%);
    }
  }

  &--danger {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;

    &:active {
      background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    }
  }

  &--ghost {
    background: transparent;
    color: var(--color-text-primary, #1f2937);
    border: 1px solid rgba(0, 0, 0, 0.1);

    &:active {
      background: rgba(0, 0, 0, 0.05);
    }
  }

  // 圆形按钮
  &--round {
    border-radius: 50%;
    width: auto;
    aspect-ratio: 1;

    &.icon-button--small {
      width: 32px;
      height: 32px;
      padding: 0;
    }

    &.icon-button--medium {
      width: 40px;
      height: 40px;
      padding: 0;
    }

    &.icon-button--large {
      width: 48px;
      height: 48px;
      padding: 0;
    }
  }

  // 块级按钮
  &--block {
    width: 100%;
  }

  // 禁用状态
  &--disabled {
    opacity: 0.5;
    cursor: not-allowed;

    &:active {
      background: inherit !important;
    }
  }

  // 加载状态
  &--loading {
    cursor: not-allowed;
  }

  // 有文字的按钮
  &--text {
    gap: 6px;
  }
}

.button-icon {
  line-height: 1;
}

.button-text {
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
}

.loading-icon {
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 深色模式适配
@media (prefers-color-scheme: dark) {
  .icon-button--secondary {
    background: rgba(0, 0, 0, 0.25);
    color: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.1);

    &:active {
      background: rgba(0, 0, 0, 0.35);
    }
  }

  .icon-button--ghost {
    color: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.1);

    &:active {
      background: rgba(255, 255, 255, 0.05);
    }
  }
}
</style>
