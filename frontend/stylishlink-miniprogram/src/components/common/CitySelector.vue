<!-- 城市选择器组件 -->
<script setup lang="ts">
import UniPopup from '@dcloudio/uni-ui/lib/uni-popup/uni-popup.vue'
import { computed, ref } from 'vue'

// 城市数据接口
interface CityItem {
  id: string
  name: string
  province?: string
}

// Props 定义
interface Props {
  modelValue: string // 当前选中的城市
  placeholder?: string
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '选择城市',
})

// Events 定义
const emit = defineEmits<{
  'update:modelValue': [value: string]
  'change': [city: CityItem]
}>()

// 弹窗引用
const cityPopup = ref()

// 搜索关键词
const searchKeyword = ref('')

// 热门城市数据
const hotCities = ref<CityItem[]>([
  { id: 'beijing', name: '北京', province: '北京市' },
  { id: 'shanghai', name: '上海', province: '上海市' },
  { id: 'guangzhou', name: '广州', province: '广东省' },
  { id: 'shenzhen', name: '深圳', province: '广东省' },
  { id: 'hangzhou', name: '杭州', province: '浙江省' },
  { id: 'chengdu', name: '成都', province: '四川省' },
  { id: 'wuhan', name: '武汉', province: '湖北省' },
  { id: 'xian', name: '西安', province: '陕西省' },
])

// 全部城市数据（按省份分组）
const allCities = ref<{ [province: string]: CityItem[] }>({
  北京市: [
    { id: 'beijing', name: '北京', province: '北京市' },
  ],
  上海市: [
    { id: 'shanghai', name: '上海', province: '上海市' },
  ],
  广东省: [
    { id: 'guangzhou', name: '广州', province: '广东省' },
    { id: 'shenzhen', name: '深圳', province: '广东省' },
    { id: 'dongguan', name: '东莞', province: '广东省' },
    { id: 'foshan', name: '佛山', province: '广东省' },
    { id: 'zhuhai', name: '珠海', province: '广东省' },
  ],
  浙江省: [
    { id: 'hangzhou', name: '杭州', province: '浙江省' },
    { id: 'ningbo', name: '宁波', province: '浙江省' },
    { id: 'wenzhou', name: '温州', province: '浙江省' },
  ],
  江苏省: [
    { id: 'nanjing', name: '南京', province: '江苏省' },
    { id: 'suzhou', name: '苏州', province: '江苏省' },
    { id: 'wuxi', name: '无锡', province: '江苏省' },
  ],
  四川省: [
    { id: 'chengdu', name: '成都', province: '四川省' },
    { id: 'mianyang', name: '绵阳', province: '四川省' },
  ],
  湖北省: [
    { id: 'wuhan', name: '武汉', province: '湖北省' },
    { id: 'xiangyang', name: '襄阳', province: '湖北省' },
  ],
  陕西省: [
    { id: 'xian', name: '西安', province: '陕西省' },
    { id: 'xianyang', name: '咸阳', province: '陕西省' },
  ],
})

// 搜索结果
const searchResults = computed(() => {
  if (!searchKeyword.value.trim()) {
    return []
  }

  const keyword = searchKeyword.value.toLowerCase()
  const results: CityItem[] = []

  Object.values(allCities.value).forEach((cities) => {
    cities.forEach((city) => {
      if (city.name.toLowerCase().includes(keyword)
        || city.province?.toLowerCase().includes(keyword)) {
        results.push(city)
      }
    })
  })

  return results
})

// 显示的城市名称
const displayName = computed(() => {
  return props.modelValue || props.placeholder
})

// 打开城市选择器
function openSelector() {
  cityPopup.value?.open()
}

// 关闭城市选择器
function closeSelector() {
  cityPopup.value?.close()
  searchKeyword.value = ''
}

// 选择城市
function selectCity(city: CityItem) {
  emit('update:modelValue', city.name)
  emit('change', city)
  closeSelector()
}

// 清空搜索
function clearSearch() {
  searchKeyword.value = ''
}
</script>

<template>
  <view class="city-selector">
    <!-- 城市显示触发区域 -->
    <view class="city-display" @click="openSelector">
      <text class="iconfont icon-dingwei city-icon" />
      <text class="city-name">
        {{ displayName }}
      </text>
      <text class="iconfont arrow-icon icon-xiangxia" />
    </view>

    <!-- 城市选择弹窗 -->
    <uni-popup ref="cityPopup" type="bottom" background-color="#fff" border-radius="20px 20px 0 0">
      <view class="selector-container">
        <!-- 弹窗头部 -->
        <view class="selector-header">
          <text class="cancel-btn" @click="closeSelector">
            取消
          </text>
          <text class="header-title">
            选择城市
          </text>
          <text class="confirm-btn" @click="closeSelector">
            确定
          </text>
        </view>

        <!-- 搜索框 -->
        <view class="search-section">
          <view class="search-box">
            <text class="iconfont search-icon icon-sousuo" />
            <input
              v-model="searchKeyword"
              class="search-input"
              placeholder="搜索城市"
              placeholder-style="color: #9ca3af"
            >
            <text
              v-if="searchKeyword"
              class="iconfont clear-icon icon-guanbi"
              @click="clearSearch"
            />
          </view>
        </view>

        <!-- 城市列表区域 - 设置固定高度和滚动 -->
        <scroll-view
          class="city-list-container"
          scroll-y
          :show-scrollbar="false"
          :enhanced="true"
          :bounces="false"
        >
          <!-- 搜索结果 -->
          <view v-if="searchKeyword && searchResults.length > 0" class="search-results">
            <view class="section-title">
              搜索结果
            </view>
            <view
              v-for="city in searchResults"
              :key="city.id"
              class="city-item"
              @click="selectCity(city)"
            >
              <text class="city-name">
                {{ city.name }}
              </text>
              <text class="city-province">
                {{ city.province }}
              </text>
            </view>
          </view>

          <!-- 无搜索结果 -->
          <view v-else-if="searchKeyword && searchResults.length === 0" class="no-results">
            <text class="no-results-text">
              未找到相关城市
            </text>
          </view>

          <!-- 热门城市和全部城市 -->
          <view v-else class="city-sections">
            <!-- 热门城市 -->
            <view class="hot-cities-section">
              <view class="section-title">
                热门城市
              </view>
              <view class="hot-cities-grid">
                <view
                  v-for="city in hotCities"
                  :key="city.id"
                  class="hot-city-item"
                  :class="{ 'hot-city-item--selected': city.name === modelValue }"
                  @click="selectCity(city)"
                >
                  <text class="hot-city-name">
                    {{ city.name }}
                  </text>
                </view>
              </view>
            </view>

            <!-- 全部城市 -->
            <view class="all-cities-section">
              <view class="section-title">
                全部城市
              </view>
              <view
                v-for="(cities, province) in allCities"
                :key="province"
                class="province-group"
              >
                <view class="province-title">
                  {{ province }}
                </view>
                <view
                  v-for="city in cities"
                  :key="city.id"
                  class="city-item"
                  :class="{ 'city-item--selected': city.name === modelValue }"
                  @click="selectCity(city)"
                >
                  <text class="city-name">
                    {{ city.name }}
                  </text>
                </view>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>
    </uni-popup>
  </view>
</template>

<style scoped lang="scss">
.city-selector {
  display: inline-block;
}

.city-display {
  display: flex;
  align-items: center;
  gap: 4px;
  cursor: pointer;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.85) 0%,
    rgba(255, 255, 255, 0.65) 50%,
    rgba(255, 255, 255, 0.85) 100%
  );
  border: 1px solid rgba(255, 255, 255, 0.8);
  border-radius: 12px;
  padding: 5px 10px;
  transition: all 0.2s ease;
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.06),
    0 1px 4px rgba(0, 0, 0, 0.03);

  &:active {
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.95) 0%,
      rgba(255, 255, 255, 0.75) 50%,
      rgba(255, 255, 255, 0.95) 100%
    );
    transform: scale(0.98);
    box-shadow:
      0 1px 4px rgba(0, 0, 0, 0.08),
      0 0px 2px rgba(0, 0, 0, 0.04);
  }
}

.city-icon {
  font-size: 12px;
  color: #8b5cf6;
  font-weight: 500;
}

.city-name {
  font-size: 12px;
  color: var(--color-text-primary, #1f2937);
  font-weight: 500;
}

.arrow-icon {
  font-size: 10px;
  color: var(--color-text-tertiary, #9ca3af);
  transition: transform 0.2s ease;
}

.selector-container {
  height: 60vh;
  max-height: 500px;
  min-height: 400px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.6);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border-radius: 20px 20px 0 0;
}

.selector-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(10px);
  flex-shrink: 0;
  position: relative;
  z-index: 10;
}

.cancel-btn, .confirm-btn {
  font-size: 14px;
  font-weight: 500;
  color: #8b5cf6;
  cursor: pointer;
  padding: 6px 12px;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.5);
  transition: all 0.2s ease;

  &:active {
    background: rgba(255, 255, 255, 0.6);
    transform: scale(0.98);
  }
}

.header-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--color-text-primary, #1f2937);
}

.search-section {
  padding: 16px 20px;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  flex-shrink: 0;
}

.search-box {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.5);
  border-radius: 16px;
  padding: 8px 16px;
  gap: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.search-icon {
  font-size: 14px;
  color: var(--color-text-secondary, #6b7280);
}

.search-input {
  flex: 1;
  font-size: 12px;
  color: var(--color-text-primary, #1f2937);
  background: transparent;
  border: none;
  outline: none;
  line-height: 1.4;
}

.clear-icon {
  font-size: 12px;
  color: var(--color-text-tertiary, #9ca3af);
  cursor: pointer;
  padding: 6px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(5px);

  &:active {
    background: rgba(255, 255, 255, 0.5);
    transform: scale(0.95);
  }
}

.city-list-container {
  flex: 1;
  min-height: 0;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);

  // 隐藏滚动条
  ::-webkit-scrollbar {
    display: none !important;
    width: 0 !important;
    height: 0 !important;
  }

  scrollbar-width: none !important;
  -ms-overflow-style: none !important;
}

.city-sections {
  padding: 16px 0 20px;
}

.section-title {
  font-size: 10px;
  font-weight: 600;
  color: var(--color-text-secondary, #6b7280);
  margin-bottom: 12px;
  margin-top: 20px;
  padding: 0 20px;

  &:first-child {
    margin-top: 0;
  }
}

.hot-cities-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
  padding: 0 20px;
}

.hot-city-item {
  background: rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.5);
  border-radius: 16px;
  padding: 12px 8px;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

  &:active {
    background: rgba(255, 255, 255, 0.6);
    transform: scale(0.98);
  }

  &--selected {
    background: rgba(139, 92, 246, 0.3);
    border-color: #8b5cf6;
    box-shadow: 0 4px 16px rgba(139, 92, 246, 0.2);

    .hot-city-name {
      color: #8b5cf6;
      font-weight: 600;
    }
  }
}

.hot-city-name {
  font-size: 12px;
  color: var(--color-text-primary, #1f2937);
  font-weight: 500;
}

.province-group {
  margin-bottom: 0;
}

.province-title {
  font-size: 10px;
  color: var(--color-text-tertiary, #9ca3af);
  margin-bottom: 0;
  padding: 8px 20px 4px;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(5px);
  font-weight: 600;
}

.city-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 44px;
  background: rgba(255, 255, 255, 0.1);

  &:last-child {
    border-bottom: none;
  }

  &:active {
    background: rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(5px);
  }

  &--selected {
    background: rgba(139, 92, 246, 0.2);
    backdrop-filter: blur(10px);

    .city-name {
      color: #8b5cf6;
      font-weight: 600;
    }
  }
}

.city-name {
  font-size: 14px;
  color: var(--color-text-primary, #1f2937);
  font-weight: 500;
}

.city-province {
  font-size: 10px;
  color: var(--color-text-tertiary, #9ca3af);
}

.search-results {
  padding: 0 0 20px;
}

.no-results {
  padding: 60px 20px;
  text-align: center;
}

.no-results-text {
  font-size: 12px;
  color: var(--color-text-secondary, #6b7280);
}
</style>
