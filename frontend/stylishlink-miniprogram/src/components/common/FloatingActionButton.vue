<!-- 浮动操作按钮组件 -->
<script setup lang="ts">
import { computed } from 'vue'
// 位置枚举类型
export type FloatingPosition =
  | 'bottom-right'
  | 'bottom-left'
  | 'top-right'
  | 'top-left'
  | 'center-right'
  | 'center-left'

// 尺寸枚举类型
export type FloatingSize = 'small' | 'medium' | 'large'

// Props 定义
interface Props {
  icon?: string // 图标类名 - 改为可选
  position?: FloatingPosition // 位置
  size?: FloatingSize // 尺寸
  backgroundColor?: string // 背景色
  zIndex?: number // 层级
  offset?: { x?: number, y?: number } // 位置偏移
}

const props = withDefaults(defineProps<Props>(), {
  icon: 'icon-add',
  position: 'bottom-right',
  size: 'medium',
  backgroundColor: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
  zIndex: 100,
  offset: () => ({ x: 0, y: 0 }),
})

// Events 定义
const emit = defineEmits<{
  click: []
}>()

// 处理点击事件
function handleClick() {
  emit('click')
}

// 计算位置样式
const positionStyle = computed(() => {
  const offset = props.offset || {}
  const baseOffset = {
    'bottom-right': {
      'right': `calc(5% + ${offset.x || 0}px)`,
      'bottom': `calc(96px + constant(safe-area-inset-bottom) + ${offset.y || 0}px)`,
      'bottom-env': `calc(96px + env(safe-area-inset-bottom) + ${offset.y || 0}px)`,
    },
    'bottom-left': {
      'left': `calc(5% + ${offset.x || 0}px)`,
      'bottom': `calc(96px + constant(safe-area-inset-bottom) + ${offset.y || 0}px)`,
      'bottom-env': `calc(96px + env(safe-area-inset-bottom) + ${offset.y || 0}px)`,
    },
    'top-right': {
      'right': `calc(5% + ${offset.x || 0}px)`,
      'top': `calc(60px + constant(safe-area-inset-top) + ${offset.y || 0}px)`,
      'top-env': `calc(60px + env(safe-area-inset-top) + ${offset.y || 0}px)`,
    },
    'top-left': {
      'left': `calc(5% + ${offset.x || 0}px)`,
      'top': `calc(60px + constant(safe-area-inset-top) + ${offset.y || 0}px)`,
      'top-env': `calc(60px + env(safe-area-inset-top) + ${offset.y || 0}px)`,
    },
    'center-right': {
      right: `calc(5% + ${offset.x || 0}px)`,
      top: `calc(50% + ${offset.y || 0}px)`,
      transform: 'translateY(-50%)',
    },
    'center-left': {
      left: `calc(5% + ${offset.x || 0}px)`,
      top: `calc(50% + ${offset.y || 0}px)`,
      transform: 'translateY(-50%)',
    },
  }

  return baseOffset[props.position]
})

// 计算尺寸样式
const sizeStyle = computed(() => {
  const sizes = {
    small: { width: '36px', height: '36px', fontSize: '14px' },
    medium: { width: '44px', height: '44px', fontSize: '18px' },
    large: { width: '56px', height: '56px', fontSize: '22px' },
  }

  return sizes[props.size]
})
</script>

<template>
  <view
    class="floating-action-button"
    :style="{
      ...positionStyle,
      ...sizeStyle,
      backgroundColor,
      zIndex,
    }"
    @tap="handleClick"
  >
    <text class="iconfont floating-icon" :class="icon" />
  </view>
</template>

<style scoped lang="scss">
.floating-action-button {
  position: fixed;
  border-radius: 50%;
  backdrop-filter: blur(5px);
  border: 2px solid rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow:
    0 8px 25px rgba(102, 126, 234, 0.4),
    0 4px 15px rgba(118, 75, 162, 0.3),
    0 2px 8px rgba(0, 0, 0, 0.15);
  cursor: pointer;
  transition: all 0.3s ease;
  animation: pulse 2s infinite;

  &:active {
    transform: scale(0.95);
    background: linear-gradient(135deg, #5a6fd8 0%, #694389 100%) !important;
    box-shadow:
      0 4px 15px rgba(102, 126, 234, 0.6),
      0 2px 8px rgba(118, 75, 162, 0.4),
      0 1px 4px rgba(0, 0, 0, 0.2);
  }
}

.floating-icon {
  color: #8b5cf6;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
  font-size: 22px;
}

// 针对不同位置的特殊处理
.floating-action-button[style*="bottom"] {
  bottom: v-bind('positionStyle["bottom-env"]');
}

.floating-action-button[style*="top"] {
  top: v-bind('positionStyle["top-env"]');
}

// 脉冲动画
@keyframes pulse {
  0% {
    box-shadow:
      0 8px 25px rgba(102, 126, 234, 0.4),
      0 4px 15px rgba(118, 75, 162, 0.3),
      0 2px 8px rgba(0, 0, 0, 0.15),
      0 0 0 0 rgba(102, 126, 234, 0.7);
  }
  50% {
    box-shadow:
      0 8px 25px rgba(102, 126, 234, 0.4),
      0 4px 15px rgba(118, 75, 162, 0.3),
      0 2px 8px rgba(0, 0, 0, 0.15),
      0 0 0 8px rgba(102, 126, 234, 0.2);
  }
  100% {
    box-shadow:
      0 8px 25px rgba(102, 126, 234, 0.4),
      0 4px 15px rgba(118, 75, 162, 0.3),
      0 2px 8px rgba(0, 0, 0, 0.15),
      0 0 0 0 rgba(102, 126, 234, 0);
  }
}
</style>
