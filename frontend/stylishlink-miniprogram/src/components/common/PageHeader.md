# PageHeader 通用页面头部组件

## 功能特性

- ✅ 自动适配安全区域
- ✅ 可配置的标题样式
- ✅ 右侧扩展插槽支持
- ✅ 点击动画效果
- ✅ 使用iconfont返回图标
- ✅ 标题居中显示，平衡布局设计
- ✅ 无背景返回按钮，简洁设计
- ✅ 透明背景设计，不遮挡页面内容
- ✅ 防重叠布局，页面滚动时保持清晰分离

## 布局说明

组件采用透明悬浮设计：
- **透明背景**：无背景色，与页面内容自然融合
- **固定定位**：悬浮在页面顶部，覆盖安全区域
- **内容层**：在安全区域内显示，三段式布局
  - **左侧区域**：返回按钮（左对齐）
  - **中间区域**：页面标题（居中显示）
  - **右侧区域**：扩展插槽内容（右对齐）

这种设计确保页面滚动时内容不会与标题重叠，同时保持简洁的视觉效果。

## Props 参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `title` | `string` | `''` | 页面标题 |
| `showBackButton` | `boolean` | `true` | 是否显示返回按钮 |
| `backButtonStyle` | `'default' \| 'dark' \| 'light'` | `'default'` | 返回按钮样式（已移除背景效果） |
| `titleStyle` | `'default' \| 'dark' \| 'light'` | `'default'` | 标题文字样式 |

## Events 事件

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| `back` | 点击返回按钮时触发 | - |

## Slots 插槽

| 插槽名 | 说明 |
|--------|------|
| `right` | 右侧扩展内容插槽 |

## 使用示例

### 基础用法
```vue
<template>
  <PageHeader 
    title="页面标题"
    @back="handleBack"
  />
</template>

<script setup>
import PageHeader from '@/components/common/PageHeader.vue'

function handleBack() {
  uni.navigateBack()
}
</script>
```

### 自定义样式
```vue
<template>
  <PageHeader 
    title="深色页面"
    back-button-style="light"
    title-style="light"
    @back="handleBack"
  />
</template>
```

### 带右侧操作
```vue
<template>
  <PageHeader 
    title="设置页面"
    @back="handleBack"
  >
    <template #right>
      <view class="header-action" @tap="handleSave">
        <text>保存</text>
      </view>
    </template>
  </PageHeader>
</template>
```

### 隐藏返回按钮
```vue
<template>
  <PageHeader 
    title="首页"
    :show-back-button="false"
  />
</template>
```

## 样式变量

| 变量名 | 说明 | 默认值 |
|--------|------|--------|
| `--header-height` | 头部高度 | `28px` |
| `--header-padding` | 左右内边距 | `5%` |
| `--back-button-size` | 返回按钮尺寸 | `32px` |

## 图标说明

返回按钮使用项目内置的iconfont图标：
- **图标类名**：`icon-fanhuiicon`
- **Unicode**：`\e6e3`
- **显示效果**：返回箭头图标

## 注意事项

1. **安全区域适配**：组件覆盖整个安全区域，内容在安全区域内显示
2. **透明设计**：无背景色，不遮挡页面内容
3. **防重叠布局**：页面滚动容器需要正确配置以避免与标题重叠
4. **Z-index**：组件使用 `z-index: 100`，确保在其他元素之上
5. **iconfont依赖**：组件依赖项目的iconfont字体文件，确保已正确引入
6. **返回按钮**：已移除背景色，backButtonStyle参数仅影响文字颜色
7. **页面布局配置**：使用该组件的页面需要正确配置滚动容器以防重叠：

```scss
// 滚动容器配置 - 避免与PageHeader重叠
// 注意：现在PageHeader使用动态高度，建议通过props或事件获取实际高度
.main-scroll-container {
  // 基础配置（作为fallback）
  height: calc(100vh - max(calc(28px + env(safe-area-inset-top)), 100px));
  // 推荐：通过PageHeader组件传递实际计算的高度
  margin-top: var(--page-header-height, max(calc(28px + env(safe-area-inset-top)), 100px));
}

// 内容区域 - 简化间距
.main-content {
  padding-top: 16px; // 常规间距即可
}
```

### 与页面布局配合使用

```vue
<!-- 页面中使用 -->
<template>
  <view class="page-container">
    <PageHeader 
      title="页面标题" 
      @header-height-change="handleHeaderHeightChange"
    />
    <scroll-view 
      class="main-scroll-container"
      :style="{ 
        height: `calc(100vh - ${headerHeight}px)`,
        marginTop: `${headerHeight}px`
      }"
    >
      <!-- 页面内容 -->
    </scroll-view>
  </view>
</template>

<script setup>
const headerHeight = ref(100) // 默认高度

const handleHeaderHeightChange = (height) => {
  headerHeight.value = height
}
``` 