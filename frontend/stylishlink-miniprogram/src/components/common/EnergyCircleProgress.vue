<!-- 可复用的能量圆形进度条组件 -->
<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  score?: number // 能量分数 0-100
  size?: number // 圆形大小，默认120px
  label?: string // 中心标签，默认"今日能量"
  showPercentage?: boolean // 是否显示百分比文字
  percentage?: number // 超过用户的百分比
}

const props = withDefaults(defineProps<Props>(), {
  score: 0,
  size: 120,
  label: '今日能量',
  showPercentage: true,
  percentage: 0,
})

// 项目主色渐变系统
const primaryGradientStart = '#667eea' // 浅蓝紫
const primaryGradientEnd = '#764ba2' // 深蓝紫

// 计算芝麻信用风格圆锥渐变进度条
const progressConicStyle = computed(() => {
  const score = Math.max(0, Math.min(100, props.score)) // 限制在0-100范围
  const progressDegree = (score / 100) * 270 // 270度最大进度

  // 芝麻信用风格：从底部左侧开始，顺时针填充270度
  const conicGradient = `conic-gradient(
    from 135deg,
    ${primaryGradientStart} 0deg,
    ${primaryGradientEnd} ${progressDegree}deg,
    rgba(255, 255, 255, 0.3) ${progressDegree}deg,
    rgba(255, 255, 255, 0.3) 270deg,
    transparent 270deg
  )`

  return {
    background: conicGradient,
    transform: 'rotate(90deg)', // 调整起始位置到底部左侧
  }
})

// 计算光晕效果 - 使用主色系统
const glowStyle = computed(() => {
  const score = Math.max(0, Math.min(100, props.score))
  const intensity = Math.min(score / 100, 1) // 根据分数调整光晕强度

  return {
    boxShadow: `
      0 0 ${20 * intensity}px ${primaryGradientStart}40,
      0 0 ${40 * intensity}px ${primaryGradientEnd}20
    `,
    opacity: 0.6 + 0.4 * intensity, // 动态透明度
  }
})

// 计算容器尺寸
const containerStyle = computed(() => ({
  width: `${props.size}px`,
  height: `${props.size}px`,
  maxWidth: `${props.size}px`,
  maxHeight: `${props.size}px`,
}))

// 计算中心圆的尺寸（约为容器的一半）
const centerSize = computed(() => Math.round(props.size * 0.5))
</script>

<template>
  <view class="energy-circle-container">
    <view class="energy-circle-wrapper" :style="containerStyle">
      <!-- 背景圆环（270度轨道） -->
      <view class="progress-track" />

      <!-- 进度圆锥渐变 -->
      <view class="progress-conic" :style="progressConicStyle" />

      <!-- 中心内容 -->
      <view class="energy-center" :style="{ width: `${centerSize}px`, height: `${centerSize}px` }">
        <text class="energy-label">
          {{ label }}
        </text>
        <text class="energy-score">
          {{ score }}
        </text>
      </view>

      <!-- 光晕效果 -->
      <view class="energy-glow" :style="glowStyle" />
    </view>

    <!-- 百分比说明 -->
    <view v-if="showPercentage" class="energy-percentage">
      <text class="percentage-text">
        超过{{ percentage }}%用户
      </text>
    </view>
  </view>
</template>

<style scoped lang="scss">
.energy-circle-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.energy-circle-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

// 芝麻信用风格圆锥渐变进度条样式
.progress-track {
  position: absolute;
  top: 4px;
  left: 4px;
  right: 4px;
  bottom: 4px;
  border-radius: 50%;
  background: conic-gradient(
    from 135deg,
    rgba(255, 255, 255, 0.3) 0deg,
    rgba(255, 255, 255, 0.3) 270deg,
    transparent 270deg
  );
  transform: rotate(90deg);

  // 同样的内圆遮罩
  mask: radial-gradient(
    circle at center,
    transparent 28px,
    black 32px
  );
  -webkit-mask: radial-gradient(
    circle at center,
    transparent 28px,
    black 32px
  );
}

.progress-conic {
  position: absolute;
  top: 4px;
  left: 4px;
  right: 4px;
  bottom: 4px;
  border-radius: 50%;
  transition: all 1.5s cubic-bezier(0.25, 0.8, 0.25, 1);

  // 创建内圆遮罩，形成圆环效果
  mask: radial-gradient(
    circle at center,
    transparent 28px,
    black 32px
  );
  -webkit-mask: radial-gradient(
    circle at center,
    transparent 28px,
    black 32px
  );
}

.energy-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 90%;
  height: 90%;
  border-radius: 50%;
  opacity: 0.6;
  filter: blur(8px);
  z-index: 1;
  transition: all 1.5s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.energy-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 10;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.energy-label {
  font-size: 11px;
  font-weight: 500;
  color: var(--color-text-secondary, #6b7280);
  line-height: 1;
}

.energy-score {
  font-size: 22px;
  font-weight: 700;
  color: var(--color-text-primary, #1f2937);
  line-height: 1;
  margin-top: 2px;
}

.energy-percentage {
  text-align: center;
  margin-top: -8px;
}

.percentage-text {
  font-size: 11px;
  color: var(--color-text-secondary, #6b7280);
  opacity: 0.8;
  font-weight: 500;
}
</style>
