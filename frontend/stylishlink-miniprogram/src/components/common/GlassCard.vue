<!-- 毛玻璃卡片组件 -->
<script setup lang="ts">
import { computed } from 'vue'

// Props 定义
interface Props {
  blur?: number // 模糊程度
  opacity?: number // 背景透明度 (0-1)
  borderRadius?: number // 圆角大小 (px)
  padding?: number // 内边距 (px)
  border?: boolean // 是否显示边框
  shadow?: boolean // 是否显示阴影
  className?: string // 自定义样式类
}

const props = withDefaults(defineProps<Props>(), {
  blur: 10,
  opacity: 0.25,
  borderRadius: 12,
  padding: 16,
  border: false,
  shadow: true,
  className: '',
})

// 计算样式
const cardStyle = computed(() => {
  return {
    background: `rgba(255, 255, 255, ${props.opacity})`,
    backdropFilter: `blur(${props.blur}px)`,
    borderRadius: `${props.borderRadius}px`,
    padding: `${props.padding}px`,
    border: props.border ? '1px solid rgba(255, 255, 255, 0.3)' : 'none',
    boxShadow: props.shadow ? '0 8px 32px rgba(0, 0, 0, 0.05)' : 'none',
  }
})
</script>

<template>
  <view
    class="glass-card"
    :class="className"
    :style="cardStyle"
  >
    <slot />
  </view>
</template>

<style scoped lang="scss">
.glass-card {
  position: relative;
  box-sizing: border-box;
  transition: all 0.3s ease;

  // 悬停效果（适用于支持的平台）
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.08) !important;
  }

  // 激活效果
  &:active {
    transform: translateY(0);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1) !important;
  }
}

// 预设样式变体
.glass-card.variant-primary {
  background: linear-gradient(
    135deg,
    rgba(102, 126, 234, 0.25) 0%,
    rgba(118, 75, 162, 0.25) 100%
  ) !important;
}

.glass-card.variant-success {
  background: linear-gradient(
    135deg,
    rgba(34, 197, 94, 0.25) 0%,
    rgba(22, 163, 74, 0.25) 100%
  ) !important;
}

.glass-card.variant-warning {
  background: linear-gradient(
    135deg,
    rgba(245, 158, 11, 0.25) 0%,
    rgba(217, 119, 6, 0.25) 100%
  ) !important;
}

.glass-card.variant-danger {
  background: linear-gradient(
    135deg,
    rgba(239, 68, 68, 0.25) 0%,
    rgba(220, 38, 38, 0.25) 100%
  ) !important;
}
</style>
