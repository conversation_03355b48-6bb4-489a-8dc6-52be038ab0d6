<!-- 环境切换组件 - 测试阶段使用 -->
<script setup lang="ts">
import type { Environment } from '@/config/env'
import { computed, onMounted } from 'vue'
import { useEnvStore } from '@/store/env'

// 使用环境store
const envStore = useEnvStore()

// 当前环境信息
const currentEnvironmentInfo = computed(() => envStore.getEnvironmentInfo())

// 可切换的环境列表
const testableEnvironments = computed(() => envStore.getTestableEnvironments())

// 环境切换处理
function handleEnvironmentSwitch(environment: Environment) {
  console.warn('EnvironmentSwitcher - 点击环境切换按钮:', environment)

  try {
    // 显示点击反馈
    uni.showToast({
      title: '正在切换环境...',
      icon: 'loading',
      duration: 500,
    })

    // 短暂延迟后执行切换，确保toast显示
    setTimeout(() => {
      envStore.switchEnvironment(environment)
    }, 100)
  }
  catch (error) {
    console.error('EnvironmentSwitcher - 环境切换失败:', error)
    uni.showToast({
      title: '切换失败，请重试',
      icon: 'error',
      duration: 2000,
    })
  }
}

// 重置到默认环境
function handleReset() {
  console.warn('EnvironmentSwitcher - 点击重置按钮')

  try {
    uni.showModal({
      title: '确认重置',
      content: '确定要重置为开发环境吗？',
      success(res) {
        if (res.confirm) {
          console.warn('EnvironmentSwitcher - 确认重置环境')
          envStore.resetToDefault()
        }
      },
    })
  }
  catch (error) {
    console.error('EnvironmentSwitcher - 重置失败:', error)
    envStore.resetToDefault()
  }
}

// 复制URL到剪贴板
function copyApiUrl() {
  console.warn('EnvironmentSwitcher - 点击复制API地址')

  try {
    // 在小程序中，使用系统API复制文本
    uni.setClipboardData({
      data: currentEnvironmentInfo.value.apiBaseUrl,
      success: () => {
        console.warn('EnvironmentSwitcher - API地址复制成功')
        uni.showToast({
          title: 'API地址已复制',
          icon: 'success',
        })
      },
      fail: (error) => {
        console.error('EnvironmentSwitcher - API地址复制失败:', error)
        uni.showToast({
          title: '复制失败，请重试',
          icon: 'error',
        })
      },
    })
  }
  catch (error) {
    console.error('EnvironmentSwitcher - 复制操作异常:', error)
    uni.showToast({
      title: '复制功能异常',
      icon: 'error',
    })
  }
}

// 获取环境状态样式
function getEnvironmentStatusClass(env: Environment) {
  return {
    'env-btn': true,
    'env-btn-active': currentEnvironmentInfo.value.current === env,
    'env-btn-development': env === 'development',
    'env-btn-test': env === 'test',
  }
}

// 获取环境图标
function getEnvironmentIcon(env: Environment) {
  switch (env) {
    case 'development':
      return 'icon-code'
    case 'test':
      return 'icon-test'
    case 'production':
      return 'icon-global'
    default:
      return 'icon-settings'
  }
}

// 调试功能：检查环境状态
function handleDebugCheck() {
  console.warn('EnvironmentSwitcher - 执行调试检查')
  const status = envStore.checkEnvironmentStatus()

  uni.showModal({
    title: '环境状态调试',
    content: `Store环境: ${status?.storeEnv}\n存储环境: ${status?.storageEnv}\nAPI地址: ${status?.config?.apiBaseUrl}`,
    showCancel: false,
  })
}

// 组件挂载时的初始化
onMounted(() => {
  console.warn('EnvironmentSwitcher - 组件挂载，当前环境:', currentEnvironmentInfo.value)
})
</script>

<template>
  <view class="environment-switcher">
    <!-- 当前环境状态 -->
    <view class="current-env-status">
      <view class="status-header">
        <text class="status-title">
          当前环境
        </text>
        <view
          :class="`status-indicator status-${currentEnvironmentInfo.current}`"
        />
      </view>

      <view class="env-info">
        <text class="env-name">
          {{ currentEnvironmentInfo.name }}
        </text>
        <view class="env-details">
          <view class="env-detail-item" @tap="copyApiUrl">
            <text class="detail-label">
              API地址:
            </text>
            <text class="detail-value">
              {{ currentEnvironmentInfo.apiBaseUrl }}
            </text>
            <text class="iconfont icon-copy copy-icon" />
          </view>
        </view>
      </view>
    </view>

    <!-- 环境切换按钮 -->
    <view class="env-switch-section">
      <text class="section-subtitle">
        环境切换
      </text>
      <view class="env-buttons">
        <view
          v-for="envItem in testableEnvironments"
          :key="envItem.key"
          :class="getEnvironmentStatusClass(envItem.key)"
          @tap="handleEnvironmentSwitch(envItem.key)"
        >
          <text :class="`iconfont ${getEnvironmentIcon(envItem.key)}`" />
          <text class="env-btn-text">
            {{ envItem.config.name }}
          </text>
          <text v-if="currentEnvironmentInfo.current === envItem.key" class="env-current-mark">
            当前
          </text>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="env-actions">
      <view class="action-btn reset-btn" @tap="handleReset">
        <text class="iconfont icon-refresh" />
        <text class="action-text">
          重置为开发环境
        </text>
      </view>

      <!-- 调试按钮 -->
      <view class="action-btn debug-btn" @tap="handleDebugCheck">
        <text class="iconfont icon-bug" />
        <text class="action-text">
          调试检查
        </text>
      </view>
    </view>

    <!-- 环境说明 -->
    <view class="env-description">
      <text class="desc-title">
        环境说明
      </text>
      <view class="desc-content">
        <text class="desc-item">
          • 开发环境：用于本地开发和调试
        </text>
        <text class="desc-item">
          • 测试环境：用于功能测试和验证
        </text>
        <text class="desc-item">
          • 环境切换后立即生效，影响所有API请求
        </text>
        <text class="desc-item">
          • 环境设置会持久化保存，重启应用后保持
        </text>
        <text class="desc-item">
          • 如切换无效请点击"调试检查"查看状态
        </text>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.environment-switcher {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* 当前环境状态 */
.current-env-status {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.3) 0%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0.3) 100%
  );
  border-radius: 12px;
  padding: 16px;
  border: 1px solid rgba(255, 255, 255, 0.4);
}

.status-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.status-title {
  font-size: 14px;
  font-weight: 600;
  color: white;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;

  &.status-development {
    background: #10b981; // 绿色 - 开发环境
  }

  &.status-test {
    background: #f59e0b; // 橙色 - 测试环境
  }

  &.status-production {
    background: #ef4444; // 红色 - 生产环境
  }
}

.env-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.env-name {
  font-size: 16px;
  font-weight: 600;
  color: white;
}

.env-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.env-detail-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 0;
  position: relative;
}

.detail-label {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  min-width: 60px;
}

.detail-value {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.9);
  flex: 1;
  font-family: monospace;
}

.copy-icon {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;

  &:active {
    background: rgba(255, 255, 255, 0.1);
  }
}

/* 环境切换区域 */
.env-switch-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.section-subtitle {
  font-size: 14px;
  font-weight: 600;
  color: white;
}

.env-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.env-btn {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  background: rgba(255, 255, 255, 0.1);
  transition: all 0.2s ease;
  position: relative;

  &:active {
    transform: scale(0.98);
  }

  &.env-btn-active {
    border-color: rgba(255, 255, 255, 0.6);
    background: rgba(255, 255, 255, 0.25);

    .env-current-mark {
      position: absolute;
      right: 12px;
      top: 50%;
      transform: translateY(-50%);
      font-size: 10px;
      color: #10b981;
      background: rgba(16, 185, 129, 0.2);
      padding: 2px 6px;
      border-radius: 4px;
      font-weight: 500;
    }
  }

  &.env-btn-development.env-btn-active {
    border-color: rgba(16, 185, 129, 0.6);
    background: rgba(16, 185, 129, 0.15);
  }

  &.env-btn-test.env-btn-active {
    border-color: rgba(245, 158, 11, 0.6);
    background: rgba(245, 158, 11, 0.15);
  }

  .iconfont {
    font-size: 16px;
    color: white;
  }

  .env-btn-text {
    font-size: 14px;
    color: white;
    font-weight: 500;
    flex: 1;
  }
}

/* 操作按钮 */
.env-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.15);
  transition: all 0.2s ease;
  flex: 1;
  min-width: 120px;

  &:active {
    transform: scale(0.98);
    background: rgba(255, 255, 255, 0.25);
  }

  .iconfont {
    font-size: 14px;
    color: white;
  }

  .action-text {
    font-size: 12px;
    color: white;
  }
}

.reset-btn {
  border-color: rgba(239, 68, 68, 0.4);
  background: rgba(239, 68, 68, 0.1);

  &:active {
    background: rgba(239, 68, 68, 0.2);
  }
}

.debug-btn {
  border-color: rgba(59, 130, 246, 0.4);
  background: rgba(59, 130, 246, 0.1);

  &:active {
    background: rgba(59, 130, 246, 0.2);
  }
}

/* 环境说明 */
.env-description {
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.08);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.desc-title {
  font-size: 12px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 8px;
  display: block;
}

.desc-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.desc-item {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.4;
}
</style>
