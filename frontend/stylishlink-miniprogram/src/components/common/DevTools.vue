<!-- 开发工具组件 - 优化版 -->
<script setup lang="ts">
import { computed, ref } from 'vue'

// 位置类型
export type DevToolsPosition = 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right'
export type DevToolsSize = 'small' | 'medium' | 'large'

// Props 定义
interface Props {
  visible?: boolean // 是否显示
  position?: DevToolsPosition // 位置
  size?: DevToolsSize // 尺寸
  icon?: string // 图标
  zIndex?: number // 层级
  opacity?: number // 透明度 (0-1)
  enableHide?: boolean // 是否支持双击隐藏
}

const props = withDefaults(defineProps<Props>(), {
  visible: true,
  position: 'top-left',
  size: 'medium',
  icon: 'icon-shezhi',
  zIndex: 1000,
  opacity: 0.6,
  enableHide: true,
})

// Events 定义
const emit = defineEmits<{
  click: []
  toggle: [visible: boolean]
}>()

// 内部状态
const isVisible = ref(props.visible)
const lastTapTime = ref(0)

// 处理点击事件（支持双击隐藏）
function handleClick() {
  const now = Date.now()
  const timeDiff = now - lastTapTime.value

  if (props.enableHide && timeDiff < 600) {
    // 双击隐藏/显示
    isVisible.value = !isVisible.value
    emit('toggle', isVisible.value)
  }
  else {
    // 单击事件
    emit('click')
  }

  lastTapTime.value = now
}

// 计算尺寸
const sizeConfig = computed(() => {
  const sizes = {
    small: { width: 32, height: 32, fontSize: 14 },
    medium: { width: 40, height: 40, fontSize: 16 },
    large: { width: 48, height: 48, fontSize: 20 },
  }
  return sizes[props.size]
})

// 计算位置样式
const positionStyle = computed(() => {
  const positions = {
    'top-left': {
      'top': 'calc(60px + constant(safe-area-inset-top))',
      'top-env': 'calc(60px + env(safe-area-inset-top))',
      'left': '16px',
    },
    'top-right': {
      'top': 'calc(60px + constant(safe-area-inset-top))',
      'top-env': 'calc(60px + env(safe-area-inset-top))',
      'right': '16px',
    },
    'bottom-left': {
      'bottom': 'calc(20px + constant(safe-area-inset-bottom))',
      'bottom-env': 'calc(20px + env(safe-area-inset-bottom))',
      'left': '16px',
    },
    'bottom-right': {
      'bottom': 'calc(20px + constant(safe-area-inset-bottom))',
      'bottom-env': 'calc(20px + env(safe-area-inset-bottom))',
      'right': '16px',
    },
  }

  return positions[props.position]
})

// 最终显示状态
const shouldShow = computed(() => {
  return props.visible && isVisible.value
})
</script>

<template>
  <view
    v-if="shouldShow"
    class="dev-tools"
    :class="[`size-${size}`, `position-${position}`]"
    :style="{
      ...positionStyle,
      zIndex,
      width: `${sizeConfig.width}px`,
      height: `${sizeConfig.height}px`,
      opacity,
    }"
    @tap="handleClick"
  >
    <text class="iconfont dev-icon" :class="icon" :style="{ fontSize: `${sizeConfig.fontSize}px` }" />

    <!-- 提示文字 -->
    <view v-if="enableHide" class="dev-hint">
      <text class="hint-text">
        双击隐藏
      </text>
    </view>
  </view>
</template>

<style scoped lang="scss">
.dev-tools {
  position: fixed;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  cursor: pointer;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.95);
    background: rgba(255, 255, 255, 0.25);
  }

  &:hover {
    opacity: 0.9 !important;
    background: rgba(255, 255, 255, 0.2);

    .dev-hint {
      opacity: 1;
      visibility: visible;
    }
  }

  // 根据尺寸调整发光效果
  &.size-small::before {
    top: -1px;
    left: -1px;
    right: -1px;
    bottom: -1px;
  }

  &.size-medium::before {
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
  }

  &.size-large::before {
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
  }

  // 开发环境提示动画（更柔和）
  &::before {
    content: '';
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(45deg,
      rgba(102, 126, 234, 0.2),
      rgba(118, 75, 162, 0.2),
      rgba(102, 126, 234, 0.2)
    );
    animation: devToolsGlow 3s ease-in-out infinite;
    z-index: -1;
  }
}

.dev-icon {
  color: var(--color-text-primary, #1f2937);
  opacity: 0.8;
}

// 提示文字
.dev-hint {
  position: absolute;
  bottom: -30px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 10px;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  z-index: 10;

  &::before {
    content: '';
    position: absolute;
    top: -4px;
    left: 50%;
    transform: translateX(-50%);
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-bottom: 4px solid rgba(0, 0, 0, 0.8);
  }
}

.hint-text {
  font-size: 10px;
  color: white;
}

// 针对不同位置的特殊处理
.dev-tools.position-top-left,
.dev-tools.position-top-right {
  top: calc(var(--safe-area-top, 50px) + 16px);
  // 兼容旧版本CSS变量
  top: calc(var(--status-bar-height, 50px) + 16px);
}

.dev-tools.position-bottom-left,
.dev-tools.position-bottom-right {
  bottom: calc(16px + constant(safe-area-inset-bottom, 20px));
  bottom: calc(16px + env(safe-area-inset-bottom, 20px));
}

@keyframes devToolsGlow {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.02);
  }
}

// 生产环境隐藏动画
.dev-tools.production {
  &::before {
    display: none;
  }
}
</style>
