<!-- 星级评分组件 -->
<script setup lang="ts">
import { computed } from 'vue'

// 尺寸类型
export type StarSize = 'small' | 'medium' | 'large'

// Props 定义
interface Props {
  rating?: number // 评分值，0-5 - 改为可选
  size?: StarSize // 星星大小
  color?: string // 星星颜色
  emptyColor?: string // 空星颜色
  readonly?: boolean // 是否只读
  showScore?: boolean // 是否显示分数
  maxStars?: number // 最大星数
}

const props = withDefaults(defineProps<Props>(), {
  rating: 0,
  size: 'medium',
  color: '#ffc25c',
  emptyColor: '#e5e7eb',
  readonly: true,
  showScore: false,
  maxStars: 5,
})

// Events 定义
const emit = defineEmits<{
  change: [rating: number]
}>()

// 计算星级显示
const starsData = computed(() => {
  const fullStars = Math.floor(props.rating)
  const hasHalfStar = props.rating % 1 >= 0.5
  const emptyStars = props.maxStars - fullStars - (hasHalfStar ? 1 : 0)

  return {
    full: fullStars,
    half: hasHalfStar,
    empty: emptyStars,
  }
})

// 计算星星尺寸
const starSize = computed(() => {
  const sizes = {
    small: '12px',
    medium: '16px',
    large: '20px',
  }
  return sizes[props.size]
})

// 计算分数字体大小
const scoreSize = computed(() => {
  const sizes = {
    small: '10px',
    medium: '12px',
    large: '14px',
  }
  return sizes[props.size]
})

// 处理星星点击
function handleStarClick(starIndex: number) {
  if (!props.readonly) {
    const newRating = starIndex + 1
    emit('change', newRating)
  }
}

// 格式化评分显示
const formattedRating = computed(() => {
  return props.rating % 1 === 0 ? props.rating.toString() : props.rating.toFixed(1)
})
</script>

<template>
  <view class="star-rating">
    <view class="stars-container">
      <!-- 满星 -->
      <text
        v-for="i in starsData.full"
        :key="`full-${i}`"
        class="star star-full"
        :style="{
          fontSize: starSize,
          color,
        }"
        @tap="handleStarClick(i - 1)"
      >
        ★
      </text>

      <!-- 半星 -->
      <text
        v-if="starsData.half"
        class="star star-half"
        :style="{
          fontSize: starSize,
        }"
        @tap="handleStarClick(starsData.full)"
      >
        <text class="star-half-filled" :style="{ color }">
          ★
        </text>
        <text class="star-half-empty" :style="{ color: emptyColor }">
          ☆
        </text>
      </text>

      <!-- 空星 -->
      <text
        v-for="i in starsData.empty"
        :key="`empty-${i}`"
        class="star star-empty"
        :style="{
          fontSize: starSize,
          color: emptyColor,
        }"
        @tap="handleStarClick(starsData.full + (starsData.half ? 1 : 0) + i - 1)"
      >
        ☆
      </text>
    </view>

    <!-- 评分数字 -->
    <text
      v-if="showScore"
      class="rating-score"
      :style="{ fontSize: scoreSize }"
    >
      {{ formattedRating }}
    </text>
  </view>
</template>

<style scoped lang="scss">
.star-rating {
  display: flex;
  align-items: center;
  gap: 4px;
}

.stars-container {
  display: flex;
  align-items: center;
}

.star {
  transition: all 0.2s ease;
  cursor: pointer;
  line-height: 1;

  &:not(.star-half) {
    &:hover {
      transform: scale(1.1);
    }

    &:active {
      transform: scale(0.95);
    }
  }
}

.star-half {
  position: relative;
  display: inline-block;
}

.star-half-filled {
  position: absolute;
  top: 0;
  left: 0;
  width: 50%;
  overflow: hidden;
  z-index: 2;
}

.star-half-empty {
  position: relative;
  z-index: 1;
}

.rating-score {
  font-weight: 500;
  color: var(--color-text-primary, #1f2937);
  margin-left: 2px;
}

// 只读模式下禁用交互
.star-rating.readonly .star {
  cursor: default;

  &:hover {
    transform: none;
  }
}

// 不同尺寸的间距调整
.star-rating.size-small .stars-container {
  gap: 1px;
}

.star-rating.size-medium .stars-container {
  gap: 2px;
}

.star-rating.size-large .stars-container {
  gap: 3px;
}
</style>
