<!-- 分类标签组件 -->
<script setup lang="ts">
import type { CategoryTab } from '@/types/business'

// Props 定义
interface Props {
  categories?: CategoryTab[]
  activeKey?: string
}

const props = withDefaults(defineProps<Props>(), {
  categories: () => [],
  activeKey: '',
})

// Events 定义
const emit = defineEmits<{
  'change': [activeKey: string, category: CategoryTab]
  'tab-click': [category: CategoryTab]
}>()

// 处理标签点击
function handleTabClick(category: CategoryTab) {
  emit('tab-click', category)
  emit('change', category.id, category)
}

// 判断是否为激活状态
function isActive(category: CategoryTab) {
  return props.activeKey ? category.id === props.activeKey : category.active
}
</script>

<template>
  <view class="category-tabs-container">
    <view class="category-tabs">
      <view
        v-for="category in props.categories"
        :key="category.id"
        class="category-tab"
        :class="{ active: isActive(category) }"
        @tap="handleTabClick(category)"
      >
        <text class="iconfont tab-icon" :class="category.icon" />
        <text class="tab-text">
          {{ category.name }}
        </text>
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
// 分类标签栏容器 - 完全延伸到屏幕边缘
.category-tabs-container {
  margin: 24px 0 0 0; // 增加上边距，从8px改为16px，加大与今日推荐卡片的间距
  width: 100%;
  max-width: 100vw;
}

// 分类标签栏 - 无左右边距
.category-tabs {
  display: flex;
  overflow-x: auto;
  overflow-y: hidden;
  padding: 4px 0; // 只保留上下内边距
  white-space: nowrap;
  width: 100%;
  max-width: 100vw;
  box-sizing: border-box;
  -webkit-overflow-scrolling: touch; // iOS平滑滚动

  /* 隐藏滚动条 - 微信小程序兼容 */
  &::-webkit-scrollbar {
    display: none !important;
    width: 0 !important;
    height: 0 !important;
    background: transparent !important;
    -webkit-appearance: none !important;
  }

  &::-webkit-scrollbar-track {
    display: none !important;
    background: transparent !important;
  }

  &::-webkit-scrollbar-thumb {
    display: none !important;
    background: transparent !important;
  }

  scrollbar-width: none !important; // Firefox
  -ms-overflow-style: none !important; // IE
}

.category-tab {
  display: flex;
  align-items: center;
  height: 28px;
  padding: 0 16px;
  border-radius: 50px;
  background: rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(4px);
  margin-right: 8px;
  flex-shrink: 0;
  cursor: pointer;
  transition: all 0.3s ease;

  &.active {
    background: rgba(255, 255, 255, 0.5);

    .tab-icon {
      color: #8b5cf6; // 使用真正的项目主色（紫色）
    }

    .tab-text {
      color: #8b5cf6; // 使用真正的项目主色（紫色）
      font-weight: 600; // 增加字重
    }
  }

  &:first-child {
    margin-left: 5%; // 第一个tab添加左边距
  }

  &:last-child {
    margin-right: 5%; // 最后一个tab添加右边距，确保能滑动到视野范围内
  }
}

.tab-icon {
  font-size: 14px;
  margin-right: 6px;
  color: var(--color-text-secondary, #4b5563); // 默认状态颜色
  transition: color 0.3s ease; // 添加颜色过渡动画
}

.tab-text {
  font-size: 12px;
  color: var(--color-text-secondary, #4b5563);
  white-space: nowrap;
  transition: all 0.3s ease; // 添加过渡动画
}
</style>
