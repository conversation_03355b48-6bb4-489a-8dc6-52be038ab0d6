<!-- 通用页面头部组件 -->
<script setup lang="ts">
import { useMenuButton } from '@/composables/useMenuButton'

// Props 定义
interface Props {
  title?: string
  showBackButton?: boolean
  backButtonStyle?: 'default' | 'dark' | 'light'
  titleStyle?: 'default' | 'dark' | 'light'
}

const props = withDefaults(defineProps<Props>(), {
  title: '',
  showBackButton: true,
  backButtonStyle: 'default',
  titleStyle: 'default',
})

// Emits 定义
const emit = defineEmits<{
  back: []
}>()

// 使用胶囊按钮适配
const { headerHeight, contentTop } = useMenuButton()

// 处理返回按钮点击
function handleBack() {
  if (props.showBackButton) {
    emit('back')
  }
}
</script>

<template>
  <view class="page-header" :style="{ height: headerHeight }">
    <!-- 内容层：在安全区域内显示 -->
    <view class="header-content" :style="{ top: contentTop }">
      <view class="header-left-content">
        <!-- 返回按钮 -->
        <view
          v-if="showBackButton"
          class="header-left"
          :class="`back-style-${backButtonStyle}`"
          @tap="handleBack"
        >
          <text class="iconfont icon-fanhuiicon back-icon" />
        </view>
      </view>

      <!-- 页面标题 - 居中显示 -->
      <text
        v-if="title"
        class="page-title"
        :class="`title-style-${titleStyle}`"
      >
        {{ title }}
      </text>

      <!-- 右侧扩展插槽 -->
      <view class="header-right-content">
        <slot name="right" />
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
.page-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  // 包含整个安全区域的高度 - 进一步增加iOS真机适配
  height: max(calc(28px + constant(safe-area-inset-top)), 100px);
  height: max(calc(28px + env(safe-area-inset-top)), 100px);
}

// 内容层：在安全区域内显示
.header-content {
  position: absolute;
  top: max(calc(constant(safe-area-inset-top) + 12px), 56px);
  top: max(calc(env(safe-area-inset-top) + 12px), 56px);
  left: 0;
  right: 0;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 5%;
}

.header-left-content {
  display: flex;
  align-items: center;
  flex: 1;
  justify-content: flex-start;
}

.header-right-content {
  display: flex;
  align-items: center;
  flex: 1;
  justify-content: flex-end;
}

.header-left {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;

  &:active {
    transform: scale(0.95);
  }
}

.back-icon {
  font-size: 18px;
  font-weight: bold;
  color: #1e293b;
}

.page-title {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  font-size: 16px;
  font-weight: 600;
  flex-shrink: 0;

  // 默认样式
  &.title-style-default {
    color: #1e293b;
  }

  // 深色文字样式
  &.title-style-dark {
    color: #1e293b;
  }

  // 亮色文字样式
  &.title-style-light {
    color: #ffffff;
  }
}
</style>
