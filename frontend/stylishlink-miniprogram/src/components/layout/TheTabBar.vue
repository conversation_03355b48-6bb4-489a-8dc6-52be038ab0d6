<script setup lang="ts">
import { onMounted, onUnmounted } from 'vue'
import { useTabBarStore } from '@/store/tabbar'

// 使用全局 TabBar 状态
const tabBarStore = useTabBarStore()

// 定期同步状态
let syncInterval: number | null = null

onMounted(() => {
  console.warn('TabBar: 组件挂载完成')

  try {
    // 初始化当前页面路径
    tabBarStore.syncCurrentPath()

    // 定期同步（降低频率）
    syncInterval = setInterval(() => {
      tabBarStore.syncCurrentPath()
    }, 1000) // 1秒检查一次

    console.warn('TabBar: 初始化完成')
  }
  catch (error) {
    console.error('TabBar: 初始化失败', error)
  }
})

onUnmounted(() => {
  console.warn('TabBar: 组件卸载')
  if (syncInterval) {
    clearInterval(syncInterval)
    syncInterval = null
  }
})

// 切换 Tab - 由全局路由守卫处理登录检查
function handleTabSwitch(path: string) {
  try {
    // 直接切换Tab，登录检查由全局路由守卫处理
    tabBarStore.switchTab(path)
  }
  catch (error) {
    console.error('TabBar: 切换Tab失败', error)
  }
}
</script>

<template>
  <view class="safe-area-inset-bottom bottom-nav">
    <view
      v-for="tab in tabBarStore.tabs"
      :key="tab.path"
      class="tab-item"
      :class="{ active: tabBarStore.currentPath === tab.path }"
      @tap="handleTabSwitch(tab.path)"
    >
      <!-- 选中背景指示器 -->
      <view v-if="tabBarStore.currentPath === tab.path" class="active-indicator" />

      <!-- 点击波纹效果 -->
      <view class="ripple-effect" />

      <view class="tab-icon">
        <text class="iconfont" :class="[tabBarStore.currentPath === tab.path ? tab.activeIcon : tab.icon]" />
      </view>
      <text class="tab-text">
        {{ tab.text }}
      </text>
    </view>
  </view>
</template>

<style scoped>
.bottom-nav {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  height: var(--bottom-nav-height);

  /* 提升背景不透明度，减少透明蒙版效果 */
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.98) 0%,
    rgba(255, 255, 255, 0.96) 25%,
    rgba(255, 255, 255, 0.94) 50%,
    rgba(255, 255, 255, 0.96) 75%,
    rgba(255, 255, 255, 0.98) 100%
  );

  /* 多重阴影营造层次感和毛玻璃效果 */
  box-shadow:
    0 -15px 45px rgba(0, 0, 0, 0.12),
    0 -8px 25px rgba(0, 0, 0, 0.08),
    0 -4px 12px rgba(0, 0, 0, 0.05),
    0 -1px 4px rgba(0, 0, 0, 0.02),
    inset 0 2px 0 rgba(255, 255, 255, 0.9),
    inset 0 -1px 0 rgba(255, 255, 255, 0.4),
    inset 1px 0 0 rgba(255, 255, 255, 0.3),
    inset -1px 0 0 rgba(255, 255, 255, 0.3);

  /* 简化边框设计，移除透明缝隙 */
  border: none;
  border-top: 1px solid rgba(255, 255, 255, 0.9);

  z-index: 100;
  display: flex;
  justify-content: space-around;
  align-items: center;
  border-radius: 20px 20px 0 0;
  padding: 8px 0;

  /* 过渡动画 */
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* 移除透明高光效果，避免产生透明蒙版
.bottom-nav::before,
.bottom-nav::after {
  display: none;
} */

.tab-item {
  flex: 1;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  padding: 6px 4px;
  border-radius: 12px;
  overflow: hidden;

  /* 优化过渡动画 */
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* 选中背景指示器 */
.active-indicator {
  position: absolute;
  top: 4px;
  left: 50%;
  transform: translateX(-50%);
  width: 32px;
  height: 32px;
  background: linear-gradient(
    135deg,
    var(--color-primary, #8b5cf6) 0%,
    rgba(139, 92, 246, 0.8) 100%
  );
  border-radius: 50%;
  opacity: 0.15;
  animation: indicator-appear 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

@keyframes indicator-appear {
  0% {
    transform: translateX(-50%) scale(0);
    opacity: 0;
  }
  50% {
    transform: translateX(-50%) scale(1.2);
    opacity: 0.2;
  }
  100% {
    transform: translateX(-50%) scale(1);
    opacity: 0.15;
  }
}

/* 波纹点击效果 */
.ripple-effect {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(
    circle,
    rgba(139, 92, 246, 0.3) 0%,
    rgba(139, 92, 246, 0.1) 50%,
    transparent 100%
  );
  border-radius: 50%;
  transform: translate(-50%, -50%);
  pointer-events: none;
  transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.tab-item:active .ripple-effect {
  width: 100px;
  height: 100px;
  opacity: 1;
}

.tab-icon {
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 4px;
  border-radius: 50%;
  background: transparent;
  z-index: 2;

  /* 优化图标过渡 */
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.tab-text {
  font-size: 10px;
  color: rgba(75, 85, 99, 0.4) !important;
  font-weight: 400;
  z-index: 2;

  /* 优化文字过渡 */
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* 选中状态样式优化 */
.tab-item.active .tab-text {
  color: var(--color-primary, #8b5cf6) !important;
  font-weight: 600;
  transform: translateY(-1px);
}

.tab-item.active .tab-icon {
  transform: translateY(-1px) scale(1.1);
}

/* 图标样式优化 */
.bottom-nav .tab-item .tab-icon .iconfont {
  font-size: 20px !important;
  color: rgba(75, 85, 99, 0.3) !important;
  font-family: "iconfont" !important;

  /* 增强图标过渡效果 */
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
}

/* 选中状态的图标样式 */
.bottom-nav .tab-item.active .tab-icon .iconfont {
  color: var(--color-primary, #8b5cf6) !important;
  font-weight: normal !important;
  filter: drop-shadow(0 2px 4px rgba(139, 92, 246, 0.3));
}

/* 优化点击反馈 */
.tab-item:active {
  transform: scale(0.95);
  background: rgba(139, 92, 246, 0.1);
}

/* 悬停效果（支持的设备） */
.tab-item:hover:not(.active) {
  background: rgba(139, 92, 246, 0.05);
}

/* 选中状态整体动画 */
.tab-item.active {
  animation: tab-activate 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

@keyframes tab-activate {
  0% {
    transform: scale(1);
  }
  30% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}
</style>
