<script setup lang="ts">
interface Props {
  title?: string
  showBack?: boolean
  showHome?: boolean
  customClass?: string
}

const { title = '', showBack = true, showHome = false, customClass = '' } = withDefaults(defineProps<Props>(), {
  title: '',
  showBack: true,
  showHome: false,
  customClass: '',
})

const emit = defineEmits(['back', 'home'])

function handleBack() {
  emit('back')
  uni.navigateBack({
    delta: 1,
  })
}

function handleHome() {
  emit('home')
  uni.switchTab({
    url: '/pages/index/index',
  })
}
</script>

<template>
  <view class="top-nav" :class="customClass">
    <view class="nav-content safe-area-inset-top">
      <!-- 左侧区域：返回按钮 -->
      <view class="nav-left">
        <view v-if="showBack" class="nav-back" @tap="handleBack">
          <text class="iconfont icon-back">
            ←
          </text>
        </view>
        <view v-if="showHome" class="nav-home" @tap="handleHome">
          <text class="iconfont icon-home">
            ⌂
          </text>
        </view>
      </view>

      <!-- 中间区域：标题 -->
      <view class="nav-title">
        <text class="title-text">
          {{ title }}
        </text>
      </view>

      <!-- 右侧区域：插槽 -->
      <view class="nav-right">
        <slot name="right" />
      </view>
    </view>
  </view>
</template>

<style scoped>
.top-nav {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  z-index: 100;
  /* 官方推荐：半透明浮层风格 */
  background: rgba(255, 255, 255, 0.92); /* 稍高不透明度，保证内容可读性 */
  box-shadow: 0 8px 32px rgba(0,0,0,0.08);
  border-bottom: 1px solid rgba(0,0,0,0.04);
  border-radius: 0 0 16px 16px;
  /* 移除毛玻璃相关属性 */
}

.nav-content {
  height: var(--status-bar-height);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 var(--spacing-md);
}

.nav-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.nav-back, .nav-home {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transition: var(--transition-base);
}

.nav-back:active, .nav-home:active {
  background: rgba(255, 255, 255, 0.5);
  transform: scale(0.95);
}

.nav-title {
  flex: 1;
  text-align: center;
  font-size: var(--font-size-page-title);
  font-weight: 600;
  color: var(--color-text-primary);
}

.nav-right {
  min-width: 32px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

/* 临时图标样式，后续替换为字体图标 */
.iconfont {
  font-size: 18px;
  color: var(--color-text-primary);
}
</style>
