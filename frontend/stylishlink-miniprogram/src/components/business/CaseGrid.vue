<!-- 案例网格组件 -->
<script setup lang="ts">
import type { ActionType } from '@/components/common/LikeFavoriteButton.vue'
import type { CaseItem } from '@/types/business'
import LikeFavoriteButton from '@/components/common/LikeFavoriteButton.vue'

// Props 定义
interface Props {
  cases?: CaseItem[] // 改为可选
}

const props = withDefaults(defineProps<Props>(), {
  cases: () => [],
})

// Events 定义
const emit = defineEmits<{
  'case-tap': [item: CaseItem]
  'like': [item: CaseItem, event: Event]
  'favorite': [item: CaseItem, event: Event]
}>()

// 处理案例点击
function handleCaseTap(item: CaseItem) {
  emit('case-tap', item)
}

// 处理按钮点击
function handleButtonClick(item: CaseItem, type: ActionType, newActive: boolean, newCount: number) {
  if (type === 'like') {
    // 更新数据状态
    item.liked = newActive
    item.likes = newCount
    emit('like', item, new Event('tap'))
  }
  else if (type === 'favorite') {
    // 更新数据状态
    item.favorited = newActive
    item.favorites = newCount
    emit('favorite', item, new Event('tap'))
  }
}
</script>

<template>
  <view class="cases-container">
    <!-- 案例分享 -->
    <view class="cases-section">
      <!-- 案例卡片网格 -->
      <view class="cases-grid">
        <view
          v-for="item in props.cases"
          :key="item.id"
          class="case-card"
        >
          <view class="case-image-container" @tap="handleCaseTap(item)">
            <image class="case-image" :src="item.image" mode="aspectFill" />
            <view class="magic-button">
              <text class="iconfont icon-mofabang magic-icon" />
            </view>
            <view class="case-title-overlay">
              <text class="case-title-text">
                {{ item.title }}
              </text>
            </view>
          </view>
          <view class="case-info">
            <view class="case-actions">
              <LikeFavoriteButton
                type="like"
                :count="item.likes"
                :active="item.liked"
                size="small"
                @click="(type, newActive, newCount) => handleButtonClick(item, type, newActive, newCount)"
              />
              <LikeFavoriteButton
                type="favorite"
                :count="item.favorites"
                :active="item.favorited"
                size="small"
                @click="(type, newActive, newCount) => handleButtonClick(item, type, newActive, newCount)"
              />
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
// 案例分享容器
.cases-container {
  padding: 0 5% 0; // 移除底部内边距，避免与tabbar产生空隙
}

// 案例分享区域
.cases-section {
  margin-bottom: 0; // 移除底部间距
  padding-bottom: 20px; // 改为内边距，避免与tabbar产生间隙
}

// 案例网格
.cases-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  padding: 0 4px 4px 4px; // 移除顶部内边距，让网格贴近分类tab
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}

.case-card {
  background: rgba(255, 255, 255, 0.15);
  border-radius: 12px;
  padding: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;

  &:active {
    transform: scale(0.98);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }
}

.case-image-container {
  position: relative;
  aspect-ratio: 1;
  border-radius: 8px;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.2);
  margin-bottom: 4px;
}

.case-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.3s ease;
}

.magic-button {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 28px;
  height: 28px;
  background: rgba(255, 255, 255, 0.25);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  z-index: 2;
}

.magic-icon {
  font-size: 12px;
  color: var(--color-text-primary, #1f2937);
}

.case-title-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(
    to top,
    rgba(0, 0, 0, 0.7) 0%,
    rgba(0, 0, 0, 0.4) 50%,
    transparent 100%
  );
  padding: 12px 8px 8px;
  z-index: 1;
}

.case-title-text {
  font-size: 11px;
  font-weight: 500;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.case-info {
  padding: 4px 0 0;
}

.case-actions {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 6px;
}

.case-action {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.25);
  border-radius: 50px;
  padding: 4px 8px;
  min-width: 52px;
  justify-content: center;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.3);

  &:active {
    transform: scale(0.95);
    background: rgba(255, 255, 255, 0.35);
  }
}

.icon-svg-container {
  margin-right: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.case-action-icon {
  font-size: 12px;
  transition: all 0.3s ease;

  &.like-normal {
    color: var(--color-text-secondary, #4b5563);
  }

  &.like-filled {
    color: #ef4444;
  }

  &.favorite-normal {
    color: var(--color-text-secondary, #4b5563);
  }

  &.favorite-filled {
    color: #f59e0b;
  }
}

.case-action-count {
  font-size: 11px;
  font-weight: 500;
  color: var(--color-text-primary, #1f2937);
}
</style>
