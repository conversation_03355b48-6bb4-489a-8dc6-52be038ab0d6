<!-- 五行柱状图组件 -->
<script setup lang="ts">
import type { WuxingRatio } from '@/utils/wuxing'
import { computed } from 'vue'
import { getWuxingClass, getWuxingPercentage, WUXING_ELEMENTS } from '@/utils/wuxing'

// 组件Props
interface Props {
  /** 五行比例数据 */
  ratio: WuxingRatio
  /** 图表高度 */
  height?: number
  /** 是否显示标签 */
  showLabel?: boolean
  /** 标签字体大小 */
  labelSize?: number
}

const props = withDefaults(defineProps<Props>(), {
  height: 64,
  showLabel: true,
  labelSize: 10,
})

// 五行数据列表
const wuxingList = computed(() => {
  return WUXING_ELEMENTS.map(element => ({
    element,
    count: props.ratio[element],
    percentage: getWuxingPercentage(props.ratio, element),
    cssClass: getWuxingClass(element),
  }))
})

// 图表样式
const chartStyle = computed(() => ({
  height: `${props.height}px`,
}))

const labelStyle = computed(() => ({
  fontSize: `${props.labelSize}px`,
}))
</script>

<template>
  <view class="wuxing-chart">
    <view class="chart-container" :style="chartStyle">
      <view
        v-for="item in wuxingList"
        :key="item.element"
        class="chart-item"
      >
        <view class="chart-bar">
          <view
            class="bar-fill"
            :class="item.cssClass"
            :style="{ height: `${item.percentage}%` }"
          />
        </view>
        <text
          v-if="showLabel"
          class="chart-label"
          :style="labelStyle"
        >
          {{ item.element }} {{ item.percentage }}%
        </text>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.wuxing-chart {
  width: 100%;
}

.chart-container {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 8px;
}

.chart-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.chart-bar {
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px 4px 0 0;
  position: relative;
  margin-bottom: 8px;
}

.bar-fill {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  border-radius: 4px 4px 0 0;
  transition: height 0.3s ease;
}

.chart-label {
  color: var(--color-text-secondary, #666);
  text-align: center;
  line-height: 1.2;
}

// 五行颜色
.wuxing-jin { background-color: #D4AF37; }
.wuxing-mu { background-color: #228B22; }
.wuxing-shui { background-color: #4169E1; }
.wuxing-huo { background-color: #DC143C; }
.wuxing-tu { background-color: #8B4513; }
</style>
