<!-- 推荐搭配卡片组件 -->
<script setup lang="ts">
import type { OutfitItem } from '@/types/business'
import { computed } from 'vue'
// Props 定义
interface Props {
  title?: string
  rating?: number
  showRating?: boolean
  outfits?: OutfitItem[]
  clickable?: boolean // 新增：是否可点击
}

const props = withDefaults(defineProps<Props>(), {
  title: '推荐内容',
  rating: 0,
  showRating: true,
  clickable: false, // 默认不可点击
})

// Emits 定义
const emit = defineEmits<{
  'card-tap': [] // 卡片点击事件
}>()

// 生成星级评分显示
const stars = computed(() => {
  const fullStars = Math.floor(props.rating)
  const hasHalfStar = props.rating % 1 >= 0.5
  const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0)

  return {
    full: fullStars,
    half: hasHalfStar,
    empty: emptyStars,
  }
})

// 处理卡片点击
function handleCardTap() {
  if (props.clickable) {
    emit('card-tap')
  }
}
</script>

<template>
  <view class="outfit-recommendation">
    <view
      class="enhanced-glass section-card"
      :class="{ clickable }"
      @tap="handleCardTap"
    >
      <view class="section-header">
        <text class="section-title">
          {{ title }}
        </text>
        <view v-if="showRating" class="rating-info">
          <view class="stars">
            <!-- 满星 -->
            <text
              v-for="i in stars.full"
              :key="`full-${i}`"
              class="star"
            >
              ⭐
            </text>
            <!-- 半星 -->
            <text
              v-if="stars.half"
              class="star half"
            >
              ⭐
            </text>
            <!-- 空星 -->
            <text
              v-for="i in stars.empty"
              :key="`empty-${i}`"
              class="star empty"
            >
              ☆
            </text>
          </view>
          <text class="rating-score">
            {{ rating.toFixed(1) }}
          </text>
        </view>
        <!-- 标题右侧扩展插槽 -->
        <slot name="header-right" />
      </view>

      <!-- 主要内容插槽 -->
      <slot />
    </view>
  </view>
</template>

<style scoped lang="scss">
// 推荐搭配容器
.outfit-recommendation {
  margin-bottom: 24px;
}

.enhanced-glass {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.05);
  border-radius: 12px;
}

.section-card {
  padding: 8px;

  // 可点击状态样式
  &.clickable {
    cursor: pointer;
    transition: all 0.2s ease;

    &:active {
      transform: scale(0.98);
      background: rgba(255, 255, 255, 0.35);
    }
  }
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--color-text-primary, #1f2937);
}

.rating-info {
  display: flex;
  align-items: center;
}

.stars {
  display: flex;
  margin-right: 4px;
}

.star {
  font-size: 12px;
  color: #ffc25c;

  &.half {
    opacity: 0.5;
  }

  &.empty {
    color: #e5e7eb;
  }
}

.rating-score {
  font-size: 12px;
  font-weight: 500;
  color: var(--color-text-primary, #1f2937);
}
</style>
