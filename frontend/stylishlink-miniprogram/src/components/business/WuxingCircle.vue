<!-- 五行相生相克圆形UI组件 - 微信小程序Canvas版本 -->
<script setup lang="ts">
import { computed, getCurrentInstance, inject, nextTick, onMounted, ref, watch } from 'vue'

// 五行数据类型定义
interface WuxingData {
  element: '金' | '木' | '水' | '火' | '土'
  percentage: number
  isHighlight?: boolean // 是否高亮显示（如日主）
}

// Props定义
interface Props {
  /** 五行分布数据 */
  wuxingData: WuxingData[]
  /** 组件尺寸 */
  size?: 'small' | 'medium' | 'large'
  /** 是否显示相生相克线条 */
  showRelations?: boolean
  /** 是否显示文字标注 */
  showLabels?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  size: 'medium',
  showRelations: true,
  showLabels: true,
})

// 五行顺序（按正五角星顶点排列：火在顶部，顺时针排列）
const wuxingOrder = ['火', '土', '金', '水', '木'] as const

// 五行颜色配置
const wuxingColors = {
  金: {
    background: '#f3f4f6',
    border: '#d1d5db',
    text: '#374151',
  },
  木: {
    background: '#e0f2e9',
    border: '#6ee7b7',
    text: '#059669',
  },
  水: {
    background: '#dbeafe',
    border: '#93c5fd',
    text: '#2563eb',
  },
  火: {
    background: '#fee2e2',
    border: '#fca5a5',
    text: '#dc2626',
  },
  土: {
    background: '#fef3c7',
    border: '#fcd34d',
    text: '#d97706',
  },
}

// 组件尺寸配置
const sizeConfig = {
  small: {
    canvasSize: 200,
    nodeRadius: 16,
    fontSize: 10,
  },
  medium: {
    canvasSize: 280,
    nodeRadius: 24,
    fontSize: 12,
  },
  large: {
    canvasSize: 320,
    nodeRadius: 32,
    fontSize: 14,
  },
}

// Canvas相关
const canvasId = `wuxing-canvas-${Date.now()}`
const instance = getCurrentInstance()
const canvasReady = ref(false)
let retryCount = 0
const maxRetry = 3

// Canvas绘制状态管理
const isDrawing = ref(false)
const isScrolling = ref(false) // 🚀 滚动状态管理

// 🚀 获取全局滚动状态
const isPageScrolling = inject<any>('isPageScrolling', ref(false))
const _scrollTimer: number | null = null // 🚀 滚动计时器（暂未使用）

// 根据元素名获取数据
function getWuxingData(element: string) {
  return props.wuxingData.find(item => item.element === element) || {
    element: element as any,
    percentage: 0,
    isHighlight: false,
  }
}

// 计算容器样式类
const containerClass = computed(() => {
  return 'wuxing-container relative mx-auto'
})

// 计算容器样式
const containerStyle = computed(() => {
  const config = sizeConfig[props.size]
  return {
    width: `${config.canvasSize}px`,
    height: `${config.canvasSize}px`,
  }
})

// Canvas初始化检查
function checkCanvasReady() {
  try {
    const ctx = uni.createCanvasContext(canvasId, instance)
    if (ctx) {
      canvasReady.value = true
      return true
    }
  }
  catch (error) {
    console.error('Canvas context creation failed:', error)
  }
  return false
}

// 计算五行节点位置（正五角星顶点）
function getNodePositions() {
  const config = sizeConfig[props.size]
  const centerX = config.canvasSize / 2
  const centerY = config.canvasSize / 2
  const radius = config.canvasSize * 0.35 // 五行节点圆的半径

  const nodePositions: { [key: string]: { x: number, y: number } } = {}
  wuxingOrder.forEach((element, index) => {
    // 正五角星的角度：从12点钟方向开始，每个顶点间隔72度
    const angle = (index * 72 - 90) * Math.PI / 180 // -90度让火在顶部
    nodePositions[element] = {
      x: centerX + radius * Math.cos(angle),
      y: centerY + radius * Math.sin(angle),
    }
  })

  return nodePositions
}

// 🚀 Canvas 2D绘制弧线（相生关系）
function drawArc2D(ctx: CanvasRenderingContext2D, startPos: any, endPos: any, centerX: number, centerY: number, radius: number, color: string = '#6b7280') {
  const config = sizeConfig[props.size]
  const nodeRadius = config.nodeRadius

  // 计算起点和终点的角度
  const startAngle = Math.atan2(startPos.y - centerY, startPos.x - centerX)
  const endAngle = Math.atan2(endPos.y - centerY, endPos.x - centerX)

  // 确保弧线是顺时针方向
  let arcStartAngle = startAngle
  let arcEndAngle = endAngle
  if (endAngle < startAngle) {
    arcEndAngle += 2 * Math.PI
  }

  // 调整弧线起点和终点，避免与节点重叠
  const adjustedStartAngle = arcStartAngle + (nodeRadius + 10) / radius
  const adjustedEndAngle = arcEndAngle - (nodeRadius + 10) / radius

  ctx.strokeStyle = color
  ctx.lineWidth = 2
  ctx.beginPath()
  ctx.arc(centerX, centerY, radius, adjustedStartAngle, adjustedEndAngle)
  ctx.stroke()

  // 在弧线末端绘制箭头
  const arrowAngle = adjustedEndAngle - 0.1 // 箭头位置稍微向内
  const arrowX = centerX + radius * Math.cos(arrowAngle)
  const arrowY = centerY + radius * Math.sin(arrowAngle)
  const tangentAngle = arrowAngle + Math.PI / 2 // 切线方向

  ctx.save()
  ctx.fillStyle = color
  ctx.translate(arrowX, arrowY)
  ctx.rotate(tangentAngle)

  // 绘制三角形箭头
  const arrowSize = config.canvasSize > 250 ? 8 : 6
  ctx.beginPath()
  ctx.moveTo(0, 0)
  ctx.lineTo(-arrowSize / 2, -arrowSize / 2)
  ctx.lineTo(arrowSize / 2, arrowSize / 2)
  ctx.closePath()
  ctx.fill()

  ctx.restore()

  // 在弧线中间绘制"生"字标注
  const midAngle = (arcStartAngle + arcEndAngle) / 2
  const textRadius = radius + 15
  const textX = centerX + textRadius * Math.cos(midAngle)
  const textY = centerY + textRadius * Math.sin(midAngle)

  ctx.save()
  ctx.fillStyle = color
  const labelFontSize = Math.max(8, config.fontSize - 2)
  ctx.font = `${labelFontSize}px sans-serif`
  ctx.textAlign = 'center'
  ctx.textBaseline = 'middle'
  ctx.fillText('生', textX, textY)
  ctx.restore()
}

// 🚀 Canvas 2D绘制虚线（相克关系）
function drawDashedLine2D(ctx: CanvasRenderingContext2D, startPos: any, endPos: any, color: string = '#9ca3af') {
  const config = sizeConfig[props.size]
  const nodeRadius = config.nodeRadius

  // 计算方向向量
  const dx = endPos.x - startPos.x
  const dy = endPos.y - startPos.y
  const length = Math.sqrt(dx * dx + dy * dy)
  const unitX = dx / length
  const unitY = dy / length

  // 调整起点和终点，避免与节点重叠
  const adjustedStartX = startPos.x + unitX * (nodeRadius + 10)
  const adjustedStartY = startPos.y + unitY * (nodeRadius + 10)
  const adjustedEndX = endPos.x - unitX * (nodeRadius + 10)
  const adjustedEndY = endPos.y - unitY * (nodeRadius + 10)

  ctx.strokeStyle = color
  ctx.lineWidth = 2
  ctx.setLineDash([6, 4])
  ctx.beginPath()
  ctx.moveTo(adjustedStartX, adjustedStartY)
  ctx.lineTo(adjustedEndX, adjustedEndY)
  ctx.stroke()
  ctx.setLineDash([]) // 重置虚线设置

  // 在虚线末端绘制箭头
  const arrowDistance = nodeRadius + 8
  const arrowX = endPos.x - unitX * arrowDistance
  const arrowY = endPos.y - unitY * arrowDistance
  const angle = Math.atan2(dy, dx)

  ctx.save()
  ctx.fillStyle = color
  ctx.translate(arrowX, arrowY)
  ctx.rotate(angle)

  // 绘制三角形箭头
  const arrowSize = config.canvasSize > 250 ? 8 : 6
  ctx.beginPath()
  ctx.moveTo(0, 0)
  ctx.lineTo(-arrowSize, -arrowSize / 2)
  ctx.lineTo(-arrowSize, arrowSize / 2)
  ctx.closePath()
  ctx.fill()

  ctx.restore()

  // 在虚线中间绘制"克"字标注
  const midX = (adjustedStartX + adjustedEndX) / 2
  const midY = (adjustedStartY + adjustedEndY) / 2

  // 计算垂直于虚线的方向向量，向外偏移
  const offsetDistance = 12
  const perpX = unitY
  const perpY = -unitX

  // "克"字位置：虚线中点向外偏移
  const textX = midX + perpX * offsetDistance
  const textY = midY + perpY * offsetDistance

  ctx.save()
  ctx.fillStyle = color
  const labelFontSize = Math.max(8, config.fontSize - 2)
  ctx.font = `${labelFontSize}px sans-serif`
  ctx.textAlign = 'center'
  ctx.textBaseline = 'middle'
  ctx.fillText('克', textX, textY)
  ctx.restore()
}

// 绘制弧线（相生关系）- 旧版本保留
function drawArc(ctx: any, startPos: any, endPos: any, centerX: number, centerY: number, radius: number, color: string = '#10b981') {
  const config = sizeConfig[props.size]
  const startAngle = Math.atan2(startPos.y - centerY, startPos.x - centerX)
  const endAngle = Math.atan2(endPos.y - centerY, endPos.x - centerX)

  // 处理角度跨越的情况，确保按顺时针方向绘制弧线
  let actualEndAngle = endAngle
  if (endAngle < startAngle) {
    actualEndAngle = endAngle + 2 * Math.PI
  }

  // 计算弧线起始和停止角度（距离节点边缘一定距离）
  const nodeRadius = config.nodeRadius
  const startDistance = nodeRadius + 12 // 弧线距离起始节点边缘16px开始
  const stopDistance = nodeRadius + 16 // 弧线距离目标节点边缘12px停止
  const startAngleOffset = startDistance / radius // 起始角度偏移量
  const stopAngleOffset = stopDistance / radius // 停止角度偏移量
  const arcStartAngle = startAngle + startAngleOffset
  const arcEndAngle = actualEndAngle - stopAngleOffset

  // 绘制弧线（从起始节点边缘开始，不完全到达目标节点）
  ctx.setStrokeStyle(color)
  ctx.setLineWidth(2)
  ctx.setLineCap('round')
  ctx.beginPath()
  ctx.arc(centerX, centerY, radius, arcStartAngle, arcEndAngle, false)
  ctx.stroke()

  // 计算箭头位置：弧线的实际终点
  const arrowX = centerX + radius * Math.cos(arcEndAngle)
  const arrowY = centerY + radius * Math.sin(arcEndAngle)

  // 箭头方向：沿弧线切线方向（垂直于半径方向）
  const tangentAngle = arcEndAngle - Math.PI

  ctx.save()
  ctx.setFillStyle(color)
  ctx.translate(arrowX, arrowY)
  ctx.rotate(tangentAngle)

  // 绘制三角形箭头
  const arrowSize = config.canvasSize > 250 ? 8 : 6 // 根据尺寸调整箭头大小
  ctx.beginPath()
  ctx.moveTo(0, -arrowSize)
  ctx.lineTo(-arrowSize / 2, arrowSize / 2)
  ctx.lineTo(arrowSize / 2, arrowSize / 2)
  ctx.closePath()
  ctx.fill()

  ctx.restore()

  // 在弧线中间绘制"生"字标注（使用动态字号）
  // 使用实际弧线的中点角度
  const midAngle = (arcStartAngle + arcEndAngle) / 2
  const textRadius = radius + 15 // 文字距离弧线外侧适中距离
  const textX = centerX + textRadius * Math.cos(midAngle)
  const textY = centerY + textRadius * Math.sin(midAngle)

  ctx.save()
  ctx.setFillStyle(color)
  const labelFontSize = Math.max(8, config.fontSize - 2) // 关系标注字号比节点字号小2px，最小8px
  ctx.setFontSize(labelFontSize)
  ctx.setTextAlign('center')
  ctx.setTextBaseline('middle')
  ctx.fillText('生', textX, textY)
  ctx.restore()
}

// 绘制虚线（相克关系）
function drawDashedLine(ctx: any, startPos: any, endPos: any, color: string = '#9ca3af') {
  const config = sizeConfig[props.size]
  const nodeRadius = config.nodeRadius

  // 计算方向向量
  const dx = endPos.x - startPos.x
  const dy = endPos.y - startPos.y
  const length = Math.sqrt(dx * dx + dy * dy)
  const unitX = dx / length
  const unitY = dy / length

  // 调整起点和终点，避免与节点重叠
  const adjustedStartX = startPos.x + unitX * (nodeRadius + 10)
  const adjustedStartY = startPos.y + unitY * (nodeRadius + 10)
  const adjustedEndX = endPos.x - unitX * (nodeRadius + 10)
  const adjustedEndY = endPos.y - unitY * (nodeRadius + 10)

  ctx.setStrokeStyle(color)
  ctx.setLineWidth(2)
  ctx.setLineDash([6, 4])
  ctx.beginPath()
  ctx.moveTo(adjustedStartX, adjustedStartY)
  ctx.lineTo(adjustedEndX, adjustedEndY)
  ctx.stroke()
  ctx.setLineDash([]) // 重置虚线设置

  // 在虚线末端绘制箭头（距离目标节点适当距离）
  const arrowDistance = nodeRadius + 8 // 箭头距离节点边缘8px
  const arrowX = endPos.x - unitX * arrowDistance
  const arrowY = endPos.y - unitY * arrowDistance
  const angle = Math.atan2(dy, dx)

  ctx.save()
  ctx.setFillStyle(color)
  ctx.translate(arrowX, arrowY)
  ctx.rotate(angle)

  // 绘制三角形箭头
  const arrowSize = config.canvasSize > 250 ? 8 : 6 // 根据尺寸调整箭头大小
  ctx.beginPath()
  ctx.moveTo(0, 0)
  ctx.lineTo(-arrowSize, -arrowSize / 2)
  ctx.lineTo(-arrowSize, arrowSize / 2)
  ctx.closePath()
  ctx.fill()

  ctx.restore()

  // 在虚线中间绘制"克"字标注（使用动态字号）
  const midX = (adjustedStartX + adjustedEndX) / 2
  const midY = (adjustedStartY + adjustedEndY) / 2

  // 计算垂直于虚线的方向向量，向外偏移
  const offsetDistance = 12 // "克"字距离虚线的外侧距离
  const perpX = unitY // 垂直方向（向右旋转90度）
  const perpY = -unitX

  // "克"字位置：虚线中点向外偏移
  const textX = midX + perpX * offsetDistance
  const textY = midY + perpY * offsetDistance

  ctx.save()
  ctx.setFillStyle(color)
  const labelFontSize = Math.max(8, config.fontSize - 2) // 关系标注字号比节点字号小2px，最小8px
  ctx.setFontSize(labelFontSize)
  ctx.setTextAlign('center')
  ctx.setTextBaseline('middle')
  ctx.fillText('克', textX, textY)
  ctx.restore()
}

// 绘制五行相生相克图
function drawWuxingChart() {
  if (isDrawing.value)
    return // 防止重复绘制

  // 🚀 优化滚动检测：只在已经绘制过且正在滚动时跳过重绘
  if (isDrawing.value) {
    console.warn('WuxingCircle: 正在绘制中，跳过重绘')
    return
  }

  // 🚀 只有在Canvas已经初始化且正在滚动时才跳过重绘
  if (canvasReady.value && (isScrolling.value || isPageScrolling.value)) {
    console.warn('WuxingCircle: 滚动中，跳过重绘', {
      localScrolling: isScrolling.value,
      pageScrolling: isPageScrolling.value,
    })
    return
  }

  if (!canvasReady.value && !checkCanvasReady()) {
    console.warn('Canvas not ready, will retry...')
    if (retryCount < maxRetry) {
      retryCount++
      setTimeout(() => {
        drawWuxingChart()
      }, 1000 * retryCount)
    }
    return
  }

  isDrawing.value = true

  // 🚀 Canvas 2D接口获取方式
  const query = uni.createSelectorQuery().in(getCurrentInstance())
  query.select(`#${canvasId}`)
    .fields({ node: true, size: true })
    .exec((res) => {
      if (!res || !res[0]) {
        console.error('WuxingCircle Canvas 2D节点获取失败')
        isDrawing.value = false
        return
      }

      const canvas = res[0].node
      if (!canvas) {
        console.error('WuxingCircle Canvas 2D节点为空')
        isDrawing.value = false
        return
      }

      const ctx = canvas.getContext('2d')
      if (!ctx) {
        console.error('WuxingCircle Canvas 2D上下文获取失败')
        isDrawing.value = false
        return
      }

      try {
        // 🚀 Canvas 2D设置尺寸和DPR适配
        const config = sizeConfig[props.size]
        const dpr = uni.getSystemInfoSync().pixelRatio || 1
        canvas.width = config.canvasSize * dpr
        canvas.height = config.canvasSize * dpr
        ctx.scale(dpr, dpr)

        // 🚀 Canvas 2D性能优化设置
        ctx.imageSmoothingEnabled = true
        ctx.imageSmoothingQuality = 'high'

        // 清空画布
        ctx.clearRect(0, 0, config.canvasSize, config.canvasSize)

        // 绘制五行图 - 使用Canvas 2D标准API
        drawWuxingChart2D(ctx, config)

        canvasReady.value = true
        console.warn('WuxingCircle Canvas 2D绘制完成')
        isDrawing.value = false
      }
      catch (error) {
        console.error('WuxingCircle Canvas 2D绘制错误:', error)
        isDrawing.value = false
      }
    })
}

// 🚀 Canvas 2D绘制五行图
function drawWuxingChart2D(ctx: CanvasRenderingContext2D, config: any) {

  const centerX = config.canvasSize / 2
  const centerY = config.canvasSize / 2
  const radius = config.canvasSize * 0.35

  // 获取节点位置
  const positions = getNodePositions()

  if (props.showRelations) {
    // 1. 绘制相生关系（绿色弧线+箭头）
    // 相生顺序：木→火→土→金→水→木
    const shengPairs = [
      ['木', '火'],
      ['火', '土'],
      ['土', '金'],
      ['金', '水'],
      ['水', '木'],
    ]

    shengPairs.forEach(([from, to]) => {
      const fromPos = positions[from]
      const toPos = positions[to]
      drawArc2D(ctx, fromPos, toPos, centerX, centerY, radius)
    })

    // 2. 绘制相克关系（红色虚线）
    // 相克关系：火克金、金克木、木克土、土克水、水克火
    const kePairs = [
      ['火', '金'],
      ['金', '木'],
      ['木', '土'],
      ['土', '水'],
      ['水', '火'],
    ]

    kePairs.forEach(([from, to]) => {
      const fromPos = positions[from]
      const toPos = positions[to]
      drawDashedLine2D(ctx, fromPos, toPos)
    })
  }

  // 3. 绘制五行节点
  wuxingOrder.forEach((element) => {
    const pos = positions[element]
    const data = getWuxingData(element)
    const colors = wuxingColors[element]
    const nodeRadius = config.nodeRadius

    // 绘制节点圆形背景
    ctx.fillStyle = colors.background
    ctx.strokeStyle = data.isHighlight ? colors.text : colors.border
    ctx.lineWidth = data.isHighlight ? 3 : 2

    ctx.beginPath()
    ctx.arc(pos.x, pos.y, nodeRadius, 0, 2 * Math.PI)
    ctx.fill()
    ctx.stroke()

    // 绘制文字
    ctx.fillStyle = colors.text
    ctx.textAlign = 'center'
    ctx.textBaseline = 'middle'

    // 绘制五行名称（保持原始位置）
    ctx.font = `${config.fontSize}px sans-serif`
    const nameOffsetY = nodeRadius * 0.25 // 五行名称向上偏移
    ctx.fillText(element, pos.x, pos.y - nameOffsetY)

    // 绘制百分比（保持原始位置）
    ctx.font = `${config.fontSize - 2}px sans-serif`
    const percentOffsetY = nodeRadius * 0.25 // 百分比向下偏移
    ctx.fillText(`${data.percentage}%`, pos.x, pos.y + percentOffsetY)

    // 如果是日主，在圆边缘下方添加"日主"标注
    if (data.isHighlight) {
      const masterFontSize = Math.max(8, config.fontSize - 2) // 比原字号小2号
      const masterOffsetY = nodeRadius - 2 // 圆边缘位置，稍微向内2px
      const textX = pos.x
      const textY = pos.y + masterOffsetY

      // 绘制"日主"文字背景
      ctx.font = `${masterFontSize}px sans-serif`
      const textWidth = 32 // 加宽背景以凸显圆角
      const textHeight = masterFontSize + 8 // 增加上下padding
      const bgX = textX - textWidth / 2
      const bgY = textY - textHeight / 2

      // 绘制半透明白色圆角背景
      ctx.fillStyle = 'rgba(255, 255, 255, 0.9)'
      ctx.strokeStyle = 'rgba(0, 0, 0, 0.1)'
      ctx.lineWidth = 1
      ctx.beginPath()

      // 手动绘制圆角矩形（兼容微信小程序）
      const radius = 8 // 增加圆角半径以凸显效果
      ctx.moveTo(bgX + radius, bgY)
      ctx.lineTo(bgX + textWidth - radius, bgY)
      ctx.arc(bgX + textWidth - radius, bgY + radius, radius, -Math.PI / 2, 0)
      ctx.lineTo(bgX + textWidth, bgY + textHeight - radius)
      ctx.arc(bgX + textWidth - radius, bgY + textHeight - radius, radius, 0, Math.PI / 2)
      ctx.lineTo(bgX + radius, bgY + textHeight)
      ctx.arc(bgX + radius, bgY + textHeight - radius, radius, Math.PI / 2, Math.PI)
      ctx.lineTo(bgX, bgY + radius)
      ctx.arc(bgX + radius, bgY + radius, radius, Math.PI, Math.PI * 3 / 2)
      ctx.closePath()

      ctx.fill()
      ctx.stroke()

      // 绘制"日主"文字
      ctx.fillStyle = colors.text
      ctx.textAlign = 'center'
      ctx.textBaseline = 'middle'
      ctx.fillText('日主', textX, textY)
    }
  })
}

// Canvas错误处理
function handleCanvasError(e: any) {
  console.error('Canvas error:', e)
  // 出错时重试绘制
  setTimeout(() => {
    drawWuxingChart()
  }, 500)
}

// 防抖计时器
let debounceTimer: number | null = null

// 监听属性变化重新绘制 - 数据就绪检查 + iOS滚动抖动优化
watch([() => props.wuxingData, () => props.size, () => props.showRelations], (newValues, oldValues) => {
  // 如果正在绘制，则跳过重绘
  if (isDrawing.value)
    return

  // 数据变化时的特殊处理
  const [newData, _newSize, _newRelations] = newValues
  const [oldData, _oldSize, _oldRelations] = oldValues || []

  // 检查是否为数据首次加载（从空到有数据）
  const isFirstDataLoad = (!oldData || oldData.length === 0) && (newData && newData.length > 0)

  if (isFirstDataLoad) {
    console.warn('WuxingCircle: 检测到数据首次加载，使用智能延时绘制')
    smartDelayedDraw()
    return
  }

  // 清除之前的计时器
  if (debounceTimer) {
    clearTimeout(debounceTimer)
  }

  // 设置新的防抖计时器，进一步增加延迟减少滚动抖动
  debounceTimer = setTimeout(() => {
    if (!checkDataReady()) {
      console.warn('WuxingCircle: 数据不完整，跳过重绘')
      return
    }

    // 使用requestAnimationFrame优化重绘时机
    if (typeof requestAnimationFrame !== 'undefined') {
      requestAnimationFrame(() => {
        nextTick(() => {
          drawWuxingChart()
        })
      })
    }
    else {
      nextTick(() => {
        drawWuxingChart()
      })
    }
    debounceTimer = null
  }, 600) // 🚀 增加到600ms防抖延迟，进一步减少iOS滚动抖动
}, { deep: true })

// 数据就绪检查函数
function checkDataReady(): boolean {
  const isReady = props.wuxingData && Array.isArray(props.wuxingData) && props.wuxingData.length > 0
  console.warn('WuxingCircle数据检查:', {
    hasWuxingData: !!props.wuxingData,
    isArray: Array.isArray(props.wuxingData),
    dataLength: props.wuxingData?.length || 0,
    dataContent: props.wuxingData,
    isReady,
  })
  return isReady
}

// iOS设备检测
function isIOSDevice(): boolean {
  const deviceInfo = uni.getDeviceInfo()
  return deviceInfo.platform === 'ios'
}

// 智能延时绘制
function smartDelayedDraw() {
  console.warn('WuxingCircle: 开始智能延时绘制检查')

  if (!checkDataReady()) {
    console.warn('WuxingCircle: 数据未就绪，跳过绘制')
    return
  }

  // iOS设备使用更长延时，确保渲染完成
  const delay = isIOSDevice() ? 800 : 300

  setTimeout(() => {
    if (checkDataReady()) {
      console.warn('WuxingCircle: 开始绘制，延时:', `${delay}ms`)
      drawWuxingChart()
    }
    else {
      console.warn('WuxingCircle: 延时后数据仍未就绪')
    }
  }, delay)
}

// 组件挂载时初始化
onMounted(() => {
  nextTick(() => {
    smartDelayedDraw()
  })
})
</script>

<template>
  <view :class="containerClass" :style="containerStyle">
    <!-- 🚀 Canvas 2D方案：支持同层渲染，解决滚动抖动 -->
    <canvas
      :id="canvasId"
      :canvas-id="canvasId"
      class="wuxing-canvas"
      type="2d"
      :style="{
        width: `${sizeConfig[size].canvasSize}px`,
        height: `${sizeConfig[size].canvasSize}px`,
      }"
      @error="handleCanvasError"
    />
  </view>
</template>

<style scoped lang="scss">
.wuxing-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  overflow: hidden;

  // 🚀 Canvas滚动性能优化
  will-change: transform; /* 明确告知浏览器会发生变换 */
  contain: layout style paint; /* 限制重绘范围 */
  transform: translate3d(0, 0, 0); /* 强制GPU加速 */
  -webkit-transform: translate3d(0, 0, 0);

  // 🚀 防止滚动时的层级抖动
  isolation: isolate;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;

  // 🚀 优化合成层
  -webkit-perspective: 1000px;
  perspective: 1000px;
}

.wuxing-canvas {
  display: block;

  // 🚀 Canvas滚动性能优化
  will-change: transform; /* 明确告知会发生变换 */
  transform: translate3d(0, 0, 0); /* 强制GPU加速 */
  -webkit-transform: translate3d(0, 0, 0);
  contain: strict; /* 最严格的包含，防止影响其他元素 */

  // 🚀 防止Canvas重绘抖动
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  -webkit-perspective: 1000px;
  perspective: 1000px;

  // 🚀 优化Canvas渲染
  image-rendering: -webkit-optimize-contrast; /* iOS优化 */
  image-rendering: crisp-edges;

  // 🚀 防止滚动时的内容跳动
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// 🚀 滚动时应用的优化样式
.canvas-scrolling {
  // 🚀 关键：滚动时轻微缩放，强制使用合成层
  transform: translate3d(0, 0, 0) scale(0.999) !important;
  -webkit-transform: translate3d(0, 0, 0) scale(0.999) !important;

  // 🚀 滚动时降低透明度，减少视觉冲击
  opacity: 0.95;

  // 🚀 滚动时禁用指针事件，减少交互干扰
  pointer-events: none;

  // 🚀 强制硬件加速
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  -webkit-perspective: 1000px;
  perspective: 1000px;
}
</style>
