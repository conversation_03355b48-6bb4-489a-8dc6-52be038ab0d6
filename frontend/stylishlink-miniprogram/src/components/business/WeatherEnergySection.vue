<!-- 天气和能量建议整合组件 -->
<script setup lang="ts">
import type {
  AdviceItem,
  EnergyData,
  LocationInfo,
  LunarInfo,
  WeatherData,
} from '@/types/business'
import { computed, ref } from 'vue'
import CitySelector from '@/components/common/CitySelector.vue'
import EnergyCircleProgress from '@/components/common/EnergyCircleProgress.vue'

// Props 定义
interface Props {
  weatherData: WeatherData
  locationInfo: LocationInfo
  lunarInfo: LunarInfo
  energyData: EnergyData
  adviceList: AdviceItem[]
  loading?: boolean
  error?: Error | null
}

const props = defineProps<Props>()

// Events 定义
const emit = defineEmits<{
  'city-change': [cityName: string]
  'energy-tap': []
  'advice-tap': [advice: AdviceItem]
  'reload': []
}>()

// 当前选中的城市
const currentCity = ref(props.locationInfo.city || '北京')

// 处理城市改变
function handleCityChange(city: any) {
  currentCity.value = city.name
  emit('city-change', city.name)
}

// 处理能量点击
function handleEnergyTap() {
  emit('energy-tap')
}

// 处理建议点击
function handleAdviceTap(advice: AdviceItem) {
  emit('advice-tap', advice)
}

// 处理重新加载
function handleReload() {
  emit('reload')
}

// 格式化天气显示
const weatherDisplay = computed(() => {
  return `${props.weatherData.condition}, ${props.weatherData.temperature}°`
})

// 是否显示错误状态
const showError = computed(() => {
  return props.error && !props.loading
})

// 是否显示加载状态
const showLoading = computed(() => {
  return props.loading
})
</script>

<template>
  <view class="weather-energy-section">
    <!-- 顶部天气区域 -->
    <view class="weather-header">
      <view class="weather-main">
        <view class="weather-line">
          <CitySelector
            v-model="currentCity"
            @change="handleCityChange"
          />
          <text class="weather-text">
            {{ weatherDisplay }}
          </text>
        </view>
        <text class="lunar-text">
          {{ lunarInfo.fullDate }}
        </text>
      </view>
    </view>

    <!-- 能量和建议区域 -->
    <view class="energy-advice-container">
      <!-- 错误状态显示 -->
      <view v-if="showError" class="error-container">
        <view class="error-content">
          <text class="iconfont icon-error error-icon" />
          <text class="error-message">
            {{ error?.message || '能量数据加载失败' }}
          </text>
          <view class="error-actions">
            <button class="reload-button" @tap="handleReload">
              <text class="iconfont icon-refresh reload-icon" />
              <text class="reload-text">
                重新加载
              </text>
            </button>
          </view>
        </view>
      </view>

      <!-- 加载状态显示 -->
      <view v-else-if="showLoading" class="loading-container">
        <view class="loading-content">
          <text class="loading-spinner" />
          <text class="loading-text">
            正在加载能量数据...
          </text>
        </view>
      </view>

      <!-- 正常状态：左侧能量区域 -->
      <view v-else class="energy-section" @tap="handleEnergyTap">
        <!-- 使用新的圆形进度条组件 -->
        <EnergyCircleProgress
          :score="energyData.score"
          :percentage="energyData.percentage"
          :size="120"
          label="今日能量"
          :show-percentage="true"
        />
      </view>

      <!-- 右侧建议区域（正常状态和加载状态都显示） -->
      <view v-if="!showError" class="advice-section">
        <view
          v-for="(advice, index) in adviceList"
          :key="advice.id"
          class="advice-item"
          :class="{ 'advice-item--last': index === adviceList.length - 1 }"
          @tap="handleAdviceTap(advice)"
        >
          <view class="advice-content">
            <view
              class="advice-icon-wrapper"
              :style="{ backgroundColor: advice.backgroundColor }"
            >
              <text class="iconfont advice-icon" :class="advice.icon" />
            </view>
            <view class="advice-text">
              <text class="advice-title">
                {{ advice.title }}
              </text>
              <text class="advice-description">
                {{ advice.description }}
              </text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
.weather-energy-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

// 天气头部
.weather-header {
  margin-bottom: 6px;
}

.weather-main {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.weather-line {
  display: flex;
  align-items: center;
  gap: 8px;
}

.weather-text {
  font-size: 14px;
  font-weight: 600;
  color: var(--color-text-primary, #1f2937);
  line-height: 1.3;
}

.lunar-text {
  font-size: 11px;
  color: var(--color-text-secondary, #6b7280);
  line-height: 1.3;
  opacity: 0.9;
}

// 能量和建议容器 - 左右分栏布局
.energy-advice-container {
  display: flex;
  gap: 8px;
  margin-top: 4px;
}

// 左侧能量区域 - 1/3宽度
.energy-section {
  flex: 1;
  max-width: 33.333%;
  padding-right: 8px;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
}

// 右侧建议区域 - 2/3宽度
.advice-section {
  flex: 2;
  max-width: 66.667%;
  padding-left: 8px;
  border-left: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.advice-item {
  cursor: pointer;

  &:not(.advice-item--last) {
    margin-bottom: 0;
  }
}

.advice-content {
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.advice-icon-wrapper {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  margin-top: 2px;
}

.advice-icon {
  font-size: 14px;
  color: white;
}

.advice-text {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
}

.advice-title {
  font-size: 13px;
  font-weight: 600;
  color: var(--color-text-primary, #1f2937);
  line-height: 1.2;
  margin-bottom: 2px;
}

.advice-description {
  font-size: 11px;
  color: var(--color-text-secondary, #6b7280);
  line-height: 1.4;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

// 响应式调整
@media (max-width: 375px) {
  .weather-text {
    font-size: 14px;
  }

  .lunar-text {
    font-size: 10px;
  }

  .advice-title {
    font-size: 12px;
  }

  .advice-description {
    font-size: 10px;
  }

  .energy-score {
    font-size: 20px;
  }

  .energy-label {
    font-size: 10px;
  }

  .percentage-text {
    font-size: 10px;
  }
}

// 错误状态样式
.error-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 120px;
  padding: 16px;
}

.error-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  text-align: center;
}

.error-icon {
  font-size: 32px;
  color: #ff6b6b;
  opacity: 0.8;
}

.error-message {
  font-size: 12px;
  color: var(--color-text-secondary, #6b7280);
  line-height: 1.4;
}

.error-actions {
  margin-top: 8px;
}

.reload-button {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 8px 16px;
  background: rgba(139, 92, 246, 0.1);
  border: 1px solid rgba(139, 92, 246, 0.3);
  border-radius: 20px;
  font-size: 12px;
  color: var(--color-primary, #8b5cf6);
  transition: all 0.2s ease;

  &:active {
    transform: scale(0.95);
    background: rgba(139, 92, 246, 0.15);
  }
}

.reload-icon {
  font-size: 14px;
}

.reload-text {
  font-size: 12px;
  font-weight: 500;
}

// 加载状态样式
.loading-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 120px;
  padding: 16px;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  text-align: center;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(139, 92, 246, 0.2);
  border-top: 2px solid var(--color-primary, #8b5cf6);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 12px;
  color: var(--color-text-secondary, #6b7280);
  line-height: 1.4;
}
</style>
