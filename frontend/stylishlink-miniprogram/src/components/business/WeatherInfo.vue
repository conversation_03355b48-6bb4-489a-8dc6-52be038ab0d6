<!-- 天气信息组件 -->
<script setup lang="ts">
import type { LocationInfo, LunarInfo, WeatherData } from '@/types/business'
import { computed } from 'vue'

// Props 定义
interface Props {
  weatherData?: WeatherData
  locationInfo?: LocationInfo
  lunarInfo?: LunarInfo
}

const props = withDefaults(defineProps<Props>(), {
  weatherData: () => ({
    temperature: 22,
    condition: '晴',
    icon: 'icon-taiyang',
  }),
  locationInfo: () => ({
    city: '北京市',
  }),
  lunarInfo: () => ({
    month: '二月',
    day: '十九',
    fullDate: '二月十九 己巳卯 丙成',
  }),
})

// Events 定义
const emit = defineEmits<{
  'location-tap': [locationInfo: LocationInfo]
}>()

// 处理位置点击
function handleLocationTap() {
  emit('location-tap', props.locationInfo)
}

// 格式化温度显示
const formattedTemperature = computed(() => {
  return `${props.weatherData.condition}, ${props.weatherData.temperature}°`
})
</script>

<template>
  <view class="weather-header">
    <view class="weather-info">
      <view class="weather-main">
        <view class="weather-temp-wrapper">
          <text class="iconfont weather-icon" :class="weatherData.icon" />
          <text class="weather-temp">
            {{ formattedTemperature }}
          </text>
        </view>
        <view class="location-info" @tap="handleLocationTap">
          <text class="iconfont icon-dingwei location-icon" />
          <text class="location-text">
            {{ locationInfo.city }}
          </text>
        </view>
      </view>
      <text class="weather-date">
        {{ lunarInfo.fullDate }}
      </text>
    </view>
  </view>
</template>

<style scoped lang="scss">
.weather-header {
  margin-bottom: 12px;
}

.weather-info {
  display: flex;
  flex-direction: column;
}

.weather-main {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 4px;
}

.weather-temp-wrapper {
  display: flex;
  align-items: center;
  gap: 4px;
}

.weather-icon {
  font-size: 16px;
  color: #fbbf24;
}

.weather-temp {
  font-size: 16px;
  font-weight: 700;
  color: var(--color-text-primary, #1f2937);
}

.weather-date {
  font-size: 10px;
  color: var(--color-text-secondary, #4b5563);
}

.location-info {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: var(--color-text-secondary, #4b5563);
  background: rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(4px);
  border-radius: 12px;
  padding: 2px 6px;
  cursor: pointer;
  transition: all 0.3s ease;

  &:active {
    background: rgba(255, 255, 255, 0.5);
  }
}

.location-icon {
  font-size: 12px;
}

.location-text {
  margin-left: 4px;
}
</style>
