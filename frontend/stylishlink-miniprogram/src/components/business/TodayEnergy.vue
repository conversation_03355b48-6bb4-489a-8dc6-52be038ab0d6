<!-- 今日能量组件 -->
<script setup lang="ts">
import type { AdviceItem, EnergyData } from '@/types/business'

// Props 定义
interface Props {
  energyData?: EnergyData
  adviceList?: AdviceItem[]
}

const props = withDefaults(defineProps<Props>(), {
  energyData: () => ({ score: 0, percentage: 0 }),
  adviceList: () => [],
})

// Events 定义
const emit = defineEmits<{
  'energy-tap': []
  'advice-tap': [item: AdviceItem]
}>()

// 处理能量区域点击
function handleEnergyTap() {
  emit('energy-tap')
}

// 处理建议项点击
function handleAdviceTap(item: AdviceItem) {
  emit('advice-tap', item)
}
</script>

<template>
  <view class="fortune-advice-area">
    <!-- 左侧运势区域 -->
    <view class="fortune-section" @tap="handleEnergyTap">
      <view class="energy-circle">
        <!-- 圆形进度条容器 -->
        <view class="circle-container">
          <view class="progress-circle">
            <view class="circle-bg" />
            <view
              class="circle-progress"
              :style="{
                transform: `rotate(-90deg)`,
                background: `conic-gradient(
                  from 0deg,
                  #d8b4fe 0deg,
                  #c4a6fa ${props.energyData?.score * 3.6}deg,
                  transparent ${props.energyData?.score * 3.6}deg,
                  transparent 360deg
                )`,
              }"
            />
          </view>
          <view class="circle-content">
            <text class="energy-label">
              今日能量
            </text>
            <text class="energy-score">
              {{ props.energyData?.score }}
            </text>
          </view>
        </view>
        <text class="energy-desc">
          超过了{{ props.energyData?.percentage }}%的用户
        </text>
      </view>
    </view>

    <!-- 右侧穿搭建议列表 -->
    <view class="advice-section">
      <view
        v-for="item in props.adviceList"
        :key="item.id"
        class="advice-item"
        @tap="handleAdviceTap(item)"
      >
        <view class="advice-icon" :style="{ background: item.backgroundColor }">
          <text class="iconfont icon" :class="item.icon" />
        </view>
        <view class="advice-content">
          <text class="advice-title">
            {{ item.title }}
          </text>
          <text class="advice-desc">
            {{ item.description }}
          </text>
        </view>
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
// 运势与建议区域
.fortune-advice-area {
  display: flex;
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 16px;
  margin-bottom: 20px;
  border: 1px solid rgba(255, 255, 255, 0.6);
  box-shadow:
    0 -8px 32px rgba(0, 0, 0, 0.06),
    0 -2px 8px rgba(0, 0, 0, 0.03),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

.fortune-section {
  width: 33.33%;
  padding-right: 8px;
  cursor: pointer;
}

.energy-circle {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.circle-container {
  position: relative;
  width: 80px;
  height: 80px;
  margin-bottom: 8px;
}

.progress-circle {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  overflow: hidden;
}

.circle-bg {
  position: absolute;
  top: 4px;
  left: 4px;
  right: 4px;
  bottom: 4px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.15);
  border: 4px solid rgba(255, 255, 255, 0.3);
  box-sizing: border-box;
}

.circle-progress {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  mask: radial-gradient(
    circle at center,
    transparent 28px,
    black 32px
  );
}

.circle-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.energy-label {
  font-size: 10px;
  font-weight: 500;
  color: var(--color-text-secondary, #4b5563);
  margin-bottom: 2px;
}

.energy-score {
  font-size: 24px;
  font-weight: 700;
  color: var(--color-text-primary, #1f2937);
}

.energy-desc {
  margin-top: 4px;
  font-size: 10px;
  color: var(--color-text-secondary, #4b5563);
  opacity: 0.8;
  text-align: center;
}

.advice-section {
  width: 66.67%;
  padding-left: 8px;
  border-left: 1px solid rgba(255, 255, 255, 0.2);
}

.advice-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 10px;
  cursor: pointer;

  &:last-child {
    margin-bottom: 0;
  }
}

.advice-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
  margin-top: 2px;
  flex-shrink: 0;
}

.icon {
  font-size: 11px;
}

.advice-content {
  flex: 1;
}

.advice-title {
  display: block;
  font-size: 12px;
  font-weight: 500;
  color: var(--color-text-primary, #1f2937);
  margin-bottom: 2px;
}

.advice-desc {
  font-size: 10px;
  line-height: 1.5;
  color: var(--color-text-secondary, #4b5563);
}
</style>
