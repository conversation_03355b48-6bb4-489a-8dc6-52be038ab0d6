<!-- 五行比例显示组件 -->
<script setup lang="ts">
import type { WuxingRatio } from '@/utils/wuxing'
import { computed } from 'vue'
import { getWuxingIcon, getWuxingPercentage, WUXING_ELEMENTS } from '@/utils/wuxing'

// 组件Props
interface Props {
  /** 五行比例数据 */
  ratio: WuxingRatio
  /** 显示模式：icon=图标模式，text=文字模式 */
  mode?: 'icon' | 'text'
  /** 布局方向 */
  direction?: 'horizontal' | 'vertical'
  /** 图标大小 */
  iconSize?: number
  /** 文字大小 */
  fontSize?: number
}

const props = withDefaults(defineProps<Props>(), {
  mode: 'icon',
  direction: 'vertical',
  iconSize: 12,
  fontSize: 10,
})

// 五行数据列表
const wuxingList = computed(() => {
  return WUXING_ELEMENTS.map(element => ({
    element,
    count: props.ratio[element],
    percentage: getWuxingPercentage(props.ratio, element),
    icon: getWuxingIcon(element),
  }))
})

// 动态样式
const containerClass = computed(() => {
  return `wuxing-ratio wuxing-ratio--${props.direction}`
})

const iconStyle = computed(() => ({
  fontSize: `${props.iconSize}px`,
  width: `${props.iconSize}px`,
  height: `${props.iconSize}px`,
}))

const textStyle = computed(() => ({
  fontSize: `${props.fontSize}px`,
}))
</script>

<template>
  <view :class="containerClass">
    <view
      v-for="item in wuxingList"
      :key="item.element"
      class="ratio-item"
    >
      <!-- 图标模式 -->
      <template v-if="mode === 'icon'">
        <text
          class="iconfont ratio-icon"
          :class="item.icon"
          :style="iconStyle"
        />
      </template>

      <!-- 文字模式 -->
      <template v-else>
        <view class="ratio-dot" />
      </template>

      <text class="ratio-text" :style="textStyle">
        {{ item.element }}{{ item.percentage }}%
      </text>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.wuxing-ratio {
  display: flex;
  gap: 4px;

  &--vertical {
    flex-direction: column;
  }

  &--horizontal {
    flex-direction: row;
    flex-wrap: wrap;
  }
}

.ratio-item {
  display: flex;
  align-items: center;
  gap: 6px;
}

.ratio-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.ratio-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--color-text-secondary, #999);
}

.ratio-text {
  color: var(--color-text-secondary, #666);
}
</style>
