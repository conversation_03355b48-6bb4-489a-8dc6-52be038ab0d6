<!-- 扑克牌组件 - 今日推荐搭配 -->
<script setup lang="ts">
import { onMounted, ref } from 'vue'

// Props 定义
interface Props {
  /** 搭配数据池 */
  dataPool?: Array<{
    id: number
    title: string
    description: string
    image: string
    rating: number
    likes: number
    favorites: number
    liked: boolean
    favorited: boolean
  }>
}

const props = withDefaults(defineProps<Props>(), {
  dataPool: () => [
    {
      id: 1,
      title: '清新白裙搭配',
      description: '金属性白色提升今日财运，木质配饰平衡整体五行',
      image: 'https://images.unsplash.com/photo-1483985988355-763728e1935b',
      rating: 4.5,
      likes: 12,
      favorites: 8,
      liked: false,
      favorited: false,
    },
    {
      id: 2,
      title: '优雅蓝色系搭配',
      description: '水属性蓝色增强智慧，金质饰品助力人际关系',
      image: 'https://images.unsplash.com/photo-1485230895905-ec40ba36b9bc',
      rating: 4.7,
      likes: 28,
      favorites: 15,
      liked: false,
      favorited: false,
    },
    {
      id: 3,
      title: '活力橙色系搭配',
      description: '火属性橙色激发创造力，土系配饰稳定整体气场',
      image: 'https://images.unsplash.com/photo-1475180098004-ca77a66827be',
      rating: 4.3,
      likes: 19,
      favorites: 11,
      liked: false,
      favorited: false,
    },
  ],
})

// Events 定义
const emit = defineEmits<{
  /** 点击卡片时触发 */
  cardTap: [outfit: any]
  /** 点击喜欢按钮时触发 */
  like: [outfit: any]
  /** 点击收藏按钮时触发 */
  favorite: [outfit: any]
  /** 点击分享按钮时触发 */
  share: [outfit: any]
}>()

// 当前显示的搭配数据（保持3张卡片循环显示）
const outfitSlides = ref([...props.dataPool.slice(0, 3)])
const nextCardIndex = ref(0) // 下一张要添加的卡片索引（从0开始循环3个数据）

// 触摸滑动相关
const touchStartX = ref(0)
const touchEndX = ref(0)
const touchStartY = ref(0)
const touchEndY = ref(0)
const touchedCardIndex = ref(-1) // 记录当前触摸的卡片索引
const swipeDirection = ref<'horizontal' | 'vertical' | 'unknown'>('unknown') // 滑动方向判断

// 滑动提示相关
const showSwipeIndicator = ref(false)
const hasUserSwiped = ref(false)

// 飞出动画相关
const flyingCardId = ref<number | null>(null)
const flyDirection = ref<'left' | 'right' | null>(null)
const isAnimating = ref(false)
const touchStartTime = ref(0)
const isTouching = ref(false)

onMounted(() => {
  // 3秒后显示滑动提示
  setTimeout(() => {
    if (!hasUserSwiped.value) {
      showSwipeIndicator.value = true
      // 5秒后自动隐藏提示
      setTimeout(() => {
        showSwipeIndicator.value = false
      }, 5000)
    }
  }, 3000)
})

// 单张卡片触摸事件处理
function handleCardTouchStart(e: any, cardIndex: number) {
  // 只有顶层卡片（index === 0）才响应触摸
  if (cardIndex !== 0 || isAnimating.value)
    return

  touchStartX.value = e.touches[0].clientX
  touchStartY.value = e.touches[0].clientY
  touchStartTime.value = Date.now()
  isTouching.value = true
  touchedCardIndex.value = cardIndex
  swipeDirection.value = 'unknown' // 重置滑动方向判断

  // 卡片触摸开始记录
}

// 触摸移动事件处理 - 实时判断滑动方向
function handleCardTouchMove(e: any, cardIndex: number) {
  // 只有顶层卡片（index === 0）才响应触摸
  if (cardIndex !== 0 || isAnimating.value || !isTouching.value)
    return

  const currentX = e.touches[0].clientX
  const currentY = e.touches[0].clientY
  const diffX = Math.abs(currentX - touchStartX.value)
  const diffY = Math.abs(currentY - touchStartY.value)

  // 当移动距离足够时，判断主要滑动方向
  if (diffX > 8 || diffY > 8) {
    if (swipeDirection.value === 'unknown') {
      // 优化滑动判断：放宽垂直滑动条件，严格限制水平滑动
      const swipeAngle = Math.abs(Math.atan2(diffY, diffX) * 180 / Math.PI)

      if (diffY > 15 && (swipeAngle >= 60 && swipeAngle <= 120)) {
        // 垂直滑动：距离>15px，角度在±30度内
        swipeDirection.value = 'vertical'
        // 判断为垂直滑动，允许页面滚动
        // 不阻止默认行为，让页面正常滚动
      }
      else if (diffX > 15 && (swipeAngle <= 30 || swipeAngle >= 150)) {
        // 水平滑动：距离>15px，角度接近水平（±30度内）
        swipeDirection.value = 'horizontal'
        // 判断为水平滑动，阻止页面滚动
        // 只有水平滑动时才阻止默认行为
        e.preventDefault()
        e.stopPropagation()
      }
      // 未确定方向时不阻止默认行为，让系统决定
    }
    else if (swipeDirection.value === 'horizontal') {
      // 已确定为水平滑动，继续阻止默认行为
      e.preventDefault()
      e.stopPropagation()
    }
    // 垂直滑动或未确定方向：不阻止默认行为
  }
}

function handleCardTouchEnd(e: any, cardIndex: number) {
  // 只有顶层卡片（index === 0）才响应触摸
  if (cardIndex !== 0 || isAnimating.value || !isTouching.value)
    return

  touchEndX.value = e.changedTouches[0].clientX
  touchEndY.value = e.changedTouches[0].clientY
  isTouching.value = false

  const touchDuration = Date.now() - touchStartTime.value
  // 卡片触摸结束记录

  // 只在水平滑动时处理卡片交互
  if (touchDuration > 50 && touchDuration < 2000 && swipeDirection.value === 'horizontal') {
    handleCardInteraction(cardIndex)
  }
  else if (swipeDirection.value === 'vertical') {
    // 垂直滑动，忽略卡片交互
  }
  else if (swipeDirection.value === 'unknown') {
    // 未确定方向的短触摸，视为点击
    // 未确定方向的短触摸，视为点击
    emit('cardTap', outfitSlides.value[0])
  }
  else {
    // 触摸时间不合理，忽略交互
  }

  // 重置触摸状态
  touchedCardIndex.value = -1
  swipeDirection.value = 'unknown'
}

// 卡片点击事件处理
function handleCardTap(e: any, cardIndex: number) {
  // 只有顶层卡片（index === 0）才响应点击
  if (cardIndex !== 0 || isAnimating.value)
    return

  // 顶层卡片点击进入详情
  emit('cardTap', outfitSlides.value[0])
}

// 卡片交互处理逻辑
function handleCardInteraction(cardIndex: number) {
  // 只有顶层卡片（index === 0）才处理交互
  if (cardIndex !== 0 || isAnimating.value)
    return

  const diffX = touchStartX.value - touchEndX.value
  const distanceX = Math.abs(diffX)

  // 水平滑动交互记录

  // 由于已经在touchmove中确定为水平滑动，直接执行飞出逻辑
  if (distanceX >= 10 && outfitSlides.value.length === 3) {
    // 执行卡片飞出
    handleTopCardFlyAway(diffX)
  }
  else {
    // 水平滑动距离不足，忽略
  }
}

// 顶部卡片飞出逻辑
function handleTopCardFlyAway(diffX: number) {
  // 获取当前活跃卡片信息（总是第0个）
  const currentCard = outfitSlides.value[0]
  if (!currentCard) {
    console.error('错误：找不到当前活跃卡片')
    return
  }

  // 即将飞出的卡片记录

  // 用户已经滑动过，记录状态并隐藏提示
  hasUserSwiped.value = true
  showSwipeIndicator.value = false

  // 设置飞出状态
  isAnimating.value = true
  flyingCardId.value = currentCard.id

  if (diffX > 0) {
    flyDirection.value = 'left'
    // 设置飞出方向: 左
  }
  else {
    flyDirection.value = 'right'
    // 设置飞出方向: 右
  }

  // 立即添加新卡片到底部，让补位动画与飞出动画同时进行
  addNewCardToBottom()

  // 使用 setTimeout 确保飞出动画已经应用（微信小程序不支持 requestAnimationFrame）
  setTimeout(() => {
    // 等待CSS动画完成后再移除飞出的卡片
    setTimeout(() => {
      // 动画完成，移除飞出的卡片
      removeFlyingCard()
      resetFlyingState()
    }, 300) // 缩短延迟时间，让动画更连贯（CSS动画时长是0.4s）
  }, 10) // 减少初始延迟
}

// 添加新卡片到底部
function addNewCardToBottom() {
  // 添加新卡片到底部

  // 直接从3个数据中循环选择下一个
  const poolIndex = nextCardIndex.value % props.dataPool.length
  const newCard = { ...props.dataPool[poolIndex] }

  // 更新下一个卡片索引
  nextCardIndex.value = (nextCardIndex.value + 1) % props.dataPool.length

  // 立即添加到末尾，现在有4张卡片
  outfitSlides.value.push(newCard)
}

// 移除飞出的卡片
function removeFlyingCard() {
  // 移除飞出的卡片

  // 找到并移除飞出的卡片
  const flyingIndex = outfitSlides.value.findIndex(card => card.id === flyingCardId.value)

  if (flyingIndex !== -1) {
    outfitSlides.value.splice(flyingIndex, 1)
    // 由于总是移除第0个（活跃卡片），不需要调整索引
  }
}

function handleTouchCancel() {
  // 触摸被取消
  isTouching.value = false
  swipeDirection.value = 'unknown'
}

function resetFlyingState() {
  // 重置飞出状态
  flyingCardId.value = null
  flyDirection.value = null
  isAnimating.value = false
}

// 交互事件处理
function handleLike(item: any, event: any) {
  event.stopPropagation()
  if (item.liked) {
    item.likes--
    item.liked = false
  }
  else {
    item.likes++
    item.liked = true
  }
  emit('like', item)
}

function handleFavorite(item: any, event: any) {
  event.stopPropagation()
  if (item.favorited) {
    item.favorites--
    item.favorited = false
  }
  else {
    item.favorites++
    item.favorited = true
  }
  emit('favorite', item)
}

function handleShare(event: any) {
  event.stopPropagation()
  emit('share', outfitSlides.value[0])
}

// 获取卡片进度显示
function getCardProgress(outfit: any, index: number) {
  if (index !== 0 || flyingCardId.value === outfit.id) {
    return '' // 只有顶层卡片显示进度
  }

  // 找到当前卡片在原始数据池中的位置
  const poolIndex = props.dataPool.findIndex(item => item.id === outfit.id)
  if (poolIndex === -1) {
    return '1/3' // 如果找不到，默认显示1/3
  }

  // 计算在3张循环中的位置 (1/3, 2/3, 3/3)
  const position = (poolIndex % 3) + 1
  return `${position}/3`
}

// 获取卡片CSS类名
function getCardClasses(outfit: any, index: number) {
  const classes: Record<string, boolean> = {}

  // 飞出动画状态
  if (flyingCardId.value === outfit.id) {
    classes['flying-left'] = flyDirection.value === 'left'
    classes['flying-right'] = flyDirection.value === 'right'
    return classes
  }

  // 正常状态：基于当前数组中的实际位置
  if (index === 0) {
    classes.active = true
  }
  else if (index === 1) {
    classes.second = true
  }
  else if (index === 2) {
    classes.third = true
  }
  else if (index >= 3) {
    classes.hidden = true
  }

  return classes
}
</script>

<template>
  <view class="outfit-cards-wrapper">
    <!-- 扑克牌效果搭配区域 -->
    <view class="outfit-cards-container">
      <!-- 滑动提示 -->
      <view v-if="showSwipeIndicator" class="swipe-indicator">
        <text class="swipe-text">
          ← 左右滑动切换 →
        </text>
      </view>

      <!-- 卡片堆叠 -->
      <view
        v-for="(outfit, index) in outfitSlides"
        :key="outfit.id"
        class="outfit-card"
        :class="getCardClasses(outfit, index)"
        @touchstart="(e) => handleCardTouchStart(e, index)"
        @touchmove="(e) => handleCardTouchMove(e, index)"
        @touchend="(e) => handleCardTouchEnd(e, index)"
        @touchcancel="handleTouchCancel"
        @tap="(e) => handleCardTap(e, index)"
      >
        <image class="outfit-image" :src="outfit.image" mode="aspectFill" />
        <view v-if="getCardProgress(outfit, index)" class="slide-indicator">
          <text class="indicator-text">
            {{ getCardProgress(outfit, index) }}
          </text>
        </view>
      </view>
    </view>

    <!-- 搭配信息区域 -->
    <view class="outfit-info">
      <view class="outfit-header">
        <text class="outfit-title">
          {{ outfitSlides[0]?.title }}
        </text>
        <view class="outfit-actions">
          <view class="action-button" @tap="handleLike(outfitSlides[0], $event)">
            <view class="icon-svg-container">
              <text v-if="!outfitSlides[0]?.liked" :key="`like-normal-${outfitSlides[0]?.id}`" class="iconfont action-icon icon-aixin like-normal" />
              <text v-else :key="`like-filled-${outfitSlides[0]?.id}`" class="iconfont action-icon icon-aixin_shixin like-filled" />
            </view>
            <text class="action-count">
              {{ outfitSlides[0]?.likes }}
            </text>
          </view>
          <view class="action-button" @tap="handleFavorite(outfitSlides[0], $event)">
            <view class="icon-svg-container">
              <text v-if="!outfitSlides[0]?.favorited" :key="`favorite-normal-${outfitSlides[0]?.id}`" class="iconfont action-icon icon-shoucang favorite-normal" />
              <text v-else :key="`favorite-filled-${outfitSlides[0]?.id}`" class="iconfont action-icon icon-shoucang1 favorite-filled" />
            </view>
            <text class="action-count">
              {{ outfitSlides[0]?.favorites }}
            </text>
          </view>
          <view class="action-button" @tap="handleShare">
            <text class="iconfont action-icon icon-fenxiang share-icon" />
            <text class="action-count">
              分享
            </text>
          </view>
        </view>
      </view>
      <text class="outfit-description">
        <text class="iconfont info-icon icon-xinxi" />
        {{ outfitSlides[0]?.description }}
      </text>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.outfit-cards-wrapper {
  width: 100%;
}

// 扑克牌效果区域
.outfit-cards-container {
  position: relative;
  height: 320px;
  margin: 0 auto 16px;
  perspective: 1500px;
  transform-style: preserve-3d;
  overflow: visible;
}

// 滑动提示
.swipe-indicator {
  position: absolute;
  top: 10px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 100;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 20px;
  padding: 4px 12px;
  backdrop-filter: blur(4px);
  animation: swipeIndicatorFadeIn 0.5s ease-out;
}

.swipe-text {
  font-size: 10px;
  color: white;
  opacity: 0.9;
}

@keyframes swipeIndicatorFadeIn {
  0% {
    opacity: 0;
    transform: translateX(-50%) translateY(-10px);
  }
  100% {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

.outfit-card {
  position: absolute;
  width: 85%;
  height: 100%;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.6s cubic-bezier(0.4, 0.0, 0.2, 1);
  backface-visibility: hidden;
  transform-style: preserve-3d;
  cursor: pointer;
  will-change: transform, opacity, filter;

  &.active {
    z-index: 40;
    left: 3%;
    transform: rotate(1deg) scale(1);
    opacity: 1;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.25);
    filter: brightness(1);
  }

  &.second {
    z-index: 30;
    left: 7%;
    transform: rotate(6deg) translateX(20px) translateY(25px) scale(0.95);
    opacity: 0.8;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
    filter: brightness(0.85);
    pointer-events: none; // 禁用底层卡片的所有事件
  }

  &.third {
    z-index: 20;
    left: 11%;
    transform: rotate(12deg) translateX(40px) translateY(50px) scale(0.9);
    opacity: 0.6;
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.12);
    filter: brightness(0.7);
    pointer-events: none; // 禁用底层卡片的所有事件
  }

  &.hidden {
    z-index: 10;
    left: 15%;
    transform: rotate(18deg) translateX(60px) translateY(75px) scale(0.85);
    opacity: 0;
    visibility: hidden;
    box-shadow: none;
    pointer-events: none; // 禁用底层卡片的所有事件
  }

  // 飞出动画状态
  &.flying-left {
    z-index: 50;
    transform: translateX(-120%) rotate(-15deg) scale(0.8) !important;
    opacity: 0 !important;
    transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                opacity 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
  }

  &.flying-right {
    z-index: 50;
    transform: translateX(120%) rotate(15deg) scale(0.8) !important;
    opacity: 0 !important;
    transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                opacity 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
  }

  &:hover {
    transform: scale(1.01) translateZ(30px);
  }

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: inherit;
    pointer-events: none;
    box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.2);
  }
}

.outfit-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.slide-indicator {
  position: absolute;
  top: 8px;
  left: 8px;
  padding: 4px 8px;
  border-radius: 50px;
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(4px);
}

.indicator-text {
  font-size: 10px;
  color: white;
}

// 搭配信息区域
.outfit-info {
  padding: 8px 4px 4px;
  margin-top: 8px;
  position: relative;
  z-index: 35;
}

.outfit-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.outfit-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--color-text-primary, #1f2937);
}

.outfit-actions {
  display: flex;
  gap: 8px;
}

.action-button {
  display: flex;
  align-items: center;
  padding: 2px 8px;
  border-radius: 50px;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.7);
  min-width: 52px;
  justify-content: center;
  cursor: pointer;
  transition: background 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  &:active {
    background: rgba(255, 255, 255, 0.9);
  }
}

.action-icon {
  font-size: 12px;
  margin-right: 4px;
}

.action-count {
  font-size: 12px;
  color: var(--color-text-primary, #1f2937);
}

.outfit-description {
  font-size: 10px;
  color: var(--color-text-secondary, #4b5563);
  line-height: 1.4;
}

.info-icon {
  margin-right: 4px;
}

/* 彩色图标样式 */
.icon-svg-container {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

// 点赞图标颜色和动效
.like-normal {
  color: #ff6b6b !important; // 未点赞时的线型红色
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  transform-origin: center;
}

.like-filled {
  color: #ff6b6b !important; // 已点赞时的实心红色
  animation: likeAnimation 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  transform-origin: center;
}

// 收藏图标颜色和动效
.favorite-normal {
  color: #ffc107 !important; // 未收藏时的线型黄色
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  transform-origin: center;
}

.favorite-filled {
  color: #ffc107 !important; // 已收藏时的实心黄色
  animation: favoriteAnimation 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  transform-origin: center;
}

// 分享图标颜色
.share-icon {
  color: #8b5cf6 !important; // 分享图标始终显示紫色
  transition: all 0.2s ease;
}

// 点赞动画
@keyframes likeAnimation {
  0% {
    transform: scale(1);
  }
  15% {
    transform: scale(1.2);
  }
  30% {
    transform: scale(0.95);
  }
  45% {
    transform: scale(1.1);
  }
  60% {
    transform: scale(0.98);
  }
  100% {
    transform: scale(1);
  }
}

// 收藏动画
@keyframes favoriteAnimation {
  0% {
    transform: scale(1) rotate(0deg);
  }
  25% {
    transform: scale(1.1) rotate(-5deg);
  }
  50% {
    transform: scale(1.2) rotate(0deg);
  }
  75% {
    transform: scale(1.1) rotate(5deg);
  }
  100% {
    transform: scale(1) rotate(0deg);
  }
}

/* CSS变量定义 */
:root {
  --color-text-primary: #1f2937;
  --color-text-secondary: #4b5563;
  --color-text-tertiary: #9ca3af;
}
</style>
