/**
 * 环境配置
 */

export type Environment = 'development' | 'test' | 'production'

export interface EnvConfig {
  name: string
  apiBaseUrl: string
  gatewayUrl: string
}

// 环境配置映射
export const ENV_CONFIG: Record<Environment, EnvConfig> = {
  development: {
    name: '开发环境',
    apiBaseUrl: 'http://192.168.8.46:8080',
    gatewayUrl: 'http://192.168.8.46:8080', // API网关地址
    // apiBaseUrl: 'http://119.29.147.70:8080',
    // gatewayUrl: 'http://119.29.147.70:8080', // API网关地址
  },
  test: {
    name: '测试环境',
    apiBaseUrl: 'https://miniapp.daibeibei.com',
    gatewayUrl: 'https://miniapp.daibeibei.com',
  },
  production: {
    name: '正式环境',
    apiBaseUrl: 'https://api.stylishlink.com',
    gatewayUrl: 'https://api.stylishlink.com',
  },
}

/**
 * 获取当前环境
 * 支持动态环境切换：优先从本地存储获取，避免循环依赖
 */
export function getCurrentEnv(): Environment {
  try {
    // 直接从本地存储获取环境，避免循环依赖
    const savedEnv = uni.getStorageSync('current_environment') as Environment
    if (savedEnv && ENV_CONFIG[savedEnv]) {
      return savedEnv
    }
  }
  catch (error) {
    console.warn('从本地存储获取环境失败:', error)
  }

  // 降级到默认开发环境
  return 'test'
}

/**
 * 获取当前环境配置
 */
export function getCurrentEnvConfig(): EnvConfig {
  return ENV_CONFIG[getCurrentEnv()]
}

/**
 * 获取API基础URL
 */
export function getApiBaseUrl(): string {
  return getCurrentEnvConfig().apiBaseUrl
}

/**
 * 获取网关URL
 */
export function getGatewayUrl(): string {
  return getCurrentEnvConfig().gatewayUrl
}

/**
 * 构建完整的API URL
 */
export function buildApiUrl(path: string): string {
  const baseUrl = getApiBaseUrl()

  // 确保path以/开头
  const cleanPath = path.startsWith('/') ? path : `/${path}`

  // 移除baseUrl末尾的/，避免双重斜杠
  const cleanBaseUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl

  return `${cleanBaseUrl}${cleanPath}`
}
