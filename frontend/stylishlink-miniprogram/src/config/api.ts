/**
 * API端点配置
 * 定义各个微服务的API路径
 */

/**
 * 用户服务API端点
 */
export const USER_API = {
  // 发送验证码
  SEND_CODE: '/api/user/send-code',

  // 用户登录（验证码登录）
  LOGIN: '/api/user/login',

  // 完善用户信息
  COMPLETE_PROFILE: '/api/user/complete-profile',

  // 获取用户信息
  GET_USER_INFO: '/api/user/info',

  // 更新用户信息
  UPDATE_USER_INFO: '/api/user/info',

  // 用户登出
  LOGOUT: '/api/user/logout',

  // 刷新Token
  REFRESH_TOKEN: '/api/user/refresh-token',

  // 获取今日能量信息
  TODAY_ENERGY: '/api/user/today-energy',

  // 获取能量历史趋势
  ENERGY_TREND: '/api/user/energy-trend',

  // 获取今日能量简要信息
  TODAY_ENERGY_BRIEF: '/api/user/today-energy-brief',

  // 获取用户完整运势解读
  COMPLETE_FORTUNE_READING: '/api/user/fortune/complete-reading',
} as const

/**
 * 衣橱服务API端点
 */
export const WARDROBE_API = {
  // 衣物管理
  GET_CLOTHES_LIST: '/api/wardrobe/clothes',
  ADD_CLOTHES: '/api/wardrobe/clothes',
  UPDATE_CLOTHES: '/api/wardrobe/clothes',
  DELETE_CLOTHES: '/api/wardrobe/clothes',

  // 分类管理
  GET_CATEGORIES: '/api/wardrobe/categories',
} as const

/**
 * 推荐服务API端点
 */
export const RECOMMENDATION_API = {
  // 获取穿搭推荐
  GET_RECOMMENDATIONS: '/api/recommendation/outfits',

  // 基于天气的推荐
  GET_WEATHER_RECOMMENDATIONS: '/api/recommendation/weather',

  // 基于五行的推荐
  GET_WUXING_RECOMMENDATIONS: '/api/recommendation/wuxing',
} as const

/**
 * AI服务API端点
 */
export const AI_API = {
  // 图片识别
  RECOGNIZE_IMAGE: '/api/ai/recognize',

  // 虚拟试衣
  VIRTUAL_TRY_ON: '/api/ai/try-on',

  // 色彩分析
  COLOR_ANALYSIS: '/api/ai/color-analysis',
} as const

/**
 * 社交服务API端点
 */
export const SOCIAL_API = {
  // 获取动态
  GET_POSTS: '/api/social/posts',

  // 发布动态
  CREATE_POST: '/api/social/posts',

  // 点赞
  LIKE_POST: '/api/social/posts/like',

  // 评论
  COMMENT_POST: '/api/social/posts/comment',
} as const

/**
 * 运营服务API端点
 */
export const OPERATION_API = {
  // 灵感值
  GET_INSPIRATION: '/api/operation/inspiration',

  // 任务
  GET_TASKS: '/api/operation/tasks',

  // 成就
  GET_ACHIEVEMENTS: '/api/operation/achievements',
} as const

/**
 * 支付服务API端点
 */
export const PAYMENT_API = {
  // 会员套餐
  GET_PLANS: '/api/payment/plans',

  // 创建订单
  CREATE_ORDER: '/api/payment/orders',

  // 支付
  PAY: '/api/payment/pay',
} as const

/**
 * 文件服务API端点
 */
export const FILE_API = {
  // 上传文件
  UPLOAD: '/api/file/upload',

  // 删除文件
  DELETE: '/api/file/delete',
} as const

/**
 * 所有API端点的集合
 */
export const API_ENDPOINTS = {
  USER: USER_API,
  WARDROBE: WARDROBE_API,
  RECOMMENDATION: RECOMMENDATION_API,
  AI: AI_API,
  SOCIAL: SOCIAL_API,
  OPERATION: OPERATION_API,
  PAYMENT: PAYMENT_API,
  FILE: FILE_API,
} as const
