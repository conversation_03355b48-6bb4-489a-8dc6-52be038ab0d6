/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by vite-plugin-uni-pages

interface NavigateToOptions {
  url: "/pages/index/index" |
       "/pages/fortune/index" |
       "/pages/login/index" |
       "/pages/outfit/detail" |
       "/pages/outfit/index" |
       "/pages/profile/index" |
       "/pages/test/index" |
       "/pages/user-info/index" |
       "/pages/wardrobe/index" |
       "/pages/fortune/complete/index" |
       "/pages/fortune/components/EnergyDimensionChart" |
       "/pages/fortune/components/EnergyScoreDisplay" |
       "/pages/fortune/components/LuckyElements" |
       "/pages/fortune/components/TodayAdvice" |
       "/pages/wardrobe/add-clothing/index" |
       "/pages/fortune/complete/components/BaziCombination" |
       "/pages/fortune/complete/components/DetailedFortune" |
       "/pages/fortune/complete/components/LuckyAdvice" |
       "/pages/fortune/complete/components/OverallFortune";
}
interface RedirectToOptions extends NavigateToOptions {}

interface SwitchTabOptions {
  url: "/pages/index/index" | "/pages/wardrobe/index" | "/pages/outfit/index" | "/pages/profile/index"
}

type ReLaunchOptions = NavigateToOptions | SwitchTabOptions;

declare interface Uni {
  navigateTo(options: UniNamespace.NavigateToOptions & NavigateToOptions): void;
  redirectTo(options: UniNamespace.RedirectToOptions & RedirectToOptions): void;
  switchTab(options: UniNamespace.SwitchTabOptions & SwitchTabOptions): void;
  reLaunch(options: UniNamespace.ReLaunchOptions & ReLaunchOptions): void;
}
