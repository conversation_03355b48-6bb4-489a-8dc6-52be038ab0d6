// 用户mock数据
import { http } from 'msw'

export const userHandlers = [
  http.get('/api/user/profile', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({
        id: 'u123',
        nickname: '测试用户',
        avatar: '/static/avatar.png',
        gender: 'female',
        birthday: '2000-01-01',
        fiveElements: '木',
        inspiration: 88,
      }),
    )
  }),
  http.post('/api/user/login', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({ token: 'mock-token-123', userId: 'u123' }),
    )
  }),
]
