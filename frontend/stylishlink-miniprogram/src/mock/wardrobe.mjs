// 衣橱mock数据
import { http } from 'msw'

export const wardrobeHandlers = [
  http.get('/api/wardrobe/list', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({
        items: [
          { id: 'c1', name: '白色T恤', category: '上衣', color: '白', image: '/static/cloth1.png' },
          { id: 'c2', name: '牛仔裤', category: '裤子', color: '蓝', image: '/static/cloth2.png' },
        ],
      }),
    )
  }),
  http.post('/api/wardrobe/add', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({ success: true, id: 'c3' }),
    )
  }),
]
