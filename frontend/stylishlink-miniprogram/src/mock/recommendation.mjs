// 推荐mock数据
import { http } from 'msw'

export const recommendationHandlers = [
  http.get('/api/recommendation/list', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({
        recommendations: [
          {
            id: 'r1',
            title: '今日五行穿搭',
            description: '根据五行命理和天气为你推荐',
            items: [
              { id: 'c1', name: '白色T恤', image: '/static/cloth1.png' },
              { id: 'c2', name: '牛仔裤', image: '/static/cloth2.png' },
            ],
            fiveElements: '木',
            weather: '晴',
            inspiration: 95,
          },
        ],
      }),
    )
  }),
]
