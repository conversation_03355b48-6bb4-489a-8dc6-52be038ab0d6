/**
 * 环境状态管理 Store
 * 支持在测试阶段动态切换开发环境和测试环境
 */

import type { EnvConfig, Environment } from '@/config/env'
import { defineStore } from 'pinia'
import { computed, ref } from 'vue'
import { ENV_CONFIG } from '@/config/env'

export const useEnvStore = defineStore('env', () => {
  // 当前环境状态
  const currentEnv = ref<Environment>('development')

  // 初始化环境
  const initEnvironment = () => {
    try {
      // 尝试从本地存储获取之前保存的环境
      const savedEnv = uni.getStorageSync('current_environment') as Environment
      console.warn('环境Store初始化 - 从存储获取环境:', savedEnv)

      if (savedEnv && ENV_CONFIG[savedEnv]) {
        currentEnv.value = savedEnv
        console.warn('环境Store初始化 - 使用存储的环境:', savedEnv)
      }
      else {
        // 默认使用开发环境
        currentEnv.value = 'development'
        console.warn('环境Store初始化 - 使用默认环境: development')
      }
    }
    catch (error) {
      console.error('环境Store初始化失败:', error)
      currentEnv.value = 'development'
    }
  }

  // 计算属性：当前环境配置
  const currentEnvConfig = computed<EnvConfig>(() => {
    return ENV_CONFIG[currentEnv.value]
  })

  // 计算属性：当前API基础URL
  const currentApiBaseUrl = computed(() => {
    return currentEnvConfig.value.apiBaseUrl
  })

  // 计算属性：当前网关URL
  const currentGatewayUrl = computed(() => {
    return currentEnvConfig.value.gatewayUrl
  })

  // 计算属性：是否为开发环境
  const isDevelopment = computed(() => {
    return currentEnv.value === 'development'
  })

  // 计算属性：是否为测试环境
  const isTest = computed(() => {
    return currentEnv.value === 'test'
  })

  // 计算属性：是否为生产环境
  const isProduction = computed(() => {
    return currentEnv.value === 'production'
  })

  // 切换环境
  const switchEnvironment = (newEnv: Environment) => {
    console.warn('环境切换请求:', { from: currentEnv.value, to: newEnv })

    if (newEnv === currentEnv.value) {
      console.warn('环境未变化:', newEnv)
      uni.showToast({
        title: '当前已是该环境',
        icon: 'none',
        duration: 1500,
      })
      return
    }

    try {
      console.warn('开始切换环境:', currentEnv.value, '->', newEnv)

      const oldEnv = currentEnv.value
      currentEnv.value = newEnv

      // 保存到本地存储
      uni.setStorageSync('current_environment', newEnv)
      console.warn('环境已保存到本地存储:', newEnv)

      // 验证保存是否成功
      const savedCheck = uni.getStorageSync('current_environment')
      console.warn('验证保存结果:', savedCheck)

      // 显示切换成功提示
      uni.showToast({
        title: `已切换到${ENV_CONFIG[newEnv].name}`,
        icon: 'success',
        duration: 2000,
      })

      // 打印环境信息
      console.warn('环境切换成功，当前环境信息:', {
        environment: newEnv,
        name: ENV_CONFIG[newEnv].name,
        apiBaseUrl: ENV_CONFIG[newEnv].apiBaseUrl,
        gatewayUrl: ENV_CONFIG[newEnv].gatewayUrl,
        oldEnv,
      })
    }
    catch (error) {
      console.error('环境切换失败:', error)

      uni.showToast({
        title: '环境切换失败',
        icon: 'error',
        duration: 2000,
      })
    }
  }

  // 获取环境列表（用于测试阶段切换）
  const getTestableEnvironments = (): Array<{ key: Environment, config: EnvConfig }> => {
    return [
      { key: 'development', config: ENV_CONFIG.development },
      { key: 'test', config: ENV_CONFIG.test },
    ]
  }

  // 重置为默认环境
  const resetToDefault = () => {
    console.warn('重置环境为默认值')
    switchEnvironment('development')
  }

  // 获取环境描述信息
  const getEnvironmentInfo = () => {
    const info = {
      current: currentEnv.value,
      name: currentEnvConfig.value.name,
      apiBaseUrl: currentEnvConfig.value.apiBaseUrl,
      gatewayUrl: currentEnvConfig.value.gatewayUrl,
      isDevelopment: isDevelopment.value,
      isTest: isTest.value,
      isProduction: isProduction.value,
    }
    console.warn('获取环境信息:', info)
    return info
  }

  // 手动触发环境检查（用于调试）
  const checkEnvironmentStatus = () => {
    try {
      const storageEnv = uni.getStorageSync('current_environment')
      console.warn('环境状态检查:', {
        storeEnv: currentEnv.value,
        storageEnv,
        config: currentEnvConfig.value,
      })
      return {
        storeEnv: currentEnv.value,
        storageEnv,
        config: currentEnvConfig.value,
      }
    }
    catch (error) {
      console.error('环境状态检查失败:', error)
      return null
    }
  }

  // 初始化环境（应用启动时调用）
  initEnvironment()

  return {
    // 状态
    currentEnv: computed(() => currentEnv.value),
    currentEnvConfig,
    currentApiBaseUrl,
    currentGatewayUrl,

    // 计算属性
    isDevelopment,
    isTest,
    isProduction,

    // 方法
    switchEnvironment,
    getTestableEnvironments,
    resetToDefault,
    getEnvironmentInfo,
    initEnvironment,
    checkEnvironmentStatus,
  }
})
