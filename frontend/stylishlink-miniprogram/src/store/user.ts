import type {
  LoginRequest,
  StoreLoginResponse as LoginResponse,
  StoreUserInfo as UserInfo,
} from '@/types/user'
import { defineStore } from 'pinia'
import { computed, ref } from 'vue'
import * as userApi from '@/api/user'
import { clearAuth } from '@/utils/request'

export const useUserStore = defineStore('user', () => {
  // 状态
  const token = ref<string>('')
  const userInfo = ref<UserInfo | null>(null)
  const isLoggedIn = ref(false)
  const loading = ref(false)

  // 计算属性
  const isProfileCompleted = computed(() => {
    return userInfo.value?.profileCompleted || false
  })

  const shouldShowEnergySection = computed(() => {
    return isLoggedIn.value && isProfileCompleted.value
  })

  // 初始化用户数据
  const initUserData = () => {
    try {
      const savedToken = uni.getStorageSync('token')
      const savedUserInfo = uni.getStorageSync('userInfo')

      if (savedToken && savedUserInfo) {
        token.value = savedToken
        userInfo.value = savedUserInfo
        isLoggedIn.value = true
      }
    }
    catch (error) {
      console.error('初始化用户数据失败:', error)
    }
  }

  // 登录
  const login = async (loginData: LoginRequest): Promise<LoginResponse> => {
    try {
      loading.value = true

      // 调用真实登录API
      const apiResponse = await userApi.login(loginData)

      // JWT token已在请求工具中自动从响应体提取并保存
      token.value = apiResponse.token || uni.getStorageSync('token') || ''

      // 保存refresh token（如果返回了）
      if (apiResponse.refreshToken) {
        uni.setStorageSync('refreshToken', apiResponse.refreshToken)
      }

      // 转换API响应到store格式
      userInfo.value = {
        userId: apiResponse.userInfo.id,
        nickname: apiResponse.userInfo.nickname || '',
        avatar: apiResponse.userInfo.avatar,
        phone: apiResponse.userInfo.phone,
        gender: apiResponse.userInfo.gender === 'male' ? 1 : apiResponse.userInfo.gender === 'female' ? 0 : undefined,
        profileCompleted: apiResponse.hasBasicInfo && apiResponse.hasWuxingInfo,
        fullName: apiResponse.userInfo.nickname,
        birthDate: apiResponse.userInfo.birthday,
        photoUrl: apiResponse.userInfo.avatar,
      }

      isLoggedIn.value = true

      // 保存用户信息到本地存储
      uni.setStorageSync('userInfo', userInfo.value)

      const message = apiResponse.isNewUser ? '注册成功' : '登录成功'
      uni.showToast({
        title: message,
        icon: 'success',
      })

      // 返回格式化的响应
      return {
        userId: apiResponse.userInfo.id,
        userInfo: userInfo.value,
        hasBasicInfo: apiResponse.hasBasicInfo,
        hasWuxingInfo: apiResponse.hasWuxingInfo,
        isNewUser: apiResponse.isNewUser,
      }
    }
    catch (error: any) {
      uni.showToast({
        title: error.message || '登录失败',
        icon: 'none',
      })
      throw error
    }
    finally {
      loading.value = false
    }
  }

  // 完善用户信息（处理hasBasicInfo和hasWuxingInfo状态）
  const completeProfile = async (profileData: any): Promise<any> => {
    try {
      loading.value = true

      // 调用完善信息API
      const apiResponse = await userApi.completeProfile(profileData)

      // 更新用户信息和状态
      if (userInfo.value && apiResponse.userInfo) {
        userInfo.value = {
          ...userInfo.value,
          userId: apiResponse.userInfo.id,
          nickname: apiResponse.userInfo.nickname || userInfo.value.nickname,
          avatar: apiResponse.userInfo.avatar || userInfo.value.avatar,
          phone: apiResponse.userInfo.phone || userInfo.value.phone,
          gender: apiResponse.userInfo.gender === 'male' ? 1 : apiResponse.userInfo.gender === 'female' ? 0 : userInfo.value.gender,
          profileCompleted: apiResponse.hasBasicInfo && apiResponse.hasWuxingInfo,
          fullName: apiResponse.userInfo.fullName || userInfo.value.fullName,
          birthDate: apiResponse.userInfo.birthDate || userInfo.value.birthDate,
          photoUrl: apiResponse.userInfo.photoUrl || userInfo.value.photoUrl,
        }

        // 更新本地存储
        uni.setStorageSync('userInfo', userInfo.value)
      }

      uni.showToast({
        title: apiResponse.message || '信息更新成功',
        icon: 'success',
      })

      return {
        success: apiResponse.success,
        message: apiResponse.message,
        hasBasicInfo: apiResponse.hasBasicInfo,
        hasWuxingInfo: apiResponse.hasWuxingInfo,
        userInfo: userInfo.value,
      }
    }
    catch (error: any) {
      uni.showToast({
        title: error.message || '更新失败',
        icon: 'none',
      })
      throw error
    }
    finally {
      loading.value = false
    }
  }

  // 更新用户信息
  const updateUserInfo = async (updateData: Partial<UserInfo>): Promise<void> => {
    try {
      loading.value = true

      // 转换store格式到API格式
      const apiUpdateData = {
        nickname: updateData.nickname,
        gender: updateData.gender === 1 ? 'male' : updateData.gender === 0 ? 'female' : undefined,
        avatar: updateData.avatar,
        // 可以根据需要添加更多字段映射
      }

      // 调用真实API
      const apiResponse = await userApi.updateUserInfo(apiUpdateData)

      // 转换API响应到store格式并更新
      const updatedStoreData = {
        nickname: apiResponse.nickname || userInfo.value?.nickname || '',
        avatar: apiResponse.avatar,
        gender: apiResponse.gender === 'male' ? 1 : apiResponse.gender === 'female' ? 0 : userInfo.value?.gender,
        profileCompleted: true, // 更新信息后标记为已完善
      }

      userInfo.value = { ...userInfo.value, ...updatedStoreData }

      // 更新本地存储
      uni.setStorageSync('userInfo', userInfo.value)

      uni.showToast({
        title: '更新成功',
        icon: 'success',
      })
    }
    catch (error: any) {
      uni.showToast({
        title: error.message || '更新失败',
        icon: 'none',
      })
      throw error
    }
    finally {
      loading.value = false
    }
  }

  // 退出登录
  const logout = async () => {
    try {
      loading.value = true

      // 先调用后端API清除服务器端认证信息
      await userApi.logout()
    }
    catch (error) {
      console.error('调用退出登录API失败:', error)
      // 即使API调用失败，也继续执行客户端清理
    }
    finally {
      loading.value = false

      // 清除客户端状态
      token.value = ''
      userInfo.value = null
      isLoggedIn.value = false

      // 清除本地存储
      clearAuth()
      uni.removeStorageSync('userInfo')

      // 返回首页
      uni.switchTab({
        url: '/pages/index/index',
      })

      uni.showToast({
        title: '已退出登录',
        icon: 'success',
      })
    }
  }

  // 检查登录状态
  const checkLoginStatus = (silent = false): Promise<boolean> => {
    return new Promise((resolve) => {
      if (!isLoggedIn.value) {
        if (silent) {
          // 静默模式：直接跳转到登录页，不弹出提示
          uni.navigateTo({
            url: '/pages/login/index',
          })
          resolve(false)
        }
        else {
          // 非静默模式：弹出提示确认后跳转
          uni.showModal({
            title: '提示',
            content: '请先登录',
            showCancel: true,
            confirmText: '去登录',
            success: (res) => {
              if (res.confirm) {
                uni.navigateTo({
                  url: '/pages/login/index',
                })
              }
            },
          })
          resolve(false)
        }
      }
      else {
        resolve(true)
      }
    })
  }

  return {
    // 状态
    token,
    userInfo,
    isLoggedIn,
    loading,

    // 计算属性
    isProfileCompleted,
    shouldShowEnergySection,

    // 方法
    initUserData,
    login,
    completeProfile,
    updateUserInfo,
    logout,
    checkLoginStatus,
  }
})
