import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useAppStore = defineStore('app', () => {
  // 应用初始化状态
  const isAppReady = ref(false)
  const isAppMounted = ref(false)

  // 设置应用就绪状态
  const setAppReady = (ready: boolean) => {
    isAppReady.value = ready
    console.warn(`应用状态更新: ${ready ? '就绪' : '未就绪'}`)
  }

  // 设置应用挂载状态
  const setAppMounted = (mounted: boolean) => {
    isAppMounted.value = mounted
    console.warn(`应用挂载状态: ${mounted ? '已挂载' : '未挂载'}`)
  }

  // 检查应用是否完全初始化
  const checkAppFullyInitialized = (): boolean => {
    try {
      const app = getApp()
      // 检查应用实例和 Vue 实例是否都存在
      const hasApp = !!app
      const hasVm = !!(app && (app as any).$vm)
      const isReady = isAppReady.value && isAppMounted.value

      const fullyInitialized = hasApp && hasVm && isReady

      if (!fullyInitialized) {
        console.warn('应用初始化检查:', {
          hasApp,
          hasVm,
          isReady,
          isAppReady: isAppReady.value,
          isAppMounted: isAppMounted.value,
        })
      }

      return fullyInitialized
    }
    catch (error) {
      console.warn('应用初始化检查失败:', error)
      return false
    }
  }

  // 等待应用完全初始化
  const waitForAppReady = (timeout = 5000): Promise<boolean> => {
    return new Promise((resolve) => {
      if (checkAppFullyInitialized()) {
        resolve(true)
        return
      }

      let attempts = 0
      const maxAttempts = timeout / 100 // 每100ms检查一次

      const checkInterval = setInterval(() => {
        attempts++

        if (checkAppFullyInitialized()) {
          clearInterval(checkInterval)
          resolve(true)
          return
        }

        if (attempts >= maxAttempts) {
          clearInterval(checkInterval)
          console.warn('应用初始化等待超时')
          resolve(false)
        }
      }, 100)
    })
  }

  return {
    isAppReady,
    isAppMounted,
    setAppReady,
    setAppMounted,
    checkAppFullyInitialized,
    waitForAppReady,
  }
})
