import { defineStore } from 'pinia'
import { computed, ref } from 'vue'

export interface TabItem {
  text: string
  icon: string
  activeIcon: string
  path: string
}

export const useTabBarStore = defineStore('tabbar', () => {
  // TabBar 配置
  const tabs = ref<TabItem[]>([
    {
      text: '首页',
      icon: 'icon-shouye-shouye',
      activeIcon: 'icon-shouye-shouye',
      path: '/pages/index/index',
    },
    {
      text: '衣橱',
      icon: 'icon-yigui',
      activeIcon: 'icon-yigui',
      path: '/pages/wardrobe/index',
    },
    {
      text: '搭配',
      icon: 'icon-magic',
      activeIcon: 'icon-magic',
      path: '/pages/outfit/index',
    },
    {
      text: '我的',
      icon: 'icon-wode',
      activeIcon: 'icon-wode',
      path: '/pages/profile/index',
    },
  ])

  // 当前选中的路径
  const currentPath = ref('/pages/index/index')

  // 计算属性 - 当前选中的 Tab
  const activeTab = computed(() => {
    return tabs.value.find(tab => tab.path === currentPath.value)
  })

  // 更新当前路径
  const updateCurrentPath = (path: string) => {
    // TabBar Store: 更新路径
    currentPath.value = path
  }

  // 从页面栈获取当前路径
  const syncCurrentPath = () => {
    try {
      const pages = getCurrentPages()
      if (pages.length > 0) {
        const currentPage = pages[pages.length - 1]
        const route = currentPage.route || (currentPage as any).__route__

        if (route) {
          const fullPath = route.startsWith('/') ? route : `/${route}`

          if (fullPath !== currentPath.value) {
            // TabBar Store: 同步路径
            currentPath.value = fullPath
          }
        }
      }
    }
    catch (error) {
      console.warn('TabBar Store: 同步路径失败', error)
    }
  }

  // 切换 Tab
  const switchTab = (path: string) => {
    if (path === currentPath.value) {
      // TabBar Store: 已在当前页面
      return Promise.resolve()
    }

    // TabBar Store: 切换到目标页面

    // 立即更新状态
    updateCurrentPath(path)

    return new Promise((resolve, reject) => {
      uni.switchTab({
        url: path,
        success: () => {
          // TabBar Store: 切换成功
          // 确保状态同步
          setTimeout(() => {
            syncCurrentPath()
          }, 50)
          resolve(true)
        },
        fail: (err) => {
          console.warn('TabBar Store: 切换失败', err)
          // 恢复状态
          syncCurrentPath()
          reject(err)
        },
      })
    })
  }

  return {
    tabs,
    currentPath,
    activeTab,
    updateCurrentPath,
    syncCurrentPath,
    switchTab,
  }
})
