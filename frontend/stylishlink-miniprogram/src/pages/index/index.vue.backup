<!-- 首页 - StylishLink 时尚搭配助手 -->
<script setup lang="ts">
import { ref, onMounted } from 'vue'
import TheTabBar from '@/components/layout/TheTabBar.vue'

interface FeatureCard {
  title: string
  subtitle: string
  icon: string
  color: string
  route: string
}

// 页面状态
const isLoading = ref(true)

// 功能卡片数据
const featureCards = ref([
  {
    id: 1,
    title: '智能推荐',
    desc: '基于五行命理的个性化穿搭',
    icon: '🎯',
    gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
  },
  {
    id: 2,
    title: '数字衣橱',
    desc: 'AI识别管理你的服饰',
    icon: '👗',
    gradient: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)'
  },
  {
    id: 3,
    title: '真人试衣',
    desc: '虚拟试衣看搭配效果',
    icon: '✨',
    gradient: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)'
  },
  {
    id: 4,
    title: '穿搭社区',
    desc: '分享交流时尚灵感',
    icon: '🌟',
    gradient: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)'
  }
])

onMounted(() => {
  console.log('首页加载完成')
  // 模拟加载过程
  setTimeout(() => {
    isLoading.value = false
  }, 800)
})

// 功能卡片点击
const handleFeatureClick = (feature: any) => {
  uni.showToast({
    title: `${feature.title}功能开发中`,
    icon: 'none'
  })
}

// 开发工具入口
const goToTest = () => {
  uni.navigateTo({
    url: '/pages/test/index'
  })
}

// 快速入口点击
const goToWardrobe = () => {
  uni.switchTab({
    url: '/pages/wardrobe/index'
  })
}

const goToOutfit = () => {
  uni.switchTab({
    url: '/pages/outfit/index'
  })
}

const goToProfile = () => {
  uni.switchTab({
    url: '/pages/profile/index'
  })
}
</script>

<template>
  <view class="index-page">
    <!-- 开发工具入口 - 左上角 -->
    <view class="dev-tools" @tap="goToTest">
      <text class="dev-icon">🔧</text>
    </view>

    <!-- 主要内容区域 -->
    <view class="main-content">
      <!-- 顶部欢迎区域 -->
      <view class="welcome-section">
        <view class="welcome-content">
          <text class="app-title">StylishLink</text>
          <text class="app-subtitle">时尚搭配助手</text>
          <text class="welcome-text">基于五行命理的智能穿搭推荐</text>
        </view>

        <!-- 装饰性元素 -->
        <view class="decoration-circles">
          <view class="circle circle-1" />
          <view class="circle circle-2" />
          <view class="circle circle-3" />
        </view>
      </view>

      <!-- 功能卡片区域 -->
      <view class="features-section">
        <view class="section-title">
          <text class="title-text">核心功能</text>
          <text class="title-desc">发现你的专属时尚风格</text>
        </view>

        <view class="features-grid">
          <view
            v-for="feature in featureCards"
            :key="feature.id"
            class="feature-card"
            :style="{ background: feature.gradient }"
            @tap="handleFeatureClick(feature)"
          >
            <view class="card-content">
              <text class="feature-icon">{{ feature.icon }}</text>
              <text class="feature-title">{{ feature.title }}</text>
              <text class="feature-desc">{{ feature.desc }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 快速入口区域 -->
      <view class="quick-actions">
        <view class="action-item" @tap="goToWardrobe">
          <view class="action-icon">📂</view>
          <text class="action-text">我的衣橱</text>
        </view>
        <view class="action-item" @tap="goToOutfit">
          <view class="action-icon">🎨</view>
          <text class="action-text">开始搭配</text>
        </view>
        <view class="action-item" @tap="goToProfile">
          <view class="action-icon">👤</view>
          <text class="action-text">个人中心</text>
        </view>
      </view>
    </view>

    <!-- 底部导航栏 -->
    <TheTabBar />
  </view>
</template>

<style lang="scss" scoped>
.index-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #c2e9fb 0%, #d8c6fd 100%);
  position: relative;
  padding-bottom: 100px; // 为 TabBar 留出空间
}

/* 开发工具入口 */
.dev-tools {
  position: fixed;
  top: 60px;
  left: 20px;
  width: 44px;
  height: 44px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.dev-icon {
  font-size: 18px;
}

/* 主要内容区域 */
.main-content {
  padding: 60px 20px 20px;
}

/* 欢迎区域 */
.welcome-section {
  position: relative;
  text-align: center;
  padding: 40px 0 60px;
  margin-bottom: 40px;
}

.welcome-content {
  position: relative;
  z-index: 2;
}

.app-title {
  display: block;
  font-size: 36px;
  font-weight: bold;
  color: white;
  margin-bottom: 8px;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.app-subtitle {
  display: block;
  font-size: 18px;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 12px;
}

.welcome-text {
  display: block;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.5;
}

/* 装饰圆圈 */
.decoration-circles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 80px;
  height: 80px;
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.circle-2 {
  width: 60px;
  height: 60px;
  top: 60%;
  right: 15%;
  animation-delay: 2s;
}

.circle-3 {
  width: 40px;
  height: 40px;
  top: 30%;
  right: 30%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

/* 功能区域 */
.features-section {
  margin-bottom: 40px;
}

.section-title {
  text-align: center;
  margin-bottom: 30px;
}

.title-text {
  display: block;
  font-size: 24px;
  font-weight: bold;
  color: white;
  margin-bottom: 8px;
}

.title-desc {
  display: block;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
}

/* 功能卡片网格 */
.features-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.feature-card {
  padding: 24px 16px;
  border-radius: 20px;
  text-align: center;
  position: relative;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.feature-card:active {
  transform: scale(0.95);
}

.card-content {
  position: relative;
  z-index: 2;
}

.feature-icon {
  display: block;
  font-size: 32px;
  margin-bottom: 12px;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.feature-title {
  display: block;
  font-size: 16px;
  font-weight: 600;
  color: white;
  margin-bottom: 6px;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.feature-desc {
  display: block;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.4;
}

/* 快速入口 */
.quick-actions {
  display: flex;
  justify-content: space-around;
  padding: 24px 0;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px 12px;
  border-radius: 16px;
  transition: all 0.3s ease;
}

.action-item:active {
  background: rgba(255, 255, 255, 0.1);
  transform: scale(0.95);
}

.action-icon {
  font-size: 24px;
  margin-bottom: 8px;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.action-text {
  font-size: 12px;
  color: white;
  text-align: center;
  font-weight: 500;
}
</style>
