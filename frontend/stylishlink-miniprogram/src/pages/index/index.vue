<!-- 首页 - StylishLink 时尚搭配助手 -->
<script setup lang="ts">
import type { CaseItem, CategoryTab, LocationInfo, LunarInfo, WeatherData } from '@/types/business'
import { computed, onMounted, ref, watch } from 'vue'
import { getWeather, getWeatherIcon } from '@/api/ai'
import CaseGrid from '@/components/business/CaseGrid.vue'
import OutfitCards from '@/components/business/OutfitCards.vue'
import OutfitRecommendationCard from '@/components/business/OutfitRecommendationCard.vue'
import WeatherEnergySection from '@/components/business/WeatherEnergySection.vue'
import CategoryTabs from '@/components/common/CategoryTabs.vue'
import DevTools from '@/components/common/DevTools.vue'
import FloatingActionButton from '@/components/common/FloatingActionButton.vue'
import TheTabBar from '@/components/layout/TheTabBar.vue'
import { useEnergyData } from '@/composables/useEnergyData'
import { useMenuButton } from '@/composables/useMenuButton'
import { useUserStore } from '@/store/user'

// 用户状态
const userStore = useUserStore()

// 胶囊按钮适配
const { weatherAlignTop } = useMenuButton()

// 能量数据管理 - 新增
const {
  loading: _energyLoading,
  error: _energyError,
  energyData,
  adviceList,
  dateInfo: energyDateInfo, // API返回的日期信息
  weatherData: _energyWeatherData,
  locationInfo: _energyLocationInfo,
  lunarInfo: _energyLunarInfo,
  shouldShowEnergySection: _energyShouldShow,
  refresh: _refreshEnergyData,
  fetchEnergyData: _fetchEnergyData,
} = useEnergyData({ autoLoad: false }) // 手动控制加载时机

// 监听用户状态变化，当用户完善信息后自动获取能量数据
watch(() => userStore.shouldShowEnergySection, async (newVal) => {
  if (newVal) {
    try {
      await _fetchEnergyData()
    }
    catch (error) {
      console.error('用户状态变化后能量数据获取失败:', error)
    }
  }
})

// 页面状态
const isLoading = ref(true)
const weatherLoading = ref(false)

// 计算属性 - 登录和信息完善状态
const shouldShowEnergySection = computed(() => {
  return userStore.shouldShowEnergySection
})

// 先移除这部分，稍后在适当位置添加

const loginPromptText = computed(() => {
  if (!userStore.isLoggedIn) {
    return '登录后查看个性化建议'
  }
  else if (!userStore.isProfileCompleted) {
    return '完善个人信息后查看专属建议'
  }
  return ''
})

// 推荐搭配轮播状态 (不再需要，因为总是显示第0个为活跃卡片)
// const currentSlide = ref(0)

// 推荐搭配数据（传递给扑克牌组件）
const outfitDataPool = [
  {
    id: 1,
    title: '清新白裙搭配',
    description: '金属性白色提升今日财运，木质配饰平衡整体五行',
    image: 'https://images.unsplash.com/photo-1483985988355-763728e1935b',
    rating: 4.5,
    likes: 12,
    favorites: 8,
    liked: false,
    favorited: false,
  },
  {
    id: 2,
    title: '优雅蓝色系搭配',
    description: '水属性蓝色增强智慧，金质饰品助力人际关系',
    image: 'https://images.unsplash.com/photo-1485230895905-ec40ba36b9bc',
    rating: 4.7,
    likes: 28,
    favorites: 15,
    liked: false,
    favorited: false,
  },
  {
    id: 3,
    title: '活力橙色系搭配',
    description: '火属性橙色激发创造力，土系配饰稳定整体气场',
    image: 'https://images.unsplash.com/photo-1475180098004-ca77a66827be',
    rating: 4.3,
    likes: 19,
    favorites: 11,
    liked: false,
    favorited: false,
  },
]

// 移除硬编码的能量数据，使用 useEnergyData 提供的数据

// 案例分享数据
const caseData = ref<CaseItem[]>([
  { id: 1, title: '春季踏青穿搭', image: 'https://images.unsplash.com/photo-1483985988355-763728e1935b', likes: 28, favorites: 12, liked: false, favorited: false },
  { id: 2, title: '职场通勤搭配', image: 'https://images.unsplash.com/photo-1485230895905-ec40ba36b9bc', likes: 42, favorites: 18, liked: false, favorited: false },
  { id: 3, title: '浪漫约会穿搭', image: 'https://images.unsplash.com/photo-1475180098004-ca77a66827be', likes: 56, favorites: 24, liked: false, favorited: false },
  { id: 4, title: '极简主义风格', image: 'https://images.unsplash.com/photo-1554412933-514a83d2f3c8', likes: 61, favorites: 27, liked: false, favorited: false },
  { id: 5, title: '秋季层叠风格', image: 'https://images.unsplash.com/photo-1581044777550-4cfa60707c03', likes: 47, favorites: 19, liked: false, favorited: false },
  { id: 6, title: '街头潮流搭配', image: 'https://images.unsplash.com/photo-1529139574466-a303027c1d8b', likes: 65, favorites: 32, liked: false, favorited: false },
  { id: 7, title: '优雅轻熟风', image: 'https://images.unsplash.com/photo-1499939667766-4afceb292d05', likes: 51, favorites: 22, liked: false, favorited: false },
  { id: 8, title: '复古学院风', image: 'https://images.unsplash.com/photo-1503185912284-5271ff81b9a8', likes: 44, favorites: 16, liked: false, favorited: false },
])

// 场景分类标签
const categories = ref<CategoryTab[]>([
  { id: 'all', name: '全部', icon: 'icon-changjing', active: true },
  { id: 'work', name: '职场', icon: 'icon-zhichang', active: false },
  { id: 'date', name: '约会', icon: 'icon-yuehui', active: false },
  { id: 'casual', name: '休闲', icon: 'icon-xiuxian2', active: false },
  { id: 'travel', name: '旅行', icon: 'icon-lvhang', active: false },
  { id: 'school', name: '学院', icon: 'icon-xueyuan', active: false },
  { id: 'vacation', name: '度假', icon: 'icon-dujia', active: false },
])

// 当前激活的标签
const activeCategory = ref('all')

// 天气数据
const weatherData = ref<WeatherData>({
  temperature: 22,
  condition: '晴',
  icon: 'icon-taiyang',
})

// 位置信息存储key
const LOCATION_STORAGE_KEY = 'user_selected_city'

// 位置信息 - 从本地存储读取，默认为北京市
const locationInfo = ref<LocationInfo>({
  city: uni.getStorageSync(LOCATION_STORAGE_KEY) || '北京市',
})

// 农历信息
const lunarInfo = ref<LunarInfo>({
  month: '二月',
  day: '十九',
  fullDate: '二月十九 己巳卯 丙成',
})

// 合并数据 - 优先使用API数据，回退到静态数据
const finalLunarInfo = computed(() => {
  // 如果API返回了新的日期信息，组合公历和农历显示
  if (energyDateInfo.value && energyDateInfo.value.lunar && energyDateInfo.value.lunar !== '农历信息加载中...') {
    const gregorian = energyDateInfo.value.gregorian || '今日'
    const lunar = energyDateInfo.value.lunar || ''
    return {
      month: '',
      day: '',
      fullDate: `${gregorian} ${lunar}`, // 格式：2025-06-06 农历五月初十
    }
  }
  return lunarInfo.value
})

// 页面导航
function goToFortuneDetail() {
  uni.navigateTo({
    url: '/pages/fortune/index',
  })
}

function goToOutfitDetail(param?: string | number | CaseItem) {
  // 处理不同类型的参数
  let outfitId: string | number | undefined

  if (typeof param === 'object' && param !== null) {
    // 如果是CaseItem对象，提取id
    outfitId = param.id
  }
  else {
    // 如果是string或number，直接使用
    outfitId = param
  }

  // 跳转到搭配详情页
  uni.navigateTo({
    url: `/pages/outfit/detail${outfitId ? `?id=${outfitId}` : ''}`,
  })
}

// 检查登录状态的拍照功能
async function handleCamera() {
  const canProceed = await userStore.checkLoginStatus()
  if (canProceed) {
    uni.showToast({
      title: '拍照功能开发中',
      icon: 'none',
    })
  }
}

// 能量数据重新加载
async function handleEnergyReload() {
  try {
    const canProceed = await userStore.checkLoginStatus()
    if (canProceed) {
      await _refreshEnergyData()
    }
  }
  catch (error) {
    console.error('重新加载能量数据失败:', error)
  }
}

// 分类切换
function switchCategory(category: CategoryTab) {
  categories.value.forEach(cat => cat.active = false)
  category.active = true
  activeCategory.value = category.id
}

// 处理分类标签点击
function handleCategoryChange(activeKey: string, category: CategoryTab) {
  switchCategory(category)
}

// 获取天气数据
async function fetchWeatherData(cityName: string) {
  // 检查登录状态，未登录时不请求天气数据
  if (!userStore.isLoggedIn) {
    console.warn('用户未登录，跳过天气数据获取')
    return
  }

  if (!cityName) {
    console.warn('城市名称为空，跳过天气数据获取')
    return
  }

  try {
    weatherLoading.value = true

    const weatherResponse = await getWeather(cityName)

    // 更新天气数据
    weatherData.value = {
      city: weatherResponse.city,
      temperature: weatherResponse.temperature,
      condition: weatherResponse.condition,
      icon: getWeatherIcon(weatherResponse.condition), // 使用映射函数获取图标
      humidity: weatherResponse.humidity,
      windSpeed: weatherResponse.windSpeed,
      windDirection: weatherResponse.windDirection,
      airQuality: weatherResponse.airQuality,
      updateTime: weatherResponse.updateTime,
    }
  }
  catch (error) {
    console.error('获取天气数据失败:', error)

    // 可以选择显示错误提示，但不阻塞页面使用
    uni.showToast({
      title: '天气信息获取失败',
      icon: 'none',
      duration: 2000,
    })

    // 保持默认天气数据，避免页面崩溃
  }
  finally {
    weatherLoading.value = false
  }
}

// 处理城市改变
async function handleCityChange(cityName: string) {
  try {
    // 更新本地城市状态
    locationInfo.value.city = cityName

    // 保存到本地存储
    uni.setStorageSync(LOCATION_STORAGE_KEY, cityName)

    uni.showToast({
      title: `切换到${cityName}`,
      icon: 'none',
    })

    // 获取新城市的天气数据（fetchWeatherData内部会检查登录状态）
    await fetchWeatherData(cityName)
  }
  catch (error) {
    console.error('城市切换失败:', error)
    uni.showToast({
      title: '城市切换失败',
      icon: 'none',
    })
  }
}

// 开发工具入口
function goToTest() {
  uni.navigateTo({
    url: '/pages/test/index',
  })
}

// 处理开发工具显示/隐藏切换
function handleDevToolsToggle(visible: boolean) {
  uni.showToast({
    title: visible ? '开发工具已显示' : '开发工具已隐藏，双击重新显示',
    icon: 'none',
    duration: 1500,
  })
}

// 扑克牌事件处理函数 - 需要登录检查（静默模式）
async function handleOutfitCardTap(outfit: any) {
  const canProceed = await userStore.checkLoginStatus(true)
  if (canProceed) {
    goToOutfitDetail(outfit?.id)
  }
}

async function handleOutfitLike() {
  const canProceed = await userStore.checkLoginStatus(true)
  if (canProceed) {
    // 处理点赞逻辑
    uni.showToast({
      title: '点赞成功',
      icon: 'success',
    })
  }
}

async function handleOutfitFavorite() {
  const canProceed = await userStore.checkLoginStatus(true)
  if (canProceed) {
    // 处理收藏逻辑
    uni.showToast({
      title: '收藏成功',
      icon: 'success',
    })
  }
}

async function handleOutfitShare() {
  const canProceed = await userStore.checkLoginStatus(true)
  if (canProceed) {
    uni.showToast({
      title: '分享功能开发中',
      icon: 'none',
    })
  }
}

// 交互事件处理 - 需要登录检查
async function handleLike(item: CaseItem, _event: Event) {
  const canProceed = await userStore.checkLoginStatus()
  if (!canProceed)
    return

  if (item.liked) {
    item.likes--
    item.liked = false
  }
  else {
    item.likes++
    item.liked = true
  }
}

async function handleFavorite(item: CaseItem, _event: Event) {
  const canProceed = await userStore.checkLoginStatus()
  if (!canProceed)
    return

  if (item.favorited) {
    item.favorites--
    item.favorited = false
  }
  else {
    item.favorites++
    item.favorited = true
  }
}

// 处理登录提示点击
function handleLoginPrompt() {
  if (!userStore.isLoggedIn) {
    uni.navigateTo({
      url: '/pages/login/index',
    })
  }
  else if (!userStore.isProfileCompleted) {
    uni.navigateTo({
      url: '/pages/user-info/index?from=home',
    })
  }
}

onMounted(async () => {
  // 初始化用户数据
  await userStore.initUserData()

  // 仅在用户已登录时获取天气数据
  if (userStore.isLoggedIn) {
    await fetchWeatherData(locationInfo.value.city)
  }

  // 如果用户已登录且信息完善，获取能量数据
  if (userStore.shouldShowEnergySection) {
    try {
      await _fetchEnergyData()
    }
    catch (error) {
      console.error('能量数据获取失败:', error)
    }
  }

  setTimeout(() => {
    isLoading.value = false
  }, 800)
})
</script>

<template>
  <view class="index-page">
    <!-- 主滚动容器 -->
    <scroll-view class="main-scroll-container" scroll-y enable-flex :enhanced="true" :bounces="false" :show-scrollbar="false">
      <!-- 主内容区 -->
      <view class="main-content" :style="{ paddingTop: weatherAlignTop }">
        <!-- 天气和穿搭建议 -->
        <view class="weather-fortune-section">
          <!-- 已登录且信息完善：显示天气和能量组件 -->
          <WeatherEnergySection
            v-if="shouldShowEnergySection"
            :weather-data="weatherData"
            :location-info="locationInfo"
            :lunar-info="finalLunarInfo"
            :energy-data="energyData"
            :advice-list="adviceList"
            :loading="_energyLoading"
            :error="_energyError"
            @city-change="handleCityChange"
            @energy-tap="goToFortuneDetail"
            @advice-tap="goToFortuneDetail"
            @reload="handleEnergyReload"
          />

          <!-- 未登录或信息未完善：显示登录提示 -->
          <view v-else class="enhanced-glass login-prompt-card" @tap="handleLoginPrompt">
            <view class="login-prompt-content">
              <view class="prompt-text">
                <text class="prompt-title">
                  {{ loginPromptText }}
                </text>
                <text class="prompt-desc">
                  {{ userStore.isLoggedIn ? '设置个人信息，获得专属搭配建议' : '获得天气穿搭建议和五行运势分析' }}
                </text>
              </view>
              <view class="prompt-arrow">
                <text class="iconfont icon-youjiantou" />
              </view>
            </view>
          </view>
        </view>

        <!-- 今日推荐搭配 -->
        <OutfitRecommendationCard
          title="今日推荐搭配"
          :rating="4.5"
          :clickable="true"
          @card-tap="goToOutfitDetail"
        >
          <!-- 扑克牌组件 -->
          <OutfitCards
            :data-pool="outfitDataPool"
            @card-tap="handleOutfitCardTap"
            @like="handleOutfitLike"
            @favorite="handleOutfitFavorite"
            @share="handleOutfitShare"
          />
        </OutfitRecommendationCard>
      </view>

      <!-- 场景分类tab栏 -->
      <CategoryTabs
        :categories="categories"
        :active-key="activeCategory"
        @change="handleCategoryChange"
      />

      <!-- 案例分享区域 -->
      <CaseGrid
        :cases="caseData"
        @case-tap="goToOutfitDetail"
        @like="handleLike"
        @favorite="handleFavorite"
      />
    </scroll-view>

    <!-- 拍照浮动按钮 -->
    <FloatingActionButton
      icon="icon-xiangji1"
      position="bottom-right"
      size="medium"
      @click="handleCamera"
    />

    <!-- 开发工具 - 左上角小按钮，避开微信菜单 -->
    <DevTools
      position="top-left"
      size="small"
      :opacity="0.5"
      :enable-hide="true"
      @click="goToTest"
      @toggle="handleDevToolsToggle"
    />

    <!-- 底部导航栏 -->
    <TheTabBar />
  </view>
</template>

<style lang="scss" scoped>
// 导入全局样式变量
@import '@/uni.scss';

.index-page {
  min-height: 100vh; // 最小高度避免滚动
  position: relative;
  overflow: hidden; // 完全隐藏滚动条
  width: 100%;
  max-width: 100vw;
  background: linear-gradient(135deg, #c2e9fb 0%, #d8c6fd 100%);

  // 确保隐藏滚动条 - 全局强制隐藏
  ::-webkit-scrollbar {
    display: none !important;
    width: 0 !important;
    height: 0 !important;
    background: transparent !important;
  }

  ::-webkit-scrollbar-track {
    display: none !important;
    background: transparent !important;
  }

  ::-webkit-scrollbar-thumb {
    display: none !important;
    background: transparent !important;
  }

  scrollbar-width: none !important;
  scrollbar-color: transparent transparent !important;
  -ms-overflow-style: none !important;
}

// 主滚动容器
.main-scroll-container {
  height: calc(100vh - var(--bottom-nav-height, 68px));
  width: 100%;
  max-width: 100vw;
  box-sizing: border-box;

      // 微信小程序隐藏滚动条 - 多重方案确保兼容性
  ::-webkit-scrollbar {
    display: none !important;
    width: 0 !important;
    height: 0 !important;
    background: transparent !important;
    -webkit-appearance: none !important;
  }

  ::-webkit-scrollbar-track {
    display: none !important;
    width: 0 !important;
    background: transparent !important;
  }

  ::-webkit-scrollbar-thumb {
    display: none !important;
    width: 0 !important;
    background: transparent !important;
  }

  ::-webkit-scrollbar-corner {
    display: none !important;
    background: transparent !important;
  }

  // 额外隐藏滚动条样式
  scrollbar-width: none !important; // Firefox
  scrollbar-color: transparent transparent !important; // Firefox滚动条颜色
  -ms-overflow-style: none !important; // IE
  overflow: -moz-scrollbars-none !important; // 老版本Firefox

  // 真机特殊处理
  &::-webkit-scrollbar {
    width: 0px !important;
    background: transparent !important;
  }
}

// 主内容区域
.main-content {
  padding: 16px 5% 0;
  // 使用动态计算的padding-top，通过:style绑定设置
  // padding-top 现在由useMenuButton() composable动态计算提供
  padding-bottom: 0; // 移除底部间距
  width: 100%;
  max-width: 100vw;
  box-sizing: border-box;
}

// 毛玻璃卡片效果
.enhanced-glass {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.05);
  border-radius: 12px;
}

// 天气和命理区域
.weather-fortune-section {
  margin-bottom: 24px;
}

// 登录提示卡片
.login-prompt-card {
  padding: 24px;
  margin-bottom: 0;
  transition: all 0.3s ease;
  cursor: pointer;

  &:active {
    transform: scale(0.98);
    opacity: 0.9;
  }
}

.login-prompt-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.prompt-text {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.prompt-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--color-text-primary, #1f2937);
  line-height: 1.4;
}

.prompt-desc {
  font-size: 12px;
  color: var(--color-text-secondary, #6b7280);
  line-height: 1.4;
}

.prompt-arrow {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;

  .iconfont {
    font-size: 14px;
    color: var(--color-text-tertiary, #9ca3af);
  }
}

/* 彩色图标样式 */
.icon-svg-container {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

// 点赞图标颜色和动效
.like-normal {
  color: #ff6b6b !important; // 未点赞时的线型红色
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  transform-origin: center;
}

.like-filled {
  color: #ff6b6b !important; // 已点赞时的实心红色
  animation: likeAnimation 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  transform-origin: center;
}

// 收藏图标颜色和动效
.favorite-normal {
  color: #ffc107 !important; // 未收藏时的线型黄色
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  transform-origin: center;
}

.favorite-filled {
  color: #ffc107 !important; // 已收藏时的实心黄色
  animation: favoriteAnimation 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  transform-origin: center;
}

// 分享图标颜色
.share-icon {
  color: #8b5cf6 !important; // 分享图标始终显示紫色
  transition: all 0.2s ease;
}

// 点赞动画
@keyframes likeAnimation {
  0% {
    transform: scale(1);
  }
  15% {
    transform: scale(1.2);
  }
  30% {
  transform: scale(0.95);
  }
  45% {
    transform: scale(1.1);
  }
  60% {
    transform: scale(0.98);
  }
  100% {
    transform: scale(1);
  }
}

// 收藏动画
@keyframes favoriteAnimation {
  0% {
    transform: scale(1) rotate(0deg);
  }
  25% {
    transform: scale(1.1) rotate(-5deg);
  }
  50% {
    transform: scale(1.2) rotate(0deg);
  }
  75% {
    transform: scale(1.1) rotate(5deg);
  }
  100% {
    transform: scale(1) rotate(0deg);
  }
}

/* CSS变量定义 */
:root {
  --color-text-primary: #1f2937;
  --color-text-secondary: #4b5563;
  --color-text-tertiary: #9ca3af;
}
</style>
