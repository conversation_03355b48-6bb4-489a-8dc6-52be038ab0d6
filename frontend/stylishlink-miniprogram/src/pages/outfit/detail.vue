<!-- 搭配详情页面 -->
<script setup lang="ts">
import type { ActionType } from '@/components/common/LikeFavoriteButton.vue'
import { onMounted, ref } from 'vue'
import LikeFavoriteButton from '@/components/common/LikeFavoriteButton.vue'
import PageHeader from '@/components/common/PageHeader.vue'
import { useMenuButton } from '@/composables/useMenuButton'

// 胶囊按钮适配
const { contentOffset12px } = useMenuButton()

// 页面状态
const activeTab = ref<'items' | 'wuxing'>('items')
const isVideoPlaying = ref(false)
const showReplaceModal = ref(false)
const currentReplaceType = ref('')
const outfitId = ref<string | number>('')

// 搭配数据
const outfitDetail = ref({
  id: '1',
  title: '清新白裙搭配',
  mainImage: 'https://images.unsplash.com/photo-1515886657613-9f3515b0c78f',
  rating: 4.5,
  likes: 24,
  favorites: 16,
  shares: '分享',
  liked: false,
  favorited: false,
  reason: '这套穿搭以清爽的白色为基调，简约大气，适合春夏季节。蓝色包包作为点缀，为整体造型增添亮点，同时补充了水行能量。米色高跟鞋中和了整体偏冷的色调，带来温暖质感。',
  occasions: [
    { icon: 'icon-building', text: '商务会议' },
    { icon: 'icon-briefcase', text: '职场通勤' },
    { icon: 'icon-handshake', text: '客户洽谈' },
    { icon: 'icon-utensils', text: '商务午餐' },
  ],
  items: [
    {
      id: '1',
      type: 'shirt',
      name: '白色简约衬衫',
      image: 'https://images.unsplash.com/photo-1597843664423-e547e596dee4',
      description: '轻薄透气棉质面料，极简风格，适合多种场合搭配',
      wuxing: '金',
      tags: ['清爽', '百搭', '职场'],
    },
    {
      id: '2',
      type: 'skirt',
      name: '白色A字半身裙',
      image: 'https://images.unsplash.com/photo-1577900232427-18219b9166a0',
      description: '优雅流畅线条，高腰设计，修饰身材比例',
      wuxing: '金',
      tags: ['优雅', '简约', '通勤'],
    },
    {
      id: '3',
      type: 'shoes',
      name: '米色尖头高跟鞋',
      image: 'https://images.unsplash.com/photo-1543163521-1bf539c55dd2',
      description: '中跟设计，舒适修饰腿部线条，正式场合首选',
      wuxing: '土',
      tags: ['气质', '正式', '舒适'],
    },
    {
      id: '4',
      type: 'bag',
      name: '蓝色简约手提包',
      image: 'https://images.unsplash.com/photo-1584917865442-de89df76afd3',
      description: '结构感设计，色彩点缀整体造型，实用美观兼具',
      wuxing: '水',
      tags: ['通勤', '实用', '亮点'],
    },
    {
      id: '5',
      type: 'accessory',
      name: '优雅珍珠项链',
      image: 'https://images.unsplash.com/photo-1599643478518-a784e5dc4c8f',
      description: '天然珍珠材质，简约大方，提升整体气质',
      wuxing: '金',
      tags: ['优雅', '百搭', '气质'],
    },
  ],
  wuxingAnalysis: {
    distribution: { 金: 40, 木: 10, 水: 20, 火: 0, 土: 30 },
    overall: '此搭配以金(白色系)为主，辅以土(米色)和水(蓝色)，形成金生水、土生金的良性循环，整体能量和谐流畅。适合性格稳重，需要提升沟通能力和思维敏捷度的场合。',
    elements: [
      {
        element: '金',
        percentage: 40,
        description: '白色调衬衫和裙子，带来清爽利落的气质，提升个人形象与专业感。',
        color: 'linear-gradient(135deg, #ffffff, #f0f0f0)',
      },
      {
        element: '土',
        percentage: 30,
        description: '米色高跟鞋提供稳定、温暖的能量，平衡整体偏冷的色调，增强踏实感。',
        color: 'linear-gradient(135deg, #ffeaa7, #ffc25c)',
      },
      {
        element: '水',
        percentage: 20,
        description: '蓝色手提包点缀，提升智慧与沟通能力，增添灵活和适应能力。',
        color: 'linear-gradient(135deg, #b8c6db, #648dae)',
      },
      {
        element: '木',
        percentage: 10,
        description: '整体设计中的流畅线条，带来成长能量，促进个人发展和创新思维。',
        color: 'linear-gradient(135deg, #a8e6cf, #73c1a8)',
      },
    ],
    suggestions: [
      '搭配绿色或青色小饰品，增添生机与活力',
      '添加暖红色或粉色唇妆，提升个人魅力',
      '佩戴木质或红色系首饰，平衡整体能量',
    ],
  },
})

// 五行色彩映射
const wuxingColorMap = {
  金: 'linear-gradient(135deg, #ffffff, #f0f0f0)',
  木: 'linear-gradient(135deg, #a8e6cf, #73c1a8)',
  水: 'linear-gradient(135deg, #b8c6db, #648dae)',
  火: 'linear-gradient(135deg, #ff9a9e, #ff5458)',
  土: 'linear-gradient(135deg, #ffeaa7, #ffc25c)',
}

// 五行图标映射
const wuxingIconMap = {
  金: 'icon-coins',
  木: 'icon-tree',
  水: 'icon-water',
  火: 'icon-fire',
  土: 'icon-mountain',
}

// 返回上级页面
function goBack() {
  uni.navigateBack()
}

// 切换标签页
function switchTab(tab: 'items' | 'wuxing') {
  activeTab.value = tab
}

// 处理点赞收藏操作
function handleAction(type: ActionType, newActive: boolean, newCount: number) {
  if (type === 'like') {
    outfitDetail.value.liked = newActive
    outfitDetail.value.likes = newCount
  }
  else if (type === 'favorite') {
    outfitDetail.value.favorited = newActive
    outfitDetail.value.favorites = newCount
  }
  else if (type === 'share') {
    // 处理分享
    uni.showToast({
      title: '分享功能开发中',
      icon: 'none',
    })
  }
}

// 播放视频
function playVideo() {
  isVideoPlaying.value = true
}

// 更换单品
function replaceItem(type: string) {
  currentReplaceType.value = type
  showReplaceModal.value = true
}

// 关闭更换弹窗
function closeReplaceModal() {
  showReplaceModal.value = false
  currentReplaceType.value = ''
}

// 选择替代商品
function selectAlternativeItem(itemId: number) {
  // 这里可以实现实际的替换逻辑
  uni.showToast({
    title: `已选择替代商品 ${itemId}`,
    icon: 'success',
  })
  closeReplaceModal()
}

// 页面加载时处理URL参数
onMounted(() => {
  // 获取页面参数
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const options = (currentPage as any).$page?.options || {}

  if (options.id) {
    outfitId.value = options.id
    // 这里可以根据ID加载对应的搭配数据
    // 已获取搭配ID，可用于后续数据加载
  }
})
</script>

<template>
  <view class="outfit-detail-page">
    <!-- 页面标题 -->
    <PageHeader
      :title="outfitDetail.title"
      @back="goBack"
    />

    <!-- 主滚动容器 -->
    <scroll-view
      class="main-scroll-container"
      scroll-y
      enable-flex
      :enhanced="true"
      :bounces="false"
      :show-scrollbar="false"
      :style="{ paddingTop: contentOffset12px }"
    >
      <view class="content-container">
        <!-- 搭配图片展示 -->
        <view class="enhanced-glass outfit-display">
          <view class="outfit-image-container">
            <image
              :src="outfitDetail.mainImage"
              class="outfit-image"
              mode="aspectFill"
              :alt="outfitDetail.title"
            />

            <!-- 评分悬浮组件 -->
            <view class="rating-overlay">
              <view class="star-rating">
                <text
                  v-for="star in 5"
                  :key="star"
                  class="star"
                  :class="star <= Math.floor(outfitDetail.rating) ? 'star-filled' : (star === Math.ceil(outfitDetail.rating) && outfitDetail.rating % 1 !== 0 ? 'star-half' : 'star-empty')"
                >
                  ★
                </text>
              </view>
              <text class="rating-text">
                {{ outfitDetail.rating }}
              </text>
            </view>

            <!-- 控制按钮 -->
            <view class="control-buttons">
              <button class="play-button" @tap="playVideo">
                <text class="iconfont icon-bofang play-icon" />
              </button>

              <view class="action-buttons">
                <LikeFavoriteButton
                  type="like"
                  :count="outfitDetail.likes"
                  :active="outfitDetail.liked"
                  size="small"
                  @click="handleAction"
                />
                <LikeFavoriteButton
                  type="favorite"
                  :count="outfitDetail.favorites"
                  :active="outfitDetail.favorited"
                  size="small"
                  @click="handleAction"
                />
                <LikeFavoriteButton
                  type="share"
                  :text="outfitDetail.shares"
                  size="small"
                  @click="handleAction"
                />
              </view>
            </view>
          </view>
        </view>

        <!-- 搭配推荐理由和使用场合 -->
        <view class="enhanced-glass outfit-info">
          <view class="reason-section">
            <text class="section-title">
              搭配推荐理由
            </text>
            <text class="reason-text">
              {{ outfitDetail.reason }}
            </text>
          </view>

          <view class="occasions-section">
            <text class="section-title">
              推荐场合
            </text>
            <view class="occasions-list">
              <view
                v-for="occasion in outfitDetail.occasions"
                :key="occasion.text"
                class="occasion-tag"
              >
                <text class="iconfont occasion-icon" :class="occasion.icon" />
                <text class="occasion-text">
                  {{ occasion.text }}
                </text>
              </view>
            </view>
          </view>
        </view>

        <!-- 标签页导航 -->
        <view class="enhanced-glass tab-navigation">
          <view
            class="tab-item"
            :class="{ active: activeTab === 'items' }"
            @tap="switchTab('items')"
          >
            <text class="tab-text">
              搭配单品
            </text>
          </view>
          <view
            class="tab-item"
            :class="{ active: activeTab === 'wuxing' }"
            @tap="switchTab('wuxing')"
          >
            <text class="tab-text">
              五行解读
            </text>
          </view>
        </view>

        <!-- 标签页内容 -->
        <view class="tab-content">
          <!-- 搭配单品列表 -->
          <view v-if="activeTab === 'items'" class="items-content">
            <view
              v-for="item in outfitDetail.items"
              :key="item.id"
              class="enhanced-glass item-card"
            >
              <view class="item-image-section">
                <image
                  :src="item.image"
                  class="item-image"
                  mode="aspectFill"
                  :alt="item.name"
                />
                <view
                  class="wuxing-badge"
                  :style="{ background: wuxingColorMap[item.wuxing as keyof typeof wuxingColorMap] }"
                >
                  <text class="iconfont wuxing-icon" :class="wuxingIconMap[item.wuxing as keyof typeof wuxingIconMap]" />
                  <text class="wuxing-text">
                    {{ item.wuxing }}
                  </text>
                </view>
              </view>

              <view class="item-info-section">
                <view class="item-header">
                  <view class="item-details">
                    <text class="item-name">
                      {{ item.name }}
                    </text>
                    <text class="item-description">
                      {{ item.description }}
                    </text>
                  </view>
                  <view class="replace-button" @tap="replaceItem(item.type)">
                    <text class="iconfont icon-random replace-icon" />
                  </view>
                </view>

                <view class="item-tags">
                  <text
                    v-for="tag in item.tags"
                    :key="tag"
                    class="item-tag"
                  >
                    {{ tag }}
                  </text>
                </view>
              </view>
            </view>
          </view>

          <!-- 五行解读内容 -->
          <view v-if="activeTab === 'wuxing'" class="wuxing-content">
            <!-- 五行分布图 -->
            <view class="enhanced-glass wuxing-analysis">
              <text class="section-title">
                <text class="iconfont icon-taijitu title-icon" />
                五行能量解读
              </text>

              <!-- 五行能量条 -->
              <view class="wuxing-bar-container">
                <view class="wuxing-bar">
                  <view
                    v-for="(percentage, element) in outfitDetail.wuxingAnalysis.distribution"
                    :key="element"
                    class="wuxing-segment"
                    :style="{
                      width: `${percentage}%`,
                      background: wuxingColorMap[element as keyof typeof wuxingColorMap],
                    }"
                  />
                </view>

                <!-- 五行标签 -->
                <view class="wuxing-labels">
                  <view
                    v-for="(percentage, element) in outfitDetail.wuxingAnalysis.distribution"
                    :key="element"
                    class="wuxing-label"
                  >
                    <view
                      class="wuxing-color-dot"
                      :style="{ background: wuxingColorMap[element as keyof typeof wuxingColorMap] }"
                    />
                    <text class="wuxing-label-text">
                      {{ element }}({{ percentage }}%)
                    </text>
                  </view>
                </view>
              </view>

              <!-- 整体解读 -->
              <view class="overall-analysis">
                <text class="overall-text">
                  <text class="iconfont icon-xinxi info-icon" />
                  {{ outfitDetail.wuxingAnalysis.overall }}
                </text>
              </view>

              <!-- 各元素解析 -->
              <view class="elements-analysis">
                <view
                  v-for="elementInfo in outfitDetail.wuxingAnalysis.elements"
                  :key="elementInfo.element"
                  class="element-card"
                  :style="{ background: elementInfo.color }"
                >
                  <view class="element-header">
                    <text class="iconfont element-icon" :class="wuxingIconMap[elementInfo.element as keyof typeof wuxingIconMap]" />
                    <text class="element-title">
                      {{ elementInfo.element }} ({{ elementInfo.percentage }}%)
                    </text>
                  </view>
                  <text class="element-description">
                    {{ elementInfo.description }}
                  </text>
                </view>
              </view>
            </view>

            <!-- 能量提升建议 -->
            <view class="enhanced-glass energy-suggestions">
              <text class="section-title">
                <text class="iconfont title-icon icon-mofabang" />
                能量提升建议
              </text>

              <text class="suggestions-intro">
                如需进一步优化五行平衡，可考虑添加以下元素：
              </text>

              <view class="suggestions-list">
                <view
                  v-for="(suggestion, index) in outfitDetail.wuxingAnalysis.suggestions"
                  :key="index"
                  class="suggestion-item"
                >
                  <text class="suggestion-text">
                    {{ suggestion }}
                  </text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 更换单品弹窗 -->
    <view
      v-if="showReplaceModal"
      class="replace-modal-overlay"
      @tap="closeReplaceModal"
    >
      <view class="replace-modal-container" @tap.stop>
        <view class="replace-modal-header">
          <text class="replace-modal-title">
            选择替代商品
          </text>
          <button class="close-button" @tap="closeReplaceModal">
            <text class="iconfont icon-guanbi" />
          </button>
        </view>

        <view class="alternative-items">
          <view
            v-for="item in 6"
            :key="item"
            class="alternative-item"
            @tap="selectAlternativeItem(item)"
          >
            <image
              :src="`https://images.unsplash.com/photo-${1597843664423 + item}`"
              class="alternative-image"
              mode="aspectFill"
            />
            <view class="item-source-badge wardrobe">
              <text class="source-text">
                衣橱
              </text>
            </view>
            <text class="alternative-name">
              替代商品 {{ item }}
            </text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
.outfit-detail-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #c2e9fb 0%, #d8c6fd 100%);

  // CSS变量定义
  --color-text-primary: #333333;
  --color-text-secondary: #666666;
  --color-text-tertiary: #999999;
}

.main-scroll-container {
  height: calc(100vh - var(--bottom-nav-height, 68px));
  width: 100%;
  max-width: 100vw;
  box-sizing: border-box;

  // 隐藏滚动条
  ::-webkit-scrollbar {
    display: none !important;
    width: 0 !important;
    height: 0 !important;
    background: transparent !important;
    -webkit-appearance: none !important;
  }

  ::-webkit-scrollbar-track {
    display: none !important;
    background: transparent !important;
  }

  ::-webkit-scrollbar-thumb {
    display: none !important;
    background: transparent !important;
  }

  scrollbar-width: none !important;
  -ms-overflow-style: none !important;
}

.content-container {
  padding: 0 5% 20px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

// 毛玻璃效果基础样式 - 微信小程序兼容版本
.enhanced-glass {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.85) 0%,
    rgba(255, 255, 255, 0.65) 50%,
    rgba(255, 255, 255, 0.85) 100%
  );
  box-shadow:
    0 -8px 32px rgba(0, 0, 0, 0.06),
    0 -2px 8px rgba(0, 0, 0, 0.03),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.6);
  border-radius: 16px;
  overflow: hidden;
}

// 搭配图片展示
.outfit-display {
  .outfit-image-container {
    position: relative;
    width: 100%;
    aspect-ratio: 4/5;
  }

  .outfit-image {
    width: 100%;
    height: 100%;
  }

  .rating-overlay {
    position: absolute;
    top: 16px;
    right: 16px;
    background: rgba(0, 0, 0, 0.4);
    border-radius: 12px;
    padding: 4px 12px;
    display: flex;
    align-items: center;
    gap: 4px;
  }

  .star-rating {
    display: flex;
    gap: 1px;
  }

  .star {
    font-size: 12px;
    color: rgba(255, 215, 0, 0.9);

    &.star-filled {
      color: rgba(255, 215, 0, 0.9);
    }

    &.star-half {
      color: rgba(255, 215, 0, 0.6);
    }

    &.star-empty {
      color: rgba(255, 255, 255, 0.3);
    }
  }

  .rating-text {
    font-size: 10px;
    color: white;
    font-weight: 500;
  }

  .control-buttons {
    position: absolute;
    left: 16px;
    right: 16px;
    bottom: 16px;
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
  }

  .play-button {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.4);
    border: 1px solid rgba(255, 255, 255, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;

    &:active {
      background: rgba(255, 255, 255, 0.6);
      transform: scale(0.98);
    }
  }

  .play-icon {
    font-size: 16px;
    color: var(--color-text-primary);
  }

  .action-buttons {
    display: flex;
    gap: 6px;
  }
}

// 搭配推荐理由和场合
.outfit-info {
  padding: 16px;

  .reason-section,
  .occasions-section {
    &:not(:last-child) {
      margin-bottom: 16px;
      padding-bottom: 16px;
      border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    }
  }

  .section-title {
    font-size: 12px;
    font-weight: 600;
    color: var(--color-text-primary);
    margin-bottom: 8px;
    display: block;
  }

  .reason-text {
    font-size: 12px;
    color: var(--color-text-secondary);
    line-height: 1.5;
  }

  .occasions-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }

  .occasion-tag {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.4);
  }

  .occasion-icon {
    font-size: 10px;
    color: var(--color-text-secondary);
  }

  .occasion-text {
    font-size: 10px;
    color: var(--color-text-secondary);
  }
}

// 标签页导航
.tab-navigation {
  padding: 4px;
  display: flex;
  margin: 0 0 16px 0;

  .tab-item {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 8px 16px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;

    .tab-text {
      font-size: 14px;
      color: var(--color-text-secondary);
      font-weight: 500;
    }

    &.active {
      background: rgba(255, 255, 255, 0.3);

      .tab-text {
        color: var(--color-text-primary);
        font-weight: 600;
      }
    }

    &:active {
      transform: scale(0.98);
    }
  }
}

// 单品卡片样式
.items-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.item-card {
  padding: 12px;
  display: flex;
  gap: 12px;

  .item-image-section {
    position: relative;
    width: 80px;
    height: 80px;
    border-radius: 8px;
    overflow: hidden;
    flex-shrink: 0;
  }

  .item-image {
    width: 100%;
    height: 100%;
  }

  .wuxing-badge {
    position: absolute;
    top: 4px;
    left: 4px;
    padding: 2px 6px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    gap: 2px;

    .wuxing-icon {
      font-size: 8px;
    }

    .wuxing-text {
      font-size: 8px;
      font-weight: 600;
    }
  }

  .item-info-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .item-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
  }

  .item-details {
    flex: 1;
  }

  .item-name {
    font-size: 14px;
    font-weight: 600;
    color: var(--color-text-primary);
    margin-bottom: 4px;
    display: block;
  }

  .item-description {
    font-size: 12px;
    color: var(--color-text-secondary);
    line-height: 1.4;
  }

  .replace-button {
    width: 24px;
    height: 24px;
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.9);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: transform 0.2s ease;
    cursor: pointer;

    &:active {
      transform: scale(0.95);
    }

    .replace-icon {
      font-size: 12px;
      color: var(--color-text-primary);
    }
  }

  .item-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
  }

  .item-tag {
    padding: 2px 6px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    font-size: 10px;
    color: var(--color-text-secondary);
  }
}

// 五行分析样式增强
.wuxing-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.wuxing-analysis {
  padding: 16px;

  .section-title {
    font-size: 14px;
    font-weight: 600;
    color: var(--color-text-primary);
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    gap: 6px;

    .title-icon {
      font-size: 16px;
      color: var(--color-text-secondary);
    }
  }

  .wuxing-bar-container {
    margin-bottom: 16px;
  }

  .wuxing-bar {
    height: 8px;
    border-radius: 4px;
    overflow: hidden;
    display: flex;
    margin-bottom: 12px;
  }

  .wuxing-segment {
    height: 100%;
    transition: width 0.5s ease;
  }

  .wuxing-labels {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }

  .wuxing-label {
    display: flex;
    align-items: center;
    gap: 4px;
  }

  .wuxing-color-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
  }

  .wuxing-label-text {
    font-size: 10px;
    color: var(--color-text-secondary);
  }

  .overall-analysis {
    margin: 16px 0;
    padding: 12px;
    background: rgba(255, 255, 255, 0.15);
    border-radius: 8px;
    border-left: 3px solid rgba(115, 193, 168, 0.8);
  }

  .overall-text {
    font-size: 12px;
    color: var(--color-text-secondary);
    line-height: 1.5;
    display: flex;
    align-items: flex-start;
    gap: 6px;

    .info-icon {
      font-size: 12px;
      color: rgba(115, 193, 168, 0.8);
      margin-top: 2px;
      flex-shrink: 0;
    }
  }

  .elements-analysis {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .element-card {
    padding: 12px;
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.3);

    .element-header {
      display: flex;
      align-items: center;
      gap: 6px;
      margin-bottom: 6px;
    }

    .element-icon {
      font-size: 14px;
    }

    .element-title {
      font-size: 12px;
      font-weight: 600;
    }

    .element-description {
      font-size: 11px;
      line-height: 1.4;
      opacity: 0.9;
    }
  }
}

.energy-suggestions {
  padding: 16px;

  .section-title {
    font-size: 14px;
    font-weight: 600;
    color: var(--color-text-primary);
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    gap: 6px;

    .title-icon {
      font-size: 16px;
      color: var(--color-text-secondary);
    }
  }

  .suggestions-intro {
    font-size: 12px;
    color: var(--color-text-secondary);
    margin-bottom: 12px;
    line-height: 1.4;
  }

  .suggestions-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .suggestion-item {
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.15);
    border-radius: 6px;
    border-left: 3px solid rgba(255, 193, 7, 0.8);

    .suggestion-text {
      font-size: 11px;
      color: var(--color-text-secondary);
      line-height: 1.4;
    }
  }
}

// 更换单品弹窗样式 - 微信小程序兼容版本
.replace-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  z-index: 200;
  display: flex;
  align-items: flex-end;
  opacity: 1;
  transition: opacity 0.3s ease;
}

.replace-modal-container {
  width: 100%;
  max-height: 80vh;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px 20px 0 0;
  overflow: hidden;
  transform: translateY(0);
  transition: transform 0.3s ease;
  box-shadow: 0 -8px 32px rgba(0, 0, 0, 0.15);
}

.replace-modal-header {
  padding: 16px 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;

  .replace-modal-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--color-text-primary);
  }

  .close-button {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: rgba(0, 0, 0, 0.05);
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    cursor: pointer;

    &:active {
      transform: scale(0.95);
    }
  }
}

.alternative-items {
  padding: 16px 20px;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
  max-height: 60vh;
  overflow-y: auto;
}

.alternative-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  transition: transform 0.2s ease;

  &:active {
    transform: scale(0.98);
  }

  .alternative-image {
    width: 80px;
    height: 80px;
    border-radius: 8px;
    position: relative;
  }

  .item-source-badge {
    position: absolute;
    top: 4px;
    left: 4px;
    padding: 2px 6px;
    border-radius: 4px;

        &.wardrobe {
      background: rgba(168, 230, 207, 0.9);

      .source-text {
        color: #1a1a1a;
        font-size: 8px;
      }
    }

    &.recommended {
      background: rgba(255, 154, 158, 0.9);

      .source-text {
        color: #ffffff;
        font-size: 8px;
      }
    }
  }

  .alternative-name {
    font-size: 10px;
    color: var(--color-text-secondary);
    text-align: center;
    line-height: 1.2;
  }
}

// 搭配信息
.outfit-info {
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;

  .reason-section {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .occasions-section {
    padding-top: 12px;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .section-title {
    font-size: 12px;
    font-weight: 500;
    color: var(--color-text-primary);
  }

  .reason-text {
    font-size: 12px;
    color: var(--color-text-secondary);
    line-height: 1.5;
  }

  .occasions-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }

  .occasion-tag {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 4px 12px;
    border-radius: 50px;
    background: rgba(255, 255, 255, 0.3);
  }

  .occasion-icon {
    font-size: 8px;
    color: var(--color-text-secondary);
  }

  .occasion-text {
    font-size: 10px;
    color: var(--color-text-secondary);
  }
}

// 标签页导航
.tab-navigation {
  display: flex;
  border-bottom: 1px solid rgba(255, 255, 255, 0.3);

  .tab-item {
    flex: 1;
    padding: 8px 0;
    text-align: center;

    &.active {
      border-bottom: 2px solid #73c1a8;

      .tab-text {
        color: var(--color-text-primary);
        font-weight: 500;
      }
    }
  }

  .tab-text {
    font-size: 12px;
    color: var(--color-text-secondary);
  }
}

// 搭配单品内容
.items-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.item-card {
  display: flex;

  .item-image-section {
    position: relative;
    width: 33.33%;
    aspect-ratio: 1;
  }

  .item-image {
    width: 100%;
    height: 100%;
  }

  .wuxing-badge {
    position: absolute;
    top: 8px;
    right: 8px;
    padding: 2px 8px;
    border-radius: 50px;
    display: flex;
    align-items: center;
    gap: 2px;
  }

  .wuxing-icon {
    font-size: 6px;
  }

  .wuxing-text {
    font-size: 8px;
    font-weight: 500;
  }

  .item-info-section {
    flex: 1;
    padding: 12px;
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .item-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
  }

  .item-details {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .item-name {
    font-size: 12px;
    font-weight: 500;
    color: var(--color-text-primary);
  }

  .item-description {
    font-size: 10px;
    color: var(--color-text-secondary);
    line-height: 1.3;
  }

  .replace-button {
    width: 24px;
    height: 24px;
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.9);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .replace-icon {
    font-size: 10px;
    color: var(--color-text-primary);
  }

  .item-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
  }

  .item-tag {
    font-size: 8px;
    padding: 2px 6px;
    border-radius: 50px;
    background: rgba(255, 255, 255, 0.4);
    color: var(--color-text-secondary);
  }
}

// 五行解读内容
.wuxing-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.wuxing-analysis {
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;

  .section-title {
    font-size: 14px;
    font-weight: 500;
    color: var(--color-text-primary);
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .title-icon {
    font-size: 16px;
    color: #8b5cf6;
  }
}

.wuxing-bar-container {
  display: flex;
  flex-direction: column;
  gap: 16px;

  .wuxing-bar {
    height: 12px;
    border-radius: 6px;
    overflow: hidden;
    display: flex;
  }

  .wuxing-segment {
    transition: width 0.3s ease;
  }

  .wuxing-labels {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 8px;
  }

  .wuxing-label {
    display: flex;
    align-items: center;
    gap: 4px;
  }

  .wuxing-color-dot {
    width: 12px;
    height: 12px;
    border-radius: 6px;
  }

  .wuxing-label-text {
    font-size: 10px;
    color: var(--color-text-secondary);
  }
}

.overall-analysis {
  padding: 12px;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.2);

  .overall-text {
    font-size: 12px;
    line-height: 1.5;
    color: var(--color-text-primary);
    display: flex;
    align-items: flex-start;
    gap: 8px;
  }

  .info-icon {
    font-size: 12px;
    color: #8b5cf6;
    margin-top: 2px;
  }
}

.elements-analysis {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.element-card {
  padding: 12px;
  border-radius: 8px;

  .element-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
  }

  .element-icon {
    font-size: 12px;
  }

  .element-title {
    font-size: 12px;
    font-weight: 500;
  }

  .element-description {
    font-size: 12px;
    line-height: 1.4;
  }
}

.energy-suggestions {
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;

  .suggestions-intro {
    font-size: 12px;
    color: var(--color-text-primary);
  }

  .suggestions-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .suggestion-item {
    padding: 12px;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.2);
  }

  .suggestion-text {
    font-size: 12px;
    color: var(--color-text-primary);
    display: flex;
    align-items: center;
  }
}

// CSS变量定义
:root {
  --color-text-primary: #1f2937;
  --color-text-secondary: #4b5563;
  --color-text-tertiary: #9ca3af;
  --bottom-nav-height: 68px;
}
</style>
