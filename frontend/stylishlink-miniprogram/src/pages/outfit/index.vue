<!-- 搭配页面 - 智能穿搭推荐 -->
<script setup lang="ts">
import type { ActionType } from '@/components/common/LikeFavoriteButton.vue'
import type { CategoryTab } from '@/types/business'
import { ref } from 'vue'
import CategoryTabs from '@/components/common/CategoryTabs.vue'
import FloatingActionButton from '@/components/common/FloatingActionButton.vue'
import LikeFavoriteButton from '@/components/common/LikeFavoriteButton.vue'
import TheTabBar from '@/components/layout/TheTabBar.vue'
import { useMenuButton } from '@/composables/useMenuButton'

// 胶囊按钮适配
const { contentTop, contentOffset12px } = useMenuButton()

// 场景分类数据 - 与首页完全一致
const sceneCategories = ref<CategoryTab[]>([
  { id: 'all', name: '全部', icon: 'icon-changjing', active: true },
  { id: 'work', name: '职场', icon: 'icon-zhichang', active: false },
  { id: 'date', name: '约会', icon: 'icon-yuehui', active: false },
  { id: 'casual', name: '休闲', icon: 'icon-xiuxian2', active: false },
  { id: 'travel', name: '旅行', icon: 'icon-lvhang', active: false },
  { id: 'school', name: '学院', icon: 'icon-xueyuan', active: false },
  { id: 'vacation', name: '度假', icon: 'icon-dujia', active: false },
])

// 当前激活的分类
const activeCategory = ref('all')

// 搭配推荐数据
interface OutfitItem {
  id: string
  imageUrl: string
  alt: string
}

interface OutfitRecommendation {
  id: string
  title: string
  mainImage: string
  items: OutfitItem[]
  scene: string
  rating: number
  likes: number
  favorites: number
  shares: string
  wuxingAdvice: string
  isLiked: boolean
  isFavorited: boolean
}

const outfitRecommendations = ref<OutfitRecommendation[]>([
  {
    id: '1',
    title: '简约商务风搭配',
    mainImage: 'https://images.unsplash.com/photo-1515886657613-9f3515b0c78f',
    items: [
      { id: '1-1', imageUrl: 'https://images.unsplash.com/photo-1434389677669-e08b4cac3105', alt: '上衣' },
      { id: '1-2', imageUrl: 'https://images.unsplash.com/photo-1475178626620-a4d074967452', alt: '裤子' },
      { id: '1-3', imageUrl: 'https://images.unsplash.com/photo-1560769629-975ec94e6a86', alt: '鞋子' },
    ],
    scene: '职场通勤',
    rating: 4.8,
    likes: 12,
    favorites: 8,
    shares: '分享',
    wuxingAdvice: '金属性白色提升财运，搭配木质配饰平衡五行',
    isLiked: false,
    isFavorited: false,
  },
  {
    id: '2',
    title: '优雅连衣裙搭配',
    mainImage: 'https://images.unsplash.com/photo-1485230895905-ec40ba36b9bc',
    items: [
      { id: '2-1', imageUrl: 'https://images.unsplash.com/photo-1551107696-a4b0c5a0d9a2', alt: '连衣裙' },
      { id: '2-2', imageUrl: 'https://images.unsplash.com/photo-1560769629-975ec94e6a86', alt: '鞋子' },
      { id: '2-3', imageUrl: 'https://images.unsplash.com/photo-1541099649105-f69ad21f3246', alt: '墨镜' },
    ],
    scene: '约会',
    rating: 4.5,
    likes: 24,
    favorites: 16,
    shares: '分享',
    wuxingAdvice: '水属性蓝色增强气场，搭配金属饰品提升魅力',
    isLiked: false,
    isFavorited: false,
  },
  {
    id: '3',
    title: '休闲运动风搭配',
    mainImage: 'https://images.unsplash.com/photo-1552374196-1ab2a1c593e8',
    items: [
      { id: '3-1', imageUrl: 'https://images.unsplash.com/photo-1523381294911-8d3cead13475', alt: '卫衣' },
      { id: '3-2', imageUrl: 'https://images.unsplash.com/photo-1517438476312-10d79c077509', alt: '运动鞋' },
      { id: '3-3', imageUrl: 'https://images.unsplash.com/photo-1516975080664-ed2fc6a32937', alt: '帽子' },
    ],
    scene: '日常休闲',
    rating: 4.2,
    likes: 18,
    favorites: 12,
    shares: '分享',
    wuxingAdvice: '土属性色彩增强稳定感，适合户外活动穿着',
    isLiked: false,
    isFavorited: false,
  },
])

// 处理分类切换 - 使用首页相同的处理方式
function handleCategoryChange(activeKey: string, category: CategoryTab) {
  sceneCategories.value.forEach(cat => cat.active = false)
  category.active = true
  activeCategory.value = activeKey

  uni.showToast({
    title: `切换到${category.name}`,
    icon: 'none',
  })
}

// 处理按钮点击 - 使用首页的LikeFavoriteButton组件
function handleButtonClick(outfit: OutfitRecommendation, type: ActionType, newActive: boolean, newCount: number) {
  if (type === 'like') {
    outfit.isLiked = newActive
    outfit.likes = newCount
  }
  else if (type === 'favorite') {
    outfit.isFavorited = newActive
    outfit.favorites = newCount
  }
  else if (type === 'share') {
    uni.showToast({
      title: `分享${outfit.title}`,
      icon: 'none',
    })
  }
}

// 跳转到搭配详情
function goToOutfitDetail(outfitId: string) {
  uni.navigateTo({
    url: `/pages/outfit/detail?id=${outfitId}`,
  })
}

// 创建新搭配 - 将来跳转到自定义搭配页
function createNewOutfit() {
  uni.showToast({
    title: '自定义搭配功能开发中',
    icon: 'none',
  })
}
</script>

<template>
  <view class="outfit-page">
    <!-- 页面标题 - 固定在顶部 -->
    <view class="page-header" :style="{ top: contentTop }">
      <text class="page-title">
        搭配
      </text>
    </view>

    <!-- 主滚动容器 -->
    <scroll-view
      class="main-scroll-container"
      scroll-y
      enable-flex
      :enhanced="true"
      :bounces="false"
      :show-scrollbar="false"
      :style="{ paddingTop: contentOffset12px }"
    >
      <!-- 场景分类tab - 移到最前面，让用户一进入就能看到 -->
      <CategoryTabs
        :categories="sceneCategories"
        :active-key="activeCategory"
        @change="handleCategoryChange"
      />

      <!-- 主内容区 -->
      <view class="main-content">
        <view class="content-container">
          <!-- 搭配推荐列表 -->
          <view class="outfit-list">
            <view
              v-for="outfit in outfitRecommendations"
              :key="outfit.id"
              class="outfit-card"
              @tap="goToOutfitDetail(outfit.id)"
            >
              <!-- 搭配网格布局 -->
              <view class="outfit-grid">
                <!-- 主图 -->
                <view class="outfit-main">
                  <image
                    :src="outfit.mainImage"
                    class="main-image"
                    mode="aspectFill"
                    :alt="outfit.title"
                  />
                  <!-- 评分标签 -->
                  <view class="rating-badge">
                    <text class="rating-icon">
                      ★
                    </text>
                    <text class="rating-text">
                      {{ outfit.rating }}
                    </text>
                  </view>
                </view>

                <!-- 配件小图 -->
                <view
                  v-for="item in outfit.items"
                  :key="item.id"
                  class="outfit-item"
                >
                  <image
                    :src="item.imageUrl"
                    class="item-image"
                    mode="aspectFill"
                    :alt="item.alt"
                  />
                </view>
              </view>

              <!-- 搭配信息区 -->
              <view class="outfit-info">
                <view class="outfit-header">
                  <text class="outfit-title">
                    {{ outfit.title }}
                  </text>
                  <view class="action-buttons">
                    <LikeFavoriteButton
                      type="like"
                      :count="outfit.likes"
                      :active="outfit.isLiked"
                      size="small"
                      @click="(type, newActive, newCount) => handleButtonClick(outfit, type, newActive, newCount)"
                    />
                    <LikeFavoriteButton
                      type="favorite"
                      :count="outfit.favorites"
                      :active="outfit.isFavorited"
                      size="small"
                      @click="(type, newActive, newCount) => handleButtonClick(outfit, type, newActive, newCount)"
                    />
                    <LikeFavoriteButton
                      type="share"
                      :text="outfit.shares"
                      size="small"
                      @click="(type, newActive, newCount) => handleButtonClick(outfit, type, newActive, newCount)"
                    />
                  </view>
                </view>
                <view class="wuxing-advice">
                  <text class="advice-icon fas fa-circle-info" />
                  <text class="advice-text">
                    {{ outfit.wuxingAdvice }}
                  </text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 魔法棒浮动按钮 - 使用首页的FloatingActionButton组件 -->
    <FloatingActionButton
      icon="icon-mofabang"
      position="bottom-right"
      size="medium"
      @click="createNewOutfit"
    />

    <!-- 底部导航栏 -->
    <TheTabBar current-tab="outfit" />
  </view>
</template>

<style scoped lang="scss">
// 导入全局样式变量
@import '@/uni.scss';

.outfit-page {
  min-height: 100vh;
  position: relative;
  overflow: hidden;
  width: 100%;
  max-width: 100vw;
  background: linear-gradient(135deg, #c2e9fb 0%, #d8c6fd 100%);

  // 确保隐藏滚动条 - 全局强制隐藏
  ::-webkit-scrollbar {
    display: none !important;
    width: 0 !important;
    height: 0 !important;
    background: transparent !important;
  }

  ::-webkit-scrollbar-track {
    display: none !important;
    background: transparent !important;
  }

  ::-webkit-scrollbar-thumb {
    display: none !important;
    background: transparent !important;
  }

  scrollbar-width: none !important;
  scrollbar-color: transparent transparent !important;
  -ms-overflow-style: none !important;
}

/* 页面标题 - 固定在顶部 */
.page-header {
  position: fixed;
  // top位置现在由useMenuButton() composable动态计算提供
  left: 5%;
  z-index: 100;
  height: 28px;
  display: flex;
  align-items: center;
}

.page-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--color-text-primary, #1f2937);
}

// 主滚动容器
.main-scroll-container {
  height: calc(100vh - var(--bottom-nav-height, 68px));
  width: 100%;
  max-width: 100vw;
  box-sizing: border-box;
  // padding-top现在由useMenuButton() composable动态计算提供

  // 微信小程序隐藏滚动条 - 多重方案确保兼容性
  ::-webkit-scrollbar {
    display: none !important;
    width: 0 !important;
    height: 0 !important;
    background: transparent !important;
    -webkit-appearance: none !important;
  }

  ::-webkit-scrollbar-track {
    display: none !important;
    width: 0 !important;
    background: transparent !important;
  }

  ::-webkit-scrollbar-thumb {
    display: none !important;
    width: 0 !important;
    background: transparent !important;
  }

  ::-webkit-scrollbar-corner {
    display: none !important;
    background: transparent !important;
  }

  // 额外隐藏滚动条样式
  scrollbar-width: none !important; // Firefox
  scrollbar-color: transparent transparent !important; // Firefox滚动条颜色
  -ms-overflow-style: none !important; // IE
  overflow: -moz-scrollbars-none !important; // 老版本Firefox

  // 真机特殊处理
  &::-webkit-scrollbar {
    width: 0px !important;
    background: transparent !important;
  }
}

.main-content {
  padding: 16px 5% 0;
  padding-bottom: 0;
  width: 100%;
  max-width: 100vw;
  box-sizing: border-box;
  margin-top: -12px;
}

.content-container {
  padding-left: 0;
  padding-right: 0;
  padding-top: 0;
}

/* 搭配列表 */
.outfit-list {
  display: flex;
  flex-direction: column;
  gap: 24px;
  padding: 0 0 4px 4px;
}

.outfit-card {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.05);
  border-radius: 16px;
  padding: 12px;
  transition: all 0.3s ease;
}

.outfit-card:active {
  transform: scale(0.98);
}

/* 搭配网格布局 */
.outfit-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(3, 1fr);
  gap: 12px;
  aspect-ratio: 1.1;
  margin-bottom: 12px;
}

.outfit-main {
  grid-row: span 3;
  grid-column: span 2;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
  background: rgba(255, 255, 255, 0.2);
}

.main-image {
  width: 100%;
  height: 100%;
  transition: transform 0.7s ease;
}

.outfit-card:active .main-image {
  transform: scale(1.1);
}

.rating-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  background: rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(5px);
  border-radius: 12px;
  padding: 2px 8px;
  display: flex;
  align-items: center;
}

.rating-icon {
  font-size: 10px;
  color: #ffc25c;
  margin-right: 2px;
}

.rating-text {
  font-size: 10px;
  color: white;
  font-weight: 500;
}

.outfit-item {
  border-radius: 8px;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.2);
}

.item-image {
  width: 100%;
  height: 100%;
  transition: transform 0.7s ease;
}

.outfit-card:active .item-image {
  transform: scale(1.1);
}

/* 搭配信息区 */
.outfit-info {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.outfit-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.outfit-title {
  font-size: 13px;
  font-weight: 500;
  color: var(--color-text-primary);
}

.action-buttons {
  display: flex;
  gap: 6px;
}

.wuxing-advice {
  display: flex;
  align-items: flex-start;
  gap: 4px;
}

.advice-icon {
  font-size: 11px;
  color: var(--color-text-secondary);
  margin-top: 1px;
}

.advice-text {
  font-size: 11px;
  color: var(--color-text-secondary);
  line-height: 1.3;
}

/* CSS变量定义 - 与首页保持一致 */
:root {
  --color-text-primary: #1f2937;
  --color-text-secondary: #4b5563;
  --color-text-tertiary: #9ca3af;
}
</style>
