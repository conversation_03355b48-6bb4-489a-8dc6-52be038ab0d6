<!-- 个人信息完善页面 -->
<script setup lang="ts">
import { computed, onMounted, reactive, ref } from 'vue'
import { analyzeBodyShape } from '@/api/ai'
import { uploadImageWithDetails } from '@/api/file'
import { useUserStore } from '@/store/user'
import { mockAnalyzeBodyShape } from '@/utils/mock-ai'

const userStore = useUserStore()

// 页面参数
const fromLogin = ref(false)

// 表单数据
const formData = reactive({
  // 第一步：全身照
  photoUrl: '',

  // 体型细分数据
  bodyShape: {
    shoulderWidth: '正常',
    waistShape: '直筒',
    belly: '没有',
    hip: '下榻',
    hipShape: '直筒',
    armLength: '正常',
    armCircum: '正常',
    hipWidth: '正常',
    thigh: '正常',
    calf: '正常',
    bodyFat: '匀称',
    bodyLength: '匀称',
  },

  // 第二步：基础信息
  height: 170,
  weight: 60,
  bodyType: '', // 偏瘦/标准/偏胖
  skinTone: '', // 白皙/自然/小麦色
  stylePreferences: [] as string[], // 风格偏好

  // 第三步：身份信息(能量信息)
  fullName: '',
  gender: null as number | null, // 1: 男, 2: 女
  birthDate: '',
  birthTime: '',
  birthPlace: '',
  mode: 'natural' as 'natural' | 'energy',
})

// 页面状态
const isLoading = ref(false)
const currentStep = ref(1) // 1: 全身照, 2: 基础信息, 3: 身份信息
const totalSteps = 3
const showBodyShapeDetail = ref(false) // 显示体型细分
const showMoreShapes = ref(false) // 展开更多体型特征
const aiAnalysisResult = ref<typeof import('@/api/ai').BodyShapeAnalysisResult | null>(null) // AI分析结果

// 跟踪bodyShape是否被修改过
const bodyShapeModified = ref(false)
// 原始bodyShape数据用于对比
const originalBodyShape = ref<typeof formData.bodyShape>({ ...formData.bodyShape })

// 上传和识别状态
const uploadStatus = ref<'none' | 'uploading' | 'success' | 'failed'>('none') // 上传状态
const recognitionStatus = ref<'none' | 'processing' | 'success' | 'failed'>('none') // 识别状态
// const uploadProgress = ref(0) // 上传进度
// const recognitionProgress = ref(0) // 识别进度

// 步骤标题
const stepTitles = ['上传全身照', '基础信息', '身份信息']

// 体型选项
const bodyTypeOptions = [
  { label: '偏瘦', value: 'slim' },
  { label: '标准', value: 'normal' },
  { label: '偏胖', value: 'chubby' },
]

// 肤色选项
const skinToneOptions = [
  { label: '白皙', value: 'fair' },
  { label: '自然', value: 'medium' },
  { label: '小麦色', value: 'dark' },
]

// 风格偏好选项
const styleOptions = [
  { label: '休闲', value: 'casual' },
  { label: '正式', value: 'formal' },
  { label: '运动', value: 'sports' },
  { label: '优雅', value: 'elegant' },
  { label: '街头', value: 'street' },
  { label: '复古', value: 'vintage' },
]

// 性别选项
const genderOptions = [
  { label: '男', value: 1 },
  { label: '女', value: 2 },
]

// 体型细分选项
const bodyShapeOptions = {
  shoulderWidth: ['窄', '偏窄', '正常', '偏宽', '宽'],
  waistShape: ['直筒', '略有曲线', '有曲线', '曲线较明显', '曲线明显'],
  belly: ['没有', '略有小肚腩', '小肚腩', '偏大肚腩', '大肚腩'],
  hip: ['下榻', '略有上翘', '正常', '较上翘', '上翘'],
  hipShape: ['直筒', '略有曲线', '有曲线', '曲线较明显', '曲线明显'],
  armLength: ['短', '偏短', '正常', '偏长', '长'],
  armCircum: ['细', '偏细', '正常', '偏粗', '粗'],
  hipWidth: ['窄', '偏窄', '正常', '偏宽', '宽'],
  thigh: ['细', '偏细', '正常', '偏粗', '粗'],
  calf: ['细', '偏细', '正常', '偏粗', '粗'],
  bodyFat: ['上身粗', '偏上身粗', '匀称', '偏下身粗', '下身粗'],
  bodyLength: ['上身长', '偏上身长', '匀称', '偏下身长', '下身长'],
}

// 数字映射到文字（AI接口返回的是1-5的数字，需要转换为文字显示）
function mapNumberToLabel(key: string, value: number): string {
  const options = bodyShapeOptions[key as keyof typeof bodyShapeOptions]
  if (!options || !Array.isArray(options)) {
    console.error('mapNumberToLabel: 无效的key', key)
    return '正常'
  }

  // 确保 value 是有效数字
  const numValue = typeof value === 'number' ? value : Number(value)
  if (Number.isNaN(numValue) || numValue < 1 || numValue > 5) {
    console.error('mapNumberToLabel: 无效的value', value)
    return options[2] || '正常' // 默认返回中间值
  }

  const index = Math.round(numValue) - 1 // 1-5映射到0-4索引
  const result = options[index]
  return typeof result === 'string' ? result : '正常'
}

// 文字映射到数字（用户手动调整时，需要转换为数字）
function mapLabelToNumber(key: string, label: string): number {
  const options = bodyShapeOptions[key as keyof typeof bodyShapeOptions]
  if (!options)
    return 3 // 默认值
  const index = options.indexOf(label)
  return index >= 0 ? index + 1 : 3 // 0-4索引映射到1-5，默认返回3（正常）
}

// 转换bodyShape对象：将中文字符串转换为数字（用于API提交）
function convertBodyShapeToNumbers(bodyShape: typeof formData.bodyShape) {
  const result: Record<string, number> = {}
  Object.keys(bodyShape).forEach((key) => {
    const value = bodyShape[key as keyof typeof bodyShape]
    result[key] = mapLabelToNumber(key, value)
  })
  return result
}

// 计算属性
const canNextStep = computed(() => {
  switch (currentStep.value) {
    case 1:
      // 第一步：需要照片上传成功且AI识别成功才能下一步
      return !!formData.photoUrl && uploadStatus.value === 'success' && recognitionStatus.value === 'success'
    case 2:
      return !!formData.bodyType && !!formData.skinTone
    case 3:
      return true // 身份信息可选
    default:
      return false
  }
})

// 移除未使用的stepProgress变量

const currentStepTitle = computed(() => {
  return stepTitles[currentStep.value - 1] || ''
})

// 判断是否为能量模式
const isEnergyMode = computed(() => {
  return formData.fullName && formData.birthDate && formData.birthPlace
})

// 页面加载
onMounted(() => {
  // 获取页面参数
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const options = (currentPage as any).options || {}
  fromLogin.value = options.from === 'login'

  // 如果是从个人中心来的，加载已有信息
  if (!fromLogin.value && userStore.userInfo) {
    loadUserInfo()
  }
})

// 加载用户信息
function loadUserInfo() {
  const info = userStore.userInfo
  if (info) {
    Object.assign(formData, {
      photoUrl: info.photoUrl || '',
      height: info.height || 170,
      weight: info.weight || 60,
      bodyType: info.bodyType || '',
      skinTone: info.skinTone || '',
      stylePreferences: info.stylePreferences || [],
      fullName: info.fullName || '',
      gender: info.gender || null,
      birthDate: info.birthDate || '',
      birthTime: info.birthTime || '',
      birthPlace: info.birthPlace || '',
      mode: info.mode || 'natural',
    })

    // 如果有bodyShape数据，也要更新并设置为原始状态
    if (info.bodyShape && typeof info.bodyShape === 'object') {
      // 逐个字段赋值，确保类型安全
      const validKeys = Object.keys(formData.bodyShape)
      validKeys.forEach((key) => {
        const value = (info.bodyShape as any)[key]
        if (typeof value === 'string' && value.trim() !== '') {
          (formData.bodyShape as any)[key] = value
        }
      })
      originalBodyShape.value = { ...formData.bodyShape }
      bodyShapeModified.value = false
    }
  }
}

// 选择全身照
async function selectPhoto() {
  try {
    // 重置状态
    uploadStatus.value = 'none'
    recognitionStatus.value = 'none'
    showBodyShapeDetail.value = false

    const res = await uni.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['camera', 'album'],
    })

    if (res.tempFilePaths && res.tempFilePaths.length > 0) {
      const tempFilePath = res.tempFilePaths[0]
      formData.photoUrl = tempFilePath

      // 显示上传进度
      uni.showLoading({
        title: '正在上传...',
        mask: true,
      })

      try {
        // 尝试上传图片到文件服务
        uploadStatus.value = 'uploading'
        let imageUrl = tempFilePath
        let imageId = ''
        let useCloudService = false

        try {
          // 使用上传接口获取文件ID和URL
          console.warn('开始上传图片，文件路径:', tempFilePath)
          const uploadResult = await uploadImageWithDetails(tempFilePath)
          imageUrl = uploadResult.url
          imageId = uploadResult.fileId
          formData.photoUrl = imageUrl
          useCloudService = true
          uploadStatus.value = 'success'
          console.warn('图片上传成功:', { imageUrl, imageId })
        }
        catch (uploadError) {
          console.error('文件上传失败，使用本地图片:', uploadError)
          console.error('上传错误详情:', uploadError)
          console.error('错误类型:', typeof uploadError)
          if (uploadError instanceof Error) {
            console.error('错误堆栈:', uploadError.stack)
          }
          formData.photoUrl = tempFilePath
          uploadStatus.value = 'failed'
        }

        // 更新加载提示
        uni.showLoading({
          title: 'AI识别中...',
          mask: true,
        })

        recognitionStatus.value = 'processing'
        let analysisResult
        try {
          // 如果上传成功，尝试调用真实AI服务
          if (useCloudService && imageId) {
            analysisResult = await analyzeBodyShape(imageId)
            recognitionStatus.value = 'success'
          }
          else {
            throw new Error('使用Mock识别')
          }
        }
        catch (aiError) {
          console.warn('AI服务调用失败，使用Mock识别:', aiError)
          try {
            // 使用Mock AI识别作为降级方案
            analysisResult = await mockAnalyzeBodyShape(imageUrl)
            recognitionStatus.value = 'success'
          }
          catch (mockError) {
            console.error('Mock识别也失败:', mockError)
            recognitionStatus.value = 'failed'
            throw mockError
          }
        }

        // 更新体型数据（AI接口返回数字1-5，转换为文字存储在前端状态中）
        if ((analysisResult as any).bodyShape && typeof (analysisResult as any).bodyShape === 'object') {
          // 逐个字段安全赋值
          const bodyShapeKeys = Object.keys(formData.bodyShape)
          bodyShapeKeys.forEach((key) => {
            const aiValue = ((analysisResult as any).bodyShape as any)[key]
            if (aiValue !== undefined && aiValue !== null) {
              const labelValue = mapNumberToLabel(key, aiValue)
              if (typeof labelValue === 'string' && labelValue.trim() !== '') {
                (formData.bodyShape as any)[key] = labelValue
              }
            }
          })
        }

        // 设置推荐的体型分类
        formData.bodyType = analysisResult.bodyType

        // 保存AI分析结果用于显示
        aiAnalysisResult.value = analysisResult

        // 显示识别结果
        showBodyShapeDetail.value = true

        // 重置修改标记（AI识别的结果视为初始状态）
        bodyShapeModified.value = false
        originalBodyShape.value = { ...formData.bodyShape }

        uni.hideLoading()
        uni.showToast({
          title: useCloudService ? '体型识别完成' : '离线识别完成',
          icon: 'success',
        })

        console.warn('AI识别结果:', analysisResult)
      }
      catch (error) {
        uni.hideLoading()
        console.error('识别过程完全失败:', error)

        // 设置识别状态为失败
        recognitionStatus.value = 'failed'

        uni.showToast({
          title: '识别失败，请重试或跳过',
          icon: 'none',
        })
      }
    }
  }
  catch (error) {
    console.error('选择照片失败:', error)
    uploadStatus.value = 'failed'
    recognitionStatus.value = 'failed'
    uni.showToast({
      title: '选择照片失败',
      icon: 'none',
    })
  }
}

// 选择体型
function selectBodyType(bodyType: string) {
  formData.bodyType = bodyType
}

// 选择肤色
function selectSkinTone(skinTone: string) {
  formData.skinTone = skinTone
}

// 切换风格偏好
function toggleStylePreference(style: string) {
  const index = formData.stylePreferences.indexOf(style)
  if (index > -1) {
    formData.stylePreferences.splice(index, 1)
  }
  else {
    if (formData.stylePreferences.length < 3) {
      formData.stylePreferences.push(style)
    }
    else {
      uni.showToast({
        title: '最多选择3个风格',
        icon: 'none',
      })
    }
  }
}

// 选择性别
function selectGender(gender: number) {
  formData.gender = gender
}

// 选择体型细分
function selectBodyShape(key: string, value: string) {
  // 类型检查和防御性编程
  if (!formData.bodyShape || typeof key !== 'string' || typeof value !== 'string') {
    console.error('selectBodyShape: 参数类型错误', { key, value })
    return
  }

  // 确保 key 是有效的 bodyShape 属性
  const validKeys = Object.keys(formData.bodyShape)
  if (!validKeys.includes(key)) {
    console.error('selectBodyShape: 无效的key', key)
    return
  }

  // 获取旧值，确保是字符串类型
  const oldValue = formData.bodyShape[key as keyof typeof formData.bodyShape]
  const oldValueStr = typeof oldValue === 'string'
    ? oldValue
    : `${oldValue || ''}`

  // 强制更新响应式数据 - 先删除再设置，确保Vue检测到变化
  const newBodyShape = { ...formData.bodyShape }
  newBodyShape[key as keyof typeof formData.bodyShape] = value as any
  formData.bodyShape = newBodyShape

  // 显示toast确认更新
  uni.showToast({
    title: `${getShapeLabel(key)}已更新为${value}`,
    icon: 'none',
    duration: 2000,
  })

  // 如果值发生了变化，标记为已修改
  if (oldValueStr !== value) {
    bodyShapeModified.value = true
  }
}

// 身高变化 - slider滑动过程中的实时更新
function onHeightChanging(e: any) {
  formData.height = Number.parseInt(e.detail.value)
}

// 身高变化 - slider滑动结束
function onHeightChange(e: any) {
  formData.height = Number.parseInt(e.detail.value)
}

// 体重变化 - slider滑动过程中的实时更新
function onWeightChanging(e: any) {
  formData.weight = Number.parseInt(e.detail.value)
}

// 体重变化 - slider滑动结束
function onWeightChange(e: any) {
  formData.weight = Number.parseInt(e.detail.value)
}

// 计算滑块位置百分比（用于气泡跟随）
function getSliderPosition(value: number, min: number, max: number): number {
  const percentage = ((value - min) / (max - min)) * 100
  // 限制在0-100范围内，并考虑气泡宽度偏移
  return Math.max(5, Math.min(95, percentage))
}

// 日期选择
function onDateChange(e: any) {
  formData.birthDate = e.detail.value
}

// 时间选择
function onTimeChange(e: any) {
  formData.birthTime = e.detail.value
}

// 下一步
async function nextStep() {
  if (!canNextStep.value) {
    let message = ''
    switch (currentStep.value) {
      case 1:
        message = '请上传全身照'
        break
      case 2:
        message = '请选择体型和肤色'
        break
    }
    if (message) {
      uni.showToast({
        title: message,
        icon: 'none',
      })
    }
    return
  }

  // 第一步：如果bodyShape被修改了，需要调用接口保存
  if (currentStep.value === 1 && bodyShapeModified.value) {
    try {
      isLoading.value = true
      uni.showLoading({
        title: '保存中...',
        mask: true,
      })

      // 只提交bodyShape数据，使用完善信息接口
      const submitData = {
        bodyShape: convertBodyShapeToNumbers(formData.bodyShape),
      }

      await userStore.completeProfile(submitData as any)

      // 更新本地用户信息（保持中文格式供界面显示）
      if (userStore.userInfo) {
        (userStore.userInfo as any).bodyShape = formData.bodyShape
      }

      // 保存成功后重置修改标记
      bodyShapeModified.value = false
      originalBodyShape.value = { ...formData.bodyShape }

      uni.hideLoading()
      uni.showToast({
        title: '保存成功',
        icon: 'success',
        duration: 1000,
      })
    }
    catch (error) {
      uni.hideLoading()
      console.error('保存bodyShape失败:', error)
      uni.showToast({
        title: '保存失败，请重试',
        icon: 'none',
      })
      return
    }
    finally {
      isLoading.value = false
    }
  }

  if (currentStep.value < totalSteps) {
    currentStep.value++
  }
  else {
    submitUserInfo()
  }
}

// 上一步
function prevStep() {
  if (currentStep.value > 1) {
    currentStep.value--
  }
}

// 提交用户信息
async function submitUserInfo() {
  try {
    isLoading.value = true

    // 确定模式
    formData.mode = isEnergyMode.value ? 'energy' : 'natural'

    // 准备提交数据
    const submitData: any = {}
    Object.keys(formData).forEach((key) => {
      const value = (formData as any)[key]
      if (value !== null && value !== '' && value !== undefined) {
        if (Array.isArray(value) && value.length > 0) {
          submitData[key] = value
        }
        else if (!Array.isArray(value)) {
          // 如果是bodyShape，需要转换为数字格式
          if (key === 'bodyShape') {
            submitData[key] = convertBodyShapeToNumbers(value)
          }
          else {
            submitData[key] = value
          }
        }
      }
    })

    await userStore.completeProfile(submitData as any)

    // 提示信息已在store中处理，这里不再重复显示

    // 跳转到首页
    setTimeout(() => {
      uni.switchTab({
        url: '/pages/index/index',
      })
    }, 1500)
  }
  catch {
    // 错误已在store中处理
  }
  finally {
    isLoading.value = false
  }
}

// 跳过设置
function skipSetup() {
  uni.showModal({
    title: '提示',
    content: '跳过设置后，部分功能可能无法使用，确定要跳过吗？',
    showCancel: true,
    confirmText: '跳过',
    cancelText: '继续设置',
    success: (res) => {
      if (res.confirm) {
        if (fromLogin.value) {
          // 来自登录页面，跳转到首页
          uni.switchTab({
            url: '/pages/index/index',
          })
        }
        else {
          // 来自个人中心，返回上一页
          uni.navigateBack()
        }
      }
    },
  })
}

// 获取形状标签
function getShapeLabel(key: string) {
  const labels: any = {
    shoulderWidth: '肩膀',
    waistShape: '腰型',
    hip: '臀型',
    hipShape: '胯型',
    belly: '肚腩',
    armLength: '臂长',
    armCircum: '臂围',
    hipWidth: '胯部',
    thigh: '大腿',
    calf: '小腿',
    bodyFat: '上下身粗',
    bodyLength: '上下身长',
  }
  return labels[key] || ''
}

// 获取体型标签
function getBodyTypeTag() {
  const waistShape = formData.bodyShape.waistShape
  return `${waistShape}体型`
}

// 显示体型细分选项
function showShapeOptions(key: string) {
  const options = bodyShapeOptions[key as keyof typeof bodyShapeOptions]
  if (!options)
    return

  uni.showActionSheet({
    itemList: options,
    success: (res) => {
      if (res.tapIndex >= 0) {
        selectBodyShape(key, options[res.tapIndex])
      }
    },
  })
}

// 切换更多体型特征
function toggleMoreShapes() {
  showMoreShapes.value = !showMoreShapes.value
}
</script>

<template>
  <view class="user-info-page">
    <!-- 页面标题 -->
    <view class="page-header">
      <text class="page-title">
        {{ currentStepTitle }}
      </text>
    </view>

    <!-- 滚动容器 -->
    <scroll-view
      class="main-scroll-container"
      scroll-y
      :enhanced="true"
      :bounces="false"
      :show-scrollbar="false"
    >
      <!-- 进度指示器 -->
      <view class="progress-section">
        <view class="progress-container">
          <view
            v-for="n in totalSteps"
            :key="n"
            class="progress-step"
            :class="{
              active: currentStep === n,
              completed: currentStep > n,
            }"
          >
            <text class="step-number">
              {{ n }}
            </text>
          </view>
          <view class="progress-lines">
            <view
              v-for="n in totalSteps - 1"
              :key="n"
              class="progress-line"
              :class="{ completed: currentStep > n }"
            />
          </view>
        </view>
      </view>
      <!-- 第一步：上传全身照 -->
      <view v-if="currentStep === 1" class="step-container">
        <view class="step-desc">
          <text class="desc-text">
            上传一张全身照，帮助我们为您提供更贴合的穿搭推荐
          </text>
        </view>

        <!-- 照片上传区域 -->
        <view class="photo-upload-section">
          <view class="upload-container" @tap="selectPhoto">
            <view v-if="!formData.photoUrl" class="upload-placeholder">
              <text class="iconfont icon-xiangji upload-icon" />
              <text class="upload-text">
                点击上传全身照
              </text>
              <text class="upload-tip">
                建议使用站立正面照
              </text>
            </view>
            <image
              v-else
              :src="formData.photoUrl"
              class="uploaded-photo"
              mode="aspectFit"
            />
          </view>
        </view>

        <!-- 体型识别结果 -->
        <view v-if="showBodyShapeDetail" class="body-shape-section">
          <!-- 标题区域 -->
          <view class="result-header">
            <text class="iconfont icon-zhengque result-icon" />
            <text class="result-title">
              识别结果
            </text>
            <view class="body-type-tag">
              {{ aiAnalysisResult?.bodyType || getBodyTypeTag() }}
            </view>
          </view>

          <!-- 提示文案 -->
          <view class="shape-tip">
            <text class="tip-text">
              识别不准确？点击下方数据可修改
            </text>
          </view>

          <!-- 主要体型特征 -->
          <view class="shape-grid">
            <view
              v-for="key in [
                'shoulderWidth',
                'waistShape',
                'hip',
                'hipShape',
              ]"
              :key="key"
              class="shape-item"
            >
              <text class="shape-label">
                {{ getShapeLabel(key) }}
              </text>
              <view class="shape-selector" @tap="showShapeOptions(key)">
                <text class="shape-value">
                  {{ (formData.bodyShape as any)[key] }}
                </text>
                <text class="iconfont icon-xiala shape-arrow" />
              </view>
            </view>
          </view>

          <!-- 展开更多 -->
          <view v-if="showMoreShapes" class="shape-grid">
            <view
              v-for="key in [
                'belly',
                'armLength',
                'armCircum',
                'hipWidth',
                'thigh',
                'calf',
                'bodyFat',
                'bodyLength',
              ]"
              :key="key"
              class="shape-item"
            >
              <text class="shape-label">
                {{ getShapeLabel(key) }}
              </text>
              <view class="shape-selector" @tap="showShapeOptions(key)">
                <text class="shape-value">
                  {{ (formData.bodyShape as any)[key] }}
                </text>
                <text class="iconfont icon-xiala shape-arrow" />
              </view>
            </view>
          </view>

          <!-- 展开按钮 -->
          <view class="expand-container">
            <view class="expand-toggle-small" @tap="toggleMoreShapes">
              <text class="expand-text">
                {{ showMoreShapes ? "收起" : "更多" }}
              </text>
              <text
                class="iconfont expand-icon"
                :class="showMoreShapes ? 'icon-shouqi' : 'icon-zhankai'"
              />
            </view>
          </view>
        </view>

        <!-- 下一步按钮 -->
        <view class="step-actions">
          <button class="skip-button" @tap="skipSetup">
            跳过
          </button>
          <button
            class="next-button"
            :class="{ disabled: !canNextStep }"
            :disabled="!canNextStep"
            @tap="nextStep"
          >
            下一步
          </button>
        </view>
      </view>

      <!-- 第二步：基础信息 -->
      <view v-if="currentStep === 2" class="step-container">
        <view class="step-desc">
          <text class="desc-text">
            填写基本信息，让我们了解您的穿搭需求
          </text>
        </view>

        <!-- 穿搭基础信息 -->
        <view class="glass-card">
          <view class="card-header">
            <text class="card-title">
              穿搭基础信息
            </text>
          </view>
          <view class="card-content">
            <!-- 身高滑动条 -->
            <view class="slider-group">
              <text class="slider-label">
                身高
              </text>
              <view class="slider-container">
                <view class="slider-wrapper">
                  <!-- 动态数值气泡 -->
                  <view
                    class="slider-bubble"
                    :style="{ left: `${getSliderPosition(formData.height, 140, 210)}%` }"
                  >
                    {{ formData.height }}cm
                  </view>
                  <slider
                    class="custom-slider"
                    :value="formData.height"
                    :min="140"
                    :max="210"
                    @changing="onHeightChanging"
                    @change="onHeightChange"
                  />
                </view>
                <view class="slider-range">
                  <text class="range-text">
                    140cm
                  </text>
                  <text class="range-text">
                    210cm
                  </text>
                </view>
              </view>
            </view>

            <!-- 体重滑动条 -->
            <view class="slider-group">
              <text class="slider-label">
                体重
              </text>
              <view class="slider-container">
                <view class="slider-wrapper">
                  <!-- 动态数值气泡 -->
                  <view
                    class="slider-bubble"
                    :style="{ left: `${getSliderPosition(formData.weight, 40, 120)}%` }"
                  >
                    {{ formData.weight }}kg
                  </view>
                  <slider
                    class="custom-slider"
                    :value="formData.weight"
                    :min="40"
                    :max="120"
                    @changing="onWeightChanging"
                    @change="onWeightChange"
                  />
                </view>
                <view class="slider-range">
                  <text class="range-text">
                    40kg
                  </text>
                  <text class="range-text">
                    120kg
                  </text>
                </view>
              </view>
            </view>

            <!-- 体型选择 -->
            <view class="option-group">
              <text class="option-label">
                体型
              </text>
              <view class="option-grid">
                <view
                  v-for="option in bodyTypeOptions"
                  :key="option.value"
                  class="option-item"
                  :class="{ selected: formData.bodyType === option.value }"
                  @tap="selectBodyType(option.value)"
                >
                  {{ option.label }}
                </view>
              </view>
            </view>

            <!-- 肤色选择 -->
            <view class="option-group">
              <text class="option-label">
                肤色
              </text>
              <view class="option-grid">
                <view
                  v-for="option in skinToneOptions"
                  :key="option.value"
                  class="option-item"
                  :class="{ selected: formData.skinTone === option.value }"
                  @tap="selectSkinTone(option.value)"
                >
                  {{ option.label }}
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 风格偏好 -->
        <view class="glass-card">
          <view class="card-header">
            <text class="card-title">
              风格偏好
            </text>
          </view>
          <view class="card-content">
            <view class="option-group">
              <text class="option-label">
                喜欢的穿搭风格（可多选，最多3个）
              </text>
              <view class="style-grid">
                <view
                  v-for="option in styleOptions"
                  :key="option.value"
                  class="style-item"
                  :class="{
                    selected: formData.stylePreferences.includes(option.value),
                  }"
                  @tap="toggleStylePreference(option.value)"
                >
                  {{ option.label }}
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 步骤操作 -->
        <view class="step-actions">
          <button class="back-button" @tap="prevStep">
            返回
          </button>
          <button
            class="next-button"
            :class="{ disabled: !canNextStep }"
            :disabled="!canNextStep"
            @tap="nextStep"
          >
            下一步
          </button>
        </view>
      </view>

      <!-- 第三步：身份信息(能量信息) -->
      <view v-if="currentStep === 3" class="step-container">
        <view class="step-desc">
          <text class="desc-text">
            填写能量信息，获得更精准的五行穿搭建议（选填）
          </text>
        </view>

        <!-- 能量信息 -->
        <view class="glass-card">
          <view class="card-header">
            <text class="card-title">
              能量信息
            </text>
          </view>
          <view class="card-content">
            <!-- 姓名 -->
            <view class="input-group">
              <text class="input-label">
                姓名
              </text>
              <input
                v-model="formData.fullName"
                class="form-input"
                placeholder="请输入您的姓名"
                :maxlength="20"
              >
            </view>

            <!-- 性别 -->
            <view class="option-group">
              <text class="option-label">
                性别
              </text>
              <view class="gender-grid">
                <view
                  v-for="option in genderOptions"
                  :key="option.value"
                  class="gender-item"
                  :class="{ selected: formData.gender === option.value }"
                  @tap="selectGender(option.value)"
                >
                  {{ option.label }}
                </view>
              </view>
            </view>

            <!-- 出生日期和时间 -->
            <view class="date-time-group">
              <view class="input-group">
                <text class="input-label">
                  出生日期
                </text>
                <picker
                  mode="date"
                  :value="formData.birthDate"
                  @change="onDateChange"
                >
                  <input
                    class="form-input"
                    :value="formData.birthDate"
                    placeholder="请选择出生日期"
                    disabled
                  >
                </picker>
              </view>
              <view class="input-group">
                <text class="input-label">
                  出生时间
                </text>
                <picker
                  mode="time"
                  :value="formData.birthTime"
                  @change="onTimeChange"
                >
                  <input
                    class="form-input"
                    :value="formData.birthTime"
                    placeholder="请选择出生时间"
                    disabled
                  >
                </picker>
              </view>
            </view>

            <!-- 出生地点 -->
            <view class="input-group">
              <text class="input-label">
                出生地点
              </text>
              <input
                v-model="formData.birthPlace"
                class="form-input"
                placeholder="请输入出生城市"
                :maxlength="50"
              >
            </view>
          </view>
        </view>

        <!-- 步骤操作 -->
        <view class="step-actions">
          <button class="back-button" @tap="prevStep">
            返回
          </button>
          <button
            class="complete-button"
            :class="{ loading: isLoading }"
            :disabled="isLoading"
            @tap="nextStep"
          >
            <text v-if="isLoading" class="iconfont icon-loading loading-icon" />
            {{ isLoading ? "提交中..." : "完成" }}
          </button>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<style lang="scss" scoped>
.user-info-page {
  position: relative;
  min-height: 100vh;
  background: linear-gradient(135deg, #c2e9fb 0%, #d8c6fd 100%);
  width: 100%;
  max-width: 100vw;
  overflow: hidden;
}

/* 页面标题 */
.page-header {
  position: fixed;
  top: calc(constant(safe-area-inset-top, 50px) + 8px);
  top: calc(env(safe-area-inset-top, 50px) + 8px);
  top: calc(var(--safe-area-top, 50px) + 8px);
  left: 5%;
  right: 5%;
  z-index: 100;
  height: 44px;
  display: flex;
  align-items: center;
}

.page-title {
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
  text-shadow: none;
  text-align: left;
}

/* 进度条 */
.progress-section {
  margin: 16px 0 24px 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.progress-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.progress-step {
  position: relative;
  z-index: 2;
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid rgba(139, 92, 246, 0.3);

  &.active {
    background: #8b5cf6;
    border-color: #8b5cf6;
    box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
  }

  &.completed {
    background: #10b981;
    border-color: #10b981;
  }
}

.step-number {
  font-size: 14px;
  font-weight: 600;
  color: #8b5cf6;

  .progress-step.active &,
  .progress-step.completed & {
  color: white;
  }
}

.progress-lines {
  position: absolute;
  top: 50%;
  left: 16px;
  right: 16px;
  height: 2px;
  display: flex;
  z-index: 1;
}

.progress-line {
  flex: 1;
  height: 2px;
  background: rgba(255, 255, 255, 0.3);
  margin: 0 16px;

  &.completed {
    background: #8b5cf6;
  }
}

/* 滚动容器 */
.main-scroll-container {
  height: 100vh;
  padding-top: calc(36px + constant(safe-area-inset-top, 50px));
  padding-top: calc(36px + env(safe-area-inset-top, 50px));
  padding-top: calc(36px + var(--safe-area-top, 50px));
  padding-left: 5%;
  padding-right: 5%;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  width: 100%;
  max-width: 100vw;
  box-sizing: border-box;

  // 隐藏滚动条
  ::-webkit-scrollbar {
    display: none !important;
    width: 0 !important;
    height: 0 !important;
    background: transparent !important;
    -webkit-appearance: none !important;
  }

  ::-webkit-scrollbar-track {
    display: none !important;
    width: 0 !important;
    background: transparent !important;
  }

  ::-webkit-scrollbar-thumb {
    display: none !important;
    width: 0 !important;
    background: transparent !important;
  }

  scrollbar-width: none !important;
  -ms-overflow-style: none !important;
}

/* 步骤容器 */
.step-container {
  padding: 16px 0;
}

.step-desc {
  text-align: center;
  margin-bottom: 24px;
}

.desc-text {
  font-size: 14px;
  color: #666666;
  line-height: 1.5;
}

/* 照片上传区域 */
.photo-upload-section {
  text-align: center;
  margin-bottom: 24px;
}

.upload-container {
  width: 280px;
  height: 280px;
  border-radius: 16px;
  margin: 0 auto;
  overflow: hidden;
  border: 2px dashed rgba(139, 92, 246, 0.4);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
    border-color: #8b5cf6;
  }
}

.upload-placeholder {
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.6);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  .iconfont {
    font-size: 48px;
    color: #8b5cf6;
    margin-bottom: 12px;
  }

  .upload-text {
    font-size: 16px;
    color: #333333;
    margin-bottom: 4px;
  }

  .upload-tip {
    font-size: 12px;
    color: #999999;
  }
}

.uploaded-photo {
  width: 100%;
  height: 100%;
}

/* 体型识别结果 */

.result-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.shape-tip {
  margin-bottom: 8px;
}

.tip-text {
  font-size: 11px;
  color: #666666;
  text-align: center;
}

.result-icon {
  color: #10b981;
  font-size: 12px;
  margin-right: 6px;
}

.result-title {
  font-size: 11px;
  font-weight: 600;
  color: #1a1a1a;
  margin-right: 6px;
}

.glass-card {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.05);
  border-radius: 16px;
  padding: 12px 16px;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-bottom: 12px;
}

.card-title {
  display: block;
  font-size: 13px;
  font-weight: 500;
  color: #1a1a1a;
  margin-bottom: 0;
}

.card-content {
  text-align: left;
}

.body-shape-section {
  background: rgba(255, 255, 255, 0.25);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 16px;
  padding: 12px 16px;
  margin-bottom: 24px;
}

.shape-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 6px;
  margin-bottom: 8px;
}

.shape-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: rgba(255, 255, 255, 0.4);
  border: 1px solid rgba(255, 255, 255, 0.5);
  border-radius: 6px;
  padding: 4px 2px;
  min-height: 44px;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
    background: rgba(255, 255, 255, 0.6);
    border-color: #8b5cf6;
  }
}

.shape-label {
  display: block;
  font-size: 10px;
  color: #333333;
  margin-bottom: 3px;
  text-align: center;
  font-weight: 500;
}

.shape-selector {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  min-height: 24px;
  background: rgba(255, 255, 255, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.4);
  border-radius: 6px;
  padding: 4px 3px;
  box-sizing: border-box;
  transition: all 0.3s ease;

  &:active {
    background: rgba(255, 255, 255, 0.5);
    border-color: #8b5cf6;
  }
}

.shape-value {
  font-size: 10px;
  color: #1a1a1a;
  font-weight: 600;
  text-align: center;
  flex: 1;
  max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.shape-arrow {
  font-size: 8px;
  color: #8b5cf6;
  margin-left: 1px;
  flex-shrink: 0;
}

.expand-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 6px;
}

.expand-toggle-small {
  display: inline-flex;
  align-items: center;
  padding: 2px 8px;
  background: rgba(255, 255, 255, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.4);
  border-radius: 12px;
  color: #8b5cf6;
  font-weight: 500;
  transition: all 0.3s ease;

  &:active {
    background: rgba(255, 255, 255, 0.5);
    border-color: #8b5cf6;
  }
}

.expand-text {
  margin-right: 3px;
  font-size: 10px;
  cursor: pointer;
}

.expand-icon {
  font-size: 10px;
}

.card-subtitle {
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.3);
}

.subtitle-text {
  font-size: 12px;
  color: #666666;
  font-style: italic;
}

/* 体型分析 */
.glass-card {
  margin-bottom: 20px;
}

.body-type-tag {
  background: rgba(139, 92, 246, 0.1);
  border: 1px solid rgba(139, 92, 246, 0.3);
  border-radius: 6px;
  padding: 1px 6px;
  font-size: 9px;
  color: #8b5cf6;
  margin-left: auto;
  font-weight: 500;
}

.analysis-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12px;

  &:last-child {
    margin-bottom: 0;
  }
}

.analysis-icon {
  font-size: 16px;
  color: #10b981;
  margin-right: 8px;
  margin-top: 2px;
  flex-shrink: 0;
}

.analysis-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.analysis-title {
  font-size: 14px;
  font-weight: 600;
  color: #1a1a1a;
  line-height: 1.2;
}

.analysis-text {
  font-size: 13px;
  color: #666666;
  line-height: 1.4;
}

/* 按钮样式 - 参考登录页面高度设置 */
.step-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 24px;
  gap: 16px;
}

.next-button {
  background: linear-gradient(135deg, #8b5cf6, #a855f7);
  border: none;
  border-radius: 16px;
  height: 52px;
  font-size: 16px;
  font-weight: 600;
  color: white;
  box-shadow: 0 8px 24px rgba(139, 92, 246, 0.3);
  transition: all 0.3s ease;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;

  &:active:not(.disabled) {
    transform: scale(0.98);
    box-shadow: 0 4px 16px rgba(139, 92, 246, 0.4);
  }

  &.disabled {
    opacity: 0.6;
  }
}

/* 返回按钮 */
.back-button {
  background: rgba(255, 255, 255, 0.3);
  border: none;
  border-radius: 16px;
  height: 52px;
  font-size: 16px;
  font-weight: 600;
  color: white;
  transition: all 0.3s ease;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;

  &:active {
    transform: scale(0.98);
    background: rgba(255, 255, 255, 0.5);
  }
}

/* 完成按钮 */
.complete-button {
  background: linear-gradient(135deg, #8b5cf6, #a855f7);
  border: none;
  border-radius: 16px;
  height: 52px;
  font-size: 16px;
  font-weight: 600;
  color: white;
  box-shadow: 0 8px 24px rgba(139, 92, 246, 0.3);
  transition: all 0.3s ease;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;

  &:active:not(.loading) {
    transform: scale(0.98);
    box-shadow: 0 4px 16px rgba(139, 92, 246, 0.4);
  }

  &.loading {
    opacity: 0.8;
  }
}

/* 跳过按钮 */
.skip-button {
  background: rgba(255, 255, 255, 0.3);
  border: 1px solid rgba(139, 92, 246, 0.3);
  border-radius: 16px;
  height: 52px;
  font-size: 16px;
  font-weight: 600;
  color: #8b5cf6;
  transition: all 0.3s ease;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;

  &:active {
    transform: scale(0.98);
    background: rgba(139, 92, 246, 0.1);
    border-color: #8b5cf6;
  }
}

/* 表单组件样式 */
.input-group {
  margin-bottom: 24px;

  &:last-child {
    margin-bottom: 0;
  }
}

.input-label {
  display: block;
    font-size: 14px;
  font-weight: 500;
    color: #1a1a1a;
  margin-bottom: 8px;
}

.form-input {
  width: 100%;
  height: 48px;
  background: rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.5);
  border-radius: 12px;
  padding: 0 16px;
  font-size: 16px;
  color: #1a1a1a;
  box-sizing: border-box;
  transition: all 0.3s ease;

  &:focus {
    background: rgba(255, 255, 255, 0.6);
    border-color: #8b5cf6;
    outline: none;
    box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.2);
  }

  &::placeholder {
    color: rgba(0, 0, 0, 0.4);
  }
}

/* 滑动条样式 */
.slider-group {
  margin-bottom: 24px;
}

.slider-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #1a1a1a;
  margin-bottom: 16px;
}

.slider-container {
  position: relative;
}

.slider-wrapper {
  position: relative;
  width: 100%;
}

.slider-bubble {
  position: absolute;
  top: -40px;
  transform: translateX(-50%);
  background: linear-gradient(135deg, #8b5cf6, #a855f7);
  color: white;
  padding: 6px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  white-space: nowrap;
  z-index: 10;
  transition: left 0.1s ease;
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
}

.slider-bubble::after {
  content: '';
  position: absolute;
  bottom: -6px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 6px solid #8b5cf6;
}

.custom-slider {
  width: 100%;
  margin: 8px 0;
}

.slider-range {
  display: flex;
  justify-content: space-between;
  margin-top: 8px;
}

.range-text {
  font-size: 12px;
  color: #999999;
}

/* 选项组样式 */
.option-group {
  margin-bottom: 24px;

  &:last-child {
    margin-bottom: 0;
  }
}

.option-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #1a1a1a;
  margin-bottom: 16px;
}

.option-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
}

.option-item {
  height: 44px;
  background: rgba(255, 255, 255, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.4);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #1a1a1a;
  font-weight: 500;
  transition: all 0.3s ease;

  &.selected {
    background: rgba(139, 92, 246, 0.8);
    border-color: #8b5cf6;
    color: white;
  }

  &:active {
    transform: scale(0.98);
  }
}

/* 风格网格 */
.style-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
}

.style-item {
  height: 44px;
  background: rgba(255, 255, 255, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.4);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #1a1a1a;
  font-weight: 500;
  transition: all 0.3s ease;

  &.selected {
    background: rgba(139, 92, 246, 0.8);
    border-color: #8b5cf6;
    color: white;
  }

  &:active {
    transform: scale(0.98);
  }
}

/* 性别网格 */
.gender-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.gender-item {
  height: 44px;
  background: rgba(255, 255, 255, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.4);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #1a1a1a;
  font-weight: 500;
  transition: all 0.3s ease;

  &.selected {
    background: rgba(139, 92, 246, 0.8);
    border-color: #8b5cf6;
    color: white;
  }

  &:active {
    transform: scale(0.98);
  }
}

/* 日期时间组 */
.date-time-group {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

/* 步骤操作 */
.step-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 48px;
  padding-top: 24px;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
}

/* 动画和加载效果 */
.loading-icon {
  font-size: 16px;
  color: white;
  margin-right: 8px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 体型特征展开 */
.expand-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  padding: 8px;
  cursor: pointer;
  transition: all 0.3s ease;

  &:active {
    opacity: 0.7;
  }
}

.expand-text {
  font-size: 12px;
  color: #8b5cf6;
}

/* 响应式优化 */
@media screen and (max-width: 375px) {
  .option-grid,
  .style-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
  }

  .option-item,
  .style-item {
    font-size: 12px;
    height: 40px;
  }
}
</style>
