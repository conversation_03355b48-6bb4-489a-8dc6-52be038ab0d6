<!-- 衣橱页面 - 数字衣橱管理 -->
<script setup lang="ts">
import type { CategoryTab } from '@/types/business'
import type { WuxingRatio } from '@/utils/wuxing'
import { computed, ref } from 'vue'
import WuxingChart from '@/components/business/WuxingChart.vue'
import CategoryTabs from '@/components/common/CategoryTabs.vue'
import FloatingActionButton from '@/components/common/FloatingActionButton.vue'
import TheTabBar from '@/components/layout/TheTabBar.vue'
import { useMenuButton } from '@/composables/useMenuButton'

// 胶囊按钮适配
const { contentTop, contentOffset12px } = useMenuButton()

// 衣物分类数据
const clothingCategories = ref<CategoryTab[]>([
  { id: 'all', name: '全部(126)', icon: 'icon-quanbu1', active: true },
  { id: 'top', name: '上衣(38)', icon: 'icon-tixu', active: false },
  { id: 'bottom', name: '下装(27)', icon: 'icon-a-114_xiazhuang', active: false },
  { id: 'outer', name: '外套(22)', icon: 'icon-a-143_fengyiwaitao', active: false },
  { id: 'shoes', name: '鞋子(15)', icon: 'icon-xiezi', active: false },
  { id: 'bags', name: '包(12)', icon: 'icon-xiebao-nvbao', active: false },
  { id: 'accessories', name: '配饰(24)', icon: 'icon-peishi-xuanzhong', active: false },
])

// 当前选中的分类
const activeCategory = ref('all')

// 衣橱统计数据
const wardrobeStats = ref({
  totalItems: 120,
  stylePosition: '洒脱自然风格',
  percentile: 82,
  wuxingRatio: {
    金: 24, // 120 * 0.20
    木: 18, // 120 * 0.15
    水: 21, // 120 * 0.175
    火: 30, // 120 * 0.25
    土: 27, // 120 * 0.225
  } as WuxingRatio,
  recentlyAdded: '3天前添加了连衣裙',
  mostWorn: '白色衬衫 (本月8次)',
})

// 衣物数据
const clothingItems = ref([
  {
    id: 1,
    image: 'https://images.unsplash.com/photo-1551638254-27ad5000d1e2',
    category: 'top',
    label: '职场正装',
    alt: '白色衬衫',
  },
  {
    id: 2,
    image: 'https://images.unsplash.com/photo-1578587018452-892bacefd3f2',
    category: 'top',
    label: '通勤必备',
    alt: '蓝色西装',
  },
  {
    id: 3,
    image: 'https://images.unsplash.com/photo-1578766441804-48f30e6e02c4',
    category: 'bottom',
    label: '休闲百搭',
    alt: '牛仔裤',
  },
  {
    id: 4,
    image: 'https://images.unsplash.com/photo-1551232864-3f0890e580d9',
    category: 'top',
    label: '正式场合',
    alt: '黑色连衣裙',
  },
  {
    id: 5,
    image: 'https://images.unsplash.com/photo-1543076447-215ad9ba6923',
    category: 'accessories',
    label: '搭配裙装',
    alt: '珍珠耳环',
  },
  {
    id: 6,
    image: 'https://images.unsplash.com/photo-1525507119028-ed4c629a60a3',
    category: 'outer',
    label: '商务正式',
    alt: '蓝色西装外套',
  },
  {
    id: 7,
    image: 'https://images.unsplash.com/photo-1541099649105-f69ad21f3246',
    category: 'accessories',
    label: '夏季必备',
    alt: '时尚墨镜',
  },
  {
    id: 8,
    image: 'https://images.unsplash.com/photo-1551107696-a4b0c5a0d9a2',
    category: 'top',
    label: '约会聚会',
    alt: '绿色连衣裙',
  },
  {
    id: 9,
    image: 'https://images.unsplash.com/photo-1617627143750-d86bc21e42bb',
    category: 'bags',
    label: '职场百搭',
    alt: '米色手提包',
  },
  {
    id: 10,
    image: 'https://images.unsplash.com/photo-1575428652377-a2d80e2277fc',
    category: 'shoes',
    label: '提升气场',
    alt: '黑色高跟鞋',
  },
  {
    id: 11,
    image: 'https://images.unsplash.com/photo-1516975080664-ed2fc6a32937',
    category: 'accessories',
    label: '秋冬保暖',
    alt: '针织帽',
  },
  {
    id: 12,
    image: 'https://images.unsplash.com/photo-1551488831-00ddcb6c6bd3',
    category: 'accessories',
    label: '增添亮点',
    alt: '花卉围巾',
  },
])

// 分析弹窗显示状态
const showAnalysisModal = ref(false)

// 分类筛选后的衣物列表
const filteredClothingItems = computed(() => {
  if (activeCategory.value === 'all') {
    return clothingItems.value
  }
  return clothingItems.value.filter(item => item.category === activeCategory.value)
})

// 处理分类切换
function handleCategoryChange(activeKey: string, category: CategoryTab) {
  clothingCategories.value.forEach(cat => cat.active = false)
  category.active = true
  activeCategory.value = activeKey
}

// 显示分析弹窗
function showAnalysis() {
  showAnalysisModal.value = true
}

// 关闭分析弹窗
function closeAnalysis() {
  showAnalysisModal.value = false
}

// 添加衣物
function addClothing() {
  console.warn('开始跳转到添加衣物页面')

  try {
    uni.navigateTo({
      url: '/pages/wardrobe/add-clothing/index',
      success: (res) => {
        console.warn('页面跳转成功:', res)
      },
      fail: (error) => {
        console.error('页面跳转失败:', error)

        // 根据错误类型提供不同的处理方案
        if (error.errMsg?.includes('timeout')) {
          uni.showToast({
            title: '页面加载超时，请重试',
            icon: 'none',
            duration: 2000,
          })

          // 尝试延迟后重新跳转
          setTimeout(() => {
            uni.navigateTo({
              url: '/pages/wardrobe/add-clothing/index',
              fail: (retryError) => {
                console.error('重试跳转失败:', retryError)
                uni.showToast({
                  title: '页面加载失败，请检查网络',
                  icon: 'none',
                })
              },
            })
          }, 1000)
        }
        else {
          uni.showToast({
            title: '跳转失败，请重试',
            icon: 'none',
          })
        }
      },
      complete: () => {
        console.warn('页面跳转操作完成')
      },
    })
  }
  catch (error) {
    console.error('跳转操作异常:', error)
    uni.showToast({
      title: '操作异常，请重试',
      icon: 'none',
    })
  }
}

// 查看衣物详情
function viewClothingDetail(id: number) {
  uni.showToast({
    title: `查看衣物详情 ID: ${id}`,
    icon: 'none',
  })
}
</script>

<template>
  <view class="wardrobe-page">
    <!-- 页面标题 -->
    <view class="page-header" :style="{ top: contentTop }">
      <text class="page-title">
        衣橱
      </text>
    </view>

    <!-- 主滚动容器 -->
    <scroll-view class="main-scroll-container" enable-flex scroll-y :enhanced="true" :bounces="false" :show-scrollbar="false" :style="{ paddingTop: contentOffset12px }">
      <!-- 衣橱统计分析卡片 -->
      <view class="enhanced-glass stats-card">
        <view class="stats-content">
          <view class="stats-main">
            <text class="section-title">
              衣橱分析
            </text>
            <view class="stats-row">
              <view class="stats-numbers">
                <text class="total-count">
                  {{ wardrobeStats.totalItems }}
                </text>
                <text class="total-label">
                  件单品
                </text>
              </view>
              <text class="badge-text">
                超过{{ wardrobeStats.percentile }}%用户
              </text>
            </view>
            <text class="style-position">
              您的风格定位：<text class="style-highlight">
                {{ wardrobeStats.stylePosition }}
              </text>
            </text>
          </view>
          <button class="analysis-btn glass-button" @tap="showAnalysis">
            <text class="btn-text">
              查看详情
            </text>
          </button>
        </view>
      </view>

      <!-- 分类标签栏 -->
      <CategoryTabs
        :categories="clothingCategories"
        :active-key="activeCategory"
        @change="handleCategoryChange"
      />

      <!-- 主内容区 -->
      <view class="main-content">
        <view class="content-container">
          <!-- 衣物网格 -->
          <view class="clothing-grid">
            <view
              v-for="item in filteredClothingItems"
              :key="item.id"
              class="enhanced-glass clothing-item"
              @tap="viewClothingDetail(item.id)"
            >
              <view class="item-image">
                <image :src="item.image" mode="aspectFill" :alt="item.alt" />
              </view>
              <view class="item-info">
                <text class="item-label">
                  {{ item.label }}
                </text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 悬浮添加按钮 -->
    <FloatingActionButton
      icon="icon-tianjia"
      @click="addClothing"
    />

    <!-- 分析弹窗 -->
    <view v-if="showAnalysisModal" class="analysis-modal">
      <view class="modal-overlay" @tap="closeAnalysis" />
      <view class="enhanced-glass modal-content">
        <view class="modal-header">
          <text class="modal-title">
            衣橱详细分析
          </text>
          <button class="close-btn" @tap="closeAnalysis">
            <text class="iconfont icon-close" />
          </button>
        </view>

        <!-- 雷达图区域 -->
        <view class="radar-section">
          <view class="radar-chart">
            <text class="chart-placeholder">
              雷达图
            </text>
          </view>
          <view class="legend">
            <view class="legend-item">
              <view class="legend-dot" style="background: #a8e6cf;" />
              <text class="legend-label">
                多样性
              </text>
            </view>
            <view class="legend-item">
              <view class="legend-dot" style="background: #ffd3b6;" />
              <text class="legend-label">
                时尚度
              </text>
            </view>
            <view class="legend-item">
              <view class="legend-dot" style="background: #ffaaa5;" />
              <text class="legend-label">
                实用性
              </text>
            </view>
            <view class="legend-item">
              <view class="legend-dot" style="background: #b8c6db;" />
              <text class="legend-label">
                季节均衡
              </text>
            </view>
            <view class="legend-item">
              <view class="legend-dot" style="background: #ffc25c;" />
              <text class="legend-label">
                五行均衡
              </text>
            </view>
          </view>
        </view>

        <!-- 五行分布 -->
        <view class="wuxing-section">
          <text class="section-title">
            五行属性分布
          </text>
          <WuxingChart
            :ratio="wardrobeStats.wuxingRatio"
            :height="64"
            :show-label="true"
            :label-size="10"
          />
        </view>

        <!-- 优化建议 -->
        <view class="suggestions-section">
          <text class="section-title">
            衣橱优化建议
          </text>
          <view class="suggestions">
            <text class="suggestion-item">
              ● 建议添加更多火属性服饰，平衡五行
            </text>
            <text class="suggestion-item">
              ● 工作场合服饰偏少，可增加正装单品
            </text>
            <text class="suggestion-item">
              ● 有6件超过3个月未穿的单品，建议考虑搭配或处理
            </text>
            <text class="suggestion-item">
              ● 秋季单品较少，建议适当添加
            </text>
          </view>
        </view>

        <!-- 导出按钮 -->
        <button class="glass-button export-btn">
          <text class="btn-text">
            导出完整分析报告
          </text>
        </button>
      </view>
    </view>

    <!-- 底部导航栏 -->
    <TheTabBar />
  </view>
</template>

<style lang="scss" scoped>
.wardrobe-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #c2e9fb 0%, #d8c6fd 100%);
  position: relative;
  overflow: hidden;
  width: 100%;
  max-width: 100vw;

  ::-webkit-scrollbar {
    display: none !important;
    width: 0 !important;
    height: 0 !important;
    background: transparent !important;
  }

  ::-webkit-scrollbar-track {
    display: none !important;
    background: transparent !important;
  }

  ::-webkit-scrollbar-thumb {
    display: none !important;
    background: transparent !important;
  }

  scrollbar-width: none !important;
  scrollbar-color: transparent transparent !important;
  -ms-overflow-style: none !important;
}

// 页面标题
.page-header {
  position: fixed;
  // top位置现在由useMenuButton() composable动态计算提供
  left: 5%;
  z-index: 100;
  height: 28px;
  display: flex;
  align-items: center;
}

.page-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--color-text-primary, #1a1a1a);
}

// 主内容区
.main-content {
  padding: 16px 5% 60px;
  width: 100%;
  max-width: 100vw;
  box-sizing: border-box;
  margin-top: -12px;
}

// 统计卡片
.stats-card {
  margin: 16px 5% 0;
  padding: 12px;
  border-radius: 16px;
  position: relative;
}

.stats-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.stats-main {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.section-title {
  font-size: 12px;
  font-weight: 600;
  color: var(--color-text-primary);
  margin-bottom: 4px;
}

.stats-row {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.stats-numbers {
  display: flex;
  align-items: baseline;
  gap: 4px;
}

.total-count {
  font-size: 14px;
  font-weight: 500;
  color: var(--color-text-primary);
  line-height: 1;
}

.total-label {
  font-size: 10px;
  color: var(--color-text-secondary);
}

.badge-text {
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.3);
  color: var(--color-text-secondary);
}

.style-position {
  font-size: 10px;
  color: var(--color-text-secondary);
  line-height: 1.4;
}

.style-highlight {
  color: var(--color-text-primary);
  font-weight: 500;
}

.analysis-btn {
  height: 24px;
  padding: 0 8px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

// 衣物网格
.clothing-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
  padding-bottom: 40px;
}

.clothing-item {
  border-radius: 16px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
}

.clothing-item:active {
  transform: scale(0.95);
}

.item-image {
  aspect-ratio: 1;
  background: rgba(255, 255, 255, 0.2);
  position: relative;
}

.item-image image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.item-info {
  padding: 12px;
}

.item-label {
  font-size: 12px;
  font-weight: 500;
  color: var(--color-text-primary);
}

// 分析弹窗
.analysis-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(4px);
}

.modal-content {
  margin: 20px;
  border-radius: 16px;
  padding: 20px;
  max-width: 350px;
  width: 100%;
  max-height: 80vh;
  overflow-y: auto;
  position: relative;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.modal-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--color-text-primary);
}

.close-btn {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
}

.close-btn .iconfont {
  font-size: 12px;
  color: var(--color-text-primary);
}

// 雷达图区域
.radar-section {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.radar-chart {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-placeholder {
  font-size: 12px;
  color: var(--color-text-secondary);
}

.legend {
  display: flex;
  flex-direction: column;
  gap: 8px;
  justify-content: center;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 6px;
}

.legend-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.legend-label {
  font-size: 10px;
  color: var(--color-text-secondary);
}

// 五行分布图
.wuxing-section {
  margin-bottom: 20px;
}

.section-title {
  font-size: 12px;
  font-weight: 500;
  color: var(--color-text-primary);
  margin-bottom: 12px;
  display: block;
}

// 建议区域
.suggestions-section {
  margin-bottom: 20px;
}

.suggestions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.suggestion-item {
  font-size: 10px;
  color: var(--color-text-secondary);
  line-height: 1.4;
}

// 导出按钮
.export-btn {
  width: 100%;
  height: 36px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

// 毛玻璃效果
.enhanced-glass {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.85) 0%,
    rgba(255, 255, 255, 0.65) 50%,
    rgba(255, 255, 255, 0.85) 100%
  );
  box-shadow:
    0 -8px 32px rgba(0, 0, 0, 0.08),
    0 -2px 8px rgba(0, 0, 0, 0.03),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.6);
}

.glass-button {
  background: rgba(255, 255, 255, 0.6);
  border: 1px solid rgba(255, 255, 255, 0.8);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.btn-text {
  font-size: 12px;
  color: var(--color-text-primary);
  font-weight: 500;
}

// 主滚动容器
.main-scroll-container {
  height: calc(100vh - var(--bottom-nav-height, 68px));
  // padding-top现在由useMenuButton() composable动态计算提供
  width: 100%;
  max-width: 100vw;
  box-sizing: border-box;

  // 微信小程序隐藏滚动条 - 多重方案确保兼容性
  ::-webkit-scrollbar {
    display: none !important;
    width: 0 !important;
    height: 0 !important;
    background: transparent !important;
    -webkit-appearance: none !important;
  }

  ::-webkit-scrollbar-track {
    display: none !important;
    width: 0 !important;
    background: transparent !important;
  }

  ::-webkit-scrollbar-thumb {
    display: none !important;
    width: 0 !important;
    background: transparent !important;
  }

  ::-webkit-scrollbar-corner {
    display: none !important;
    background: transparent !important;
  }

  // 额外隐藏滚动条样式
  scrollbar-width: none !important; // Firefox
  scrollbar-color: transparent transparent !important; // Firefox滚动条颜色
  -ms-overflow-style: none !important; // IE
  overflow: -moz-scrollbars-none !important; // 老版本Firefox

  // 真机特殊处理
  &::-webkit-scrollbar {
    width: 0px !important;
    background: transparent !important;
  }
}
</style>
