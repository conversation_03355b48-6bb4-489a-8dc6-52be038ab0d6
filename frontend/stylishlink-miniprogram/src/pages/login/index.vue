<!-- 登录页面 -->
<script setup lang="ts">
import { onUnmounted, reactive, ref } from 'vue'
import * as userApi from '@/api/user'
import { useUserStore } from '@/store/user'

const userStore = useUserStore()

// 表单数据
const formData = reactive({
  phone: '',
  code: '',
  codeId: '',
})

// 表单验证状态
const formErrors = reactive({
  phone: '',
  code: '',
})

// 页面状态
const isLoading = ref(false)
const isSendingCode = ref(false)
const countdown = ref(0)
const countdownTimer = ref<NodeJS.Timeout | null>(null)

// 防抖定时器
let phoneDebounceTimer: NodeJS.Timeout | null = null
let codeDebounceTimer: NodeJS.Timeout | null = null

// 验证手机号
function validatePhone(phone: string): boolean {
  const phoneRegex = /^1[3-9]\d{9}$/
  return phoneRegex.test(phone)
}

// 验证表单
function validateForm(): boolean {
  let isValid = true

  // 重置错误信息
  formErrors.phone = ''
  formErrors.code = ''

  // 验证手机号
  if (!formData.phone) {
    formErrors.phone = '请输入手机号'
    isValid = false
  }
  else if (!validatePhone(formData.phone)) {
    formErrors.phone = '请输入正确的手机号'
    isValid = false
  }

  // 验证验证码
  if (!formData.code) {
    formErrors.code = '请输入验证码'
    isValid = false
  }
  else if (formData.code.length !== 6) {
    formErrors.code = '验证码为6位数字'
    isValid = false
  }

  return isValid
}

// 发送验证码
async function sendCode() {
  // 验证手机号
  if (!formData.phone) {
    formErrors.phone = '请输入手机号'
    return
  }
  if (!validatePhone(formData.phone)) {
    formErrors.phone = '请输入正确的手机号'
    return
  }

  // 手机号校验通过，清除错误提示
  formErrors.phone = ''

  try {
    isSendingCode.value = true

    // 调用真实发送验证码API
    const response = await userApi.sendCode({
      phone: formData.phone,
      type: 'login',
    })

    formData.codeId = response.codeId
    startCountdown()

    // 开发环境显示验证码
    if (process.env.NODE_ENV === 'development') {
      uni.showToast({
        title: '验证码: 123456',
        icon: 'none',
        duration: 3000,
      })
    }
    else {
      uni.showToast({
        title: response.message || '验证码已发送',
        icon: 'success',
      })
    }
  }
  catch (error: any) {
    uni.showToast({
      title: error.message || '发送验证码失败',
      icon: 'none',
    })
  }
  finally {
    isSendingCode.value = false
  }
}

// 手机号输入时的实时校验
function onPhoneInput() {
  // 清除之前的定时器
  if (phoneDebounceTimer) {
    clearTimeout(phoneDebounceTimer)
  }

  // 如果有错误提示且当前输入有内容，延迟校验
  if (formErrors.phone && formData.phone) {
    phoneDebounceTimer = setTimeout(() => {
      if (formData.phone && validatePhone(formData.phone)) {
        formErrors.phone = ''
      }
    }, 300) // 减少延迟时间，提升响应速度
  }
}

// 验证码输入时的实时校验
function onCodeInput() {
  // 清除之前的定时器
  if (codeDebounceTimer) {
    clearTimeout(codeDebounceTimer)
  }

  // 如果有错误提示且当前输入有内容，延迟校验
  if (formErrors.code && formData.code) {
    codeDebounceTimer = setTimeout(() => {
      if (formData.code && formData.code.length === 6) {
        formErrors.code = ''
      }
    }, 300) // 减少延迟时间，提升响应速度
  }
}

// 开始倒计时
function startCountdown() {
  countdown.value = 60
  countdownTimer.value = setInterval(() => {
    countdown.value--
    if (countdown.value <= 0) {
      clearInterval(countdownTimer.value!)
      countdownTimer.value = null
    }
  }, 1000)
}

// 登录处理
async function handleLogin() {
  if (!validateForm()) {
    return
  }

  try {
    isLoading.value = true
    const loginData = {
      phone: formData.phone,
      code: formData.code,
      codeId: formData.codeId,
    }

    const result = await userStore.login(loginData)

    // 根据返回状态决定跳转页面
    if (result.hasBasicInfo && result.hasWuxingInfo) {
      // 信息已完善，跳转首页
      uni.switchTab({
        url: '/pages/index/index',
      })
    }
    else {
      // 需要完善信息，跳转信息完善页
      uni.redirectTo({
        url: '/pages/user-info/index?from=login',
      })
    }
  }
  catch {
    // 错误已在store中处理
  }
  finally {
    isLoading.value = false
  }
}

// 清理定时器
onUnmounted(() => {
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value)
  }

  // 清理防抖定时器
  if (phoneDebounceTimer) {
    clearTimeout(phoneDebounceTimer)
  }
  if (codeDebounceTimer) {
    clearTimeout(codeDebounceTimer)
  }
})
</script>

<template>
  <view class="login-page">
    <!-- 页面标题 -->
    <view class="page-header">
      <text class="page-title">
        登录
      </text>
    </view>

    <!-- 滚动容器 -->
    <scroll-view
      class="main-scroll-container"
      scroll-y
      :enhanced="true"
      :bounces="false"
      :show-scrollbar="false"
    >
      <!-- 登录表单 -->
      <view class="login-container">
        <!-- Logo 区域 -->
        <view class="logo-section">
          <view class="logo">
            <text class="iconfont icon-magic logo-icon" />
          </view>
          <text class="app-name">
            StylishLink
          </text>
          <text class="app-desc">
            时尚搭配，智慧穿衣
          </text>
        </view>

        <!-- 表单区域 -->
        <view class="form-section">
          <view class="form-card">
            <!-- 手机号输入 -->
            <view class="input-group">
              <view class="input-label">
                <text class="iconfont icon-shouji" />
                <text class="label-text">
                  手机号
                </text>
              </view>
              <input
                v-model="formData.phone"
                type="number"
                class="form-input"
                :class="{ error: formErrors.phone }"
                placeholder="请输入手机号"
                maxlength="11"
                @input="onPhoneInput"
              >
              <text v-if="formErrors.phone" class="error-text">
                {{ formErrors.phone }}
              </text>
            </view>

            <!-- 验证码输入 -->
            <view class="input-group">
              <view class="input-label">
                <text class="iconfont icon-yanzhengma" />
                <text class="label-text">
                  验证码
                </text>
              </view>
              <view class="code-input-container">
                <input
                  v-model="formData.code"
                  type="number"
                  class="code-input"
                  :class="{ error: formErrors.code }"
                  placeholder="请输入验证码"
                  maxlength="6"
                  @input="onCodeInput"
                >
                <button
                  class="send-code-button"
                  :class="{ disabled: countdown > 0 || isSendingCode }"
                  :disabled="countdown > 0 || isSendingCode"
                  @tap="sendCode"
                >
                  <text v-if="isSendingCode" class="iconfont icon-loading loading-icon" />
                  <text class="button-text">
                    {{
                      isSendingCode ? '发送中...'
                      : countdown > 0 ? `${countdown}s` : '发送验证码'
                    }}
                  </text>
                </button>
              </view>
              <text v-if="formErrors.code" class="error-text">
                {{ formErrors.code }}
              </text>
            </view>

            <!-- 登录按钮 -->
            <button
              class="login-button"
              :class="{ loading: isLoading }"
              :disabled="isLoading"
              style="margin-top: 32px;"
              @tap="handleLogin"
            >
              <text v-if="isLoading" class="iconfont icon-loading loading-icon" />
              <text class="button-text">
                {{ isLoading ? '登录中...' : '登录' }}
              </text>
            </button>
          </view>
        </view>

        <!-- 说明文字 -->
        <view class="description-section">
          <text class="description-text">
            验证成功后自动登录，新用户将自动注册
          </text>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<style lang="scss" scoped>
.login-page {
  position: relative;
  min-height: 100vh;
  background: linear-gradient(135deg, #c2e9fb 0%, #d8c6fd 100%);
  width: 100%;
  max-width: 100vw;
  overflow: hidden;
}

/* 页面标题 */
.page-header {
  position: fixed;
  top: calc(constant(safe-area-inset-top, 50px) + 16px);
  top: calc(env(safe-area-inset-top, 50px) + 16px);
  top: calc(var(--safe-area-top, 50px) + 16px);
  left: 5%;
  z-index: 100;
  height: 28px;
  display: flex;
  align-items: center;
}

.page-title {
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
  text-shadow: 0 2px 8px rgba(255, 255, 255, 0.5);
}

/* 滚动容器 */
.main-scroll-container {
  height: 100vh;
  padding-top: calc(44px + constant(safe-area-inset-top, 50px));
  padding-top: calc(44px + env(safe-area-inset-top, 50px));
  padding-top: calc(44px + var(--safe-area-top, 50px));
  padding-left: 5%;
  padding-right: 5%;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  width: 100%;
  max-width: 100vw;
  box-sizing: border-box;

  // 隐藏滚动条
  ::-webkit-scrollbar {
    display: none !important;
    width: 0 !important;
    height: 0 !important;
    background: transparent !important;
    -webkit-appearance: none !important;
  }

  ::-webkit-scrollbar-track {
    display: none !important;
    width: 0 !important;
    background: transparent !important;
  }

  ::-webkit-scrollbar-thumb {
    display: none !important;
    width: 0 !important;
    background: transparent !important;
  }

  scrollbar-width: none !important;
  -ms-overflow-style: none !important;
}

/* 登录容器 */
.login-container {
  display: flex;
  flex-direction: column;
  min-height: calc(100vh - 88px - constant(safe-area-inset-top) - constant(safe-area-inset-bottom));
  min-height: calc(100vh - 88px - env(safe-area-inset-top) - env(safe-area-inset-bottom));
  justify-content: center;
  padding: 40px 0;
}

/* Logo区域 */
.logo-section {
  text-align: center;
  margin-bottom: 60px;
}

.logo {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
}

.logo-icon {
  font-size: 36px;
  color: white;
}

.app-name {
  display: block;
  font-size: 28px;
  font-weight: 700;
  color: #1a1a1a;
  text-shadow: 0 2px 8px rgba(255, 255, 255, 0.5);
  margin-bottom: 8px;
}

.app-desc {
  font-size: 14px;
  color: rgba(26, 26, 26, 0.7);
  text-shadow: 0 1px 4px rgba(255, 255, 255, 0.5);
}

/* 表单区域 */
.form-section {
  margin-bottom: 40px;
}

.form-card {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.05);
  border-radius: 24px;
  padding: 32px 24px;
}

/* 输入组 */
.input-group {
  margin-bottom: 24px;

  &:last-child {
    margin-bottom: 0;
  }
}

.input-label {
  display: flex;
  align-items: center;
  margin-bottom: 8px;

  .iconfont {
    font-size: 16px;
    color: #8b5cf6;
    margin-right: 8px;
  }

  .label-text {
    font-size: 14px;
    font-weight: 500;
    color: #1a1a1a;
  }
}

.form-input {
  width: 100%;
  height: 48px;
  background: rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.5);
  border-radius: 12px;
  padding: 0 16px;
  font-size: 16px;
  color: #1a1a1a;
  box-sizing: border-box;
  transition: all 0.3s ease;

  &:focus {
    background: rgba(255, 255, 255, 0.6);
    border-color: #8b5cf6;
    outline: none;
    box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.2);
  }

  &.error {
    border-color: #ef4444;
    background: rgba(239, 68, 68, 0.1);
  }

  &::placeholder {
    color: rgba(0, 0, 0, 0.4);
  }
}

.error-text {
  display: block;
  font-size: 12px;
  color: #ef4444;
  margin-top: 4px;
  padding-left: 4px;
}

/* 验证码输入容器 */
.code-input-container {
  display: flex;
  gap: 8px;
  align-items: center;
}

.code-input {
  flex: 1;
  height: 48px;
  background: rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.5);
  border-radius: 12px;
  padding: 0 16px;
  font-size: 16px;
  color: #1a1a1a;
  box-sizing: border-box;
  transition: all 0.3s ease;

  &:focus {
    background: rgba(255, 255, 255, 0.6);
    border-color: #8b5cf6;
    outline: none;
    box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.2);
  }

  &.error {
    border-color: #ef4444;
    background: rgba(239, 68, 68, 0.1);
  }

  &::placeholder {
    color: rgba(0, 0, 0, 0.4);
  }
}

.send-code-button {
  height: 48px;
  padding: 0 16px;
  background: linear-gradient(135deg, #8b5cf6, #a855f7);
  border: none;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 100px;
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
  transition: all 0.3s ease;

  &:active:not(.disabled) {
    transform: scale(0.98);
    box-shadow: 0 2px 8px rgba(139, 92, 246, 0.4);
  }

  &.disabled {
    opacity: 0.6;
    background: rgba(139, 92, 246, 0.5);
    box-shadow: none;
  }

  .button-text {
    font-size: 14px;
    font-weight: 500;
    color: white;
  }

  .loading-icon {
    font-size: 14px;
    color: white;
    margin-right: 4px;
    animation: spin 1s linear infinite;
  }
}

/* 登录按钮 */
.login-button {
  width: 100%;
  height: 52px;
  background: linear-gradient(135deg, #8b5cf6, #a855f7);
  border: none;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 24px rgba(139, 92, 246, 0.3);
  transition: all 0.3s ease;

  &:active:not(.loading) {
    transform: scale(0.98);
    box-shadow: 0 4px 16px rgba(139, 92, 246, 0.4);
  }

  &.loading {
    opacity: 0.8;
  }

  &:disabled {
    opacity: 0.6;
  }
}

.loading-icon {
  font-size: 16px;
  color: white;
  margin-right: 8px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.button-text {
  font-size: 16px;
  font-weight: 600;
  color: white;
}

/* 说明区域 */
.description-section {
  text-align: center;
  margin-bottom: 40px;
}

.description-text {
  font-size: 14px;
  color: rgba(26, 26, 26, 0.7);
  line-height: 1.5;
}
</style>
