// 今日能量页面相关类型定义
import type { ColorItem } from '@/api/fortune'

export interface FortuneDetailData {
  totalScore: number
  dimensions: {
    love: number
    career: number
    wealth: number
    health: number
    relationship: number
  }
  luckyElements: {
    colors: ColorItem[]
    clothing: string[]
    accessories: string[]
    makeup: string[]
  }
  advice: {
    suitable: string[]
    avoid: string[]
  }
}

export interface DimensionItem {
  key: string
  label: string
  icon: string
  color: string
  score: number
}

export interface LuckyElementItem {
  type: string
  label: string
  items: string[]
  icon: string
}

export interface AdviceItem {
  id: string
  icon: string
  text: string
  category: string
  color?: string
}

export interface DateInfo {
  gregorian: string // 公历日期
  lunar: string // 农历信息
}

export interface AdviceCategory {
  type: 'suitable' | 'avoid'
  label: string
  items: AdviceItem[]
}

export interface LifeSuggestion {
  icon: string
  content: string
}

export interface FortuneData {
  // 基础信息
  dateInfo: DateInfo
  totalScore: number
  percentage: number
  peakTime: string
  peakTimeDescription: string
  description: string

  // 五维能量
  dimensions: {
    love: number
    career: number
    wealth: number
    health: number
    relationship: number
  }

  // 宜忌指南
  advice: {
    categories: AdviceCategory[]
    lifeSuggestions: LifeSuggestion[]
  }

  // 幸运元素
  luckyElements: {
    colors: ColorItem[]
    clothing: string[]
    accessories: string[]
    makeup: string[]
  }
}
