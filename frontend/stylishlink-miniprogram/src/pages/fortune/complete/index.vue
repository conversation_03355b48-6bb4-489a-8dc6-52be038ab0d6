<script setup lang="ts">
import { computed, onMounted, provide, ref } from 'vue'
import WuxingCircle from '@/components/business/WuxingCircle.vue'
import PageHeader from '@/components/common/PageHeader.vue'
import { useMenuButton } from '@/composables/useMenuButton'
import { useFortune } from '../composables/useFortune'
import BaziCombination from './components/BaziCombination.vue'
import DetailedFortune from './components/DetailedFortune.vue'
import LuckyAdvice from './components/LuckyAdvice.vue'
import OverallFortune from './components/OverallFortune.vue'
import WuxingAnalysis from './components/WuxingAnalysis.vue'

// 从composable中获取状态和方法
const {
  completeReadingData,
  completeReadingLoading,
  completeReadingError,
  fetchCompleteReading,
} = useFortune()

// 胶囊按钮适配
const { contentOffset12px } = useMenuButton()

// 临时测试：将API数据转换为WuxingCircle组件需要的格式
const directWuxingData = computed(() => {
  if (!completeReadingData.value?.wuxingAnalysis?.elements) {
    return []
  }
  return completeReadingData.value.wuxingAnalysis.elements.map(item => ({
    element: item.element as '金' | '木' | '水' | '火' | '土',
    percentage: item.percentage,
    isHighlight: item.isRizhu, // 日主高亮显示
  }))
})

// 返回上一页
function handleBack() {
  uni.navigateBack()
}

// 🚀 新方案：优化的全局滚动状态管理
const isPageScrolling = ref(false)
let scrollTimer: number | null = null
let lastScrollTime = 0

function handleScroll() {
  const now = Date.now()

  // 🚀 节流：限制滚动状态更新频率
  if (now - lastScrollTime < 16) { // 约60fps
    return
  }
  lastScrollTime = now

  // 🚀 设置滚动状态，应用CSS优化
  if (!isPageScrolling.value) {
    isPageScrolling.value = true
  }

  if (scrollTimer) {
    clearTimeout(scrollTimer)
  }

  // 🚀 滚动结束后恢复正常状态
  scrollTimer = setTimeout(() => {
    isPageScrolling.value = false
    console.warn('页面滚动结束，恢复Canvas正常状态')
  }, 150) // 减少到150ms，更快恢复
}

function handleScrollEnd() {
  // 滚动到边界时，快速恢复正常状态
  if (scrollTimer) {
    clearTimeout(scrollTimer)
  }

  scrollTimer = setTimeout(() => {
    isPageScrolling.value = false
    console.warn('滚动到边界，恢复Canvas正常状态')
  }, 50) // 更快恢复
}

// 🚀 提供全局滚动状态给子组件
provide('isPageScrolling', isPageScrolling)

// 页面加载时获取完整运势解读数据
onMounted(() => {
  // 🔧 iOS Canvas层级问题调试信息
  const systemInfo = uni.getSystemInfoSync()
  const windowInfo = uni.getWindowInfo()
  const menuButtonInfo = uni.getMenuButtonBoundingClientRect()

  console.warn('=== iOS Canvas层级调试信息 ===', {
    platform: systemInfo.platform,
    system: systemInfo.system,
    version: systemInfo.version,
    screenHeight: systemInfo.screenHeight,
    windowHeight: windowInfo.windowHeight,
    statusBarHeight: systemInfo.statusBarHeight,
    safeAreaTop: systemInfo.safeArea?.top || 0,
    safeAreaBottom: systemInfo.safeArea?.bottom || 0,
    menuButtonTop: menuButtonInfo.top,
    menuButtonBottom: menuButtonInfo.bottom,
    menuButtonHeight: menuButtonInfo.height,
    contentOffset: contentOffset12px,
  })

  fetchCompleteReading().then(() => {
    // 数据加载完成后，给Canvas组件额外的时间初始化
    console.warn('完整解读数据加载完成，准备初始化Canvas组件')

    // 检查数据结构
    console.warn('完整解读数据内容:', {
      hasWuxingAnalysis: !!completeReadingData.value?.wuxingAnalysis,
      hasOverallFortune: !!completeReadingData.value?.overallFortune,
      wuxingElements: completeReadingData.value?.wuxingAnalysis?.elements?.length || 0,
      fortuneDecades: completeReadingData.value?.overallFortune?.decadeFortunes?.length || 0,
    })

    // 使用新的API替代废弃的getSystemInfoSync
    const deviceInfo = uni.getDeviceInfo()
    const delay = deviceInfo.platform === 'ios' ? 2000 : 500 // iOS增加延时

    setTimeout(() => {
      console.warn('Canvas组件初始化延时完成')
      // 这里可以触发自定义事件通知子组件重新绘制
    }, delay)
  }).catch((error) => {
    console.error('数据加载失败:', error)
  })
})
</script>

<template>
  <view class="fortune-complete-page">
    <!-- 页面标题 -->
    <PageHeader
      title="运势解读"
      @back="handleBack"
    />

    <!-- 固定背景层 - 与测试页面保持一致 -->
    <view class="fixed-background" />

    <!-- 主滚动容器 - 添加滚动性能优化 -->
    <scroll-view
      class="main-scroll-container"
      scroll-y
      :bounces="false"
      :show-scrollbar="false"
      :style="{
        height: `calc(100vh - ${contentOffset12px})`,
        marginTop: contentOffset12px,
      }"
      @scroll="handleScroll"
      @scrolltoupper="handleScrollEnd"
      @scrolltolower="handleScrollEnd"
    >
      <!-- 加载状态 -->
      <view v-if="completeReadingLoading" class="loading-container">
        <text class="loading-text">
          正在为您解读运势...
        </text>
      </view>

      <!-- 错误状态 -->
      <view v-else-if="completeReadingError" class="error-container">
        <text class="error-text">
          {{ completeReadingError }}
        </text>
        <view class="retry-button" @click="fetchCompleteReading()">
          <text class="retry-text">
            重新获取
          </text>
        </view>
      </view>

      <!-- 主要内容 -->
      <view v-else-if="completeReadingData" class="main-content">
        <!-- 八字组合 -->
        <BaziCombination :bazi-data="completeReadingData.baziCombination" />

        <!-- 🔧 临时测试：直接WuxingCircle组件 -->
        <view v-if="directWuxingData.length > 0" class="direct-wuxing-test">
          <text class="test-title">
            直接WuxingCircle测试
          </text>
          <WuxingCircle
            :wuxing-data="directWuxingData"
            size="large"
            :show-relations="true"
            :show-labels="true"
          />
        </view>

        <!-- 五行分析 -->
        <WuxingAnalysis :wuxing-data="completeReadingData.wuxingAnalysis" />

        <!-- 一生运势 -->
        <OverallFortune :fortune-data="completeReadingData.overallFortune" />

        <!-- 吉运建议 -->
        <LuckyAdvice :advice-data="completeReadingData.luckyAdvice" />

        <!-- 详细运势 -->
        <DetailedFortune :detailed-data="completeReadingData.detailedFortune" />
      </view>
    </scroll-view>
  </view>
</template>

<style lang="scss" scoped>
.fortune-complete-page {
  min-height: 100vh;
  position: relative;
  overflow: hidden;
  width: 100%;
  max-width: 100vw;
  // 移除直接背景设置，改用固定背景层

  // 🔑 关键：创建新的层级上下文，解决iOS Canvas层级问题
  isolation: isolate;

  // iOS触摸优化
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  user-select: none;

  // 确保根容器可以响应触摸
  touch-action: manipulation;
}

// 固定背景层 - 与测试页面保持一致
.fixed-background {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1; /* 使用负z-index确保始终在最底层 */
  background: linear-gradient(180deg, #c2e9fb 0%, #d8c6fd 100%);

  // 强制创建层级上下文，确保iOS兼容性
  transform: translateZ(0);
  -webkit-transform: translateZ(0);

  // 防止背景干扰触摸事件
  pointer-events: none;

  // iOS触摸优化
  -webkit-user-select: none;
  user-select: none;
}

// 主滚动容器 - 与测试页面保持完全一致
.main-scroll-container {
  width: 100%;
  max-width: 100vw;
  box-sizing: border-box;
  // height 和 marginTop 现在通过 :style 动态设置

  // iOS触摸滚动优化
  -webkit-overflow-scrolling: touch;
  touch-action: pan-y;

  // 确保触摸事件正常传递
  pointer-events: auto;

  // 隐藏滚动条
  ::-webkit-scrollbar {
    display: none !important;
    width: 0 !important;
    height: 0 !important;
    background: transparent !important;
    -webkit-appearance: none !important;
  }

  ::-webkit-scrollbar-track {
    display: none !important;
    background: transparent !important;
  }

  ::-webkit-scrollbar-thumb {
    display: none !important;
    background: transparent !important;
  }

  scrollbar-width: none !important;
  -ms-overflow-style: none !important;
}

// 主内容区域 - 与测试页面保持一致的层级设置
.main-content {
  position: relative;
  z-index: 10; /* 🔑 关键：提升z-index确保在背景层之上，与测试页面一致 */
  padding: 0 5% 20px 5%;
  // iOS底部安全区域适配，确保内容不被Home指示器遮挡
  padding-bottom: calc(20px + constant(safe-area-inset-bottom));
  padding-bottom: calc(20px + env(safe-area-inset-bottom));
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  gap: 16px;

  // 🔑 强制创建层级上下文，确保iOS兼容性
  transform: translateZ(0);
  -webkit-transform: translateZ(0);

  // 确保内容区域可交互
  pointer-events: auto;

  // iOS触摸优化
  -webkit-user-select: auto;
  user-select: auto;

  // 确保触摸事件传递
  touch-action: manipulation;
}

// 用户信息卡片
.user-info-card {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.05);
  border-radius: 24px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 8px;
}

.user-avatar {
  width: 48px;
  height: 48px;
  background: rgba(103, 126, 234, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;

  .iconfont {
    font-size: 20px;
    color: #677eea;
  }
}

.user-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.user-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--color-text-primary, #333333);
}

.user-birth {
  font-size: 12px;
  color: var(--color-text-secondary, #666666);
}

// 加载状态
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.loading-text {
  color: var(--color-text-secondary, #666666);
  font-size: 14px;
}

// 错误状态
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 20px;
  gap: 16px;
}

.error-text {
  color: var(--color-text-secondary, #666666);
  font-size: 14px;
  text-align: center;
  line-height: 1.5;
}

.retry-button {
  background: rgba(103, 126, 234, 0.1);
  border: 1px solid rgba(103, 126, 234, 0.3);
  border-radius: 20px;
  padding: 8px 16px;
  cursor: pointer;
  transition: all 0.3s ease;

  &:active {
    background: rgba(103, 126, 234, 0.2);
  }
}

.retry-text {
  font-size: 12px;
  color: #677eea;
  font-weight: 500;
}

// 无数据状态
.no-data-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.no-data-text {
  color: var(--color-text-secondary, #666666);
  font-size: 14px;
}

// 临时测试样式 - 与测试页面保持一致的容器设置
.direct-wuxing-test {
  background: rgba(255, 255, 255, 0.25);
  border-radius: 24px;
  padding: 24px;
  margin-bottom: 16px;
  min-height: 350px; /* 🔑 关键：确保足够高度显示large尺寸的Canvas组件 */

  // 🔑 确保测试容器有正确的层级和渲染上下文
  position: relative;
  z-index: 1;
  transform: translateZ(0);
  -webkit-transform: translateZ(0);

  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.test-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  text-align: center;
  margin-bottom: 20px;
}
</style>
