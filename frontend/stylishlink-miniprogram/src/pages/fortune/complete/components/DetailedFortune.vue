<script setup lang="ts">
import type { DetailedFortune } from '@/api/fortune'
import { computed, ref } from 'vue'

interface Props {
  detailedData: DetailedFortune
}

const props = defineProps<Props>()

// Tab选项
const tabs = [
  { key: 'monthly', name: '月运势' },
  { key: 'yearly', name: '年运势' },
]

const activeTab = ref('monthly')

// 运势类型配置
const fortuneTypes = [
  {
    key: 'career' as const,
    title: '事业运势',
    icon: 'icon-zhichang', // 使用已存在的职场图标
    iconColor: '#fbbf24',
    bgColor: 'rgba(251, 191, 36, 0.1)',
  },
  {
    key: 'wealth' as const,
    title: '财富运势',
    icon: 'icon-shouzhimingxicaifuhongbaoyue', // 使用财富图标
    iconColor: '#10b981',
    bgColor: 'rgba(16, 185, 129, 0.1)',
  },
  {
    key: 'health' as const,
    title: '健康运势',
    icon: 'icon-run', // 使用运动跑步图标代表健康
    iconColor: '#3b82f6',
    bgColor: 'rgba(59, 130, 246, 0.1)',
  },
  {
    key: 'marriage' as const,
    title: '婚姻运势',
    icon: 'icon-xin', // 使用已存在的心形图标
    iconColor: '#ec4899',
    bgColor: 'rgba(236, 72, 153, 0.1)',
  },
  {
    key: 'children' as const,
    title: '子女运势',
    icon: 'icon-xueyuan', // 使用学院图标代表子女教育
    iconColor: '#8b5cf6',
    bgColor: 'rgba(139, 92, 246, 0.1)',
  },
]

// 切换Tab
function switchTab(tabKey: string) {
  activeTab.value = tabKey
}

// 获取当前运势数据
const getCurrentFortuneData = computed(() => {
  return activeTab.value === 'monthly' ? props.detailedData.monthly : props.detailedData.yearly
})
</script>

<template>
  <view class="detailed-fortune-card">
    <!-- Tab切换 -->
    <view class="tab-container">
      <view
        v-for="tab in tabs"
        :key="tab.key"
        class="tab-item"
        :class="{ active: activeTab === tab.key }"
        @click="switchTab(tab.key)"
      >
        <text class="tab-text">
          {{ tab.name }}
        </text>
      </view>
    </view>

    <!-- 运势内容 -->
    <view class="fortune-content">
      <view
        v-for="fortuneType in fortuneTypes"
        :key="fortuneType.key"
        class="fortune-section"
      >
        <!-- 运势类型标题 -->
        <view class="section-header">
          <view
            class="section-icon"
            :style="{ backgroundColor: fortuneType.bgColor }"
          >
            <text
              class="iconfont"
              :class="fortuneType.icon"
              :style="{ color: fortuneType.iconColor }"
            />
          </view>
          <text class="section-title">
            {{ fortuneType.title }}
          </text>
        </view>

        <!-- 运势内容 -->
        <view class="section-content">
          <view class="fortune-details">
            <!-- 运势状态和高亮信息 -->
            <view
              v-if="getCurrentFortuneData[fortuneType.key].status || getCurrentFortuneData[fortuneType.key].highlight"
              class="status-info"
            >
              <text
                v-if="getCurrentFortuneData[fortuneType.key].status"
                class="status-text"
                :style="{ color: getCurrentFortuneData[fortuneType.key].color }"
              >
                {{ getCurrentFortuneData[fortuneType.key].status }}
              </text>
              <text
                v-if="getCurrentFortuneData[fortuneType.key].highlight"
                class="highlight-text"
              >
                {{ getCurrentFortuneData[fortuneType.key].highlight }}
              </text>
            </view>

            <!-- 运势描述 -->
            <view class="content-description">
              <template v-if="Array.isArray(getCurrentFortuneData[fortuneType.key].content)">
                <text
                  v-for="(paragraph, index) in getCurrentFortuneData[fortuneType.key].content"
                  :key="index"
                  class="description-text"
                >
                  {{ paragraph }}
                </text>
              </template>
              <text
                v-else
                class="description-text"
              >
                {{ getCurrentFortuneData[fortuneType.key].content }}
              </text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.detailed-fortune-card {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.05);
  border-radius: 24px;
  padding: 0 0 24px 0;
  margin-bottom: 20px;
}

.tab-container {
  display: flex;
  margin-bottom: 20px;
}

.tab-item {
  flex: 1;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  background: transparent;

  &.active {
    background: rgba(255, 255, 255, 0.4);

    &::after {
      content: '';
      position: absolute;
      bottom: -1px;
      left: 0;
      right: 0;
      height: 2px;
      background: #6366f1;
    }
  }
}

.tab-text {
  font-size: 12px;
  color: var(--color-text-primary, #333333);
  font-weight: 500;
}

.fortune-content {
  padding: 0 24px;

  .content-header {
    margin-bottom: 16px;
  }

  .content-title {
    font-size: 12px;
    font-weight: 500;
    color: var(--color-text-secondary, #666666);
  }
}

.fortune-section {
  margin-bottom: 24px;

  &:last-child {
    margin-bottom: 0;
  }
}

.section-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.section-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;

  .iconfont {
    font-size: 12px;
  }
}

.section-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--color-text-primary, #333333);
}

.section-content {
  padding-left: 32px;
}

.fortune-details {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.status-info {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.status-text {
  font-size: 12px;
  font-weight: 500;
  line-height: 1.4;
}

.highlight-text {
  font-size: 12px;
  color: var(--color-text-secondary, #666666);
  line-height: 1.4;
}

.content-description {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.description-text {
  font-size: 12px;
  color: var(--color-text-secondary, #666666);
  line-height: 1.5;
}

// 响应式调整
@media (max-width: 375px) {
  .detailed-fortune-card {
    padding: 16px;
  }

  .fortune-section {
    margin-bottom: 20px;
  }

  .section-content {
    padding-left: 28px;
  }

  .tab-item {
    height: 28px;
  }

  .tab-text {
    font-size: 11px;
  }
}
</style>
