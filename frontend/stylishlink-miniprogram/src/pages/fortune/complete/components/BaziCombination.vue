<script setup lang="ts">
import type { BaziCombination } from '@/api/fortune'
import { getWuxingColor, getWuxingIcon } from '@/utils/wuxing'

interface Props {
  baziData: BaziCombination
}

const props = defineProps<Props>()

// 天干地支五行映射
const ganzhiWuxing: Record<string, string> = {
  // 天干
  甲: '木',
  乙: '木',
  丙: '火',
  丁: '火',
  戊: '土',
  己: '土',
  庚: '金',
  辛: '金',
  壬: '水',
  癸: '水',
  // 地支
  子: '水',
  丑: '土',
  寅: '木',
  卯: '木',
  辰: '土',
  巳: '火',
  午: '火',
  未: '土',
  申: '金',
  酉: '金',
  戌: '土',
  亥: '水',
}

// 获取干支对应的五行
function getWuxing(ganzhi: string): string {
  return ganzhiWuxing[ganzhi] || '木'
}

// 获取干支对应的五行图标
function getGanzhiWuxingIcon(ganzhi: string): string {
  const wuxing = getWuxing(ganzhi) as any
  return getWuxingIcon(wuxing)
}

// 获取干支对应的五行颜色
function getGanzhiWuxingColor(ganzhi: string): string {
  const wuxing = getWuxing(ganzhi) as any
  return getWuxingColor(wuxing)
}
</script>

<template>
  <view class="bazi-combination-card">
    <view class="card-header">
      <text class="card-title">
        八字组合
      </text>
    </view>

    <view class="bazi-table">
      <!-- 表头行 -->
      <view class="header-row table-row">
        <view class="table-cell">
          <text class="cell-label">
            日期
          </text>
        </view>
        <view class="table-cell">
          <text class="cell-label">
            年柱
          </text>
        </view>
        <view class="table-cell">
          <text class="cell-label">
            月柱
          </text>
        </view>
        <view class="table-cell">
          <text class="cell-label">
            日柱
          </text>
        </view>
        <view class="table-cell">
          <text class="cell-label">
            时柱
          </text>
        </view>
      </view>

      <!-- 天干行 -->
      <view class="table-row">
        <view class="table-cell">
          <text class="cell-label">
            天干
          </text>
        </view>
        <view class="table-cell">
          <view class="ganzhi-item">
            <view
              class="ganzhi-wrapper"
              :style="{
                backgroundColor: `${getGanzhiWuxingColor(props.baziData.year.tiangan)}1A`,
              }"
            >
              <text
                class="iconfont wuxing-icon"
                :class="getGanzhiWuxingIcon(props.baziData.year.tiangan)"
                :style="{ color: getGanzhiWuxingColor(props.baziData.year.tiangan) }"
              />
              <text class="ganzhi-text">
                {{ props.baziData.year.tiangan }}
              </text>
            </view>
          </view>
        </view>
        <view class="table-cell">
          <view class="ganzhi-item">
            <view
              class="ganzhi-wrapper"
              :style="{
                backgroundColor: `${getGanzhiWuxingColor(props.baziData.month.tiangan)}1A`,
              }"
            >
              <text
                class="iconfont wuxing-icon"
                :class="getGanzhiWuxingIcon(props.baziData.month.tiangan)"
                :style="{ color: getGanzhiWuxingColor(props.baziData.month.tiangan) }"
              />
              <text class="ganzhi-text">
                {{ props.baziData.month.tiangan }}
              </text>
            </view>
          </view>
        </view>
        <view class="table-cell">
          <view class="ganzhi-item">
            <view
              class="ganzhi-wrapper"
              :style="{
                backgroundColor: `${getGanzhiWuxingColor(props.baziData.day.tiangan)}1A`,
              }"
            >
              <text
                class="iconfont wuxing-icon"
                :class="getGanzhiWuxingIcon(props.baziData.day.tiangan)"
                :style="{ color: getGanzhiWuxingColor(props.baziData.day.tiangan) }"
              />
              <text class="ganzhi-text">
                {{ props.baziData.day.tiangan }}
              </text>
            </view>
          </view>
        </view>
        <view class="table-cell">
          <view class="ganzhi-item">
            <view
              class="ganzhi-wrapper"
              :style="{
                backgroundColor: `${getGanzhiWuxingColor(props.baziData.hour.tiangan)}1A`,
              }"
            >
              <text
                class="iconfont wuxing-icon"
                :class="getGanzhiWuxingIcon(props.baziData.hour.tiangan)"
                :style="{ color: getGanzhiWuxingColor(props.baziData.hour.tiangan) }"
              />
              <text class="ganzhi-text">
                {{ props.baziData.hour.tiangan }}
              </text>
            </view>
          </view>
        </view>
      </view>

      <!-- 地支行 -->
      <view class="table-row">
        <view class="table-cell">
          <text class="cell-label">
            地支
          </text>
        </view>
        <view class="table-cell">
          <view class="ganzhi-item">
            <view
              class="ganzhi-wrapper"
              :style="{
                backgroundColor: `${getGanzhiWuxingColor(props.baziData.year.dizhi)}1A`,
              }"
            >
              <text
                class="iconfont wuxing-icon"
                :class="getGanzhiWuxingIcon(props.baziData.year.dizhi)"
                :style="{ color: getGanzhiWuxingColor(props.baziData.year.dizhi) }"
              />
              <text class="ganzhi-text">
                {{ props.baziData.year.dizhi }}
              </text>
            </view>
          </view>
        </view>
        <view class="table-cell">
          <view class="ganzhi-item">
            <view
              class="ganzhi-wrapper"
              :style="{
                backgroundColor: `${getGanzhiWuxingColor(props.baziData.month.dizhi)}1A`,
              }"
            >
              <text
                class="iconfont wuxing-icon"
                :class="getGanzhiWuxingIcon(props.baziData.month.dizhi)"
                :style="{ color: getGanzhiWuxingColor(props.baziData.month.dizhi) }"
              />
              <text class="ganzhi-text">
                {{ props.baziData.month.dizhi }}
              </text>
            </view>
          </view>
        </view>
        <view class="table-cell">
          <view class="ganzhi-item">
            <view
              class="ganzhi-wrapper"
              :style="{
                backgroundColor: `${getGanzhiWuxingColor(props.baziData.day.dizhi)}1A`,
              }"
            >
              <text
                class="iconfont wuxing-icon"
                :class="getGanzhiWuxingIcon(props.baziData.day.dizhi)"
                :style="{ color: getGanzhiWuxingColor(props.baziData.day.dizhi) }"
              />
              <text class="ganzhi-text">
                {{ props.baziData.day.dizhi }}
              </text>
            </view>
          </view>
        </view>
        <view class="table-cell">
          <view class="ganzhi-item">
            <view
              class="ganzhi-wrapper"
              :style="{
                backgroundColor: `${getGanzhiWuxingColor(props.baziData.hour.dizhi)}1A`,
              }"
            >
              <text
                class="iconfont wuxing-icon"
                :class="getGanzhiWuxingIcon(props.baziData.hour.dizhi)"
                :style="{ color: getGanzhiWuxingColor(props.baziData.hour.dizhi) }"
              />
              <text class="ganzhi-text">
                {{ props.baziData.hour.dizhi }}
              </text>
            </view>
          </view>
        </view>
      </view>

      <!-- 藏干行 -->
      <view class="table-row">
        <view class="table-cell">
          <text class="cell-label">
            藏干
          </text>
        </view>
        <view class="table-cell">
          <view class="canggan-column">
            <view
              v-for="(gan, index) in props.baziData.year.canggan"
              :key="index"
              class="ganzhi-item"
            >
              <view
                class="ganzhi-wrapper"
                :style="{
                  backgroundColor: `${getGanzhiWuxingColor(gan)}1A`,
                }"
              >
                <text
                  class="iconfont wuxing-icon"
                  :class="getGanzhiWuxingIcon(gan)"
                  :style="{ color: getGanzhiWuxingColor(gan) }"
                />
                <text class="ganzhi-text">
                  {{ gan }}
                </text>
              </view>
            </view>
          </view>
        </view>
        <view class="table-cell">
          <view class="canggan-column">
            <view
              v-for="(gan, index) in props.baziData.month.canggan"
              :key="index"
              class="ganzhi-item"
            >
              <view
                class="ganzhi-wrapper"
                :style="{
                  backgroundColor: `${getGanzhiWuxingColor(gan)}1A`,
                }"
              >
                <text
                  class="iconfont wuxing-icon"
                  :class="getGanzhiWuxingIcon(gan)"
                  :style="{ color: getGanzhiWuxingColor(gan) }"
                />
                <text class="ganzhi-text">
                  {{ gan }}
                </text>
              </view>
            </view>
          </view>
        </view>
        <view class="table-cell">
          <view class="canggan-column">
            <view
              v-for="(gan, index) in props.baziData.day.canggan"
              :key="index"
              class="ganzhi-item"
            >
              <view
                class="ganzhi-wrapper"
                :style="{
                  backgroundColor: `${getGanzhiWuxingColor(gan)}1A`,
                }"
              >
                <text
                  class="iconfont wuxing-icon"
                  :class="getGanzhiWuxingIcon(gan)"
                  :style="{ color: getGanzhiWuxingColor(gan) }"
                />
                <text class="ganzhi-text">
                  {{ gan }}
                </text>
              </view>
            </view>
          </view>
        </view>
        <view class="table-cell">
          <view class="canggan-column">
            <view
              v-for="(gan, index) in props.baziData.hour.canggan"
              :key="index"
              class="ganzhi-item"
            >
              <view
                class="ganzhi-wrapper"
                :style="{
                  backgroundColor: `${getGanzhiWuxingColor(gan)}1A`,
                }"
              >
                <text
                  class="iconfont wuxing-icon"
                  :class="getGanzhiWuxingIcon(gan)"
                  :style="{ color: getGanzhiWuxingColor(gan) }"
                />
                <text class="ganzhi-text">
                  {{ gan }}
                </text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.bazi-combination-card {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.05);
  border-radius: 24px;
  padding: 24px;
  margin-bottom: 16px;
  margin-top: 20px;
}

.card-header {
  margin-bottom: 20px;
}

.card-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--color-text-primary, #333333);
}

.bazi-table {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.table-row {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
  gap: 4px;
  align-items: center;
}

.header-row {
  margin-bottom: 4px;
}

.table-cell {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 32px;
}

.cell-label {
  font-size: 10px;
  color: var(--color-text-secondary, #666666);
  text-align: center;
}

.ganzhi-item {
  display: flex;
  justify-content: center;
  align-items: center;
}

.ganzhi-wrapper {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  gap: 2px;
  padding: 3px 5px;
  border-radius: 10px;
  box-sizing: border-box;
  width: 42px;
  height: 20px;
  white-space: nowrap;
}

.wuxing-icon {
  font-size: 11px;
  flex-shrink: 0;
}

.ganzhi-text {
  font-size: 10px;
  color: var(--color-text-primary, #1a1a1a);
  font-weight: 500;
  flex-shrink: 0;
}

.canggan-column {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

// 响应式调整
@media (max-width: 375px) {
  .bazi-combination-card {
    padding: 16px;
  }

  .table-row {
    gap: 2px;
  }

  .cell-label,
  .ganzhi-text {
    font-size: 9px;
  }

  .ganzhi-wrapper {
    width: 38px;
    height: 18px;
    padding: 2px 4px;
    gap: 1px;
  }

  .wuxing-icon {
    font-size: 9px;
  }

  .ganzhi-text {
    font-size: 9px;
  }
}
</style>
