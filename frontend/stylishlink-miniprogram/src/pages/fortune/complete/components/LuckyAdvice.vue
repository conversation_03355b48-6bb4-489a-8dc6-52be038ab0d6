<script setup lang="ts">
import type { LuckyAdvice } from '@/api/fortune'

interface Props {
  adviceData: LuckyAdvice
}

const props = defineProps<Props>()
</script>

<template>
  <view class="lucky-advice-card">
    <view class="card-header">
      <view class="header-icon">
        <text class="iconfont icon-linggan" />
      </view>
      <text class="card-title">
        吉运提升建议
      </text>
    </view>

    <view class="advice-sections">
      <!-- 服装选择 -->
      <view class="advice-section">
        <view class="section-header">
          <view class="section-icon">
            <text class="iconfont icon-yichu" />
          </view>
          <text class="section-title">
            {{ props.adviceData.clothing.title }}
          </text>
        </view>
        <view class="advice-items">
          <view
            v-for="(item, index) in props.adviceData.clothing.items"
            :key="index"
            class="advice-item simple"
          >
            <text class="advice-label">
              {{ item.label }}
            </text>
            <text class="advice-text">
              {{ item.advice }}
            </text>
          </view>
        </view>
      </view>

      <!-- 首饰佩戴 -->
      <view class="advice-section">
        <view class="section-header">
          <view class="section-icon">
            <text class="iconfont icon-zhubaopeishi" />
          </view>
          <text class="section-title">
            {{ props.adviceData.jewelry.title }}
          </text>
        </view>
        <view class="advice-items">
          <view
            v-for="(item, index) in props.adviceData.jewelry.items"
            :key="index"
            class="advice-item detailed"
          >
            <view class="item-content">
              <text class="advice-label">
                {{ item.label }}
              </text>
              <text class="advice-text">
                {{ item.advice }}
              </text>
            </view>
          </view>
        </view>
      </view>

      <!-- 居家风水 -->
      <view class="advice-section">
        <view class="section-header">
          <view class="section-icon">
            <text class="iconfont icon-jujia" />
          </view>
          <text class="section-title">
            {{ props.adviceData.fengshui.title }}
          </text>
        </view>
        <view class="advice-items">
          <view
            v-for="(item, index) in props.adviceData.fengshui.items"
            :key="index"
            class="advice-item with-icon"
          >
            <view class="item-icon">
              <text
                class="iconfont"
                :class="item.type === 'avoid' ? 'icon-cuowu' : 'icon-zhengque'"
                :style="{ color: item.type === 'avoid' ? '#ef4444' : '#10b981' }"
              />
            </view>
            <text class="advice-text">
              {{ item.advice }}
            </text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.lucky-advice-card {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.05);
  border-radius: 24px;
  padding: 24px;
  margin-bottom: 16px;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 24px;
}

.header-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: rgba(251, 191, 36, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;

  .iconfont {
    font-size: 12px;
    color: #fbbf24;
  }
}

.card-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--color-text-primary, #333333);
}

.advice-sections {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.advice-section {
  .section-header {
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .section-icon {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: rgba(59, 130, 246, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;

    .iconfont {
      font-size: 11px;
      color: #3b82f6;
    }
  }

  .section-title {
    font-size: 12px;
    font-weight: 500;
    color: var(--color-text-primary, #333333);
  }

  .advice-items {
    display: flex;
    flex-direction: column;
    gap: 8px;
    position: relative;
    margin-left: 28px;
  }

  &:last-child .advice-items {
    margin-left: 4px;

    .advice-item.with-icon {
      padding-left: 24px;
    }
  }
}

.advice-item {
  &.simple {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    align-items: center;
  }

  &.detailed {
    .item-content {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }

  &.with-icon {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    position: relative;

    .item-icon {
      position: absolute;
      left: -20px;
      top: 2px;
    }

    .advice-text {
      width: 100%;
    }
  }

  .item-icon {
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;

    .iconfont {
      font-size: 10px;
    }
  }

  .advice-label {
    font-size: 10px;
    color: var(--color-text-secondary, #666666);
    font-weight: 500;
  }

  .advice-text {
    font-size: 10px;
    color: var(--color-text-secondary, #666666);
    line-height: 1.4;
    flex: 1;
  }
}

// 响应式调整
@media (max-width: 375px) {
  .lucky-advice-card {
    padding: 16px;
  }

  .advice-sections {
    gap: 16px;
  }

  .advice-section .advice-items {
    gap: 6px;
  }

  .advice-item {
    &.detailed .item-content {
      flex-direction: column;
      align-items: flex-start;
      gap: 4px;
    }
  }
}
</style>
