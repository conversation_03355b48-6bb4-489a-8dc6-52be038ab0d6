<script setup lang="ts">
import { computed } from 'vue'
import EnergyCircleProgress from '@/components/common/EnergyCircleProgress.vue'

interface Props {
  totalScore?: number
  percentage?: number
  peakTime?: string
  peakTimeDescription?: string
  description?: string
  dimensions?: {
    love: number
    career: number
    wealth: number
    health: number
    relationship: number
  }
}

const props = withDefaults(defineProps<Props>(), {
  totalScore: 0,
  percentage: 0,
  peakTime: '',
  peakTimeDescription: '',
  description: '',
  dimensions: () => ({
    love: 0,
    career: 0,
    wealth: 0,
    health: 0,
    relationship: 0,
  }),
})

// 计算能量等级
const energyLevel = computed(() => {
  if (props.totalScore >= 90)
    return '极佳'
  if (props.totalScore >= 80)
    return '中上能量'
  if (props.totalScore >= 70)
    return '一般'
  if (props.totalScore >= 60)
    return '偏低'
  return '较差'
})

// 维度配置数据 - 横向显示
const dimensionItems = computed(() => [
  {
    key: 'love',
    label: '爱情',
    score: props.dimensions.love,
    color: '#FF6B9D',
  },
  {
    key: 'career',
    label: '事业',
    score: props.dimensions.career,
    color: '#FFA726',
  },
  {
    key: 'wealth',
    label: '财富',
    score: props.dimensions.wealth,
    color: '#66BB6A',
  },
  {
    key: 'health',
    label: '健康',
    score: props.dimensions.health,
    color: '#42A5F5',
  },
  {
    key: 'relationship',
    label: '人际',
    score: props.dimensions.relationship,
    color: '#AB47BC',
  },
])
</script>

<template>
  <view class="energy-reading-card">
    <view class="card-header">
      <text class="card-title">
        能量解读
      </text>
    </view>

    <view class="energy-content">
      <!-- 圆形进度条区域 -->
      <view class="energy-circle-section">
        <!-- 使用新的圆形进度条组件 -->
        <EnergyCircleProgress
          :score="totalScore"
          :percentage="percentage"
          :size="120"
          label="今日能量"
          :show-percentage="false"
        />

        <!-- 能量描述 -->
        <view class="energy-description">
          <text class="percentage-text">
            超过了{{ percentage }}%的用户
          </text>
          <text class="level-text">
            {{ energyLevel }}
          </text>
        </view>
      </view>

      <!-- 五行评分 -->
      <view class="dimension-scores">
        <view
          v-for="item in dimensionItems"
          :key="item.key"
          class="dimension-item"
        >
          <text class="dimension-score" :style="{ color: item.color }">
            {{ item.score }}
          </text>
          <text class="dimension-label">
            {{ item.label }}
          </text>
        </view>
      </view>

      <!-- 时段和建议信息 -->
      <view class="info-section">
        <!-- 能量高峰时段容器 -->
        <view class="peak-time-container">
          <!-- 能量高峰时段 -->
          <view class="info-item peak-time">
            <view class="peak-time-left">
              <view class="info-icon">
                <text class="iconfont icon-qingjiaguanli info-icon-font" />
              </view>
              <text class="info-label">
                能量高峰时段
              </text>
            </view>
            <view class="peak-time-right">
              <text class="time-tag">
                {{ peakTime }}
              </text>
            </view>
          </view>

          <!-- 能量高峰时段描述 -->
          <view v-if="peakTimeDescription" class="peak-time-description">
            {{ peakTimeDescription }}
          </view>
        </view>

        <!-- 详细描述 -->
        <view v-if="description" class="description-text">
          {{ description }}
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.energy-reading-card {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.05);
  border-radius: 20px;
  padding: 24px;
  margin-bottom: 20px;
}

.card-header {
  margin-bottom: 20px;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
}

.energy-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

// 圆形进度条区域
.energy-circle-section {
  display: flex;
  align-items: center;
  gap: 24px;
}

.energy-description {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 8px;
}

.percentage-text {
  font-size: 12px;
  color: #64748b;
}

.level-text {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
}

// 五行评分
.dimension-scores {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0;
}

.dimension-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.dimension-score {
  font-size: 20px;
  font-weight: 700;
  line-height: 1;
}

.dimension-label {
  font-size: 10px;
  color: #64748b;
  font-weight: 500;
}

// 信息区域
.info-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.info-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 0;
  padding: 0;
}

.peak-time-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.info-icon {
  width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.info-icon-font {
  font-size: 18px;
  color: #4A90E2;
}

.info-label {
  font-size: 12px;
  color: #1e293b;
  font-weight: 500;
}

.peak-time-right {
  display: flex;
  align-items: center;
}

.time-tag {
  font-size: 11px;
  color: #1e293b;
  background: rgba(74, 144, 226, 0.1);
  border: 1px solid rgba(74, 144, 226, 0.2);
  border-radius: 12px;
  padding: 4px 12px;
  font-weight: 500;
}

.description-text {
  font-size: 12px;
  line-height: 1.5;
  color: #334155;
  padding: 0;
}

// 能量高峰时段描述
.peak-time-container {
  display: flex;
  flex-direction: column;
  margin-top: 8px;
}

.peak-time-description {
  font-size: 12px;
  line-height: 1.5;
  color: #4A90E2;
  padding: 4px 0 4px 0;
  margin-left: 0;
  font-weight: 500;
  text-align: left;
}

// 响应式调整
@media (max-width: 375px) {
  .energy-circle-section {
    flex-direction: column;
    align-items: center;
    gap: 16px;
  }

  .energy-description {
    text-align: center;
  }

  .percentage-text,
  .level-text {
    font-size: 11px;
  }

  .level-text {
    font-size: 14px;
  }

  .dimension-scores {
    padding: 16px 20px;
    margin: 0 -24px;
  }

  .dimension-score {
    font-size: 18px;
  }

  .dimension-label {
    font-size: 10px;
  }
}
</style>
