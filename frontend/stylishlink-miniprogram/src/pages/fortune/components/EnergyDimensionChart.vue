<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  dimensions: {
    love: number
    career: number
    wealth: number
    health: number
    relationship: number
  }
}

const props = defineProps<Props>()

// 维度配置数据 - 横向显示
const dimensionItems = computed(() => [
  {
    key: 'love',
    label: '爱情',
    score: props.dimensions.love,
    color: '#FF6B9D',
  },
  {
    key: 'career',
    label: '事业',
    score: props.dimensions.career,
    color: '#FFA726',
  },
  {
    key: 'wealth',
    label: '财富',
    score: props.dimensions.wealth,
    color: '#66BB6A',
  },
  {
    key: 'health',
    label: '健康',
    score: props.dimensions.health,
    color: '#42A5F5',
  },
  {
    key: 'relationship',
    label: '人际',
    score: props.dimensions.relationship,
    color: '#AB47BC',
  },
])
</script>

<template>
  <view class="dimension-scores">
    <view
      v-for="item in dimensionItems"
      :key="item.key"
      class="dimension-item"
    >
      <text class="dimension-score" :style="{ color: item.color }">
        {{ item.score }}
      </text>
      <text class="dimension-label">
        {{ item.label }}
      </text>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.dimension-scores {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 0;
  margin: 20px 0;
}

.dimension-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.dimension-score {
  font-size: 20px;
  font-weight: 700;
  line-height: 1;
}

.dimension-label {
  font-size: 10px;
  color: #64748b;
  font-weight: 500;
}

// 响应式调整
@media (max-width: 375px) {
  .dimension-scores {
    padding: 16px 0;
  }

  .dimension-score {
    font-size: 18px;
  }

  .dimension-label {
    font-size: 10px;
  }
}
</style>
