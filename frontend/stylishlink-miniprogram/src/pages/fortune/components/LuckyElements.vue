<script setup lang="ts">
interface Props {
  luckyElements: {
    colors: Array<{
      value: string
      name: string
    }>
    clothing: string[]
    accessories: string[]
    makeup: string[]
  }
}

defineProps<Props>()
</script>

<template>
  <view class="energy-boost-card">
    <view class="card-header">
      <text class="card-title">
        提升能量建议
      </text>
    </view>

    <view class="boost-content">
      <!-- 今日幸运色 -->
      <view class="boost-section">
        <view class="section-header">
          <view class="header-line" />
          <text class="section-title">
            今日幸运色
          </text>
        </view>

        <view class="color-items">
          <view
            v-for="(color, index) in luckyElements.colors"
            :key="index"
            class="color-item"
          >
            <view
              class="color-circle"
              :style="{ backgroundColor: color.value }"
            />
            <text class="color-name">
              {{ color.name }}
            </text>
          </view>
        </view>
      </view>

      <!-- 服饰建议 -->
      <view class="boost-section">
        <view class="section-header">
          <view class="header-line" />
          <text class="section-title">
            服饰建议
          </text>
        </view>

        <view class="advice-content">
          <view class="advice-icon-wrapper clothing-icon">
            <text class="advice-icon iconfont icon-taobaorenshengyichu-fuzhuang_lianyi-xuanzhong" />
          </view>
          <view class="advice-text-list">
            <text
              v-for="(item, index) in luckyElements.clothing"
              :key="index"
              class="advice-text"
            >
              {{ item }}
            </text>
          </view>
        </view>
      </view>

      <!-- 配饰建议 -->
      <view class="boost-section">
        <view class="section-header">
          <view class="header-line" />
          <text class="section-title">
            配饰建议
          </text>
        </view>

        <view class="advice-content">
          <view class="advice-icon-wrapper accessories-icon">
            <text class="advice-icon iconfont icon-zhubaopeishi" />
          </view>
          <view class="advice-text-list">
            <text
              v-for="(item, index) in luckyElements.accessories"
              :key="index"
              class="advice-text"
            >
              {{ item }}
            </text>
          </view>
        </view>
      </view>

      <!-- 妆容建议 -->
      <view class="boost-section">
        <view class="section-header">
          <view class="header-line" />
          <text class="section-title">
            妆容建议
          </text>
        </view>

        <view class="advice-content">
          <view class="advice-icon-wrapper makeup-icon">
            <text class="advice-icon iconfont icon-huazhuangpin" />
          </view>
          <view class="advice-text-list">
            <text
              v-for="(item, index) in luckyElements.makeup"
              :key="index"
              class="advice-text"
            >
              {{ item }}
            </text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
.energy-boost-card {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.05);
  border-radius: 24px;
  padding: 24px;
  margin-bottom: 20px;
}

.card-header {
  margin-bottom: 24px;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
}

.boost-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

// 分类区域
.boost-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.section-header {
  position: relative;
  padding-left: 12px;
  display: flex;
  align-items: center;
  min-height: 20px;
}

.header-line {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 14px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 2px;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #1e293b;
}

// 颜色展示
.color-items {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.4);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.color-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.color-circle {
  width: 22px;
  height: 22px;
  border-radius: 50%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.color-name {
  font-size: 12px;
  color: #1e293b;
  font-weight: 500;
}

// 建议内容
.advice-content {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.4);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.advice-icon-wrapper {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  flex-shrink: 0;

  // 服饰建议 - 紫色（参考首页）
  &.clothing-icon {
    background: rgba(147, 51, 234, 0.8);
  }

  // 配饰建议 - 蓝色（参考首页）
  &.accessories-icon {
    background: rgba(59, 130, 246, 0.8);
  }

  // 妆容建议 - 粉色（参考首页）
  &.makeup-icon {
    background: rgba(236, 72, 153, 0.8);
  }
}

.advice-icon {
  font-size: 16px;
  color: white;
}

.advice-text-list {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.advice-text {
  font-size: 12px;
  line-height: 1.5;
  color: #334155;
  margin: 0;
}

// 响应式调整
@media (max-width: 375px) {
  .color-items {
    gap: 12px;
    padding: 12px;
  }

  .advice-content {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .advice-icon-wrapper {
    align-self: flex-start;
  }

  .advice-text {
    font-size: 11px;
  }
}
</style>
