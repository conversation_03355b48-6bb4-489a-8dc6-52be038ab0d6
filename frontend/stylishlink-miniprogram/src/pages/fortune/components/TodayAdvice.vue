<script setup lang="ts">
import type { AdviceCategory, LifeSuggestion } from '../types'

interface Props {
  advice: {
    categories: AdviceCategory[]
    lifeSuggestions: LifeSuggestion[]
  }
}

defineProps<Props>()

// 获取分类标题的样式
function getCategoryHeaderStyle(type: string) {
  return type === 'suitable' ? 'suitable-header' : 'avoid-header'
}

// 获取事项的样式
function getItemStyle(type: string) {
  return type === 'suitable' ? 'suitable-item' : 'avoid-item'
}

function onImageError(event: any) {
  console.error('Image load error:', event)
}
</script>

<template>
  <view class="advice-guide-card">
    <view class="card-header">
      <text class="card-title">
        宜忌指南
      </text>
    </view>

    <view class="advice-content">
      <!-- 宜忌分类 -->
      <view
        v-for="category in advice.categories"
        :key="category.type"
        class="advice-category"
      >
        <!-- 分类标题 -->
        <view class="category-header" :class="getCategoryHeaderStyle(category.type)">
          <text class="category-title">
            {{ category.label }}
          </text>
        </view>

        <!-- 事项列表 -->
        <view class="items-grid">
          <view
            v-for="item in category.items"
            :key="item.id"
            class="advice-item"
            :class="getItemStyle(category.type)"
          >
            <!-- 支持图片路径或iconfont图标 -->
            <view class="item-icon-container">
              <image
                v-if="item.icon.includes('.png') || item.icon.includes('.jpg') || item.icon.includes('.jpeg') || item.icon.startsWith('@/static/') || item.icon.startsWith('/static/')"
                :src="item.icon"
                class="item-icon-img"
                mode="aspectFit"
                @error="onImageError"
              />
              <text
                v-else
                class="iconfont item-icon"
                :class="item.icon"
              />
            </view>
            <text class="item-text">
              {{ item.text }}
            </text>
          </view>
        </view>
      </view>

      <!-- 生活建议 -->
      <view class="life-suggestions">
        <view class="category-header life-header">
          <text class="category-title">
            生活建议
          </text>
        </view>

        <view class="suggestions-list">
          <view
            v-for="(suggestion, index) in advice.lifeSuggestions"
            :key="index"
            class="suggestion-item"
          >
            <view class="suggestion-icon">
              <!-- 支持图片路径或iconfont图标 -->
              <image
                v-if="suggestion.icon.includes('.png') || suggestion.icon.includes('.jpg') || suggestion.icon.includes('.jpeg') || suggestion.icon.startsWith('@/static/') || suggestion.icon.startsWith('/static/')"
                :src="suggestion.icon"
                class="suggestion-icon-img"
                mode="aspectFit"
                @error="onImageError"
              />
              <text
                v-else-if="suggestion.icon.startsWith('icon-')"
                class="iconfont suggestion-icon-font"
                :class="suggestion.icon"
              />
              <text
                v-else
                class="suggestion-icon-text"
              >
                {{ suggestion.icon }}
              </text>
            </view>
            <text class="suggestion-text">
              {{ suggestion.content }}
            </text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
.advice-guide-card {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.05);
  border-radius: 24px;
  padding: 24px;
  margin-bottom: 20px;
}

.card-header {
  margin-bottom: 24px;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
}

.advice-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

// 分类区域
.advice-category {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.category-header {
  position: relative;
  padding-left: 12px;
  display: flex;
  align-items: center;
  min-height: 20px;

  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 14px;
    border-radius: 2px;
    background: #1bafa2;
  }

  // 忌做事项的竖条颜色
  &.avoid-header::before {
    background: #FF6B6B;
  }

  // 生活建议的竖条颜色
  &.life-header::before {
    background: #45B7D1;
  }
}

.category-title {
  font-size: 14px;
  font-weight: 600;
  color: #1e293b;
}

// 事项网格
.items-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.advice-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.4);
  transition: transform 0.2s ease;

  &:active {
    transform: scale(0.98);
  }

  &.suitable-item {
    background: rgba(78, 205, 196, 0.1);
    border-color: rgba(78, 205, 196, 0.3);
  }

  &.avoid-item {
    background: rgba(255, 107, 107, 0.1);
    border-color: rgba(255, 107, 107, 0.3);
  }
}

.item-icon-container {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.item-icon-img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.item-icon {
  font-size: 12px;
  color: #1bafa2;
  font-weight: 500;

  // iconfont特殊处理
  &.iconfont {
    font-size: 14px;
    color: #1bafa2 !important;
  }
}

// 在avoid-item内的图标使用红色
.avoid-item .item-icon {
  color: #FF6B6B;

  &.iconfont {
    color: #FF6B6B !important;
  }
}

.item-text {
  font-size: 12px;
  color: #1e293b;
  font-weight: 500;
}

// 生活建议
.life-suggestions {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.suggestions-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.suggestion-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  background: rgba(69, 183, 209, 0.1);
  border-radius: 12px;
  border: 1px solid rgba(69, 183, 209, 0.3);
}

.suggestion-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(69, 183, 209, 0.2);
  border-radius: 50%;
  flex-shrink: 0;
  margin-top: 2px;
}

.suggestion-icon-img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.suggestion-icon-font {
  font-size: 14px;
  color: #45B7D1;
}

.suggestion-icon-text {
  font-size: 12px;
  color: #45B7D1;
}

.suggestion-text {
  font-size: 12px;
  line-height: 1.5;
  color: #334155;
  flex: 1;
}

// 响应式调整
@media (max-width: 375px) {
  .items-grid {
    grid-template-columns: 1fr;
  }

  .advice-item {
    padding: 10px;
  }

  .item-text {
    font-size: 11px;
  }

  .suggestion-text {
    font-size: 11px;
  }
}
</style>
