import type { FortuneData } from '../types'
import type { CompleteFortuneReadingResponse, EnergyTrendRequest, TodayEnergyRequest } from '@/api/fortune'
import { computed, ref } from 'vue'
import { getCompleteFortuneReading, getEnergyTrend, getTodayEnergy } from '@/api/fortune'
import { useUserStore } from '@/store/user'

// 模拟API数据获取
const mockFortuneData: FortuneData = {
  // 日期信息
  dateInfo: {
    gregorian: '2023年11月20日',
    lunar: '二月十九 己巳卯 丙戌 水行',
  },

  // 基础能量信息
  totalScore: 78,
  percentage: 82,
  peakTime: '上午8-10点',
  peakTimeDescription: '适合重要会议和决策，思维活跃度高。',
  description: '今日五行能量以金主导，水行偏弱，整体能量场偏向稳定，利于手术等需要集中注意力的活动。但需注意与家人的沟通。',

  // 五维能量数据
  dimensions: {
    love: 65,
    career: 88,
    wealth: 72,
    health: 81,
    relationship: 76,
  },

  // 宜忌指南 - 使用动态生成
  advice: {
    categories: [
      {
        type: 'suitable',
        label: '宜做事项',
        items: [
          {
            id: 'health_s3',
            icon: 'icon-jianshen',
            text: '健身运动',
            category: '健康养生',
          },
          {
            id: 'social_s7',
            icon: 'icon-shequ',
            text: '参与活动',
            category: '社交人际',
          },
          {
            id: 'work_s5',
            icon: 'icon-jiuyeguanli',
            text: '提升技能',
            category: '工作事业',
          },
          {
            id: 'learning_s1',
            icon: 'icon-aixuexi',
            text: '持续学习',
            category: '学习成长',
          },
        ],
      },
      {
        type: 'avoid',
        label: '忌做事项',
        items: [
          {
            id: 'health_a2',
            icon: 'icon-jianshen',
            text: '熬夜晚睡',
            category: '健康养生',
          },
          {
            id: 'social_a4',
            icon: 'icon-shequ',
            text: '言语伤人',
            category: '社交人际',
          },
          {
            id: 'work_a1',
            icon: 'icon-jiuyeguanli',
            text: '工作拖延',
            category: '工作事业',
          },
          {
            id: 'learning_a3',
            icon: 'icon-aixuexi',
            text: '固步自封',
            category: '学习成长',
          },
        ],
      },
    ],
    lifeSuggestions: [
      {
        icon: 'icon-linggan',
        content: '今天会让你徜徉在生活中充满了不顺心，很多来自外界的质疑挑战，缺少安全感。',
      },
      {
        icon: 'icon-xinlizixun',
        content: '关于闲对关乎某些本质的东西的不解，会导致你情绪的明显波动，建议二个人多想想观静，不要太任性而为。',
      },
    ],
  },

  // 幸运元素
  luckyElements: {
    colors: [
      { value: '#B3D8E8', name: '水蓝色' },
      { value: '#E8F5E8', name: '浅青色' },
      { value: '#333333', name: '深黑色' },
    ],
    clothing: [
      '推荐丝绸、雪纺类柔软面料。',
      '上装可选丝质衬衫或针织款，下装适合休闲牛仔裤或阔腿裤。',
    ],
    accessories: [
      '首饰宜选银色、白金系，搭配蓝色水晶或珍珠点缀。',
      '建议佩戴简约的水滴造型耳环或项链，手提包选深蓝色或黑色。',
      '配饰数量控制在2-3件为佳，避免过多千扰能量流动。',
    ],
    makeup: [
      '今日妆容以清透水润为主，底妆轻薄透。',
      '眼部可点蹭淡蓝或淡紫色眼影，增添深度感。',
      '唇妆宜选水润质感的粉色或明珊色，眨红以自然粉嫩为主，打造清新气色。',
    ],
  },
}

// 五行元素配置
const wuxingElements = {
  金: { color: '#E8E8E8' },
  木: { color: '#A8E6CF' },
  水: { color: '#B8C6DB' },
  火: { color: '#FF9A9E' },
  土: { color: '#FFEAA7' },
}

// 模拟完整运势解读数据
const _mockCompleteReading: CompleteFortuneReadingResponse = {
  // 八字组合
  baziCombination: {
    year: {
      tiangan: '乙',
      dizhi: '巳',
      canggan: ['丙', '戊', '庚'],
    },
    month: {
      tiangan: '己',
      dizhi: '卯',
      canggan: ['乙'],
    },
    day: {
      tiangan: '庚',
      dizhi: '子',
      canggan: ['癸'],
    },
    hour: {
      tiangan: '乙',
      dizhi: '酉',
      canggan: ['辛'],
    },
  },

  // 五行分析
  wuxingAnalysis: {
    elements: [
      { element: '火', percentage: 28, isRizhu: false },
      { element: '土', percentage: 22, isRizhu: false },
      { element: '金', percentage: 20, isRizhu: true },
      { element: '水', percentage: 18, isRizhu: false },
      { element: '木', percentage: 12, isRizhu: false },
    ],
    analysis: '命局以火土为主，金为日主略显不足，需要增强金的力量。水木相对较弱，整体五行分布较为均衡，但需注意调和阴阳平衡。',
  },

  // 整体运势 - 使用新的十年运势结构
  overallFortune: {
    currentAge: 0,
    decadeFortunes: [
      {
        decade: 1,
        ageRange: '0-9岁',
        yearRange: '2025-2034',
        score: 78,
        theme: '成长奠基',
        description: '童年时期，身体健康，家庭和睦，为未来发展奠定良好基础。',
        keyEvents: ['启蒙教育', '性格养成'],
      },
      {
        decade: 2,
        ageRange: '10-19岁',
        yearRange: '2035-2044',
        score: 84,
        theme: '求学成长',
        description: '学业运势良好，智慧开启，有贵人相助。建议专注学习，培养兴趣。',
        keyEvents: ['学业成就', '才能显现'],
      },
      {
        decade: 3,
        ageRange: '20-29岁',
        yearRange: '2045-2054',
        score: 85,
        theme: '事业起步',
        description: '踏入社会，事业起步，虽有挑战但机遇并存。需积极进取，把握机会。',
        keyEvents: ['职场初入', '人际拓展'],
      },
      {
        decade: 4,
        ageRange: '30-39岁',
        yearRange: '2055-2064',
        score: 92,
        theme: '发展建设',
        description: '事业发展期，运势上升，有重要突破。感情生活也将有所收获。',
        keyEvents: ['事业突破', '感情收获'],
      },
      {
        decade: 5,
        ageRange: '40-49岁',
        yearRange: '2065-2074',
        score: 93,
        theme: '事业巅峰',
        description: '人生巅峰期，事业财运双收，家庭美满。是人生最辉煌的十年。',
        keyEvents: ['事业巅峰', '财富积累'],
      },
      {
        decade: 6,
        ageRange: '50-59岁',
        yearRange: '2075-2084',
        score: 79,
        theme: '成熟稳定',
        description: '成熟稳定期，事业稳固，财富积累。注重健康，享受家庭生活。',
        keyEvents: ['家庭稳定', '事业成熟'],
      },
      {
        decade: 7,
        ageRange: '60-69岁',
        yearRange: '2085-2094',
        score: 85,
        theme: '收获智慧',
        description: '智慧收获期，经验丰富，德高望重。可考虑传承知识，培养后进。',
        keyEvents: ['智慧传承', '声望提升'],
      },
      {
        decade: 8,
        ageRange: '70-79岁',
        yearRange: '2095-2104',
        score: 71,
        theme: '传承经验',
        description: '传承经验期，享受天伦之乐，关注健康养生。运势平稳安康。',
        keyEvents: ['天伦之乐', '健康养生'],
      },
      {
        decade: 9,
        ageRange: '80-89岁',
        yearRange: '2105-2114',
        score: 57,
        theme: '享受人生',
        description: '享受人生期，儿孙满堂，生活安逸。运势平和，健康是重点。',
        keyEvents: ['安享晚年', '儿孙满堂'],
      },
      {
        decade: 10,
        ageRange: '90-99岁',
        yearRange: '2115-2124',
        score: 59,
        theme: '圆满安康',
        description: '圆满安康期，人生圆满，长寿安康。晚年生活幸福美满。',
        keyEvents: ['人生圆满', '长寿安康'],
      },
    ],
    lifePhase: '成长期',
    currentDecade: 1,
    lifetimeOverview: '综合八字分析，您的一生运势呈波浪式发展趋势，中年期达到人生巅峰，晚年平稳安康。重要转折点出现在30-40岁期间，需把握机遇。',
  },

  // 吉运建议
  luckyAdvice: {
    clothing: {
      title: '服装选择',
      items: [
        { label: '颜色搭配', advice: '多穿金色、白色、银色系服装，有助于增强个人气场' },
        { label: '面料选择', advice: '选择丝绸、棉麻等天然面料，避免过多化纤材质' },
        { label: '款式建议', advice: '简约大方的设计风格更适合，避免过于复杂的装饰' },
      ],
    },
    jewelry: {
      title: '首饰佩戴',
      items: [
        { label: '材质推荐', advice: '黄金、白金首饰最为适宜，可增强财运' },
        { label: '宝石选择', advice: '白水晶、黄水晶有助于提升正能量' },
        { label: '佩戴位置', advice: '左手佩戴手镯，有助于招财进宝' },
      ],
    },
    fengshui: {
      title: '居家风水',
      items: [
        { type: 'recommend', advice: '在家中西方位放置金属装饰品，增强金的力量' },
        { type: 'recommend', advice: '保持室内光线充足，有助于提升阳气' },
        { type: 'avoid', advice: '避免在卧室放置过多绿植，以免木气过旺' },
        { type: 'avoid', advice: '不宜在客厅摆放尖锐物品，影响家庭和谐' },
      ],
    },
  },

  // 详细运势解读
  detailedFortune: {
    monthly: {
      career: {
        status: '稳步上升',
        highlight: '贵人运佳',
        color: '#4CAF50',
        content: ['本月工作运势整体向好，有机会接触到重要项目', '上级对你的表现较为满意，可考虑提出升职加薪', '注意与同事保持良好关系，团队合作将是成功关键'],
      },
      wealth: {
        status: '财运亨通',
        highlight: '投资有利',
        color: '#FF9800',
        content: '正财收入稳定，偏财运也不错。适合进行稳健的投资理财，但要避免高风险项目。',
      },
      health: {
        status: '精神良好',
        highlight: '注意休息',
        color: '#8BC34A',
        content: ['身体状况总体良好，精力充沛', '注意不要过度劳累，保持规律作息', '适当进行户外运动，有助于提升阳气'],
      },
      marriage: {
        status: '感情和睦',
        highlight: '沟通顺畅',
        color: '#E91E63',
        content: '单身者有望遇到心仪对象，已婚者感情稳定。重要的是保持开放的心态，多与伴侣沟通交流。',
      },
      children: {
        status: '亲子关系良好',
        highlight: '教育有方',
        color: '#9C27B0',
        content: ['与子女关系融洽，沟通无障碍', '在教育方面要注意方式方法，以鼓励为主', '适合安排亲子活动，增进感情'],
      },
    },
    yearly: {
      career: {
        status: '突破发展',
        highlight: '机遇众多',
        color: '#2196F3',
        content: ['今年是事业发展的关键年，会有多个重要机会出现', '适合学习新技能，提升个人竞争力', '可考虑跳槽或创业，但要慎重评估风险'],
      },
      wealth: {
        status: '财源广进',
        highlight: '多元收入',
        color: '#4CAF50',
        content: '收入来源将更加多元化，正财偏财都有不错表现。投资理财方面建议稳健为主，可适当配置一些成长性资产。',
      },
      health: {
        status: '健康平稳',
        highlight: '预防为主',
        color: '#009688',
        content: ['整体健康状况良好，但要注意预防季节性疾病', '建议定期体检，及早发现潜在问题', '保持良好的生活习惯，注意饮食均衡'],
      },
      marriage: {
        status: '感情深化',
        highlight: '家庭美满',
        color: '#F44336',
        content: '感情生活将更加丰富多彩，单身者有望步入婚姻殿堂，已婚者家庭和睦。要学会包容理解，共同面对生活挑战。',
      },
      children: {
        status: '成长顺利',
        highlight: '天赋展现',
        color: '#673AB7',
        content: ['子女在学习和成长方面表现出色', '要给予更多关爱和支持，发现并培养他们的天赋', '注意平衡学习和娱乐，让孩子健康快乐成长'],
      },
    },
  },
}

export function useFortune() {
  const loading = ref(false)
  const error = ref<string | null>(null)
  const fortuneData = ref<FortuneData>(mockFortuneData)

  // 完整运势解读数据
  const completeReadingData = ref<CompleteFortuneReadingResponse | null>(null)
  const completeReadingLoading = ref(false)
  const completeReadingError = ref<string | null>(null)

  const userStore = useUserStore()

  // 计算属性：能量等级
  const energyLevel = computed(() => {
    const score = fortuneData.value.totalScore
    if (score >= 90)
      return { level: '极佳', color: '#4CAF50' }
    if (score >= 80)
      return { level: '良好', color: '#8BC34A' }
    if (score >= 70)
      return { level: '一般', color: '#FF9800' }
    if (score >= 60)
      return { level: '偏低', color: '#FFC107' }
    return { level: '较差', color: '#F44336' }
  })

  // 计算属性：维度平均分
  const averageScore = computed(() => {
    const dimensions = fortuneData.value.dimensions
    const total = Object.values(dimensions).reduce((sum, score) => sum + score, 0)
    return Math.round(total / Object.keys(dimensions).length)
  })

  // 计算属性：最佳维度
  const bestDimension = computed(() => {
    const dimensions = fortuneData.value.dimensions
    const entries = Object.entries(dimensions)
    const best = entries.reduce((max, current) =>
      current[1] > max[1] ? current : max,
    )

    const dimensionLabels: Record<string, string> = {
      love: '爱情运势',
      career: '事业运势',
      wealth: '财富运势',
      health: '健康运势',
      relationship: '人际运势',
    }

    return {
      key: best[0],
      label: dimensionLabels[best[0]],
      score: best[1],
    }
  })

  // 获取fortune数据
  const fetchFortuneData = async (userId?: string, date?: string): Promise<void> => {
    loading.value = true
    error.value = null

    try {
      // 调用真实的API接口
      const params: TodayEnergyRequest | undefined = date ? { date } : undefined
      const response = await getTodayEnergy(params)

      // 将API响应转换为FortuneData格式
      fortuneData.value = {
        dateInfo: response.dateInfo,
        totalScore: response.totalScore,
        percentage: response.percentage,
        peakTime: response.peakTime,
        peakTimeDescription: response.peakTimeDescription,
        description: response.description,
        dimensions: response.dimensions,
        advice: response.advice,
        luckyElements: response.luckyElements,
      }
    }
    catch (err) {
      error.value = err instanceof Error ? err.message : '获取运势数据失败'
      console.error('获取运势数据失败:', err)

      // 失败时使用模拟数据作为后备
      console.warn('API调用失败，使用模拟数据')
      fortuneData.value = mockFortuneData
    }
    finally {
      loading.value = false
    }
  }

  // 获取能量趋势数据
  const fetchEnergyTrend = async (params?: EnergyTrendRequest) => {
    try {
      const response = await getEnergyTrend(params)
      return response
    }
    catch (err) {
      console.error('获取能量趋势失败:', err)
      throw err
    }
  }

  // 刷新fortune数据
  const refreshFortune = async (date?: string): Promise<void> => {
    await fetchFortuneData(undefined, date)
  }

  // 刷新宜忌建议
  const refreshAdvice = (): void => {
    // 由于后台直接返回数据，这里可以调用 API 重新获取最新的宜忌建议
    // 或者暂时保持当前数据不变
    console.warn('宜忌建议已更新')
  }

  // 根据生辰八字计算五行属性（模拟实现）
  const calculateWuxing = (birthDate: string): string => {
    // 简化的五行计算逻辑，实际应该基于真实的命理计算
    const elements = Object.keys(wuxingElements)
    const hash = birthDate.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0)
    return elements[hash % elements.length]
  }

  // 获取今日主要五行属性
  const getTodayElement = (): { element: string, config: typeof wuxingElements[keyof typeof wuxingElements] } => {
    const now = new Date()
    const elements = Object.keys(wuxingElements)
    const index = (now.getFullYear() + now.getMonth() + now.getDate()) % elements.length
    const element = elements[index]
    return {
      element,
      config: wuxingElements[element as keyof typeof wuxingElements],
    }
  }

  /**
   * 获取完整运势解读数据
   */
  const fetchCompleteReading = async (): Promise<void> => {
    completeReadingLoading.value = true
    completeReadingError.value = null

    try {
      // 检查用户登录状态
      if (!userStore.isLoggedIn) {
        throw new Error('用户未登录，无法获取运势数据')
      }

      // 调用真实的API接口 - 不需要参数
      const response = await getCompleteFortuneReading()
      completeReadingData.value = response

      console.warn('完整运势解读数据获取成功:', response)
    }
    catch (err) {
      const errorMessage = err instanceof Error ? err.message : '获取完整运势解读数据失败'
      completeReadingError.value = errorMessage
      completeReadingData.value = null

      console.error('获取完整运势解读失败:', err)

      // 显示错误提示
      uni.showToast({
        title: errorMessage,
        icon: 'none',
        duration: 2000,
      })
    }
    finally {
      completeReadingLoading.value = false
    }
  }

  /**
   * 刷新完整运势解读
   */
  const refreshCompleteReading = async (): Promise<void> => {
    await fetchCompleteReading()
  }

  return {
    // 状态
    loading,
    error,
    fortuneData,

    // 计算属性
    energyLevel,
    averageScore,
    bestDimension,

    // 方法
    fetchFortuneData,
    fetchEnergyTrend,
    refreshFortune,
    refreshAdvice,
    calculateWuxing,
    getTodayElement,
    fetchCompleteReading,
    refreshCompleteReading,

    // 常量
    wuxingElements,

    // 完整运势解读数据
    completeReadingData,
    completeReadingLoading,
    completeReadingError,
  }
}
