<script setup lang="ts">
import type { WuxingElement } from '@/utils/wuxing'
import { computed, onMounted } from 'vue'
import PageHeader from '@/components/common/PageHeader.vue'
import { useMenuButton } from '@/composables/useMenuButton'
import { getWuxingColor, getWuxingIcon, getWuxingInfo } from '@/utils/wuxing'
import EnergyScoreDisplay from './components/EnergyScoreDisplay.vue'
import LuckyElements from './components/LuckyElements.vue'
import TodayAdvice from './components/TodayAdvice.vue'
import { useFortune } from './composables/useFortune'

// 使用fortune数据管理
const { fortuneData, loading, fetchFortuneData, getTodayElement } = useFortune()

// 胶囊按钮适配
const { contentOffset12px } = useMenuButton()

// 计算当前五行属性
const currentWuxingInfo = computed(() => {
  // 从农历信息中提取五行信息
  const lunarInfo = fortuneData.value.dateInfo.lunar
  const wuxingMatch = lunarInfo.match(/([金木水火土])行/)

  if (wuxingMatch) {
    const element = wuxingMatch[1] as WuxingElement
    const wuxingInfo = getWuxingInfo(element)
    return {
      element,
      color: getWuxingColor(element),
      icon: getWuxingIcon(element),
      info: wuxingInfo,
    }
  }

  // 如果没有匹配到，使用今日五行计算
  const todayElement = getTodayElement()
  const element = todayElement.element as WuxingElement
  const wuxingInfo = getWuxingInfo(element)
  return {
    element,
    color: getWuxingColor(element),
    icon: getWuxingIcon(element),
    info: wuxingInfo,
  }
})

// 返回首页
function goBack() {
  uni.navigateBack()
}

// 查看完整解读
function viewFullReading() {
  uni.navigateTo({
    url: '/pages/fortune/complete/index',
  })
}

// 查看搭配建议
function viewOutfitAdvice() {
  uni.showToast({
    title: '搭配建议功能开发中',
    icon: 'none',
  })
}

// 页面加载时获取数据
onMounted(() => {
  fetchFortuneData()
})
</script>

<template>
  <view class="fortune-page">
    <!-- 页面标题 -->
    <PageHeader
      title="今日能量"
      @back="goBack"
    />

    <!-- 主滚动容器 -->
    <scroll-view
      class="main-scroll-container"
      scroll-y enable-flex
      :enhanced="true"
      :bounces="false"
      :show-scrollbar="false"
      :style="{
        height: `calc(100vh - ${contentOffset12px})`,
        marginTop: contentOffset12px,
      }"
    >
      <view class="main-content">
        <!-- 日期信息区域 -->
        <view class="date-info-section">
          <view class="date-info-container">
            <view class="date-content">
              <view class="date-primary">
                {{ fortuneData.dateInfo.gregorian }}
              </view>
              <view class="date-lunar">
                {{ fortuneData.dateInfo.lunar }}
              </view>
            </view>

            <!-- 五行信息 -->
            <view class="wuxing-info">
              <view class="wuxing-icon">
                <text
                  class="iconfont wuxing-symbol"
                  :class="currentWuxingInfo.icon"
                  :style="{ color: currentWuxingInfo.color }"
                />
              </view>
              <view class="wuxing-text">
                <text class="wuxing-element">
                  {{ currentWuxingInfo.element }}行
                </text>
              </view>
            </view>
          </view>
        </view>

        <!-- 加载状态 -->
        <view v-if="loading" class="loading-container">
          <text class="loading-text">
            正在加载运势数据...
          </text>
        </view>

        <!-- 主要内容 -->
        <template v-else>
          <!-- 能量解读卡片 -->
          <EnergyScoreDisplay
            :total-score="fortuneData.totalScore"
            :percentage="fortuneData.percentage"
            :peak-time="fortuneData.peakTime"
            :peak-time-description="fortuneData.peakTimeDescription"
            :description="fortuneData.description"
            :dimensions="fortuneData.dimensions"
          />

          <!-- 宜忌指南 -->
          <TodayAdvice :advice="fortuneData.advice" />

          <!-- 提升能量建议 -->
          <LuckyElements :lucky-elements="fortuneData.luckyElements" />
        </template>

        <!-- 底部操作按钮 -->
        <view class="bottom-actions">
          <view class="action-button secondary" @tap="viewFullReading">
            <text class="iconfont icon-shoucang button-icon purple-icon" />
            <text class="button-text">
              能量完整解读
            </text>
          </view>
          <view class="action-button primary" @tap="viewOutfitAdvice">
            <text class="button-text">
              查看搭配建议
            </text>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<style lang="scss" scoped>
.fortune-page {
  min-height: 100vh;
  position: relative;
  overflow: hidden;
  width: 100%;
  max-width: 100vw;
  background: linear-gradient(180deg, #c2e9fb 0%, #d8c6fd 100%);
}

// 主滚动容器
.main-scroll-container {
  width: 100%;
  max-width: 100vw;
  box-sizing: border-box;
  // height 和 marginTop 现在通过 :style 动态设置

  // 隐藏滚动条
  ::-webkit-scrollbar {
    display: none !important;
    width: 0 !important;
    height: 0 !important;
    background: transparent !important;
    -webkit-appearance: none !important;
  }

  ::-webkit-scrollbar-track {
    display: none !important;
    background: transparent !important;
  }

  ::-webkit-scrollbar-thumb {
    display: none !important;
    background: transparent !important;
  }

  scrollbar-width: none !important;
  -ms-overflow-style: none !important;
}

// 主内容区域
.main-content {
  padding: 16px 5% 120px;
  width: 100%;
  max-width: 100vw;
  box-sizing: border-box;
}

// 日期信息区域
.date-info-section {
  margin-bottom: 24px;
}

.date-info-container {
  display: flex;
  align-items: stretch;
  justify-content: flex-start;
  gap: 12px;
}

.date-content {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.date-primary {
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 4px;
}

.date-lunar {
  font-size: 12px;
  color: #64748b;
  display: flex;
  align-items: center;
  gap: 8px;
}

// 五行信息
.wuxing-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 4px;
  flex-shrink: 0;
  padding: 4px 10px;
  background: rgba(255, 255, 255, 0.25);
  border-radius: 6px;
  backdrop-filter: blur(8px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  height: 100%;
}

.wuxing-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.wuxing-symbol {
  font-size: 16px;
  font-weight: bold;
}

.wuxing-text {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.wuxing-element {
  font-size: 10px;
  font-weight: 600;
  color: #64748b;
  line-height: 1.2;
  text-align: center;
}

// 加载状态
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.loading-text {
  font-size: 12px;
  color: #64748b;
}

// 底部操作按钮
.bottom-actions {
  display: flex;
  gap: 12px;
  margin-top: 32px;
  padding: 0 4px;
}

.action-button {
  flex: 1;
  height: 48px;
  border-radius: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-weight: 500;
  transition: all 0.3s ease;

  &.primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
  }

  &.secondary {
    background: rgba(255, 255, 255, 0.4);
    color: #1e293b;
    border: 1px solid rgba(255, 255, 255, 0.5);

    &:active {
      background: rgba(255, 255, 255, 0.6);
    }
  }
}

.button-icon {
  font-size: 14px;
}

.purple-icon {
  color: #764ba2 !important;
}

.button-text {
  font-size: 12px;
}
</style>
