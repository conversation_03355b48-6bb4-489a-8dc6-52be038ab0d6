<!-- 个人中心页面 - 用户信息与设置 -->
<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import TheTabBar from '@/components/layout/TheTabBar.vue'
import { useMenuButton } from '@/composables/useMenuButton'
import { useUserStore } from '@/store/user'

// 胶囊按钮适配
const { contentTop, contentOffset12px } = useMenuButton()

// 使用用户store
const userStore = useUserStore()

// 用户信息数据
const userInfo = ref({
  avatar: '', // 支持真实头像 URL
  nickname: '时尚达人',
  level: 'VIP',
  levelProgress: 75, // 等级进度百分比
  inspirationValue: 1280,
  maxInspiration: 2000, // 下一等级所需灵感值
  outfitCount: 26,
  followCount: 128,
  fanCount: 89,
  wuxingType: '木', // 五行属性
})

// 页面状态
const isLoading = ref(true)
const showLevelDetail = ref(false)

// 计算属性
const inspirationProgress = computed(() => {
  return Math.min((userInfo.value.inspirationValue / userInfo.value.maxInspiration) * 100, 100)
})

const wuxingColor = computed(() => {
  const colors = {
    金: 'linear-gradient(135deg, #f8f9fa, #e9ecef)',
    木: 'linear-gradient(135deg, #a8e6cf, #73c1a8)',
    水: 'linear-gradient(135deg, #b8c6db, #648dae)',
    火: 'linear-gradient(135deg, #ff9a9e, #ff5458)',
    土: 'linear-gradient(135deg, #ffeaa7, #ffc25c)',
  }
  return colors[userInfo.value.wuxingType as keyof typeof colors] || colors.木
})

onMounted(() => {
  // 模拟数据加载
  setTimeout(() => {
    isLoading.value = false
  }, 300)
})

// 编辑个人信息
function editProfile() {
  uni.showToast({
    title: '编辑资料功能开发中',
    icon: 'none',
  })
}

// 设置项点击
function handleSettingClick(type: string) {
  uni.showToast({
    title: `${type}功能开发中`,
    icon: 'none',
  })
}

// 查看统计详情
function viewStats(type: string) {
  uni.showToast({
    title: `查看${type}详情功能开发中`,
    icon: 'none',
  })
}

// 查看等级详情
function toggleLevelDetail() {
  showLevelDetail.value = !showLevelDetail.value
}

// 退出登录
async function handleLogout() {
  uni.showModal({
    title: '退出登录',
    content: '确定要退出登录吗？',
    showCancel: true,
    confirmText: '退出',
    cancelText: '取消',
    confirmColor: '#ff5458',
    success: async (res) => {
      if (res.confirm) {
        await userStore.logout()
      }
    },
  })
}
</script>

<template>
  <view class="profile-page">
    <!-- 页面标题 -->
    <view class="page-header" :style="{ top: contentTop }">
      <text class="page-title">
        我的
      </text>
    </view>

    <!-- 滚动容器 -->
    <scroll-view
      class="main-scroll-container"
      scroll-y
      :enhanced="true"
      :bounces="false"
      :show-scrollbar="false"
      :style="{ paddingTop: contentOffset12px }"
    >
      <!-- 用户信息卡片 -->
      <view class="user-card">
        <!-- 个性化背景 -->
        <view class="user-bg" :style="{ background: wuxingColor }">
          <view class="bg-overlay" />
        </view>

        <!-- 头像和基础信息 -->
        <view class="user-info">
          <view class="avatar-section" @tap="editProfile">
            <view class="avatar">
              <image
                v-if="userInfo.avatar"
                :src="userInfo.avatar"
                class="avatar-image"
                mode="aspectFill"
              />
              <text v-else class="iconfont avatar-placeholder icon-wode" />
            </view>
            <view class="edit-badge">
              <text class="iconfont icon-bianji" />
            </view>
          </view>

          <view class="info-section">
            <view class="nickname-row">
              <text class="nickname">
                {{ userInfo.nickname }}
              </text>
              <view class="level-badge" @tap="toggleLevelDetail">
                <text class="level-text">
                  {{ userInfo.level }}
                </text>
                <text class="iconfont icon-youjiantou level-arrow" />
              </view>
            </view>

            <view class="user-tags">
              <view class="tag wuxing-tag" :style="{ background: wuxingColor }">
                <text class="tag-text">
                  {{ userInfo.wuxingType }}行属性
                </text>
              </view>
              <view class="tag desc-tag">
                <text class="tag-text">
                  时尚达人
                </text>
              </view>
            </view>
          </view>
        </view>

        <!-- 等级进度详情 -->
        <view v-if="showLevelDetail" class="level-detail">
          <view class="level-progress">
            <view class="progress-info">
              <text class="progress-label">
                等级进度
              </text>
              <text class="progress-percent">
                {{ userInfo.levelProgress }}%
              </text>
            </view>
            <view class="progress-bar">
              <view
                class="progress-fill"
                :style="{ width: `${userInfo.levelProgress}%` }"
              />
            </view>
          </view>
        </view>

        <!-- 灵感值展示 -->
        <view class="inspiration-section">
          <view class="inspiration-header">
            <view class="inspiration-icon">
              <text class="iconfont icon-magic" />
            </view>
            <view class="inspiration-info">
              <text class="inspiration-value">
                {{ userInfo.inspirationValue }}
              </text>
              <text class="inspiration-label">
                灵感值
              </text>
            </view>
          </view>

          <view class="inspiration-progress">
            <view class="progress-bar">
              <view
                class="progress-fill inspiration-fill"
                :style="{ width: `${inspirationProgress}%` }"
              />
            </view>
            <text class="progress-text">
              距离下一等级还需 {{ userInfo.maxInspiration - userInfo.inspirationValue }} 灵感值
            </text>
          </view>
        </view>
      </view>

      <!-- 统计数据 -->
      <view class="stats-section">
        <view class="stats-grid">
          <view class="stat-card" @tap="viewStats('完善资料')">
            <view class="stat-icon">
              <text class="iconfont icon-bianji" />
            </view>
            <view class="stat-content">
              <text class="stat-number">
                85%
              </text>
              <text class="stat-label">
                完善资料
              </text>
            </view>
            <view class="stat-trend">
              <text class="trend-icon">
                ↗
              </text>
            </view>
          </view>

          <view class="stat-card" @tap="viewStats('尺寸身材')">
            <view class="stat-icon">
              <text class="iconfont icon-chicun" />
            </view>
            <view class="stat-content">
              <text class="stat-number">
                M
              </text>
              <text class="stat-label">
                尺寸身材
              </text>
            </view>
            <view class="stat-trend">
              <text class="trend-icon">
                ↗
              </text>
            </view>
          </view>

          <view class="stat-card" @tap="viewStats('灵感值')">
            <view class="stat-icon">
              <text class="iconfont icon-magic" />
            </view>
            <view class="stat-content">
              <text class="stat-number">
                {{ userInfo.inspirationValue }}
              </text>
              <text class="stat-label">
                灵感值
              </text>
            </view>
            <view class="stat-trend">
              <text class="trend-icon">
                ↗
              </text>
            </view>
          </view>
        </view>
      </view>

      <!-- 功能菜单 -->
      <view class="menu-section">
        <!-- 我的功能 -->
        <view class="menu-group">
          <view class="group-title">
            <text class="title-text">
              我的功能
            </text>
          </view>

          <view class="menu-list">
            <view class="menu-item" @tap="handleSettingClick('收藏夹')">
              <view class="menu-icon">
                <text class="iconfont icon-shoucang-xian" />
              </view>
              <view class="menu-content">
                <text class="menu-text">
                  收藏夹
                </text>
                <text class="menu-desc">
                  收藏的穿搭和单品
                </text>
              </view>
              <text class="iconfont icon-youjiantou menu-arrow" />
            </view>

            <view class="menu-item" @tap="handleSettingClick('赞过')">
              <view class="menu-icon">
                <text class="iconfont icon-dianzan" />
              </view>
              <view class="menu-content">
                <text class="menu-text">
                  赞过
                </text>
                <text class="menu-desc">
                  点赞过的精彩内容
                </text>
              </view>
              <text class="iconfont icon-youjiantou menu-arrow" />
            </view>

            <view class="menu-item" @tap="handleSettingClick('风格诊断')">
              <view class="menu-icon wuxing-icon" :style="{ background: wuxingColor }">
                <text class="iconfont icon-magic" />
              </view>
              <view class="menu-content">
                <text class="menu-text">
                  风格诊断
                </text>
                <text class="menu-desc">
                  发现你的专属风格
                </text>
              </view>
              <text class="iconfont icon-youjiantou menu-arrow" />
            </view>
          </view>
        </view>

        <!-- 设置功能 -->
        <view class="menu-group">
          <view class="group-title">
            <text class="title-text">
              设置
            </text>
          </view>

          <view class="menu-list">
            <view class="menu-item" @tap="handleSettingClick('隐私设置')">
              <view class="menu-icon">
                <text class="iconfont icon-suo" />
              </view>
              <view class="menu-content">
                <text class="menu-text">
                  隐私设置
                </text>
                <text class="menu-desc">
                  个人信息保护
                </text>
              </view>
              <text class="iconfont icon-youjiantou menu-arrow" />
            </view>

            <view class="menu-item" @tap="handleSettingClick('通知设置')">
              <view class="menu-icon">
                <text class="iconfont icon-xiaoxi" />
              </view>
              <view class="menu-content">
                <text class="menu-text">
                  通知设置
                </text>
                <text class="menu-desc">
                  消息推送管理
                </text>
              </view>
              <text class="iconfont icon-youjiantou menu-arrow" />
            </view>

            <view class="menu-item" @tap="handleSettingClick('帮助中心')">
              <view class="menu-icon">
                <text class="iconfont icon-bangzhu" />
              </view>
              <view class="menu-content">
                <text class="menu-text">
                  帮助中心
                </text>
                <text class="menu-desc">
                  使用指南和FAQ
                </text>
              </view>
              <text class="iconfont icon-youjiantou menu-arrow" />
            </view>

            <view class="menu-item" @tap="handleSettingClick('关于我们')">
              <view class="menu-icon">
                <text class="iconfont icon-xinxi" />
              </view>
              <view class="menu-content">
                <text class="menu-text">
                  关于我们
                </text>
                <text class="menu-desc">
                  版本信息和团队
                </text>
              </view>
              <text class="iconfont icon-youjiantou menu-arrow" />
            </view>

            <!-- 退出登录按钮 -->
            <view class="menu-item" @tap="handleLogout">
              <view class="menu-icon">
                <text class="iconfont icon-tuichu" />
              </view>
              <view class="menu-content">
                <text class="menu-text">
                  退出登录
                </text>
                <text class="menu-desc">
                  注销当前账户
                </text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 底部导航栏 -->
    <TheTabBar />
  </view>
</template>

<style lang="scss" scoped>
.profile-page {
  position: relative;
  min-height: 100vh;
  background: linear-gradient(135deg, #c2e9fb 0%, #d8c6fd 100%);
  width: 100%;
  max-width: 100vw;
  overflow: hidden;
}

/* 页面标题 */
.page-header {
  position: fixed;
  // top位置现在由useMenuButton() composable动态计算提供
  left: 5%;
  z-index: 100;
  height: 28px;
  display: flex;
  align-items: center;
}

.page-title {
  font-size: 18px;
  font-weight: 600;
  color: white;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 滚动容器 */
.main-scroll-container {
  height: calc(100vh - var(--bottom-nav-height, 68px));
  // padding-top现在由useMenuButton() composable动态计算提供
  padding-left: 5%;
  padding-right: 5%;
  padding-bottom: 24px; // 增加底部间距，确保可以滚动到退出登录按钮
  width: 100%;
  max-width: 100vw;
  box-sizing: border-box;

  // 微信小程序隐藏滚动条 - 多重方案确保兼容性
  ::-webkit-scrollbar {
    display: none !important;
    width: 0 !important;
    height: 0 !important;
    background: transparent !important;
    -webkit-appearance: none !important;
  }

  ::-webkit-scrollbar-track {
    display: none !important;
    width: 0 !important;
    background: transparent !important;
  }

  ::-webkit-scrollbar-thumb {
    display: none !important;
    width: 0 !important;
    background: transparent !important;
  }

  ::-webkit-scrollbar-corner {
    display: none !important;
    background: transparent !important;
  }

  // 额外隐藏滚动条样式
  scrollbar-width: none !important; // Firefox
  scrollbar-color: transparent transparent !important; // Firefox滚动条颜色
  -ms-overflow-style: none !important; // IE
  overflow: -moz-scrollbars-none !important; // 老版本Firefox

  // 真机特殊处理
  &::-webkit-scrollbar {
    width: 0px !important;
    background: transparent !important;
  }
}

/* 用户信息卡片 */
.user-card {
  position: relative;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.85) 0%,
    rgba(255, 255, 255, 0.65) 50%,
    rgba(255, 255, 255, 0.85) 100%
  );
  border-radius: 24px;
  padding: 24px;
  margin-bottom: 16px;
  overflow: hidden;
  box-shadow:
    0 -8px 32px rgba(0, 0, 0, 0.08),
    0 -2px 8px rgba(0, 0, 0, 0.03),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.6);
}

/* 个性化背景 */
.user-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 120px;
  opacity: 0.1;
  border-radius: 24px 24px 0 0;
}

.bg-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(180deg, transparent 0%, rgba(255, 255, 255, 0.8) 100%);
}

/* 用户基础信息 */
.user-info {
  position: relative;
  display: flex;
  align-items: flex-start;
  margin-bottom: 20px;
  z-index: 2;
}

.avatar-section {
  position: relative;
  margin-right: 16px;
}

.avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 3px solid rgba(255, 255, 255, 0.8);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.avatar-image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}

.avatar-placeholder {
  font-size: 36px;
  color: #8b5cf6;
}

.edit-badge {
  position: absolute;
  bottom: -2px;
  right: -2px;
  width: 28px;
  height: 28px;
  background: #8b5cf6;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);

  .iconfont {
    font-size: 12px;
    color: white;
  }
}

.info-section {
  flex: 1;
  padding-top: 4px;
}

.nickname-row {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.nickname {
  font-size: 20px;
  font-weight: 600;
  color: #1a1a1a;
  margin-right: 12px;
}

.level-badge {
  display: flex;
  align-items: center;
  padding: 4px 12px;
  background: linear-gradient(135deg, #ffeaa7, #ffc25c);
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.95);
  }
}

.level-text {
  font-size: 12px;
  font-weight: 600;
  color: #333;
  margin-right: 4px;
}

.level-arrow {
  font-size: 10px;
  color: #666;
}

.user-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tag {
  padding: 6px 12px;
  border-radius: 16px;

  .tag-text {
    font-size: 12px;
    font-weight: 500;
  }
}

.wuxing-tag {
  .tag-text {
    color: white;
  }
}

.desc-tag {
  background: rgba(139, 92, 246, 0.1);

  .tag-text {
    color: #8b5cf6;
  }
}

/* 等级进度详情 */
.level-detail {
  position: relative;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid rgba(139, 92, 246, 0.2);
  z-index: 2;
}

.level-progress {
  .progress-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;

    .progress-label {
      font-size: 12px;
      color: #666;
      font-weight: 500;
    }

    .progress-percent {
      font-size: 12px;
      color: #8b5cf6;
      font-weight: 600;
    }
  }

  .progress-bar {
    height: 6px;
    background: rgba(139, 92, 246, 0.1);
    border-radius: 3px;
    overflow: hidden;

    .progress-fill {
      height: 100%;
      background: linear-gradient(90deg, #8b5cf6, #a855f7);
      border-radius: 3px;
      transition: width 0.6s ease;
    }
  }
}

/* 灵感值区域 */
.inspiration-section {
  position: relative;
  border-top: 1px solid rgba(139, 92, 246, 0.2);
  padding-top: 20px;
  z-index: 2;
}

.inspiration-header {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
}

.inspiration-icon {
  width: 44px;
  height: 44px;
  background: linear-gradient(135deg, #8b5cf6, #a855f7);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  box-shadow: 0 4px 16px rgba(139, 92, 246, 0.3);

  .iconfont {
    font-size: 20px;
    color: white;
  }
}

.inspiration-info {
  text-align: center;
}

.inspiration-value {
  display: block;
  font-size: 28px;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 2px;
}

.inspiration-label {
  font-size: 14px;
  color: #8b5cf6;
  font-weight: 500;
}

.inspiration-progress {
  .progress-bar {
    height: 8px;
    background: rgba(139, 92, 246, 0.1);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 8px;

    .inspiration-fill {
      height: 100%;
      background: linear-gradient(90deg, #8b5cf6, #a855f7, #c084fc);
      border-radius: 4px;
      transition: width 0.8s ease;
    }
  }

  .progress-text {
    font-size: 10px;
    color: #666;
    text-align: center;
    display: block;
  }
}

/* 统计数据 */
.stats-section {
  margin-bottom: 16px;
}

.stats-grid {
  display: flex;
  gap: 12px;
}

.stat-card {
  flex: 1;
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.05);
  border-radius: 16px;
  padding: 12px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &:active {
    transform: scale(0.98);
  }
}

.stat-icon {
  width: 32px;
  height: 32px;
  background: rgba(139, 92, 246, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 8px;

  .iconfont {
    font-size: 16px;
    color: #8b5cf6;
  }
}

.stat-content {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 20px;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 2px;
}

.stat-label {
  font-size: 10px;
  color: #666;
  font-weight: 500;
}

.stat-trend {
  position: absolute;
  top: 8px;
  right: 8px;

  .trend-icon {
    font-size: 12px;
    color: #10b981;
    font-weight: 600;
  }
}

/* 功能菜单 */
.menu-section {
  .menu-group {
    margin-bottom: 16px;
  }

  .group-title {
    padding: 0 4px 12px;
  }

  .title-text {
    font-size: 16px;
    font-weight: 600;
    color: white;
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .menu-list {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.05);
    border-radius: 16px;
    overflow: hidden;
  }

  .menu-item {
    display: flex;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid rgba(139, 92, 246, 0.08);
    transition: all 0.3s ease;
    position: relative;

    &:last-child {
      border-bottom: none;
    }

    &:active {
      background: rgba(139, 92, 246, 0.1);
      transform: scale(0.98);
    }

    // 添加左侧装饰线
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 3px;
      height: 0;
      background: linear-gradient(135deg, #8b5cf6, #a855f7);
      border-radius: 0 3px 3px 0;
      transition: height 0.3s ease;
    }

    &:active::before {
      height: 40%;
    }
  }

  .menu-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(139, 92, 246, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16px;
    transition: all 0.3s ease;

    .iconfont {
      font-size: 18px;
      color: #8b5cf6;
    }
  }

  .wuxing-icon {
    background: rgba(255, 255, 255, 0.9);

    .iconfont {
      color: white;
    }
  }

  .menu-content {
    flex: 1;
  }

  .menu-text {
    display: block;
    font-size: 14px;
    color: #1a1a1a;
    font-weight: 600;
    margin-bottom: 2px;
  }

  .menu-desc {
    font-size: 11px;
    color: #666;
    font-weight: 400;
    line-height: 1.3;
  }

  .menu-arrow {
    font-size: 14px;
    color: #8b5cf6;
    transition: all 0.3s ease;
  }

  .menu-item:active .menu-arrow {
    transform: translateX(4px);
  }

  // 退出登录按钮特殊样式
  .menu-item:has(.icon-tuichu) {
    .menu-icon {
      background: rgba(255, 84, 88, 0.1);

      .iconfont {
        color: #ff5458;
      }
    }

    .menu-text {
      color: #ff5458;
    }

    &:active {
      background: rgba(255, 84, 88, 0.1);
    }

    &::before {
      background: linear-gradient(135deg, #ff5458, #ff9a9e);
    }
  }
}
</style>
