@charset "UTF-8";
/**
 * 微信小程序兼容性样式
 * 处理不支持或有兼容性问题的CSS属性
 * 注意：WXSS不支持通配符选择器*和属性选择器[attr]
 */
/* 毛玻璃效果兼容性处理 */
.backdrop-filter-fallback {
  /* 为不支持backdrop-filter的环境提供fallback */
  background: rgba(255, 255, 255, 0.8);
}

/* 小程序环境下的按钮点击反馈 */
.clickable {
  transition: all 0.2s ease;
}

.clickable:active {
  transform: scale(0.95);
  opacity: 0.8;
}

/* 禁用状态下的样式（仅使用类选择器） */
.disabled {
  opacity: 0.6;
  pointer-events: none;
}

/* 常用交互元素的点击反馈 */
.btn,
.button,
button {
  min-height: 44px;
  /* 确保最小点击区域 */
  min-width: 44px;
  transition: all 0.2s ease;
}

.btn:active,
.button:active,
button:active {
  transform: scale(0.95);
  opacity: 0.8;
}

/* 滚动容器优化 */
.scroll-container {
  -webkit-overflow-scrolling: touch;
}
