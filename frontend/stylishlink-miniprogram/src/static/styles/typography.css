/* 基础字体设置 */
page {
  font-family: -apple-system, BlinkMacSystemFont, 'Inter', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  line-height: 1.5;
}

/* 标题样式 */
.text-h1 {
  font-size: 32px;
  font-weight: 700;
  letter-spacing: -0.02em;
}

.text-h2 {
  font-size: 24px;
  font-weight: 600;
  letter-spacing: -0.01em;
}

/* 页面标题 */
.page-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--color-text-primary);
}

/* 卡片标题 */
.card-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--color-text-primary);
}

/* 正文内容 */
.body-text {
  font-size: 12px;
  font-weight: 400;
  color: var(--color-text-secondary);
}

/* 辅助说明文本 */
.helper-text {
  font-size: 10px;
  font-weight: 400;
  color: var(--color-text-tertiary);
}

/* 字重工具类 */
.font-bold { font-weight: 700; }
.font-semibold { font-weight: 600; }
.font-medium { font-weight: 500; }
.font-regular { font-weight: 400; }

/* 文本对齐 */
.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }

/* 文本截断 */
.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 多行文本截断 */
.text-truncate-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
