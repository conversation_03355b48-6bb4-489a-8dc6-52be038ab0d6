/* 卡片基础样式 */
.enhanced-glass {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.05);
  border-radius: var(--radius-xl);
  padding: var(--spacing-md);
}

/* 按钮基础样式 */
.glass-button {
  background: rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.5);
  border-radius: var(--radius-md);
  padding: var(--spacing-sm) var(--spacing-md);
  transition: var(--transition-base);
  font-size: 14px;
  font-weight: 500;
  color: var(--color-text-primary);
  text-align: center;
  cursor: pointer;
}

.glass-button:hover {
  background: rgba(255, 255, 255, 0.6);
}

.glass-button:active {
  transform: scale(0.98);
}

/* 功能入口图标 */
.function-entry {
  width: 50px;
  height: 50px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transition: transform 0.2s ease;
}

.function-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(8px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  aspect-ratio: 1;
}

/* 分类标签 */
.category-tag {
  height: 32px;
  border-radius: var(--radius-full);
  padding: 0 var(--spacing-md);
  background: rgba(255, 255, 255, 0.3);
  color: var(--color-text-secondary);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  transition: var(--transition-base);
}

.category-tag.active {
  background: rgba(255, 255, 255, 0.5);
  color: var(--color-text-primary);
}

/* 列表项 */
.list-item {
  padding: var(--spacing-md);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  transition: var(--transition-base);
}

.list-item:hover {
  background: rgba(255, 255, 255, 0.1);
}

/* 输入框 */
.input-field {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-md);
  padding: var(--spacing-sm) var(--spacing-md);
  color: var(--color-text-primary);
  transition: var(--transition-base);
}

.input-field:focus {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
  outline: none;
}

/* 加载动画 */
.loading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top-color: var(--color-primary);
  border-radius: 50%;
  animation: spin 0.8s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}
