/* 
 * 全局安全区域适配样式
 * 统一管理所有页面的安全区域配置，简化维护
 */

/* 
 * 2025年最佳实践：安全区域混合器
 * 优先使用CSS环境变量，智能fallback机制
 */
@mixin safe-area-top($base-padding: 16px) {
  /* 
   * 多重安全保障策略（按优先级排序）：
   * 1. env() - 现代浏览器和新版微信小程序
   * 2. constant() - 旧版Safari兼容
   * 3. var() - 自定义JavaScript计算的动态fallback
   */
  padding-top: calc(#{$base-padding} + var(--safe-area-top, 20px));
  padding-top: calc(#{$base-padding} + constant(safe-area-inset-top, var(--safe-area-top, 20px)));
  padding-top: calc(#{$base-padding} + env(safe-area-inset-top, var(--safe-area-top, 20px)));
}

@mixin safe-area-bottom($base-padding: 16px) {
  /* 底部安全区域多重保障 */
  padding-bottom: #{$base-padding};
  padding-bottom: calc(#{$base-padding} + constant(safe-area-inset-bottom, 0px));
  padding-bottom: calc(#{$base-padding} + env(safe-area-inset-bottom, 0px));
}

/* 全局安全区域样式类 */
.safe-area-top-base {
  @include safe-area-top(16px);
}

.safe-area-top-page {
  @include safe-area-top(44px); /* 有页面标题的页面 */
}

.safe-area-top-complex {
  @include safe-area-top(100px); /* 复杂布局页面（如用户信息页） */
}

.safe-area-bottom-base {
  @include safe-area-bottom(16px);
}

.safe-area-bottom-page {
  @include safe-area-bottom(0px);
}

/* 页面头部标题安全区域 */
.page-header-safe {
  position: fixed;
  top: calc(constant(safe-area-inset-top, 50px) + 16px);
  top: calc(env(safe-area-inset-top, 50px) + 16px);
  top: calc(var(--safe-area-top, 50px) + 16px);
  left: 5%;
  z-index: 100;
  height: 28px;
  display: flex;
  align-items: center;
}

/* 主滚动容器安全区域（标准页面布局） */
.main-scroll-safe {
  height: calc(100vh - var(--bottom-nav-height, 68px));
  
  /* 为有标题的页面预留标题空间 */
  &.with-title {
    @include safe-area-top(44px);
  }
  
  /* 为首页等无标题页面 */
  &.no-title {
    @include safe-area-top(16px);
  }
  
  /* 为复杂布局页面 */
  &.complex-layout {
    @include safe-area-top(100px);
  }
}

/* 浮动按钮安全区域 */
.floating-button-safe {
  &.top-position {
    top: calc(60px + constant(safe-area-inset-top, 50px));
    top: calc(60px + env(safe-area-inset-top, 50px));
    top: calc(60px + var(--safe-area-top, 50px));
  }
  
  &.bottom-position {
    bottom: calc(96px + constant(safe-area-inset-bottom, 20px));
    bottom: calc(96px + env(safe-area-inset-bottom, 20px));
  }
}

/* TabBar 安全区域 */
.tab-bar-safe {
  @include safe-area-bottom(0px);
}

/* DevTools 组件安全区域 */
.dev-tools-safe {
  &.top-left, &.top-right {
    top: calc(var(--safe-area-top, 50px) + 16px);
    top: calc(var(--status-bar-height, 50px) + 16px);
  }
  
  &.bottom-left, &.bottom-right {
    bottom: calc(16px + constant(safe-area-inset-bottom, 20px));
    bottom: calc(16px + env(safe-area-inset-bottom, 20px));
  }
}

/* 特殊页面布局 */

/* 用户信息页面步骤指示器 */
.step-indicators-safe {
  position: fixed;
  top: calc(44px + constant(safe-area-inset-top, 50px) + 16px);
  top: calc(44px + env(safe-area-inset-top, 50px) + 16px);
  top: calc(44px + var(--safe-area-top, 50px) + 16px);
  left: 5%;
  right: 5%;
  z-index: 90;
  display: flex;
  justify-content: center;
  gap: 16px;
} 