/* 五行基础色彩 */
.wuxing-jin {
  background: linear-gradient(135deg, #ffffff, #f0f0f0);
  color: #1f2937;
}

.wuxing-mu {
  background: linear-gradient(135deg, #a8e6cf, #73c1a8);
  color: #1f2937;
}

.wuxing-shui {
  background: linear-gradient(135deg, #b8c6db, #648dae);
  color: #ffffff;
}

.wuxing-huo {
  background: linear-gradient(135deg, #ff9a9e, #ff5458);
  color: #ffffff;
}

.wuxing-tu {
  background: linear-gradient(135deg, #ffeaa7, #ffc25c);
  color: #1f2937;
}

/* 五行能量标签 */
.wuxing-tag {
  display: inline-flex;
  align-items: center;
  padding: 4px 12px;
  border-radius: var(--radius-full);
  font-size: 12px;
  font-weight: 500;
}

.wuxing-tag.jin { background: linear-gradient(135deg, #ffffff, #f0f0f0); }
.wuxing-tag.mu { background: linear-gradient(135deg, #a8e6cf, #73c1a8); }
.wuxing-tag.shui { background: linear-gradient(135deg, #b8c6db, #648dae); }
.wuxing-tag.huo { background: linear-gradient(135deg, #ff9a9e, #ff5458); }
.wuxing-tag.tu { background: linear-gradient(135deg, #ffeaa7, #ffc25c); }

/* 五行能量卡片 */
.wuxing-card {
  border-radius: var(--radius-xl);
  padding: var(--spacing-md);
  backdrop-filter: blur(10px);
  box-shadow: var(--shadow-md);
  transition: var(--transition-base);
}

.wuxing-card.jin {
  background: rgba(255, 255, 255, 0.25);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.wuxing-card.mu {
  background: rgba(168, 230, 207, 0.25);
  border: 1px solid rgba(168, 230, 207, 0.3);
}

.wuxing-card.shui {
  background: rgba(184, 198, 219, 0.25);
  border: 1px solid rgba(184, 198, 219, 0.3);
}

.wuxing-card.huo {
  background: rgba(255, 154, 158, 0.25);
  border: 1px solid rgba(255, 154, 158, 0.3);
}

.wuxing-card.tu {
  background: rgba(255, 234, 167, 0.25);
  border: 1px solid rgba(255, 234, 167, 0.3);
}

/* 五行能量进度条 */
.wuxing-progress {
  width: 100%;
  height: 4px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.wuxing-progress-bar {
  height: 100%;
  border-radius: var(--radius-full);
  transition: width 0.3s ease;
}

.wuxing-progress-bar.jin { background: linear-gradient(90deg, #ffffff, #f0f0f0); }
.wuxing-progress-bar.mu { background: linear-gradient(90deg, #a8e6cf, #73c1a8); }
.wuxing-progress-bar.shui { background: linear-gradient(90deg, #b8c6db, #648dae); }
.wuxing-progress-bar.huo { background: linear-gradient(90deg, #ff9a9e, #ff5458); }
.wuxing-progress-bar.tu { background: linear-gradient(90deg, #ffeaa7, #ffc25c); }

/* 五行能量图标 */
.wuxing-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  transition: var(--transition-base);
}

.wuxing-icon.jin { background: linear-gradient(135deg, #ffffff, #f0f0f0); }
.wuxing-icon.mu { background: linear-gradient(135deg, #a8e6cf, #73c1a8); }
.wuxing-icon.shui { background: linear-gradient(135deg, #b8c6db, #648dae); }
.wuxing-icon.huo { background: linear-gradient(135deg, #ff9a9e, #ff5458); }
.wuxing-icon.tu { background: linear-gradient(135deg, #ffeaa7, #ffc25c); }
