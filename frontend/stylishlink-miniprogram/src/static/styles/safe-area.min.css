.safe-area-top-base{padding-top:calc(16px + var(--safe-area-top, 20px));padding-top:calc(16px + constant(safe-area-inset-top, var(--safe-area-top, 20px)));padding-top:calc(16px + env(safe-area-inset-top, var(--safe-area-top, 20px)))}.safe-area-top-page{padding-top:calc(44px + var(--safe-area-top, 20px));padding-top:calc(44px + constant(safe-area-inset-top, var(--safe-area-top, 20px)));padding-top:calc(44px + env(safe-area-inset-top, var(--safe-area-top, 20px)))}.safe-area-top-complex{padding-top:calc(100px + var(--safe-area-top, 20px));padding-top:calc(100px + constant(safe-area-inset-top, var(--safe-area-top, 20px)));padding-top:calc(100px + env(safe-area-inset-top, var(--safe-area-top, 20px)))}.safe-area-bottom-base{padding-bottom:16px;padding-bottom:calc(16px + constant(safe-area-inset-bottom, 0px));padding-bottom:calc(16px + env(safe-area-inset-bottom, 0px))}.safe-area-bottom-page{padding-bottom:0px;padding-bottom:calc(0px + constant(safe-area-inset-bottom, 0px));padding-bottom:calc(0px + env(safe-area-inset-bottom, 0px))}.page-header-safe{position:fixed;top:calc(constant(safe-area-inset-top, 50px) + 16px);top:calc(env(safe-area-inset-top, 50px) + 16px);top:calc(var(--safe-area-top, 50px) + 16px);left:5%;z-index:100;height:28px;display:flex;align-items:center}.main-scroll-safe{height:calc(100vh - var(--bottom-nav-height, 68px))}.main-scroll-safe.with-title{padding-top:calc(44px + var(--safe-area-top, 20px));padding-top:calc(44px + constant(safe-area-inset-top, var(--safe-area-top, 20px)));padding-top:calc(44px + env(safe-area-inset-top, var(--safe-area-top, 20px)))}.main-scroll-safe.no-title{padding-top:calc(16px + var(--safe-area-top, 20px));padding-top:calc(16px + constant(safe-area-inset-top, var(--safe-area-top, 20px)));padding-top:calc(16px + env(safe-area-inset-top, var(--safe-area-top, 20px)))}.main-scroll-safe.complex-layout{padding-top:calc(100px + var(--safe-area-top, 20px));padding-top:calc(100px + constant(safe-area-inset-top, var(--safe-area-top, 20px)));padding-top:calc(100px + env(safe-area-inset-top, var(--safe-area-top, 20px)))}.floating-button-safe.top-position{top:calc(60px + constant(safe-area-inset-top, 50px));top:calc(60px + env(safe-area-inset-top, 50px));top:calc(60px + var(--safe-area-top, 50px))}.floating-button-safe.bottom-position{bottom:calc(96px + constant(safe-area-inset-bottom, 20px));bottom:calc(96px + env(safe-area-inset-bottom, 20px))}.tab-bar-safe{padding-bottom:0px;padding-bottom:calc(0px + constant(safe-area-inset-bottom, 0px));padding-bottom:calc(0px + env(safe-area-inset-bottom, 0px))}.dev-tools-safe.top-left,.dev-tools-safe.top-right{top:calc(var(--safe-area-top, 50px) + 16px);top:calc(var(--status-bar-height, 50px) + 16px)}.dev-tools-safe.bottom-left,.dev-tools-safe.bottom-right{bottom:calc(16px + constant(safe-area-inset-bottom, 20px));bottom:calc(16px + env(safe-area-inset-bottom, 20px))}.step-indicators-safe{position:fixed;top:calc(44px + constant(safe-area-inset-top, 50px) + 16px);top:calc(44px + env(safe-area-inset-top, 50px) + 16px);top:calc(44px + var(--safe-area-top, 50px) + 16px);left:5%;right:5%;z-index:90;display:flex;justify-content:center;gap:16px}
