<!-- 应用根组件 - 简化版 -->
<script setup lang="ts">
import { onHide, onLaunch, onShow } from '@dcloudio/uni-app'
import { useUserStore } from '@/store/user'

// 初始化用户状态管理
const userStore = useUserStore()

onLaunch(() => {
  console.warn('App: onLaunch 启动')
  // 应用启动时初始化用户数据
  userStore.initUserData()

  // 增强的设备检测和安全区域计算
  const systemInfo = uni.getSystemInfoSync()
  console.warn('系统信息:', systemInfo)

  // 获取状态栏高度
  const statusBarHeight = systemInfo.statusBarHeight || 44

  // 2025年最佳实践：优先使用CSS环境变量，最小化fallback依赖
  // 检测设备类型并设置最小安全边距（仅作为CSS环境变量失效时的最后保障）
  let minSafeMargin = 20 // 默认最小安全边距

  if (systemInfo.platform === 'ios') {
    // iOS设备：根据屏幕高度判断设备类型
    const { screenHeight } = systemInfo
    if (screenHeight >= 812) {
      // 全面屏iPhone：刘海屏需要更大的安全边距
      minSafeMargin = 30
    }
    else {
      // 传统iPhone：Home键设备
      minSafeMargin = 20
    }
  }
  else if (systemInfo.platform === 'android') {
    // Android设备：根据状态栏高度动态计算
    if (statusBarHeight >= 30) {
      // 高状态栏设备（如小米、华为部分机型）
      minSafeMargin = 15
    }
    else {
      // 标准状态栏设备
      minSafeMargin = 25
    }
  }

  // 计算底部安全区域
  let bottomSafeHeight = 0

  // 2025年最佳实践：基于设备特征和安全区域计算底部安全距离
  if (systemInfo.safeArea) {
    // 优先使用系统安全区域信息
    const { screenHeight, safeArea } = systemInfo
    const actualScreenHeight = Math.max(screenHeight, screenHeight)
    const safeAreaHeight = Math.max(safeArea.height, safeArea.width)

    // 计算底部安全区域高度
    bottomSafeHeight = actualScreenHeight - safeAreaHeight - statusBarHeight

    // 边界检查：确保底部安全区域值合理
    if (bottomSafeHeight < 0) {
      bottomSafeHeight = 0
    }

    // 设备特定优化
    if (systemInfo.platform === 'ios') {
      // iPhone X 及以上设备的 Home Indicator 区域
      if (screenHeight >= 812 && bottomSafeHeight < 20) {
        bottomSafeHeight = 34 // iPhone X 系列标准 Home Indicator 高度
      }
    }
    else if (systemInfo.platform === 'android') {
      // Android 设备底部导航栏处理
      if (bottomSafeHeight < 10 && statusBarHeight >= 30) {
        bottomSafeHeight = 20 // 某些全面屏 Android 设备
      }
    }
  }
  else {
    // fallback：根据设备平台设置默认底部安全距离
    if (systemInfo.platform === 'ios') {
      const { screenHeight } = systemInfo
      if (screenHeight >= 812) {
        bottomSafeHeight = 34 // iPhone X 及以上
      }
      else {
        bottomSafeHeight = 0 // 传统 iPhone
      }
    }
    else {
      bottomSafeHeight = 0 // Android 默认
    }
  }

  // 最终安全区域计算
  const safeAreaTop = statusBarHeight + minSafeMargin

  console.warn(`安全区域计算结果:`, {
    statusBarHeight,
    safeAreaTop,
    bottomSafeHeight,
    platform: systemInfo.platform,
    screenHeight: systemInfo.screenHeight,
    safeArea: systemInfo.safeArea,
  })

  // 设置全局CSS变量（仅在Web环境下）
  try {
    if (typeof document !== 'undefined' && document.documentElement) {
      const documentElement = document.documentElement
      documentElement.style.setProperty('--status-bar-height', `${statusBarHeight}px`)
      documentElement.style.setProperty('--safe-area-top', `${safeAreaTop}px`)
      documentElement.style.setProperty('--safe-area-bottom', `${bottomSafeHeight}px`)
      documentElement.style.setProperty('--system-platform', systemInfo.platform)
    }
  }
  catch (e) {
    // 小程序环境中忽略document相关操作
    console.warn('设置CSS变量失败（小程序环境下正常）:', e)
  }

  // 兼容小程序环境
  try {
    if (typeof wx !== 'undefined' && wx.setStorageSync) {
      wx.setStorageSync('systemInfo', {
        statusBarHeight,
        safeAreaTop,
        bottomSafeHeight,
        platform: systemInfo.platform,
        model: systemInfo.model,
      })
    }
  }
  catch (e) {
    console.warn('存储系统信息失败:', e)
  }
})

onShow(() => {
  console.warn('App: onShow 显示')
})

onHide(() => {
  console.warn('App: onHide 隐藏')
})
</script>

<template>
  <view>
    <!-- 应用根组件内容为空，由各页面自行管理 -->
  </view>
</template>

<style>
/* 引入 iconfont 字体样式 */
@import url('./static/iconfont/iconfont.css');

/* 引入全局安全区域适配样式 */
/* @import './static/styles/safe-area.scss'; */

/* 引入微信小程序兼容性样式 */
@import './static/styles/compatibility.scss';

/* 2025年最佳实践：安全区域适配样式 */
.main-content-safe {
  /* 多重安全保障：优先使用env()，fallback到动态计算值 */
  padding-top: calc(44px + var(--safe-area-top, 20px));
  padding-top: calc(44px + constant(safe-area-inset-top, var(--safe-area-top, 20px)));
  padding-top: calc(44px + env(safe-area-inset-top, var(--safe-area-top, 20px)));
}

.page-header-safe {
  position: fixed;
  top: calc(var(--safe-area-top, 20px) + 16px);
  top: calc(constant(safe-area-inset-top, var(--safe-area-top, 20px)) + 16px);
  top: calc(env(safe-area-inset-top, var(--safe-area-top, 20px)) + 16px);
  left: 5%;
  z-index: 100;
  height: 28px;
  display: flex;
  align-items: center;
}

.main-scroll-safe {
  height: calc(100vh - var(--bottom-nav-height, 68px));
  padding-top: calc(44px + var(--safe-area-top, 20px));
  padding-top: calc(44px + constant(safe-area-inset-top, var(--safe-area-top, 20px)));
  padding-top: calc(44px + env(safe-area-inset-top, var(--safe-area-top, 20px)));
}

/* 全局CSS变量 */
:root {
  --color-primary: #8b5cf6;
  --color-secondary: #f59e0b;
  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;
  --color-info: #3b82f6;

  /* 底部导航栏高度 */
  --bottom-nav-height: 68px;

  /* 过渡动画 */
  --transition-base: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  /* 边距和圆角 */
  --border-radius-sm: 8px;
  --border-radius-md: 12px;
  --border-radius-lg: 16px;
  --border-radius-xl: 20px;

  /* 阴影 */
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* 页面基础样式 */
page {
  background: #f5f5f5;
  min-height: 100vh;
  --status-bar-height: 44px;
}

/* 2025年最佳实践：底部安全区域适配 */
.safe-area-bottom {
  /* 多重安全保障：优先使用env()，fallback到动态计算值 */
  padding-bottom: var(--safe-area-bottom, 0px);
  padding-bottom: constant(safe-area-inset-bottom, var(--safe-area-bottom, 0px));
  padding-bottom: env(safe-area-inset-bottom, var(--safe-area-bottom, 0px));
}

/* TabBar 底部安全区域适配 */
.tab-bar-safe {
  padding-bottom: var(--safe-area-bottom, 0px);
  padding-bottom: constant(safe-area-inset-bottom, var(--safe-area-bottom, 0px));
  padding-bottom: env(safe-area-inset-bottom, var(--safe-area-bottom, 0px));
}

/* 浮动按钮底部安全区域适配 */
.floating-button-bottom-safe {
  bottom: calc(96px + var(--safe-area-bottom, 0px));
  bottom: calc(96px + constant(safe-area-inset-bottom, var(--safe-area-bottom, 0px)));
  bottom: calc(96px + env(safe-area-inset-bottom, var(--safe-area-bottom, 0px)));
}

/* 兼容性样式 */
.safe-area-inset-bottom {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

.safe-area-inset-top {
  padding-top: constant(safe-area-inset-top);
  padding-top: env(safe-area-inset-top);
}

/* 通用类 */
.text-center {
  text-align: center;
}

.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.align-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

/* iconfont 增强样式 */
.iconfont {
  font-family: "iconfont" !important;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</style>
