/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */

/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */

/* 颜色变量 */

/* 行为相关颜色 */
$uni-color-primary: #007aff;
$uni-color-success: #4cd964;
$uni-color-warning: #f0ad4e;
$uni-color-error: #dd524d;

/* 文字基本颜色 */
$uni-text-color: #333; // 基本色
$uni-text-color-inverse: #fff; // 反色
$uni-text-color-grey: #999; // 辅助灰色，如加载更多的提示信息
$uni-text-color-placeholder: #808080;
$uni-text-color-disable: #c0c0c0;

/* 背景颜色 */
$uni-bg-color: #fff;
$uni-bg-color-grey: #f8f8f8;
$uni-bg-color-hover: #f1f1f1; // 点击状态颜色
$uni-bg-color-mask: rgba(0, 0, 0, 0.4); // 遮罩颜色

/* 边框颜色 */
$uni-border-color: #c8c7cc;

/* 尺寸变量 */

/* 文字尺寸 */
$uni-font-size-sm: 12px;
$uni-font-size-base: 14px;
$uni-font-size-lg: 16px;

/* 图片尺寸 */
$uni-img-size-sm: 20px;
$uni-img-size-base: 26px;
$uni-img-size-lg: 40px;

/* Border Radius */
$uni-border-radius-sm: 2px;
$uni-border-radius-base: 3px;
$uni-border-radius-lg: 6px;
$uni-border-radius-circle: 50%;

/* 水平间距 */
$uni-spacing-row-sm: 5px;
$uni-spacing-row-base: 10px;
$uni-spacing-row-lg: 15px;

/* 垂直间距 */
$uni-spacing-col-sm: 4px;
$uni-spacing-col-base: 8px;
$uni-spacing-col-lg: 12px;

/* 透明度 */
$uni-opacity-disabled: 0.3; // 组件禁用态的透明度

/* 文章场景相关 */
$uni-color-title: #2c405a; // 文章标题颜色
$uni-font-size-title: 20px;
$uni-color-subtitle: #555; // 二级标题颜色
$uni-font-size-subtitle: 18px;
$uni-color-paragraph: #3f536e; // 文章段落颜色
$uni-font-size-paragraph: 15px;

/* 全局样式变量 */

// 主题色
$primary-color: #8b5cf6;
$primary-light: #a78bfa;
$primary-dark: #7c3aed;

// 文本颜色
$text-primary: #1f2937;
$text-secondary: #4b5563;
$text-tertiary: #9ca3af;

// 背景色
$background-color: #f8fafc;

// 功能色
$success-color: #10b981;
$warning-color: #f59e0b;
$error-color: #ef4444;

// 五行色彩系统
$wuxing-jin-gradient: linear-gradient(135deg, #ffffff, #f0f0f0);
$wuxing-mu-gradient: linear-gradient(135deg, #a8e6cf, #73c1a8);
$wuxing-shui-gradient: linear-gradient(135deg, #b8c6db, #648dae);
$wuxing-huo-gradient: linear-gradient(135deg, #ff9a9e, #ff5458);
$wuxing-tu-gradient: linear-gradient(135deg, #ffeaa7, #ffc25c);

// 字体
$font-family-base: -apple-system, BlinkMacSystemFont, 'Inter', sans-serif;

// 字号
$font-size-h1: 32px;
$font-size-h2: 24px;
$font-size-page-title: 14px;
$font-size-card-title: 14px;
$font-size-body: 12px;
$font-size-helper: 10px;

// 字重
$font-weight-bold: 700;
$font-weight-semibold: 600;
$font-weight-medium: 500;
$font-weight-regular: 400;

// 间距
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 16px;
$spacing-lg: 24px;
$spacing-xl: 32px;

// 圆角
$radius-sm: 4px;
$radius-md: 6px;
$radius-lg: 8px;
$radius-xl: 16px;
$radius-full: 9999px;

// 阴影
$shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
$shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
$shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);

// 动画
$transition-base: all 0.3s ease;
$transition-smooth: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);

// 响应式断点
$breakpoint-sm: 640px;
$breakpoint-md: 768px;
$breakpoint-lg: 1024px;
$breakpoint-xl: 1280px;

// 布局相关
$status-bar-height: 44px;
$bottom-nav-height: 80px;
$content-padding: 5%;

/* 全局基础样式 */
page {
  background-color: $background-color;
  color: $text-primary;
  font-family: $font-family-base;
  font-size: $font-size-body;
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 毛玻璃效果混入 */
@mixin glass-effect {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.05);
}

@mixin glass-button {
  background: rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.5);
  transition: background 0.3s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.6);
  }
}
