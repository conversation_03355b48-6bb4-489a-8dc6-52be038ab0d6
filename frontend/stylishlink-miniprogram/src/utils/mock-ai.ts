// Mock AI 识别结果
interface MockBodyShapeResult {
  shoulderWidth: string
  waistShape: string
  belly: string
  hip: string
  hipShape: string
  armLength: string
  armCircum: string
  hipWidth: string
  thigh: string
  calf: string
  bodyFat: string
  bodyLength: string
  bodyType: string
  confidence: number
  recommendations: string[]
}

/**
 * 模拟体型识别结果
 * @param _imageUrl 图片URL (暂时未使用)
 * @returns Promise<MockBodyShapeResult>
 */
export async function mockAnalyzeBodyShape(_imageUrl: string): Promise<MockBodyShapeResult> {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 1500))

  // 随机生成体型特征
  const mockResults = [
    {
      shoulderWidth: '正常',
      waistShape: '有曲线',
      belly: '没有',
      hip: '正常',
      hipShape: '有曲线',
      armLength: '正常',
      armCircum: '正常',
      hipWidth: '正常',
      thigh: '正常',
      calf: '正常',
      bodyFat: '匀称',
      bodyLength: '匀称',
      bodyType: 'normal',
      confidence: 0.85,
      recommendations: [
        '您的体型比例匀称，适合多种穿搭风格',
        '建议选择修身剪裁的服装突出腰线',
        '可以尝试多种颜色搭配',
      ],
    },
    {
      shoulderWidth: '偏宽',
      waistShape: '直筒',
      belly: '略有小肚腩',
      hip: '下榻',
      hipShape: '直筒',
      armLength: '正常',
      armCircum: '偏粗',
      hipWidth: '窄',
      thigh: '偏细',
      calf: '正常',
      bodyFat: '偏上身粗',
      bodyLength: '偏上身长',
      bodyType: 'chubby',
      confidence: 0.78,
      recommendations: [
        '建议选择A字型剪裁平衡上下身比例',
        '深色上装可以显瘦',
        '避免过于紧身的上装',
      ],
    },
    {
      shoulderWidth: '偏窄',
      waistShape: '略有曲线',
      belly: '没有',
      hip: '较上翘',
      hipShape: '曲线较明显',
      armLength: '偏长',
      armCircum: '细',
      hipWidth: '偏宽',
      thigh: '偏粗',
      calf: '偏细',
      bodyFat: '偏下身粗',
      bodyLength: '偏下身长',
      bodyType: 'slim',
      confidence: 0.92,
      recommendations: [
        '梨形身材，建议突出上半身',
        '可选择亮色或有图案的上装',
        '下装选择深色直筒裤显瘦',
      ],
    },
  ]

  // 随机返回一个结果
  const randomIndex = Math.floor(Math.random() * mockResults.length)
  return mockResults[randomIndex]
}

/**
 * 根据图片路径判断是否可能包含人体
 * 这是一个简单的Mock判断，实际应用中可能需要更复杂的逻辑
 */
export function isValidBodyPhoto(imagePath: string): boolean {
  // 简单检查文件扩展名
  const validExtensions = ['.jpg', '.jpeg', '.png', '.webp']
  const extension = imagePath.toLowerCase().substring(imagePath.lastIndexOf('.'))
  return validExtensions.includes(extension)
}

/**
 * 生成随机置信度
 */
export function generateConfidence(): number {
  return Math.random() * 0.3 + 0.7 // 0.7-1.0 之间的随机数
}

// 衣物AI识别结果接口
interface MockClothingRecognitionResult {
  name: string
  category: string
  subCategory: string
  colors: string[]
  materials: string[]
  wuxing: {
    metal: number
    wood: number
    water: number
    fire: number
    earth: number
    primary: string
  }
  confidence: number
  suggestions: string[]
}

/**
 * 模拟衣物AI识别
 * @param _imageUrl 图片URL (暂时未使用)
 * @returns Promise<MockClothingRecognitionResult>
 */
export async function mockAIClothingRecognition(_imageUrl: string): Promise<MockClothingRecognitionResult> {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 2000))

  // 随机生成衣物识别结果
  const mockResults = [
    {
      name: '白色T恤',
      category: '上衣',
      subCategory: 'T恤',
      colors: ['白色'],
      materials: ['棉'],
      wuxing: {
        metal: 3,
        wood: 1,
        water: 1,
        fire: 0,
        earth: 2,
        primary: '金',
      },
      confidence: 0.92,
      suggestions: [
        '基础款白色T恤，百搭实用',
        '适合春夏季节穿着',
        '建议搭配牛仔裤或休闲裤',
      ],
    },
    {
      name: '蓝色牛仔裤',
      category: '下装',
      subCategory: '牛仔裤',
      colors: ['蓝色'],
      materials: ['棉', '氨纶'],
      wuxing: {
        metal: 1,
        wood: 2,
        water: 3,
        fire: 0,
        earth: 1,
        primary: '水',
      },
      confidence: 0.88,
      suggestions: [
        '经典牛仔裤，休闲百搭',
        '四季皆宜',
        '可搭配各种上装',
      ],
    },
    {
      name: '黑色休闲外套',
      category: '外套',
      subCategory: '休闲外套',
      colors: ['黑色'],
      materials: ['聚酯纤维'],
      wuxing: {
        metal: 1,
        wood: 0,
        water: 4,
        fire: 0,
        earth: 2,
        primary: '水',
      },
      confidence: 0.85,
      suggestions: [
        '百搭黑色外套，修身显瘦',
        '适合秋冬季节',
        '商务休闲两相宜',
      ],
    },
    {
      name: '红色连衣裙',
      category: '裙装',
      subCategory: '连衣裙',
      colors: ['红色'],
      materials: ['丝绸'],
      wuxing: {
        metal: 0,
        wood: 1,
        water: 0,
        fire: 4,
        earth: 1,
        primary: '火',
      },
      confidence: 0.79,
      suggestions: [
        '优雅红色连衣裙，气质出众',
        '适合约会或正式场合',
        '春夏季节穿着最佳',
      ],
    },
    {
      name: '棕色皮鞋',
      category: '鞋履',
      subCategory: '皮鞋',
      colors: ['棕色'],
      materials: ['真皮'],
      wuxing: {
        metal: 2,
        wood: 2,
        water: 0,
        fire: 1,
        earth: 3,
        primary: '土',
      },
      confidence: 0.83,
      suggestions: [
        '经典棕色皮鞋，商务必备',
        '四季通用',
        '适合正式场合穿着',
      ],
    },
  ]

  // 随机返回一个结果
  const randomIndex = Math.floor(Math.random() * mockResults.length)
  return mockResults[randomIndex]
}

/**
 * 检查图片是否适合衣物识别
 * @param imagePath 图片路径
 * @returns boolean
 */
export function isValidClothingPhoto(imagePath: string): boolean {
  // 简单检查文件扩展名
  const validExtensions = ['.jpg', '.jpeg', '.png', '.webp']
  const extension = imagePath.toLowerCase().substring(imagePath.lastIndexOf('.'))
  return validExtensions.includes(extension)
}

/**
 * 生成随机五行属性
 * @returns 五行属性对象
 */
export function generateRandomWuxing(): { metal: number, wood: number, water: number, fire: number, earth: number, primary: string } {
  const values = [0, 1, 2, 3, 4]
  const wuxing = {
    metal: values[Math.floor(Math.random() * values.length)],
    wood: values[Math.floor(Math.random() * values.length)],
    water: values[Math.floor(Math.random() * values.length)],
    fire: values[Math.floor(Math.random() * values.length)],
    earth: values[Math.floor(Math.random() * values.length)],
    primary: '',
  }

  // 找出最高值对应的五行
  const maxValue = Math.max(wuxing.metal, wuxing.wood, wuxing.water, wuxing.fire, wuxing.earth)
  const elements = ['金', '木', '水', '火', '土']
  const keys = ['metal', 'wood', 'water', 'fire', 'earth']

  for (let i = 0; i < keys.length; i++) {
    if (wuxing[keys[i] as keyof typeof wuxing] === maxValue) {
      wuxing.primary = elements[i]
      break
    }
  }

  return wuxing
}
