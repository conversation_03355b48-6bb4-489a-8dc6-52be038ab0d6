/**
 * API请求工具
 * 统一处理请求header和响应数据，支持JWT认证
 */

import { USER_API } from '@/config/api'
import { buildApiUrl } from '@/config/env'

// Token刷新状态管理
let isRefreshing = false
let refreshPromise: Promise<boolean> | null = null

// 防止重复跳转登录页
let isNavigatingToLogin = false

/**
 * 处理认证失败，跳转到登录页
 */
function handleAuthFailure(reason: string): void {
  console.error('认证失败:', reason)

  // 防止重复跳转
  if (isNavigatingToLogin) {
    console.warn('已在跳转登录页过程中，跳过重复跳转')
    return
  }

  isNavigatingToLogin = true

  // 清除所有认证信息
  clearAuth()

  // 跳转到登录页
  uni.navigateTo({
    url: '/pages/login/index',
    success: () => {
      console.warn('已跳转到登录页')
      // 延迟重置标志位，避免页面跳转过程中的重复请求
      setTimeout(() => {
        isNavigatingToLogin = false
      }, 1000)
    },
    fail: (error) => {
      console.error('跳转登录页失败:', error)
      // 跳转失败时也要重置标志位
      isNavigatingToLogin = false
    },
  })
}

interface RequestConfig {
  url: string
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE'
  data?: any
  header?: Record<string, string>
  needAuth?: boolean
  isFullUrl?: boolean // 是否为完整URL，如果是则不进行环境拼接
}

interface ApiResponse<T = any> {
  code: number | string // 支持数字0和字符串"200"
  msg?: string // 旧格式消息字段
  message?: string // 新格式消息字段
  data: T
  timestamp?: number // 后端可能返回的时间戳
}

interface ResponseWithHeaders<T = any> {
  data: ApiResponse<T>
  header: Record<string, string>
  statusCode: number
}

/**
 * 获取存储的JWT token
 */
function getToken(): string {
  try {
    return uni.getStorageSync('token') || ''
  }
  catch {
    return ''
  }
}

/**
 * 保存JWT token
 */
function setToken(token: string): void {
  try {
    uni.setStorageSync('token', token)
  }
  catch (error) {
    console.error('保存token失败:', error)
  }
}

/**
 * 获取存储的refresh token
 */
function _getRefreshToken(): string {
  try {
    return uni.getStorageSync('refreshToken') || ''
  }
  catch {
    return ''
  }
}

/**
 * 保存refresh token
 */
function setRefreshToken(refreshToken: string): void {
  try {
    uni.setStorageSync('refreshToken', refreshToken)
  }
  catch (error) {
    console.error('保存refreshToken失败:', error)
  }
}

/**
 * 检查token是否即将过期（提前5分钟刷新）
 */
function isTokenExpiring(): boolean {
  try {
    const expiresAt = uni.getStorageSync('tokenExpiresAt')
    if (!expiresAt) {
      console.warn('tokenExpiresAt未设置，无法判断过期时间')
      return false
    }

    const now = Date.now()
    const twelveHours = 12 * 60 * 60 * 1000 // 12小时
    const timeUntilExpiry = expiresAt - now
    const isExpiring = timeUntilExpiry <= twelveHours

    console.warn('Token过期检查:', {
      expiresAt,
      currentTime: now,
      timeUntilExpiry: `${Math.floor(timeUntilExpiry / 1000 / 60 / 60)}小时${Math.floor((timeUntilExpiry % (60 * 60 * 1000)) / 1000 / 60)}分钟`,
      isExpiring,
      threshold: '12小时',
    })

    return isExpiring
  }
  catch (error) {
    console.error('检查token过期时间失败:', error)
    return false
  }
}

/**
 * 检查token是否已过期
 */
function _isTokenExpired(): boolean {
  try {
    const expiresAt = uni.getStorageSync('tokenExpiresAt')
    if (!expiresAt)
      return false

    return Date.now() >= expiresAt
  }
  catch {
    return true
  }
}

/**
 * 刷新token
 */
async function performTokenRefresh(): Promise<boolean> {
  // 如果已经在刷新中，返回现有的Promise
  if (isRefreshing && refreshPromise) {
    console.warn('Token刷新已在进行中，等待结果...')
    return refreshPromise
  }

  isRefreshing = true
  refreshPromise = (async () => {
    try {
      // 目前没有refreshToken 先用token代替
      const refreshToken = getToken()
      if (!refreshToken) {
        console.warn('没有refresh token，无法刷新')
        return false
      }

      console.warn('开始刷新token...')

      const currentToken = getToken()
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      }

      // 如果有当前JWT token，添加到请求头
      if (currentToken) {
        headers.Authorization = `Bearer ${currentToken}`
        console.warn('刷新token请求携带当前JWT:', `Bearer ${currentToken.substring(0, 20)}...`)
      }

      const result: any = await new Promise((resolve, reject) => {
        uni.request({
          url: buildApiUrl(USER_API.REFRESH_TOKEN),
          method: 'POST',
          data: { refreshToken },
          header: headers,
          success: resolve,
          fail: reject,
        })
      })

      if (!isSuccessCode(result.data.code)) {
        throw new Error(result.data.message || result.data.msg || 'Token刷新失败')
      }

      const tokenData = result.data.data

      // 更新token信息
      setToken(tokenData.token)
      if (tokenData.refreshToken) {
        setRefreshToken(tokenData.refreshToken)
      }

      // 更新过期时间
      const expiresAt = Date.now() + tokenData.expiresIn * 1000
      uni.setStorageSync('tokenExpiresAt', expiresAt)

      console.warn('Token刷新成功')
      return true
    }
    catch (error) {
      console.error('Token刷新失败:', error)
      // 刷新失败，清除所有认证信息
      clearAuth()
      return false
    }
    finally {
      isRefreshing = false
      refreshPromise = null
    }
  })()

  return refreshPromise
}

/**
 * 从响应数据中提取JWT token
 * 现在token在响应体的data.token字段中
 */
function extractTokenFromResponse(responseData: any): { token: string, expiresIn?: number } | null {
  if (responseData && responseData.data && responseData.data.token) {
    return {
      token: responseData.data.token,
      expiresIn: responseData.data.expiresIn,
    }
  }
  return null
}

/**
 * 检查是否为成功状态码
 */
function isSuccessCode(code: number | string): boolean {
  return code === 0 || code === '0' || code === 200 || code === '200'
}

/**
 * 获取响应消息
 */
function getResponseMessage(response: ApiResponse<any>): string {
  return response.msg || response.message || '请求失败'
}

/**
 * 统一请求方法
 */
export async function request<T = any>(config: RequestConfig): Promise<ResponseWithHeaders<T>> {
  const { url, method = 'GET', data, header = {}, needAuth = true, isFullUrl = false } = config

  // 如果需要认证，检查token状态
  if (needAuth) {
    const token = getToken()
    const refreshToken = _getRefreshToken()

    const expiresAt = uni.getStorageSync('tokenExpiresAt')
    const now = Date.now()

    console.warn('请求认证检查:', {
      hasToken: !!token,
      hasRefreshToken: !!refreshToken,
      isTokenExpiring: isTokenExpiring(),
      tokenExpiresAt: expiresAt,
      currentTime: now,
      timeUntilExpiry: expiresAt
        ? (() => {
            const minutes = Math.floor((expiresAt - now) / 1000 / 60)
            const hours = Math.floor(minutes / 60)
            const remainingMinutes = minutes % 60
            return hours > 0 ? `${hours}小时${remainingMinutes}分钟` : `${remainingMinutes}分钟`
          })()
        : '未设置',
      url,
    })

    // 检查token是否即将过期，尝试刷新
    if (isTokenExpiring()) {
      console.warn('Token即将过期，尝试刷新...')
      const refreshSuccess = await performTokenRefresh()
      if (!refreshSuccess) {
        console.error('预刷新失败，请求将可能失败')
        // 处理认证失败，跳转到登录页
        handleAuthFailure('Token预刷新失败')
        throw new Error('Token刷新失败，请重新登录')
      }
    }

    const finalToken = getToken()
    if (finalToken) {
      header.Authorization = `Bearer ${finalToken}`
      console.warn('添加Authorization header:', `Bearer ${finalToken.substring(0, 20)}...`)
    }
    else {
      console.warn('没有token，但请求需要认证')
    }
  }

  // 构建完整URL
  const fullUrl = isFullUrl ? url : buildApiUrl(url)

  // 添加默认header
  header['Content-Type'] = header['Content-Type'] || 'application/json'

  return new Promise((resolve, reject) => {
    uni.request({
      url: fullUrl,
      method,
      data,
      header,
      success: (res) => {
        const response: ResponseWithHeaders<T> = {
          data: res.data as ApiResponse<T>,
          header: res.header,
          statusCode: res.statusCode,
        }

        // 首先检查HTTP状态码401（优先级高于业务错误码）
        if (res.statusCode === 401 && needAuth) {
          // HTTP 401未授权，尝试刷新token
          console.warn('收到HTTP 401错误，尝试刷新token...')
          performTokenRefresh().then((refreshSuccess) => {
            if (refreshSuccess) {
              // 刷新成功，重试原请求
              console.warn('Token刷新成功，重试原请求')
              request(config).then(resolve).catch(reject)
            }
            else {
              console.error('Token刷新失败，需要重新登录')
              // 处理认证失败，跳转到登录页
              handleAuthFailure('HTTP 401 - Token刷新失败')
              reject(new Error('认证失败，请重新登录'))
            }
          }).catch(() => {
            console.error('Token刷新过程异常，需要重新登录')
            // 处理认证失败，跳转到登录页
            handleAuthFailure('HTTP 401 - Token刷新异常')
            reject(new Error('认证失败，请重新登录'))
          })
          return
        }

        // 检查业务状态码
        if (isSuccessCode(response.data.code)) {
          // 从响应体中提取token并自动保存（主要用于登录接口）
          const tokenInfo = extractTokenFromResponse(res.data)
          if (tokenInfo) {
            setToken(tokenInfo.token)
            if (tokenInfo.expiresIn) {
              try {
                const expiresAt = Date.now() + tokenInfo.expiresIn * 1000
                uni.setStorageSync('tokenExpiresAt', expiresAt)
                console.warn('Token过期时间已设置:', {
                  expiresIn: `${tokenInfo.expiresIn}秒`,
                  expiresAt,
                  expiresAtReadable: new Date(expiresAt).toLocaleString(),
                })
              }
              catch (error) {
                console.error('保存token过期时间失败:', error)
              }
            }
            else {
              console.warn('响应中没有expiresIn字段，无法设置过期时间')
            }
          }

          resolve(response)
        }
        else if (response.data.code === 1002 && needAuth) {
          // 业务错误码1002：Token无效，尝试刷新token
          console.warn('收到业务错误码1002，尝试刷新token...')
          performTokenRefresh().then((refreshSuccess) => {
            if (refreshSuccess) {
              // 刷新成功，重试原请求
              console.warn('Token刷新成功，重试原请求')
              request(config).then(resolve).catch(reject)
            }
            else {
              console.error('Token刷新失败，需要重新登录')
              // 处理认证失败，跳转到登录页
              handleAuthFailure('业务错误码1002 - Token刷新失败')
              reject(new Error('认证失败，请重新登录'))
            }
          }).catch(() => {
            console.error('Token刷新过程异常，需要重新登录')
            // 处理认证失败，跳转到登录页
            handleAuthFailure('业务错误码1002 - Token刷新异常')
            reject(new Error('认证失败，请重新登录'))
          })
        }
        else {
          reject(new Error(getResponseMessage(response.data)))
        }
      },
      fail: (error) => {
        console.error('请求失败:', error)
        reject(new Error(error.errMsg || '网络请求失败'))
      },
    })
  })
}

/**
 * GET请求
 */
export function get<T = any>(url: string, config?: Omit<RequestConfig, 'url' | 'method'>): Promise<ResponseWithHeaders<T>> {
  return request<T>({ ...config, url, method: 'GET' })
}

/**
 * POST请求
 */
export function post<T = any>(url: string, data?: any, config?: Omit<RequestConfig, 'url' | 'method' | 'data'>): Promise<ResponseWithHeaders<T>> {
  return request<T>({ ...config, url, method: 'POST', data })
}

/**
 * PUT请求
 */
export function put<T = any>(url: string, data?: any, config?: Omit<RequestConfig, 'url' | 'method' | 'data'>): Promise<ResponseWithHeaders<T>> {
  return request<T>({ ...config, url, method: 'PUT', data })
}

/**
 * DELETE请求
 */
export function del<T = any>(url: string, config?: Omit<RequestConfig, 'url' | 'method'>): Promise<ResponseWithHeaders<T>> {
  return request<T>({ ...config, url, method: 'DELETE' })
}

/**
 * 清除认证信息
 */
export function clearAuth(): void {
  try {
    uni.removeStorageSync('token')
    uni.removeStorageSync('refreshToken')
    uni.removeStorageSync('tokenExpiresAt')
  }
  catch (error) {
    console.error('清除认证信息失败:', error)
  }
}
