/**
 * 应用实例工具函数
 * 提供安全的应用实例访问和状态检测
 */

/**
 * 安全获取应用实例
 * @returns 应用实例或 null
 */
export function safeGetApp(): any | null {
  try {
    const app = getApp()
    return app || null
  }
  catch (error) {
    console.warn('获取应用实例失败:', error)
    return null
  }
}

/**
 * 安全获取 Vue 实例
 * @returns Vue 实例或 null
 */
export function safeGetAppVm(): any | null {
  try {
    const app = safeGetApp()
    if (!app)
      return null

    const vm = (app as any).$vm
    return vm || null
  }
  catch (error) {
    console.warn('获取 Vue 实例失败:', error)
    return null
  }
}

/**
 * 检查应用是否完全初始化
 * @returns boolean 初始化状态
 */
export function isAppFullyInitialized(): boolean {
  try {
    const app = safeGetApp()
    const vm = safeGetAppVm()

    // 检查基本条件
    const hasApp = !!app
    const hasVm = !!vm

    // 检查页面栈是否存在
    let hasPages = false
    try {
      const pages = getCurrentPages()
      hasPages = Array.isArray(pages) && pages.length > 0
    }
    catch {
      hasPages = false
    }

    const isInitialized = hasApp && hasVm && hasPages

    if (!isInitialized) {
      console.warn('应用初始化状态检查:', {
        hasApp,
        hasVm,
        hasPages,
        timestamp: Date.now(),
      })
    }

    return isInitialized
  }
  catch (error) {
    console.warn('应用初始化检查异常:', error)
    return false
  }
}

/**
 * 等待应用完全初始化
 * @param timeout 超时时间（毫秒）
 * @param interval 检查间隔（毫秒）
 * @returns Promise<boolean> 是否在超时前完成初始化
 */
export function waitForAppInitialization(
  timeout = 5000,
  interval = 100,
): Promise<boolean> {
  return new Promise((resolve) => {
    // 首次检查
    if (isAppFullyInitialized()) {
      resolve(true)
      return
    }

    let attempts = 0
    const maxAttempts = Math.floor(timeout / interval)

    const checkInterval = setInterval(() => {
      attempts++

      if (isAppFullyInitialized()) {
        clearInterval(checkInterval)
        console.warn(`应用初始化完成，耗时: ${attempts * interval}ms`)
        resolve(true)
        return
      }

      if (attempts >= maxAttempts) {
        clearInterval(checkInterval)
        console.warn(`应用初始化超时 (${timeout}ms)`)
        resolve(false)
      }
    }, interval)
  })
}

/**
 * 在应用就绪后执行回调
 * @param callback 回调函数
 * @param timeout 超时时间（毫秒）
 */
export async function onAppReady(
  callback: () => void,
  timeout = 3000,
): Promise<void> {
  const isReady = await waitForAppInitialization(timeout)

  if (isReady) {
    try {
      callback()
    }
    catch (error) {
      console.error('应用就绪回调执行失败:', error)
    }
  }
  else {
    console.warn('应用初始化超时，跳过回调执行')
  }
}
