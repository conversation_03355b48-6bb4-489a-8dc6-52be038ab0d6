/**
 * 五行系统工具函数
 * 提供五行相关的映射、颜色、图标等功能
 */

// 五行元素类型
export type WuxingElement = '金' | '木' | '水' | '火' | '土'

// 五行属性接口
export interface WuxingInfo {
  en: string
  icon: string
  color: string
  name: string
}

// 五行比例数据接口
export interface WuxingRatio {
  金: number
  木: number
  水: number
  火: number
  土: number
}

// 五行映射表
export const WUXING_MAPPING: Record<WuxingElement, WuxingInfo> = {
  金: { en: 'jin', icon: 'icon-wuxing-jin', color: '#D4AF37', name: '金' },
  木: { en: 'mu', icon: 'icon-wuxing-mu', color: '#228B22', name: '木' },
  水: { en: 'shui', icon: 'icon-wuxing-shui', color: '#4169E1', name: '水' },
  火: { en: 'huo', icon: 'icon-wuxing-huo', color: '#DC143C', name: '火' },
  土: { en: 'tu', icon: 'icon-wuxing-tu', color: '#8B4513', name: '土' },
}

// 五行元素数组（用于遍历）
export const WUXING_ELEMENTS: WuxingElement[] = ['金', '木', '水', '火', '土']

/**
 * 获取五行的英文类名
 * @param element 五行元素
 * @returns CSS类名
 */
export function getWuxingClass(element: WuxingElement): string {
  return `wuxing-${WUXING_MAPPING[element]?.en || element}`
}

/**
 * 获取五行图标
 * @param element 五行元素
 * @returns iconfont类名
 */
export function getWuxingIcon(element: WuxingElement): string {
  return WUXING_MAPPING[element]?.icon || ''
}

/**
 * 获取五行颜色
 * @param element 五行元素
 * @returns 颜色值
 */
export function getWuxingColor(element: WuxingElement): string {
  return WUXING_MAPPING[element]?.color || '#666'
}

/**
 * 获取五行信息
 * @param element 五行元素
 * @returns 五行信息对象
 */
export function getWuxingInfo(element: WuxingElement): WuxingInfo | undefined {
  return WUXING_MAPPING[element]
}

/**
 * 计算五行比例百分比
 * @param ratio 五行比例数据
 * @param element 五行元素
 * @returns 百分比（保留整数）
 */
export function getWuxingPercentage(ratio: WuxingRatio, element: WuxingElement): number {
  const total = Object.values(ratio).reduce((sum, count) => sum + count, 0)
  return total > 0 ? Math.round((ratio[element] / total) * 100) : 0
}

/**
 * 获取五行总数
 * @param ratio 五行比例数据
 * @returns 总数
 */
export function getWuxingTotal(ratio: WuxingRatio): number {
  return Object.values(ratio).reduce((sum, count) => sum + count, 0)
}

/**
 * 生成五行样式变量
 * @returns CSS变量对象
 */
export function generateWuxingCSSVars(): Record<string, string> {
  const vars: Record<string, string> = {}
  WUXING_ELEMENTS.forEach((element) => {
    const info = WUXING_MAPPING[element]
    vars[`--wuxing-${info.en}-color`] = info.color
  })
  return vars
}
