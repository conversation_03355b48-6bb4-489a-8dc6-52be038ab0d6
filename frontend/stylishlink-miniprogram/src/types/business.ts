// 业务相关类型定义

// 能量数据接口
export interface EnergyData {
  score: number // 能量分数 (0-100)
  percentage: number // 超过用户百分比
}

// 建议项接口
export interface AdviceItem {
  id: string
  type: 'fashion' | 'accessory' | 'makeup' // 建议类型：服饰、配饰、妆容
  title: string
  description: string
  icon: string // iconfont图标类名
  backgroundColor: string // 图标背景色
}

// 案例项接口
export interface CaseItem {
  id: number
  title: string
  image: string
  rating?: number // 评分 (可选)
  likes: number
  favorites: number // 新增：收藏数
  liked: boolean // 修改：点赞状态，之前是isLiked
  favorited: boolean // 新增：收藏状态
  isLiked?: boolean // 保留向后兼容性
  isFavorited?: boolean // 保留向后兼容性
  tags?: string[] // 标签数组 (可选)
}

// 搭配数据接口
export interface OutfitItem {
  id: number
  title: string
  description: string
  image: string
  rating: number
  likes: number
  favorites: number
  liked: boolean
  favorited: boolean
}

// 分类标签接口
export interface CategoryTab {
  id: string
  name: string
  icon: string
  active: boolean
}

// 天气数据接口
export interface WeatherData {
  city?: string // 城市名称 (可选)
  temperature: number // 温度（摄氏度）
  condition: string // 天气状况文字描述
  icon: string // 天气图标类名
  humidity?: number // 湿度百分比 (可选)
  windSpeed?: number // 风速 (可选)
  windDirection?: string // 风向 (可选)
  airQuality?: string // 空气质量 (可选)
  updateTime?: string // 更新时间 (可选)
}

// 位置信息接口
export interface LocationInfo {
  city: string // 城市名称
  district?: string // 区县 (可选)
  province?: string // 省份 (可选)
}

// 农历信息接口
export interface LunarInfo {
  month: string // 农历月份
  day: string // 农历日期
  year?: string // 农历年份 (可选)
  fullDate: string // 完整农历日期描述
}
