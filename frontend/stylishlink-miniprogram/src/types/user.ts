/**
 * 用户相关类型定义
 */

// 重新导出API类型，供其他模块使用
export type {
  LoginResponse as ApiLoginResponse,
  UserInfo as ApiUserInfo,
  CompleteProfileRequest,
  CompleteProfileResponse,
  LoginRequest,
  SendCodeRequest,
  SendCodeResponse,
} from '@/api/user'

/**
 * Store中的用户信息格式（兼容旧格式）
 */
export interface StoreUserInfo {
  userId: string
  nickname: string
  avatar?: string
  phone?: string
  gender?: number // 0: 女性, 1: 男性
  height?: number
  weight?: number
  bodyType?: string
  skinTone?: string
  stylePreferences?: string[]
  bodyShape?: Record<string, string>
  mode?: 'natural' | 'energy'
  fullName?: string
  birthDate?: string
  birthTime?: string
  birthPlace?: string
  photoUrl?: string
  profileCompleted?: boolean
}

/**
 * Store登录响应格式
 */
export interface StoreLoginResponse {
  userId: string
  userInfo: StoreUserInfo
  hasBasicInfo: boolean
  hasWuxingInfo: boolean
  isNewUser: boolean
}
