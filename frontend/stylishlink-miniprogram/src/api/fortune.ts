/**
 * 今日能量服务API
 */

import { USER_API } from '@/config/api'
import { get } from '@/utils/request'

// ========================= 类型定义 =========================

/**
 * 日期信息
 */
export interface DateInfo {
  gregorian: string // 公历日期，如"2023年11月20日"
  lunar: string // 农历信息，如"二月十九 己巳卯 丙戌 水行"
}

/**
 * 五维能量评分
 */
export interface EnergyDimensions {
  love: number // 爱情运势(0-100)
  career: number // 事业运势(0-100)
  wealth: number // 财富运势(0-100)
  health: number // 健康运势(0-100)
  relationship: number // 人际运势(0-100)
}

/**
 * 宜忌事项
 */
export interface AdviceItem {
  id: string // 事项唯一标识
  icon: string // 图标（iconfont类名或图片路径）
  text: string // 事项具体描述
  category?: string // 事项分类，如"健康养生"、"社交人际"等
}

/**
 * 宜忌分类
 */
export interface AdviceCategory {
  type: 'suitable' | 'avoid' // 宜做或忌做
  label: string // 分类标签，如"宜做事项"、"忌做事项"
  items: AdviceItem[] // 具体宜忌事项
}

/**
 * 生活建议
 */
export interface LifeSuggestion {
  icon: string // 建议类型图标（emoji或字符）
  content: string // 具体生活建议内容
}

/**
 * 宜忌指南
 */
export interface AdviceData {
  categories: AdviceCategory[] // 宜忌分类列表
  lifeSuggestions: LifeSuggestion[] // 生活建议列表
}

/**
 * 颜色项
 */
export interface ColorItem {
  value: string // 色值(hex)
  name: string // 颜色名称
}

/**
 * 幸运元素
 */
export interface LuckyElements {
  colors: ColorItem[] // 幸运色列表（包含色值和名称）
  clothing: string[] // 服饰建议列表
  accessories: string[] // 配饰建议列表
  makeup: string[] // 妆容建议列表
}

/**
 * 获取今日能量信息请求参数
 */
export interface TodayEnergyRequest {
  date?: string // 指定日期，格式YYYY-MM-DD，默认今日
}

/**
 * 获取今日能量信息响应
 */
export interface TodayEnergyResponse {
  dateInfo: DateInfo // 日期信息
  totalScore: number // 今日总能量分数(0-100)
  percentage: number // 超过的用户百分比(0-100)
  peakTime: string // 能量高峰时段，如"上午8-10点"
  peakTimeDescription: string // 高峰时段描述
  description: string // 今日能量总体描述
  dimensions: EnergyDimensions // 五维能量评分
  advice: AdviceData // 宜忌指南
  luckyElements: LuckyElements // 幸运元素
}

/**
 * 获取能量历史趋势请求参数
 */
export interface EnergyTrendRequest {
  period?: 'week' | 'month' // 时间周期：week/month，默认week
  startDate?: string // 开始日期，格式YYYY-MM-DD
  endDate?: string // 结束日期，格式YYYY-MM-DD
}

/**
 * 趋势数据点
 */
export interface TrendDataPoint {
  date: string // 日期
  totalScore: number // 总能量分数
  dimensions: EnergyDimensions // 五维能量评分
}

/**
 * 最佳维度
 */
export interface BestDimension {
  key: string // 维度键名
  label: string // 维度标签，如"事业运势"
  avgScore: number // 平均分数
}

/**
 * 日期范围
 */
export interface DateRange {
  startDate: string // 开始日期
  endDate: string // 结束日期
}

/**
 * 获取能量历史趋势响应
 */
export interface EnergyTrendResponse {
  period: string // 查询的时间周期
  dateRange: DateRange // 查询的日期范围
  trendData: TrendDataPoint[] // 趋势数据点
  avgScore: number // 平均能量分数
  maxScore: number // 最高能量分数
  minScore: number // 最低能量分数
  bestDimension: BestDimension // 表现最佳的维度
}

// ========================= 完整运势解读类型定义 =========================

/**
 * 八字柱信息（年月日时）
 */
export interface BaziPillar {
  tiangan: string // 天干
  dizhi: string // 地支
  canggan: string[] // 藏干
}

/**
 * 八字组合
 */
export interface BaziCombination {
  year: BaziPillar // 年柱
  month: BaziPillar // 月柱
  day: BaziPillar // 日柱
  hour: BaziPillar // 时柱
}

/**
 * 五行元素
 */
export interface WuxingElement {
  element: string // 元素名称（金、木、水、火、土）
  percentage: number // 百分比
  isRizhu: boolean // 是否为日主
}

/**
 * 五行分析
 */
export interface WuxingAnalysis {
  elements: WuxingElement[] // 五行元素列表
  analysis: string // 分析描述
}

/**
 * 十年运势数据点
 */
export interface DecadeFortune {
  decade: number // 第几个十年
  ageRange: string // 年龄范围
  yearRange: string // 年份范围
  score: number // 运势分数
  theme: string // 主题
  description: string // 描述
  keyEvents: string[] // 关键事件
}

/**
 * 整体运势
 */
export interface OverallFortune {
  currentAge: number // 当前年龄
  decadeFortunes: DecadeFortune[] // 十年运势数据
  lifePhase: string // 当前人生阶段
  currentDecade: number // 当前所在第几个十年
  lifetimeOverview: string // 一生运势概览
}

/**
 * 吉运建议项目
 */
export interface LuckyAdviceItem {
  label: string // 标签
  advice: string // 建议内容
}

/**
 * 风水建议项目
 */
export interface FengshuiAdviceItem {
  type: 'recommend' | 'avoid' // 建议类型：推荐 | 避免
  advice: string // 建议内容
}

/**
 * 吉运建议
 */
export interface LuckyAdvice {
  clothing: {
    title: string
    items: LuckyAdviceItem[]
  }
  jewelry: {
    title: string
    items: LuckyAdviceItem[]
  }
  fengshui: {
    title: string
    items: FengshuiAdviceItem[]
  }
}

/**
 * 运势详细解读项目
 */
export interface FortuneDetailItem {
  status: string // 状态描述
  highlight: string // 重点提示
  color: string // 显示颜色
  content: string | string[] // 内容（支持字符串或字符串数组）
}

/**
 * 运势详细解读
 */
export interface DetailedFortune {
  monthly: {
    career: FortuneDetailItem // 事业运势
    wealth: FortuneDetailItem // 财富运势
    health: FortuneDetailItem // 健康运势
    marriage: FortuneDetailItem // 婚姻运势
    children: FortuneDetailItem // 子女运势
  }
  yearly: {
    career: FortuneDetailItem // 事业运势
    wealth: FortuneDetailItem // 财富运势
    health: FortuneDetailItem // 健康运势
    marriage: FortuneDetailItem // 婚姻运势
    children: FortuneDetailItem // 子女运势
  }
}

/**
 * 完整运势解读响应
 */
export interface CompleteFortuneReadingResponse {
  baziCombination: BaziCombination // 八字组合
  wuxingAnalysis: WuxingAnalysis // 五行分析
  overallFortune: OverallFortune // 整体运势
  luckyAdvice: LuckyAdvice // 吉运建议
  detailedFortune: DetailedFortune // 详细运势解读
}

// ========================= API方法 =========================

/**
 * 获取今日能量信息
 */
export async function getTodayEnergy(params?: TodayEnergyRequest): Promise<TodayEnergyResponse> {
  const response = await get<TodayEnergyResponse>(USER_API.TODAY_ENERGY, {
    data: params,
  })
  return response.data.data
}

/**
 * 获取能量历史趋势
 */
export async function getEnergyTrend(params?: EnergyTrendRequest): Promise<EnergyTrendResponse> {
  const response = await get<EnergyTrendResponse>(USER_API.ENERGY_TREND, {
    data: params,
  })
  return response.data.data
}

/**
 * 获取完整运势解读
 */
export async function getCompleteFortuneReading(): Promise<CompleteFortuneReadingResponse> {
  const response = await get<CompleteFortuneReadingResponse>(USER_API.COMPLETE_FORTUNE_READING)
  return response.data.data
}
