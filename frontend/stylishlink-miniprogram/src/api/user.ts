/**
 * 用户服务API
 */

import { USER_API } from '@/config/api'
import { get, post, put } from '@/utils/request'

// ========================= 类型定义 =========================

/**
 * 发送验证码请求参数
 */
export interface SendCodeRequest {
  phone: string
  type?: 'login' | 'register' // 验证码类型，默认为login
}

/**
 * 发送验证码响应
 */
export interface SendCodeResponse {
  codeId: string
  expiresIn: number // 过期时间（秒）
  message: string
}

/**
 * 用户登录请求参数
 */
export interface LoginRequest {
  phone: string
  code: string
  codeId: string
}

/**
 * 用户信息
 */
export interface UserInfo {
  id: string
  phone: string
  nickname?: string
  avatar?: string
  gender?: 'male' | 'female'
  birthday?: string
  wuxing?: '金' | '木' | '水' | '火' | '土'
  hasBasicInfo: boolean // 是否已完善基本信息
  hasWuxingInfo: boolean // 是否已完善五行信息
  createdAt: string
  updatedAt: string
}

/**
 * 登录响应
 */
export interface LoginResponse {
  userInfo: UserInfo
  token: string // JWT访问令牌
  expiresIn: number // token过期时间（秒）
  refreshToken?: string // 刷新令牌（新增）
  hasBasicInfo: boolean // 是否已完善基本信息
  hasWuxingInfo: boolean // 是否已完善五行信息
  isNewUser: boolean // 是否为新用户
  message?: string
}

/**
 * 完善用户信息请求参数
 */
export interface CompleteProfileRequest {
  nickname: string
  gender: 'male' | 'female'
  birthday: string
  wuxing?: '金' | '木' | '水' | '火' | '土'
  avatar?: string
}

/**
 * 完善用户信息响应
 */
export interface CompleteProfileResponse {
  success: boolean
  message: string
  hasBasicInfo: boolean
  hasWuxingInfo: boolean
  userInfo: UserInfo
}

/**
 * 刷新Token请求参数
 */
export interface RefreshTokenRequest {
  refreshToken: string
}

/**
 * 刷新Token响应
 */
export interface RefreshTokenResponse {
  token: string // 新的JWT访问令牌
  expiresIn: number // token过期时间（秒）
  refreshToken?: string // 新的刷新令牌（可选）
}

/**
 * 今日能量简要信息响应（基于API文档3.12接口）
 */
export interface TodayEnergyBriefResponse {
  dateInfo: {
    gregorian: string // 公历日期
    lunar: string // 农历信息
  }
  totalScore: number // 今日总能量分数(0-100)
  percentage: number // 超过的用户百分比
  luckyElements: {
    clothingSummary: string // 服饰建议总结
    accessoriesSummary: string // 配饰建议总结
    makeupSummary: string // 妆容建议总结
  } | null // 幸运元素可能为null，需要安全处理
}

// ========================= API方法 =========================

/**
 * 发送验证码
 */
export async function sendCode(params: SendCodeRequest): Promise<SendCodeResponse> {
  const response = await post<SendCodeResponse>(USER_API.SEND_CODE, params)
  return response.data.data
}

/**
 * 用户登录（验证码登录）
 */
export async function login(params: LoginRequest): Promise<LoginResponse> {
  const response = await post<LoginResponse>(USER_API.LOGIN, params)
  return response.data.data
}

/**
 * 完善用户信息
 */
export async function completeProfile(params: CompleteProfileRequest): Promise<CompleteProfileResponse> {
  const response = await put<CompleteProfileResponse>(USER_API.COMPLETE_PROFILE, params)
  return response.data.data
}

/**
 * 获取用户信息
 */
export async function getUserInfo(): Promise<UserInfo> {
  const response = await get<UserInfo>(USER_API.GET_USER_INFO)
  return response.data.data
}

/**
 * 更新用户信息
 */
export async function updateUserInfo(params: Partial<UserInfo>): Promise<UserInfo> {
  const response = await put<UserInfo>(USER_API.UPDATE_USER_INFO, params)
  return response.data.data
}

/**
 * 用户登出
 */
export async function logout(): Promise<void> {
  try {
    await post(USER_API.LOGOUT, {})
  }
  catch (error) {
    console.error('退出登录API调用失败:', error)
    // 即使API调用失败，也继续执行客户端退出逻辑
    // 由调用方（如store）处理本地清理
  }
}

/**
 * 刷新JWT Token
 * 使用refresh token换取新的access token
 */
export async function refreshToken(params: RefreshTokenRequest): Promise<RefreshTokenResponse> {
  const response = await post<RefreshTokenResponse>(USER_API.REFRESH_TOKEN, params, {
    needAuth: false, // 刷新token接口不需要携带当前token
  })
  return response.data.data
}

/**
 * 获取今日能量简要信息
 * @param date 指定日期，格式YYYY-MM-DD，默认今日
 */
export async function getTodayEnergyBrief(date?: string): Promise<TodayEnergyBriefResponse> {
  const url = date ? `${USER_API.TODAY_ENERGY_BRIEF}?date=${date}` : USER_API.TODAY_ENERGY_BRIEF
  const response = await get<TodayEnergyBriefResponse>(url)
  return response.data.data
}
