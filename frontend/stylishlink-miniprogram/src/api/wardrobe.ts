import { request } from '@/utils/request'

// 衣物基础信息接口
interface ClothingItem {
  id?: string
  name: string
  category: string
  subCategory?: string
  colors: string[]
  tags?: string[]
  seasons: string[]
  occasions: string[]
  materials: string[]
  styles?: string[]
  wuxing?: WuxingAttribute
  mainImageUrl: string
  imageUrls?: string[]
  brand?: string
  description?: string
  purchaseDate?: string
  price?: number
  wearCount?: number
  lastWornAt?: string
  createdAt?: string
  updatedAt?: string
}

// 五行属性接口
interface WuxingAttribute {
  metal?: number
  wood?: number
  water?: number
  fire?: number
  earth?: number
  primary?: string
}

// 衣物列表响应接口
interface ClothingListResponse {
  list: ClothingItem[]
  total?: number
}

// 新增衣物请求接口
interface AddClothingRequest {
  name: string
  category: string
  subCategory?: string
  colors: string[]
  seasons: string[]
  occasions: string[]
  materials: string[]
  wuxing?: WuxingAttribute
  mainImageUrl: string
  description?: string
}

// 衣橱统计信息接口
interface WardrobeStatsResponse {
  totalItems: number
  categoryStats: Record<string, number>
  seasonStats: Record<string, number>
  wuxingStats: Record<string, number>
}

// 衣橱API服务
export function useWardrobeApi() {
  /**
   * 新增衣物
   */
  const addClothing = async (data: AddClothingRequest): Promise<ClothingItem> => {
    try {
      const response = await request<ClothingItem>({
        url: '/api/wardrobe/clothing',
        method: 'POST',
        data,
      })
      return response.data.data
    }
    catch (error) {
      console.error('添加衣物失败:', error)
      throw error
    }
  }

  /**
   * 获取衣物列表
   */
  const getClothingList = async (params?: {
    category?: string
    season?: string
    occasion?: string
    page?: number
    limit?: number
  }): Promise<ClothingListResponse> => {
    try {
      const response = await request<ClothingListResponse>({
        url: '/api/wardrobe/clothing/list',
        method: 'GET',
        data: params,
      })
      return response.data.data
    }
    catch (error) {
      console.error('获取衣物列表失败:', error)
      throw error
    }
  }

  /**
   * 获取衣物详情
   */
  const getClothingDetail = async (id: string): Promise<ClothingItem> => {
    try {
      const response = await request<ClothingItem>({
        url: `/api/wardrobe/clothing/${id}`,
        method: 'GET',
      })
      return response.data.data
    }
    catch (error) {
      console.error('获取衣物详情失败:', error)
      throw error
    }
  }

  /**
   * 编辑衣物
   */
  const updateClothing = async (id: string, data: Partial<AddClothingRequest>): Promise<ClothingItem> => {
    try {
      const response = await request<ClothingItem>({
        url: `/api/wardrobe/clothing/${id}`,
        method: 'PUT',
        data,
      })
      return response.data.data
    }
    catch (error) {
      console.error('编辑衣物失败:', error)
      throw error
    }
  }

  /**
   * 删除衣物
   */
  const deleteClothing = async (id: string): Promise<void> => {
    try {
      await request<void>({
        url: `/api/wardrobe/clothing/${id}`,
        method: 'DELETE',
      })
    }
    catch (error) {
      console.error('删除衣物失败:', error)
      throw error
    }
  }

  /**
   * 上传衣物图片
   */
  const uploadClothingImage = async (filePath: string): Promise<string> => {
    try {
      return new Promise((resolve, reject) => {
        uni.uploadFile({
          url: '/api/wardrobe/upload/image',
          filePath,
          name: 'file',
          header: {
            Authorization: `Bearer ${uni.getStorageSync('token')}`,
          },
          success: (res) => {
            try {
              const data = JSON.parse(res.data)
              if (data.code === 0) {
                resolve(data.data.url)
              }
              else {
                reject(new Error(data.message))
              }
            }
            catch {
              reject(new Error('解析上传响应失败'))
            }
          },
          fail: (error) => {
            reject(error)
          },
        })
      })
    }
    catch (error) {
      console.error('上传图片失败:', error)
      throw error
    }
  }

  /**
   * 获取衣橱统计信息
   */
  const getWardrobeStats = async (): Promise<WardrobeStatsResponse> => {
    try {
      const response = await request<WardrobeStatsResponse>({
        url: '/api/wardrobe/stats',
        method: 'GET',
      })
      return response.data.data
    }
    catch (error) {
      console.error('获取衣橱统计失败:', error)
      throw error
    }
  }

  return {
    addClothing,
    getClothingList,
    getClothingDetail,
    updateClothing,
    deleteClothing,
    uploadClothingImage,
    getWardrobeStats,
  }
}

// 导出类型
export {
  type AddClothingRequest,
  type ClothingItem,
  type ClothingListResponse,
  type WardrobeStatsResponse,
  type WuxingAttribute,
}
