import { getApiBaseUrl } from '@/config/env'

/**
 * 检查是否为成功状态码（复用request.ts的逻辑）
 */
function isSuccessCode(code: number | string): boolean {
  return code === 0 || code === '0' || code === 200 || code === '200'
}

/**
 * 文件上传响应数据接口 - 根据API文档更新
 */
export interface FileUploadResult {
  fileId: string
  url: string // 修正字段名，与API文档一致
  thumbnailUrl?: string
  fileInfo: {
    id: string
    originalName: string
    fileSize: number
    mimeType: string
    md5Hash: string
    status: string
    createdAt: string
  }
}

/**
 * 标准API响应格式
 */
interface ApiResponse<T> {
  code: number
  message: string
  data: T
}

/**
 * 文件分类枚举 - 根据API文档定义
 */
export enum FileCategory {
  USER_AVATAR = 'USER_AVATAR',
  USER_PHOTO = 'USER_PHOTO',
  CLOTHING_IMAGE = 'CLOTHING_IMAGE',
  ACCESSORY_IMAGE = 'ACCESSORY_IMAGE',
  OUTFIT_IMAGE = 'OUTFIT_IMAGE',
  AI_GENERATED_VIDEO = 'AI_GENERATED_VIDEO',
  AI_GENERATED_IMAGE = 'AI_GENERATED_IMAGE',
  SYSTEM_ICON = 'SYSTEM_ICON',
  SYSTEM_BACKGROUND = 'SYSTEM_BACKGROUND',
  TEMP_FILE = 'TEMP_FILE',
}

/**
 * 访问级别枚举
 */
export enum AccessLevel {
  PUBLIC = 'PUBLIC',
  PRIVATE = 'PRIVATE',
  PROTECTED = 'PROTECTED',
  INTERNAL = 'INTERNAL',
}

/**
 * 上传文件参数接口
 */
export interface UploadFileOptions {
  filePath: string
  category: FileCategory
  accessLevel?: AccessLevel
  metadata?: Record<string, any>
  originalName?: string
}

/**
 * 上传图片文件并返回完整信息
 * @param filePath 本地文件路径
 * @param fileName 文件名（可选，保持向后兼容）
 * @returns Promise<FileUploadResult> 返回完整的文件信息
 */
export async function uploadImageWithDetails(filePath: string, fileName?: string): Promise<FileUploadResult> {
  try {
    console.warn('uploadImageWithDetails 被调用，参数:', { filePath, fileName })

    // 获取token
    const token = uni.getStorageSync('token')
    console.warn('获取到token:', !!token)

    // 准备表单数据 - 使用简化的参数
    const formData: Record<string, any> = {
      category: 'USER_PHOTO', // 直接使用字符串，避免枚举问题
    }

    console.warn('准备上传到:', `${getApiBaseUrl()}/api/file/files/upload`)
    console.warn('表单数据:', formData)

    // 在微信小程序中使用 uploadFile 方法
    const uploadResult = await uni.uploadFile({
      url: `${getApiBaseUrl()}/api/file/files/upload`,
      filePath,
      name: 'file', // 后端接收的字段名
      header: {
        Authorization: `Bearer ${token}`, // 添加认证头
      },
      formData,
    })

    console.warn('上传结果:', uploadResult)

    if (uploadResult.statusCode === 200) {
      const response: ApiResponse<FileUploadResult> = JSON.parse(uploadResult.data)
      console.warn('解析的响应:', response)
      if (isSuccessCode(response.code)) { // 使用统一的状态码判断
        return response.data
      }
      else {
        throw new Error(response.message || '文件上传失败')
      }
    }
    else {
      throw new Error(`上传请求失败，状态码: ${uploadResult.statusCode}`)
    }
  }
  catch (error) {
    console.error('文件上传失败:', error)
    throw error
  }
}

/**
 * 上传图片文件（简化版本，向后兼容）
 * @param filePath 本地文件路径
 * @param fileName 文件名（可选，保持向后兼容）
 * @returns Promise<string> 返回文件访问URL
 */
export async function uploadImage(filePath: string, fileName?: string): Promise<string> {
  const result = await uploadImageWithDetails(filePath, fileName)
  return result.url
}

/**
 * 上传文件（完整版本）
 * @param options 上传选项
 * @returns Promise<FileUploadResult> 返回完整的文件信息
 */
export async function uploadFile(options: UploadFileOptions): Promise<FileUploadResult> {
  try {
    // 获取token
    const token = uni.getStorageSync('token')

    // 准备表单数据
    const formData: Record<string, any> = {
      category: options.category,
    }

    // 添加可选字段
    if (options.accessLevel) {
      formData.accessLevel = options.accessLevel
    }

    if (options.metadata) {
      formData.metadata = JSON.stringify(options.metadata)
    }

    const uploadResult = await uni.uploadFile({
      url: `${getApiBaseUrl()}/api/file/files/upload`,
      filePath: options.filePath,
      name: 'file',
      header: {
        Authorization: `Bearer ${token}`,
      },
      formData,
    })

    if (uploadResult.statusCode === 200) {
      const response: ApiResponse<FileUploadResult> = JSON.parse(uploadResult.data)
      if (isSuccessCode(response.code)) { // 使用统一的状态码判断
        return response.data
      }
      else {
        throw new Error(response.message || '文件上传失败')
      }
    }
    else {
      throw new Error(`上传请求失败，状态码: ${uploadResult.statusCode}`)
    }
  }
  catch (error) {
    console.error('文件上传失败:', error)
    throw error
  }
}

/**
 * 删除文件响应接口
 */
export interface DeleteFileResponse {
  success: boolean
}

/**
 * 删除文件
 * @param fileId 文件ID
 * @returns Promise<boolean>
 */
export async function deleteFile(fileId: string): Promise<boolean> {
  const { del } = await import('@/utils/request')
  const response = await del<DeleteFileResponse>(
    `${getApiBaseUrl()}/api/v1/files/${fileId}`, // 使用RESTful风格的删除接口
  )
  return response.data.code === 0
}

/**
 * 文件信息接口 - 根据API文档更新
 */
export interface FileInfo {
  id: string
  userId: string
  originalName: string
  storageName: string
  bucketName: string
  objectKey: string
  category: string
  fileType: string
  mimeType: string
  fileSize: number
  md5Hash: string
  status: string
  accessLevel: string
  url: string
  thumbnailUrl?: string
  metadata?: Record<string, any>
  createdAt: string
  updatedAt: string
}

/**
 * 获取文件信息
 * @param fileId 文件ID
 * @returns Promise<FileInfo>
 */
export async function getFileInfo(fileId: string): Promise<FileInfo> {
  const { get } = await import('@/utils/request')
  const response = await get<FileInfo>(
    `${getApiBaseUrl()}/api/v1/files/${fileId}`, // 使用RESTful风格
  )
  return response.data.data
}
