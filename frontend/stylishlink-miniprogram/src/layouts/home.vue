<!-- 首页布局组件 -->
<script setup lang="ts">
import DefaultLayout from './default.vue'

// 定义事件
const emit = defineEmits<{
  (e: 'refresh', event: any): void
  (e: 'loadMore', event: any): void
}>()

// 下拉刷新处理
function onRefresh(event: any) {
  // 触发父组件的刷新事件
  emit('refresh', event)
}

// 上拉加载更多
function onLoadMore(event: any) {
  // 触发父组件的加载更多事件
  emit('loadMore', event)
}
</script>

<template>
  <default-layout>
    <view class="home-layout">
      <!-- 内容滚动区域 -->
      <scroll-view
        scroll-y
        class="h-full w-full px-4"
        :refresher-enabled="true"
        :refresher-threshold="45"
        refresher-default-style="black"
        @refresherrefresh="onRefresh"
        @scrolltolower="onLoadMore"
      >
        <slot />
      </scroll-view>
    </view>
  </default-layout>
</template>

<style lang="scss" scoped>
.home-layout {
  height: calc(100vh - constant(safe-area-inset-bottom));
  height: calc(100vh - env(safe-area-inset-bottom));
}
</style>
