<!-- 默认布局组件 -->
<script setup lang="ts">
import TheHeader from '@/components/layout/TheHeader.vue'
// import TheTabBar from '@/components/layout/TheTabBar.vue'
import { useThemeStore } from '@/composables/useTheme'

const themeStore = useThemeStore()
</script>

<template>
  <view class="layout-default" :class="{ dark: themeStore.isDark }">
    <!-- 顶部导航栏 -->
    <TheHeader />

    <!-- 主要内容区域 -->
    <view class="safe-area-inset-bottom w-full flex-1 overflow-auto pb-[96px]">
      <slot />
    </view>

    <!-- 底部导航栏 - 临时注释 -->
    <!-- <TheTabBar class="fixed bottom-0 left-0 right-0" /> -->
  </view>
</template>

<style lang="scss" scoped>
.layout-default {
  // 确保内容区域不被底部导航遮挡
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
</style>
