/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by vite-plugin-uni-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    AppFooter: typeof import('./components/AppFooter.vue')['default']
    AppLogos: typeof import('./components/AppLogos.vue')['default']
    BusinessCaseGrid: typeof import('./components/business/CaseGrid.vue')['default']
    BusinessOutfitCards: typeof import('./components/business/OutfitCards.vue')['default']
    BusinessOutfitRecommendationCard: typeof import('./components/business/OutfitRecommendationCard.vue')['default']
    BusinessTodayEnergy: typeof import('./components/business/TodayEnergy.vue')['default']
    BusinessWeatherEnergySection: typeof import('./components/business/WeatherEnergySection.vue')['default']
    BusinessWeatherInfo: typeof import('./components/business/WeatherInfo.vue')['default']
    BusinessWuxingChart: typeof import('./components/business/WuxingChart.vue')['default']
    BusinessWuxingCircle: typeof import('./components/business/WuxingCircle.vue')['default']
    BusinessWuxingRatio: typeof import('./components/business/WuxingRatio.vue')['default']
    CommonCategoryTabs: typeof import('./components/common/CategoryTabs.vue')['default']
    CommonCitySelector: typeof import('./components/common/CitySelector.vue')['default']
    CommonDevTools: typeof import('./components/common/DevTools.vue')['default']
    CommonEnergyCircleProgress: typeof import('./components/common/EnergyCircleProgress.vue')['default']
    CommonEnvironmentSwitcher: typeof import('./components/common/EnvironmentSwitcher.vue')['default']
    CommonFloatingActionButton: typeof import('./components/common/FloatingActionButton.vue')['default']
    CommonGlassCard: typeof import('./components/common/GlassCard.vue')['default']
    CommonIconButton: typeof import('./components/common/IconButton.vue')['default']
    CommonLikeFavoriteButton: typeof import('./components/common/LikeFavoriteButton.vue')['default']
    CommonPageHeader: typeof import('./components/common/PageHeader.vue')['default']
    CommonStarRating: typeof import('./components/common/StarRating.vue')['default']
    HiCounter: typeof import('./components/HiCounter.vue')['default']
    InputEntry: typeof import('./components/InputEntry.vue')['default']
    LayoutTheHeader: typeof import('./components/layout/TheHeader.vue')['default']
    LayoutTheTabBar: typeof import('./components/layout/TheTabBar.vue')['default']
  }
}
