# 开发工具位置修复说明

## 问题描述

用户反馈：开发工具按钮被微信小程序的菜单按钮挡住

## 问题原因

微信小程序右上角为官方系统UI区域，包含：
- 转发按钮
- 设置菜单
- 关闭按钮

**微信小程序设计规范明确规定**：开发者不能在右上角区域放置任何可交互元素。

## 解决方案

### 位置调整
- **修改前**: `position="top-right"` （与微信菜单冲突）
- **修改后**: `position="top-left"` （安全区域，无冲突）

### 代码变更
```vue
<!-- 修改前 -->
<DevTools position="top-right" />

<!-- 修改后 -->
<DevTools position="top-left" />
```

### 其他配置保持不变
- 尺寸：small (32px)
- 透明度：0.5
- 双击隐藏功能：启用

## 遵循的设计规范

根据微信小程序设计规范和项目开发规范：

1. **右上角禁区**：
   - 禁止放置任何自定义交互元素
   - 与官方菜单保持足够距离（≥16px）

2. **左上角安全区域**：
   - 可放置自定义组件
   - 需考虑状态栏高度适配
   - 不与页面标题冲突

3. **其他可选位置**：
   - `bottom-left`: 左下角
   - `bottom-right`: 右下角
   - 均已在DevTools组件中支持

## 测试验证

✅ **编译成功**: 微信小程序编译通过
✅ **位置调整**: 开发工具已移至左上角
✅ **功能正常**: 所有交互功能保持正常
✅ **视觉效果**: 不再被微信菜单遮挡

## 经验总结

在微信小程序开发中，必须严格遵循官方UI约束：
- 仔细阅读微信小程序设计规范
- 避开系统预留的UI区域
- 优先选择安全的布局区域
- 充分测试不同设备的显示效果
