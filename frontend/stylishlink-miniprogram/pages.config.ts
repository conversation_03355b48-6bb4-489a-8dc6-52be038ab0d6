import { defineUniPages } from '@uni-helper/vite-plugin-uni-pages'

export default defineUniPages({
  pages: [],
  globalStyle: {
    backgroundColor: '#f8fafc',
    backgroundColorBottom: '#f8fafc',
    backgroundColorTop: '#f8fafc',
    backgroundTextStyle: 'dark',
    navigationBarBackgroundColor: '#ffffff',
    navigationBarTextStyle: 'black',
    navigationBarTitleText: 'StylishLink',
    navigationStyle: 'custom',
  },
  tabBar: {
    custom: true,
    color: '#7A7E83',
    selectedColor: '#8b5cf6',
    borderStyle: 'white',
    backgroundColor: '#ffffff',
    list: [
      {
        pagePath: 'pages/index/index',
        text: '首页',
      },
      {
        pagePath: 'pages/wardrobe/index',
        text: '衣橱',
      },
      {
        pagePath: 'pages/outfit/index',
        text: '搭配',
      },
      {
        pagePath: 'pages/profile/index',
        text: '我的',
      },
    ],
  },
})
