# macOS系统文件
.DS_Store
.AppleDouble
.LSOverride

# 缩略图
._*

# 可能出现在卷根目录中的文件
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# 可能在远程AFP共享上创建的目录
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# 项目特定的忽略文件
node_modules/
dist/
.env

# Maven项目忽略文件
target/
**/target/
bin/
**/bin/
build/
**/build/
out/
**/out/
classes/
**/classes/
*.class
*.jar
*.war
*.ear
*.nar
*.log
*.ctxt
hs_err_pid*
replay_pid*

# Eclipse相关文件
.classpath
.project
.settings/
.factorypath
.apt_generated/
.apt_generated_test/

# IntelliJ IDEA相关文件
.idea/
*.iml
*.iws
*.ipr
.idea_modules/
out/

# NetBeans相关文件
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/

# VS Code相关文件
.vscode/
**/.history/

# Spring Boot相关文件
.spring-boot-devtools.restart.trigger

# 日志文件
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.mvn/
mvnw
mvnw.cmd
