# Canvas 2D实施方案 - 解决滚动抖动问题

## 背景
经过多轮优化尝试，包括CSS transform、opacity调整等方案都无法彻底解决微信小程序Canvas滚动抖动问题。最终采用Canvas 2D接口，利用其同层渲染特性来根本性解决问题。

## Canvas 2D vs 旧Canvas接口

### 旧Canvas接口问题
- **层级问题**：Canvas为原生组件，拥有最高层级，容易产生滚动抖动
- **渲染机制**：使用独立的渲染层，与页面其他元素不在同一层
- **性能限制**：滚动时容易出现重绘和层级冲突

### Canvas 2D优势
- **同层渲染**：与页面其他元素在同一渲染层，避免层级冲突
- **标准API**：使用标准Canvas 2D API，性能更好
- **滚动优化**：天然支持页面滚动，不会产生抖动

## 实施方案

### 1. Canvas配置修改
```vue
<!-- 旧版本 -->
<canvas
  :force-use-old-canvas="true"
  class="chart-canvas"
/>

<!-- Canvas 2D版本 -->
<canvas
  type="2d"
  class="chart-canvas"
/>
```

### 2. 绘制逻辑重构

#### 2.1 Canvas上下文获取
```typescript
// 旧版本
const ctx = uni.createCanvasContext(canvasId, instance)

// Canvas 2D版本
const query = uni.createSelectorQuery().in(getCurrentInstance())
query.select(`#${canvasId}`)
  .fields({ node: true, size: true })
  .exec((res) => {
    const canvas = res[0].node
    const ctx = canvas.getContext('2d')
    
    // DPR适配
    const dpr = uni.getSystemInfoSync().pixelRatio || 1
    canvas.width = chartConfig.width * dpr
    canvas.height = chartConfig.height * dpr
    ctx.scale(dpr, dpr)
    
    // 性能优化设置
    ctx.imageSmoothingEnabled = true
    ctx.imageSmoothingQuality = 'high'
  })
```

#### 2.2 API调用转换
```typescript
// 旧版本 API -> Canvas 2D API
ctx.setFillStyle(color)     -> ctx.fillStyle = color
ctx.setStrokeStyle(color)   -> ctx.strokeStyle = color
ctx.setLineWidth(width)     -> ctx.lineWidth = width
ctx.setFontSize(size)       -> ctx.font = `${size}px sans-serif`
ctx.setTextAlign(align)     -> ctx.textAlign = align
ctx.setTextBaseline(base)   -> ctx.textBaseline = base
ctx.setLineDash(pattern)    -> ctx.setLineDash(pattern)

// 绘制提交
ctx.draw(false, callback)   -> 不需要，Canvas 2D自动渲染
```

### 3. 组件适配

#### 3.1 OverallFortune组件
- ✅ Canvas配置改为`type="2d"`
- ✅ 绘制函数重构为`drawChart()`使用Canvas 2D API
- ✅ 创建Canvas 2D版本的绘制函数：
  - `drawAxis2D()`
  - `drawLabels2D()`
  - `drawFortuneLine2D()`
  - `drawKeyPoints2D()`
  - `drawPlaceholderChart2D()`

#### 3.2 WuxingCircle组件
- ✅ Canvas配置改为`type="2d"`
- ✅ 绘制函数重构为`drawWuxingChart()`使用Canvas 2D API
- ✅ 创建Canvas 2D版本的绘制函数：
  - `drawArc2D()`
  - `drawDashedLine2D()`
  - `drawWuxingChart2D()`

### 4. 关键技术点

#### 4.1 DPR适配
```typescript
const dpr = uni.getSystemInfoSync().pixelRatio || 1
canvas.width = chartConfig.width * dpr
canvas.height = chartConfig.height * dpr
ctx.scale(dpr, dpr)
```

#### 4.2 性能优化
```typescript
ctx.imageSmoothingEnabled = true
ctx.imageSmoothingQuality = 'high'
```

#### 4.3 无需draw()调用
Canvas 2D自动渲染，无需调用`ctx.draw()`方法

## 预期效果

### 1. 滚动抖动彻底解决
- Canvas与页面元素同层渲染
- 滚动时不会产生层级冲突
- 视觉效果流畅自然

### 2. 性能提升
- 使用标准Canvas 2D API，性能更优
- 减少不必要的重绘和层级计算
- 更好的硬件加速支持

### 3. 兼容性
- 微信小程序基础库2.9.0+支持Canvas 2D
- 向后兼容，可根据需要降级到旧接口

## 测试要点

1. **功能测试**
   - 运势趋势图正常显示
   - 五行图正常显示
   - 所有绘制元素完整

2. **滚动测试**
   - 页面滚动时Canvas不抖动
   - 组件内滚动（如运势图横向滚动）流畅
   - iOS和Android设备表现一致

3. **性能测试**
   - 绘制速度对比
   - 内存使用情况
   - 滚动帧率测试

## 回退方案

如果Canvas 2D出现兼容性问题，可以快速回退：
1. 移除`type="2d"`属性
2. 添加`:force-use-old-canvas="true"`
3. 使用旧版本的绘制函数

这是目前最根本的Canvas滚动抖动解决方案，通过同层渲染机制从技术架构层面解决问题。
