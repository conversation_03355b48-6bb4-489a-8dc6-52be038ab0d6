<!-- Generated by SpecStory -->

# 用户服务异步调用ai服务失败 (2025-06-11 10:19:13)

_**User**_

查一下用户服务异步调用ai服务失败了：2025-06-11 10:15:00.833 [] DEBUG o.s.security.web.FilterChainProxy  - Securing PUT /user/complete-profile
2025-06-11 10:15:00.841 [] DEBUG o.s.security.web.FilterChainProxy  - Secured PUT /user/complete-profile
2025-06-11 10:15:00.858 [d874655549f8436aa1ee61b6fdaccf05] INFO  c.s.common.aop.RequestLoggingAspect  - ===> Request: com.stylishlink.user.controller.NewUserController.completeProfile() | Params: [{"nickname":null,"avatar":null,"gender":2,"photoUrl":"https://dev-1361654307.cos.ap-guangzhou.myqcloud.com/user/photo/2025-06-11/d1cd7d55e52a40d6a0768a7e8619740d.jpg","height":170,"weight":60,"bodyType":"chubby","skinTone":"medium","stylePreferences":["casual","sports"],"bodyShape":{"shoulderWidth":"3","waistShape":"1","belly":"1","hip":"1","hipShape":"1","armLength":"3","armCircum":"3","hipWidth":"3","thigh":"3","calf":"3","bodyFat":"3","bodyLength":"3"},"mode":"energy","residenceName":null,"residenceCode":null,"fullName":"rain","birthDate":"1991-06-11","birthTime":"10:14","birthPlace":"天津"}]
2025-06-11 10:15:00.858 [d874655549f8436aa1ee61b6fdaccf05] INFO  c.s.u.controller.NewUserController  - 完善用户信息: u6c1de3ba
2025-06-11 10:15:00.858 [d874655549f8436aa1ee61b6fdaccf05] INFO  c.s.u.service.impl.UserServiceImpl  - 完善用户信息: u6c1de3ba
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6b91cf8d] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@1004402142 wrapping com.mysql.cj.jdbc.ConnectionImpl@42a89cef] will not be managed by Spring
==>  Preparing: SELECT ID,user_id,open_id,union_id,nickname,avatar,gender,phone,email,password_hash,register_date,last_login_date,photo_url,height,weight,body_type,skin_tone,style_preferences,body_shape,mode,residence_name,residence_code,full_name,birth_date,birth_time,birth_place,preferences,body_info,wuxing_profile,settings,stats,extensions,create_time,update_time FROM user_info WHERE (user_id = ?)
==> Parameters: u6c1de3ba(String)
<==    Columns: ID, user_id, open_id, union_id, nickname, avatar, gender, phone, email, password_hash, register_date, last_login_date, photo_url, height, weight, body_type, skin_tone, style_preferences, body_shape, mode, residence_name, residence_code, full_name, birth_date, birth_time, birth_place, preferences, body_info, wuxing_profile, settings, stats, extensions, create_time, update_time
<==        Row: 13, u6c1de3ba, null, null, 用户_000004, null, 0, 13500000004, null, null, 2025-06-11 10:13:23, 2025-06-11 10:13:23, null, null, null, null, null, <<BLOB>>, <<BLOB>>, natural, null, null, null, null, null, null, <<BLOB>>, <<BLOB>>, <<BLOB>>, <<BLOB>>, <<BLOB>>, <<BLOB>>, 2025-06-11 10:13:23, 2025-06-11 10:13:23
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6b91cf8d]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2b9c552c] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@662535601 wrapping com.mysql.cj.jdbc.ConnectionImpl@42a89cef] will not be managed by Spring
==>  Preparing: UPDATE user_info SET user_id=?, nickname=?, gender=?, phone=?, register_date=?, last_login_date=?, photo_url=?, height=?, weight=?, body_type=?, skin_tone=?, style_preferences=?, body_shape=?, mode=?, full_name=?, birth_date=?, birth_time=?, birth_place=?, create_time=?, update_time=? WHERE ID=?
==> Parameters: u6c1de3ba(String), 用户_000004(String), 2(Integer), 13500000004(String), 2025-06-11 10:13:23.0(Timestamp), 2025-06-11 10:13:23.0(Timestamp), https://dev-1361654307.cos.ap-guangzhou.myqcloud.com/user/photo/2025-06-11/d1cd7d55e52a40d6a0768a7e8619740d.jpg(String), 170(Integer), 60(Integer), chubby(String), medium(String), ["casual","sports"](String), {"shoulderWidth":"3","waistShape":"1","belly":"1","hip":"1","hipShape":"1","armLength":"3","armCircum":"3","hipWidth":"3","thigh":"3","calf":"3","bodyFat":"3","bodyLength":"3"}(String), energy(String), rain(String), 1991-06-11(String), 10:14(String), 天津(String), 2025-06-11 10:13:23.0(Timestamp), 2025-06-11 10:15:00.882(Timestamp), 13(Long)
<==    Updates: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2b9c552c]
2025-06-11 10:15:00.906 [d874655549f8436aa1ee61b6fdaccf05] INFO  c.s.u.service.impl.UserServiceImpl  - 满足异步八字字段变更重新计算条件，启动异步任务，用户: u6c1de3ba
2025-06-11 10:15:00.915 [] INFO  c.s.u.service.impl.UserServiceImpl  - 开始异步生成八字信息，用户: u6c1de3ba, 姓名: rain
2025-06-11 10:15:00.915 [] INFO  c.s.u.service.impl.UserServiceImpl  - WuxingService 获取完成，耗时: 0ms，用户: u6c1de3ba
2025-06-11 10:15:00.915 [] INFO  c.s.u.service.impl.UserServiceImpl  - 开始调用八字计算服务，耗时: 0ms，用户: u6c1de3ba
2025-06-11 10:15:00.915 [] INFO  c.s.u.service.impl.WuxingServiceImpl  - 计算用户八字: u6c1de3ba
2025-06-11 10:15:00.917 [d874655549f8436aa1ee61b6fdaccf05] INFO  c.s.u.service.impl.UserServiceImpl  - 异步八字字段变更重新计算任务已提交到专用线程池，用户: u6c1de3ba
2025-06-11 10:15:00.920 [d874655549f8436aa1ee61b6fdaccf05] INFO  c.s.common.aop.RequestLoggingAspect  - <=== Response: com.stylishlink.user.controller.NewUserController.completeProfile() | Duration: 61ms | Result: {"code":200,"message":"操作成功","data":{"success":true,"message":"用户信息完善成功","hasBasicInfo":true,"hasWuxingInfo":true,"userInfo":{"id":13,"userId":"u6c1de3ba","nickname":"用户_000004","avatar":null,"gender":2,"phone":"13500000004","email":null,"photoUrl":"https://dev-1361654307.cos.ap-guangzhou.myqcloud.com/user/photo/2025-06-11/d1cd7d55e52a40d6a0768a7e8619740d.jpg","height":170,"weight":60,"bodyType":"chubby","skinTone":"medium","stylePreferences":["casual","sports"],"bodyShape":{"shoulderWidth":"3","waistShape":"1","belly":"1","hip":"1","hipShape":"1","armLength":"3","armCircum":"3","hipWidth":"3","thigh":"3","calf":"3","bodyFat":"3","bodyLength":"3"},"mode":"energy","residenceName":null,"residenceCode":null,"fullName":"rain","birthDate":"1991-06-11","birthTime":"10:14","birthPlace":"天津","createTime":"2025-06-11T02:13:23.000+00:00","updateTime":"2025-06-11T02:15:00.882+00:00"}},"timestamp":"2025-06-11T10:15:00.919215735"}
2025-06-11 10:15:00.998 [] ERROR c.s.u.service.impl.WuxingServiceImpl  - 计算八字失败: u6c1de3ba
feign.FeignException$InternalServerError: [500] during [POST] to [http://localhost:8084/api/ai/bazi/calculate] [AIServiceClient#calculateBaZi(Map)]: [{"code":9001,"message":"系统异常: JSON parse error: Unrecognized field \"fullName\" (class com.stylishlink.ai.dto.BaZiRequest), not marked as ignorable","timestamp":"2025-06-11 10:15:00"}]
	at feign.FeignException.serverErrorStatus(FeignException.java:259)
	at feign.FeignException.errorStatus(FeignException.java:206)
	at feign.FeignException.errorStatus(FeignException.java:194)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:103)
	at feign.InvocationContext.decodeError(InvocationContext.java:126)
	at feign.InvocationContext.proceed(InvocationContext.java:72)
	at feign.ResponseHandler.handleResponse(ResponseHandler.java:63)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:114)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:70)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:99)
	at org.springframework.cloud.openfeign.FeignCachingInvocationHandlerFactory$1.proceed(FeignCachingInvocationHandlerFactory.java:66)
	at org.springframework.cache.interceptor.CacheInterceptor.lambda$invoke$0(CacheInterceptor.java:64)
	at org.springframework.cache.interceptor.CacheAspectSupport.invokeOperation(CacheAspectSupport.java:416)
	at org.springframework.cache.interceptor.CacheAspectSupport.execute(CacheAspectSupport.java:401)
	at org.springframework.cache.interceptor.CacheInterceptor.invoke(CacheInterceptor.java:74)
	at org.springframework.cloud.openfeign.FeignCachingInvocationHandlerFactory.lambda$create$1(FeignCachingInvocationHandlerFactory.java:53)
	at jdk.proxy2/jdk.proxy2.$Proxy144.calculateBaZi(Unknown Source)
	at com.stylishlink.user.service.impl.WuxingServiceImpl.calculateBaZi(WuxingServiceImpl.java:166)
	at com.stylishlink.user.service.impl.UserServiceImpl.asyncGenerateBazi(UserServiceImpl.java:1381)
	at com.stylishlink.user.service.impl.UserServiceImpl.lambda$completeProfile$2(UserServiceImpl.java:220)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-06-11 10:15:01.001 [] ERROR c.s.u.service.impl.UserServiceImpl  - 异步八字生成失败，耗时: 86ms，用户: u6c1de3ba, 错误: 八字计算服务异常: [500] during [POST] to [http://localhost:8084/api/ai/bazi/calculate] [AIServiceClient#calculateBaZi(Map)]: [{"code":9001,"message":"系统异常: JSON parse error: Unrecognized field \"fullName\" (class com.stylishlink.ai.dto.BaZiRequest), not marked as ignorable","timestamp":"2025-06-11 10:15:00"}]
com.stylishlink.common.exception.BusinessException: 八字计算服务异常: [500] during [POST] to [http://localhost:8084/api/ai/bazi/calculate] [AIServiceClient#calculateBaZi(Map)]: [{"code":9001,"message":"系统异常: JSON parse error: Unrecognized field \"fullName\" (class com.stylishlink.ai.dto.BaZiRequest), not marked as ignorable","timestamp":"2025-06-11 10:15:00"}]


---

_**Assistant**_



---

[MODE: RESEARCH]

根据日志分析，问题出现在用户服务异步调用AI服务计算八字时，AI服务无法识别请求中的`fullName`字段。让我检查相关代码来定位问题。

---

<details>
            <summary>Searched codebase "BaZiRequest fullName JSON parse error" • **20** results</summary>
        
| File | Lines |
|------|-------|
| `backend/ai-service/src/main/java/com/stylishlink/ai/controller/BaZiController.java` | L595-621 |
| `backend/ai-service/src/main/java/com/stylishlink/ai/controller/BaZiController.java` | L257-282 |
| `backend/ai-service/src/main/java/com/stylishlink/ai/controller/BaZiController.java` | L541-564 |
| `backend/ai-service/src/main/java/com/stylishlink/ai/dto/BaZiRequest.java` | L38-72 |
| `backend/ai-service/src/main/java/com/stylishlink/ai/controller/BaZiController.java` | L564-595 |
| `backend/ai-service/src/main/java/com/stylishlink/ai/controller/BaZiController.java` | L201-227 |
| `backend/ai-service/src/main/java/com/stylishlink/ai/controller/BaZiController.java` | L227-257 |
| `backend/user-service/src/main/java/com/stylishlink/user/service/impl/UserServiceImpl.java` | L996-1031 |
| `backend/user-service/src/main/java/com/stylishlink/user/service/impl/WuxingServiceImpl.java` | L149-190 |
| `backend/ai-service/src/main/java/com/stylishlink/ai/controller/BaZiController.java` | L427-469 |
| `backend/ai-service/src/main/java/com/stylishlink/ai/service/impl/BaZiServiceImpl.java` | L53-91 |
| `backend/ai-service/src/main/java/com/stylishlink/ai/service/impl/EnergyServiceImpl.java` | L60-97 |
| `backend/ai-service/src/main/java/com/stylishlink/ai/controller/BaZiController.java` | L390-427 |
| `backend/user-service/src/main/java/com/stylishlink/user/service/impl/WuxingServiceImpl.java` | L129-149 |
| `backend/ai-service/src/main/java/com/stylishlink/ai/service/impl/BaZiServiceImpl.java` | L1-53 |
| `backend/ai-service/src/main/java/com/stylishlink/ai/dto/BaZiRequest.java` | L1-38 |
| `backend/ai-service/src/main/java/com/stylishlink/ai/controller/BaZiController.java` | L125-162 |
| `backend/ai-service/src/main/java/com/stylishlink/ai/service/impl/BaZiServiceImpl.java` | L91-126 |
| `backend/user-service/src/main/java/com/stylishlink/user/service/impl/FortuneReadingServiceImpl.java` | L150-184 |
| `backend/user-service/src/main/java/com/stylishlink/user/entity/User.java` | L239-288 |

</details>

---

现在我找到了问题的根源。让我检查AI服务的BaZiRequest类定义和用户服务中调用AI服务的代码：

---

<details>
            <summary>Searched codebase "WuxingServiceImpl calculateBaZi aiRequest fullName" • **20** results</summary>
        
| File | Lines |
|------|-------|
| `backend/ai-service/src/main/java/com/stylishlink/ai/service/impl/WuXingServiceImpl.java` | L216-245 |
| `backend/user-service/src/main/java/com/stylishlink/user/service/impl/WuxingServiceImpl.java` | L149-190 |
| `backend/ai-service/src/main/java/com/stylishlink/ai/controller/BaZiController.java` | L564-595 |
| `backend/user-service/src/main/java/com/stylishlink/user/service/impl/UserServiceImpl.java` | L996-1031 |
| `backend/user-service/src/main/java/com/stylishlink/user/service/impl/UserServiceImpl.java` | L411-464 |
| `backend/ai-service/src/main/java/com/stylishlink/ai/service/impl/EnergyServiceImpl.java` | L60-97 |
| `backend/ai-service/src/main/java/com/stylishlink/ai/service/impl/BaZiServiceImpl.java` | L91-126 |
| `backend/ai-service/src/main/java/com/stylishlink/ai/controller/BaZiController.java` | L257-282 |
| `backend/user-service/src/main/java/com/stylishlink/user/service/impl/UserServiceImpl.java` | L1351-1384 |
| `backend/ai-service/src/main/java/com/stylishlink/ai/service/impl/BaZiServiceImpl.java` | L1-53 |
| `backend/user-service/src/main/java/com/stylishlink/user/service/impl/WuxingServiceImpl.java` | L1-26 |
| `backend/user-service/src/main/java/com/stylishlink/user/service/impl/FortuneReadingServiceImpl.java` | L150-184 |
| `backend/user-service/src/main/java/com/stylishlink/user/service/impl/FortuneReadingServiceImpl.java` | L346-393 |
| `backend/ai-service/src/main/java/com/stylishlink/ai/service/impl/WuXingServiceImpl.java` | L465-516 |
| `backend/ai-service/src/main/java/com/stylishlink/ai/service/impl/WuXingServiceImpl.java` | L70-106 |
| `backend/user-service/src/main/java/com/stylishlink/user/service/impl/FortuneReadingServiceImpl.java` | L428-454 |
| `backend/user-service/src/main/java/com/stylishlink/user/service/impl/WuxingServiceImpl.java` | L92-129 |
| `backend/ai-service/src/main/java/com/stylishlink/ai/service/impl/WuXingServiceImpl.java` | L862-900 |
| `backend/user-service/src/main/java/com/stylishlink/user/dto/request/WuxingRequest.java` | L1-15 |
| `backend/user-service/src/main/java/com/stylishlink/user/service/impl/WuxingServiceImpl.java` | L129-149 |

</details>

---

现在我明白了问题所在。让我检查AI服务中BaZiRequest类，看看是否缺少`fullName`字段：

---

Read file: backend/ai-service/src/main/java/com/stylishlink/ai/dto/BaZiRequest.java

---

[MODE: INNOVATE]

现在我找到了问题的根本原因！用户服务在调用AI服务计算八字时，发送的请求中包含了`fullName`字段，但是AI服务的`BaZiRequest` DTO类中没有定义这个字段，导致JSON反序列化失败。

从代码分析可以看到：

1. **用户服务发送的请求**（在`WuxingServiceImpl.calculateBaZi`方法中）：
   ```java
   aiRequest.put("fullName", request.getFullName());
   aiRequest.put("birthDate", request.getBirthDate());
   aiRequest.put("birthTime", request.getBirthTime());
   aiRequest.put("birthPlace", request.getBirthPlace());
   aiRequest.put("gender", request.getGender());
   ```

2. **AI服务的BaZiRequest类**缺少`fullName`字段，只有：
   - `birthDate`
   - `birthTime` 
   - `gender`
   - `birthPlace`
   - `userId`

解决方案有两种：
1. **在AI服务的BaZiRequest类中添加fullName字段**（推荐）
2. 在用户服务中移除fullName字段的发送

考虑到八字计算可能需要姓名信息，第一种方案更合理。

[MODE: PLAN]

基于问题分析，我需要修复AI服务中BaZiRequest类缺少`fullName`字段的问题。

**变更计划**：

文件：`backend/ai-service/src/main/java/com/stylishlink/ai/dto/BaZiRequest.java`
理由：添加缺失的fullName字段，使其与用户服务发送的请求参数匹配

实施检查清单：
1. 在BaZiRequest类中添加fullName字段，包含适当的注解和文档
2. 验证字段顺序和命名规范符合项目标准
3. 确保新字段与用户服务发送的参数名称完全匹配

[MODE: EXECUTE]

正在执行检查清单第1项：在BaZiRequest类中添加fullName字段。

---



---

检查清单第1项已完成：✅ 在BaZiRequest类中添加了fullName字段，包含了合适的@Schema注解和示例值。

**检查清单项目1执行完成**：
- 步骤：在BaZiRequest类中添加fullName字段
- 修改：backend/ai-service/src/main/java/com/stylishlink/ai/dto/BaZiRequest.java - 添加fullName字段及相关注解
- 更改摘要：在BaZiRequest DTO类中添加了缺失的fullName字段，解决用户服务调用AI服务时的JSON反序列化错误
- 原因：执行计划步骤1 - 修复字段缺失问题
- 阻碍：无

请检查针对步骤1的更改。请确认状态（成功 / 成功但有小问题 / 失败）并在必要时提供反馈。

---

_**User**_

下面是AI服务的报错日志：2025-06-11 10:14:02 [nacos-grpc-client-executor-*********-81] INFO  com.alibaba.nacos.common.remote.client - [378ef072-2b36-4246-923c-1e914da1e969] Receive server push request, request = NotifySubscriberRequest, requestId = 3
2025-06-11 10:14:02 [nacos-grpc-client-executor-*********-81] INFO  com.alibaba.nacos.common.remote.client - [378ef072-2b36-4246-923c-1e914da1e969] Ack server push request, request = NotifySubscriberRequest, requestId = 3
2025-06-11 10:15:00 [http-nio-8084-exec-5] DEBUG com.stylishlink.ai.config.GatewayUserFilter - 处理请求: POST /api/ai/bazi/calculate, 网关用户信息 - User-Id: null, Nickname: null, Phone: null, Roles: null
2025-06-11 10:15:00 [http-nio-8084-exec-5] DEBUG com.stylishlink.ai.config.GatewayUserFilter - 请求缺少网关用户认证header，路径: /api/ai/bazi/calculate
2025-06-11 10:15:00 [http-nio-8084-exec-5] ERROR c.stylishlink.ai.exception.GlobalExceptionHandler - 运行时异常
org.springframework.http.converter.HttpMessageNotReadableException: JSON parse error: Unrecognized field "fullName" (class com.stylishlink.ai.dto.BaZiRequest), not marked as ignorable
	at org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter.readJavaType(AbstractJackson2HttpMessageConverter.java:406)
	at org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter.read(AbstractJackson2HttpMessageConverter.java:354)
	at org.springframework.web.servlet.mvc.method.annotation.AbstractMessageConverterMethodArgumentResolver.readWithMessageConverters(AbstractMessageConverterMethodArgumentResolver.java:184)
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.readWithMessageConverters(RequestResponseBodyMethodProcessor.java:159)
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.resolveArgument(RequestResponseBodyMethodProcessor.java:134)
	at org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:122)
	at org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:225)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:178)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:917)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:829)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.ObservationFilterChainDecorator$FilterObservation$SimpleFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:479)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:340)
	at org.springframework.security.web.ObservationFilterChainDecorator.lambda$wrapSecured$0(ObservationFilterChainDecorator.java:82)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:128)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at com.stylishlink.ai.config.GatewayUserFilter.doFilterInternal(GatewayUserFilter.java:77)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:195)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:225)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException: Unrecognized field "fullName" (class com.stylishlink.ai.dto.BaZiRequest), not marked as ignorable (5 known properties: "birthPlace", "gender", "birthTime", "birthDate", "userId"])
 at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 47] (through reference chain: com.stylishlink.ai.dto.BaZiRequest["fullName"])
	at com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException.from(UnrecognizedPropertyException.java:61)
	at com.fasterxml.jackson.databind.DeserializationContext.handleUnknownProperty(DeserializationContext.java:1138)
	at com.fasterxml.jackson.databind.deser.std.StdDeserializer.handleUnknownProperty(StdDeserializer.java:2224)
	at com.fasterxml.jackson.databind.deser.BeanDeserializerBase.handleUnknownProperty(BeanDeserializerBase.java:1709)
	at com.fasterxml.jackson.databind.deser.BeanDeserializerBase.handleUnknownVanilla(BeanDeserializerBase.java:1687)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.vanillaDeserialize(BeanDeserializer.java:320)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.deserialize(BeanDeserializer.java:177)
	at com.fasterxml.jackson.databind.deser.DefaultDeserializationContext.readRootValue(DefaultDeserializationContext.java:323)
	at com.fasterxml.jackson.databind.ObjectReader._bindAndClose(ObjectReader.java:2105)
	at com.fasterxml.jackson.databind.ObjectReader.readValue(ObjectReader.java:1481)
	at org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter.readJavaType(AbstractJackson2HttpMessageConverter.java:395)
	... 138 common frames omitted


---

_**Assistant**_



---

[MODE: RESEARCH]

我看到AI服务依然报告同样的错误，说明修改可能没有生效或者服务没有重新编译。让我检查文件是否真的被修改了：

---

Read file: backend/ai-service/src/main/java/com/stylishlink/ai/dto/BaZiRequest.java

---

文件确实已经修改了，包含了`fullName`字段。但是从AI服务的错误日志可以看出，它报告的已知属性只有5个：`["birthPlace", "gender", "birthTime", "birthDate", "userId"]`，没有包含`fullName`。

这说明AI服务需要重新编译和重启才能生效。让我检查项目结构，确认如何重启AI服务：

---

<details>
            <summary>Listed current directory • **26** results</summary>
        
| Name |
|-------|
| 📁 `.git` |
| 📁 `backend` |
| 📄 `TASK_weather_api_implementation.md` |
| 📄 `temp_pages.json` |
| 📄 `TASK_selectBodyShape_fix.md` |
| 📁 `doc` |
| 📄 `.DS_Store` |
| 📄 `.gitignore` |
| 📄 `application.log` |
| 📄 `file-service-run.log` |
| 📄 `gateway-stripprefix-test.log` |
| 📄 `final-gateway.log` |
| 📄 `new-gateway.log` |
| 📄 `final-test.log` |
| 📄 `test-gateway.log` |
| 📄 `gateway-new.log` |
| 📄 `gateway.log` |
| 📁 `.vscode` |
| 📄 `pnpm-lock.yaml` |
| 📄 `package.json` |
| 📄 `pnpm-workspace.yaml` |
| 📁 `frontend` |
| 📁 `database` |
| 📄 `.cursorindexingignore` |
| 📁 `.cursor` |
| 📁 `.specstory` |

</details>

---

<details>
            <summary>Listed directory backend • **16** results</summary>
        
| Name |
|-------|
| 📁 `.idea` |
| 📁 `ai-service` |
| 📁 `user-service` |
| 📁 `common` |
| 📁 `logs` |
| 📁 `file-service` |
| 📁 `gateway-service` |
| 📄 `README-MAVEN.md` |
| 📁 `recommendation-service` |
| 📁 `wardrobe-service` |
| 📁 `payment-service` |
| 📁 `operation-service` |
| 📁 `social-service` |
| 📁 `eureka-server` |
| 📁 `docs` |
| 📄 `pom.xml` |

</details>

---

让我检查AI服务是否有独立的启动脚本：

---

<details>
            <summary>Listed directory backend/ai-service • **13** results</summary>
        
| Name |
|-------|
| 📁 `target` |
| 📄 `pom.xml` |
| 📄 `deploy.sh` |
| 📁 `logs` |
| 📁 `docs` |
| 📄 `ai-service.log` |
| 📄 `start.sh` |
| 📄 `LUCKY_ELEMENTS_SUMMARIZE_API.md` |
| 📄 `.DS_Store` |
| 📄 `FEIGN_MIGRATION.md` |
| 📁 `src` |
| 📄 `README.md` |
| 📄 `DEPLOYMENT.md` |

</details>

---

发现AI服务有自己的启动脚本。现在让我重新编译并重启AI服务：

---

```bash
cd backend/ai-service && mvn clean compile
```

```
[INFO] Scanning for projects...
[INFO] 
[INFO] ---------------< com.stylishlink:stylishlink-ai-service >---------------
[INFO] Building StylishLink - AI Service 1.0.0-SNAPSHOT
[INFO]   from pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] --- clean:3.2.0:clean (default-clean) @ stylishlink-ai-service ---
[INFO] Deleting /Users/<USER>/pa/codes/stylishlink/backend/ai-service/target
[INFO] 
[INFO] --- resources:3.3.1:resources (default-resources) @ stylishlink-ai-servic
e ---
[INFO] Copying 4 resources from src/main/resources to target/classes
[INFO] 
[INFO] --- compiler:3.11.0:compile (default-compile) @ stylishlink-ai-service --
-
[INFO] Changes detected - recompiling the module! :source
[INFO] Compiling 75 source files with javac [debug target 17] to target/classes
[INFO] /Users/<USER>/pa/codes/stylishlink/backend/ai-service/src/main/java/co
m/stylishlink/ai/client/impl
/DoubaoVisionClientImpl.java: 某些输入文件使用了未经检查或不安全的操作。
       [INFO] /Users/<USER>/pa/codes/stylishlink/backend/ai-service/src/main/
java/com/stylishlink/ai/client/impl
/DoubaoVisionClientImpl.java: 有关详细信息, 请使用 -Xlint:unchecked 重新编译。
              [INFO] -----------------------------------------------------------
-------------
[INFO] BUILD SUCCESS
[INFO] ------------------------------------------------------------------------
[INFO] Total time:  7.835 s
[INFO] Finished at: 2025-06-11T10:22:50+08:00
[INFO] ------------------------------------------------------------------------

```

---

编译成功！现在需要重启AI服务。让我检查AI服务的启动脚本：

---

Read file: backend/ai-service/start.sh

---

现在我需要先停止AI服务，然后重新启动它。首先检查当前是否有AI服务进程在运行：

---

```bash
./start.sh status
```

```
(base) liuyongyu@liuyongyudeMacBook-Pro ai-service % ./start.sh status
[INFO] stylishlink-ai-service 状态检查
----------------------------------------
状态: 未运行
JAR文件: ./stylishlink-ai-service-1.0.0-SNAPSHOT.jar
日志文件: /Users/<USER>/pa/codes/stylishlink/backend/ai-service/logs/stylishlink-ai-service.log
PID文件: /Users/<USER>/pa/codes/stylishlink/backend/ai-service/stylishlink-ai-service.pid
(base) liuyongyu@liuyongyudeMacBook-Pro ai-service %
```

---

AI服务显示未运行，但是从日志看它确实在运行。让我检查是否有相关的Java进程：

---

```bash
ps aux | grep stylishlink-ai-service
```

```
(base) liuyongyu@liuyongyudeMacBook-Pro ai-service % ps aux | grep stylishlink-ai-service
liuyongyu        89467   0.0  0.0 33615272    716 s113  S+   10:23AM   0:00.01 grep stylishlink-ai-service
(base) liuyongyu@liuyongyudeMacBook-Pro ai-service %
```

---

看起来AI服务可能是通过其他方式启动的。让我查看是否有通过Spring Boot方式启动的Java进程：

---

```bash
ps aux | grep java | grep 8084
```

```
(base) liuyongyu@liuyongyudeMacBook-Pro ai-service % 

```

---

让我查看所有Java进程：

---

```bash
ps aux | grep java
```

```
liuyongyu        31951   0.0  0.2 40552660  30668   ??  S     5:41PM   3:08.72 /Library/Java/JavaVirtualMach
ines/jdk-17.jdk/Contents/Home/bin/java -javaagent:/Applications/IntelliJ IDEA CE.app/Contents/lib/idea_rt.ja
r=52397 -Dfile.encoding=UTF-8 -classpath /Users/<USER>/pa/codes/stylishlink/backend/gateway-service/targe
t/classes:/Users/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-starter-gateway/4.1.0/sprin
g-cloud-starter-gateway-4.1.0.jar:/Users/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-sta
rter/4.1.0/spring-cloud-starter-4.1.0.jar:/Users/<USER>/.m2/repository/org/springframework/security/sprin
g-security-rsa/1.1.1/spring-security-rsa-1.1.1.jar:/Users/<USER>/.m2/repository/org/bouncycastle/bcprov-j
dk18on/1.74/bcprov-jdk18on-1.74.jar:/Users/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-g
ateway-server/4.1.0/spring-cloud-gateway-server-4.1.0.jar:/Users/<USER>/.m2/repository/io/projectreactor/
addons/reactor-extra/3.5.1/reactor-extra-3.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/
spring-boot-starter-webflux/3.2.2/spring-boot-starter-webflux-3.2.2.jar:/Users/<USER>/.m2/repository/org/
springframework/boot/spring-boot-starter-json/3.2.2/spring-boot-starter-json-3.2.2.jar:/Users/<USER>/.m2/
repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.15.3/jackson-datatype-jdk8-2.15.3.jar:/Use
rs/liuyongyu/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.15.3/jackson-modul
e-parameter-names-2.15.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-re
actor-netty/3.2.2/spring-boot-starter-reactor-netty-3.2.2.jar:/Users/<USER>/.m2/repository/io/projectreac
tor/netty/reactor-netty-http/1.1.15/reactor-netty-http-1.1.15.jar:/Users/<USER>/.m2/repository/io/netty/n
etty-codec-http/4.1.105.Final/netty-codec-http-4.1.105.Final.jar:/Users/<USER>/.m2/repository/io/netty/ne
tty-codec-http2/4.1.105.Final/netty-codec-http2-4.1.105.Final.jar:/Users/<USER>/.m2/repository/io/netty/n
etty-resolver-dns/4.1.105.Final/netty-resolver-dns-4.1.105.Final.jar:/Users/<USER>/.m2/repository/io/nett
y/netty-codec-dns/4.1.105.Final/netty-codec-dns-4.1.105.Final.jar:/Users/<USER>/.m2/repository/io/netty/n
etty-resolver-dns-native-macos/4.1.105.Final/netty-resolver-dns-native-macos-4.1.105.Final-osx-x86_64.jar:/U
sers/liuyongyu/.m2/repository/io/netty/netty-resolver-dns-classes-macos/4.1.105.Final/netty-resolver-dns-cla
sses-macos-4.1.105.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-native-epoll/4.1.105.F
inal/netty-transport-native-epoll-4.1.105.Final-linux-x86_64.jar:/Users/<USER>/.m2/repository/io/netty/ne
tty-transport-classes-epoll/4.1.105.Final/netty-transport-classes-epoll-4.1.105.Final.jar:/Users/<USER>/.
m2/repository/io/projectreactor/netty/reactor-netty-core/1.1.15/reactor-netty-core-1.1.15.jar:/Users/<USER>
yu/.m2/repository/io/netty/netty-handler-proxy/4.1.105.Final/netty-handler-proxy-4.1.105.Final.jar:/Users/<USER>
uyongyu/.m2/repository/io/netty/netty-codec-socks/4.1.105.Final/netty-codec-socks-4.1.105.Final.jar:/Users/<USER>
iuyongyu/.m2/repository/org/springframework/spring-web/6.1.3/spring-web-6.1.3.jar:/Users/<USER>/.m2/repos
itory/org/springframework/spring-webflux/6.1.3/spring-webflux-6.1.3.jar:/Users/<USER>/.m2/repository/com/
alibaba/cloud/spring-cloud-starter-alibaba-nacos-discovery/2023.0.1.0/spring-cloud-starter-alibaba-nacos-dis
covery-2023.0.1.0.jar:/Users/<USER>/.m2/repository/com/alibaba/cloud/spring-cloud-alibaba-commons/2023.0.
1.0/spring-cloud-alibaba-commons-2023.0.1.0.jar:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-clie
nt/2.3.2/nacos-client-2.3.2.jar:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-auth-plugin/2.3.2/na
cos-auth-plugin-2.3.2.jar:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-encryption-plugin/2.3.2/na
cos-encryption-plugin-2.3.2.jar:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.16.0/commons-c
odec-1.16.0.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.15.3/jackson-core-
2.15.3.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpasyncclient/4.1.5/httpasyncclient-4
.1.5.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore-nio/4.4.16/httpcore-nio-4.4.16.j
ar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpclient/4.5.13/httpclient-4.5.13.jar:/Users/
liuyongyu/.m2/repository/org/apache/httpcomponents/httpcore/4.4.16/httpcore-4.4.16.jar:/Users/<USER>/.m2/
repository/io/prometheus/simpleclient/0.16.0/simpleclient-0.16.0.jar:/Users/<USER>/.m2/repository/io/prom
etheus/simpleclient_tracer_otel/0.16.0/simpleclient_tracer_otel-0.16.0.jar:/Users/<USER>/.m2/repository/i
o/prometheus/simpleclient_tracer_common/0.16.0/simpleclient_tracer_common-0.16.0.jar:/Users/<USER>/.m2/re
pository/io/prometheus/simpleclient_tracer_otel_agent/0.16.0/simpleclient_tracer_otel_agent-0.16.0.jar:/User
s/liuyongyu/.m2/repository/io/micrometer/micrometer-core/1.12.2/micrometer-core-1.12.2.jar:/Users/<USER>/
.m2/repository/org/hdrhistogram/HdrHistogram/2.1.12/HdrHistogram-2.1.12.jar:/Users/<USER>/.m2/repository/
org/latencyutils/LatencyUtils/2.0.3/LatencyUtils-2.0.3.jar:/Users/<USER>/.m2/repository/com/alibaba/sprin
g/spring-context-support/1.0.11/spring-context-support-1.0.11.jar:/Users/<USER>/.m2/repository/org/spring
framework/cloud/spring-cloud-commons/4.1.0/spring-cloud-commons-4.1.0.jar:/Users/<USER>/.m2/repository/or
g/springframework/security/spring-security-crypto/6.2.1/spring-security-crypto-6.2.1.jar:/Users/<USER>/.m
2/repository/org/springframework/cloud/spring-cloud-context/4.1.0/spring-cloud-context-4.1.0.jar:/Users/<USER>
ongyu/.m2/repository/com/alibaba/cloud/spring-cloud-starter-alibaba-nacos-config/2023.0.1.0/spring-cloud-sta
rter-alibaba-nacos-config-2023.0.1.0.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/2.0.11/slf4j-ap
i-2.0.11.jar:/Users/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/2.1.1/jakarta.annotat
ion-api-2.1.1.jar:/Users/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-starter-config/4.1.
0/spring-cloud-starter-config-4.1.0.jar:/Users/<USER>/.m2/repository/org/springframework/cloud/spring-clo
ud-config-client/4.1.0/spring-cloud-config-client-4.1.0.jar:/Users/<USER>/.m2/repository/com/fasterxml/ja
ckson/core/jackson-annotations/2.15.3/jackson-annotations-2.15.3.jar:/Users/<USER>/.m2/repository/org/apa
che/httpcomponents/client5/httpclient5/5.2.3/httpclient5-5.2.3.jar:/Users/<USER>/.m2/repository/org/apach
e/httpcomponents/core5/httpcore5/5.2.4/httpcore5-5.2.4.jar:/Users/<USER>/.m2/repository/org/apache/httpco
mponents/core5/httpcore5-h2/5.2.4/httpcore5-h2-5.2.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jacks
on/core/jackson-databind/2.15.3/jackson-databind-2.15.3.jar:/Users/<USER>/.m2/repository/org/springframew
ork/boot/spring-boot-starter-security/3.2.2/spring-boot-starter-security-3.2.2.jar:/Users/<USER>/.m2/repo
sitory/org/springframework/spring-aop/6.1.3/spring-aop-6.1.3.jar:/Users/<USER>/.m2/repository/org/springf
ramework/spring-beans/6.1.3/spring-beans-6.1.3.jar:/Users/<USER>/.m2/repository/org/springframework/secur
ity/spring-security-config/6.2.1/spring-security-config-6.2.1.jar:/Users/<USER>/.m2/repository/org/spring
framework/security/spring-security-core/6.2.1/spring-security-core-6.2.1.jar:/Users/<USER>/.m2/repository
/org/springframework/spring-context/6.1.3/spring-context-6.1.3.jar:/Users/<USER>/.m2/repository/org/sprin
gframework/security/spring-security-web/6.2.1/spring-security-web-6.2.1.jar:/Users/<USER>/.m2/repository/
org/springframework/spring-expression/6.1.3/spring-expression-6.1.3.jar:/Users/<USER>/.m2/repository/org/
springdoc/springdoc-openapi-starter-webflux-ui/2.5.0/springdoc-openapi-starter-webflux-ui-2.5.0.jar:/Users/<USER>
iuyongyu/.m2/repository/org/springdoc/springdoc-openapi-starter-webflux-api/2.5.0/springdoc-openapi-starter-
webflux-api-2.5.0.jar:/Users/<USER>/.m2/repository/org/springdoc/springdoc-openapi-starter-common/2.5.0/s
pringdoc-openapi-starter-common-2.5.0.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-core-ja
karta/2.2.21/swagger-core-jakarta-2.2.21.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-anno
tations-jakarta/2.2.21/swagger-annotations-jakarta-2.2.21.jar:/Users/<USER>/.m2/repository/io/swagger/cor
e/v3/swagger-models-jakarta/2.2.21/swagger-models-jakarta-2.2.21.jar:/Users/<USER>/.m2/repository/com/fas
terxml/jackson/dataformat/jackson-dataformat-yaml/2.15.3/jackson-dataformat-yaml-2.15.3.jar:/Users/<USER>
/.m2/repository/org/webjars/swagger-ui/5.13.0/swagger-ui-5.13.0.jar:/Users/<USER>/.m2/repository/io/jsonw
ebtoken/jjwt-api/0.12.5/jjwt-api-0.12.5.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-impl/0.12.5
/jjwt-impl-0.12.5.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-jackson/0.12.5/jjwt-jackson-0.12.
5.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-actuator/3.2.2/spring-boo
t-starter-actuator-3.2.2.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator-a
utoconfigure/3.2.2/spring-boot-actuator-autoconfigure-3.2.2.jar:/Users/<USER>/.m2/repository/org/springfr
amework/boot/spring-boot-actuator/3.2.2/spring-boot-actuator-3.2.2.jar:/Users/<USER>/.m2/repository/io/mi
crometer/micrometer-observation/1.12.2/micrometer-observation-1.12.2.jar:/Users/<USER>/.m2/repository/io/
micrometer/micrometer-commons/1.12.2/micrometer-commons-1.12.2.jar:/Users/<USER>/.m2/repository/io/microm
eter/micrometer-jakarta9/1.12.2/micrometer-jakarta9-1.12.2.jar:/Users/<USER>/.m2/repository/org/springfra
mework/boot/spring-boot-starter-data-redis-reactive/3.2.2/spring-boot-starter-data-redis-reactive-3.2.2.jar:
/Users/<USER>/.m2/repository/io/lettuce/lettuce-core/6.3.1.RELEASE/lettuce-core-6.3.1.RELEASE.jar:/Users/
liuyongyu/.m2/repository/io/netty/netty-common/4.1.105.Final/netty-common-4.1.105.Final.jar:/Users/<USER>
/.m2/repository/io/netty/netty-handler/4.1.105.Final/netty-handler-4.1.105.Final.jar:/Users/<USER>/.m2/re
pository/io/netty/netty-resolver/4.1.105.Final/netty-resolver-4.1.105.Final.jar:/Users/<USER>/.m2/reposit
ory/io/netty/netty-buffer/4.1.105.Final/netty-buffer-4.1.105.Final.jar:/Users/<USER>/.m2/repository/io/ne
tty/netty-transport-native-unix-common/4.1.105.Final/netty-transport-native-unix-common-4.1.105.Final.jar:/U
sers/liuyongyu/.m2/repository/io/netty/netty-codec/4.1.105.Final/netty-codec-4.1.105.Final.jar:/Users/<USER>
gyu/.m2/repository/io/netty/netty-transport/4.1.105.Final/netty-transport-4.1.105.Final.jar:/Users/<USER>
/.m2/repository/io/projectreactor/reactor-core/3.6.2/reactor-core-3.6.2.jar:/Users/<USER>/.m2/repository/
org/reactivestreams/reactive-streams/1.0.4/reactive-streams-1.0.4.jar:/Users/<USER>/.m2/repository/org/sp
ringframework/data/spring-data-redis/3.2.2/spring-data-redis-3.2.2.jar:/Users/<USER>/.m2/repository/org/s
pringframework/data/spring-data-keyvalue/3.2.2/spring-data-keyvalue-3.2.2.jar:/Users/<USER>/.m2/repositor
y/org/springframework/data/spring-data-commons/3.2.2/spring-data-commons-3.2.2.jar:/Users/<USER>/.m2/repo
sitory/org/springframework/spring-tx/6.1.3/spring-tx-6.1.3.jar:/Users/<USER>/.m2/repository/org/springfra
mework/spring-oxm/6.1.3/spring-oxm-6.1.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-cont
ext-support/6.1.3/spring-context-support-6.1.3.jar:/Users/<USER>/pa/codes/stylishlink/backend/common/targ
et/classes:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-aop/3.2.2/spring-boo
t-starter-aop-3.2.2.jar:/Users/<USER>/.m2/repository/org/aspectj/aspectjweaver/1.9.21/aspectjweaver-1.9.2
1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.15.3/jackson-
datatype-jsr310-2.15.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-vali
dation/3.2.2/spring-boot-starter-validation-3.2.2.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embe
d/tomcat-embed-el/10.1.18/tomcat-embed-el-10.1.18.jar:/Users/<USER>/.m2/repository/org/hibernate/validato
r/hibernate-validator/8.0.1.Final/hibernate-validator-8.0.1.Final.jar:/Users/<USER>/.m2/repository/jakart
a/validation/jakarta.validation-api/3.0.2/jakarta.validation-api-3.0.2.jar:/Users/<USER>/.m2/repository/o
rg/jboss/logging/jboss-logging/3.5.3.Final/jboss-logging-3.5.3.Final.jar:/Users/<USER>/.m2/repository/com
/fasterxml/classmate/1.6.0/classmate-1.6.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spri
ng-boot-starter-data-mongodb/3.2.2/spring-boot-starter-data-mongodb-3.2.2.jar:/Users/<USER>/.m2/repositor
y/org/mongodb/mongodb-driver-sync/4.11.1/mongodb-driver-sync-4.11.1.jar:/Users/<USER>/.m2/repository/org/
mongodb/bson/4.11.1/bson-4.11.1.jar:/Users/<USER>/.m2/repository/org/mongodb/mongodb-driver-core/4.11.1/m
ongodb-driver-core-4.11.1.jar:/Users/<USER>/.m2/repository/org/mongodb/bson-record-codec/4.11.1/bson-reco
rd-codec-4.11.1.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-mongodb/4.2.2/sprin
g-data-mongodb-4.2.2.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.14.0/commons-lan
g3-3.14.0.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-collections4/4.4/commons-collection
s4-4.4.jar:/Users/<USER>/.m2/repository/commons-io/commons-io/2.15.1/commons-io-2.15.1.jar:/Users/<USER>
yu/.m2/repository/com/google/guava/guava/32.1.3-jre/guava-32.1.3-jre.jar:/Users/<USER>/.m2/repository/com
/google/guava/failureaccess/1.0.1/failureaccess-1.0.1.jar:/Users/<USER>/.m2/repository/com/google/guava/l
istenablefuture/9999.0-empty-to-avoid-conflict-with-guava/listenablefuture-9999.0-empty-to-avoid-conflict-wi
th-guava.jar:/Users/<USER>/.m2/repository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar:/Users/<USER>
iuyongyu/.m2/repository/org/checkerframework/checker-qual/3.37.0/checker-qual-3.37.0.jar:/Users/<USER>/.m
2/repository/com/google/errorprone/error_prone_annotations/2.21.1/error_prone_annotations-2.21.1.jar:/Users/
liuyongyu/.m2/repository/com/google/j2objc/j2objc-annotations/2.8/j2objc-annotations-2.8.jar:/Users/<USER>
u/.m2/repository/cn/hutool/hutool-all/5.8.25/hutool-all-5.8.25.jar:/Users/<USER>/.m2/repository/org/mapst
ruct/mapstruct/1.5.5.Final/mapstruct-1.5.5.Final.jar:/Users/<USER>/.m2/repository/jakarta/xml/bind/jakart
a.xml.bind-api/4.0.1/jakarta.xml.bind-api-4.0.1.jar:/Users/<USER>/.m2/repository/jakarta/activation/jakar
ta.activation-api/2.1.2/jakarta.activation-api-2.1.2.jar:/Users/<USER>/.m2/repository/org/springframework
/spring-core/6.1.3/spring-core-6.1.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/6.1.
3/spring-jcl-6.1.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/3.2.2/sp
ring-boot-starter-3.2.2.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/3.2.2/sprin
g-boot-3.2.2.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/3.2.2/sp
ring-boot-autoconfigure-3.2.2.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-start
er-logging/3.2.2/spring-boot-starter-logging-3.2.2.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logbac
k-classic/1.4.14/logback-classic-1.4.14.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.4.
14/logback-core-1.4.14.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.21.1/lo
g4j-to-slf4j-2.21.1.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.21.1/log4j-api-
2.21.1.jar:/Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/2.0.11/jul-to-slf4j-2.0.11.jar:/Users/<USER>
ongyu/.m2/repository/org/yaml/snakeyaml/2.2/snakeyaml-2.2.jar com.stylishlink.gateway.GatewayServiceApplicat
ion
liuyongyu        29080   0.0  0.2 40419784  34432   ??  S     5:14PM   3:29.23 /Library/Java/JavaVirtualMach
ines/jdk-17.jdk/Contents/Home/bin/java -javaagent:/Applications/IntelliJ IDEA CE.app/Contents/lib/idea_rt.ja
r=62495 -Dfile.encoding=UTF-8 -classpath /Users/<USER>/pa/codes/stylishlink/backend/file-service/target/c
lasses:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/3.2.2/spring-boot-st
arter-web-3.2.2.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/3.2.2/sprin
g-boot-starter-3.2.2.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/3.2.2/spring-b
oot-3.2.2.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/3.2.2/spr
ing-boot-starter-logging-3.2.2.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.4.14/log
back-classic-1.4.14.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.4.14/logback-core-1.4.
14.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.21.1/log4j-to-slf4j-2.21.1.
jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.21.1/log4j-api-2.21.1.jar:/Users/<USER>
uyongyu/.m2/repository/org/slf4j/jul-to-slf4j/2.0.11/jul-to-slf4j-2.0.11.jar:/Users/<USER>/.m2/repository
/org/yaml/snakeyaml/2.2/snakeyaml-2.2.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-bo
ot-starter-json/3.2.2/spring-boot-starter-json-3.2.2.jar:/Users/<USER>/.m2/repository/com/fasterxml/jacks
on/datatype/jackson-datatype-jdk8/2.15.3/jackson-datatype-jdk8-2.15.3.jar:/Users/<USER>/.m2/repository/co
m/fasterxml/jackson/module/jackson-module-parameter-names/2.15.3/jackson-module-parameter-names-2.15.3.jar:/
Users/liuyongyu/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/3.2.2/spring-boot-starter
-tomcat-3.2.2.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/10.1.18/tomcat-e
mbed-core-10.1.18.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/10.1.18
/tomcat-embed-websocket-10.1.18.jar:/Users/<USER>/.m2/repository/org/springframework/spring-web/6.1.3/spr
ing-web-6.1.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-beans/6.1.3/spring-beans-6.1.3.
jar:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/6.1.3/spring-webmvc-6.1.3.jar:/Users/<USER>
iuyongyu/.m2/repository/org/springframework/spring-context/6.1.3/spring-context-6.1.3.jar:/Users/<USER>/.
m2/repository/org/springframework/spring-expression/6.1.3/spring-expression-6.1.3.jar:/Users/<USER>/.m2/r
epository/org/springframework/boot/spring-boot-starter-validation/3.2.2/spring-boot-starter-validation-3.2.2
.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/10.1.18/tomcat-embed-el-10.1.18
.jar:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/8.0.1.Final/hibernate-valid
ator-8.0.1.Final.jar:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.5.3.Final/jboss-loggi
ng-3.5.3.Final.jar:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.6.0/classmate-1.6.0.jar:/Users/
liuyongyu/.m2/repository/jakarta/validation/jakarta.validation-api/3.0.2/jakarta.validation-api-3.0.2.jar:/U
sers/liuyongyu/.m2/repository/com/alibaba/cloud/spring-cloud-starter-alibaba-nacos-discovery/2022.0.0.0/spri
ng-cloud-starter-alibaba-nacos-discovery-2022.0.0.0.jar:/Users/<USER>/.m2/repository/com/alibaba/cloud/sp
ring-cloud-alibaba-commons/2022.0.0.0/spring-cloud-alibaba-commons-2022.0.0.0.jar:/Users/<USER>/.m2/repos
itory/com/alibaba/nacos/nacos-client/2.2.1/nacos-client-2.2.1.jar:/Users/<USER>/.m2/repository/com/alibab
a/nacos/nacos-auth-plugin/2.2.1/nacos-auth-plugin-2.2.1.jar:/Users/<USER>/.m2/repository/com/alibaba/naco
s/nacos-encryption-plugin/2.2.1/nacos-encryption-plugin-2.2.1.jar:/Users/<USER>/.m2/repository/org/apache
/httpcomponents/httpasyncclient/4.1.5/httpasyncclient-4.1.5.jar:/Users/<USER>/.m2/repository/org/apache/h
ttpcomponents/httpcore-nio/4.4.16/httpcore-nio-4.4.16.jar:/Users/<USER>/.m2/repository/io/prometheus/simp
leclient/0.16.0/simpleclient-0.16.0.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient_tracer_ot
el/0.16.0/simpleclient_tracer_otel-0.16.0.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient_tra
cer_common/0.16.0/simpleclient_tracer_common-0.16.0.jar:/Users/<USER>/.m2/repository/io/prometheus/simple
client_tracer_otel_agent/0.16.0/simpleclient_tracer_otel_agent-0.16.0.jar:/Users/<USER>/.m2/repository/co
m/alibaba/spring/spring-context-support/1.0.11/spring-context-support-1.0.11.jar:/Users/<USER>/.m2/reposi
tory/org/springframework/cloud/spring-cloud-commons/4.1.0/spring-cloud-commons-4.1.0.jar:/Users/<USER>/.m
2/repository/org/springframework/security/spring-security-crypto/6.2.1/spring-security-crypto-6.2.1.jar:/Use
rs/liuyongyu/.m2/repository/org/springframework/cloud/spring-cloud-context/4.1.0/spring-cloud-context-4.1.0.
jar:/Users/<USER>/.m2/repository/com/alibaba/cloud/spring-cloud-starter-alibaba-nacos-config/2022.0.0.0/s
pring-cloud-starter-alibaba-nacos-config-2022.0.0.0.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/
2.0.11/slf4j-api-2.0.11.jar:/Users/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/2.1.1/
jakarta.annotation-api-2.1.1.jar:/Users/<USER>/.m2/repository/com/mysql/mysql-connector-j/8.3.0/mysql-con
nector-j-8.3.0.jar:/Users/<USER>/.m2/repository/com/google/protobuf/protobuf-java/3.25.1/protobuf-java-3.
25.1.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-boot-starter/3.5.5/mybatis-plus-boot-star
ter-3.5.5.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus/3.5.5/mybatis-plus-3.5.5.jar:/Users/
liuyongyu/.m2/repository/com/baomidou/mybatis-plus-core/3.5.5/mybatis-plus-core-3.5.5.jar:/Users/<USER>/.
m2/repository/com/baomidou/mybatis-plus-annotation/3.5.5/mybatis-plus-annotation-3.5.5.jar:/Users/<USER>/
.m2/repository/com/baomidou/mybatis-plus-extension/3.5.5/mybatis-plus-extension-3.5.5.jar:/Users/<USER>/.
m2/repository/org/mybatis/mybatis/3.5.15/mybatis-3.5.15.jar:/Users/<USER>/.m2/repository/com/github/jsqlp
arser/jsqlparser/4.6/jsqlparser-4.6.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring-boo
t-autoconfigure/3.5.5/mybatis-plus-spring-boot-autoconfigure-3.5.5.jar:/Users/<USER>/.m2/repository/org/s
pringframework/boot/spring-boot-autoconfigure/3.2.2/spring-boot-autoconfigure-3.2.2.jar:/Users/<USER>/.m2
/repository/org/springframework/boot/spring-boot-starter-jdbc/3.2.2/spring-boot-starter-jdbc-3.2.2.jar:/User
s/liuyongyu/.m2/repository/com/zaxxer/HikariCP/5.0.1/HikariCP-5.0.1.jar:/Users/<USER>/.m2/repository/org/
springframework/spring-jdbc/6.1.3/spring-jdbc-6.1.3.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis-
spring/3.0.3/mybatis-spring-3.0.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-s
tarter-data-redis/3.2.2/spring-boot-starter-data-redis-3.2.2.jar:/Users/<USER>/.m2/repository/io/lettuce/
lettuce-core/6.3.1.RELEASE/lettuce-core-6.3.1.RELEASE.jar:/Users/<USER>/.m2/repository/io/netty/netty-com
mon/4.1.105.Final/netty-common-4.1.105.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-handler/4.1.
105.Final/netty-handler-4.1.105.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver/4.1.105.Fi
nal/netty-resolver-4.1.105.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-buffer/4.1.105.Final/net
ty-buffer-4.1.105.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-native-unix-common/4.1.
105.Final/netty-transport-native-unix-common-4.1.105.Final.jar:/Users/<USER>/.m2/repository/io/netty/nett
y-codec/4.1.105.Final/netty-codec-4.1.105.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport
/4.1.105.Final/netty-transport-4.1.105.Final.jar:/Users/<USER>/.m2/repository/io/projectreactor/reactor-c
ore/3.6.2/reactor-core-3.6.2.jar:/Users/<USER>/.m2/repository/org/reactivestreams/reactive-streams/1.0.4/
reactive-streams-1.0.4.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-redis/3.2.2/
spring-data-redis-3.2.2.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-keyvalue/3.
2.2/spring-data-keyvalue-3.2.2.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-comm
ons/3.2.2/spring-data-commons-3.2.2.jar:/Users/<USER>/.m2/repository/org/springframework/spring-tx/6.1.3/
spring-tx-6.1.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-oxm/6.1.3/spring-oxm-6.1.3.ja
r:/Users/<USER>/.m2/repository/org/springframework/spring-context-support/6.1.3/spring-context-support-6.
1.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-webflux/3.2.2/spring-bo
ot-starter-webflux-3.2.2.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-re
actor-netty/3.2.2/spring-boot-starter-reactor-netty-3.2.2.jar:/Users/<USER>/.m2/repository/io/projectreac
tor/netty/reactor-netty-http/1.1.15/reactor-netty-http-1.1.15.jar:/Users/<USER>/.m2/repository/io/netty/n
etty-codec-http/4.1.105.Final/netty-codec-http-4.1.105.Final.jar:/Users/<USER>/.m2/repository/io/netty/ne
tty-codec-http2/4.1.105.Final/netty-codec-http2-4.1.105.Final.jar:/Users/<USER>/.m2/repository/io/netty/n
etty-resolver-dns/4.1.105.Final/netty-resolver-dns-4.1.105.Final.jar:/Users/<USER>/.m2/repository/io/nett
y/netty-codec-dns/4.1.105.Final/netty-codec-dns-4.1.105.Final.jar:/Users/<USER>/.m2/repository/io/netty/n
etty-resolver-dns-native-macos/4.1.105.Final/netty-resolver-dns-native-macos-4.1.105.Final-osx-x86_64.jar:/U
sers/liuyongyu/.m2/repository/io/netty/netty-resolver-dns-classes-macos/4.1.105.Final/netty-resolver-dns-cla
sses-macos-4.1.105.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-native-epoll/4.1.105.F
inal/netty-transport-native-epoll-4.1.105.Final-linux-x86_64.jar:/Users/<USER>/.m2/repository/io/netty/ne
tty-transport-classes-epoll/4.1.105.Final/netty-transport-classes-epoll-4.1.105.Final.jar:/Users/<USER>/.
m2/repository/io/projectreactor/netty/reactor-netty-core/1.1.15/reactor-netty-core-1.1.15.jar:/Users/<USER>
yu/.m2/repository/io/netty/netty-handler-proxy/4.1.105.Final/netty-handler-proxy-4.1.105.Final.jar:/Users/<USER>
uyongyu/.m2/repository/io/netty/netty-codec-socks/4.1.105.Final/netty-codec-socks-4.1.105.Final.jar:/Users/<USER>
iuyongyu/.m2/repository/org/springframework/spring-webflux/6.1.3/spring-webflux-6.1.3.jar:/Users/<USER>/.
m2/repository/com/qcloud/cos_api/5.6.155/cos_api-5.6.155.jar:/Users/<USER>/.m2/repository/org/apache/http
components/httpclient/4.5.13/httpclient-4.5.13.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents
/httpcore/4.4.16/httpcore-4.4.16.jar:/Users/<USER>/.m2/repository/commons-logging/commons-logging/1.2/com
mons-logging-1.2.jar:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.16.0/commons-codec-1.16.0
.jar:/Users/<USER>/.m2/repository/joda-time/joda-time/2.9.9/joda-time-2.9.9.jar:/Users/<USER>/.m2/repo
sitory/com/tencentcloudapi/tencentcloud-sdk-java-kms/3.1.213/tencentcloud-sdk-java-kms-3.1.213.jar:/Users/<USER>
uyongyu/.m2/repository/com/tencentcloudapi/tencentcloud-sdk-java-common/3.1.213/tencentcloud-sdk-java-common
-3.1.213.jar:/Users/<USER>/.m2/repository/com/squareup/okhttp/logging-interceptor/2.7.5/logging-intercept
or-2.7.5.jar:/Users/<USER>/.m2/repository/com/squareup/okhttp/okhttp/2.7.5/okhttp-2.7.5.jar:/Users/<USER>
gyu/.m2/repository/com/squareup/okio/okio/1.12.0/okio-1.12.0.jar:/Users/<USER>/.m2/repository/org/bouncyc
astle/bcprov-jdk15on/1.67/bcprov-jdk15on-1.67.jar:/Users/<USER>/.m2/repository/com/thoughtworks/xstream/x
stream/1.4.20/xstream-1.4.20.jar:/Users/<USER>/.m2/repository/io/github/x-stream/mxparser/1.2.2/mxparser-
1.2.2.jar:/Users/<USER>/.m2/repository/xmlpull/xmlpull/1.1.3.1/xmlpull-1.1.3.1.jar:/Users/<USER>/.m2/r
epository/com/aliyun/oss/aliyun-sdk-oss/3.17.4/aliyun-sdk-oss-3.17.4.jar:/Users/<USER>/.m2/repository/org
/jdom/jdom2/2.0.6.1/jdom2-2.0.6.1.jar:/Users/<USER>/.m2/repository/org/codehaus/jettison/jettison/1.5.4/j
ettison-1.5.4.jar:/Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-core/4.5.10/aliyun-java-sdk-cor
e-4.5.10.jar:/Users/<USER>/.m2/repository/com/google/code/gson/gson/2.10.1/gson-2.10.1.jar:/Users/<USER>
yu/.m2/repository/javax/xml/bind/jaxb-api/2.3.1/jaxb-api-2.3.1.jar:/Users/<USER>/.m2/repository/javax/act
ivation/javax.activation-api/1.2.0/javax.activation-api-1.2.0.jar:/Users/<USER>/.m2/repository/org/jacoco
/org.jacoco.agent/0.8.5/org.jacoco.agent-0.8.5-runtime.jar:/Users/<USER>/.m2/repository/org/ini4j/ini4j/0
.5.4/ini4j-0.5.4.jar:/Users/<USER>/.m2/repository/io/opentracing/opentracing-api/0.33.0/opentracing-api-0
.33.0.jar:/Users/<USER>/.m2/repository/io/opentracing/opentracing-util/0.33.0/opentracing-util-0.33.0.jar
:/Users/<USER>/.m2/repository/io/opentracing/opentracing-noop/0.33.0/opentracing-noop-0.33.0.jar:/Users/<USER>
iuyongyu/.m2/repository/com/aliyun/aliyun-java-sdk-ram/3.1.0/aliyun-java-sdk-ram-3.1.0.jar:/Users/<USER>/
.m2/repository/com/aliyun/aliyun-java-sdk-kms/2.11.0/aliyun-java-sdk-kms-2.11.0.jar:/Users/<USER>/.m2/rep
ository/org/apache/tika/tika-core/2.9.1/tika-core-2.9.1.jar:/Users/<USER>/.m2/repository/commons-io/commo
ns-io/2.15.1/commons-io-2.15.1.jar:/Users/<USER>/.m2/repository/net/coobird/thumbnailator/0.4.20/thumbnai
lator-0.4.20.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.15.3/jackson-
databind-2.15.3.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.15.3/ja
ckson-annotations-2.15.3.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.15.3/
jackson-core-2.15.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-actuato
r/3.2.2/spring-boot-starter-actuator-3.2.2.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spri
ng-boot-actuator-autoconfigure/3.2.2/spring-boot-actuator-autoconfigure-3.2.2.jar:/Users/<USER>/.m2/repos
itory/org/springframework/boot/spring-boot-actuator/3.2.2/spring-boot-actuator-3.2.2.jar:/Users/<USER>/.m
2/repository/io/micrometer/micrometer-observation/1.12.2/micrometer-observation-1.12.2.jar:/Users/<USER>/
.m2/repository/io/micrometer/micrometer-commons/1.12.2/micrometer-commons-1.12.2.jar:/Users/<USER>/.m2/re
pository/io/micrometer/micrometer-jakarta9/1.12.2/micrometer-jakarta9-1.12.2.jar:/Users/<USER>/.m2/reposi
tory/io/micrometer/micrometer-core/1.12.2/micrometer-core-1.12.2.jar:/Users/<USER>/.m2/repository/org/hdr
histogram/HdrHistogram/2.1.12/HdrHistogram-2.1.12.jar:/Users/<USER>/.m2/repository/org/latencyutils/Laten
cyUtils/2.0.3/LatencyUtils-2.0.3.jar:/Users/<USER>/pa/codes/stylishlink/backend/common/target/classes:/Us
ers/liuyongyu/.m2/repository/org/springframework/boot/spring-boot-starter-aop/3.2.2/spring-boot-starter-aop-
3.2.2.jar:/Users/<USER>/.m2/repository/org/aspectj/aspectjweaver/1.9.21/aspectjweaver-1.9.21.jar:/Users/<USER>
iuyongyu/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.15.3/jackson-datatype-jsr31
0-2.15.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-mongodb/3.2.2
/spring-boot-starter-data-mongodb-3.2.2.jar:/Users/<USER>/.m2/repository/org/mongodb/mongodb-driver-sync/
4.11.1/mongodb-driver-sync-4.11.1.jar:/Users/<USER>/.m2/repository/org/mongodb/bson/4.11.1/bson-4.11.1.ja
r:/Users/<USER>/.m2/repository/org/mongodb/mongodb-driver-core/4.11.1/mongodb-driver-core-4.11.1.jar:/Use
rs/liuyongyu/.m2/repository/org/mongodb/bson-record-codec/4.11.1/bson-record-codec-4.11.1.jar:/Users/<USER>
yu/.m2/repository/org/springframework/data/spring-data-mongodb/4.2.2/spring-data-mongodb-4.2.2.jar:/Users/<USER>
uyongyu/.m2/repository/org/apache/commons/commons-lang3/3.14.0/commons-lang3-3.14.0.jar:/Users/<USER>/.m2
/repository/org/apache/commons/commons-collections4/4.4/commons-collections4-4.4.jar:/Users/<USER>/.m2/re
pository/com/google/guava/guava/32.1.3-jre/guava-32.1.3-jre.jar:/Users/<USER>/.m2/repository/com/google/g
uava/failureaccess/1.0.1/failureaccess-1.0.1.jar:/Users/<USER>/.m2/repository/com/google/guava/listenable
future/9999.0-empty-to-avoid-conflict-with-guava/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.
jar:/Users/<USER>/.m2/repository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar:/Users/<USER>/
.m2/repository/org/checkerframework/checker-qual/3.37.0/checker-qual-3.37.0.jar:/Users/<USER>/.m2/reposit
ory/com/google/errorprone/error_prone_annotations/2.21.1/error_prone_annotations-2.21.1.jar:/Users/<USER>
/.m2/repository/com/google/j2objc/j2objc-annotations/2.8/j2objc-annotations-2.8.jar:/Users/<USER>/.m2/rep
ository/cn/hutool/hutool-all/5.8.25/hutool-all-5.8.25.jar:/Users/<USER>/.m2/repository/org/mapstruct/maps
truct/1.5.5.Final/mapstruct-1.5.5.Final.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-api/0.12.5/
jjwt-api-0.12.5.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-impl/0.12.5/jjwt-impl-0.12.5.jar:/U
sers/liuyongyu/.m2/repository/io/jsonwebtoken/jjwt-jackson/0.12.5/jjwt-jackson-0.12.5.jar:/Users/<USER>/.
m2/repository/jakarta/xml/bind/jakarta.xml.bind-api/4.0.1/jakarta.xml.bind-api-4.0.1.jar:/Users/<USER>/.m
2/repository/jakarta/activation/jakarta.activation-api/2.1.2/jakarta.activation-api-2.1.2.jar:/Users/<USER>
yu/.m2/repository/org/springframework/spring-core/6.1.3/spring-core-6.1.3.jar:/Users/<USER>/.m2/repositor
y/org/springframework/spring-jcl/6.1.3/spring-jcl-6.1.3.jar:/Users/<USER>/.m2/repository/org/springdoc/sp
ringdoc-openapi-starter-webmvc-ui/2.0.4/springdoc-openapi-starter-webmvc-ui-2.0.4.jar:/Users/<USER>/.m2/r
epository/org/springdoc/springdoc-openapi-starter-webmvc-api/2.0.4/springdoc-openapi-starter-webmvc-api-2.0.
4.jar:/Users/<USER>/.m2/repository/org/springdoc/springdoc-openapi-starter-common/2.0.4/springdoc-openapi
-starter-common-2.0.4.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-core-jakarta/2.2.8/swag
ger-core-jakarta-2.2.8.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-annotations-jakarta/2.
2.8/swagger-annotations-jakarta-2.2.8.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-models-
jakarta/2.2.8/swagger-models-jakarta-2.2.8.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datafor
mat/jackson-dataformat-yaml/2.15.3/jackson-dataformat-yaml-2.15.3.jar:/Users/<USER>/.m2/repository/org/we
bjars/swagger-ui/4.18.1/swagger-ui-4.18.1.jar:/Users/<USER>/.m2/repository/org/webjars/webjars-locator-co
re/0.55/webjars-locator-core-0.55.jar:/Users/<USER>/.m2/repository/io/github/classgraph/classgraph/4.8.14
9/classgraph-4.8.149.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-securi
ty/3.2.2/spring-boot-starter-security-3.2.2.jar:/Users/<USER>/.m2/repository/org/springframework/spring-a
op/6.1.3/spring-aop-6.1.3.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-c
onfig/6.2.1/spring-security-config-6.2.1.jar:/Users/<USER>/.m2/repository/org/springframework/security/sp
ring-security-core/6.2.1/spring-security-core-6.2.1.jar:/Users/<USER>/.m2/repository/org/springframework/
security/spring-security-web/6.2.1/spring-security-web-6.2.1.jar com.stylishlink.file.FileServiceApplication
liuyongyu         5155   0.0  0.1 36479116  16204   ??  S     2:56PM   1:03.16 /Users/<USER>/.trae-cn/ext
ensions/redhat.java-1.42.0-darwin-x64/jre/21.0.7-macosx-x86_64/bin/java --add-modules=ALL-SYSTEM --add-opens
 java.base/java.util=ALL-UNNAMED --add-opens java.base/java.lang=ALL-UNNAMED --add-opens java.base/sun.nio.f
s=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse
.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMI
nstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePoli
cyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx1G -Xms100m -Xlog:disable -javaagent:/Users/<USER>/.t
rae-cn/extensions/redhat.java-1.42.0-darwin-x64/lombok/lombok-1.18.36.jar -XX:+HeapDumpOnOutOfMemoryError -X
X:HeapDumpPath=/Users/<USER>/Library/Application Support/Trae CN/User/workspaceStorage/d535b19a3981872309
cc713b7964edcc/redhat.java -Daether.dependencyCollector.impl=bf -jar /Users/<USER>/.trae-cn/extensions/re
dhat.java-1.42.0-darwin-x64/server/plugins/org.eclipse.equinox.launcher_1.7.0.v20250424-1814.jar -configurat
ion /Users/<USER>/Library/Application Support/Trae CN/User/globalStorage/redhat.java/1.42.0/config_mac -d
ata /Users/<USER>/Library/Application Support/Trae CN/User/workspaceStorage/d535b19a3981872309cc713b7964e
dcc/redhat.java/jdt_ws --pipe=/private/var/folders/87/5jsr7g3x59j43rp8m0kyzs8r0000gn/T/lsp-7910b65b65d8c76f8
70e6cbbaac0005b.sock
liuyongyu        15164   0.0  0.5 36739192  79588   ??  S    Fri04PM  33:22.89 /Users/<USER>/.cursor/exte
nsions/redhat.java-1.42.0-darwin-x64/jre/21.0.7-macosx-x86_64/bin/java --add-modules=ALL-SYSTEM --add-opens
java.base/java.util=ALL-UNNAMED --add-opens java.base/java.lang=ALL-UNNAMED --add-opens java.base/sun.nio.fs
=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.
product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMIn
stallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolic
yWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx1G -Xms100m -Xlog:disable -javaagent:/Users/<USER>/.cu
rsor/extensions/redhat.java-1.42.0-darwin-x64/lombok/lombok-1.18.36.jar -XX:+HeapDumpOnOutOfMemoryError -XX:
HeapDumpPath=/Users/<USER>/Library/Application Support/Cursor/User/workspaceStorage/425d19869cf150dceef5b
2b99386fb2c/redhat.java -Daether.dependencyCollector.impl=bf -jar /Users/<USER>/.cursor/extensions/redhat
.java-1.42.0-darwin-x64/server/plugins/org.eclipse.equinox.launcher_1.7.0.v20250424-1814.jar -configuration
/Users/<USER>/Library/Application Support/Cursor/User/globalStorage/redhat.java/1.42.0/config_mac -data /
Users/liuyongyu/Library/Application Support/Cursor/User/workspaceStorage/425d19869cf150dceef5b2b99386fb2c/re
dhat.java/jdt_ws --pipe=/private/var/folders/87/5jsr7g3x59j43rp8m0kyzs8r0000gn/T/lsp-5e796e8f3f3dcccaa542846
fb165bb1e.sock
liuyongyu        89499   0.0  0.0 33606056    668 s113  R+   10:23AM   0:00.00 grep java
liuyongyu        87896   0.0  0.4 40371868  74848   ??  S    10:07AM   0:29.28 /Library/Java/JavaVirtualMach
ines/jdk-17.jdk/Contents/Home/bin/java -javaagent:/Applications/IntelliJ IDEA CE.app/Contents/lib/idea_rt.ja
r=56323 -Dfile.encoding=UTF-8 -classpath /Users/<USER>/pa/codes/stylishlink/backend/user-service/target/c
lasses:/Users/<USER>/pa/codes/stylishlink/backend/common/target/classes:/Users/<USER>/.m2/repository/o
rg/springframework/boot/spring-boot-starter-aop/3.2.2/spring-boot-starter-aop-3.2.2.jar:/Users/<USER>/.m2
/repository/org/aspectj/aspectjweaver/1.9.21/aspectjweaver-1.9.21.jar:/Users/<USER>/.m2/repository/com/fa
sterxml/jackson/core/jackson-databind/2.15.3/jackson-databind-2.15.3.jar:/Users/<USER>/.m2/repository/com
/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.15.3/jackson-datatype-jsr310-2.15.3.jar:/Users/<USER>
yu/.m2/repository/org/springframework/boot/spring-boot-starter-data-mongodb/3.2.2/spring-boot-starter-data-m
ongodb-3.2.2.jar:/Users/<USER>/.m2/repository/org/mongodb/mongodb-driver-sync/4.11.1/mongodb-driver-sync-
4.11.1.jar:/Users/<USER>/.m2/repository/org/mongodb/bson/4.11.1/bson-4.11.1.jar:/Users/<USER>/.m2/repo
sitory/org/mongodb/mongodb-driver-core/4.11.1/mongodb-driver-core-4.11.1.jar:/Users/<USER>/.m2/repository
/org/mongodb/bson-record-codec/4.11.1/bson-record-codec-4.11.1.jar:/Users/<USER>/.m2/repository/org/sprin
gframework/data/spring-data-mongodb/4.2.2/spring-data-mongodb-4.2.2.jar:/Users/<USER>/.m2/repository/org/
springframework/data/spring-data-commons/3.2.2/spring-data-commons-3.2.2.jar:/Users/<USER>/.m2/repository
/org/apache/commons/commons-lang3/3.14.0/commons-lang3-3.14.0.jar:/Users/<USER>/.m2/repository/org/apache
/commons/commons-collections4/4.4/commons-collections4-4.4.jar:/Users/<USER>/.m2/repository/commons-io/co
mmons-io/2.15.1/commons-io-2.15.1.jar:/Users/<USER>/.m2/repository/com/google/guava/guava/32.1.3-jre/guav
a-32.1.3-jre.jar:/Users/<USER>/.m2/repository/com/google/guava/failureaccess/1.0.1/failureaccess-1.0.1.ja
r:/Users/<USER>/.m2/repository/com/google/guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guav
a/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar:/Users/<USER>/.m2/repository/com/google/
code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar:/Users/<USER>/.m2/repository/org/checkerframework/checker-qua
l/3.37.0/checker-qual-3.37.0.jar:/Users/<USER>/.m2/repository/com/google/errorprone/error_prone_annotatio
ns/2.21.1/error_prone_annotations-2.21.1.jar:/Users/<USER>/.m2/repository/com/google/j2objc/j2objc-annota
tions/2.8/j2objc-annotations-2.8.jar:/Users/<USER>/.m2/repository/cn/hutool/hutool-all/5.8.25/hutool-all-
5.8.25.jar:/Users/<USER>/.m2/repository/org/mapstruct/mapstruct/1.5.5.Final/mapstruct-1.5.5.Final.jar:/Us
ers/liuyongyu/.m2/repository/io/jsonwebtoken/jjwt-api/0.12.5/jjwt-api-0.12.5.jar:/Users/<USER>/.m2/reposi
tory/io/jsonwebtoken/jjwt-impl/0.12.5/jjwt-impl-0.12.5.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/j
jwt-jackson/0.12.5/jjwt-jackson-0.12.5.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-b
oot-starter-web/3.2.2/spring-boot-starter-web-3.2.2.jar:/Users/<USER>/.m2/repository/org/springframework/
boot/spring-boot-starter/3.2.2/spring-boot-starter-3.2.2.jar:/Users/<USER>/.m2/repository/org/springframe
work/boot/spring-boot/3.2.2/spring-boot-3.2.2.jar:/Users/<USER>/.m2/repository/org/springframework/boot/s
pring-boot-starter-logging/3.2.2/spring-boot-starter-logging-3.2.2.jar:/Users/<USER>/.m2/repository/ch/qo
s/logback/logback-classic/1.4.14/logback-classic-1.4.14.jar:/Users/<USER>/.m2/repository/ch/qos/logback/l
ogback-core/1.4.14/logback-core-1.4.14.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to
-slf4j/2.21.1/log4j-to-slf4j-2.21.1.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2
.21.1/log4j-api-2.21.1.jar:/Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/2.0.11/jul-to-slf4j-2.0.11
.jar:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/2.2/snakeyaml-2.2.jar:/Users/<USER>/.m2/repositor
y/org/springframework/boot/spring-boot-starter-json/3.2.2/spring-boot-starter-json-3.2.2.jar:/Users/<USER>
u/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.15.3/jackson-datatype-jdk8-2.15.3.ja
r:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.15.3/jackson
-module-parameter-names-2.15.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-star
ter-tomcat/3.2.2/spring-boot-starter-tomcat-3.2.2.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embe
d/tomcat-embed-core/10.1.18/tomcat-embed-core-10.1.18.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/
embed/tomcat-embed-websocket/10.1.18/tomcat-embed-websocket-10.1.18.jar:/Users/<USER>/.m2/repository/org/
springframework/spring-web/6.1.3/spring-web-6.1.3.jar:/Users/<USER>/.m2/repository/org/springframework/sp
ring-beans/6.1.3/spring-beans-6.1.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/6.
1.3/spring-webmvc-6.1.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context/6.1.3/spring-
context-6.1.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-expression/6.1.3/spring-express
ion-6.1.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-validation/3.2.2/
spring-boot-starter-validation-3.2.2.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embe
d-el/10.1.18/tomcat-embed-el-10.1.18.jar:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-v
alidator/8.0.1.Final/hibernate-validator-8.0.1.Final.jar:/Users/<USER>/.m2/repository/jakarta/validation/
jakarta.validation-api/3.0.2/jakarta.validation-api-3.0.2.jar:/Users/<USER>/.m2/repository/org/jboss/logg
ing/jboss-logging/3.5.3.Final/jboss-logging-3.5.3.Final.jar:/Users/<USER>/.m2/repository/com/fasterxml/cl
assmate/1.6.0/classmate-1.6.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-start
er-security/3.2.2/spring-boot-starter-security-3.2.2.jar:/Users/<USER>/.m2/repository/org/springframework
/spring-aop/6.1.3/spring-aop-6.1.3.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-s
ecurity-config/6.2.1/spring-security-config-6.2.1.jar:/Users/<USER>/.m2/repository/org/springframework/se
curity/spring-security-web/6.2.1/spring-security-web-6.2.1.jar:/Users/<USER>/.m2/repository/org/springfra
mework/boot/spring-boot-starter-actuator/3.2.2/spring-boot-starter-actuator-3.2.2.jar:/Users/<USER>/.m2/r
epository/org/springframework/boot/spring-boot-actuator-autoconfigure/3.2.2/spring-boot-actuator-autoconfigu
re-3.2.2.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator/3.2.2/spring-boot
-actuator-3.2.2.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-observation/1.12.2/micrometer-o
bservation-1.12.2.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-commons/1.12.2/micrometer-com
mons-1.12.2.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-jakarta9/1.12.2/micrometer-jakarta9
-1.12.2.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-core/1.12.2/micrometer-core-1.12.2.jar:
/Users/<USER>/.m2/repository/org/hdrhistogram/HdrHistogram/2.1.12/HdrHistogram-2.1.12.jar:/Users/<USER>
u/.m2/repository/org/latencyutils/LatencyUtils/2.0.3/LatencyUtils-2.0.3.jar:/Users/<USER>/.m2/repository/
com/alibaba/cloud/spring-cloud-starter-alibaba-nacos-discovery/2022.0.0.0/spring-cloud-starter-alibaba-nacos
-discovery-2022.0.0.0.jar:/Users/<USER>/.m2/repository/com/alibaba/cloud/spring-cloud-alibaba-commons/202
2.0.0.0/spring-cloud-alibaba-commons-2022.0.0.0.jar:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-
client/2.2.1/nacos-client-2.2.1.jar:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-auth-plugin/2.2.
1/nacos-auth-plugin-2.2.1.jar:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-encryption-plugin/2.2.
1/nacos-encryption-plugin-2.2.1.jar:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.16.0/commo
ns-codec-1.16.0.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpasyncclient/4.1.5/httpasyn
cclient-4.1.5.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore/4.4.16/httpcore-4.4.16.
jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore-nio/4.4.16/httpcore-nio-4.4.16.jar:/U
sers/liuyongyu/.m2/repository/org/apache/httpcomponents/httpclient/4.5.13/httpclient-4.5.13.jar:/Users/<USER>
ngyu/.m2/repository/io/prometheus/simpleclient/0.16.0/simpleclient-0.16.0.jar:/Users/<USER>/.m2/repositor
y/io/prometheus/simpleclient_tracer_otel/0.16.0/simpleclient_tracer_otel-0.16.0.jar:/Users/<USER>/.m2/rep
ository/io/prometheus/simpleclient_tracer_common/0.16.0/simpleclient_tracer_common-0.16.0.jar:/Users/<USER>
yu/.m2/repository/io/prometheus/simpleclient_tracer_otel_agent/0.16.0/simpleclient_tracer_otel_agent-0.16.0.
jar:/Users/<USER>/.m2/repository/com/alibaba/spring/spring-context-support/1.0.11/spring-context-support-
1.0.11.jar:/Users/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-commons/4.1.0/spring-cloud
-commons-4.1.0.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-crypto/6.2.1
/spring-security-crypto-6.2.1.jar:/Users/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-con
text/4.1.0/spring-cloud-context-4.1.0.jar:/Users/<USER>/.m2/repository/com/alibaba/cloud/spring-cloud-sta
rter-alibaba-nacos-config/2022.0.0.0/spring-cloud-starter-alibaba-nacos-config-2022.0.0.0.jar:/Users/<USER>
yu/.m2/repository/org/slf4j/slf4j-api/2.0.11/slf4j-api-2.0.11.jar:/Users/<USER>/.m2/repository/jakarta/an
notation/jakarta.annotation-api/2.1.1/jakarta.annotation-api-2.1.1.jar:/Users/<USER>/.m2/repository/org/s
pringframework/cloud/spring-cloud-starter-openfeign/4.1.0/spring-cloud-starter-openfeign-4.1.0.jar:/Users/<USER>
uyongyu/.m2/repository/org/springframework/cloud/spring-cloud-starter/4.1.0/spring-cloud-starter-4.1.0.jar:/
Users/liuyongyu/.m2/repository/org/springframework/security/spring-security-rsa/1.1.1/spring-security-rsa-1.
1.1.jar:/Users/<USER>/.m2/repository/org/bouncycastle/bcprov-jdk18on/1.74/bcprov-jdk18on-1.74.jar:/Users/
liuyongyu/.m2/repository/org/springframework/cloud/spring-cloud-openfeign-core/4.1.0/spring-cloud-openfeign-
core-4.1.0.jar:/Users/<USER>/.m2/repository/io/github/openfeign/form/feign-form-spring/3.8.0/feign-form-s
pring-3.8.0.jar:/Users/<USER>/.m2/repository/io/github/openfeign/form/feign-form/3.8.0/feign-form-3.8.0.j
ar:/Users/<USER>/.m2/repository/commons-fileupload/commons-fileupload/1.5/commons-fileupload-1.5.jar:/Use
rs/liuyongyu/.m2/repository/io/github/openfeign/feign-core/13.1/feign-core-13.1.jar:/Users/<USER>/.m2/rep
ository/io/github/openfeign/feign-slf4j/13.1/feign-slf4j-13.1.jar:/Users/<USER>/.m2/repository/org/spring
framework/cloud/spring-cloud-starter-circuitbreaker-resilience4j/3.1.0/spring-cloud-starter-circuitbreaker-r
esilience4j-3.1.0.jar:/Users/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-circuitbreaker-
resilience4j/3.1.0/spring-cloud-circuitbreaker-resilience4j-3.1.0.jar:/Users/<USER>/.m2/repository/io/git
hub/resilience4j/resilience4j-spring-boot3/2.1.0/resilience4j-spring-boot3-2.1.0.jar:/Users/<USER>/.m2/re
pository/io/github/resilience4j/resilience4j-spring6/2.1.0/resilience4j-spring6-2.1.0.jar:/Users/<USER>/.
m2/repository/io/github/resilience4j/resilience4j-annotations/2.1.0/resilience4j-annotations-2.1.0.jar:/User
s/liuyongyu/.m2/repository/io/github/resilience4j/resilience4j-consumer/2.1.0/resilience4j-consumer-2.1.0.ja
r:/Users/<USER>/.m2/repository/io/github/resilience4j/resilience4j-circularbuffer/2.1.0/resilience4j-circ
ularbuffer-2.1.0.jar:/Users/<USER>/.m2/repository/io/github/resilience4j/resilience4j-framework-common/2.
1.0/resilience4j-framework-common-2.1.0.jar:/Users/<USER>/.m2/repository/io/github/resilience4j/resilienc
e4j-micrometer/2.1.0/resilience4j-micrometer-2.1.0.jar:/Users/<USER>/.m2/repository/io/github/resilience4
j/resilience4j-retry/2.1.0/resilience4j-retry-2.1.0.jar:/Users/<USER>/.m2/repository/io/github/resilience
4j/resilience4j-ratelimiter/2.1.0/resilience4j-ratelimiter-2.1.0.jar:/Users/<USER>/.m2/repository/io/gith
ub/resilience4j/resilience4j-circuitbreaker/2.1.0/resilience4j-circuitbreaker-2.1.0.jar:/Users/<USER>/.m2
/repository/io/github/resilience4j/resilience4j-core/2.1.0/resilience4j-core-2.1.0.jar:/Users/<USER>/.m2/
repository/io/github/resilience4j/resilience4j-timelimiter/2.1.0/resilience4j-timelimiter-2.1.0.jar:/Users/<USER>
iuyongyu/.m2/repository/org/springframework/boot/spring-boot-starter-data-redis/3.2.2/spring-boot-starter-da
ta-redis-3.2.2.jar:/Users/<USER>/.m2/repository/io/lettuce/lettuce-core/6.3.1.RELEASE/lettuce-core-6.3.1.
RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-redis/3.2.2/spring-data-red
is-3.2.2.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-keyvalue/3.2.2/spring-data
-keyvalue-3.2.2.jar:/Users/<USER>/.m2/repository/org/springframework/spring-tx/6.1.3/spring-tx-6.1.3.jar:
/Users/<USER>/.m2/repository/org/springframework/spring-oxm/6.1.3/spring-oxm-6.1.3.jar:/Users/<USER>/.
m2/repository/org/springframework/spring-context-support/6.1.3/spring-context-support-6.1.3.jar:/Users/<USER>
ngyu/.m2/repository/org/redisson/redisson/3.26.0/redisson-3.26.0.jar:/Users/<USER>/.m2/repository/io/nett
y/netty-common/4.1.105.Final/netty-common-4.1.105.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-c
odec/4.1.105.Final/netty-codec-4.1.105.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-buffer/4.1.1
05.Final/netty-buffer-4.1.105.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport/4.1.105.Fin
al/netty-transport-4.1.105.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver/4.1.105.Final/n
etty-resolver-4.1.105.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver-dns/4.1.105.Final/ne
tty-resolver-dns-4.1.105.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-dns/4.1.105.Final/ne
tty-codec-dns-4.1.105.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-handler/4.1.105.Final/netty-h
andler-4.1.105.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-native-unix-common/4.1.105
.Final/netty-transport-native-unix-common-4.1.105.Final.jar:/Users/<USER>/.m2/repository/javax/cache/cach
e-api/1.1.1/cache-api-1.1.1.jar:/Users/<USER>/.m2/repository/io/projectreactor/reactor-core/3.6.2/reactor
-core-3.6.2.jar:/Users/<USER>/.m2/repository/org/reactivestreams/reactive-streams/1.0.4/reactive-streams-
1.0.4.jar:/Users/<USER>/.m2/repository/io/reactivex/rxjava3/rxjava/3.1.8/rxjava-3.1.8.jar:/Users/<USER>
u/.m2/repository/org/jboss/marshalling/jboss-marshalling/2.0.11.Final/jboss-marshalling-2.0.11.Final.jar:/Us
ers/liuyongyu/.m2/repository/org/jboss/marshalling/jboss-marshalling-river/2.0.11.Final/jboss-marshalling-ri
ver-2.0.11.Final.jar:/Users/<USER>/.m2/repository/com/esotericsoftware/kryo/5.6.0/kryo-5.6.0.jar:/Users/<USER>
iuyongyu/.m2/repository/com/esotericsoftware/reflectasm/1.11.9/reflectasm-1.11.9.jar:/Users/<USER>/.m2/re
pository/org/objenesis/objenesis/3.3/objenesis-3.3.jar:/Users/<USER>/.m2/repository/com/esotericsoftware/
minlog/1.3.1/minlog-1.3.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations
/2.15.3/jackson-annotations-2.15.3.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jack
son-dataformat-yaml/2.15.3/jackson-dataformat-yaml-2.15.3.jar:/Users/<USER>/.m2/repository/com/fasterxml/
jackson/core/jackson-core/2.15.3/jackson-core-2.15.3.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-
buddy/1.14.11/byte-buddy-1.14.11.jar:/Users/<USER>/.m2/repository/org/jodd/jodd-bean/5.1.6/jodd-bean-5.1.
6.jar:/Users/<USER>/.m2/repository/org/jodd/jodd-core/5.1.6/jodd-core-5.1.6.jar:/Users/<USER>/.m2/repo
sitory/com/mysql/mysql-connector-j/8.3.0/mysql-connector-j-8.3.0.jar:/Users/<USER>/.m2/repository/com/goo
gle/protobuf/protobuf-java/3.25.1/protobuf-java-3.25.1.jar:/Users/<USER>/.m2/repository/com/baomidou/myba
tis-plus-boot-starter/3.5.5/mybatis-plus-boot-starter-3.5.5.jar:/Users/<USER>/.m2/repository/com/baomidou
/mybatis-plus/3.5.5/mybatis-plus-3.5.5.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-core/3.
5.5/mybatis-plus-core-3.5.5.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-annotation/3.5.5/m
ybatis-plus-annotation-3.5.5.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-extension/3.5.5/m
ybatis-plus-extension-3.5.5.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis/3.5.15/mybatis-3.5.15.ja
r:/Users/<USER>/.m2/repository/com/github/jsqlparser/jsqlparser/4.6/jsqlparser-4.6.jar:/Users/<USER>/.
m2/repository/org/mybatis/mybatis-spring/3.0.3/mybatis-spring-3.0.3.jar:/Users/<USER>/.m2/repository/com/
baomidou/mybatis-plus-spring-boot-autoconfigure/3.5.5/mybatis-plus-spring-boot-autoconfigure-3.5.5.jar:/User
s/liuyongyu/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/3.2.2/spring-boot-autoconfigur
e-3.2.2.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/3.2.2/spring-b
oot-starter-jdbc-3.2.2.jar:/Users/<USER>/.m2/repository/com/zaxxer/HikariCP/5.0.1/HikariCP-5.0.1.jar:/Use
rs/liuyongyu/.m2/repository/org/springframework/spring-jdbc/6.1.3/spring-jdbc-6.1.3.jar:/Users/<USER>/.m2
/repository/jakarta/xml/bind/jakarta.xml.bind-api/4.0.1/jakarta.xml.bind-api-4.0.1.jar:/Users/<USER>/.m2/
repository/jakarta/activation/jakarta.activation-api/2.1.2/jakarta.activation-api-2.1.2.jar:/Users/<USER>
/.m2/repository/org/springframework/spring-core/6.1.3/spring-core-6.1.3.jar:/Users/<USER>/.m2/repository/
org/springframework/spring-jcl/6.1.3/spring-jcl-6.1.3.jar:/Users/<USER>/.m2/repository/org/springframewor
k/security/spring-security-core/6.2.1/spring-security-core-6.2.1.jar:/Users/<USER>/.m2/repository/org/spr
ingdoc/springdoc-openapi-starter-webmvc-ui/2.2.0/springdoc-openapi-starter-webmvc-ui-2.2.0.jar:/Users/<USER>
gyu/.m2/repository/org/springdoc/springdoc-openapi-starter-webmvc-api/2.2.0/springdoc-openapi-starter-webmvc
-api-2.2.0.jar:/Users/<USER>/.m2/repository/org/springdoc/springdoc-openapi-starter-common/2.2.0/springdo
c-openapi-starter-common-2.2.0.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-core-jakarta/2
.2.15/swagger-core-jakarta-2.2.15.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-annotations
-jakarta/2.2.15/swagger-annotations-jakarta-2.2.15.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/sw
agger-models-jakarta/2.2.15/swagger-models-jakarta-2.2.15.jar:/Users/<USER>/.m2/repository/org/webjars/sw
agger-ui/5.2.0/swagger-ui-5.2.0.jar com.stylishlink.user.UserServiceApplication
liuyongyu        85454   0.0  0.7 40415436 110724   ??  S     9:45AM   1:01.99 /Library/Java/JavaVirtualMach
ines/jdk-17.jdk/Contents/Home/bin/java -javaagent:/Applications/IntelliJ IDEA CE.app/Contents/lib/idea_rt.ja
r=51676 -Dfile.encoding=UTF-8 -classpath /Users/<USER>/pa/codes/stylishlink/backend/ai-service/target/cla
sses:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/3.2.2/spring-boot-star
ter-web-3.2.2.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/3.2.2/spring-
boot-starter-3.2.2.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/3.2.2/spring-boo
t-3.2.2.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/3.2.2/sprin
g-boot-starter-logging-3.2.2.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.4.14/logba
ck-classic-1.4.14.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.4.14/logback-core-1.4.14
.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.21.1/log4j-to-slf4j-2.21.1.ja
r:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.21.1/log4j-api-2.21.1.jar:/Users/<USER>
ongyu/.m2/repository/org/slf4j/jul-to-slf4j/2.0.11/jul-to-slf4j-2.0.11.jar:/Users/<USER>/.m2/repository/o
rg/yaml/snakeyaml/2.2/snakeyaml-2.2.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot
-starter-json/3.2.2/spring-boot-starter-json-3.2.2.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson
/datatype/jackson-datatype-jdk8/2.15.3/jackson-datatype-jdk8-2.15.3.jar:/Users/<USER>/.m2/repository/com/
fasterxml/jackson/module/jackson-module-parameter-names/2.15.3/jackson-module-parameter-names-2.15.3.jar:/Us
ers/liuyongyu/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/3.2.2/spring-boot-starter-t
omcat-3.2.2.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/10.1.18/tomcat-emb
ed-core-10.1.18.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/10.1.18/t
omcat-embed-websocket-10.1.18.jar:/Users/<USER>/.m2/repository/org/springframework/spring-web/6.1.3/sprin
g-web-6.1.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-beans/6.1.3/spring-beans-6.1.3.ja
r:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/6.1.3/spring-webmvc-6.1.3.jar:/Users/<USER>
yongyu/.m2/repository/org/springframework/spring-context/6.1.3/spring-context-6.1.3.jar:/Users/<USER>/.m2
/repository/org/springframework/spring-expression/6.1.3/spring-expression-6.1.3.jar:/Users/<USER>/.m2/rep
ository/org/springframework/boot/spring-boot-starter-validation/3.2.2/spring-boot-starter-validation-3.2.2.j
ar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/10.1.18/tomcat-embed-el-10.1.18.j
ar:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/8.0.1.Final/hibernate-validat
or-8.0.1.Final.jar:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.5.3.Final/jboss-logging
-3.5.3.Final.jar:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.6.0/classmate-1.6.0.jar:/Users/<USER>
uyongyu/.m2/repository/org/springframework/boot/spring-boot-starter-security/3.2.2/spring-boot-starter-secur
ity-3.2.2.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aop/6.1.3/spring-aop-6.1.3.jar:/Use
rs/liuyongyu/.m2/repository/org/springframework/security/spring-security-config/6.2.1/spring-security-config
-6.2.1.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-core/6.2.1/spring-se
curity-core-6.2.1.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-web/6.2.1
/spring-security-web-6.2.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-
aop/3.2.2/spring-boot-starter-aop-3.2.2.jar:/Users/<USER>/.m2/repository/org/aspectj/aspectjweaver/1.9.21
/aspectjweaver-1.9.21.jar:/Users/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/3.0.2/ja
karta.validation-api-3.0.2.jar:/Users/<USER>/.m2/repository/com/alibaba/cloud/spring-cloud-starter-alibab
a-nacos-discovery/2022.0.0.0/spring-cloud-starter-alibaba-nacos-discovery-2022.0.0.0.jar:/Users/<USER>/.m
2/repository/com/alibaba/cloud/spring-cloud-alibaba-commons/2022.0.0.0/spring-cloud-alibaba-commons-2022.0.0
.0.jar:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.2.1/nacos-client-2.2.1.jar:/Users/<USER>
uyongyu/.m2/repository/com/alibaba/nacos/nacos-auth-plugin/2.2.1/nacos-auth-plugin-2.2.1.jar:/Users/<USER>
u/.m2/repository/com/alibaba/nacos/nacos-encryption-plugin/2.2.1/nacos-encryption-plugin-2.2.1.jar:/Users/<USER>
uyongyu/.m2/repository/commons-codec/commons-codec/1.16.0/commons-codec-1.16.0.jar:/Users/<USER>/.m2/repo
sitory/org/apache/httpcomponents/httpasyncclient/4.1.5/httpasyncclient-4.1.5.jar:/Users/<USER>/.m2/reposi
tory/org/apache/httpcomponents/httpcore/4.4.16/httpcore-4.4.16.jar:/Users/<USER>/.m2/repository/org/apach
e/httpcomponents/httpcore-nio/4.4.16/httpcore-nio-4.4.16.jar:/Users/<USER>/.m2/repository/org/apache/http
components/httpclient/4.5.13/httpclient-4.5.13.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclien
t/0.16.0/simpleclient-0.16.0.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient_tracer_otel/0.16
.0/simpleclient_tracer_otel-0.16.0.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient_tracer_com
mon/0.16.0/simpleclient_tracer_common-0.16.0.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient_
tracer_otel_agent/0.16.0/simpleclient_tracer_otel_agent-0.16.0.jar:/Users/<USER>/.m2/repository/com/aliba
ba/spring/spring-context-support/1.0.11/spring-context-support-1.0.11.jar:/Users/<USER>/.m2/repository/or
g/springframework/cloud/spring-cloud-commons/4.1.0/spring-cloud-commons-4.1.0.jar:/Users/<USER>/.m2/repos
itory/org/springframework/security/spring-security-crypto/6.2.1/spring-security-crypto-6.2.1.jar:/Users/<USER>
ongyu/.m2/repository/org/springframework/cloud/spring-cloud-context/4.1.0/spring-cloud-context-4.1.0.jar:/Us
ers/liuyongyu/.m2/repository/com/alibaba/cloud/spring-cloud-starter-alibaba-nacos-config/2022.0.0.0/spring-c
loud-starter-alibaba-nacos-config-2022.0.0.0.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/2.0.11/
slf4j-api-2.0.11.jar:/Users/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/2.1.1/jakarta
.annotation-api-2.1.1.jar:/Users/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-starter-ope
nfeign/4.1.0/spring-cloud-starter-openfeign-4.1.0.jar:/Users/<USER>/.m2/repository/org/springframework/cl
oud/spring-cloud-starter/4.1.0/spring-cloud-starter-4.1.0.jar:/Users/<USER>/.m2/repository/org/springfram
ework/security/spring-security-rsa/1.1.1/spring-security-rsa-1.1.1.jar:/Users/<USER>/.m2/repository/org/b
ouncycastle/bcprov-jdk18on/1.74/bcprov-jdk18on-1.74.jar:/Users/<USER>/.m2/repository/org/springframework/
cloud/spring-cloud-openfeign-core/4.1.0/spring-cloud-openfeign-core-4.1.0.jar:/Users/<USER>/.m2/repositor
y/commons-fileupload/commons-fileupload/1.5/commons-fileupload-1.5.jar:/Users/<USER>/.m2/repository/io/gi
thub/openfeign/feign-core/13.1/feign-core-13.1.jar:/Users/<USER>/.m2/repository/io/github/openfeign/feign
-slf4j/13.1/feign-slf4j-13.1.jar:/Users/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-star
ter-loadbalancer/4.1.0/spring-cloud-starter-loadbalancer-4.1.0.jar:/Users/<USER>/.m2/repository/org/sprin
gframework/cloud/spring-cloud-loadbalancer/4.1.0/spring-cloud-loadbalancer-4.1.0.jar:/Users/<USER>/.m2/re
pository/io/projectreactor/reactor-core/3.6.2/reactor-core-3.6.2.jar:/Users/<USER>/.m2/repository/org/rea
ctivestreams/reactive-streams/1.0.4/reactive-streams-1.0.4.jar:/Users/<USER>/.m2/repository/io/projectrea
ctor/addons/reactor-extra/3.5.1/reactor-extra-3.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/
boot/spring-boot-starter-cache/3.2.2/spring-boot-starter-cache-3.2.2.jar:/Users/<USER>/.m2/repository/org
/springframework/spring-context-support/6.1.3/spring-context-support-6.1.3.jar:/Users/<USER>/.m2/reposito
ry/com/stoyanr/evictor/1.0.0/evictor-1.0.0.jar:/Users/<USER>/.m2/repository/io/github/openfeign/form/feig
n-form/3.8.0/feign-form-3.8.0.jar:/Users/<USER>/.m2/repository/io/github/openfeign/form/feign-form-spring
/3.8.0/feign-form-spring-3.8.0.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-starter-
model-deepseek/1.0.0/spring-ai-starter-model-deepseek-1.0.0.jar:/Users/<USER>/.m2/repository/org/springfr
amework/ai/spring-ai-autoconfigure-model-deepseek/1.0.0/spring-ai-autoconfigure-model-deepseek-1.0.0.jar:/Us
ers/liuyongyu/.m2/repository/org/springframework/ai/spring-ai-autoconfigure-model-tool/1.0.0/spring-ai-autoc
onfigure-model-tool-1.0.0.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-autoconfigure
-retry/1.0.0/spring-ai-autoconfigure-retry-1.0.0.jar:/Users/<USER>/.m2/repository/org/springframework/ai/
spring-ai-autoconfigure-model-chat-observation/1.0.0/spring-ai-autoconfigure-model-chat-observation-1.0.0.ja
r:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-deepseek/1.0.0/spring-ai-deepseek-1.0.0.j
ar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-model/1.0.0/spring-ai-model-1.0.0.jar:/U
sers/liuyongyu/.m2/repository/org/springframework/ai/spring-ai-commons/1.0.0/spring-ai-commons-1.0.0.jar:/Us
ers/liuyongyu/.m2/repository/io/micrometer/context-propagation/1.1.3/context-propagation-1.1.3.jar:/Users/<USER>
uyongyu/.m2/repository/org/springframework/ai/spring-ai-template-st/1.0.0/spring-ai-template-st-1.0.0.jar:/U
sers/liuyongyu/.m2/repository/org/antlr/ST4/4.3.4/ST4-4.3.4.jar:/Users/<USER>/.m2/repository/org/antlr/an
tlr-runtime/3.5.3/antlr-runtime-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-messagi
ng/6.1.3/spring-messaging-6.1.3.jar:/Users/<USER>/.m2/repository/org/antlr/antlr4-runtime/4.13.1/antlr4-r
untime-4.13.1.jar:/Users/<USER>/.m2/repository/com/github/victools/jsonschema-generator/4.37.0/jsonschema
-generator-4.37.0.jar:/Users/<USER>/.m2/repository/com/github/victools/jsonschema-module-jackson/4.37.0/j
sonschema-module-jackson-4.37.0.jar:/Users/<USER>/.m2/repository/com/github/victools/jsonschema-module-sw
agger-2/4.37.0/jsonschema-module-swagger-2-4.37.0.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swa
gger-annotations/2.2.25/swagger-annotations-2.2.25.jar:/Users/<USER>/.m2/repository/org/springframework/a
i/spring-ai-retry/1.0.0/spring-ai-retry-1.0.0.jar:/Users/<USER>/.m2/repository/org/springframework/retry/
spring-retry/2.0.5/spring-retry-2.0.5.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-a
utoconfigure-model-chat-client/1.0.0/spring-ai-autoconfigure-model-chat-client-1.0.0.jar:/Users/<USER>/.m
2/repository/org/springframework/ai/spring-ai-client-chat/1.0.0/spring-ai-client-chat-1.0.0.jar:/Users/<USER>
ngyu/.m2/repository/com/fasterxml/jackson/module/jackson-module-jsonSchema/2.15.3/jackson-module-jsonSchema-
2.15.3.jar:/Users/<USER>/.m2/repository/javax/validation/validation-api/1.1.0.Final/validation-api-1.1.0.
Final.jar:/Users/<USER>/.m2/repository/com/knuddels/jtokkit/1.1.0/jtokkit-1.1.0.jar:/Users/<USER>/.m2/
repository/org/springframework/ai/spring-ai-autoconfigure-model-chat-memory/1.0.0/spring-ai-autoconfigure-mo
del-chat-memory-1.0.0.jar:/Users/<USER>/.m2/repository/com/mysql/mysql-connector-j/8.3.0/mysql-connector-
j-8.3.0.jar:/Users/<USER>/.m2/repository/com/google/protobuf/protobuf-java/3.25.1/protobuf-java-3.25.1.ja
r:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-boot-starter/3.5.5/mybatis-plus-boot-starter-3.5
.5.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus/3.5.5/mybatis-plus-3.5.5.jar:/Users/<USER>
yu/.m2/repository/com/baomidou/mybatis-plus-core/3.5.5/mybatis-plus-core-3.5.5.jar:/Users/<USER>/.m2/repo
sitory/com/baomidou/mybatis-plus-annotation/3.5.5/mybatis-plus-annotation-3.5.5.jar:/Users/<USER>/.m2/rep
ository/com/baomidou/mybatis-plus-extension/3.5.5/mybatis-plus-extension-3.5.5.jar:/Users/<USER>/.m2/repo
sitory/org/mybatis/mybatis/3.5.15/mybatis-3.5.15.jar:/Users/<USER>/.m2/repository/com/github/jsqlparser/j
sqlparser/4.6/jsqlparser-4.6.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring-boot-autoc
onfigure/3.5.5/mybatis-plus-spring-boot-autoconfigure-3.5.5.jar:/Users/<USER>/.m2/repository/org/springfr
amework/boot/spring-boot-autoconfigure/3.2.2/spring-boot-autoconfigure-3.2.2.jar:/Users/<USER>/.m2/reposi
tory/org/springframework/boot/spring-boot-starter-jdbc/3.2.2/spring-boot-starter-jdbc-3.2.2.jar:/Users/<USER>
ngyu/.m2/repository/com/zaxxer/HikariCP/5.0.1/HikariCP-5.0.1.jar:/Users/<USER>/.m2/repository/org/springf
ramework/spring-jdbc/6.1.3/spring-jdbc-6.1.3.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis-spring/
3.0.3/mybatis-spring-3.0.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-
data-redis/3.2.2/spring-boot-starter-data-redis-3.2.2.jar:/Users/<USER>/.m2/repository/io/lettuce/lettuce
-core/6.3.1.RELEASE/lettuce-core-6.3.1.RELEASE.jar:/Users/<USER>/.m2/repository/io/netty/netty-common/4.1
.105.Final/netty-common-4.1.105.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-handler/4.1.105.Fin
al/netty-handler-4.1.105.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver/4.1.105.Final/net
ty-resolver-4.1.105.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-buffer/4.1.105.Final/netty-buff
er-4.1.105.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-native-unix-common/4.1.105.Fin
al/netty-transport-native-unix-common-4.1.105.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec
/4.1.105.Final/netty-codec-4.1.105.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport/4.1.10
5.Final/netty-transport-4.1.105.Final.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-da
ta-redis/3.2.2/spring-data-redis-3.2.2.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-d
ata-keyvalue/3.2.2/spring-data-keyvalue-3.2.2.jar:/Users/<USER>/.m2/repository/org/springframework/data/s
pring-data-commons/3.2.2/spring-data-commons-3.2.2.jar:/Users/<USER>/.m2/repository/org/springframework/s
pring-tx/6.1.3/spring-tx-6.1.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-oxm/6.1.3/spri
ng-oxm-6.1.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-webflux/3.2.2/
spring-boot-starter-webflux-3.2.2.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-s
tarter-reactor-netty/3.2.2/spring-boot-starter-reactor-netty-3.2.2.jar:/Users/<USER>/.m2/repository/io/pr
ojectreactor/netty/reactor-netty-http/1.1.15/reactor-netty-http-1.1.15.jar:/Users/<USER>/.m2/repository/i
o/netty/netty-codec-http/4.1.105.Final/netty-codec-http-4.1.105.Final.jar:/Users/<USER>/.m2/repository/io
/netty/netty-codec-http2/4.1.105.Final/netty-codec-http2-4.1.105.Final.jar:/Users/<USER>/.m2/repository/i
o/netty/netty-resolver-dns/4.1.105.Final/netty-resolver-dns-4.1.105.Final.jar:/Users/<USER>/.m2/repositor
y/io/netty/netty-codec-dns/4.1.105.Final/netty-codec-dns-4.1.105.Final.jar:/Users/<USER>/.m2/repository/i
o/netty/netty-resolver-dns-native-macos/4.1.105.Final/netty-resolver-dns-native-macos-4.1.105.Final-osx-x86_
64.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver-dns-classes-macos/4.1.105.Final/netty-resolve
r-dns-classes-macos-4.1.105.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-native-epoll/
4.1.105.Final/netty-transport-native-epoll-4.1.105.Final-linux-x86_64.jar:/Users/<USER>/.m2/repository/io
/netty/netty-transport-classes-epoll/4.1.105.Final/netty-transport-classes-epoll-4.1.105.Final.jar:/Users/<USER>
uyongyu/.m2/repository/io/projectreactor/netty/reactor-netty-core/1.1.15/reactor-netty-core-1.1.15.jar:/User
s/liuyongyu/.m2/repository/io/netty/netty-handler-proxy/4.1.105.Final/netty-handler-proxy-4.1.105.Final.jar:
/Users/<USER>/.m2/repository/io/netty/netty-codec-socks/4.1.105.Final/netty-codec-socks-4.1.105.Final.jar
:/Users/<USER>/.m2/repository/org/springframework/spring-webflux/6.1.3/spring-webflux-6.1.3.jar:/Users/<USER>
uyongyu/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.15.3/jackson-databind-2.15.3.jar:/Users
/liuyongyu/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.15.3/jackson-annotations-2.15.3.j
ar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.15.3/jackson-core-2.15.3.jar:/U
sers/liuyongyu/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.15.3/jackson-datatype
-jsr310-2.15.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-actuator/3.2
.2/spring-boot-starter-actuator-3.2.2.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-bo
ot-actuator-autoconfigure/3.2.2/spring-boot-actuator-autoconfigure-3.2.2.jar:/Users/<USER>/.m2/repository
/org/springframework/boot/spring-boot-actuator/3.2.2/spring-boot-actuator-3.2.2.jar:/Users/<USER>/.m2/rep
ository/io/micrometer/micrometer-observation/1.12.2/micrometer-observation-1.12.2.jar:/Users/<USER>/.m2/r
epository/io/micrometer/micrometer-commons/1.12.2/micrometer-commons-1.12.2.jar:/Users/<USER>/.m2/reposit
ory/io/micrometer/micrometer-jakarta9/1.12.2/micrometer-jakarta9-1.12.2.jar:/Users/<USER>/.m2/repository/
io/micrometer/micrometer-core/1.12.2/micrometer-core-1.12.2.jar:/Users/<USER>/.m2/repository/org/hdrhisto
gram/HdrHistogram/2.1.12/HdrHistogram-2.1.12.jar:/Users/<USER>/.m2/repository/org/latencyutils/LatencyUti
ls/2.0.3/LatencyUtils-2.0.3.jar:/Users/<USER>/pa/codes/stylishlink/backend/common/target/classes:/Users/<USER>
iuyongyu/.m2/repository/org/springframework/boot/spring-boot-starter-data-mongodb/3.2.2/spring-boot-starter-
data-mongodb-3.2.2.jar:/Users/<USER>/.m2/repository/org/mongodb/mongodb-driver-sync/4.11.1/mongodb-driver
-sync-4.11.1.jar:/Users/<USER>/.m2/repository/org/mongodb/bson/4.11.1/bson-4.11.1.jar:/Users/<USER>/.m
2/repository/org/mongodb/mongodb-driver-core/4.11.1/mongodb-driver-core-4.11.1.jar:/Users/<USER>/.m2/repo
sitory/org/mongodb/bson-record-codec/4.11.1/bson-record-codec-4.11.1.jar:/Users/<USER>/.m2/repository/org
/springframework/data/spring-data-mongodb/4.2.2/spring-data-mongodb-4.2.2.jar:/Users/<USER>/.m2/repositor
y/org/apache/commons/commons-lang3/3.14.0/commons-lang3-3.14.0.jar:/Users/<USER>/.m2/repository/org/apach
e/commons/commons-collections4/4.4/commons-collections4-4.4.jar:/Users/<USER>/.m2/repository/commons-io/c
ommons-io/2.15.1/commons-io-2.15.1.jar:/Users/<USER>/.m2/repository/com/google/guava/guava/32.1.3-jre/gua
va-32.1.3-jre.jar:/Users/<USER>/.m2/repository/com/google/guava/failureaccess/1.0.1/failureaccess-1.0.1.j
ar:/Users/<USER>/.m2/repository/com/google/guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-gua
va/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar:/Users/<USER>/.m2/repository/com/google
/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar:/Users/<USER>/.m2/repository/org/checkerframework/checker-qu
al/3.37.0/checker-qual-3.37.0.jar:/Users/<USER>/.m2/repository/com/google/errorprone/error_prone_annotati
ons/2.21.1/error_prone_annotations-2.21.1.jar:/Users/<USER>/.m2/repository/com/google/j2objc/j2objc-annot
ations/2.8/j2objc-annotations-2.8.jar:/Users/<USER>/.m2/repository/cn/hutool/hutool-all/5.8.25/hutool-all
-5.8.25.jar:/Users/<USER>/.m2/repository/org/mapstruct/mapstruct/1.5.5.Final/mapstruct-1.5.5.Final.jar:/U
sers/liuyongyu/.m2/repository/io/jsonwebtoken/jjwt-api/0.12.5/jjwt-api-0.12.5.jar:/Users/<USER>/.m2/repos
itory/io/jsonwebtoken/jjwt-impl/0.12.5/jjwt-impl-0.12.5.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/
jjwt-jackson/0.12.5/jjwt-jackson-0.12.5.jar:/Users/<USER>/.m2/repository/jakarta/xml/bind/jakarta.xml.bin
d-api/4.0.1/jakarta.xml.bind-api-4.0.1.jar:/Users/<USER>/.m2/repository/jakarta/activation/jakarta.activa
tion-api/2.1.2/jakarta.activation-api-2.1.2.jar:/Users/<USER>/.m2/repository/org/springframework/spring-c
ore/6.1.3/spring-core-6.1.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/6.1.3/spring-
jcl-6.1.3.jar:/Users/<USER>/.m2/repository/org/springdoc/springdoc-openapi-starter-webmvc-ui/2.0.4/spring
doc-openapi-starter-webmvc-ui-2.0.4.jar:/Users/<USER>/.m2/repository/org/springdoc/springdoc-openapi-star
ter-webmvc-api/2.0.4/springdoc-openapi-starter-webmvc-api-2.0.4.jar:/Users/<USER>/.m2/repository/org/spri
ngdoc/springdoc-openapi-starter-common/2.0.4/springdoc-openapi-starter-common-2.0.4.jar:/Users/<USER>/.m2
/repository/io/swagger/core/v3/swagger-core-jakarta/2.2.8/swagger-core-jakarta-2.2.8.jar:/Users/<USER>/.m
2/repository/io/swagger/core/v3/swagger-annotations-jakarta/2.2.8/swagger-annotations-jakarta-2.2.8.jar:/Use
rs/liuyongyu/.m2/repository/io/swagger/core/v3/swagger-models-jakarta/2.2.8/swagger-models-jakarta-2.2.8.jar
:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-yaml/2.15.3/jackson-dat
aformat-yaml-2.15.3.jar:/Users/<USER>/.m2/repository/org/webjars/swagger-ui/4.18.1/swagger-ui-4.18.1.jar:
/Users/<USER>/.m2/repository/org/webjars/webjars-locator-core/0.55/webjars-locator-core-0.55.jar:/Users/<USER>
iuyongyu/.m2/repository/io/github/classgraph/classgraph/4.8.149/classgraph-4.8.149.jar com.stylishlink.ai.Ai
ServiceApplication

```

---

太好了！我发现AI服务进程确实在运行，是进程ID `85454`。现在我需要重新打包AI服务并重启它：

---

```bash
mvn clean package -DskipTests
```

```
[INFO] Scanning for projects...
[INFO] 
[INFO] ---------------< com.stylishlink:stylishlink-ai-service >---------------
[INFO] Building StylishLink - AI Service 1.0.0-SNAPSHOT
[INFO]   from pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] --- clean:3.2.0:clean (default-clean) @ stylishlink-ai-service ---
[INFO] Deleting /Users/<USER>/pa/codes/stylishlink/backend/ai-service/target
[INFO] 
[INFO] --- resources:3.3.1:resources (default-resources) @ stylishlink-ai-service ---
[INFO] Copying 4 resources from src/main/resources to target/classes
[INFO] 
[INFO] --- compiler:3.11.0:compile (default-compile) @ stylishlink-ai-service ---
[INFO] Changes detected - recompiling the module! :source
[INFO] Compiling 75 source files with javac [debug target 17] to target/classes
[INFO] /Users/<USER>/pa/codes/stylishlink/backend/ai-service/src/main/java/com/stylishlink/ai/client/impl
/DoubaoVisionClientImpl.java: 某些输入文件使用了未经检查或不安全的操作。                                    [INFO] /Users/<USER>
yu/pa/codes/stylishlink/backend/ai-service/src/main/java/com/stylishlink/ai/client/impl
/DoubaoVisionClientImpl.java: 有关详细信息, 请使用 -Xlint:unchecked 重新编译。                              [INFO] 
[INFO] --- resources:3.3.1:testResources (default-testResources) @ stylishlink-ai-service ---
[INFO] skip non existing resourceDirectory /Users/<USER>/pa/codes/stylishlink/backend/ai-service/src/test
/resources
[INFO] 
[INFO] --- compiler:3.11.0:testCompile (default-testCompile) @ stylishlink-ai-service ---
[INFO] No sources to compile
[INFO] 
[INFO] --- surefire:3.2.5:test (default-test) @ stylishlink-ai-service ---
[INFO] Tests are skipped.
[INFO] 
[INFO] --- jar:3.4.1:jar (default-jar) @ stylishlink-ai-service ---
[INFO] Building jar: /Users/<USER>/pa/codes/stylishlink/backend/ai-service/target/stylishlink-ai-service-
1.0.0-SNAPSHOT.jar
[INFO] 
[INFO] --- spring-boot:3.2.2:repackage (default) @ stylishlink-ai-service ---
[INFO] Replacing main artifact /Users/<USER>/pa/codes/stylishlink/backend/ai-service/target/stylishlink-a
i-service-1.0.0-SNAPSHOT.jar with repackaged archive, adding nested dependencies in BOOT-INF/.
[INFO] The original artifact has been renamed to /Users/<USER>/pa/codes/stylishlink/backend/ai-service/ta
rget/stylishlink-ai-service-1.0.0-SNAPSHOT.jar.original
[INFO] ------------------------------------------------------------------------
[INFO] BUILD SUCCESS
[INFO] ------------------------------------------------------------------------
[INFO] Total time:  10.538 s
[INFO] Finished at: 2025-06-11T10:23:40+08:00
[INFO] ------------------------------------------------------------------------

```

---

好的！现在我需要停止当前的AI服务进程并启动新版本：

---

```bash
kill 85454
```

```
(base) liuyongyu@liuyongyudeMacBook-Pro ai-service % 

```

---

现在启动新的AI服务：

---

```bash
nohup java -jar target/stylishlink-ai-service-1.0.0-SNAPSHOT.jar --spring.profiles.active=sit > ai-service.log 2>&1 &
```

```
<--spring.profiles.active=sit > ai-service.log 2>&1 &
[1] 89629
(base) liuyongyu@liuyongyudeMacBook-Pro ai-service %
```

---

现在让我检查AI服务是否成功启动：

---

```bash
sleep 5 && tail -n 20 ai-service.log
```

```
2025-06-11 10:24:01 [main] WARN  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '
xForwarderHeadersTransformer' of type [org.springframework.cloud.loadbalancer.blocking.XForwardedHeadersTran
sformer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto
-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [lbRestClientPo
stProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-06-11 10:24:01 [main] WARN  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '
org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration' of type [org.springframework.cl
oud.client.loadbalancer.LoadBalancerAutoConfiguration] is not eligible for getting processed by all BeanPost
Processors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a curre
ntly created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declar
ation and its dependencies.
2025-06-11 10:24:01 [main] WARN  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '
loadBalancerRequestFactory' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerRequestFactor
y] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxy
ing). Is this bean getting eagerly injected into a currently created BeanPostProcessor [lbRestClientPostProc
essor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-06-11 10:24:01 [main] WARN  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '
org.springframework.cloud.loadbalancer.config.BlockingLoadBalancerClientAutoConfiguration$BlockingLoadBalanc
erRetryConfig' of type [org.springframework.cloud.loadbalancer.config.BlockingLoadBalancerClientAutoConfigur
ation$BlockingLoadBalancerRetryConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPost
Processors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a curre
ntly created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declar
ation and its dependencies.
2025-06-11 10:24:01 [main] WARN  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '
loadBalancedRetryFactory' of type [org.springframework.cloud.loadbalancer.blocking.retry.BlockingLoadBalance
dRetryFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible fo
r auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [lbRestCl
ientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-06-11 10:24:01 [main] WARN  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '
loadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.RetryLoadBalancerInterceptor
] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxyi
ng). Is this bean getting eagerly injected into a currently created BeanPostProcessor [lbRestClientPostProce
ssor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-06-11 10:24:01 [main] WARN  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '
org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of ty
pe [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] i
s not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
. The currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor] is declared throug
h a non-static factory method on that class; consider declaring it as static instead.
2025-06-11 10:24:01 [main] WARN  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '
org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$Reacto
rDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBala
ncerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting
 processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eag
erly injected into a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Ch
eck the corresponding BeanPostProcessor declaration and its dependencies.
2025-06-11 10:24:01 [main] WARN  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '
reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.r
eactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostPr
ocessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a current
ly created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPo
stProcessor declaration and its dependencies.
2025-06-11 10:24:02 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port
 8084 (http)
2025-06-11 10:24:02 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-11 10:24:02 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache 
Tomcat/10.1.18]
2025-06-11 10:24:02 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring emb
edded WebApplicationContext
2025-06-11 10:24:02 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext
: initialization completed in 4327 ms
2025-06-11 10:24:02 [main] DEBUG com.stylishlink.ai.config.GatewayUserFilter - Filter 'gatewayUserFilter' co
nfigured for use
2025-06-11 10:24:04 [main] INFO  o.s.cloud.openfeign.FeignClientFactoryBean - For 'file-service' URL not pro
vided. Will try picking an instance via load-balancing.
2025-06-11 10:24:05 [main] INFO  com.stylishlink.ai.config.AiClientConfig - 配置AI客户端工厂，支持智能策略选
择                                                                                                          2
025-06-11 10:24:05 [main] INFO  com.stylishlink.ai.config.AiClientConfig - 可用的AI客户端: DeepSeek=true, O
penAI=false, DoubaoVision=true
2025-06-11 10:24:05 [main] INFO  c.stylishlink.ai.client.AiClientStrategySelector - AI客户端策略选择器初始化
完成，支持的客户端类型: [DEEPSEEK, DOUBAO_VISION]                                                           Logging ini
tialized using 'class org.apache.ibatis.logging.stdout.StdOutImpl' adapter.

```

---

看起来AI服务正在启动中。让我再等一会儿查看是否完全启动：

---

```bash
sleep 10 && tail -n 10 ai-service.log
```

```
2025-06-11 10:24:10 [com.alibaba.nacos.client.remote.worker] INFO  com.alibaba.nacos.client.naming - Grpc co
nnection connect
2025-06-11 10:24:10 [main] INFO  com.alibaba.nacos.client.naming - [REGISTER-SERVICE] public registering ser
vice ai-service with instance Instance{instanceId='null', ip='************', port=8084, weight=1.0, healthy=
true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={IPv6=[2408:8956:2c0
:bcc1:82c2:22ba:2392:8], preserved.register.source=SPRING_CLOUD}}
2025-06-11 10:24:10 [main] INFO  c.a.cloud.nacos.registry.NacosServiceRegistry - nacos registry, DEFAULT_GRO
UP ai-service ************:8084 register finished
2025-06-11 10:24:10 [main] INFO  com.stylishlink.ai.AiServiceApplication - Started AiServiceApplication in 1
5.215 seconds (process running for 15.897)
2025-06-11 10:24:10 [main] DEBUG c.b.mybatisplus.autoconfigure.DdlApplicationRunner -   ...  DDL start creat
e  ...
2025-06-11 10:24:10 [main] DEBUG c.b.mybatisplus.autoconfigure.DdlApplicationRunner -   ...  DDL end create 
 ...
2025-06-11 10:24:10 [main] INFO  com.alibaba.nacos.client.config.impl.CacheData - nacos.cache.data.init.snap
shot = true
2025-06-11 10:24:10 [main] INFO  com.alibaba.nacos.client.config.impl.ClientWorker - [fixed-192.168.8.19_884
8] [subscribe] ai-service+DEFAULT_GROUP
2025-06-11 10:24:10 [main] INFO  com.alibaba.nacos.client.config.impl.CacheData - [fixed-192.168.8.19_8848] 
[add-listener] ok, tenant=, dataId=ai-service, group=DEFAULT_GROUP, cnt=1
2025-06-11 10:24:10 [main] INFO  c.a.cloud.nacos.refresh.NacosContextRefresher - [Nacos Config] Listening co
nfig: dataId=ai-service, group=DEFAULT_GROUP

```

---

太好了！AI服务已经成功启动并监听8084端口。现在让我验证一下八字计算接口是否能够正常工作：

---

Model provided invalid arguments to terminal tool.

---

```bash
curl -X POST http://localhost:8084/api/ai/bazi/calculate -H "Content-Type: application/json" -d '{"fullName": "张三", "birthDate": "1991-06-11", "birthTime": "10:14", "birthPlace": "天津", "gender": "男"}'
```

```
{"code":200,"message":"操作成功","data":{"birthPlace":"天津","gender":"男","birthTime":
"10:14","aiAnalysis":
null,"birthDateTime":"1991-06-11 10:14:00","birthDate":"1991-06-11","baziData":{
"jishen":"金水","monthPillar":"戊子","wuxingDistribution":{"金":1,"水":2,"木":2,"火":1,"
土":2},"elements":["木","金","土"],"mingge":"正 财格","xiyongshen":"木火","yearPillar":"庚
子","dayPillar":"甲寅","hourPillar":"丙寅","dayMaster":"甲"},"timestamp":1749608697658
},"timestamp":"2025-06-11 10:24:57"}%
        
```

---

[MODE: REVIEW]

完美！AI服务现在能够成功处理包含`fullName`字段的请求了。让我验证修复是否完全解决了问题：

1. **问题确认**: ✅ AI服务的`BaZiRequest`类缺少`fullName`字段，导致JSON反序列化失败
2. **修复实施**: ✅ 成功在`BaZiRequest`类中添加了`fullName`字段
3. **服务重启**: ✅ 重新编译、打包并重启了AI服务  
4. **功能验证**: ✅ 测试确认接口现在能够正确处理`fullName`字段

**实施与最终计划完全匹配**: 修改严格按照计划执行，在`backend/ai-service/src/main/java/com/stylishlink/ai/dto/BaZiRequest.java`文件中添加了缺失的`fullName`字段，包含适当的注解和文档。

**测试结果显示**: 
- API返回状态码200，表示请求成功处理
- 响应包含完整的八字计算结果，包括年柱、月柱、日柱、时柱等信息
- 没有再出现"Unrecognized field 'fullName'"的错误

**修复总结**: 
用户服务异步调用AI服务的八字计算功能现在已经完全恢复正常。问题根源是数据传输层的字段不匹配，通过在AI服务的DTO类中补充缺失字段，确保了服务间通信的一致性。

---

_**User**_

ai服务也排查一下这个接口：cessoriesSummary":"银饰或绿松石饰品低调优雅","makeupSummary":"清新自然妆容点缀蓝绿眼影","clothingSummary":"清爽蓝绿系简洁服装提升气场"},"message":"文案简化成功"}
2025-06-11 10:14:01 [http-nio-8084-exec-4] DEBUG com.stylishlink.ai.config.GatewayUserFilter - 处理请求: POST /api/ai/analyze-body-shape-by-file-id, 网关用户信息 - User-Id: null, Nickname: null, Phone: null, Roles: null
2025-06-11 10:14:01 [http-nio-8084-exec-4] DEBUG com.stylishlink.ai.config.GatewayUserFilter - 请求缺少网关用户认证header，路径: /api/ai/analyze-body-shape-by-file-id
2025-06-11 10:14:01 [http-nio-8084-exec-4] INFO  com.stylishlink.common.aop.RequestLoggingAspect - ===> Request: com.stylishlink.ai.controller.BodyShapeController.analyzeBodyShapeByFileId() | Params: ["d1cd7d55e52a40d6a0768a7e8619740d","u6c1de3ba"]
2025-06-11 10:14:01 [http-nio-8084-exec-4] INFO  c.s.ai.service.impl.BodyShapeAnalysisServiceImpl - 开始身材识别分析（文件ID方式），文件ID: d1cd7d55e52a40d6a0768a7e8619740d, 用户: u6c1de3ba
2025-06-11 10:14:01 [http-nio-8084-exec-4] INFO  com.stylishlink.ai.service.FileUploadService - 根据文件ID获取图片URL: d1cd7d55e52a40d6a0768a7e8619740d
2025-06-11 10:14:01 [http-nio-8084-exec-4] DEBUG com.stylishlink.ai.client.FileServiceClient - [FileServiceClient#getFileInfo] ---> GET http://file-service/files/d1cd7d55e52a40d6a0768a7e8619740d HTTP/1.1
2025-06-11 10:14:01 [boundedElastic-1] INFO  com.alibaba.nacos.client.naming - [SUBSCRIBE-SERVICE] service:file-service, group:DEFAULT_GROUP, clusters: 
2025-06-11 10:14:02 [boundedElastic-1] INFO  com.alibaba.nacos.client.naming - init new ips(1) service: DEFAULT_GROUP@@file-service -> [{"instanceId":"*********#8088#DEFAULT#DEFAULT_GROUP@@file-service","ip":"*********","port":8088,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@file-service","metadata":{"preserved.register.source":"SPRING_CLOUD","version":"v1","management.context-path":"/actuator"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-06-11 10:14:02 [boundedElastic-1] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@file-service -> [{"instanceId":"*********#8088#DEFAULT#DEFAULT_GROUP@@file-service","ip":"*********","port":8088,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@file-service","metadata":{"preserved.register.source":"SPRING_CLOUD","version":"v1","management.context-path":"/actuator"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-06-11 10:14:02 [http-nio-8084-exec-4] DEBUG com.stylishlink.ai.client.FileServiceClient - [FileServiceClient#getFileInfo] <--- HTTP/1.1 200 (463ms)
2025-06-11 10:14:02 [http-nio-8084-exec-4] ERROR com.stylishlink.ai.service.FileUploadService - 获取文件信息失败: Error while extracting response for type [class com.stylishlink.ai.client.FileServiceClient$FileInfoResponse] and content type [application/json]
2025-06-11 10:14:02 [http-nio-8084-exec-4] ERROR com.stylishlink.ai.service.FileUploadService - 根据文件ID获取图片URL失败: 获取文件信息失败: Error while extracting response for type [class com.stylishlink.ai.client.FileServiceClient$FileInfoResponse] and content type [application/json]
2025-06-11 10:14:02 [http-nio-8084-exec-4] ERROR c.s.ai.service.impl.BodyShapeAnalysisServiceImpl - 身材识别失败（文件ID方式）
java.lang.RuntimeException: 获取图片URL失败: 获取文件信息失败: Error while extracting response for type [class com.stylishlink.ai.client.FileServiceClient$FileInfoResponse] and content type [application/json]
	at com.stylishlink.ai.service.FileUploadService.getImageUrlById(FileUploadService.java:99)
	at com.stylishlink.ai.service.impl.BodyShapeAnalysisServiceImpl.analyzeBodyShapeByFileId(BodyShapeAnalysisServiceImpl.java:355)
	at com.stylishlink.ai.controller.BodyShapeController.analyzeBodyShapeByFileId(BodyShapeController.java:108)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:351)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.stylishlink.common.aop.RequestLoggingAspect.logApiRequestResponse(RequestLoggingAspect.java:116)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:637)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:627)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.stylishlink.ai.controller.BodyShapeController$$SpringCGLIB$$0.analyzeBodyShapeByFileId(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:261)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:189)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:917)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:829)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.ObservationFilterChainDecorator$FilterObservation$SimpleFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:479)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:340)
	at org.springframework.security.web.ObservationFilterChainDecorator.lambda$wrapSecured$0(ObservationFilterChainDecorator.java:82)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:128)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at com.stylishlink.ai.config.GatewayUserFilter.doFilterInternal(GatewayUserFilter.java:77)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:195)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:225)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.lang.RuntimeException: 获取文件信息失败: Error while extracting response for type [class com.stylishlink.ai.client.FileServiceClient$FileInfoResponse] and content type [application/json]
	at com.stylishlink.ai.service.FileUploadService.getFileInfo(FileUploadService.java:115)
	at com.stylishlink.ai.service.FileUploadService.getImageUrlById(FileUploadService.java:80)
	... 163 common frames omitted
Caused by: feign.codec.DecodeException: Error while extracting response for type [class com.stylishlink.ai.client.FileServiceClient$FileInfoResponse] and content type [application/json]
	at feign.InvocationContext.decode(InvocationContext.java:118)
	at feign.InvocationContext.proceed(InvocationContext.java:88)
	at feign.ResponseHandler.handleResponse(ResponseHandler.java:63)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:114)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:70)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:99)
	at jdk.proxy2/jdk.proxy2.$Proxy138.getFileInfo(Unknown Source)
	at com.stylishlink.ai.service.FileUploadService.getFileInfo(FileUploadService.java:108)
	... 164 common frames omitted
Caused by: org.springframework.web.client.RestClientException: Error while extracting response for type [class com.stylishlink.ai.client.FileServiceClient$FileInfoResponse] and content type [application/json]
	at org.springframework.web.client.HttpMessageConverterExtractor.extractData(HttpMessageConverterExtractor.java:118)
	at org.springframework.cloud.openfeign.support.SpringDecoder.decode(SpringDecoder.java:70)
	at org.springframework.cloud.openfeign.support.ResponseEntityDecoder.decode(ResponseEntityDecoder.java:61)
	at feign.optionals.OptionalDecoder.decode(OptionalDecoder.java:36)
	at feign.InvocationContext.decode(InvocationContext.java:114)
	... 171 common frames omitted
Caused by: org.springframework.http.converter.HttpMessageNotReadableException: JSON parse error: Unrecognized field "timestamp" (class com.stylishlink.ai.client.FileServiceClient$FileInfoResponse), not marked as ignorable
	at org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter.readJavaType(AbstractJackson2HttpMessageConverter.java:406)
	at org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter.read(AbstractJackson2HttpMessageConverter.java:354)
	at org.springframework.web.client.HttpMessageConverterExtractor.extractData(HttpMessageConverterExtractor.java:103)
	... 175 common frames omitted
Caused by: com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException: Unrecognized field "timestamp" (class com.stylishlink.ai.client.FileServiceClient$FileInfoResponse), not marked as ignorable (3 known properties: "data", "code", "message"])
 at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 846] (through reference chain: com.stylishlink.ai.client.FileServiceClient$FileInfoResponse["timestamp"])
	at com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException.from(UnrecognizedPropertyException.java:61)
	at com.fasterxml.jackson.databind.DeserializationContext.handleUnknownProperty(DeserializationContext.java:1138)
	at com.fasterxml.jackson.databind.deser.std.StdDeserializer.handleUnknownProperty(StdDeserializer.java:2224)
	at com.fasterxml.jackson.databind.deser.BeanDeserializerBase.handleUnknownProperty(BeanDeserializerBase.java:1709)
	at com.fasterxml.jackson.databind.deser.BeanDeserializerBase.handleUnknownVanilla(BeanDeserializerBase.java:1687)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.vanillaDeserialize(BeanDeserializer.java:320)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.deserialize(BeanDeserializer.java:177)
	at com.fasterxml.jackson.databind.deser.DefaultDeserializationContext.readRootValue(DefaultDeserializationContext.java:323)
	at com.fasterxml.jackson.databind.ObjectReader._bindAndClose(ObjectReader.java:2105)
	at com.fasterxml.jackson.databind.ObjectReader.readValue(ObjectReader.java:1481)
	at org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter.readJavaType(AbstractJackson2HttpMessageConverter.java:395)
	... 177 common frames omitted
2025-06-11 10:14:02 [http-nio-8084-exec-4] ERROR com.stylishlink.ai.controller.BodyShapeController - 身材识别失败（文件ID方式）
java.lang.RuntimeException: 身材识别失败: 获取图片URL失败: 获取文件信息失败: Error while extracting response for type [class com.stylishlink.ai.client.FileServiceClient$FileInfoResponse] and content type [application/json]
	at com.stylishlink.ai.service.impl.BodyShapeAnalysisServiceImpl.analyzeBodyShapeByFileId(BodyShapeAnalysisServiceImpl.java:377)
	at com.stylishlink.ai.controller.BodyShapeController.analyzeBodyShapeByFileId(BodyShapeController.java:108)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:351)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.stylishlink.common.aop.RequestLoggingAspect.logApiRequestResponse(RequestLoggingAspect.java:116)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:637)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:627)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.stylishlink.ai.controller.BodyShapeController$$SpringCGLIB$$0.analyzeBodyShapeByFileId(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:261)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:189)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:917)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:829)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.ObservationFilterChainDecorator$FilterObservation$SimpleFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:479)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:340)
	at org.springframework.security.web.ObservationFilterChainDecorator.lambda$wrapSecured$0(ObservationFilterChainDecorator.java:82)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:128)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at com.stylishlink.ai.config.GatewayUserFilter.doFilterInternal(GatewayUserFilter.java:77)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:195)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:225)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-06-11 10:14:02 [http-nio-8084-exec-4] INFO  com.stylishlink.common.aop.RequestLoggingAspect - <=== Response: com.stylishlink.ai.controller.BodyShapeController.analyzeBodyShapeByFileId() | Duration: 565ms | Result: {"code":6001,"message":"身材识别失败: 身材识别失败: 获取图片URL失败: 获取文件信息失败: Error while extracting response for type [class com.stylishlink.ai.client.FileServiceClient$FileInfoResponse] and content type [application/json]","timestamp":"2025-06-11T10:14:02.263061569"}


---

