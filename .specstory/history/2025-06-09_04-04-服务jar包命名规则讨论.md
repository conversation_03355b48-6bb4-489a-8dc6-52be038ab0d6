<!-- Generated by SpecStory -->

# 服务jar包命名规则讨论 (2025-06-09 12:04:01)

_**User**_

为什么file service服务构建出来的jar包名叫stylishlink-file-service-1.0.0-SNAPSHOT.jar，但是gateway服务构建出来的jar包名叫gateway-service-1.0.0-SNAPSHOT.jar

---

_**Assistant**_



---

[MODE: RESEARCH]

让我先查看项目结构，了解这两个服务的构建配置差异。

---

<details>
            <summary>Listed current directory • **24** results</summary>
        
| Name |
|-------|
| 📁 `.git` |
| 📁 `backend` |
| 📄 `TASK_selectBodyShape_fix.md` |
| 📁 `doc` |
| 📄 `.DS_Store` |
| 📄 `.gitignore` |
| 📄 `application.log` |
| 📄 `file-service-run.log` |
| 📄 `gateway-stripprefix-test.log` |
| 📄 `final-gateway.log` |
| 📄 `new-gateway.log` |
| 📄 `final-test.log` |
| 📄 `test-gateway.log` |
| 📄 `gateway-new.log` |
| 📄 `gateway.log` |
| 📁 `.vscode` |
| 📄 `pnpm-lock.yaml` |
| 📄 `package.json` |
| 📄 `pnpm-workspace.yaml` |
| 📁 `frontend` |
| 📁 `database` |
| 📄 `.cursorindexingignore` |
| 📁 `.cursor` |
| 📁 `.specstory` |

</details>

---

<details>
            <summary>Listed directory backend • **15** results</summary>
        
| Name |
|-------|
| 📁 `file-service` |
| 📁 `.idea` |
| 📁 `social-service` |
| 📁 `payment-service` |
| 📁 `operation-service` |
| 📁 `ai-service` |
| 📁 `recommendation-service` |
| 📁 `wardrobe-service` |
| 📁 `user-service` |
| 📁 `gateway-service` |
| 📁 `logs` |
| 📁 `common` |
| 📁 `eureka-server` |
| 📁 `docs` |
| 📄 `pom.xml` |

</details>

---

现在让我查看两个服务的pom.xml文件来对比它们的构建配置：

---

Read file: backend/file-service/pom.xml

---

Read file: backend/gateway-service/pom.xml

---

[MODE: INNOVATE]

通过对比两个服务的pom.xml文件，我发现了jar包名称不一致的原因：

**根本原因分析**：
Maven构建的jar包名称遵循 `{artifactId}-{version}.jar` 的格式。两个服务的`artifactId`配置不同：

1. **File Service** (第11行)：
   ```xml
   <artifactId>stylishlink-file-service</artifactId>
   ```
   → 生成jar包名：`stylishlink-file-service-1.0.0-SNAPSHOT.jar`

2. **Gateway Service** (第11行)：
   ```xml
   <artifactId>gateway-service</artifactId>
   ```
   → 生成jar包名：`gateway-service-1.0.0-SNAPSHOT.jar`

**命名不一致的影响**：
- **可维护性问题**: 不同的命名规范会让开发者困惑
- **部署脚本**: 如果有自动化部署脚本，需要处理不同的命名模式
- **项目规范**: 缺乏统一的命名标准

**潜在的解决方案**：
1. **统一添加前缀**: 将所有服务的artifactId都加上`stylishlink-`前缀
2. **统一去除前缀**: 将file service的artifactId改为`file-service`
3. **通过final name属性**: 在build配置中使用`<finalName>`标签统一控制jar包名称

[MODE: PLAN]

基于发现的问题，我建议采用**统一添加前缀**的方案，原因如下：

**实施计划**：

**方案选择理由**：
- 保持项目品牌标识（stylishlink前缀）
- 与父项目的groupId和artifactId命名保持一致
- 便于在复杂环境中识别jar包归属

**具体修改计划**：
```
文件：backend/gateway-service/pom.xml
理由：将gateway-service的artifactId修改为stylishlink-gateway-service，与其他服务保持命名一致性
```

**实施检查清单**：
1. 修改gateway-service的pom.xml文件中的artifactId为`stylishlink-gateway-service`
2. 验证修改后的配置是否正确
3. 测试构建过程确保jar包名称符合预期
4. 检查是否有其他依赖引用需要同步更新

[MODE: EXECUTE]

正在执行检查清单第1项：修改gateway-service的pom.xml文件中的artifactId。

---



---

**[2024-12-22 已完成]**
- 步骤：1. 修改gateway-service的pom.xml文件中的artifactId为stylishlink-gateway-service
- 修改：backend/gateway-service/pom.xml - 将artifactId从gateway-service改为stylishlink-gateway-service
- 更改摘要：统一服务命名规范，添加stylishlink前缀保持一致性
- 原因：执行计划步骤1，解决jar包命名不一致问题
- 阻碍：无
- 状态：待确认

请检查针对步骤1的更改。请确认状态（成功 / 成功但有小问题 / 失败）并在必要时提供反馈。

正在执行检查清单第2项：验证修改后的配置是否正确。

---

Read file: backend/gateway-service/pom.xml

---

配置验证正确！artifactId已成功从`gateway-service`修改为`stylishlink-gateway-service`。

正在执行检查清单第3项：测试构建过程确保jar包名称符合预期。

---

```bash
cd backend/gateway-service && mvn clean package -DskipTests
```

```
^L
cd backend/gateway-service && mvn clean package -DskipTests
```

---

