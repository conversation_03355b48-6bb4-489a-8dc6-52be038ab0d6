<!-- Generated by SpecStory -->

# 检查网关服务问题 (2025-03-14 18:28:24)

_**User**_

检查网管服务

---

_**Assistant**_

<think>从用户提供的信息来看，我需要检查StylishLink项目中的网关服务（gateway-service）。根据日志和错误信息，我发现有几个问题：

1. 在JwtAuthenticationFilter.java文件中，有"找不到符号 log"的错误，这表明可能缺少Lombok注解或日志声明。

2. SecurityConfig类中有一些过时的方法调用，这些是警告而非错误。

3. 尝试使用`mvn spring-boot:run`命令在根目录运行时遇到"No plugin found for prefix 'spring-boot'"错误，这表明在该目录下可能没有正确配置Spring Boot Maven插件。

我将首先查看gateway-service的源代码，特别是JwtAuthenticationFilter.java文件，以解决"找不到符号 log"的错误。</think># 网关服务检查报告

我将检查网关服务的问题并找出解决方案。

---

<details>
            <summary>Searched codebase "JwtAuthenticationFilter" • **13** results</summary>
        
| File | Lines |
|------|-------|
| `backend/gateway-service/src/main/java/com/stylishlink/gateway/filter/JwtAuthenticationFilter.java` | L1-27 |
| `backend/gateway-service/src/main/java/com/stylishlink/gateway/filter/JwtAuthenticationFilter.java` | L27-63 |
| `backend/gateway-service/src/main/java/com/stylishlink/gateway/filter/JwtAuthenticationFilter.java` | L63-90 |
| `backend/gateway-service/src/main/java/com/stylishlink/gateway/config/SecurityConfig.java` | L1-48 |
| `backend/gateway-service/target/maven-status/maven-compiler-plugin/compile/default-compile/inputFiles.lst` | L1-5 |
| `backend/gateway-service/target/maven-status/maven-compiler-plugin/compile/default-compile/createdFiles.lst` | L1-5 |
| `backend/gateway-service/target/classes/application.yml` | L1-51 |
| `backend/gateway-service/src/main/resources/application.yml` | L1-51 |
| `backend/gateway-service/pom.xml` | L58-103 |
| `backend/gateway-service/pom.xml` | L1-58 |
| `backend/gateway-service/src/main/java/com/stylishlink/gateway/config/RouteConfig.java` | L1-42 |
| `backend/gateway-service/pom.xml` | L103-119 |
| `backend/gateway-service/src/main/java/com/stylishlink/gateway/GatewayServiceApplication.java` | L1-17 |

</details>

---

Read file: backend/gateway-service/src/main/java/com/stylishlink/gateway/filter/JwtAuthenticationFilter.java

---

代码检查完毕后，我发现问题在于JwtAuthenticationFilter类中使用了log变量，但是缺少Lombok的`@Slf4j`注解。虽然代码中正确定义了静态log变量：

```java
private static final Logger log = LoggerFactory.getLogger(JwtAuthenticationFilter.class);
```

但是在Maven编译时却报错找不到这个变量，这可能是由于编译时的环境问题。让我们修复这个问题：

---

```diff
  import java.util.Arrays;
  import java.util.List;
+ import lombok.extern.slf4j.Slf4j;
  
  /**
   * JWT认证过滤器
   */
  @Component
+ @Slf4j
  public class JwtAuthenticationFilter implements GlobalFilter, Ordered {
- 
-     private static final Logger log = LoggerFactory.getLogger(JwtAuthenticationFilter.class);
  
      @Value("${jwt.secret}")
```

---

现在让我们检查一下SecurityConfig类中的过时API问题，这个问题是警告而不是错误，但最好也一并修复：

---

Read file: backend/gateway-service/src/main/java/com/stylishlink/gateway/config/SecurityConfig.java

---

现在让我修复SecurityConfig类中的过时API问题：

---

```diff
      public SecurityWebFilterChain securityWebFilterChain(ServerHttpSecurity http) {
          return http
-                 .csrf().disable()
-                 .cors().configurationSource(corsConfigurationSource()).and()
-                 .authorizeExchange()
-                 .pathMatchers("/api/user/login", "/api/user/register", "/actuator/**").permitAll()
-                 .anyExchange().authenticated()
-                 .and()
-                 .httpBasic().disable()
-                 .formLogin().disable()
+                 .csrf(ServerHttpSecurity.CsrfSpec::disable)
+                 .cors(corsSpec -> corsSpec.configurationSource(corsConfigurationSource()))
+                 .authorizeExchange(authorizeExchangeSpec -> authorizeExchangeSpec
+                         .pathMatchers("/api/user/login", "/api/user/register", "/actuator/**").permitAll()
+                         .anyExchange().authenticated()
+                 )
+                 .httpBasic(ServerHttpSecurity.HttpBasicSpec::disable)
+                 .formLogin(ServerHttpSecurity.FormLoginSpec::disable)
                  .build();
      }
```

---

现在让我们尝试运行gateway-service来验证修复是否成功：

---

```bash
cd backend/gateway-service && mvn clean compile
```

```
cd backend/gateway-service && mvn clean compile
(base) dubin@admindeMBP StylishLink % cd backend/gateway-service && mvn clean compile
[INFO] Scanning for projects...
[INFO] 
[INFO] ------------------< com.stylishlink:gateway-service >-------------------
[INFO] Building Gateway Service 1.0.0-SNAPSHOT
[INFO]   from pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] --- clean:3.2.0:clean (default-clean) @ gateway-service ---
[INFO] Deleting /Users/<USER>/work/AI/StylishLink/backend/gateway-service/target
[INFO] 
[INFO] --- resources:3.3.1:resources (default-resources) @ gateway-service ---
[INFO] Copying 1 resource from src/main/resources to target/classes
[INFO] 
[INFO] --- compiler:3.13.0:compile (default-compile) @ gateway-service ---
[INFO] Recompiling the module because of changed source code.
[INFO] Compiling 4 source files with javac [debug target 17] to target/classes
[INFO] -------------------------------------------------------------
[WARNING] COMPILATION WARNING : 
[INFO] -------------------------------------------------------------
[WARNING] 未与 -source 17 一起设置系统模块的位置
  不设置系统模块的位置可能会导致类文件无法在 JDK 17 上运行
    建议使用 --release 17 而不是 -source 17 -target 17，因为它会自动设置系统模块的位置
[INFO] 1 warning
[INFO] -------------------------------------------------------------
[INFO] -------------------------------------------------------------
[ERROR] COMPILATION ERROR : 
[INFO] -------------------------------------------------------------
[ERROR] /Users/<USER>/work/AI/StylishLink/backend/gateway-service/src/main/java/com/stylishlink/gateway/filte
r/JwtAuthenticationFilter.java:[57,13] 找不到符号
  符号:   变量 log
  位置: 类 com.stylishlink.gateway.filter.JwtAuthenticationFilter
[ERROR] /Users/<USER>/work/AI/StylishLink/backend/gateway-service/src/main/java/com/stylishlink/gateway/filte
r/JwtAuthenticationFilter.java:[80,13] 找不到符号
  符号:   变量 log
  位置: 类 com.stylishlink.gateway.filter.JwtAuthenticationFilter
[INFO] 2 errors 
[INFO] -------------------------------------------------------------
[INFO] ------------------------------------------------------------------------
[INFO] BUILD FAILURE
[INFO] ------------------------------------------------------------------------
[INFO] Total time:  36.052 s
[INFO] Finished at: 2025-03-14T18:31:51+08:00
[INFO] ------------------------------------------------------------------------
[ERROR] Failed to execute goal org.apache.maven.plugins:maven-compiler-plugin:3.13.0:compile (default-compil
e) on project gateway-service: Compilation failure: Compilation failure: 
[ERROR] /Users/<USER>/work/AI/StylishLink/backend/gateway-service/src/main/java/com/stylishlink/gateway/filte
r/JwtAuthenticationFilter.java:[57,13] 找不到符号
[ERROR]   符号:   变量 log
[ERROR]   位置: 类 com.stylishlink.gateway.filter.JwtAuthenticationFilter
[ERROR] /Users/<USER>/work/AI/StylishLink/backend/gateway-service/src/main/java/com/stylishlink/gateway/filte
r/JwtAuthenticationFilter.java:[80,13] 找不到符号
[ERROR]   符号:   变量 log
[ERROR]   位置: 类 com.stylishlink.gateway.filter.JwtAuthenticationFilter
[ERROR] -> [Help 1]
[ERROR] 
[ERROR] To see the full stack trace of the errors, re-run Maven with the -e switch.
[ERROR] Re-run Maven using the -X switch to enable full debug logging.
[ERROR] 
[ERROR] For more information about the errors and possible solutions, please read the following articles:
[ERROR] [Help 1] http://cwiki.apache.org/confluence/display/MAVEN/MojoFailureException
(base) dubin@admindeMBP gateway-service %
```

---

我们需要检查JwtAuthenticationFilter.java文件，看看为什么@Slf4j注解没有正常工作。可能是我们修改的代码没有完全覆盖到原文件。让我们查看修改后的文件：

---

Read file: backend/gateway-service/src/main/java/com/stylishlink/gateway/filter/JwtAuthenticationFilter.java

---

我发现虽然添加了@Slf4j注解，但仍然出现编译错误。我们需要检查项目是否正确配置了Lombok。让我们检查下pom.xml文件：

---

Read file: backend/gateway-service/pom.xml

---

pom.xml文件显示Lombok依赖已正确配置，但我注意到在JwtAuthenticationFilter.java中，我们虽然添加了@Slf4j注解，但可能其他问题导致编译失败。让我们尝试一个不同的解决方案，直接使用手动实现日志而不使用Lombok：

---

```diff
  import java.util.Arrays;
  import java.util.List;
- import lombok.extern.slf4j.Slf4j;
  
  /**
   * JWT认证过滤器
   */
  @Component
- @Slf4j
  public class JwtAuthenticationFilter implements GlobalFilter, Ordered {
+ 
+     private static final Logger logger = LoggerFactory.getLogger(JwtAuthenticationFilter.class);
  
      @Value("${jwt.secret}")
          // 检查Authorization头
          if (authHeader == null || !authHeader.startsWith("Bearer ")) {
-             log.error("未提供有效的Authorization头");
+             logger.error("未提供有效的Authorization头");
              exchange.getResponse().setStatusCode(HttpStatus.UNAUTHORIZED);
              return exchange.getResponse().setComplete();
              return chain.filter(exchange.mutate().request(mutatedRequest).build());
          } catch (JwtException e) {
-             log.error("Token验证失败: {}", e.getMessage());
+             logger.error("Token验证失败: {}", e.getMessage());
              exchange.getResponse().setStatusCode(HttpStatus.UNAUTHORIZED);
              return exchange.getResponse().setComplete();
```

---

现在让我们尝试重新编译网关服务：

---

```bash
cd backend/gateway-service && mvn clean compile
```

```
(base) dubin@admindeMBP gateway-service % cd backend/gateway-service && mvn clean compile
cd: no such file or directory: backend/gateway-service
(base) dubin@admindeMBP gateway-service %
```

---

我们已经在gateway-service目录下，只需运行mvn clean compile命令：

---

```bash
mvn clean compile
```

```
(base) dubin@admindeMBP gateway-service % mvn clean compile
[INFO] Scanning for projects...
[INFO] 
[INFO] ------------------< com.stylishlink:gateway-service >-------------------
[INFO] Building Gateway Service 1.0.0-SNAPSHOT
[INFO]   from pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] --- clean:3.2.0:clean (default-clean) @ gateway-service ---
[INFO] Deleting /Users/<USER>/work/AI/StylishLink/backend/gateway-service/target
[INFO] 
[INFO] --- resources:3.3.1:resources (default-resources) @ gateway-service ---
[INFO] Copying 1 resource from src/main/resources to target/classes
[INFO] 
[INFO] --- compiler:3.13.0:compile (default-compile) @ gateway-service ---
[INFO] Recompiling the module because of changed source code.
[INFO] Compiling 4 source files with javac [debug target 17] to target/classes
[WARNING] 未与 -source 17 一起设置系统模块的位置
  不设置系统模块的位置可能会导致类文件无法在 JDK 17 上运行
    建议使用 --release 17 而不是 -source 17 -target 17，因为它会自动设置系统模块的位置
[INFO] ------------------------------------------------------------------------
[INFO] BUILD SUCCESS
[INFO] ------------------------------------------------------------------------
[INFO] Total time:  27.748 s
[INFO] Finished at: 2025-03-14T18:33:35+08:00
[INFO] ------------------------------------------------------------------------
(base) dubin@admindeMBP gateway-service %
```

---

太好了！编译成功，错误已经解决。现在我们尝试运行网关服务：

---

```bash
mvn spring-boot:run
```

```
(base) dubin@admindeMBP gateway-service % mvn spring-boot:run
[INFO] Scanning for projects...
[INFO] 
[INFO] ------------------< com.stylishlink:gateway-service >-------------------
[INFO] Building Gateway Service 1.0.0-SNAPSHOT
[INFO]   from pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] >>> spring-boot:3.2.2:run (default-cli) > test-compile @ gateway-service >>>
[INFO] 
[INFO] --- resources:3.3.1:resources (default-resources) @ gateway-service ---
[INFO] Copying 1 resource from src/main/resources to target/classes
[INFO] 
[INFO] --- compiler:3.13.0:compile (default-compile) @ gateway-service ---
[INFO] Nothing to compile - all classes are up to date.
[INFO] 
[INFO] --- resources:3.3.1:testResources (default-testResources) @ gateway-service ---
[INFO] skip non existing resourceDirectory /Users/<USER>/work/AI/StylishLink/backend/gateway-service/src/test
/resources
[INFO] 
[INFO] --- compiler:3.13.0:testCompile (default-testCompile) @ gateway-service ---
[INFO] No sources to compile
[INFO] 
[INFO] <<< spring-boot:3.2.2:run (default-cli) < test-compile @ gateway-service <<<
[INFO] 
[INFO] 
[INFO] --- spring-boot:3.2.2:run (default-cli) @ gateway-service ---
[INFO] Attaching agents: []
18:34:14.469 [main] ERROR org.springframework.boot.SpringApplication -- Application run failed
org.yaml.snakeyaml.scanner.ScannerException: mapping values are not allowed here
 in 'reader', line 8, column 34:
        import: optional:configserver:
                                     ^

        at org.yaml.snakeyaml.scanner.ScannerImpl.fetchValue(ScannerImpl.java:902)
        at org.yaml.snakeyaml.scanner.ScannerImpl.fetchMoreTokens(ScannerImpl.java:391)
        at org.yaml.snakeyaml.scanner.ScannerImpl.checkToken(ScannerImpl.java:238)
        at org.yaml.snakeyaml.parser.ParserImpl$ParseBlockMappingKey.produce(ParserImpl.java:637)
        at org.yaml.snakeyaml.parser.ParserImpl.peekEvent(ParserImpl.java:161)
        at org.yaml.snakeyaml.comments.CommentEventsCollector$1.peek(CommentEventsCollector.java:57)
        at org.yaml.snakeyaml.comments.CommentEventsCollector$1.peek(CommentEventsCollector.java:43)
        at org.yaml.snakeyaml.comments.CommentEventsCollector.collectEvents(CommentEventsCollector.java:136)
        at org.yaml.snakeyaml.comments.CommentEventsCollector.collectEvents(CommentEventsCollector.java:116)
        at org.yaml.snakeyaml.composer.Composer.composeScalarNode(Composer.java:241)
        at org.yaml.snakeyaml.composer.Composer.composeNode(Composer.java:205)
        at org.yaml.snakeyaml.composer.Composer.composeValueNode(Composer.java:369)
        at org.yaml.snakeyaml.composer.Composer.composeMappingChildren(Composer.java:348)
        at org.yaml.snakeyaml.composer.Composer.composeMappingNode(Composer.java:323)
        at org.yaml.snakeyaml.composer.Composer.composeNode(Composer.java:209)
        at org.yaml.snakeyaml.composer.Composer.composeValueNode(Composer.java:369)
        at org.yaml.snakeyaml.composer.Composer.composeMappingChildren(Composer.java:348)
        at org.yaml.snakeyaml.composer.Composer.composeMappingNode(Composer.java:323)
        at org.yaml.snakeyaml.composer.Composer.composeNode(Composer.java:209)
        at org.yaml.snakeyaml.composer.Composer.composeValueNode(Composer.java:369)
        at org.yaml.snakeyaml.composer.Composer.composeMappingChildren(Composer.java:348)
        at org.yaml.snakeyaml.composer.Composer.composeMappingNode(Composer.java:323)
        at org.yaml.snakeyaml.composer.Composer.composeNode(Composer.java:209)
        at org.yaml.snakeyaml.composer.Composer.getNode(Composer.java:131)
        at org.yaml.snakeyaml.constructor.BaseConstructor.getData(BaseConstructor.java:162)
        at org.springframework.boot.env.OriginTrackedYamlLoader$OriginTrackingConstructor.getData(OriginTrac
kedYamlLoader.java:99)
        at org.yaml.snakeyaml.Yaml$1.next(Yaml.java:515)
        at org.springframework.beans.factory.config.YamlProcessor.process(YamlProcessor.java:203)
        at org.springframework.beans.factory.config.YamlProcessor.process(YamlProcessor.java:169)
        at org.springframework.boot.env.OriginTrackedYamlLoader.load(OriginTrackedYamlLoader.java:84)
        at org.springframework.boot.env.YamlPropertySourceLoader.load(YamlPropertySourceLoader.java:50)
        at org.springframework.boot.context.config.StandardConfigDataLoader.load(StandardConfigDataLoader.ja
va:54)
        at org.springframework.boot.context.config.StandardConfigDataLoader.load(StandardConfigDataLoader.ja
va:36)
        at org.springframework.boot.context.config.ConfigDataLoaders.load(ConfigDataLoaders.java:96)
        at org.springframework.boot.context.config.ConfigDataImporter.load(ConfigDataImporter.java:132)
        at org.springframework.boot.context.config.ConfigDataImporter.resolveAndLoad(ConfigDataImporter.java
:87)
        at org.springframework.boot.context.config.ConfigDataEnvironmentContributors.withProcessedImports(Co
nfigDataEnvironmentContributors.java:115)
        at org.springframework.boot.context.config.ConfigDataEnvironment.processInitial(ConfigDataEnvironmen
t.java:242)
        at org.springframework.boot.context.config.ConfigDataEnvironment.processAndApply(ConfigDataEnvironme
nt.java:229)
        at org.springframework.boot.context.config.ConfigDataEnvironmentPostProcessor.postProcessEnvironment
(ConfigDataEnvironmentPostProcessor.java:96)
        at org.springframework.boot.context.config.ConfigDataEnvironmentPostProcessor.postProcessEnvironment
(ConfigDataEnvironmentPostProcessor.java:89)
        at org.springframework.boot.env.EnvironmentPostProcessorApplicationListener.onApplicationEnvironment
PreparedEvent(EnvironmentPostProcessorApplicationListener.java:109)
        at org.springframework.boot.env.EnvironmentPostProcessorApplicationListener.onApplicationEvent(Envir
onmentPostProcessorApplicationListener.java:94)
        at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplic
ationEventMulticaster.java:178)
        at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicat
ionEventMulticaster.java:171)
        at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicat
ionEventMulticaster.java:149)
        at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicat
ionEventMulticaster.java:137)
        at org.springframework.boot.context.event.EventPublishingRunListener.multicastInitialEvent(EventPubl
ishingRunListener.java:136)
        at org.springframework.boot.context.event.EventPublishingRunListener.environmentPrepared(EventPublis
hingRunListener.java:81)
        at org.springframework.boot.SpringApplicationRunListeners.lambda$environmentPrepared$2(SpringApplica
tionRunListeners.java:64)
        at java.base/java.lang.Iterable.forEach(Iterable.java:75)
        at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListen
ers.java:118)
        at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListen
ers.java:112)
        at org.springframework.boot.SpringApplicationRunListeners.environmentPrepared(SpringApplicationRunLi
steners.java:63)
        at org.springframework.boot.SpringApplication.prepareEnvironment(SpringApplication.java:369)
        at org.springframework.boot.SpringApplication.run(SpringApplication.java:329)
        at org.springframework.boot.SpringApplication.run(SpringApplication.java:1354)
        at org.springframework.boot.SpringApplication.run(SpringApplication.java:1343)
        at com.stylishlink.gateway.GatewayServiceApplication.main(GatewayServiceApplication.java:15)
[INFO] ------------------------------------------------------------------------
[INFO] BUILD FAILURE
[INFO] ------------------------------------------------------------------------
[INFO] Total time:  23.081 s
[INFO] Finished at: 2025-03-14T18:34:14+08:00
[INFO] ------------------------------------------------------------------------
[ERROR] Failed to execute goal org.springframework.boot:spring-boot-maven-plugin:3.2.2:run (default-cli) on 
project gateway-service: Process terminated with exit code: 1 -> [Help 1]
[ERROR] 
[ERROR] To see the full stack trace of the errors, re-run Maven with the -e switch.
[ERROR] Re-run Maven using the -X switch to enable full debug logging.
[ERROR] 
[ERROR] For more information about the errors and possible solutions, please read the following articles:
[ERROR] [Help 1] http://cwiki.apache.org/confluence/display/MAVEN/MojoExecutionException
(base) dubin@admindeMBP gateway-service %
```

---

我们发现项目的application.yml文件有语法错误。错误信息显示：

```
mapping values are not allowed here
 in 'reader', line 8, column 34:
        import: optional:configserver:
```

让我们修复这个问题：

---

Read file: backend/gateway-service/src/main/resources/application.yml

---

找到了问题，在application.yml中，`spring.config.import: optional:configserver:`这行有语法错误。我们需要修复它：

---

```diff
      name: gateway-service
    config:
-     import: optional:configserver:
+     import: "optional:configserver:"
    cloud:
      gateway:
```

---

现在再次尝试运行网关服务：

---

```bash
mvn spring-boot:run
```

```
[INFO] Scanning for projects...
[INFO] 
[INFO] ------------------< com.stylishlink:gateway-service >-------------------
[INFO] Building Gateway Service 1.0.0-SNAPSHOT
[INFO]   from pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] >>> spring-boot:3.2.2:run (default-cli) > test-compile @ gateway-service >>>
[INFO] 
[INFO] --- resources:3.3.1:resources (default-resources) @ gateway-service ---
[INFO] Copying 1 resource from src/main/resources to target/classes
[INFO] 
[INFO] --- compiler:3.13.0:compile (default-compile) @ gateway-service ---
[INFO] Nothing to compile - all classes are up to date.
[INFO] 
[INFO] --- resources:3.3.1:testResources (default-testResources) @ gateway-service ---
[INFO] skip non existing resourceDirectory /Users/<USER>/work/AI/StylishLink/backend/gateway-service/src/test
/resources
[INFO] 
[INFO] --- compiler:3.13.0:testCompile (default-testCompile) @ gateway-service ---
[INFO] No sources to compile
[INFO] 
[INFO] <<< spring-boot:3.2.2:run (default-cli) < test-compile @ gateway-service <<<
[INFO] 
[INFO] 
[INFO] --- spring-boot:3.2.2:run (default-cli) @ gateway-service ---
[INFO] Attaching agents: []

  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/
 :: Spring Boot ::                (v3.2.2)

2025-03-14T18:35:31.294+08:00  INFO 97636 --- [gateway-service] [           main] c.s.gateway.GatewayService
Application    : Starting GatewayServiceApplication using Java 23.0.1 with PID 97636 (/Users/<USER>/work/AI/S
tylishLink/backend/gateway-service/target/classes started by dubin in /Users/<USER>/work/AI/StylishLink/backe
nd/gateway-service)
2025-03-14T18:35:31.300+08:00  INFO 97636 --- [gateway-service] [           main] c.s.gateway.GatewayService
Application    : No active profile set, falling back to 1 default profile: "default"
2025-03-14T18:35:31.756+08:00  INFO 97636 --- [gateway-service] [           main] o.s.c.c.c.ConfigServerConf
igDataLoader   : Fetching config from server at : http://localhost:8888
2025-03-14T18:35:31.760+08:00  INFO 97636 --- [gateway-service] [           main] o.s.c.c.c.ConfigServerConf
igDataLoader   : Exception on Url - http://localhost:8888:org.springframework.web.client.ResourceAccessExcep
tion: I/O error on GET request for "http://localhost:8888/gateway-service/default": Connection refused. Will
 be trying the next url if available
2025-03-14T18:35:31.760+08:00  WARN 97636 --- [gateway-service] [           main] o.s.c.c.c.ConfigServerConf
igDataLoader   : Could not locate PropertySource ([ConfigServerConfigDataResource@8ab78bc uris = array<Strin
g>['http://localhost:8888'], optional = true, profiles = 'default']): I/O error on GET request for "http://l
ocalhost:8888/gateway-service/default": Connection refused
2025-03-14T18:35:31.761+08:00  INFO 97636 --- [gateway-service] [           main] o.s.c.c.c.ConfigServerConf
igDataLoader   : Fetching config from server at : http://localhost:8888
2025-03-14T18:35:31.762+08:00  INFO 97636 --- [gateway-service] [           main] o.s.c.c.c.ConfigServerConf
igDataLoader   : Exception on Url - http://localhost:8888:org.springframework.web.client.ResourceAccessExcep
tion: I/O error on GET request for "http://localhost:8888/gateway-service/default": Connection refused. Will
 be trying the next url if available
2025-03-14T18:35:31.763+08:00  WARN 97636 --- [gateway-service] [           main] o.s.c.c.c.ConfigServerConf
igDataLoader   : Could not locate PropertySource ([ConfigServerConfigDataResource@560513ce uris = array<Stri
ng>['http://localhost:8888'], optional = true, profiles = 'default']): I/O error on GET request for "http://
localhost:8888/gateway-service/default": Connection refused
2025-03-14T18:35:31.771+08:00  INFO 97636 --- [gateway-service] [           main] o.s.c.c.c.ConfigServerConf
igDataLoader   : Fetching config from server at : http://localhost:8888
2025-03-14T18:35:31.777+08:00  INFO 97636 --- [gateway-service] [           main] o.s.c.c.c.ConfigServerConf
igDataLoader   : Exception on Url - http://localhost:8888:org.springframework.web.client.ResourceAccessExcep
tion: I/O error on GET request for "http://localhost:8888/gateway-service/default": Connection refused. Will
 be trying the next url if available
2025-03-14T18:35:31.779+08:00  WARN 97636 --- [gateway-service] [           main] o.s.c.c.c.ConfigServerConf
igDataLoader   : Could not locate PropertySource ([ConfigServerConfigDataResource@13006998 uris = array<Stri
ng>['http://localhost:8888'], optional = true, profiles = 'default']): I/O error on GET request for "http://
localhost:8888/gateway-service/default": Connection refused
2025-03-14T18:35:36.598+08:00  INFO 97636 --- [gateway-service] [           main] .s.d.r.c.RepositoryConfigu
rationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-03-14T18:35:36.612+08:00  INFO 97636 --- [gateway-service] [           main] .s.d.r.c.RepositoryConfigu
rationDelegate : Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
2025-03-14T18:35:36.678+08:00  INFO 97636 --- [gateway-service] [           main] .s.d.r.c.RepositoryConfigu
rationDelegate : Finished Spring Data repository scanning in 46 ms. Found 0 MongoDB repository interfaces.
2025-03-14T18:35:36.728+08:00  INFO 97636 --- [gateway-service] [           main] .s.d.r.c.RepositoryConfigu
rationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-03-14T18:35:36.731+08:00  INFO 97636 --- [gateway-service] [           main] .s.d.r.c.RepositoryConfigu
rationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-03-14T18:35:36.759+08:00  INFO 97636 --- [gateway-service] [           main] .s.d.r.c.RepositoryConfigu
rationDelegate : Finished Spring Data repository scanning in 9 ms. Found 0 Redis repository interfaces.
2025-03-14T18:35:36.799+08:00  WARN 97636 --- [gateway-service] [           main] ConfigServletWebServerAppl
icationContext : Exception encountered during context initialization - cancelling refresh attempt: org.sprin
gframework.beans.factory.support.BeanDefinitionOverrideException: Invalid bean definition with name 'convers
ionServicePostProcessor' defined in class path resource [org/springframework/security/config/annotation/web/
configuration/WebSecurityConfiguration.class]: Cannot register bean definition [Root bean: class [org.spring
framework.security.config.annotation.web.configuration.WebSecurityConfiguration]; scope=; abstract=false; la
zyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null;
 factoryMethodName=conversionServicePostProcessor; initMethodNames=null; destroyMethodNames=[(inferred)]; de
fined in class path resource [org/springframework/security/config/annotation/web/configuration/WebSecurityCo
nfiguration.class]] for bean 'conversionServicePostProcessor' since there is already [Root bean: class [org.
springframework.security.config.annotation.web.reactive.WebFluxSecurityConfiguration]; scope=; abstract=fals
e; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=
null; factoryMethodName=conversionServicePostProcessor; initMethodNames=null; destroyMethodNames=[(inferred)
]; defined in class path resource [org/springframework/security/config/annotation/web/reactive/WebFluxSecuri
tyConfiguration.class]] bound.
2025-03-14T18:35:36.859+08:00  INFO 97636 --- [gateway-service] [           main] .s.b.a.l.ConditionEvaluati
onReportLogger :

Error starting ApplicationContext. To display the condition evaluation report re-run your application with '
debug' enabled.
2025-03-14T18:35:36.974+08:00 ERROR 97636 --- [gateway-service] [           main] o.s.b.d.LoggingFailureAnal
ysisReporter   :

***************************
APPLICATION FAILED TO START
***************************

Description:

The bean 'conversionServicePostProcessor', defined in class path resource [org/springframework/security/conf
ig/annotation/web/configuration/WebSecurityConfiguration.class], could not be registered. A bean with that n
ame has already been defined in class path resource [org/springframework/security/config/annotation/web/reac
tive/WebFluxSecurityConfiguration.class] and overriding is disabled.

Action:

Consider renaming one of the beans or enabling overriding by setting spring.main.allow-bean-definition-overr
iding=true

[INFO] ------------------------------------------------------------------------
[INFO] BUILD FAILURE
[INFO] ------------------------------------------------------------------------
[INFO] Total time:  35.696 s
[INFO] Finished at: 2025-03-14T18:35:37+08:00
[INFO] ------------------------------------------------------------------------
[ERROR] Failed to execute goal org.springframework.boot:spring-boot-maven-plugin:3.2.2:run (default-cli) on 
project gateway-service: Process terminated with exit code: 1 -> [Help 1]
[ERROR] 
[ERROR] To see the full stack trace of the errors, re-run Maven with the -e switch.
[ERROR] Re-run Maven using the -X switch to enable full debug logging.
[ERROR] 
[ERROR] For more information about the errors and possible solutions, please read the following articles:
[ERROR] [Help 1] http://cwiki.apache.org/confluence/display/MAVEN/MojoExecutionException

```

---

我们遇到了一个新的问题：Spring Security Web和WebFlux依赖冲突。错误信息建议设置`spring.main.allow-bean-definition-overriding=true`来解决这个问题。我们来修改application.yml：

---



---

让我们手动编辑文件：

---

```diff
  
  spring:
+   main:
+     allow-bean-definition-overriding: true
    application:
      name: gateway-service
```

---

现在我们再次尝试运行网关服务：

---

```bash
mvn spring-boot:run
```

```
[INFO] Scanning for projects...
[INFO] 
[INFO] ------------------< com.stylishlink:gateway-service >-------------------
[INFO] Building Gateway Service 1.0.0-SNAPSHOT
[INFO]   from pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] >>> spring-boot:3.2.2:run (default-cli) > test-compile @ gateway-service >>>
[INFO] 
[INFO] --- resources:3.3.1:resources (default-resources) @ gateway-service ---
[INFO] Copying 1 resource from src/main/resources to target/classes
[INFO] 
[INFO] --- compiler:3.13.0:compile (default-compile) @ gateway-service ---
[INFO] Nothing to compile - all classes are up to date.
[INFO] 
[INFO] --- resources:3.3.1:testResources (default-testResources) @ gateway-service ---
[INFO] skip non existing resourceDirectory /Users/<USER>/work/AI/StylishLink/backend/gateway-service/src/test
/resources
[INFO] 
[INFO] --- compiler:3.13.0:testCompile (default-testCompile) @ gateway-service ---
[INFO] No sources to compile
[INFO] 
[INFO] <<< spring-boot:3.2.2:run (default-cli) < test-compile @ gateway-service <<<
[INFO] 
[INFO] 
[INFO] --- spring-boot:3.2.2:run (default-cli) @ gateway-service ---
[INFO] Attaching agents: []

  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/
 :: Spring Boot ::                (v3.2.2)

2025-03-14T18:36:41.197+08:00  INFO 98279 --- [gateway-service] [           main] c.s.gateway.GatewayService
Application    : Starting GatewayServiceApplication using Java 23.0.1 with PID 98279 (/Users/<USER>/work/AI/S
tylishLink/backend/gateway-service/target/classes started by dubin in /Users/<USER>/work/AI/StylishLink/backe
nd/gateway-service)
2025-03-14T18:36:41.201+08:00  INFO 98279 --- [gateway-service] [           main] c.s.gateway.GatewayService
Application    : No active profile set, falling back to 1 default profile: "default"
2025-03-14T18:36:41.518+08:00  INFO 98279 --- [gateway-service] [           main] o.s.c.c.c.ConfigServerConf
igDataLoader   : Fetching config from server at : http://localhost:8888
2025-03-14T18:36:41.523+08:00  INFO 98279 --- [gateway-service] [           main] o.s.c.c.c.ConfigServerConf
igDataLoader   : Exception on Url - http://localhost:8888:org.springframework.web.client.ResourceAccessExcep
tion: I/O error on GET request for "http://localhost:8888/gateway-service/default": Connection refused. Will
 be trying the next url if available
2025-03-14T18:36:41.525+08:00  WARN 98279 --- [gateway-service] [           main] o.s.c.c.c.ConfigServerConf
igDataLoader   : Could not locate PropertySource ([ConfigServerConfigDataResource@23c388c2 uris = array<Stri
ng>['http://localhost:8888'], optional = true, profiles = 'default']): I/O error on GET request for "http://
localhost:8888/gateway-service/default": Connection refused
2025-03-14T18:36:41.525+08:00  INFO 98279 --- [gateway-service] [           main] o.s.c.c.c.ConfigServerConf
igDataLoader   : Fetching config from server at : http://localhost:8888
2025-03-14T18:36:41.525+08:00  INFO 98279 --- [gateway-service] [           main] o.s.c.c.c.ConfigServerConf
igDataLoader   : Exception on Url - http://localhost:8888:org.springframework.web.client.ResourceAccessExcep
tion: I/O error on GET request for "http://localhost:8888/gateway-service/default": Connection refused. Will
 be trying the next url if available
2025-03-14T18:36:41.525+08:00  WARN 98279 --- [gateway-service] [           main] o.s.c.c.c.ConfigServerConf
igDataLoader   : Could not locate PropertySource ([ConfigServerConfigDataResource@6d6bc158 uris = array<Stri
ng>['http://localhost:8888'], optional = true, profiles = 'default']): I/O error on GET request for "http://
localhost:8888/gateway-service/default": Connection refused
2025-03-14T18:36:41.525+08:00  INFO 98279 --- [gateway-service] [           main] o.s.c.c.c.ConfigServerConf
igDataLoader   : Fetching config from server at : http://localhost:8888
2025-03-14T18:36:41.525+08:00  INFO 98279 --- [gateway-service] [           main] o.s.c.c.c.ConfigServerConf
igDataLoader   : Exception on Url - http://localhost:8888:org.springframework.web.client.ResourceAccessExcep
tion: I/O error on GET request for "http://localhost:8888/gateway-service/default": Connection refused. Will
 be trying the next url if available
2025-03-14T18:36:41.525+08:00  WARN 98279 --- [gateway-service] [           main] o.s.c.c.c.ConfigServerConf
igDataLoader   : Could not locate PropertySource ([ConfigServerConfigDataResource@5dda6f9 uris = array<Strin
g>['http://localhost:8888'], optional = true, profiles = 'default']): I/O error on GET request for "http://l
ocalhost:8888/gateway-service/default": Connection refused
2025-03-14T18:36:46.609+08:00  INFO 98279 --- [gateway-service] [           main] .s.d.r.c.RepositoryConfigu
rationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-03-14T18:36:46.612+08:00  INFO 98279 --- [gateway-service] [           main] .s.d.r.c.RepositoryConfigu
rationDelegate : Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
2025-03-14T18:36:46.741+08:00  INFO 98279 --- [gateway-service] [           main] .s.d.r.c.RepositoryConfigu
rationDelegate : Finished Spring Data repository scanning in 108 ms. Found 0 MongoDB repository interfaces.
2025-03-14T18:36:46.772+08:00  INFO 98279 --- [gateway-service] [           main] .s.d.r.c.RepositoryConfigu
rationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-03-14T18:36:46.774+08:00  INFO 98279 --- [gateway-service] [           main] .s.d.r.c.RepositoryConfigu
rationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-03-14T18:36:46.801+08:00  INFO 98279 --- [gateway-service] [           main] .s.d.r.c.RepositoryConfigu
rationDelegate : Finished Spring Data repository scanning in 8 ms. Found 0 Redis repository interfaces.
2025-03-14T18:36:47.539+08:00  INFO 98279 --- [gateway-service] [           main] o.s.cloud.context.scope.Ge
nericScope     : BeanFactory id=479cde31-155c-3aae-8175-e491a12247e6
2025-03-14T18:36:48.277+08:00  WARN 98279 --- [gateway-service] [           main] trationDelegate$BeanPostPr
ocessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$LoadBalan
cerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$L
oadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example:
not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is decl
ared through a non-static factory method on that class; consider declaring it as static instead.
2025-03-14T18:36:48.288+08:00  WARN 98279 --- [gateway-service] [           main] trationDelegate$BeanPostPr
ocessorChecker : Bean 'org.springframework.cloud.loadbalancer.config.BlockingLoadBalancerClientAutoConfigura
tion' of type [org.springframework.cloud.loadbalancer.config.BlockingLoadBalancerClientAutoConfiguration] is
 not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying).
 Is this bean getting eagerly injected into a currently created BeanPostProcessor [lbRestClientPostProcessor
]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-03-14T18:36:48.292+08:00  WARN 98279 --- [gateway-service] [           main] trationDelegate$BeanPostPr
ocessorChecker : Bean 'org.springframework.cloud.loadbalancer.config.LoadBalancerAutoConfiguration' of type
[org.springframework.cloud.loadbalancer.config.LoadBalancerAutoConfiguration] is not eligible for getting pr
ocessed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerl
y injected into a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding B
eanPostProcessor declaration and its dependencies.
2025-03-14T18:36:48.359+08:00  WARN 98279 --- [gateway-service] [           main] trationDelegate$BeanPostPr
ocessorChecker : Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org
.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed
by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly inject
ed into a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostP
rocessor declaration and its dependencies.
2025-03-14T18:36:48.638+08:00  WARN 98279 --- [gateway-service] [           main] trationDelegate$BeanPostPr
ocessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAuto
Configuration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAut
oConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible f
or auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [lbRestC
lientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-03-14T18:36:48.640+08:00  WARN 98279 --- [gateway-service] [           main] trationDelegate$BeanPostPr
ocessorChecker : Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.clien
t.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda/0x000000011b49ccb0] is not eligi
ble for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this b
ean getting eagerly injected into a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check t
he corresponding BeanPostProcessor declaration and its dependencies.
2025-03-14T18:36:48.642+08:00  WARN 98279 --- [gateway-service] [           main] trationDelegate$BeanPostPr
ocessorChecker : Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.Default
sBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligi
ble for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [lb
RestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-03-14T18:36:48.672+08:00  WARN 98279 --- [gateway-service] [           main] trationDelegate$BeanPostPr
ocessorChecker : Bean 'spring.cloud.loadbalancer-org.springframework.cloud.client.loadbalancer.LoadBalancerC
lientsProperties' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerClientsProperties] is n
ot eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). I
s this bean getting eagerly injected into a currently created BeanPostProcessor [lbRestClientPostProcessor]?
 Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-03-14T18:36:48.686+08:00  WARN 98279 --- [gateway-service] [           main] trationDelegate$BeanPostPr
ocessorChecker : Bean 'default.org.springframework.cloud.loadbalancer.config.LoadBalancerAutoConfiguration.L
oadBalancerClientSpecification' of type [org.springframework.cloud.loadbalancer.annotation.LoadBalancerClien
tSpecification] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible f
or auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [lbRestC
lientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-03-14T18:36:48.687+08:00  WARN 98279 --- [gateway-service] [           main] trationDelegate$BeanPostPr
ocessorChecker : Bean 'default.org.springframework.cloud.loadbalancer.config.BlockingLoadBalancerClientAutoC
onfiguration.LoadBalancerClientSpecification' of type [org.springframework.cloud.loadbalancer.annotation.Loa
dBalancerClientSpecification] is not eligible for getting processed by all BeanPostProcessors (for example:
not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProc
essor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencie
s.
2025-03-14T18:36:48.692+08:00  WARN 98279 --- [gateway-service] [           main] trationDelegate$BeanPostPr
ocessorChecker : Bean 'default.org.springframework.cloud.netflix.eureka.loadbalancer.LoadBalancerEurekaAutoC
onfiguration.LoadBalancerClientSpecification' of type [org.springframework.cloud.loadbalancer.annotation.Loa
dBalancerClientSpecification] is not eligible for getting processed by all BeanPostProcessors (for example:
not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProc
essor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencie
s.
2025-03-14T18:36:48.698+08:00  WARN 98279 --- [gateway-service] [           main] trationDelegate$BeanPostPr
ocessorChecker : Bean 'loadBalancerClientFactory' of type [org.springframework.cloud.loadbalancer.support.Lo
adBalancerClientFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not e
ligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor
 [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-03-14T18:36:48.769+08:00  WARN 98279 --- [gateway-service] [           main] trationDelegate$BeanPostPr
ocessorChecker : Bean 'blockingLoadBalancerClient' of type [org.springframework.cloud.loadbalancer.blocking.
client.BlockingLoadBalancerClient] is not eligible for getting processed by all BeanPostProcessors (for exam
ple: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPos
tProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its depend
encies.
2025-03-14T18:36:48.836+08:00  WARN 98279 --- [gateway-service] [           main] trationDelegate$BeanPostPr
ocessorChecker : Bean 'loadBalancerServiceInstanceCookieTransformer' of type [org.springframework.cloud.load
balancer.core.LoadBalancerServiceInstanceCookieTransformer] is not eligible for getting processed by all Bea
nPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a
currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor d
eclaration and its dependencies.
2025-03-14T18:36:48.839+08:00  WARN 98279 --- [gateway-service] [           main] trationDelegate$BeanPostPr
ocessorChecker : Bean 'xForwarderHeadersTransformer' of type [org.springframework.cloud.loadbalancer.blockin
g.XForwardedHeadersTransformer] is not eligible for getting processed by all BeanPostProcessors (for example
: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostPr
ocessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependenc
ies.
2025-03-14T18:36:48.854+08:00  WARN 98279 --- [gateway-service] [           main] trationDelegate$BeanPostPr
ocessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration' of type
[org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration] is not eligible for getting pr
ocessed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerl
y injected into a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding B
eanPostProcessor declaration and its dependencies.
2025-03-14T18:36:48.871+08:00  WARN 98279 --- [gateway-service] [           main] trationDelegate$BeanPostPr
ocessorChecker : Bean 'loadBalancerRequestFactory' of type [org.springframework.cloud.client.loadbalancer.Lo
adBalancerRequestFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not
eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcesso
r [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-03-14T18:36:48.876+08:00  WARN 98279 --- [gateway-service] [           main] trationDelegate$BeanPostPr
ocessorChecker : Bean 'loadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.LoadB
alancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligib
le for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [lbR
estClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-03-14T18:36:48.940+08:00  WARN 98279 --- [gateway-service] [           main] trationDelegate$BeanPostPr
ocessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorA
utoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProces
sorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not elig
ible for auto-proxying). The currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProces
sor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-03-14T18:36:48.942+08:00  WARN 98279 --- [gateway-service] [           main] trationDelegate$BeanPostPr
ocessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorA
utoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbal
ancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is n
ot eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). I
s this bean getting eagerly injected into a currently created BeanPostProcessor [loadBalancerWebClientBuilde
rBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-03-14T18:36:48.963+08:00  WARN 98279 --- [gateway-service] [           main] trationDelegate$BeanPostPr
ocessorChecker : Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.clou
d.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting proc
essed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly
injected into a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check t
he corresponding BeanPostProcessor declaration and its dependencies.
2025-03-14T18:36:50.993+08:00  INFO 98279 --- [gateway-service] [           main] o.s.b.w.embedded.tomcat.To
mcatWebServer  : Tomcat initialized with port 8080 (http)
2025-03-14T18:36:51.036+08:00  INFO 98279 --- [gateway-service] [           main] o.apache.catalina.core.Sta
ndardService   : Starting service [Tomcat]
2025-03-14T18:36:51.036+08:00  INFO 98279 --- [gateway-service] [           main] o.apache.catalina.core.Sta
ndardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.18]
2025-03-14T18:36:51.244+08:00  INFO 98279 --- [gateway-service] [           main] o.a.c.c.C.[Tomcat].[localh
ost].[/]       : Initializing Spring embedded WebApplicationContext
2025-03-14T18:36:51.247+08:00  INFO 98279 --- [gateway-service] [           main] w.s.c.ServletWebServerAppl
icationContext : Root WebApplicationContext: initialization completed in 9701 ms
2025-03-14T18:36:53.320+08:00  WARN 98279 --- [gateway-service] [           main] ConfigServletWebServerAppl
icationContext : Exception encountered during context initialization - cancelling refresh attempt: org.sprin
gframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.gat
eway.config.GatewayClassPathWarningAutoConfiguration$SpringMvcFoundOnClasspathConfiguration': Failed to inst
antiate [org.springframework.cloud.gateway.config.GatewayClassPathWarningAutoConfiguration$SpringMvcFoundOnC
lasspathConfiguration]: Constructor threw exception
2025-03-14T18:36:53.324+08:00  INFO 98279 --- [gateway-service] [           main] o.apache.catalina.core.Sta
ndardService   : Stopping service [Tomcat]
2025-03-14T18:36:53.374+08:00  INFO 98279 --- [gateway-service] [           main] .s.b.a.l.ConditionEvaluati
onReportLogger :

Error starting ApplicationContext. To display the condition evaluation report re-run your application with '
debug' enabled.
2025-03-14T18:36:53.487+08:00 ERROR 98279 --- [gateway-service] [           main] o.s.b.d.LoggingFailureAnal
ysisReporter   :

***************************
APPLICATION FAILED TO START
***************************

Description:

Spring MVC found on classpath, which is incompatible with Spring Cloud Gateway.

Action:

Please set spring.main.web-application-type=reactive or remove spring-boot-starter-web dependency.

[INFO] ------------------------------------------------------------------------
[INFO] BUILD FAILURE
[INFO] ------------------------------------------------------------------------
[INFO] Total time:  24.480 s
[INFO] Finished at: 2025-03-14T18:36:53+08:00
[INFO] ------------------------------------------------------------------------
[ERROR] Failed to execute goal org.springframework.boot:spring-boot-maven-plugin:3.2.2:run (default-cli) on 
project gateway-service: Process terminated with exit code: 1 -> [Help 1]
[ERROR] 
[ERROR] To see the full stack trace of the errors, re-run Maven with the -e switch.
[ERROR] Re-run Maven using the -X switch to enable full debug logging.
[ERROR] 
[ERROR] For more information about the errors and possible solutions, please read the following articles:
[ERROR] [Help 1] http://cwiki.apache.org/confluence/display/MAVEN/MojoExecutionException

```

---

我们遇到了一个新的问题：Spring MVC与Spring Cloud Gateway不兼容。错误信息建议设置`spring.main.web-application-type=reactive`或移除spring-boot-starter-web依赖。让我们进一步修改application.yml：

---

```diff
    main:
      allow-bean-definition-overriding: true
+     web-application-type: reactive
    application:
      name: gateway-service
```

---

我们还需要检查pom.xml文件，看是否包含了spring-boot-starter-web依赖：

---

```bash
grep -r "<artifactId>spring-boot-starter-web</artifactId>" .
```

```
(base) dubin@admindeMBP gateway-service % 

```

---

pom.xml中没有直接包含spring-boot-starter-web依赖，但可能是通过其他依赖引入的。现在我们已经设置了spring.main.web-application-type=reactive，再次运行网关服务：

---

```bash
mvn spring-boot:run
```

```
(base) dubin@admindeMBP gateway-service % mvn spring-boot:run
[INFO] Scanning for projects...
[INFO] 
[INFO] ------------------< com.stylishlink:gateway-service >-------------------
[INFO] Building Gateway Service 1.0.0-SNAPSHOT
[INFO]   from pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] >>> spring-boot:3.2.2:run (default-cli) > test-compile @ gateway-service >>>
[INFO] 
[INFO] --- resources:3.3.1:resources (default-resources) @ gateway-service ---
[INFO] Copying 1 resource from src/main/resources to target/classes
[INFO] 
[INFO] --- compiler:3.13.0:compile (default-compile) @ gateway-service ---
[INFO] Nothing to compile - all classes are up to date.
[INFO] 
[INFO] --- resources:3.3.1:testResources (default-testResources) @ gateway-service ---
[INFO] skip non existing resourceDirectory /Users/<USER>/work/AI/StylishLink/backend/gateway-service/src/test
/resources
[INFO] 
[INFO] --- compiler:3.13.0:testCompile (default-testCompile) @ gateway-service ---
[INFO] No sources to compile
[INFO] 
[INFO] <<< spring-boot:3.2.2:run (default-cli) < test-compile @ gateway-service <<<
[INFO] 
[INFO] 
[INFO] --- spring-boot:3.2.2:run (default-cli) @ gateway-service ---
[INFO] Attaching agents: []

  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/
 :: Spring Boot ::                (v3.2.2)

2025-03-14T18:38:05.887+08:00  INFO 98886 --- [gateway-service] [           main] c.s.gateway.GatewayService
Application    : Starting GatewayServiceApplication using Java 23.0.1 with PID 98886 (/Users/<USER>/work/AI/S
tylishLink/backend/gateway-service/target/classes started by dubin in /Users/<USER>/work/AI/StylishLink/backe
nd/gateway-service)
2025-03-14T18:38:05.891+08:00  INFO 98886 --- [gateway-service] [           main] c.s.gateway.GatewayService
Application    : No active profile set, falling back to 1 default profile: "default"
2025-03-14T18:38:06.653+08:00  INFO 98886 --- [gateway-service] [           main] o.s.c.c.c.ConfigServerConf
igDataLoader   : Fetching config from server at : http://localhost:8888
2025-03-14T18:38:06.654+08:00  INFO 98886 --- [gateway-service] [           main] o.s.c.c.c.ConfigServerConf
igDataLoader   : Exception on Url - http://localhost:8888:org.springframework.web.client.ResourceAccessExcep
tion: I/O error on GET request for "http://localhost:8888/gateway-service/default": Connection refused. Will
 be trying the next url if available
2025-03-14T18:38:06.654+08:00  WARN 98886 --- [gateway-service] [           main] o.s.c.c.c.ConfigServerConf
igDataLoader   : Could not locate PropertySource ([ConfigServerConfigDataResource@f713686 uris = array<Strin
g>['http://localhost:8888'], optional = true, profiles = 'default']): I/O error on GET request for "http://l
ocalhost:8888/gateway-service/default": Connection refused
2025-03-14T18:38:06.654+08:00  INFO 98886 --- [gateway-service] [           main] o.s.c.c.c.ConfigServerConf
igDataLoader   : Fetching config from server at : http://localhost:8888
2025-03-14T18:38:06.654+08:00  INFO 98886 --- [gateway-service] [           main] o.s.c.c.c.ConfigServerConf
igDataLoader   : Exception on Url - http://localhost:8888:org.springframework.web.client.ResourceAccessExcep
tion: I/O error on GET request for "http://localhost:8888/gateway-service/default": Connection refused. Will
 be trying the next url if available
2025-03-14T18:38:06.655+08:00  WARN 98886 --- [gateway-service] [           main] o.s.c.c.c.ConfigServerConf
igDataLoader   : Could not locate PropertySource ([ConfigServerConfigDataResource@10027fc9 uris = array<Stri
ng>['http://localhost:8888'], optional = true, profiles = 'default']): I/O error on GET request for "http://
localhost:8888/gateway-service/default": Connection refused
2025-03-14T18:38:06.655+08:00  INFO 98886 --- [gateway-service] [           main] o.s.c.c.c.ConfigServerConf
igDataLoader   : Fetching config from server at : http://localhost:8888
2025-03-14T18:38:06.655+08:00  INFO 98886 --- [gateway-service] [           main] o.s.c.c.c.ConfigServerConf
igDataLoader   : Exception on Url - http://localhost:8888:org.springframework.web.client.ResourceAccessExcep
tion: I/O error on GET request for "http://localhost:8888/gateway-service/default": Connection refused. Will
 be trying the next url if available
2025-03-14T18:38:06.655+08:00  WARN 98886 --- [gateway-service] [           main] o.s.c.c.c.ConfigServerConf
igDataLoader   : Could not locate PropertySource ([ConfigServerConfigDataResource@54afd745 uris = array<Stri
ng>['http://localhost:8888'], optional = true, profiles = 'default']): I/O error on GET request for "http://
localhost:8888/gateway-service/default": Connection refused
2025-03-14T18:38:23.114+08:00  INFO 98886 --- [gateway-service] [           main] .s.d.r.c.RepositoryConfigu
rationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-03-14T18:38:23.124+08:00  INFO 98886 --- [gateway-service] [           main] .s.d.r.c.RepositoryConfigu
rationDelegate : Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
2025-03-14T18:38:23.333+08:00  INFO 98886 --- [gateway-service] [           main] .s.d.r.c.RepositoryConfigu
rationDelegate : Finished Spring Data repository scanning in 193 ms. Found 0 MongoDB repository interfaces.
2025-03-14T18:38:23.573+08:00  INFO 98886 --- [gateway-service] [           main] .s.d.r.c.RepositoryConfigu
rationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-03-14T18:38:23.576+08:00  INFO 98886 --- [gateway-service] [           main] .s.d.r.c.RepositoryConfigu
rationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-03-14T18:38:23.626+08:00  INFO 98886 --- [gateway-service] [           main] .s.d.r.c.RepositoryConfigu
rationDelegate : Finished Spring Data repository scanning in 14 ms. Found 0 Redis repository interfaces.
2025-03-14T18:38:26.715+08:00  INFO 98886 --- [gateway-service] [           main] o.s.cloud.context.scope.Ge
nericScope     : BeanFactory id=f05edc32-e4e7-3e97-b664-5ecdcd8c1d3a
2025-03-14T18:38:27.537+08:00  WARN 98886 --- [gateway-service] [           main] trationDelegate$BeanPostPr
ocessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$LoadBalan
cerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$L
oadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: 
not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is decl
ared through a non-static factory method on that class; consider declaring it as static instead.
2025-03-14T18:38:27.548+08:00  WARN 98886 --- [gateway-service] [           main] trationDelegate$BeanPostPr
ocessorChecker : Bean 'org.springframework.cloud.loadbalancer.config.BlockingLoadBalancerClientAutoConfigura
tion' of type [org.springframework.cloud.loadbalancer.config.BlockingLoadBalancerClientAutoConfiguration] is
 not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying).
 Is this bean getting eagerly injected into a currently created BeanPostProcessor [lbRestClientPostProcessor
]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-03-14T18:38:27.554+08:00  WARN 98886 --- [gateway-service] [           main] trationDelegate$BeanPostPr
ocessorChecker : Bean 'org.springframework.cloud.loadbalancer.config.LoadBalancerAutoConfiguration' of type 
[org.springframework.cloud.loadbalancer.config.LoadBalancerAutoConfiguration] is not eligible for getting pr
ocessed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerl
y injected into a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding B
eanPostProcessor declaration and its dependencies.
2025-03-14T18:38:27.756+08:00  WARN 98886 --- [gateway-service] [           main] trationDelegate$BeanPostPr
ocessorChecker : Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org
.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed 
by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly inject
ed into a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostP
rocessor declaration and its dependencies.
2025-03-14T18:38:27.858+08:00  WARN 98886 --- [gateway-service] [           main] trationDelegate$BeanPostPr
ocessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAuto
Configuration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAut
oConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible f
or auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [lbRestC
lientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-03-14T18:38:27.859+08:00  WARN 98886 --- [gateway-service] [           main] trationDelegate$BeanPostPr
ocessorChecker : Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.clien
t.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda/0x0000000123453258] is not eligi
ble for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this b
ean getting eagerly injected into a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check t
he corresponding BeanPostProcessor declaration and its dependencies.
2025-03-14T18:38:27.861+08:00  WARN 98886 --- [gateway-service] [           main] trationDelegate$BeanPostPr
ocessorChecker : Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.Default
sBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligi
ble for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [lb
RestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-03-14T18:38:27.905+08:00  WARN 98886 --- [gateway-service] [           main] trationDelegate$BeanPostPr
ocessorChecker : Bean 'spring.cloud.loadbalancer-org.springframework.cloud.client.loadbalancer.LoadBalancerC
lientsProperties' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerClientsProperties] is n
ot eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). I
s this bean getting eagerly injected into a currently created BeanPostProcessor [lbRestClientPostProcessor]?
 Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-03-14T18:38:27.972+08:00  WARN 98886 --- [gateway-service] [           main] trationDelegate$BeanPostPr
ocessorChecker : Bean 'default.org.springframework.cloud.loadbalancer.config.LoadBalancerAutoConfiguration.L
oadBalancerClientSpecification' of type [org.springframework.cloud.loadbalancer.annotation.LoadBalancerClien
tSpecification] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible f
or auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [lbRestC
lientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-03-14T18:38:27.973+08:00  WARN 98886 --- [gateway-service] [           main] trationDelegate$BeanPostPr
ocessorChecker : Bean 'default.org.springframework.cloud.loadbalancer.config.BlockingLoadBalancerClientAutoC
onfiguration.LoadBalancerClientSpecification' of type [org.springframework.cloud.loadbalancer.annotation.Loa
dBalancerClientSpecification] is not eligible for getting processed by all BeanPostProcessors (for example: 
not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProc
essor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencie
s.
2025-03-14T18:38:27.976+08:00  WARN 98886 --- [gateway-service] [           main] trationDelegate$BeanPostPr
ocessorChecker : Bean 'default.org.springframework.cloud.netflix.eureka.loadbalancer.LoadBalancerEurekaAutoC
onfiguration.LoadBalancerClientSpecification' of type [org.springframework.cloud.loadbalancer.annotation.Loa
dBalancerClientSpecification] is not eligible for getting processed by all BeanPostProcessors (for example: 
not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProc
essor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencie
s.
2025-03-14T18:38:27.992+08:00  WARN 98886 --- [gateway-service] [           main] trationDelegate$BeanPostPr
ocessorChecker : Bean 'loadBalancerClientFactory' of type [org.springframework.cloud.loadbalancer.support.Lo
adBalancerClientFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not e
ligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor
 [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-03-14T18:38:28.024+08:00  WARN 98886 --- [gateway-service] [           main] trationDelegate$BeanPostPr
ocessorChecker : Bean 'blockingLoadBalancerClient' of type [org.springframework.cloud.loadbalancer.blocking.
client.BlockingLoadBalancerClient] is not eligible for getting processed by all BeanPostProcessors (for exam
ple: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPos
tProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its depend
encies.
2025-03-14T18:38:28.068+08:00  WARN 98886 --- [gateway-service] [           main] trationDelegate$BeanPostPr
ocessorChecker : Bean 'loadBalancerServiceInstanceCookieTransformer' of type [org.springframework.cloud.load
balancer.core.LoadBalancerServiceInstanceCookieTransformer] is not eligible for getting processed by all Bea
nPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a 
currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor d
eclaration and its dependencies.
2025-03-14T18:38:28.072+08:00  WARN 98886 --- [gateway-service] [           main] trationDelegate$BeanPostPr
ocessorChecker : Bean 'xForwarderHeadersTransformer' of type [org.springframework.cloud.loadbalancer.blockin
g.XForwardedHeadersTransformer] is not eligible for getting processed by all BeanPostProcessors (for example
: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostPr
ocessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependenc
ies.
2025-03-14T18:38:28.076+08:00  WARN 98886 --- [gateway-service] [           main] trationDelegate$BeanPostPr
ocessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration' of type 
[org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration] is not eligible for getting pr
ocessed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerl
y injected into a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding B
eanPostProcessor declaration and its dependencies.
2025-03-14T18:38:28.096+08:00  WARN 98886 --- [gateway-service] [           main] trationDelegate$BeanPostPr
ocessorChecker : Bean 'loadBalancerRequestFactory' of type [org.springframework.cloud.client.loadbalancer.Lo
adBalancerRequestFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not 
eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcesso
r [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-03-14T18:38:28.109+08:00  WARN 98886 --- [gateway-service] [           main] trationDelegate$BeanPostPr
ocessorChecker : Bean 'loadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.LoadB
alancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligib
le for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [lbR
estClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-03-14T18:38:28.114+08:00  WARN 98886 --- [gateway-service] [           main] trationDelegate$BeanPostPr
ocessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorA
utoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProces
sorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not elig
ible for auto-proxying). The currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProces
sor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-03-14T18:38:28.228+08:00  WARN 98886 --- [gateway-service] [           main] trationDelegate$BeanPostPr
ocessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorA
utoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbal
ancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is n
ot eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). I
s this bean getting eagerly injected into a currently created BeanPostProcessor [loadBalancerWebClientBuilde
rBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-03-14T18:38:28.234+08:00  WARN 98886 --- [gateway-service] [           main] trationDelegate$BeanPostPr
ocessorChecker : Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.clou
d.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting proc
essed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly 
injected into a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check t
he corresponding BeanPostProcessor declaration and its dependencies.
2025-03-14T18:38:29.521+08:00  INFO 98886 --- [gateway-service] [           main] o.s.b.w.embedded.tomcat.To
mcatWebServer  : Tomcat initialized with port 8080 (http)
2025-03-14T18:38:29.548+08:00  INFO 98886 --- [gateway-service] [           main] o.apache.catalina.core.Sta
ndardService   : Starting service [Tomcat]
2025-03-14T18:38:29.548+08:00  INFO 98886 --- [gateway-service] [           main] o.apache.catalina.core.Sta
ndardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.18]
2025-03-14T18:38:31.190+08:00  INFO 98886 --- [gateway-service] [           main] ctiveUserDetailsServiceAut
oConfiguration : 

Using generated security password: 3e87b100-c03f-479a-bb69-ec5ab070ef3a

2025-03-14T18:38:40.661+08:00  INFO 98886 --- [gateway-service] [           main] o.s.c.g.r.RouteDefinitionR
outeLocator    : Loaded RoutePredicateFactory [After]
2025-03-14T18:38:40.662+08:00  INFO 98886 --- [gateway-service] [           main] o.s.c.g.r.RouteDefinitionR
outeLocator    : Loaded RoutePredicateFactory [Before]
2025-03-14T18:38:40.662+08:00  INFO 98886 --- [gateway-service] [           main] o.s.c.g.r.RouteDefinitionR
outeLocator    : Loaded RoutePredicateFactory [Between]
2025-03-14T18:38:40.662+08:00  INFO 98886 --- [gateway-service] [           main] o.s.c.g.r.RouteDefinitionR
outeLocator    : Loaded RoutePredicateFactory [Cookie]
2025-03-14T18:38:40.662+08:00  INFO 98886 --- [gateway-service] [           main] o.s.c.g.r.RouteDefinitionR
outeLocator    : Loaded RoutePredicateFactory [Header]
2025-03-14T18:38:40.662+08:00  INFO 98886 --- [gateway-service] [           main] o.s.c.g.r.RouteDefinitionR
outeLocator    : Loaded RoutePredicateFactory [Host]
2025-03-14T18:38:40.662+08:00  INFO 98886 --- [gateway-service] [           main] o.s.c.g.r.RouteDefinitionR
outeLocator    : Loaded RoutePredicateFactory [Method]
2025-03-14T18:38:40.662+08:00  INFO 98886 --- [gateway-service] [           main] o.s.c.g.r.RouteDefinitionR
outeLocator    : Loaded RoutePredicateFactory [Path]
2025-03-14T18:38:40.662+08:00  INFO 98886 --- [gateway-service] [           main] o.s.c.g.r.RouteDefinitionR
outeLocator    : Loaded RoutePredicateFactory [Query]
2025-03-14T18:38:40.662+08:00  INFO 98886 --- [gateway-service] [           main] o.s.c.g.r.RouteDefinitionR
outeLocator    : Loaded RoutePredicateFactory [ReadBody]
2025-03-14T18:38:40.662+08:00  INFO 98886 --- [gateway-service] [           main] o.s.c.g.r.RouteDefinitionR
outeLocator    : Loaded RoutePredicateFactory [RemoteAddr]
2025-03-14T18:38:40.663+08:00  INFO 98886 --- [gateway-service] [           main] o.s.c.g.r.RouteDefinitionR
outeLocator    : Loaded RoutePredicateFactory [XForwardedRemoteAddr]
2025-03-14T18:38:40.663+08:00  INFO 98886 --- [gateway-service] [           main] o.s.c.g.r.RouteDefinitionR
outeLocator    : Loaded RoutePredicateFactory [Weight]
2025-03-14T18:38:40.663+08:00  INFO 98886 --- [gateway-service] [           main] o.s.c.g.r.RouteDefinitionR
outeLocator    : Loaded RoutePredicateFactory [CloudFoundryRouteService]
2025-03-14T18:38:41.284+08:00  INFO 98886 --- [gateway-service] [localhost:27017] org.mongodb.driver.cluster
               : Monitor thread successfully connected to server with description ServerDescription{address=
localhost:27017, type=STANDALONE, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=17, maxDocument
Size=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=41336975}
2025-03-14T18:38:41.285+08:00  INFO 98886 --- [gateway-service] [           main] org.mongodb.driver.client 
               : MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync|spring-boot", "versio
n": "4.11.1"}, "os": {"type": "Darwin", "name": "Mac OS X", "architecture": "x86_64", "version": "13.7.4"}, 
"platform": "Java/Homebrew/23.0.1"} created with settings MongoClientSettings{readPreference=primary, writeC
oncern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=
ReadConcern{level=null}, credential=null, transportSettings=null, streamFactoryFactory=null, commandListener
s=[io.micrometer.core.instrument.binder.mongodb.MongoMetricsCommandListener@4a3383dc], codecRegistry=Provide
rsCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObje
ctCodecProvider{}, DocumentCodecProvider{}, CollectionCodecProvider{}, IterableCodecProvider{}, MapCodecProv
ider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{},
 BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.client.model.mql.ExpressionCodecProvider@72ce8a9b, co
m.mongodb.Jep395RecordCodecProvider@47c15468, com.mongodb.KotlinCodecProvider@9fc2f89]}, loggerSettings=Logg
erSettings{maxDocumentLength=1000}, clusterSettings={hosts=[localhost:27017], srvServiceName=mongodb, mode=S
INGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='
[]', serverSelectionTimeout='30000 ms', localThreshold='15 ms'}, socketSettings=SocketSettings{connectTimeou
tMS=10000, readTimeoutMS=0, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=
null, password=null}}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, r
eceiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, connec
tionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeM
S=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolLis
teners=[io.micrometer.core.instrument.binder.mongodb.MongoMetricsConnectionPoolListener@5f877009], maxConnec
ting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverListen
ers='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false,
 context=null}, applicationName='null', compressorList=[], uuidRepresentation=JAVA_LEGACY, serverApi=null, a
utoEncryptionSettings=null, dnsClient=null, inetAddressResolver=null, contextProvider=null}
2025-03-14T18:38:41.906+08:00  INFO 98886 --- [gateway-service] [           main] o.s.b.a.e.web.EndpointLink
sResolver      : Exposing 3 endpoint(s) beneath base path '/actuator'
2025-03-14T18:38:42.560+08:00  INFO 98886 --- [gateway-service] [           main] DiscoveryClientOptionalArg
sConfiguration : Eureka HTTP Client uses RestTemplate.
2025-03-14T18:38:42.593+08:00  WARN 98886 --- [gateway-service] [           main] iguration$LoadBalancerCaff
eineWarnLogger : Spring Cloud LoadBalancer is currently working with the default cache. While this cache imp
lementation is useful for development and tests, it's recommended to use Caffeine cache in production.You ca
n switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager t
o the classpath.
2025-03-14T18:38:42.658+08:00  INFO 98886 --- [gateway-service] [           main] o.s.c.n.eureka.InstanceInf
oFactory       : Setting initial instance status as: STARTING
2025-03-14T18:38:42.800+08:00  INFO 98886 --- [gateway-service] [           main] com.netflix.discovery.Disc
overyClient    : Initializing Eureka in region us-east-1
2025-03-14T18:38:42.810+08:00  INFO 98886 --- [gateway-service] [           main] c.n.d.s.r.aws.ConfigCluste
rResolver      : Resolving eureka endpoints via configuration
2025-03-14T18:38:42.851+08:00  INFO 98886 --- [gateway-service] [           main] com.netflix.discovery.Disc
overyClient    : Disable delta property : false
2025-03-14T18:38:42.852+08:00  INFO 98886 --- [gateway-service] [           main] com.netflix.discovery.Disc
overyClient    : Single vip registry refresh property : null
2025-03-14T18:38:42.852+08:00  INFO 98886 --- [gateway-service] [           main] com.netflix.discovery.Disc
overyClient    : Force full registry fetch : false
2025-03-14T18:38:42.852+08:00  INFO 98886 --- [gateway-service] [           main] com.netflix.discovery.Disc
overyClient    : Application is null : false
2025-03-14T18:38:42.852+08:00  INFO 98886 --- [gateway-service] [           main] com.netflix.discovery.Disc
overyClient    : Registered Applications size is zero : true
2025-03-14T18:38:42.853+08:00  INFO 98886 --- [gateway-service] [           main] com.netflix.discovery.Disc
overyClient    : Application version is -1: true
2025-03-14T18:38:42.853+08:00  INFO 98886 --- [gateway-service] [           main] com.netflix.discovery.Disc
overyClient    : Getting all instance registry info from the eureka server
2025-03-14T18:38:43.391+08:00  INFO 98886 --- [gateway-service] [           main] com.netflix.discovery.Disc
overyClient    : The response status is 200
2025-03-14T18:38:43.394+08:00  INFO 98886 --- [gateway-service] [           main] com.netflix.discovery.Disc
overyClient    : Starting heartbeat executor: renew interval is: 30
2025-03-14T18:38:43.397+08:00  INFO 98886 --- [gateway-service] [           main] c.n.discovery.InstanceInfo
Replicator     : InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-03-14T18:38:43.404+08:00  INFO 98886 --- [gateway-service] [           main] com.netflix.discovery.Disc
overyClient    : Discovery Client initialized at timestamp 1741948723404 with initial instances count: 3
2025-03-14T18:38:43.407+08:00  INFO 98886 --- [gateway-service] [           main] o.s.c.n.e.s.EurekaServiceR
egistry        : Registering application GATEWAY-SERVICE with eureka with status UP
2025-03-14T18:38:43.409+08:00  INFO 98886 --- [gateway-service] [           main] com.netflix.discovery.Disc
overyClient    : Saw local status change event StatusChangeEvent [timestamp=1741948723409, current=UP, previ
ous=STARTING]
2025-03-14T18:38:43.412+08:00  INFO 98886 --- [gateway-service] [nfoReplicator-0] com.netflix.discovery.Disc
overyClient    : DiscoveryClient_GATEWAY-SERVICE/gateway-service:8080: registering service...
2025-03-14T18:38:43.522+08:00  INFO 98886 --- [gateway-service] [nfoReplicator-0] com.netflix.discovery.Disc
overyClient    : DiscoveryClient_GATEWAY-SERVICE/gateway-service:8080 - registration status: 204
2025-03-14T18:38:43.755+08:00  INFO 98886 --- [gateway-service] [           main] o.s.b.w.embedded.tomcat.To
mcatWebServer  : Tomcat started on port 8080 (http) with context path ''
2025-03-14T18:38:43.757+08:00  INFO 98886 --- [gateway-service] [           main] .s.c.n.e.s.EurekaAutoServi
ceRegistration : Updating port to 8080
2025-03-14T18:38:43.868+08:00  INFO 98886 --- [gateway-service] [           main] c.s.gateway.GatewayService
Application    : Started GatewayServiceApplication in 45.802 seconds (process running for 48.711)
```

---

好的，我们现在已经启动了网关服务，等待它加载。让我们等待一段时间，然后检查网关服务的状态。

---

```bash
ps -ef | grep java
```

```
ps -ef | grep java
(base) dubin@admindeMBP StylishLink % ps -ef | grep java
  502  1806  1284   0  1:52下午 ??         9:19.80 /Users/<USER>/.cursor/extensions/redhat.java-1.40.0-darwin
-x64/jre/21.0.6-macosx-x86_64/bin/java --add-modules=ALL-SYSTEM --add-opens java.base/java.util=ALL-UNNAMED 
--add-opens java.base/java.lang=ALL-UNNAMED --add-opens java.base/sun.nio.fs=ALL-UNNAMED -Declipse.applicati
on=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.
product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Df
ile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemo
ryMapping=true -Xmx1G -Xms100m -Xlog:disable -javaagent:/Users/<USER>/.cursor/extensions/redhat.java-1.40.0-d
arwin-x64/lombok/lombok-1.18.36.jar -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/Users/<USER>/Library/Ap
plication Support/Cursor/User/workspaceStorage/96040186e206a88a53b2716bf232a5f3/redhat.java -Daether.depende
ncyCollector.impl=bf -jar /Users/<USER>/.cursor/extensions/redhat.java-1.40.0-darwin-x64/server/plugins/org.e
clipse.equinox.launcher_1.6.1000.v20250131-0606.jar -configuration /Users/<USER>/Library/Application Support/
Cursor/User/globalStorage/redhat.java/1.40.0/config_mac -data /Users/<USER>/Library/Application Support/Curso
r/User/workspaceStorage/96040186e206a88a53b2716bf232a5f3/redhat.java/jdt_ws --pipe=/private/var/folders/wk/c
16v9m6s4hlgh0647xfyvn_r0000gp/T/lsp-1d79c7025612c80d159a5b81da64db0a.sock
  502 20075     1   0  2:57下午 ??        14:10.04 /Users/<USER>/Library/Java/JavaVirtualMachines/corretto-17
.0.13/Contents/Home/bin/java --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL
-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAM
ED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.j
avac.util=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-
UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED -Xmx2048m -Dfile.encoding=UTF-8 -Duser
.country=CN -Duser.language=zh -Duser.variant -cp /Users/<USER>/.gradle/wrapper/dists/gradle-8.10.2-bin/e0thj
r3we83usdufs66z371ne/gradle-8.10.2/lib/gradle-daemon-main-8.10.2.jar -javaagent:/Users/<USER>/.gradle/wrapper
/dists/gradle-8.10.2-bin/e0thjr3we83usdufs66z371ne/gradle-8.10.2/lib/agents/gradle-instrumentation-agent-8.1
0.2.jar org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.10.2
  502 88746 20075   0  6:14下午 ??         1:54.22 /Users/<USER>/Library/Java/JavaVirtualMachines/corretto-17
.0.13/Contents/Home/bin/java -cp /Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlin/kotlin
-compiler-embeddable/1.9.25/6e698f23ca1f74d6672827f61e92c2fe015d1b88/kotlin-compiler-embeddable-1.9.25.jar:/
Users/dubin/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlin/kotlin-stdlib/1.9.25/f700a2f2b8f0d6d0fde
48f56d894dc722fb029d7/kotlin-stdlib-1.9.25.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains
.kotlin/kotlin-script-runtime/1.9.25/43cdc99e4582c67c2b60f609d0e4c015d33a8e89/kotlin-script-runtime-1.9.25.j
ar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlin/kotlin-reflect/1.6.10/1cbe9c92c12a94
eea200d23c2bbaedaf3daf5132/kotlin-reflect-1.6.10.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jet
brains.kotlin/kotlin-daemon-embeddable/1.9.25/3a1ebb4f98ed480e4774f45e3be215626eb3ae02/kotlin-daemon-embedda
ble-1.9.25.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.intellij.deps/trove4j/1.0.20200
330/3afb14d5f9ceb459d724e907a21145e8ff394f02/trove4j-1.0.20200330.jar:/Users/<USER>/.gradle/caches/modules-2/
files-2.1/org.jetbrains/annotations/13.0/919f0dfe192fb4e063e7dacadee7f8bb9a2672a9/annotations-13.0.jar -Djav
a.awt.headless=true -D$java.rmi.server.hostname=127.0.0.1 -Xmx2048m -Dkotlin.environment.keepalive -ea --add
-exports java.base/sun.nio.ch=ALL-UNNAMED org.jetbrains.kotlin.daemon.KotlinCompileDaemon --daemon-runFilesP
ath /Users/<USER>/Library/Application Support/kotlin/daemon --daemon-autoshutdownIdleSeconds=7200 --daemon-co
mpilerClasspath /Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlin/kotlin-compiler-embedda
ble/1.9.25/6e698f23ca1f74d6672827f61e92c2fe015d1b88/kotlin-compiler-embeddable-1.9.25.jar:/Users/<USER>/.grad
le/caches/modules-2/files-2.1/org.jetbrains.kotlin/kotlin-stdlib/1.9.25/f700a2f2b8f0d6d0fde48f56d894dc722fb0
29d7/kotlin-stdlib-1.9.25.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlin/kotlin-sc
ript-runtime/1.9.25/43cdc99e4582c67c2b60f609d0e4c015d33a8e89/kotlin-script-runtime-1.9.25.jar:/Users/<USER>/.
gradle/caches/modules-2/files-2.1/org.jetbrains.kotlin/kotlin-reflect/1.6.10/1cbe9c92c12a94eea200d23c2bbaeda
f3daf5132/kotlin-reflect-1.6.10.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlin/kot
lin-daemon-embeddable/1.9.25/3a1ebb4f98ed480e4774f45e3be215626eb3ae02/kotlin-daemon-embeddable-1.9.25.jar:/U
sers/dubin/.gradle/caches/modules-2/files-2.1/org.jetbrains.intellij.deps/trove4j/1.0.20200330/3afb14d5f9ceb
459d724e907a21145e8ff394f02/trove4j-1.0.20200330.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jet
brains/annotations/13.0/919f0dfe192fb4e063e7dacadee7f8bb9a2672a9/annotations-13.0.jar
  502 82565 76360   0  6:01下午 ttys014    0:13.25 /usr/local/opt/openjdk/libexec/openjdk.jdk/Contents/Home/
bin/java -classpath /usr/local/Cellar/maven/3.9.9/libexec/boot/plexus-classworlds-2.8.0.jar -Dclassworlds.co
nf=/usr/local/Cellar/maven/3.9.9/libexec/bin/m2.conf -Dmaven.home=/usr/local/Cellar/maven/3.9.9/libexec -Dli
brary.jansi.path=/usr/local/Cellar/maven/3.9.9/libexec/lib/jansi-native -Dmaven.multiModuleProjectDirectory=
/Users/<USER>/work/AI/StylishLink/backend/wardrobe-service org.codehaus.plexus.classworlds.launcher.Launcher 
spring-boot:run
  502 82701 82565   0  6:01下午 ttys014    0:21.73 /usr/local/Cellar/openjdk/23.0.1/libexec/openjdk.jdk/Cont
ents/Home/bin/java -XX:TieredStopAtLevel=1 -cp /Users/<USER>/work/AI/StylishLink/backend/wardrobe-service/tar
get/classes:/Users/<USER>/.m2/repository/com/stylishlink/stylishlink-common/1.0.0-SNAPSHOT/stylishlink-common
-1.0.0-SNAPSHOT.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-collections4/4.4/commons-collecti
ons4-4.4.jar:/Users/<USER>/.m2/repository/com/google/guava/guava/32.1.3-jre/guava-32.1.3-jre.jar:/Users/<USER>
/.m2/repository/com/google/guava/failureaccess/1.0.1/failureaccess-1.0.1.jar:/Users/<USER>/.m2/repository/com
/google/guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/listenablefuture-9999.0-empty-to-av
oid-conflict-with-guava.jar:/Users/<USER>/.m2/repository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.j
ar:/Users/<USER>/.m2/repository/org/checkerframework/checker-qual/3.37.0/checker-qual-3.37.0.jar:/Users/<USER>
/.m2/repository/com/google/errorprone/error_prone_annotations/2.21.1/error_prone_annotations-2.21.1.jar:/Use
rs/dubin/.m2/repository/com/google/j2objc/j2objc-annotations/2.8/j2objc-annotations-2.8.jar:/Users/<USER>/.m2
/repository/cn/hutool/hutool-all/5.8.25/hutool-all-5.8.25.jar:/Users/<USER>/.m2/repository/org/mapstruct/maps
truct/1.5.5.Final/mapstruct-1.5.5.Final.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-api/0.12.5/jjwt
-api-0.12.5.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-impl/0.12.5/jjwt-impl-0.12.5.jar:/Users/<USER>
in/.m2/repository/io/jsonwebtoken/jjwt-jackson/0.12.5/jjwt-jackson-0.12.5.jar:/Users/<USER>/.m2/repository/or
g/springframework/boot/spring-boot/3.2.2/spring-boot-3.2.2.jar:/Users/<USER>/.m2/repository/org/springframewo
rk/boot/spring-boot-autoconfigure/3.2.2/spring-boot-autoconfigure-3.2.2.jar:/Users/<USER>/.m2/repository/ch/q
os/logback/logback-classic/1.4.14/logback-classic-1.4.14.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logb
ack-core/1.4.14/logback-core-1.4.14.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/
2.21.1/log4j-to-slf4j-2.21.1.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.21.1/log4j
-api-2.21.1.jar:/Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/2.0.11/jul-to-slf4j-2.0.11.jar:/Users/<USER>
in/.m2/repository/jakarta/annotation/jakarta.annotation-api/2.1.1/jakarta.annotation-api-2.1.1.jar:/Users/<USER>
bin/.m2/repository/org/yaml/snakeyaml/2.2/snakeyaml-2.2.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackso
n/datatype/jackson-datatype-jdk8/2.15.3/jackson-datatype-jdk8-2.15.3.jar:/Users/<USER>/.m2/repository/com/fas
terxml/jackson/datatype/jackson-datatype-jsr310/2.15.3/jackson-datatype-jsr310-2.15.3.jar:/Users/<USER>/.m2/r
epository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.15.3/jackson-module-parameter-names-
2.15.3.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/10.1.18/tomcat-embed-core-1
0.1.18.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/10.1.18/tomcat-embed-w
ebsocket-10.1.18.jar:/Users/<USER>/.m2/repository/org/springframework/spring-web/6.1.3/spring-web-6.1.3.jar:/
Users/dubin/.m2/repository/org/springframework/spring-beans/6.1.3/spring-beans-6.1.3.jar:/Users/<USER>/.m2/re
pository/org/springframework/spring-webmvc/6.1.3/spring-webmvc-6.1.3.jar:/Users/<USER>/.m2/repository/org/spr
ingframework/spring-aop/6.1.3/spring-aop-6.1.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-co
ntext/6.1.3/spring-context-6.1.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-expression/6.1.3
/spring-expression-6.1.3.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/10.1.18/tom
cat-embed-el-10.1.18.jar:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/8.0.1.Final
/hibernate-validator-8.0.1.Final.jar:/Users/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/3
.0.2/jakarta.validation-api-3.0.2.jar:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.5.3.Fina
l/jboss-logging-3.5.3.Final.jar:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.6.0/classmate-1.6.0.ja
r:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator-autoconfigure/3.2.2/spring-boot-
actuator-autoconfigure-3.2.2.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator/3
.2.2/spring-boot-actuator-3.2.2.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-observation/1.12.2/
micrometer-observation-1.12.2.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-commons/1.12.2/microm
eter-commons-1.12.2.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-jakarta9/1.12.2/micrometer-jaka
rta9-1.12.2.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-core/1.12.2/micrometer-core-1.12.2.jar:
/Users/<USER>/.m2/repository/org/hdrhistogram/HdrHistogram/2.1.12/HdrHistogram-2.1.12.jar:/Users/<USER>/.m2/re
pository/org/latencyutils/LatencyUtils/2.0.3/LatencyUtils-2.0.3.jar:/Users/<USER>/.m2/repository/org/springfr
amework/cloud/spring-cloud-starter-netflix-eureka-client/4.1.0/spring-cloud-starter-netflix-eureka-client-4.
1.0.jar:/Users/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-starter/4.1.0/spring-cloud-starte
r-4.1.0.jar:/Users/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-context/4.1.0/spring-cloud-co
ntext-4.1.0.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-rsa/1.1.1/spring-se
curity-rsa-1.1.1.jar:/Users/<USER>/.m2/repository/org/bouncycastle/bcprov-jdk18on/1.74/bcprov-jdk18on-1.74.ja
r:/Users/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-netflix-eureka-client/4.1.0/spring-clou
d-netflix-eureka-client-4.1.0.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/client5/httpclient5/
5.2.3/httpclient5-5.2.3.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/core5/httpcore5/5.2.4/http
core5-5.2.4.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/core5/httpcore5-h2/5.2.4/httpcore5-h2-
5.2.4.jar:/Users/<USER>/.m2/repository/com/netflix/eureka/eureka-client/2.0.1/eureka-client-2.0.1.jar:/Users/
dubin/.m2/repository/com/netflix/netflix-commons/netflix-eventbus/0.3.0/netflix-eventbus-0.3.0.jar:/Users/<USER>
bin/.m2/repository/com/netflix/netflix-commons/netflix-infix/0.3.0/netflix-infix-0.3.0.jar:/Users/<USER>/.m2/
repository/commons-jxpath/commons-jxpath/1.3/commons-jxpath-1.3.jar:/Users/<USER>/.m2/repository/joda-time/jo
da-time/2.3/joda-time-2.3.jar:/Users/<USER>/.m2/repository/org/antlr/antlr-runtime/3.4/antlr-runtime-3.4.jar:
/Users/<USER>/.m2/repository/org/antlr/stringtemplate/3.2.1/stringtemplate-3.2.1.jar:/Users/<USER>/.m2/reposit
ory/antlr/antlr/2.7.7/antlr-2.7.7.jar:/Users/<USER>/.m2/repository/com/google/code/gson/gson/2.10.1/gson-2.10
.1.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-math/2.2/commons-math-2.2.jar:/Users/<USER>/.m2
/repository/com/thoughtworks/xstream/xstream/1.4.19/xstream-1.4.19.jar:/Users/<USER>/.m2/repository/io/github
/x-stream/mxparser/1.2.2/mxparser-1.2.2.jar:/Users/<USER>/.m2/repository/xmlpull/xmlpull/1.1.3.1/xmlpull-1.1.
3.1.jar:/Users/<USER>/.m2/repository/jakarta/ws/rs/jakarta.ws.rs-api/3.1.0/jakarta.ws.rs-api-3.1.0.jar:/Users
/dubin/.m2/repository/jakarta/inject/jakarta.inject-api/2.0.1/jakarta.inject-api-2.0.1.jar:/Users/<USER>/.m2/
repository/com/netflix/servo/servo-core/0.12.21/servo-core-0.12.21.jar:/Users/<USER>/.m2/repository/org/apach
e/httpcomponents/httpclient/4.5.3/httpclient-4.5.3.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents
/httpcore/4.4.16/httpcore-4.4.16.jar:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.16.0/commons-
codec-1.16.0.jar:/Users/<USER>/.m2/repository/commons-configuration/commons-configuration/1.10/commons-config
uration-1.10.jar:/Users/<USER>/.m2/repository/commons-lang/commons-lang/2.6/commons-lang-2.6.jar:/Users/<USER>
/.m2/repository/org/codehaus/jettison/jettison/1.4.0/jettison-1.4.0.jar:/Users/<USER>/.m2/repository/com/netf
lix/eureka/eureka-core/2.0.1/eureka-core-2.0.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/woodstox/woodst
ox-core/6.2.1/woodstox-core-6.2.1.jar:/Users/<USER>/.m2/repository/org/codehaus/woodstox/stax2-api/4.2.1/stax
2-api-4.2.1.jar:/Users/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-starter-loadbalancer/4.1.
0/spring-cloud-starter-loadbalancer-4.1.0.jar:/Users/<USER>/.m2/repository/org/springframework/cloud/spring-c
loud-loadbalancer/4.1.0/spring-cloud-loadbalancer-4.1.0.jar:/Users/<USER>/.m2/repository/io/projectreactor/ad
dons/reactor-extra/3.5.1/reactor-extra-3.5.1.jar:/Users/<USER>/.m2/repository/com/stoyanr/evictor/1.0.0/evict
or-1.0.0.jar:/Users/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-starter-openfeign/4.1.0/spri
ng-cloud-starter-openfeign-4.1.0.jar:/Users/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-open
feign-core/4.1.0/spring-cloud-openfeign-core-4.1.0.jar:/Users/<USER>/.m2/repository/org/aspectj/aspectjweaver
/1.9.21/aspectjweaver-1.9.21.jar:/Users/<USER>/.m2/repository/io/github/openfeign/form/feign-form-spring/3.8.
0/feign-form-spring-3.8.0.jar:/Users/<USER>/.m2/repository/io/github/openfeign/form/feign-form/3.8.0/feign-fo
rm-3.8.0.jar:/Users/<USER>/.m2/repository/commons-fileupload/commons-fileupload/1.5/commons-fileupload-1.5.ja
r:/Users/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-commons/4.1.0/spring-cloud-commons-4.1.
0.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-crypto/6.2.1/spring-security-
crypto-6.2.1.jar:/Users/<USER>/.m2/repository/io/github/openfeign/feign-core/13.1/feign-core-13.1.jar:/Users/
dubin/.m2/repository/io/github/openfeign/feign-slf4j/13.1/feign-slf4j-13.1.jar:/Users/<USER>/.m2/repository/o
rg/springframework/cloud/spring-cloud-starter-circuitbreaker-resilience4j/3.1.0/spring-cloud-starter-circuit
breaker-resilience4j-3.1.0.jar:/Users/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-circuitbre
aker-resilience4j/3.1.0/spring-cloud-circuitbreaker-resilience4j-3.1.0.jar:/Users/<USER>/.m2/repository/io/gi
thub/resilience4j/resilience4j-spring-boot3/2.1.0/resilience4j-spring-boot3-2.1.0.jar:/Users/<USER>/.m2/repos
itory/io/github/resilience4j/resilience4j-spring6/2.1.0/resilience4j-spring6-2.1.0.jar:/Users/<USER>/.m2/repo
sitory/io/github/resilience4j/resilience4j-annotations/2.1.0/resilience4j-annotations-2.1.0.jar:/Users/<USER>
/.m2/repository/io/github/resilience4j/resilience4j-consumer/2.1.0/resilience4j-consumer-2.1.0.jar:/Users/<USER>
bin/.m2/repository/io/github/resilience4j/resilience4j-circularbuffer/2.1.0/resilience4j-circularbuffer-2.1.
0.jar:/Users/<USER>/.m2/repository/io/github/resilience4j/resilience4j-framework-common/2.1.0/resilience4j-fr
amework-common-2.1.0.jar:/Users/<USER>/.m2/repository/io/github/resilience4j/resilience4j-micrometer/2.1.0/re
silience4j-micrometer-2.1.0.jar:/Users/<USER>/.m2/repository/io/github/resilience4j/resilience4j-retry/2.1.0/
resilience4j-retry-2.1.0.jar:/Users/<USER>/.m2/repository/io/github/resilience4j/resilience4j-ratelimiter/2.1
.0/resilience4j-ratelimiter-2.1.0.jar:/Users/<USER>/.m2/repository/io/github/resilience4j/resilience4j-circui
tbreaker/2.1.0/resilience4j-circuitbreaker-2.1.0.jar:/Users/<USER>/.m2/repository/io/github/resilience4j/resi
lience4j-core/2.1.0/resilience4j-core-2.1.0.jar:/Users/<USER>/.m2/repository/io/github/resilience4j/resilienc
e4j-timelimiter/2.1.0/resilience4j-timelimiter-2.1.0.jar:/Users/<USER>/.m2/repository/org/mongodb/mongodb-dri
ver-sync/4.11.1/mongodb-driver-sync-4.11.1.jar:/Users/<USER>/.m2/repository/org/mongodb/bson/4.11.1/bson-4.11
.1.jar:/Users/<USER>/.m2/repository/org/mongodb/mongodb-driver-core/4.11.1/mongodb-driver-core-4.11.1.jar:/Us
ers/dubin/.m2/repository/org/mongodb/bson-record-codec/4.11.1/bson-record-codec-4.11.1.jar:/Users/<USER>/.m2/
repository/org/springframework/data/spring-data-mongodb/4.2.2/spring-data-mongodb-4.2.2.jar:/Users/<USER>/.m2
/repository/org/springframework/spring-tx/6.1.3/spring-tx-6.1.3.jar:/Users/<USER>/.m2/repository/org/springfr
amework/data/spring-data-commons/3.2.2/spring-data-commons-3.2.2.jar:/Users/<USER>/.m2/repository/io/lettuce/
lettuce-core/6.3.1.RELEASE/lettuce-core-6.3.1.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/da
ta/spring-data-redis/3.2.2/spring-data-redis-3.2.2.jar:/Users/<USER>/.m2/repository/org/springframework/data/
spring-data-keyvalue/3.2.2/spring-data-keyvalue-3.2.2.jar:/Users/<USER>/.m2/repository/org/springframework/sp
ring-oxm/6.1.3/spring-oxm-6.1.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context-support/6
.1.3/spring-context-support-6.1.3.jar:/Users/<USER>/.m2/repository/org/redisson/redisson/3.26.0/redisson-3.26
.0.jar:/Users/<USER>/.m2/repository/io/netty/netty-common/4.1.105.Final/netty-common-4.1.105.Final.jar:/Users
/dubin/.m2/repository/io/netty/netty-codec/4.1.105.Final/netty-codec-4.1.105.Final.jar:/Users/<USER>/.m2/repo
sitory/io/netty/netty-buffer/4.1.105.Final/netty-buffer-4.1.105.Final.jar:/Users/<USER>/.m2/repository/io/net
ty/netty-transport/4.1.105.Final/netty-transport-4.1.105.Final.jar:/Users/<USER>/.m2/repository/io/netty/nett
y-resolver/4.1.105.Final/netty-resolver-4.1.105.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolve
r-dns/4.1.105.Final/netty-resolver-dns-4.1.105.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-dn
s/4.1.105.Final/netty-codec-dns-4.1.105.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-handler/4.1.105
.Final/netty-handler-4.1.105.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-native-unix-comm
on/4.1.105.Final/netty-transport-native-unix-common-4.1.105.Final.jar:/Users/<USER>/.m2/repository/javax/cach
e/cache-api/1.1.1/cache-api-1.1.1.jar:/Users/<USER>/.m2/repository/io/projectreactor/reactor-core/3.6.2/react
or-core-3.6.2.jar:/Users/<USER>/.m2/repository/org/reactivestreams/reactive-streams/1.0.4/reactive-streams-1.
0.4.jar:/Users/<USER>/.m2/repository/io/reactivex/rxjava3/rxjava/3.1.8/rxjava-3.1.8.jar:/Users/<USER>/.m2/repo
sitory/org/jboss/marshalling/jboss-marshalling/2.0.11.Final/jboss-marshalling-2.0.11.Final.jar:/Users/<USER>/
.m2/repository/org/jboss/marshalling/jboss-marshalling-river/2.0.11.Final/jboss-marshalling-river-2.0.11.Fin
al.jar:/Users/<USER>/.m2/repository/com/esotericsoftware/kryo/5.6.0/kryo-5.6.0.jar:/Users/<USER>/.m2/repositor
y/com/esotericsoftware/reflectasm/1.11.9/reflectasm-1.11.9.jar:/Users/<USER>/.m2/repository/org/objenesis/obj
enesis/3.3/objenesis-3.3.jar:/Users/<USER>/.m2/repository/com/esotericsoftware/minlog/1.3.1/minlog-1.3.1.jar:
/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/2.0.11/slf4j-api-2.0.11.jar:/Users/<USER>/.m2/repository/com/
fasterxml/jackson/core/jackson-annotations/2.15.3/jackson-annotations-2.15.3.jar:/Users/<USER>/.m2/repository
/com/fasterxml/jackson/dataformat/jackson-dataformat-yaml/2.15.3/jackson-dataformat-yaml-2.15.3.jar:/Users/<USER>
ubin/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.15.3/jackson-core-2.15.3.jar:/Users/<USER>/.m2/
repository/com/fasterxml/jackson/core/jackson-databind/2.15.3/jackson-databind-2.15.3.jar:/Users/<USER>/.m2/r
epository/net/bytebuddy/byte-buddy/1.14.11/byte-buddy-1.14.11.jar:/Users/<USER>/.m2/repository/org/jodd/jodd-
bean/5.1.6/jodd-bean-5.1.6.jar:/Users/<USER>/.m2/repository/org/jodd/jodd-core/5.1.6/jodd-core-5.1.6.jar:/Use
rs/dubin/.m2/repository/net/coobird/thumbnailator/0.4.20/thumbnailator-0.4.20.jar:/Users/<USER>/.m2/repositor
y/org/apache/commons/commons-lang3/3.14.0/commons-lang3-3.14.0.jar:/Users/<USER>/.m2/repository/commons-io/co
mmons-io/2.15.1/commons-io-2.15.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-core/6.1.3/spri
ng-core-6.1.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/6.1.3/spring-jcl-6.1.3.jar com.
stylishlink.wardrobe.WardrobeServiceApplication
  502 76500 76378   0  5:48下午 ttys024    0:22.35 /usr/local/opt/openjdk/libexec/openjdk.jdk/Contents/Home/
bin/java -classpath /usr/local/Cellar/maven/3.9.9/libexec/boot/plexus-classworlds-2.8.0.jar -Dclassworlds.co
nf=/usr/local/Cellar/maven/3.9.9/libexec/bin/m2.conf -Dmaven.home=/usr/local/Cellar/maven/3.9.9/libexec -Dli
brary.jansi.path=/usr/local/Cellar/maven/3.9.9/libexec/lib/jansi-native -Dmaven.multiModuleProjectDirectory=
/Users/<USER>/work/AI/StylishLink/backend/eureka-server org.codehaus.plexus.classworlds.launcher.Launcher spr
ing-boot:run
  502 76828 76500   0  5:49下午 ttys024    0:40.64 /usr/local/Cellar/openjdk/23.0.1/libexec/openjdk.jdk/Cont
ents/Home/bin/java -XX:TieredStopAtLevel=1 -cp /Users/<USER>/work/AI/StylishLink/backend/eureka-server/target
/classes:/Users/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-starter-netflix-eureka-server/4.
1.0/spring-cloud-starter-netflix-eureka-server-4.1.0.jar:/Users/<USER>/.m2/repository/org/springframework/clo
ud/spring-cloud-starter/4.1.0/spring-cloud-starter-4.1.0.jar:/Users/<USER>/.m2/repository/org/springframework
/cloud/spring-cloud-context/4.1.0/spring-cloud-context-4.1.0.jar:/Users/<USER>/.m2/repository/org/springframe
work/security/spring-security-crypto/6.2.1/spring-security-crypto-6.2.1.jar:/Users/<USER>/.m2/repository/org/
springframework/cloud/spring-cloud-commons/4.1.0/spring-cloud-commons-4.1.0.jar:/Users/<USER>/.m2/repository/
org/springframework/security/spring-security-rsa/1.1.1/spring-security-rsa-1.1.1.jar:/Users/<USER>/.m2/reposi
tory/org/bouncycastle/bcprov-jdk18on/1.74/bcprov-jdk18on-1.74.jar:/Users/<USER>/.m2/repository/org/springfram
ework/cloud/spring-cloud-netflix-eureka-server/4.1.0/spring-cloud-netflix-eureka-server-4.1.0.jar:/Users/<USER>
in/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.15.3/jackson-datatype-jdk8-2.15.3.j
ar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.15.3/jackson-mo
dule-parameter-names-2.15.3.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/10.1.1
8/tomcat-embed-core-10.1.18.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/10.1.18/
tomcat-embed-el-10.1.18.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/10.1.
18/tomcat-embed-websocket-10.1.18.jar:/Users/<USER>/.m2/repository/org/springframework/spring-web/6.1.3/sprin
g-web-6.1.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-beans/6.1.3/spring-beans-6.1.3.jar:/U
sers/dubin/.m2/repository/org/springframework/spring-webmvc/6.1.3/spring-webmvc-6.1.3.jar:/Users/<USER>/.m2/r
epository/org/springframework/spring-aop/6.1.3/spring-aop-6.1.3.jar:/Users/<USER>/.m2/repository/org/springfr
amework/spring-expression/6.1.3/spring-expression-6.1.3.jar:/Users/<USER>/.m2/repository/org/freemarker/freem
arker/2.3.32/freemarker-2.3.32.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context-support/6.
1.3/spring-context-support-6.1.3.jar:/Users/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-netf
lix-eureka-client/4.1.0/spring-cloud-netflix-eureka-client-4.1.0.jar:/Users/<USER>/.m2/repository/com/netflix
/eureka/eureka-client/2.0.1/eureka-client-2.0.1.jar:/Users/<USER>/.m2/repository/com/netflix/netflix-commons/
netflix-eventbus/0.3.0/netflix-eventbus-0.3.0.jar:/Users/<USER>/.m2/repository/com/netflix/netflix-commons/ne
tflix-infix/0.3.0/netflix-infix-0.3.0.jar:/Users/<USER>/.m2/repository/commons-jxpath/commons-jxpath/1.3/comm
ons-jxpath-1.3.jar:/Users/<USER>/.m2/repository/joda-time/joda-time/2.3/joda-time-2.3.jar:/Users/<USER>/.m2/re
pository/org/antlr/antlr-runtime/3.4/antlr-runtime-3.4.jar:/Users/<USER>/.m2/repository/org/antlr/stringtempl
ate/3.2.1/stringtemplate-3.2.1.jar:/Users/<USER>/.m2/repository/antlr/antlr/2.7.7/antlr-2.7.7.jar:/Users/<USER>
n/.m2/repository/com/google/code/gson/gson/2.10.1/gson-2.10.1.jar:/Users/<USER>/.m2/repository/org/apache/com
mons/commons-math/2.2/commons-math-2.2.jar:/Users/<USER>/.m2/repository/com/thoughtworks/xstream/xstream/1.4.
19/xstream-1.4.19.jar:/Users/<USER>/.m2/repository/io/github/x-stream/mxparser/1.2.2/mxparser-1.2.2.jar:/User
s/dubin/.m2/repository/xmlpull/xmlpull/1.1.3.1/xmlpull-1.1.3.1.jar:/Users/<USER>/.m2/repository/com/netflix/s
ervo/servo-core/0.12.21/servo-core-0.12.21.jar:/Users/<USER>/.m2/repository/com/google/guava/guava/32.1.3-jre
/guava-32.1.3-jre.jar:/Users/<USER>/.m2/repository/com/google/guava/failureaccess/1.0.1/failureaccess-1.0.1.j
ar:/Users/<USER>/.m2/repository/com/google/guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/l
istenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar:/Users/<USER>/.m2/repository/org/checkerframewo
rk/checker-qual/3.37.0/checker-qual-3.37.0.jar:/Users/<USER>/.m2/repository/com/google/errorprone/error_prone
_annotations/2.21.1/error_prone_annotations-2.21.1.jar:/Users/<USER>/.m2/repository/com/google/j2objc/j2objc-
annotations/2.8/j2objc-annotations-2.8.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpclient/
4.5.3/httpclient-4.5.3.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore/4.4.16/httpcore-4.
4.16.jar:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.16.0/commons-codec-1.16.0.jar:/Users/<USER>
n/.m2/repository/commons-configuration/commons-configuration/1.10/commons-configuration-1.10.jar:/Users/<USER>
n/.m2/repository/commons-lang/commons-lang/2.6/commons-lang-2.6.jar:/Users/<USER>/.m2/repository/org/codehaus
/jettison/jettison/1.4.0/jettison-1.4.0.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/client5/ht
tpclient5/5.2.3/httpclient5-5.2.3.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/core5/httpcore5/
5.2.4/httpcore5-5.2.4.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/core5/httpcore5-h2/5.2.4/htt
pcore5-h2-5.2.4.jar:/Users/<USER>/.m2/repository/com/netflix/eureka/eureka-client-jersey3/2.0.1/eureka-client
-jersey3-2.0.1.jar:/Users/<USER>/.m2/repository/org/glassfish/jersey/connectors/jersey-apache-connector/3.1.5
/jersey-apache-connector-3.1.5.jar:/Users/<USER>/.m2/repository/com/netflix/eureka/eureka-core-jersey3/2.0.1/
eureka-core-jersey3-2.0.1.jar:/Users/<USER>/.m2/repository/com/netflix/eureka/eureka-core/2.0.1/eureka-core-2
.0.1.jar:/Users/<USER>/.m2/repository/org/glassfish/jersey/containers/jersey-container-servlet/3.1.5/jersey-c
ontainer-servlet-3.1.5.jar:/Users/<USER>/.m2/repository/org/glassfish/jersey/containers/jersey-container-serv
let-core/3.1.5/jersey-container-servlet-core-3.1.5.jar:/Users/<USER>/.m2/repository/org/glassfish/jersey/core
/jersey-common/3.1.5/jersey-common-3.1.5.jar:/Users/<USER>/.m2/repository/org/glassfish/hk2/osgi-resource-loc
ator/1.0.3/osgi-resource-locator-1.0.3.jar:/Users/<USER>/.m2/repository/jakarta/ws/rs/jakarta.ws.rs-api/3.1.0
/jakarta.ws.rs-api-3.1.0.jar:/Users/<USER>/.m2/repository/org/glassfish/jersey/core/jersey-server/3.1.5/jerse
y-server-3.1.5.jar:/Users/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/3.0.2/jakarta.valid
ation-api-3.0.2.jar:/Users/<USER>/.m2/repository/org/glassfish/jersey/core/jersey-client/3.1.5/jersey-client-
3.1.5.jar:/Users/<USER>/.m2/repository/org/glassfish/jersey/inject/jersey-hk2/3.1.5/jersey-hk2-3.1.5.jar:/Use
rs/dubin/.m2/repository/org/glassfish/hk2/hk2-locator/3.0.5/hk2-locator-3.0.5.jar:/Users/<USER>/.m2/repositor
y/org/glassfish/hk2/external/aopalliance-repackaged/3.0.5/aopalliance-repackaged-3.0.5.jar:/Users/<USER>/.m2/
repository/org/glassfish/hk2/hk2-utils/3.0.5/hk2-utils-3.0.5.jar:/Users/<USER>/.m2/repository/org/javassist/j
avassist/3.29.2-GA/javassist-3.29.2-GA.jar:/Users/<USER>/.m2/repository/org/glassfish/hk2/spring-bridge/3.0.5
/spring-bridge-3.0.5.jar:/Users/<USER>/.m2/repository/org/glassfish/hk2/hk2-api/3.0.5/hk2-api-3.0.5.jar:/User
s/dubin/.m2/repository/org/springframework/spring-context/6.1.3/spring-context-6.1.3.jar:/Users/<USER>/.m2/re
pository/jakarta/inject/jakarta.inject-api/2.0.1/jakarta.inject-api-2.0.1.jar:/Users/<USER>/.m2/repository/co
m/fasterxml/jackson/dataformat/jackson-dataformat-xml/2.15.3/jackson-dataformat-xml-2.15.3.jar:/Users/<USER>/
.m2/repository/com/fasterxml/jackson/core/jackson-core/2.15.3/jackson-core-2.15.3.jar:/Users/<USER>/.m2/repos
itory/com/fasterxml/jackson/core/jackson-annotations/2.15.3/jackson-annotations-2.15.3.jar:/Users/<USER>/.m2/
repository/org/codehaus/woodstox/stax2-api/4.2.1/stax2-api-4.2.1.jar:/Users/<USER>/.m2/repository/com/fasterx
ml/woodstox/woodstox-core/6.5.1/woodstox-core-6.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/clou
d/spring-cloud-starter-loadbalancer/4.1.0/spring-cloud-starter-loadbalancer-4.1.0.jar:/Users/<USER>/.m2/repos
itory/org/springframework/cloud/spring-cloud-loadbalancer/4.1.0/spring-cloud-loadbalancer-4.1.0.jar:/Users/<USER>
ubin/.m2/repository/io/projectreactor/reactor-core/3.6.2/reactor-core-3.6.2.jar:/Users/<USER>/.m2/repository/
org/reactivestreams/reactive-streams/1.0.4/reactive-streams-1.0.4.jar:/Users/<USER>/.m2/repository/io/project
reactor/addons/reactor-extra/3.5.1/reactor-extra-3.5.1.jar:/Users/<USER>/.m2/repository/com/stoyanr/evictor/1
.0.0/evictor-1.0.0.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/jaxb-runtime/4.0.4/jaxb-runtime-4.0.4.
jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/jaxb-core/4.0.4/jaxb-core-4.0.4.jar:/Users/<USER>/.m2/repo
sitory/org/eclipse/angus/angus-activation/2.0.1/angus-activation-2.0.1.jar:/Users/<USER>/.m2/repository/org/g
lassfish/jaxb/txw2/4.0.4/txw2-4.0.4.jar:/Users/<USER>/.m2/repository/com/sun/istack/istack-commons-runtime/4.
1.2/istack-commons-runtime-4.1.2.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/3.2.2/
spring-boot-3.2.2.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/3.2.2/s
pring-boot-autoconfigure-3.2.2.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.4.14/logback
-classic-1.4.14.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.4.14/logback-core-1.4.14.jar:/
Users/dubin/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.21.1/log4j-to-slf4j-2.21.1.jar:/Users/<USER>
ubin/.m2/repository/org/apache/logging/log4j/log4j-api/2.21.1/log4j-api-2.21.1.jar:/Users/<USER>/.m2/reposito
ry/org/slf4j/jul-to-slf4j/2.0.11/jul-to-slf4j-2.0.11.jar:/Users/<USER>/.m2/repository/jakarta/annotation/jaka
rta.annotation-api/2.1.1/jakarta.annotation-api-2.1.1.jar:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/2.2
/snakeyaml-2.2.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator-autoconfigure/3
.2.2/spring-boot-actuator-autoconfigure-3.2.2.jar:/Users/<USER>/.m2/repository/org/springframework/boot/sprin
g-boot-actuator/3.2.2/spring-boot-actuator-3.2.2.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/
jackson-databind/2.15.3/jackson-databind-2.15.3.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataty
pe/jackson-datatype-jsr310/2.15.3/jackson-datatype-jsr310-2.15.3.jar:/Users/<USER>/.m2/repository/io/micromet
er/micrometer-observation/1.12.2/micrometer-observation-1.12.2.jar:/Users/<USER>/.m2/repository/io/micrometer
/micrometer-commons/1.12.2/micrometer-commons-1.12.2.jar:/Users/<USER>/.m2/repository/io/micrometer/micromete
r-jakarta9/1.12.2/micrometer-jakarta9-1.12.2.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-core/1
.12.2/micrometer-core-1.12.2.jar:/Users/<USER>/.m2/repository/org/hdrhistogram/HdrHistogram/2.1.12/HdrHistogr
am-2.1.12.jar:/Users/<USER>/.m2/repository/org/latencyutils/LatencyUtils/2.0.3/LatencyUtils-2.0.3.jar:/Users/
dubin/.m2/repository/org/slf4j/slf4j-api/2.0.11/slf4j-api-2.0.11.jar:/Users/<USER>/.m2/repository/jakarta/xml
/bind/jakarta.xml.bind-api/4.0.1/jakarta.xml.bind-api-4.0.1.jar:/Users/<USER>/.m2/repository/jakarta/activati
on/jakarta.activation-api/2.1.2/jakarta.activation-api-2.1.2.jar:/Users/<USER>/.m2/repository/org/springframe
work/spring-core/6.1.3/spring-core-6.1.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/6.1.
3/spring-jcl-6.1.3.jar com.stylishlink.eureka.EurekaServerApplication
  502 85835 85739   0  6:08下午 ttys040    0:13.39 /usr/local/opt/openjdk/libexec/openjdk.jdk/Contents/Home/
bin/java -classpath /usr/local/Cellar/maven/3.9.9/libexec/boot/plexus-classworlds-2.8.0.jar -Dclassworlds.co
nf=/usr/local/Cellar/maven/3.9.9/libexec/bin/m2.conf -Dmaven.home=/usr/local/Cellar/maven/3.9.9/libexec -Dli
brary.jansi.path=/usr/local/Cellar/maven/3.9.9/libexec/lib/jansi-native -Dmaven.multiModuleProjectDirectory=
/Users/<USER>/work/AI/StylishLink/backend/user-service org.codehaus.plexus.classworlds.launcher.Launcher spri
ng-boot:run
  502 85931 85835   0  6:08下午 ttys040    0:21.50 /usr/local/Cellar/openjdk/23.0.1/libexec/openjdk.jdk/Cont
ents/Home/bin/java -XX:TieredStopAtLevel=1 -cp /Users/<USER>/work/AI/StylishLink/backend/user-service/target/
classes:/Users/<USER>/.m2/repository/com/stylishlink/stylishlink-common/1.0.0-SNAPSHOT/stylishlink-common-1.0
.0-SNAPSHOT.jar:/Users/<USER>/.m2/repository/org/mongodb/mongodb-driver-sync/4.11.1/mongodb-driver-sync-4.11.
1.jar:/Users/<USER>/.m2/repository/org/mongodb/bson/4.11.1/bson-4.11.1.jar:/Users/<USER>/.m2/repository/org/mo
ngodb/mongodb-driver-core/4.11.1/mongodb-driver-core-4.11.1.jar:/Users/<USER>/.m2/repository/org/mongodb/bson
-record-codec/4.11.1/bson-record-codec-4.11.1.jar:/Users/<USER>/.m2/repository/org/springframework/data/sprin
g-data-mongodb/4.2.2/spring-data-mongodb-4.2.2.jar:/Users/<USER>/.m2/repository/org/springframework/data/spri
ng-data-commons/3.2.2/spring-data-commons-3.2.2.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-l
ang3/3.14.0/commons-lang3-3.14.0.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-collections4/4.4
/commons-collections4-4.4.jar:/Users/<USER>/.m2/repository/commons-io/commons-io/2.15.1/commons-io-2.15.1.jar
:/Users/<USER>/.m2/repository/com/google/guava/guava/32.1.3-jre/guava-32.1.3-jre.jar:/Users/<USER>/.m2/reposit
ory/com/google/guava/failureaccess/1.0.1/failureaccess-1.0.1.jar:/Users/<USER>/.m2/repository/com/google/guav
a/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/listenablefuture-9999.0-empty-to-avoid-conflict
-with-guava.jar:/Users/<USER>/.m2/repository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar:/Users/<USER>
bin/.m2/repository/org/checkerframework/checker-qual/3.37.0/checker-qual-3.37.0.jar:/Users/<USER>/.m2/reposit
ory/com/google/errorprone/error_prone_annotations/2.21.1/error_prone_annotations-2.21.1.jar:/Users/<USER>/.m2
/repository/com/google/j2objc/j2objc-annotations/2.8/j2objc-annotations-2.8.jar:/Users/<USER>/.m2/repository/
cn/hutool/hutool-all/5.8.25/hutool-all-5.8.25.jar:/Users/<USER>/.m2/repository/org/mapstruct/mapstruct/1.5.5.
Final/mapstruct-1.5.5.Final.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-api/0.12.5/jjwt-api-0.12.5.
jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-impl/0.12.5/jjwt-impl-0.12.5.jar:/Users/<USER>/.m2/repos
itory/io/jsonwebtoken/jjwt-jackson/0.12.5/jjwt-jackson-0.12.5.jar:/Users/<USER>/.m2/repository/org/springfram
ework/boot/spring-boot/3.2.2/spring-boot-3.2.2.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spri
ng-boot-autoconfigure/3.2.2/spring-boot-autoconfigure-3.2.2.jar:/Users/<USER>/.m2/repository/ch/qos/logback/l
ogback-classic/1.4.14/logback-classic-1.4.14.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.4
.14/logback-core-1.4.14.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.21.1/log4j
-to-slf4j-2.21.1.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.21.1/log4j-api-2.21.1.
jar:/Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/2.0.11/jul-to-slf4j-2.0.11.jar:/Users/<USER>/.m2/repos
itory/jakarta/annotation/jakarta.annotation-api/2.1.1/jakarta.annotation-api-2.1.1.jar:/Users/<USER>/.m2/repo
sitory/org/yaml/snakeyaml/2.2/snakeyaml-2.2.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/j
ackson-datatype-jdk8/2.15.3/jackson-datatype-jdk8-2.15.3.jar:/Users/<USER>/.m2/repository/com/fasterxml/jacks
on/datatype/jackson-datatype-jsr310/2.15.3/jackson-datatype-jsr310-2.15.3.jar:/Users/<USER>/.m2/repository/co
m/fasterxml/jackson/module/jackson-module-parameter-names/2.15.3/jackson-module-parameter-names-2.15.3.jar:/
Users/dubin/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/10.1.18/tomcat-embed-core-10.1.18.jar:/
Users/dubin/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/10.1.18/tomcat-embed-websocket-10.
1.18.jar:/Users/<USER>/.m2/repository/org/springframework/spring-web/6.1.3/spring-web-6.1.3.jar:/Users/<USER>/
.m2/repository/org/springframework/spring-beans/6.1.3/spring-beans-6.1.3.jar:/Users/<USER>/.m2/repository/org
/springframework/spring-webmvc/6.1.3/spring-webmvc-6.1.3.jar:/Users/<USER>/.m2/repository/org/springframework
/spring-context/6.1.3/spring-context-6.1.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-expres
sion/6.1.3/spring-expression-6.1.3.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/1
0.1.18/tomcat-embed-el-10.1.18.jar:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/8
.0.1.Final/hibernate-validator-8.0.1.Final.jar:/Users/<USER>/.m2/repository/jakarta/validation/jakarta.valida
tion-api/3.0.2/jakarta.validation-api-3.0.2.jar:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/
3.5.3.Final/jboss-logging-3.5.3.Final.jar:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.6.0/classmat
e-1.6.0.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aop/6.1.3/spring-aop-6.1.3.jar:/Users/<USER>
in/.m2/repository/org/springframework/security/spring-security-config/6.2.1/spring-security-config-6.2.1.jar
:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-web/6.2.1/spring-security-web-6.2.
1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator-autoconfigure/3.2.2/spring-b
oot-actuator-autoconfigure-3.2.2.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuat
or/3.2.2/spring-boot-actuator-3.2.2.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-observation/1.1
2.2/micrometer-observation-1.12.2.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-commons/1.12.2/mi
crometer-commons-1.12.2.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-jakarta9/1.12.2/micrometer-
jakarta9-1.12.2.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-core/1.12.2/micrometer-core-1.12.2.
jar:/Users/<USER>/.m2/repository/org/hdrhistogram/HdrHistogram/2.1.12/HdrHistogram-2.1.12.jar:/Users/<USER>/.m
2/repository/org/latencyutils/LatencyUtils/2.0.3/LatencyUtils-2.0.3.jar:/Users/<USER>/.m2/repository/org/spri
ngframework/cloud/spring-cloud-starter-netflix-eureka-client/4.1.0/spring-cloud-starter-netflix-eureka-clien
t-4.1.0.jar:/Users/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-starter/4.1.0/spring-cloud-st
arter-4.1.0.jar:/Users/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-context/4.1.0/spring-clou
d-context-4.1.0.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-rsa/1.1.1/sprin
g-security-rsa-1.1.1.jar:/Users/<USER>/.m2/repository/org/bouncycastle/bcprov-jdk18on/1.74/bcprov-jdk18on-1.7
4.jar:/Users/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-netflix-eureka-client/4.1.0/spring-
cloud-netflix-eureka-client-4.1.0.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/client5/httpclie
nt5/5.2.3/httpclient5-5.2.3.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/core5/httpcore5/5.2.4/
httpcore5-5.2.4.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/core5/httpcore5-h2/5.2.4/httpcore5
-h2-5.2.4.jar:/Users/<USER>/.m2/repository/com/netflix/eureka/eureka-client/2.0.1/eureka-client-2.0.1.jar:/Us
ers/dubin/.m2/repository/com/netflix/netflix-commons/netflix-eventbus/0.3.0/netflix-eventbus-0.3.0.jar:/User
s/dubin/.m2/repository/com/netflix/netflix-commons/netflix-infix/0.3.0/netflix-infix-0.3.0.jar:/Users/<USER>/
.m2/repository/commons-jxpath/commons-jxpath/1.3/commons-jxpath-1.3.jar:/Users/<USER>/.m2/repository/joda-tim
e/joda-time/2.3/joda-time-2.3.jar:/Users/<USER>/.m2/repository/org/antlr/antlr-runtime/3.4/antlr-runtime-3.4.
jar:/Users/<USER>/.m2/repository/org/antlr/stringtemplate/3.2.1/stringtemplate-3.2.1.jar:/Users/<USER>/.m2/rep
ository/antlr/antlr/2.7.7/antlr-2.7.7.jar:/Users/<USER>/.m2/repository/com/google/code/gson/gson/2.10.1/gson-
2.10.1.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-math/2.2/commons-math-2.2.jar:/Users/<USER>
/.m2/repository/com/thoughtworks/xstream/xstream/1.4.19/xstream-1.4.19.jar:/Users/<USER>/.m2/repository/io/gi
thub/x-stream/mxparser/1.2.2/mxparser-1.2.2.jar:/Users/<USER>/.m2/repository/xmlpull/xmlpull/1.1.3.1/xmlpull-
1.1.3.1.jar:/Users/<USER>/.m2/repository/jakarta/ws/rs/jakarta.ws.rs-api/3.1.0/jakarta.ws.rs-api-3.1.0.jar:/U
sers/dubin/.m2/repository/jakarta/inject/jakarta.inject-api/2.0.1/jakarta.inject-api-2.0.1.jar:/Users/<USER>/
.m2/repository/com/netflix/servo/servo-core/0.12.21/servo-core-0.12.21.jar:/Users/<USER>/.m2/repository/org/a
pache/httpcomponents/httpclient/4.5.3/httpclient-4.5.3.jar:/Users/<USER>/.m2/repository/org/apache/httpcompon
ents/httpcore/4.4.16/httpcore-4.4.16.jar:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.16.0/comm
ons-codec-1.16.0.jar:/Users/<USER>/.m2/repository/commons-configuration/commons-configuration/1.10/commons-co
nfiguration-1.10.jar:/Users/<USER>/.m2/repository/commons-lang/commons-lang/2.6/commons-lang-2.6.jar:/Users/<USER>
ubin/.m2/repository/org/codehaus/jettison/jettison/1.4.0/jettison-1.4.0.jar:/Users/<USER>/.m2/repository/com/
netflix/eureka/eureka-core/2.0.1/eureka-core-2.0.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/woodstox/wo
odstox-core/6.2.1/woodstox-core-6.2.1.jar:/Users/<USER>/.m2/repository/org/codehaus/woodstox/stax2-api/4.2.1/
stax2-api-4.2.1.jar:/Users/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-starter-loadbalancer/
4.1.0/spring-cloud-starter-loadbalancer-4.1.0.jar:/Users/<USER>/.m2/repository/org/springframework/cloud/spri
ng-cloud-loadbalancer/4.1.0/spring-cloud-loadbalancer-4.1.0.jar:/Users/<USER>/.m2/repository/io/projectreacto
r/addons/reactor-extra/3.5.1/reactor-extra-3.5.1.jar:/Users/<USER>/.m2/repository/com/stoyanr/evictor/1.0.0/e
victor-1.0.0.jar:/Users/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-starter-openfeign/4.1.0/
spring-cloud-starter-openfeign-4.1.0.jar:/Users/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-
openfeign-core/4.1.0/spring-cloud-openfeign-core-4.1.0.jar:/Users/<USER>/.m2/repository/org/aspectj/aspectjwe
aver/1.9.21/aspectjweaver-1.9.21.jar:/Users/<USER>/.m2/repository/io/github/openfeign/form/feign-form-spring/
3.8.0/feign-form-spring-3.8.0.jar:/Users/<USER>/.m2/repository/io/github/openfeign/form/feign-form/3.8.0/feig
n-form-3.8.0.jar:/Users/<USER>/.m2/repository/commons-fileupload/commons-fileupload/1.5/commons-fileupload-1.
5.jar:/Users/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-commons/4.1.0/spring-cloud-commons-
4.1.0.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-crypto/6.2.1/spring-secur
ity-crypto-6.2.1.jar:/Users/<USER>/.m2/repository/io/github/openfeign/feign-core/13.1/feign-core-13.1.jar:/Us
ers/dubin/.m2/repository/io/github/openfeign/feign-slf4j/13.1/feign-slf4j-13.1.jar:/Users/<USER>/.m2/reposito
ry/org/springframework/cloud/spring-cloud-starter-circuitbreaker-resilience4j/3.1.0/spring-cloud-starter-cir
cuitbreaker-resilience4j-3.1.0.jar:/Users/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-circui
tbreaker-resilience4j/3.1.0/spring-cloud-circuitbreaker-resilience4j-3.1.0.jar:/Users/<USER>/.m2/repository/i
o/github/resilience4j/resilience4j-spring-boot3/2.1.0/resilience4j-spring-boot3-2.1.0.jar:/Users/<USER>/.m2/r
epository/io/github/resilience4j/resilience4j-spring6/2.1.0/resilience4j-spring6-2.1.0.jar:/Users/<USER>/.m2/
repository/io/github/resilience4j/resilience4j-annotations/2.1.0/resilience4j-annotations-2.1.0.jar:/Users/<USER>
ubin/.m2/repository/io/github/resilience4j/resilience4j-consumer/2.1.0/resilience4j-consumer-2.1.0.jar:/User
s/dubin/.m2/repository/io/github/resilience4j/resilience4j-circularbuffer/2.1.0/resilience4j-circularbuffer-
2.1.0.jar:/Users/<USER>/.m2/repository/io/github/resilience4j/resilience4j-framework-common/2.1.0/resilience4
j-framework-common-2.1.0.jar:/Users/<USER>/.m2/repository/io/github/resilience4j/resilience4j-micrometer/2.1.
0/resilience4j-micrometer-2.1.0.jar:/Users/<USER>/.m2/repository/io/github/resilience4j/resilience4j-retry/2.
1.0/resilience4j-retry-2.1.0.jar:/Users/<USER>/.m2/repository/io/github/resilience4j/resilience4j-ratelimiter
/2.1.0/resilience4j-ratelimiter-2.1.0.jar:/Users/<USER>/.m2/repository/io/github/resilience4j/resilience4j-ci
rcuitbreaker/2.1.0/resilience4j-circuitbreaker-2.1.0.jar:/Users/<USER>/.m2/repository/io/github/resilience4j/
resilience4j-core/2.1.0/resilience4j-core-2.1.0.jar:/Users/<USER>/.m2/repository/io/github/resilience4j/resil
ience4j-timelimiter/2.1.0/resilience4j-timelimiter-2.1.0.jar:/Users/<USER>/.m2/repository/io/lettuce/lettuce-
core/6.3.1.RELEASE/lettuce-core-6.3.1.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/data/sprin
g-data-redis/3.2.2/spring-data-redis-3.2.2.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-d
ata-keyvalue/3.2.2/spring-data-keyvalue-3.2.2.jar:/Users/<USER>/.m2/repository/org/springframework/spring-tx/
6.1.3/spring-tx-6.1.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-oxm/6.1.3/spring-oxm-6.1.3.
jar:/Users/<USER>/.m2/repository/org/springframework/spring-context-support/6.1.3/spring-context-support-6.1.
3.jar:/Users/<USER>/.m2/repository/org/redisson/redisson/3.26.0/redisson-3.26.0.jar:/Users/<USER>/.m2/reposito
ry/io/netty/netty-common/4.1.105.Final/netty-common-4.1.105.Final.jar:/Users/<USER>/.m2/repository/io/netty/n
etty-codec/4.1.105.Final/netty-codec-4.1.105.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-buffer/4.1
.105.Final/netty-buffer-4.1.105.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport/4.1.105.Final
/netty-transport-4.1.105.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver/4.1.105.Final/netty-r
esolver-4.1.105.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver-dns/4.1.105.Final/netty-resolv
er-dns-4.1.105.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-dns/4.1.105.Final/netty-codec-dns-
4.1.105.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-handler/4.1.105.Final/netty-handler-4.1.105.Fin
al.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-native-unix-common/4.1.105.Final/netty-transport
-native-unix-common-4.1.105.Final.jar:/Users/<USER>/.m2/repository/javax/cache/cache-api/1.1.1/cache-api-1.1.
1.jar:/Users/<USER>/.m2/repository/io/projectreactor/reactor-core/3.6.2/reactor-core-3.6.2.jar:/Users/<USER>/.
m2/repository/org/reactivestreams/reactive-streams/1.0.4/reactive-streams-1.0.4.jar:/Users/<USER>/.m2/reposit
ory/io/reactivex/rxjava3/rxjava/3.1.8/rxjava-3.1.8.jar:/Users/<USER>/.m2/repository/org/jboss/marshalling/jbo
ss-marshalling/2.0.11.Final/jboss-marshalling-2.0.11.Final.jar:/Users/<USER>/.m2/repository/org/jboss/marshal
ling/jboss-marshalling-river/2.0.11.Final/jboss-marshalling-river-2.0.11.Final.jar:/Users/<USER>/.m2/reposito
ry/com/esotericsoftware/kryo/5.6.0/kryo-5.6.0.jar:/Users/<USER>/.m2/repository/com/esotericsoftware/reflectas
m/1.11.9/reflectasm-1.11.9.jar:/Users/<USER>/.m2/repository/org/objenesis/objenesis/3.3/objenesis-3.3.jar:/Us
ers/dubin/.m2/repository/com/esotericsoftware/minlog/1.3.1/minlog-1.3.1.jar:/Users/<USER>/.m2/repository/org/
slf4j/slf4j-api/2.0.11/slf4j-api-2.0.11.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-a
nnotations/2.15.3/jackson-annotations-2.15.3.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataforma
t/jackson-dataformat-yaml/2.15.3/jackson-dataformat-yaml-2.15.3.jar:/Users/<USER>/.m2/repository/com/fasterxm
l/jackson/core/jackson-core/2.15.3/jackson-core-2.15.3.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson
/core/jackson-databind/2.15.3/jackson-databind-2.15.3.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-bud
dy/1.14.11/byte-buddy-1.14.11.jar:/Users/<USER>/.m2/repository/org/jodd/jodd-bean/5.1.6/jodd-bean-5.1.6.jar:/
Users/dubin/.m2/repository/org/jodd/jodd-core/5.1.6/jodd-core-5.1.6.jar:/Users/<USER>/.m2/repository/org/spri
ngframework/spring-core/6.1.3/spring-core-6.1.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-j
cl/6.1.3/spring-jcl-6.1.3.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-core/
6.2.1/spring-security-core-6.2.1.jar com.stylishlink.user.UserServiceApplication
  502 86409 86360   0  6:09下午 ttys042    0:15.49 /usr/local/opt/openjdk/libexec/openjdk.jdk/Contents/Home/
bin/java -classpath /usr/local/Cellar/maven/3.9.9/libexec/boot/plexus-classworlds-2.8.0.jar -Dclassworlds.co
nf=/usr/local/Cellar/maven/3.9.9/libexec/bin/m2.conf -Dmaven.home=/usr/local/Cellar/maven/3.9.9/libexec -Dli
brary.jansi.path=/usr/local/Cellar/maven/3.9.9/libexec/lib/jansi-native -Dmaven.multiModuleProjectDirectory=
/Users/<USER>/work/AI/StylishLink/backend/recommendation-service org.codehaus.plexus.classworlds.launcher.Lau
ncher spring-boot:run
  502 86556 86409   0  6:09下午 ttys042    0:20.23 /usr/local/Cellar/openjdk/23.0.1/libexec/openjdk.jdk/Cont
ents/Home/bin/java -XX:TieredStopAtLevel=1 -cp /Users/<USER>/work/AI/StylishLink/backend/recommendation-servi
ce/target/classes:/Users/<USER>/.m2/repository/com/stylishlink/stylishlink-common/1.0.0-SNAPSHOT/stylishlink-
common-1.0.0-SNAPSHOT.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-collections4/4.4/commons-co
llections4-4.4.jar:/Users/<USER>/.m2/repository/com/google/guava/guava/32.1.3-jre/guava-32.1.3-jre.jar:/Users
/dubin/.m2/repository/com/google/guava/failureaccess/1.0.1/failureaccess-1.0.1.jar:/Users/<USER>/.m2/reposito
ry/com/google/guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/listenablefuture-9999.0-empty
-to-avoid-conflict-with-guava.jar:/Users/<USER>/.m2/repository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3
.0.2.jar:/Users/<USER>/.m2/repository/org/checkerframework/checker-qual/3.37.0/checker-qual-3.37.0.jar:/Users
/dubin/.m2/repository/com/google/errorprone/error_prone_annotations/2.21.1/error_prone_annotations-2.21.1.ja
r:/Users/<USER>/.m2/repository/com/google/j2objc/j2objc-annotations/2.8/j2objc-annotations-2.8.jar:/Users/<USER>
in/.m2/repository/cn/hutool/hutool-all/5.8.25/hutool-all-5.8.25.jar:/Users/<USER>/.m2/repository/org/mapstruc
t/mapstruct/1.5.5.Final/mapstruct-1.5.5.Final.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-api/0.12.
5/jjwt-api-0.12.5.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-impl/0.12.5/jjwt-impl-0.12.5.jar:/Use
rs/dubin/.m2/repository/io/jsonwebtoken/jjwt-jackson/0.12.5/jjwt-jackson-0.12.5.jar:/Users/<USER>/.m2/reposit
ory/org/springframework/boot/spring-boot/3.2.2/spring-boot-3.2.2.jar:/Users/<USER>/.m2/repository/org/springf
ramework/boot/spring-boot-autoconfigure/3.2.2/spring-boot-autoconfigure-3.2.2.jar:/Users/<USER>/.m2/repositor
y/ch/qos/logback/logback-classic/1.4.14/logback-classic-1.4.14.jar:/Users/<USER>/.m2/repository/ch/qos/logbac
k/logback-core/1.4.14/logback-core-1.4.14.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-
slf4j/2.21.1/log4j-to-slf4j-2.21.1.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.21.1
/log4j-api-2.21.1.jar:/Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/2.0.11/jul-to-slf4j-2.0.11.jar:/Use
rs/dubin/.m2/repository/jakarta/annotation/jakarta.annotation-api/2.1.1/jakarta.annotation-api-2.1.1.jar:/Us
ers/dubin/.m2/repository/org/yaml/snakeyaml/2.2/snakeyaml-2.2.jar:/Users/<USER>/.m2/repository/com/fasterxml/
jackson/datatype/jackson-datatype-jdk8/2.15.3/jackson-datatype-jdk8-2.15.3.jar:/Users/<USER>/.m2/repository/c
om/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.15.3/jackson-datatype-jsr310-2.15.3.jar:/Users/<USER>
/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.15.3/jackson-module-parameter-
names-2.15.3.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/10.1.18/tomcat-embed-
core-10.1.18.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/10.1.18/tomcat-e
mbed-websocket-10.1.18.jar:/Users/<USER>/.m2/repository/org/springframework/spring-web/6.1.3/spring-web-6.1.3
.jar:/Users/<USER>/.m2/repository/org/springframework/spring-beans/6.1.3/spring-beans-6.1.3.jar:/Users/<USER>/
.m2/repository/org/springframework/spring-webmvc/6.1.3/spring-webmvc-6.1.3.jar:/Users/<USER>/.m2/repository/o
rg/springframework/spring-aop/6.1.3/spring-aop-6.1.3.jar:/Users/<USER>/.m2/repository/org/springframework/spr
ing-context/6.1.3/spring-context-6.1.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-expression
/6.1.3/spring-expression-6.1.3.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/10.1.
18/tomcat-embed-el-10.1.18.jar:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/8.0.1
.Final/hibernate-validator-8.0.1.Final.jar:/Users/<USER>/.m2/repository/jakarta/validation/jakarta.validation
-api/3.0.2/jakarta.validation-api-3.0.2.jar:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.5.
3.Final/jboss-logging-3.5.3.Final.jar:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.6.0/classmate-1.
6.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator-autoconfigure/3.2.2/spring
-boot-actuator-autoconfigure-3.2.2.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actu
ator/3.2.2/spring-boot-actuator-3.2.2.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-observation/1
.12.2/micrometer-observation-1.12.2.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-commons/1.12.2/
micrometer-commons-1.12.2.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-jakarta9/1.12.2/micromete
r-jakarta9-1.12.2.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-core/1.12.2/micrometer-core-1.12.
2.jar:/Users/<USER>/.m2/repository/org/hdrhistogram/HdrHistogram/2.1.12/HdrHistogram-2.1.12.jar:/Users/<USER>/
.m2/repository/org/latencyutils/LatencyUtils/2.0.3/LatencyUtils-2.0.3.jar:/Users/<USER>/.m2/repository/org/sp
ringframework/cloud/spring-cloud-starter-netflix-eureka-client/4.1.0/spring-cloud-starter-netflix-eureka-cli
ent-4.1.0.jar:/Users/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-starter/4.1.0/spring-cloud-
starter-4.1.0.jar:/Users/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-context/4.1.0/spring-cl
oud-context-4.1.0.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-rsa/1.1.1/spr
ing-security-rsa-1.1.1.jar:/Users/<USER>/.m2/repository/org/bouncycastle/bcprov-jdk18on/1.74/bcprov-jdk18on-1
.74.jar:/Users/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-netflix-eureka-client/4.1.0/sprin
g-cloud-netflix-eureka-client-4.1.0.jar:/Users/<USER>/.m2/repository/com/netflix/eureka/eureka-client/2.0.1/e
ureka-client-2.0.1.jar:/Users/<USER>/.m2/repository/com/netflix/netflix-commons/netflix-eventbus/0.3.0/netfli
x-eventbus-0.3.0.jar:/Users/<USER>/.m2/repository/com/netflix/netflix-commons/netflix-infix/0.3.0/netflix-inf
ix-0.3.0.jar:/Users/<USER>/.m2/repository/commons-jxpath/commons-jxpath/1.3/commons-jxpath-1.3.jar:/Users/<USER>
in/.m2/repository/joda-time/joda-time/2.3/joda-time-2.3.jar:/Users/<USER>/.m2/repository/org/antlr/antlr-runt
ime/3.4/antlr-runtime-3.4.jar:/Users/<USER>/.m2/repository/org/antlr/stringtemplate/3.2.1/stringtemplate-3.2.
1.jar:/Users/<USER>/.m2/repository/antlr/antlr/2.7.7/antlr-2.7.7.jar:/Users/<USER>/.m2/repository/com/google/c
ode/gson/gson/2.10.1/gson-2.10.1.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-math/2.2/commons
-math-2.2.jar:/Users/<USER>/.m2/repository/com/thoughtworks/xstream/xstream/1.4.19/xstream-1.4.19.jar:/Users/
dubin/.m2/repository/io/github/x-stream/mxparser/1.2.2/mxparser-1.2.2.jar:/Users/<USER>/.m2/repository/xmlpul
l/xmlpull/1.1.3.1/xmlpull-1.1.3.1.jar:/Users/<USER>/.m2/repository/jakarta/ws/rs/jakarta.ws.rs-api/3.1.0/jaka
rta.ws.rs-api-3.1.0.jar:/Users/<USER>/.m2/repository/jakarta/inject/jakarta.inject-api/2.0.1/jakarta.inject-a
pi-2.0.1.jar:/Users/<USER>/.m2/repository/com/netflix/servo/servo-core/0.12.21/servo-core-0.12.21.jar:/Users/
dubin/.m2/repository/org/apache/httpcomponents/httpclient/4.5.3/httpclient-4.5.3.jar:/Users/<USER>/.m2/reposi
tory/org/apache/httpcomponents/httpcore/4.4.16/httpcore-4.4.16.jar:/Users/<USER>/.m2/repository/commons-codec
/commons-codec/1.16.0/commons-codec-1.16.0.jar:/Users/<USER>/.m2/repository/commons-configuration/commons-con
figuration/1.10/commons-configuration-1.10.jar:/Users/<USER>/.m2/repository/commons-lang/commons-lang/2.6/com
mons-lang-2.6.jar:/Users/<USER>/.m2/repository/org/codehaus/jettison/jettison/1.4.0/jettison-1.4.0.jar:/Users
/dubin/.m2/repository/com/netflix/eureka/eureka-core/2.0.1/eureka-core-2.0.1.jar:/Users/<USER>/.m2/repository
/com/fasterxml/woodstox/woodstox-core/6.2.1/woodstox-core-6.2.1.jar:/Users/<USER>/.m2/repository/org/codehaus
/woodstox/stax2-api/4.2.1/stax2-api-4.2.1.jar:/Users/<USER>/.m2/repository/org/springframework/cloud/spring-c
loud-starter-loadbalancer/4.1.0/spring-cloud-starter-loadbalancer-4.1.0.jar:/Users/<USER>/.m2/repository/org/
springframework/cloud/spring-cloud-loadbalancer/4.1.0/spring-cloud-loadbalancer-4.1.0.jar:/Users/<USER>/.m2/r
epository/io/projectreactor/addons/reactor-extra/3.5.1/reactor-extra-3.5.1.jar:/Users/<USER>/.m2/repository/c
om/stoyanr/evictor/1.0.0/evictor-1.0.0.jar:/Users/<USER>/.m2/repository/org/springframework/cloud/spring-clou
d-starter-openfeign/4.1.0/spring-cloud-starter-openfeign-4.1.0.jar:/Users/<USER>/.m2/repository/org/springfra
mework/cloud/spring-cloud-openfeign-core/4.1.0/spring-cloud-openfeign-core-4.1.0.jar:/Users/<USER>/.m2/reposi
tory/org/aspectj/aspectjweaver/1.9.21/aspectjweaver-1.9.21.jar:/Users/<USER>/.m2/repository/io/github/openfei
gn/form/feign-form-spring/3.8.0/feign-form-spring-3.8.0.jar:/Users/<USER>/.m2/repository/io/github/openfeign/
form/feign-form/3.8.0/feign-form-3.8.0.jar:/Users/<USER>/.m2/repository/commons-fileupload/commons-fileupload
/1.5/commons-fileupload-1.5.jar:/Users/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-commons/4
.1.0/spring-cloud-commons-4.1.0.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security
-crypto/6.2.1/spring-security-crypto-6.2.1.jar:/Users/<USER>/.m2/repository/io/github/openfeign/feign-core/13
.1/feign-core-13.1.jar:/Users/<USER>/.m2/repository/io/github/openfeign/feign-slf4j/13.1/feign-slf4j-13.1.jar
:/Users/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-starter-circuitbreaker-resilience4j/3.1.
0/spring-cloud-starter-circuitbreaker-resilience4j-3.1.0.jar:/Users/<USER>/.m2/repository/org/springframework
/cloud/spring-cloud-circuitbreaker-resilience4j/3.1.0/spring-cloud-circuitbreaker-resilience4j-3.1.0.jar:/Us
ers/dubin/.m2/repository/io/github/resilience4j/resilience4j-spring-boot3/2.1.0/resilience4j-spring-boot3-2.
1.0.jar:/Users/<USER>/.m2/repository/io/github/resilience4j/resilience4j-spring6/2.1.0/resilience4j-spring6-2
.1.0.jar:/Users/<USER>/.m2/repository/io/github/resilience4j/resilience4j-annotations/2.1.0/resilience4j-anno
tations-2.1.0.jar:/Users/<USER>/.m2/repository/io/github/resilience4j/resilience4j-consumer/2.1.0/resilience4
j-consumer-2.1.0.jar:/Users/<USER>/.m2/repository/io/github/resilience4j/resilience4j-circularbuffer/2.1.0/re
silience4j-circularbuffer-2.1.0.jar:/Users/<USER>/.m2/repository/io/github/resilience4j/resilience4j-framewor
k-common/2.1.0/resilience4j-framework-common-2.1.0.jar:/Users/<USER>/.m2/repository/io/github/resilience4j/re
silience4j-micrometer/2.1.0/resilience4j-micrometer-2.1.0.jar:/Users/<USER>/.m2/repository/io/github/resilien
ce4j/resilience4j-retry/2.1.0/resilience4j-retry-2.1.0.jar:/Users/<USER>/.m2/repository/io/github/resilience4
j/resilience4j-ratelimiter/2.1.0/resilience4j-ratelimiter-2.1.0.jar:/Users/<USER>/.m2/repository/io/github/re
silience4j/resilience4j-circuitbreaker/2.1.0/resilience4j-circuitbreaker-2.1.0.jar:/Users/<USER>/.m2/reposito
ry/io/github/resilience4j/resilience4j-core/2.1.0/resilience4j-core-2.1.0.jar:/Users/<USER>/.m2/repository/io
/github/resilience4j/resilience4j-timelimiter/2.1.0/resilience4j-timelimiter-2.1.0.jar:/Users/<USER>/.m2/repo
sitory/org/mongodb/mongodb-driver-sync/4.11.1/mongodb-driver-sync-4.11.1.jar:/Users/<USER>/.m2/repository/org
/mongodb/bson/4.11.1/bson-4.11.1.jar:/Users/<USER>/.m2/repository/org/mongodb/mongodb-driver-core/4.11.1/mong
odb-driver-core-4.11.1.jar:/Users/<USER>/.m2/repository/org/mongodb/bson-record-codec/4.11.1/bson-record-code
c-4.11.1.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-mongodb/4.2.2/spring-data-mong
odb-4.2.2.jar:/Users/<USER>/.m2/repository/org/springframework/spring-tx/6.1.3/spring-tx-6.1.3.jar:/Users/<USER>
in/.m2/repository/org/springframework/data/spring-data-commons/3.2.2/spring-data-commons-3.2.2.jar:/Users/<USER>
bin/.m2/repository/io/lettuce/lettuce-core/6.3.1.RELEASE/lettuce-core-6.3.1.RELEASE.jar:/Users/<USER>/.m2/rep
ository/org/springframework/data/spring-data-redis/3.2.2/spring-data-redis-3.2.2.jar:/Users/<USER>/.m2/reposi
tory/org/springframework/data/spring-data-keyvalue/3.2.2/spring-data-keyvalue-3.2.2.jar:/Users/<USER>/.m2/rep
ository/org/springframework/spring-oxm/6.1.3/spring-oxm-6.1.3.jar:/Users/<USER>/.m2/repository/org/springfram
ework/spring-context-support/6.1.3/spring-context-support-6.1.3.jar:/Users/<USER>/.m2/repository/org/redisson
/redisson/3.26.0/redisson-3.26.0.jar:/Users/<USER>/.m2/repository/io/netty/netty-common/4.1.105.Final/netty-c
ommon-4.1.105.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec/4.1.105.Final/netty-codec-4.1.105.F
inal.jar:/Users/<USER>/.m2/repository/io/netty/netty-buffer/4.1.105.Final/netty-buffer-4.1.105.Final.jar:/Use
rs/dubin/.m2/repository/io/netty/netty-transport/4.1.105.Final/netty-transport-4.1.105.Final.jar:/Users/<USER>
n/.m2/repository/io/netty/netty-resolver/4.1.105.Final/netty-resolver-4.1.105.Final.jar:/Users/<USER>/.m2/rep
ository/io/netty/netty-resolver-dns/4.1.105.Final/netty-resolver-dns-4.1.105.Final.jar:/Users/<USER>/.m2/repo
sitory/io/netty/netty-codec-dns/4.1.105.Final/netty-codec-dns-4.1.105.Final.jar:/Users/<USER>/.m2/repository/
io/netty/netty-handler/4.1.105.Final/netty-handler-4.1.105.Final.jar:/Users/<USER>/.m2/repository/io/netty/ne
tty-transport-native-unix-common/4.1.105.Final/netty-transport-native-unix-common-4.1.105.Final.jar:/Users/<USER>
ubin/.m2/repository/javax/cache/cache-api/1.1.1/cache-api-1.1.1.jar:/Users/<USER>/.m2/repository/io/projectre
actor/reactor-core/3.6.2/reactor-core-3.6.2.jar:/Users/<USER>/.m2/repository/org/reactivestreams/reactive-str
eams/1.0.4/reactive-streams-1.0.4.jar:/Users/<USER>/.m2/repository/io/reactivex/rxjava3/rxjava/3.1.8/rxjava-3
.1.8.jar:/Users/<USER>/.m2/repository/org/jboss/marshalling/jboss-marshalling/2.0.11.Final/jboss-marshalling-
2.0.11.Final.jar:/Users/<USER>/.m2/repository/org/jboss/marshalling/jboss-marshalling-river/2.0.11.Final/jbos
s-marshalling-river-2.0.11.Final.jar:/Users/<USER>/.m2/repository/com/esotericsoftware/kryo/5.6.0/kryo-5.6.0.
jar:/Users/<USER>/.m2/repository/com/esotericsoftware/reflectasm/1.11.9/reflectasm-1.11.9.jar:/Users/<USER>/.m
2/repository/org/objenesis/objenesis/3.3/objenesis-3.3.jar:/Users/<USER>/.m2/repository/com/esotericsoftware/
minlog/1.3.1/minlog-1.3.1.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/2.0.11/slf4j-api-2.0.11.jar:/U
sers/dubin/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.15.3/jackson-annotations-2.15.3.j
ar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-yaml/2.15.3/jackson-dataf
ormat-yaml-2.15.3.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.15.3/jackson-cor
e-2.15.3.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.15.3/jackson-databind
-2.15.3.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.14.11/byte-buddy-1.14.11.jar:/Users/<USER>
/.m2/repository/org/jodd/jodd-bean/5.1.6/jodd-bean-5.1.6.jar:/Users/<USER>/.m2/repository/org/jodd/jodd-core/
5.1.6/jodd-core-5.1.6.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/client5/httpclient5/5.2.3/ht
tpclient5-5.2.3.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/core5/httpcore5/5.2.4/httpcore5-5.
2.4.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/core5/httpcore5-h2/5.2.4/httpcore5-h2-5.2.4.ja
r:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.14.0/commons-lang3-3.14.0.jar:/Users/<USER>/
.m2/repository/commons-io/commons-io/2.15.1/commons-io-2.15.1.jar:/Users/<USER>/.m2/repository/org/springfram
ework/spring-core/6.1.3/spring-core-6.1.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/6.1
.3/spring-jcl-6.1.3.jar com.stylishlink.recommendation.RecommendationServiceApplication
  502 98659 95400   0  6:37下午 ttys052    0:13.33 /usr/local/opt/openjdk/libexec/openjdk.jdk/Contents/Home/
bin/java -classpath /usr/local/Cellar/maven/3.9.9/libexec/boot/plexus-classworlds-2.8.0.jar -Dclassworlds.co
nf=/usr/local/Cellar/maven/3.9.9/libexec/bin/m2.conf -Dmaven.home=/usr/local/Cellar/maven/3.9.9/libexec -Dli
brary.jansi.path=/usr/local/Cellar/maven/3.9.9/libexec/lib/jansi-native -Dmaven.multiModuleProjectDirectory=
/Users/<USER>/work/AI/StylishLink/backend/gateway-service org.codehaus.plexus.classworlds.launcher.Launcher s
pring-boot:run
  502 98886 98659   0  6:37下午 ttys052    0:11.86 /usr/local/Cellar/openjdk/23.0.1/libexec/openjdk.jdk/Cont
ents/Home/bin/java -XX:TieredStopAtLevel=1 -cp /Users/<USER>/work/AI/StylishLink/backend/gateway-service/targ
et/classes:/Users/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-starter-gateway/4.1.0/spring-c
loud-starter-gateway-4.1.0.jar:/Users/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-starter/4.
1.0/spring-cloud-starter-4.1.0.jar:/Users/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-contex
t/4.1.0/spring-cloud-context-4.1.0.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-secur
ity-crypto/6.2.1/spring-security-crypto-6.2.1.jar:/Users/<USER>/.m2/repository/org/springframework/cloud/spri
ng-cloud-commons/4.1.0/spring-cloud-commons-4.1.0.jar:/Users/<USER>/.m2/repository/org/springframework/securi
ty/spring-security-rsa/1.1.1/spring-security-rsa-1.1.1.jar:/Users/<USER>/.m2/repository/org/bouncycastle/bcpr
ov-jdk18on/1.74/bcprov-jdk18on-1.74.jar:/Users/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-g
ateway-server/4.1.0/spring-cloud-gateway-server-4.1.0.jar:/Users/<USER>/.m2/repository/io/projectreactor/addo
ns/reactor-extra/3.5.1/reactor-extra-3.5.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/ja
ckson-datatype-jdk8/2.15.3/jackson-datatype-jdk8-2.15.3.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackso
n/module/jackson-module-parameter-names/2.15.3/jackson-module-parameter-names-2.15.3.jar:/Users/<USER>/.m2/re
pository/io/projectreactor/netty/reactor-netty-http/1.1.15/reactor-netty-http-1.1.15.jar:/Users/<USER>/.m2/re
pository/io/netty/netty-codec-http/4.1.105.Final/netty-codec-http-4.1.105.Final.jar:/Users/<USER>/.m2/reposit
ory/io/netty/netty-codec-http2/4.1.105.Final/netty-codec-http2-4.1.105.Final.jar:/Users/<USER>/.m2/repository
/io/netty/netty-resolver-dns/4.1.105.Final/netty-resolver-dns-4.1.105.Final.jar:/Users/<USER>/.m2/repository/
io/netty/netty-codec-dns/4.1.105.Final/netty-codec-dns-4.1.105.Final.jar:/Users/<USER>/.m2/repository/io/nett
y/netty-resolver-dns-native-macos/4.1.105.Final/netty-resolver-dns-native-macos-4.1.105.Final-osx-x86_64.jar
:/Users/<USER>/.m2/repository/io/netty/netty-resolver-dns-classes-macos/4.1.105.Final/netty-resolver-dns-clas
ses-macos-4.1.105.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-native-epoll/4.1.105.Final/
netty-transport-native-epoll-4.1.105.Final-linux-x86_64.jar:/Users/<USER>/.m2/repository/io/netty/netty-trans
port-classes-epoll/4.1.105.Final/netty-transport-classes-epoll-4.1.105.Final.jar:/Users/<USER>/.m2/repository
/io/projectreactor/netty/reactor-netty-core/1.1.15/reactor-netty-core-1.1.15.jar:/Users/<USER>/.m2/repository
/io/netty/netty-handler-proxy/4.1.105.Final/netty-handler-proxy-4.1.105.Final.jar:/Users/<USER>/.m2/repositor
y/io/netty/netty-codec-socks/4.1.105.Final/netty-codec-socks-4.1.105.Final.jar:/Users/<USER>/.m2/repository/o
rg/springframework/spring-web/6.1.3/spring-web-6.1.3.jar:/Users/<USER>/.m2/repository/org/springframework/spr
ing-webflux/6.1.3/spring-webflux-6.1.3.jar:/Users/<USER>/.m2/repository/org/springframework/cloud/spring-clou
d-starter-netflix-eureka-client/4.1.0/spring-cloud-starter-netflix-eureka-client-4.1.0.jar:/Users/<USER>/.m2/
repository/org/springframework/cloud/spring-cloud-netflix-eureka-client/4.1.0/spring-cloud-netflix-eureka-cl
ient-4.1.0.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/client5/httpclient5/5.2.3/httpclient5-5
.2.3.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/core5/httpcore5/5.2.4/httpcore5-5.2.4.jar:/Us
ers/dubin/.m2/repository/org/apache/httpcomponents/core5/httpcore5-h2/5.2.4/httpcore5-h2-5.2.4.jar:/Users/<USER>
bin/.m2/repository/com/netflix/eureka/eureka-client/2.0.1/eureka-client-2.0.1.jar:/Users/<USER>/.m2/repositor
y/com/netflix/netflix-commons/netflix-eventbus/0.3.0/netflix-eventbus-0.3.0.jar:/Users/<USER>/.m2/repository/
com/netflix/netflix-commons/netflix-infix/0.3.0/netflix-infix-0.3.0.jar:/Users/<USER>/.m2/repository/commons-
jxpath/commons-jxpath/1.3/commons-jxpath-1.3.jar:/Users/<USER>/.m2/repository/joda-time/joda-time/2.3/joda-ti
me-2.3.jar:/Users/<USER>/.m2/repository/org/antlr/antlr-runtime/3.4/antlr-runtime-3.4.jar:/Users/<USER>/.m2/re
pository/org/antlr/stringtemplate/3.2.1/stringtemplate-3.2.1.jar:/Users/<USER>/.m2/repository/antlr/antlr/2.7
.7/antlr-2.7.7.jar:/Users/<USER>/.m2/repository/com/google/code/gson/gson/2.10.1/gson-2.10.1.jar:/Users/<USER>
/.m2/repository/org/apache/commons/commons-math/2.2/commons-math-2.2.jar:/Users/<USER>/.m2/repository/com/tho
ughtworks/xstream/xstream/1.4.19/xstream-1.4.19.jar:/Users/<USER>/.m2/repository/io/github/x-stream/mxparser/
1.2.2/mxparser-1.2.2.jar:/Users/<USER>/.m2/repository/xmlpull/xmlpull/1.1.3.1/xmlpull-1.1.3.1.jar:/Users/<USER>
n/.m2/repository/jakarta/ws/rs/jakarta.ws.rs-api/3.1.0/jakarta.ws.rs-api-3.1.0.jar:/Users/<USER>/.m2/reposito
ry/jakarta/inject/jakarta.inject-api/2.0.1/jakarta.inject-api-2.0.1.jar:/Users/<USER>/.m2/repository/jakarta/
annotation/jakarta.annotation-api/2.1.1/jakarta.annotation-api-2.1.1.jar:/Users/<USER>/.m2/repository/com/net
flix/servo/servo-core/0.12.21/servo-core-0.12.21.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/h
ttpclient/4.5.3/httpclient-4.5.3.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore/4.4.16/h
ttpcore-4.4.16.jar:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.16.0/commons-codec-1.16.0.jar:/
Users/dubin/.m2/repository/commons-configuration/commons-configuration/1.10/commons-configuration-1.10.jar:/
Users/dubin/.m2/repository/commons-lang/commons-lang/2.6/commons-lang-2.6.jar:/Users/<USER>/.m2/repository/co
m/fasterxml/jackson/core/jackson-annotations/2.15.3/jackson-annotations-2.15.3.jar:/Users/<USER>/.m2/reposito
ry/com/fasterxml/jackson/core/jackson-core/2.15.3/jackson-core-2.15.3.jar:/Users/<USER>/.m2/repository/org/co
dehaus/jettison/jettison/1.4.0/jettison-1.4.0.jar:/Users/<USER>/.m2/repository/com/netflix/eureka/eureka-core
/2.0.1/eureka-core-2.0.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/woodstox/woodstox-core/6.2.1/woodstox
-core-6.2.1.jar:/Users/<USER>/.m2/repository/org/codehaus/woodstox/stax2-api/4.2.1/stax2-api-4.2.1.jar:/Users
/dubin/.m2/repository/org/springframework/cloud/spring-cloud-starter-loadbalancer/4.1.0/spring-cloud-starter
-loadbalancer-4.1.0.jar:/Users/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-loadbalancer/4.1.
0/spring-cloud-loadbalancer-4.1.0.jar:/Users/<USER>/.m2/repository/com/stoyanr/evictor/1.0.0/evictor-1.0.0.ja
r:/Users/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-starter-config/4.1.0/spring-cloud-start
er-config-4.1.0.jar:/Users/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-config-client/4.1.0/s
pring-cloud-config-client-4.1.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autocon
figure/3.2.2/spring-boot-autoconfigure-3.2.2.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jack
son-databind/2.15.3/jackson-databind-2.15.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-
boot/3.2.2/spring-boot-3.2.2.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.4.14/logback-c
lassic-1.4.14.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.4.14/logback-core-1.4.14.jar:/Us
ers/dubin/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.21.1/log4j-to-slf4j-2.21.1.jar:/Users/<USER>
in/.m2/repository/org/apache/logging/log4j/log4j-api/2.21.1/log4j-api-2.21.1.jar:/Users/<USER>/.m2/repository
/org/slf4j/jul-to-slf4j/2.0.11/jul-to-slf4j-2.0.11.jar:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/2.2/sn
akeyaml-2.2.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aop/6.1.3/spring-aop-6.1.3.jar:/Users
/dubin/.m2/repository/org/springframework/spring-beans/6.1.3/spring-beans-6.1.3.jar:/Users/<USER>/.m2/reposit
ory/org/springframework/security/spring-security-config/6.2.1/spring-security-config-6.2.1.jar:/Users/<USER>/
.m2/repository/org/springframework/security/spring-security-core/6.2.1/spring-security-core-6.2.1.jar:/Users
/dubin/.m2/repository/org/springframework/spring-context/6.1.3/spring-context-6.1.3.jar:/Users/<USER>/.m2/rep
ository/org/springframework/security/spring-security-web/6.2.1/spring-security-web-6.2.1.jar:/Users/<USER>/.m
2/repository/org/springframework/spring-expression/6.1.3/spring-expression-6.1.3.jar:/Users/<USER>/.m2/reposi
tory/io/jsonwebtoken/jjwt-api/0.11.5/jjwt-api-0.11.5.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-im
pl/0.11.5/jjwt-impl-0.11.5.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-jackson/0.11.5/jjwt-jackson-
0.11.5.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator-autoconfigure/3.2.2/spr
ing-boot-actuator-autoconfigure-3.2.2.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-a
ctuator/3.2.2/spring-boot-actuator-3.2.2.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jack
son-datatype-jsr310/2.15.3/jackson-datatype-jsr310-2.15.3.jar:/Users/<USER>/.m2/repository/io/micrometer/micr
ometer-observation/1.12.2/micrometer-observation-1.12.2.jar:/Users/<USER>/.m2/repository/io/micrometer/microm
eter-commons/1.12.2/micrometer-commons-1.12.2.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-jakar
ta9/1.12.2/micrometer-jakarta9-1.12.2.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-core/1.12.2/m
icrometer-core-1.12.2.jar:/Users/<USER>/.m2/repository/org/hdrhistogram/HdrHistogram/2.1.12/HdrHistogram-2.1.
12.jar:/Users/<USER>/.m2/repository/org/latencyutils/LatencyUtils/2.0.3/LatencyUtils-2.0.3.jar:/Users/<USER>/.
m2/repository/io/lettuce/lettuce-core/6.3.1.RELEASE/lettuce-core-6.3.1.RELEASE.jar:/Users/<USER>/.m2/reposito
ry/io/netty/netty-common/4.1.105.Final/netty-common-4.1.105.Final.jar:/Users/<USER>/.m2/repository/io/netty/n
etty-handler/4.1.105.Final/netty-handler-4.1.105.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolv
er/4.1.105.Final/netty-resolver-4.1.105.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-buffer/4.1.105.
Final/netty-buffer-4.1.105.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-native-unix-common
/4.1.105.Final/netty-transport-native-unix-common-4.1.105.Final.jar:/Users/<USER>/.m2/repository/io/netty/net
ty-codec/4.1.105.Final/netty-codec-4.1.105.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport/4.
1.105.Final/netty-transport-4.1.105.Final.jar:/Users/<USER>/.m2/repository/io/projectreactor/reactor-core/3.6
.2/reactor-core-3.6.2.jar:/Users/<USER>/.m2/repository/org/reactivestreams/reactive-streams/1.0.4/reactive-st
reams-1.0.4.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-redis/3.2.2/spring-data-red
is-3.2.2.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-keyvalue/3.2.2/spring-data-key
value-3.2.2.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-commons/3.2.2/spring-data-c
ommons-3.2.2.jar:/Users/<USER>/.m2/repository/org/springframework/spring-tx/6.1.3/spring-tx-6.1.3.jar:/Users/
dubin/.m2/repository/org/springframework/spring-oxm/6.1.3/spring-oxm-6.1.3.jar:/Users/<USER>/.m2/repository/o
rg/springframework/spring-context-support/6.1.3/spring-context-support-6.1.3.jar:/Users/<USER>/.m2/repository
/org/slf4j/slf4j-api/2.0.11/slf4j-api-2.0.11.jar:/Users/<USER>/.m2/repository/com/stylishlink/stylishlink-com
mon/1.0.0-SNAPSHOT/stylishlink-common-1.0.0-SNAPSHOT.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed
/tomcat-embed-core/10.1.18/tomcat-embed-core-10.1.18.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed
/tomcat-embed-websocket/10.1.18/tomcat-embed-websocket-10.1.18.jar:/Users/<USER>/.m2/repository/org/springfra
mework/spring-webmvc/6.1.3/spring-webmvc-6.1.3.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomca
t-embed-el/10.1.18/tomcat-embed-el-10.1.18.jar:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate
-validator/8.0.1.Final/hibernate-validator-8.0.1.Final.jar:/Users/<USER>/.m2/repository/jakarta/validation/ja
karta.validation-api/3.0.2/jakarta.validation-api-3.0.2.jar:/Users/<USER>/.m2/repository/org/jboss/logging/jb
oss-logging/3.5.3.Final/jboss-logging-3.5.3.Final.jar:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.
6.0/classmate-1.6.0.jar:/Users/<USER>/.m2/repository/org/mongodb/mongodb-driver-sync/4.11.1/mongodb-driver-sy
nc-4.11.1.jar:/Users/<USER>/.m2/repository/org/mongodb/bson/4.11.1/bson-4.11.1.jar:/Users/<USER>/.m2/repositor
y/org/mongodb/mongodb-driver-core/4.11.1/mongodb-driver-core-4.11.1.jar:/Users/<USER>/.m2/repository/org/mong
odb/bson-record-codec/4.11.1/bson-record-codec-4.11.1.jar:/Users/<USER>/.m2/repository/org/springframework/da
ta/spring-data-mongodb/4.2.2/spring-data-mongodb-4.2.2.jar:/Users/<USER>/.m2/repository/org/apache/commons/co
mmons-lang3/3.14.0/commons-lang3-3.14.0.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-collectio
ns4/4.4/commons-collections4-4.4.jar:/Users/<USER>/.m2/repository/commons-io/commons-io/2.15.1/commons-io-2.1
5.1.jar:/Users/<USER>/.m2/repository/com/google/guava/guava/32.1.3-jre/guava-32.1.3-jre.jar:/Users/<USER>/.m2/
repository/com/google/guava/failureaccess/1.0.1/failureaccess-1.0.1.jar:/Users/<USER>/.m2/repository/com/goog
le/guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/listenablefuture-9999.0-empty-to-avoid-c
onflict-with-guava.jar:/Users/<USER>/.m2/repository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar:/U
sers/dubin/.m2/repository/org/checkerframework/checker-qual/3.37.0/checker-qual-3.37.0.jar:/Users/<USER>/.m2/
repository/com/google/errorprone/error_prone_annotations/2.21.1/error_prone_annotations-2.21.1.jar:/Users/<USER>
bin/.m2/repository/com/google/j2objc/j2objc-annotations/2.8/j2objc-annotations-2.8.jar:/Users/<USER>/.m2/repo
sitory/cn/hutool/hutool-all/5.8.25/hutool-all-5.8.25.jar:/Users/<USER>/.m2/repository/org/mapstruct/mapstruct
/1.5.5.Final/mapstruct-1.5.5.Final.jar:/Users/<USER>/.m2/repository/org/springframework/spring-core/6.1.3/spr
ing-core-6.1.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/6.1.3/spring-jcl-6.1.3.jar com
.stylishlink.gateway.GatewayServiceApplication
  502 99400 99289   0  6:39下午 ttys054    0:00.01 grep java
(base) dubin@admindeMBP StylishLink %
```

---

通过检查进程，我们可以看到以下服务已经在运行：

1. Eureka服务器 - 服务注册和发现中心
2. 用户服务 (user-service) - 端口8081
3. 衣橱服务 (wardrobe-service) - 端口8082 
4. 推荐服务 (recommendation-service) - 端口8083
5. 网关服务 (gateway-service) - 端口8080

现在让我们通过网关服务测试系统中的其他服务：

---

```bash
curl http://localhost:8080/actuator/health
```

```
{"status":"UP","components":{"clientConfigServer":{"status":"UNKNOWN","details":{"error":"no property source
s located"}},"discoveryComposite":{"status":"UP","components":{"discoveryClient":{"status":"UP","details":{"
services":["wardrobe-service","recommendation-service","user-service","gateway-service"]}},"eureka":{"descri
ption":"Remote status from Eureka server","status":"UP","details":{"applications":{"WARDROBE-SERVICE":1,"REC
OMMENDATION-SERVICE":1,"GATEWAY-SERVICE":1,"USER-SERVICE":1}}}}},"diskSpace":{"status":"UP","details":{"tota
l":250685575168,"free":11401940992,"threshold":10485760,"path":"/Users/<USER>/work/AI/StylishLink/backend/gat
eway-service/.","exists":true}},"mongo":{"status":"UP","details":{"maxWireVersion":17}},"ping":{"status":"UP
"},"reactiveDiscoveryClients":{"status":"UP","components":{"Simple Reactive Discovery Client":{"status":"UP"
,"details":{"services":[]}},"Spring Cloud Eureka Reactive Discovery Client":{"status":"UP","details":{"servi
ces":["wardrobe-service","recommendation-service","user-service","gateway-service"]}}}},"redis":{"status":"U
P","details":{"version":"6.2.17"}},"refreshScope":{"status":"UP"}}}%

```

---

非常好！网关服务已经正常运行，并且连接到了Eureka服务器。我们可以看到，所有服务都已注册到Eureka，包括：
- gateway-service (网关服务)
- user-service (用户服务)
- wardrobe-service (衣橱服务)
- recommendation-service (推荐服务)

现在让我们通过网关访问用户服务的健康检查端点：

---

```bash
curl http://localhost:8080/api/user/actuator/health
```

```
(base) dubin@admindeMBP StylishLink % 

```

---

没有返回结果。让我们直接访问用户服务：

---

```bash
curl http://localhost:8081/api/user/actuator/health
```

```
{"status":"UP","components":{"discoveryComposite":{"status":"UP","components":{"discoveryClient":{"status":"
UP","details":{"services":["wardrobe-service","user-service","recommendation-service","gateway-service"]}},"
eureka":{"description":"Remote status from Eureka server","status":"UP","details":{"applications":{"WARDROBE
-SERVICE":1,"RECOMMENDATION-SERVICE":1,"GATEWAY-SERVICE":1,"USER-SERVICE":1}}}}},"diskSpace":{"status":"UP",
"details":{"total":250685575168,"free":12473094144,"threshold":10485760,"path":"/Users/<USER>/work/AI/Stylish
Link/backend/user-service/.","exists":true}},"mongo":{"status":"UP","details":{"maxWireVersion":17}},"ping":
{"status":"UP"},"redis":{"status":"UP","details":{"version":"6.2.17"}},"refreshScope":{"status":"UP"}}}%

```

---

很好，用户服务也正常运行。网关服务无法转发的原因可能是网关的路由配置问题。让我们检查一下网关的路由配置：

---

<details>
            <summary>Searched codebase "RouteConfig gateway route configuration" • **14** results</summary>
        
| File | Lines |
|------|-------|
| `backend/gateway-service/src/main/java/com/stylishlink/gateway/config/RouteConfig.java` | L1-42 |
| `backend/gateway-service/src/main/resources/application.yml` | L1-54 |
| `backend/gateway-service/target/classes/application.yml` | L1-54 |
| `backend/gateway-service/pom.xml` | L1-58 |
| `backend/gateway-service/src/main/java/com/stylishlink/gateway/config/SecurityConfig.java` | L1-33 |
| `backend/gateway-service/src/main/java/com/stylishlink/gateway/config/SecurityConfig.java` | L33-48 |
| `backend/gateway-service/src/main/java/com/stylishlink/gateway/GatewayServiceApplication.java` | L1-17 |
| `backend/gateway-service/src/main/java/com/stylishlink/gateway/filter/JwtAuthenticationFilter.java` | L27-63 |
| `backend/gateway-service/src/main/java/com/stylishlink/gateway/filter/JwtAuthenticationFilter.java` | L1-27 |
| `backend/gateway-service/pom.xml` | L58-103 |
| `backend/gateway-service/target/maven-status/maven-compiler-plugin/compile/default-compile/createdFiles.lst` | L1-5 |
| `backend/gateway-service/pom.xml` | L103-119 |
| `backend/gateway-service/target/maven-status/maven-compiler-plugin/compile/default-compile/inputFiles.lst` | L1-5 |
| `backend/gateway-service/src/main/java/com/stylishlink/gateway/filter/JwtAuthenticationFilter.java` | L63-90 |

</details>

---

通过检查路由配置，我们发现RouteConfig.java文件中定义了路由，但路由规则可能存在问题。具体来说，rewritePath配置可能导致服务内的路径被重写，但没有考虑服务本身已经设置了上下文路径（例如/api/user）。让我们修改路由配置：

---

```diff
                  .route("user-service", r -> r
                          .path("/api/user/**")
-                         .filters(f -> f
-                                 .rewritePath("/api/user/(?<segment>.*)", "/${segment}")
-                                 .addRequestHeader("X-Gateway-Source", "API-Gateway"))
                          .uri("lb://user-service"))
                  
                  // 衣橱服务路由
                  .route("wardrobe-service", r -> r
                          .path("/api/wardrobe/**")
-                         .filters(f -> f
-                                 .rewritePath("/api/wardrobe/(?<segment>.*)", "/${segment}")
-                                 .addRequestHeader("X-Gateway-Source", "API-Gateway"))
                          .uri("lb://wardrobe-service"))
                  
                  // 推荐服务路由
                  .route("recommendation-service", r -> r
                          .path("/api/recommendation/**")
-                         .filters(f -> f
-                                 .rewritePath("/api/recommendation/(?<segment>.*)", "/${segment}")
-                                 .addRequestHeader("X-Gateway-Source", "API-Gateway"))
                          .uri("lb://recommendation-service"))
                  .build();
```

---

现在让我们重新启动网关服务，应用新的路由配置：

---

```bash
cd /Users/<USER>/work/AI/StylishLink/backend/gateway-service && mvn spring-boot:run
```

```
2025-03-14T18:40:42.885+08:00  WARN 372 --- [gateway-service] [           main] trationDelegate$BeanPostProc
essorChecker : Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsB
indHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligibl
e for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [lbRe
stClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-03-14T18:40:42.909+08:00  WARN 372 --- [gateway-service] [           main] trationDelegate$BeanPostProc
essorChecker : Bean 'spring.cloud.loadbalancer-org.springframework.cloud.client.loadbalancer.LoadBalancerCli
entsProperties' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerClientsProperties] is not
 eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is 
this bean getting eagerly injected into a currently created BeanPostProcessor [lbRestClientPostProcessor]? C
heck the corresponding BeanPostProcessor declaration and its dependencies.
2025-03-14T18:40:42.932+08:00  WARN 372 --- [gateway-service] [           main] trationDelegate$BeanPostProc
essorChecker : Bean 'default.org.springframework.cloud.loadbalancer.config.LoadBalancerAutoConfiguration.Loa
dBalancerClientSpecification' of type [org.springframework.cloud.loadbalancer.annotation.LoadBalancerClientS
pecification] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for
 auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [lbRestCli
entPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-03-14T18:40:43.071+08:00  WARN 372 --- [gateway-service] [           main] trationDelegate$BeanPostProc
essorChecker : Bean 'default.org.springframework.cloud.loadbalancer.config.BlockingLoadBalancerClientAutoCon
figuration.LoadBalancerClientSpecification' of type [org.springframework.cloud.loadbalancer.annotation.LoadB
alancerClientSpecification] is not eligible for getting processed by all BeanPostProcessors (for example: no
t eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProces
sor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-03-14T18:40:43.077+08:00  WARN 372 --- [gateway-service] [           main] trationDelegate$BeanPostProc
essorChecker : Bean 'default.org.springframework.cloud.netflix.eureka.loadbalancer.LoadBalancerEurekaAutoCon
figuration.LoadBalancerClientSpecification' of type [org.springframework.cloud.loadbalancer.annotation.LoadB
alancerClientSpecification] is not eligible for getting processed by all BeanPostProcessors (for example: no
t eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProces
sor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-03-14T18:40:43.080+08:00  WARN 372 --- [gateway-service] [           main] trationDelegate$BeanPostProc
essorChecker : Bean 'loadBalancerClientFactory' of type [org.springframework.cloud.loadbalancer.support.Load
BalancerClientFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eli
gible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [
lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-03-14T18:40:43.087+08:00  WARN 372 --- [gateway-service] [           main] trationDelegate$BeanPostProc
essorChecker : Bean 'blockingLoadBalancerClient' of type [org.springframework.cloud.loadbalancer.blocking.cl
ient.BlockingLoadBalancerClient] is not eligible for getting processed by all BeanPostProcessors (for exampl
e: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostP
rocessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependen
cies.
2025-03-14T18:40:43.105+08:00  WARN 372 --- [gateway-service] [           main] trationDelegate$BeanPostProc
essorChecker : Bean 'loadBalancerServiceInstanceCookieTransformer' of type [org.springframework.cloud.loadba
lancer.core.LoadBalancerServiceInstanceCookieTransformer] is not eligible for getting processed by all BeanP
ostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a cu
rrently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor dec
laration and its dependencies.
2025-03-14T18:40:43.107+08:00  WARN 372 --- [gateway-service] [           main] trationDelegate$BeanPostProc
essorChecker : Bean 'xForwarderHeadersTransformer' of type [org.springframework.cloud.loadbalancer.blocking.
XForwardedHeadersTransformer] is not eligible for getting processed by all BeanPostProcessors (for example: 
not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProc
essor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencie
s.
2025-03-14T18:40:43.109+08:00  WARN 372 --- [gateway-service] [           main] trationDelegate$BeanPostProc
essorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration' of type [o
rg.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration] is not eligible for getting proc
essed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly 
injected into a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding Bea
nPostProcessor declaration and its dependencies.
2025-03-14T18:40:43.112+08:00  WARN 372 --- [gateway-service] [           main] trationDelegate$BeanPostProc
essorChecker : Bean 'loadBalancerRequestFactory' of type [org.springframework.cloud.client.loadbalancer.Load
BalancerRequestFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not el
igible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor 
[lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-03-14T18:40:43.114+08:00  WARN 372 --- [gateway-service] [           main] trationDelegate$BeanPostProc
essorChecker : Bean 'loadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.LoadBal
ancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible
 for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [lbRes
tClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-03-14T18:40:43.117+08:00  WARN 372 --- [gateway-service] [           main] trationDelegate$BeanPostProc
essorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAut
oConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcesso
rAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligib
le for auto-proxying). The currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcesso
r] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-03-14T18:40:43.119+08:00  WARN 372 --- [gateway-service] [           main] trationDelegate$BeanPostProc
essorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAut
oConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalan
cer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not
 eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is 
this bean getting eagerly injected into a currently created BeanPostProcessor [loadBalancerWebClientBuilderB
eanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-03-14T18:40:43.123+08:00  WARN 372 --- [gateway-service] [           main] trationDelegate$BeanPostProc
essorChecker : Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.
client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting proces
sed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly in
jected into a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the
 corresponding BeanPostProcessor declaration and its dependencies.
2025-03-14T18:40:43.825+08:00  INFO 372 --- [gateway-service] [           main] o.s.b.w.embedded.tomcat.Tomc
atWebServer  : Tomcat initialized with port 8080 (http)
2025-03-14T18:40:43.852+08:00  INFO 372 --- [gateway-service] [           main] o.apache.catalina.core.Stand
ardService   : Starting service [Tomcat]
2025-03-14T18:40:43.853+08:00  INFO 372 --- [gateway-service] [           main] o.apache.catalina.core.Stand
ardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.18]
2025-03-14T18:40:44.467+08:00  INFO 372 --- [gateway-service] [           main] ctiveUserDetailsServiceAutoC
onfiguration : 

Using generated security password: 52dd5b56-d312-4f4d-9d8c-529b8efffedf

2025-03-14T18:40:50.450+08:00  INFO 372 --- [gateway-service] [           main] o.s.c.g.r.RouteDefinitionRou
teLocator    : Loaded RoutePredicateFactory [After]
2025-03-14T18:40:50.450+08:00  INFO 372 --- [gateway-service] [           main] o.s.c.g.r.RouteDefinitionRou
teLocator    : Loaded RoutePredicateFactory [Before]
2025-03-14T18:40:50.450+08:00  INFO 372 --- [gateway-service] [           main] o.s.c.g.r.RouteDefinitionRou
teLocator    : Loaded RoutePredicateFactory [Between]
2025-03-14T18:40:50.450+08:00  INFO 372 --- [gateway-service] [           main] o.s.c.g.r.RouteDefinitionRou
teLocator    : Loaded RoutePredicateFactory [Cookie]
2025-03-14T18:40:50.450+08:00  INFO 372 --- [gateway-service] [           main] o.s.c.g.r.RouteDefinitionRou
teLocator    : Loaded RoutePredicateFactory [Header]
2025-03-14T18:40:50.451+08:00  INFO 372 --- [gateway-service] [           main] o.s.c.g.r.RouteDefinitionRou
teLocator    : Loaded RoutePredicateFactory [Host]
2025-03-14T18:40:50.451+08:00  INFO 372 --- [gateway-service] [           main] o.s.c.g.r.RouteDefinitionRou
teLocator    : Loaded RoutePredicateFactory [Method]
2025-03-14T18:40:50.452+08:00  INFO 372 --- [gateway-service] [           main] o.s.c.g.r.RouteDefinitionRou
teLocator    : Loaded RoutePredicateFactory [Path]
2025-03-14T18:40:50.452+08:00  INFO 372 --- [gateway-service] [           main] o.s.c.g.r.RouteDefinitionRou
teLocator    : Loaded RoutePredicateFactory [Query]
2025-03-14T18:40:50.453+08:00  INFO 372 --- [gateway-service] [           main] o.s.c.g.r.RouteDefinitionRou
teLocator    : Loaded RoutePredicateFactory [ReadBody]
2025-03-14T18:40:50.453+08:00  INFO 372 --- [gateway-service] [           main] o.s.c.g.r.RouteDefinitionRou
teLocator    : Loaded RoutePredicateFactory [RemoteAddr]
2025-03-14T18:40:50.453+08:00  INFO 372 --- [gateway-service] [           main] o.s.c.g.r.RouteDefinitionRou
teLocator    : Loaded RoutePredicateFactory [XForwardedRemoteAddr]
2025-03-14T18:40:50.453+08:00  INFO 372 --- [gateway-service] [           main] o.s.c.g.r.RouteDefinitionRou
teLocator    : Loaded RoutePredicateFactory [Weight]
2025-03-14T18:40:50.453+08:00  INFO 372 --- [gateway-service] [           main] o.s.c.g.r.RouteDefinitionRou
teLocator    : Loaded RoutePredicateFactory [CloudFoundryRouteService]
2025-03-14T18:40:51.004+08:00  INFO 372 --- [gateway-service] [localhost:27017] org.mongodb.driver.cluster  
             : Monitor thread successfully connected to server with description ServerDescription{address=lo
calhost:27017, type=STANDALONE, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=17, maxDocumentSi
ze=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=108657541}
2025-03-14T18:40:51.058+08:00  INFO 372 --- [gateway-service] [           main] org.mongodb.driver.client   
             : MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync|spring-boot", "version"
: "4.11.1"}, "os": {"type": "Darwin", "name": "Mac OS X", "architecture": "x86_64", "version": "13.7.4"}, "p
latform": "Java/Homebrew/23.0.1"} created with settings MongoClientSettings{readPreference=primary, writeCon
cern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=Re
adConcern{level=null}, credential=null, transportSettings=null, streamFactoryFactory=null, commandListeners=
[io.micrometer.core.instrument.binder.mongodb.MongoMetricsCommandListener@46b55a0e], codecRegistry=Providers
CodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObject
CodecProvider{}, DocumentCodecProvider{}, CollectionCodecProvider{}, IterableCodecProvider{}, MapCodecProvid
er{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, B
sonCodecProvider{}, EnumCodecProvider{}, com.mongodb.client.model.mql.ExpressionCodecProvider@7abd75fc, com.
mongodb.Jep395RecordCodecProvider@31b67d61, com.mongodb.KotlinCodecProvider@2b66bf1c]}, loggerSettings=Logge
rSettings{maxDocumentLength=1000}, clusterSettings={hosts=[localhost:27017], srvServiceName=mongodb, mode=SI
NGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[
]', serverSelectionTimeout='30000 ms', localThreshold='15 ms'}, socketSettings=SocketSettings{connectTimeout
MS=10000, readTimeoutMS=0, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=n
ull, password=null}}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, re
ceiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, connect
ionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS
=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolList
eners=[io.micrometer.core.instrument.binder.mongodb.MongoMetricsConnectionPoolListener@47eff554], maxConnect
ing=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverListene
rs='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, 
context=null}, applicationName='null', compressorList=[], uuidRepresentation=JAVA_LEGACY, serverApi=null, au
toEncryptionSettings=null, dnsClient=null, inetAddressResolver=null, contextProvider=null}
2025-03-14T18:40:51.468+08:00  INFO 372 --- [gateway-service] [           main] o.s.b.a.e.web.EndpointLinksR
esolver      : Exposing 3 endpoint(s) beneath base path '/actuator'
2025-03-14T18:40:52.157+08:00  INFO 372 --- [gateway-service] [           main] DiscoveryClientOptionalArgsC
onfiguration : Eureka HTTP Client uses RestTemplate.
2025-03-14T18:40:52.445+08:00  WARN 372 --- [gateway-service] [           main] iguration$LoadBalancerCaffei
neWarnLogger : Spring Cloud LoadBalancer is currently working with the default cache. While this cache imple
mentation is useful for development and tests, it's recommended to use Caffeine cache in production.You can 
switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to 
the classpath.
2025-03-14T18:40:52.567+08:00  INFO 372 --- [gateway-service] [           main] o.s.c.n.eureka.InstanceInfoF
actory       : Setting initial instance status as: STARTING
2025-03-14T18:40:52.984+08:00  INFO 372 --- [gateway-service] [           main] com.netflix.discovery.Discov
eryClient    : Initializing Eureka in region us-east-1
2025-03-14T18:40:53.089+08:00  INFO 372 --- [gateway-service] [           main] c.n.d.s.r.aws.ConfigClusterR
esolver      : Resolving eureka endpoints via configuration
2025-03-14T18:40:53.148+08:00  INFO 372 --- [gateway-service] [           main] com.netflix.discovery.Discov
eryClient    : Disable delta property : false
2025-03-14T18:40:53.148+08:00  INFO 372 --- [gateway-service] [           main] com.netflix.discovery.Discov
eryClient    : Single vip registry refresh property : null
2025-03-14T18:40:53.149+08:00  INFO 372 --- [gateway-service] [           main] com.netflix.discovery.Discov
eryClient    : Force full registry fetch : false
2025-03-14T18:40:53.149+08:00  INFO 372 --- [gateway-service] [           main] com.netflix.discovery.Discov
eryClient    : Application is null : false
2025-03-14T18:40:53.149+08:00  INFO 372 --- [gateway-service] [           main] com.netflix.discovery.Discov
eryClient    : Registered Applications size is zero : true
2025-03-14T18:40:53.149+08:00  INFO 372 --- [gateway-service] [           main] com.netflix.discovery.Discov
eryClient    : Application version is -1: true
2025-03-14T18:40:53.149+08:00  INFO 372 --- [gateway-service] [           main] com.netflix.discovery.Discov
eryClient    : Getting all instance registry info from the eureka server
2025-03-14T18:40:53.927+08:00  INFO 372 --- [gateway-service] [           main] com.netflix.discovery.Discov
eryClient    : The response status is 200
2025-03-14T18:40:53.940+08:00  INFO 372 --- [gateway-service] [           main] com.netflix.discovery.Discov
eryClient    : Starting heartbeat executor: renew interval is: 30
2025-03-14T18:40:53.957+08:00  INFO 372 --- [gateway-service] [           main] c.n.discovery.InstanceInfoRe
plicator     : InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-03-14T18:40:53.990+08:00  INFO 372 --- [gateway-service] [           main] com.netflix.discovery.Discov
eryClient    : Discovery Client initialized at timestamp 1741948853990 with initial instances count: 4
2025-03-14T18:40:54.004+08:00  INFO 372 --- [gateway-service] [           main] o.s.c.n.e.s.EurekaServiceReg
istry        : Registering application GATEWAY-SERVICE with eureka with status UP
2025-03-14T18:40:54.005+08:00  INFO 372 --- [gateway-service] [           main] com.netflix.discovery.Discov
eryClient    : Saw local status change event StatusChangeEvent [timestamp=1741948854005, current=UP, previou
s=STARTING]
2025-03-14T18:40:54.007+08:00  INFO 372 --- [gateway-service] [nfoReplicator-0] com.netflix.discovery.Discov
eryClient    : DiscoveryClient_GATEWAY-SERVICE/gateway-service:8080: registering service...
2025-03-14T18:40:54.067+08:00  INFO 372 --- [gateway-service] [nfoReplicator-0] com.netflix.discovery.Discov
eryClient    : DiscoveryClient_GATEWAY-SERVICE/gateway-service:8080 - registration status: 204
2025-03-14T18:40:54.301+08:00  INFO 372 --- [gateway-service] [           main] o.s.c.n.e.s.EurekaServiceReg
istry        : Unregistering application GATEWAY-SERVICE with eureka with status DOWN
2025-03-14T18:40:54.302+08:00  INFO 372 --- [gateway-service] [           main] com.netflix.discovery.Discov
eryClient    : Saw local status change event StatusChangeEvent [timestamp=1741948854302, current=DOWN, previ
ous=UP]
2025-03-14T18:40:54.302+08:00  INFO 372 --- [gateway-service] [nfoReplicator-0] com.netflix.discovery.Discov
eryClient    : DiscoveryClient_GATEWAY-SERVICE/gateway-service:8080: registering service...
2025-03-14T18:40:54.304+08:00  WARN 372 --- [gateway-service] [           main] onfigReactiveWebServerApplic
ationContext : Exception encountered during context initialization - cancelling refresh attempt: org.springf
ramework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'
2025-03-14T18:40:54.311+08:00  INFO 372 --- [gateway-service] [nfoReplicator-0] com.netflix.discovery.Discov
eryClient    : DiscoveryClient_GATEWAY-SERVICE/gateway-service:8080 - registration status: 204
2025-03-14T18:40:54.342+08:00  INFO 372 --- [gateway-service] [           main] com.netflix.discovery.Discov
eryClient    : Shutting down DiscoveryClient ...
2025-03-14T18:40:57.348+08:00  INFO 372 --- [gateway-service] [           main] com.netflix.discovery.Discov
eryClient    : Unregistering ...
2025-03-14T18:40:57.954+08:00  INFO 372 --- [gateway-service] [           main] com.netflix.discovery.Discov
eryClient    : DiscoveryClient_GATEWAY-SERVICE/gateway-service:8080 - deregister  status: 200
2025-03-14T18:40:57.960+08:00  INFO 372 --- [gateway-service] [           main] com.netflix.discovery.Discov
eryClient    : Completed shut down of DiscoveryClient
2025-03-14T18:40:57.993+08:00  INFO 372 --- [gateway-service] [           main] .s.b.a.l.ConditionEvaluation
ReportLogger : 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with '
debug' enabled.
2025-03-14T18:40:58.038+08:00 ERROR 372 --- [gateway-service] [           main] o.s.b.d.LoggingFailureAnalys
isReporter   : 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8080 was already in use.

Action:

Identify and stop the process that's listening on port 8080 or configure this application to listen on anoth
er port.
```

---





---



---

