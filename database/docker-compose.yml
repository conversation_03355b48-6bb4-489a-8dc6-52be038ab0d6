version: '3.8'

services:
  mongodb:
    image: mongo:6.0
    container_name: stylishlink-mongodb
    restart: always
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      # MongoDB数据库密码
      MONGO_INITDB_ROOT_PASSWORD: stylishlink_secure_pw
      MONGO_INITDB_DATABASE: stylishlink
    volumes:
      - mongodb_data:/data/db
      - ./mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    command: ["--auth"]

  mongo-express:
    image: mongo-express:latest
    container_name: stylishlink-mongo-express
    restart: always
    ports:
      - "8181:8081"
    environment:
      ME_CONFIG_MONGODB_ADMINUSERNAME: admin
      # MongoDB Express连接数据库的密码
      ME_CONFIG_MONGODB_ADMINPASSWORD: stylishlink_secure_pw
      ME_CONFIG_MONGODB_URL: ***************************************************/
      ME_CONFIG_BASICAUTH_USERNAME: admin
      # Mongo Express网页界面的密码
      ME_CONFIG_BASICAUTH_PASSWORD: stylishlink_secure_pw
    depends_on:
      - mongodb

volumes:
  mongodb_data:
    name: stylishlink-mongodb-data 