// 创建应用数据库用户
db.createUser({
  user: "stylishlink_app",
  pwd: "stylishlink_app_pw",
  roles: [
    { role: "readWrite", db: "stylishlink" }
  ]
});

// 切换到应用数据库
db = db.getSiblingDB("stylishlink");

// 创建核心集合
db.createCollection("users");
db.createCollection("clothing");
db.createCollection("accessories");
db.createCollection("outfits");
db.createCollection("wuxing_mappings");
db.createCollection("weather_records");
db.createCollection("recommendations");

// 创建索引
db.users.createIndex({ "userId": 1 }, { unique: true });
db.users.createIndex({ "openId": 1 }, { unique: true, sparse: true });
db.clothing.createIndex({ "userId": 1 });
db.clothing.createIndex({ "category": 1 });
db.clothing.createIndex({ "tags": 1 });
db.accessories.createIndex({ "userId": 1 });
db.accessories.createIndex({ "category": 1 });
db.outfits.createIndex({ "userId": 1 });
db.outfits.createIndex({ "createdAt": -1 });
db.recommendations.createIndex({ "userId": 1, "createdAt": -1 });
db.weather_records.createIndex({ "cityCode": 1, "date": 1 }, { unique: true });

// 初始化五行属性映射集合
db.wuxing_mappings.insertMany([
  {
    "element": "木",
    "colors": ["绿色", "青色", "蓝绿色"],
    "materials": ["棉", "麻", "亚麻"],
    "styles": ["自然", "舒适", "休闲"]
  },
  {
    "element": "火",
    "colors": ["红色", "紫色", "粉色"],
    "materials": ["丝质", "绸缎", "蕾丝"],
    "styles": ["热情", "明亮", "活力"]
  },
  {
    "element": "土",
    "colors": ["黄色", "棕色", "米色"],
    "materials": ["羊毛", "羊绒", "灯芯绒"],
    "styles": ["稳重", "朴实", "传统"]
  },
  {
    "element": "金",
    "colors": ["白色", "金色", "银色"],
    "materials": ["金属", "人造革", "皮革"],
    "styles": ["精致", "高贵", "现代"]
  },
  {
    "element": "水",
    "colors": ["黑色", "深蓝色", "灰色"],
    "materials": ["尼龙", "涤纶", "丝绒"],
    "styles": ["神秘", "内敛", "沉稳"]
  }
]); 