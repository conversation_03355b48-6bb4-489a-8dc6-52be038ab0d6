// 用于导入示例数据的脚本
// 使用方法：mongosh ************************************************************************ --file sample-data.js

// 生成随机ID
function generateId() {
  return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
}

// 获取当前时间
const now = new Date();

// 创建示例用户
const users = [
  {
    "_id": ObjectId(),
    "userId": "user_" + generateId(),
    "openId": "wx_" + generateId(),
    "nickname": "时尚达人",
    "avatar": "https://example.com/avatar1.jpg",
    "gender": 2, // 女性
    "registerDate": now,
    "lastLoginDate": now,
    "preferences": {
      "favoriteColors": ["红色", "蓝色", "黑色"],
      "favoriteStyles": ["休闲", "正式"],
      "seasonalPreferences": {
        "spring": ["清新", "明亮"],
        "summer": ["轻薄", "凉爽"],
        "autumn": ["层次", "温暖"],
        "winter": ["保暖", "厚重"]
      }
    },
    "bodyInfo": {
      "height": 165,
      "weight": 55,
      "bodyShape": "沙漏型",
      "skinTone": "暖色调"
    },
    "wuxingProfile": {
      "birthDate": new Date("1990-05-15T08:00:00Z"),
      "lunarBirth": {
        "year": "庚午",
        "month": "戊子",
        "day": "己巳",
        "hour": "壬申"
      },
      "elementAnalysis": {
        "wood": 3,
        "fire": 1,
        "earth": 0,
        "metal": 2,
        "water": 2
      },
      "favorable": ["木", "水"],
      "unfavorable": ["火"],
      "eightChar": "庚午 戊子 己巳 壬申",
      "updated": now
    },
    "settings": {
      "pushNotification": true,
      "dailyRecommendation": true,
      "privacyLevel": 2,
      "language": "zh_CN"
    },
    "createdAt": now,
    "updatedAt": now
  }
];

// 创建示例衣物
const clothing = [
  {
    "_id": ObjectId(),
    "userId": users[0].userId,
    "name": "白色T恤",
    "imageUrl": "https://example.com/white-tshirt.jpg",
    "thumbnailUrl": "https://example.com/white-tshirt-thumb.jpg",
    "category": "上衣",
    "subCategory": "T恤",
    "color": "白色",
    "material": "棉",
    "season": ["春", "夏"],
    "occasions": ["休闲", "日常"],
    "tags": ["基础款", "百搭"],
    "wuxingElements": ["金"],
    "features": {
      "pattern": "纯色",
      "neckline": "圆领",
      "sleeve": "短袖"
    },
    "frequency": 5,
    "favorite": true,
    "lastWorn": now,
    "wearCount": 10,
    "createdAt": now,
    "updatedAt": now
  },
  {
    "_id": ObjectId(),
    "userId": users[0].userId,
    "name": "蓝色牛仔裤",
    "imageUrl": "https://example.com/blue-jeans.jpg",
    "thumbnailUrl": "https://example.com/blue-jeans-thumb.jpg",
    "category": "下装",
    "subCategory": "牛仔裤",
    "color": "蓝色",
    "material": "牛仔布",
    "season": ["春", "秋"],
    "occasions": ["休闲", "日常"],
    "tags": ["百搭", "经典"],
    "wuxingElements": ["水"],
    "features": {
      "pattern": "纯色",
      "fit": "直筒",
      "length": "长裤"
    },
    "frequency": 4,
    "favorite": true,
    "lastWorn": now,
    "wearCount": 8,
    "createdAt": now,
    "updatedAt": now
  }
];

// 创建示例饰品
const accessories = [
  {
    "_id": ObjectId(),
    "userId": users[0].userId,
    "name": "银色耳环",
    "imageUrl": "https://example.com/silver-earrings.jpg",
    "thumbnailUrl": "https://example.com/silver-earrings-thumb.jpg",
    "category": "耳环",
    "color": "银色",
    "material": "925银",
    "season": ["春", "夏", "秋", "冬"],
    "occasions": ["休闲", "正式"],
    "tags": ["基础款", "百搭"],
    "wuxingElements": ["金"],
    "frequency": 3,
    "favorite": true,
    "lastWorn": now,
    "wearCount": 5,
    "createdAt": now,
    "updatedAt": now
  }
];

// 创建示例搭配
const outfits = [
  {
    "_id": ObjectId(),
    "userId": users[0].userId,
    "name": "日常休闲搭配",
    "items": [
      {
        "type": "clothing",
        "itemId": clothing[0]._id.toString(),
        "category": "上衣",
        "name": clothing[0].name
      },
      {
        "type": "clothing",
        "itemId": clothing[1]._id.toString(),
        "category": "下装",
        "name": clothing[1].name
      },
      {
        "type": "accessory",
        "itemId": accessories[0]._id.toString(),
        "category": "耳环",
        "name": accessories[0].name
      }
    ],
    "occasion": "日常",
    "season": ["春", "秋"],
    "wuxingBalance": {
      "wood": 0,
      "fire": 0,
      "earth": 0,
      "metal": 2,
      "water": 1
    },
    "rating": 4.5,
    "imageUrl": "https://example.com/outfit1.jpg",
    "favorite": true,
    "wearCount": 3,
    "lastWorn": now,
    "createdAt": now,
    "updatedAt": now
  }
];

// 创建示例天气记录
const weatherRecords = [
  {
    "_id": ObjectId(),
    "cityCode": "101010100", // 北京
    "cityName": "北京",
    "date": new Date(),
    "weather": "晴",
    "temperature": {
      "min": 18,
      "max": 28
    },
    "humidity": 65,
    "wind": {
      "direction": "东北风",
      "power": "3级"
    },
    "indexes": {
      "clothing": {
        "level": "舒适",
        "description": "建议着薄型T恤或短袖衬衫等夏季服装。"
      },
      "uv": "强",
      "comfort": "较舒适"
    },
    "createdAt": now,
    "updatedAt": now
  }
];

// 创建示例推荐
const recommendations = [
  {
    "_id": ObjectId(),
    "userId": users[0].userId,
    "date": now,
    "weather": {
      "cityName": "北京",
      "weather": "晴",
      "temperature": {
        "min": 18,
        "max": 28
      }
    },
    "wuxingRecommendation": {
      "favorable": ["木", "水"],
      "unfavorable": ["火"]
    },
    "outfits": [
      {
        "name": "舒适日常搭配",
        "items": [
          {
            "type": "clothing",
            "itemId": clothing[0]._id.toString(),
            "category": "上衣",
            "name": clothing[0].name
          },
          {
            "type": "clothing",
            "itemId": clothing[1]._id.toString(),
            "category": "下装",
            "name": clothing[1].name
          },
          {
            "type": "accessory",
            "itemId": accessories[0]._id.toString(),
            "category": "耳环",
            "name": accessories[0].name
          }
        ],
        "occasion": "日常",
        "wuxingScore": 85,
        "weatherScore": 90,
        "totalScore": 87,
        "explanation": "这套搭配适合今天温暖的天气，同时符合您的五行偏好。"
      }
    ],
    "feedback": {
      "selected": 0,
      "rating": 4.5,
      "comment": "很合适的推荐"
    },
    "createdAt": now,
    "updatedAt": now
  }
];

// 插入示例数据
db.users.insertMany(users);
db.clothing.insertMany(clothing);
db.accessories.insertMany(accessories);
db.outfits.insertMany(outfits);
db.weather_records.insertMany(weatherRecords);
db.recommendations.insertMany(recommendations);

print("示例数据已成功导入！");
print(`导入了 ${users.length} 条用户数据`);
print(`导入了 ${clothing.length} 条衣物数据`);
print(`导入了 ${accessories.length} 条饰品数据`);
print(`导入了 ${outfits.length} 条搭配数据`);
print(`导入了 ${weatherRecords.length} 条天气记录`);
print(`导入了 ${recommendations.length} 条推荐数据`); 