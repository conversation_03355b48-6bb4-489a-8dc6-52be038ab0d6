# StylishLink 数据库设置指南

本目录包含StylishLink应用的MongoDB数据库设置。

## 环境要求

- Docker
- Docker Compose

## Docker安装与启动

### macOS环境

1. 如果尚未安装Docker Desktop，请从[Docker官网](https://www.docker.com/products/docker-desktop/)下载并安装。

2. 启动Docker Desktop：
   - 在应用程序文件夹中找到并启动Docker Desktop
   - 或通过Spotlight搜索(`Cmd+Space`)，输入"Docker"并启动

3. 确认Docker已启动：
   ```bash
   docker --version
   docker ps
   ```

### Linux环境

1. 安装Docker：
   ```bash
   sudo apt update
   sudo apt install docker.io docker-compose
   ```

2. 启动Docker服务：
   ```bash
   sudo systemctl start docker
   sudo systemctl enable docker
   ```

3. 确认Docker已启动：
   ```bash
   docker --version
   sudo docker ps
   ```

## 数据库设置

### 步骤1：启动数据库容器

在当前目录下执行：

```bash
docker-compose up -d
```

这将启动MongoDB数据库服务器和Mongo Express管理界面。

- MongoDB服务将在端口27017上运行
- Mongo Express管理界面将在端口8081上运行

### 步骤2：访问管理界面

打开浏览器并访问：http://localhost:8081

使用以下凭据登录：
- 用户名：admin
- 密码：stylishlink_secure_pw

### 步骤3：连接到数据库

应用应使用以下连接参数：

```
数据库名称：stylishlink
用户名：stylishlink_app
密码：stylishlink_app_pw
连接URI：************************************************************************
```

### 步骤4：导入示例数据（可选）

启动容器后，可以导入示例数据进行测试：

```bash
# 安装mongosh工具(如果尚未安装)
# macOS: brew install mongosh
# 导入示例数据
mongosh "************************************************************************" --file sample-data.js
```

## 数据库结构

数据库初始化时会创建以下集合：

1. `users` - 用户信息
2. `clothing` - 衣物信息
3. `accessories` - 饰品信息
4. `outfits` - 搭配组合
5. `wuxing_mappings` - 五行属性映射
6. `weather_records` - 天气记录
7. `recommendations` - 推荐记录

已配置适当的索引以优化查询性能。

## 数据备份与恢复

### 备份数据

```bash
docker exec stylishlink-mongodb mongodump --uri="************************************************************************" --out=/data/db/backup
```

### 恢复数据

```bash
docker exec stylishlink-mongodb mongorestore --uri="************************************************************************" /data/db/backup/stylishlink
```

## 注意事项

- 生产环境部署时，请务必修改密码
- 生产环境应配置适当的防火墙规则
- 定期备份数据库内容 