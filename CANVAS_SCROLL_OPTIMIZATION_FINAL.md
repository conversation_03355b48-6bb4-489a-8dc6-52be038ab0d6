# Canvas滚动抖动优化 - 最终解决方案

## 问题背景
微信小程序中Canvas组件在页面滚动时产生视觉抖动，特别是在iOS设备上表现明显。经过多轮优化尝试，最终采用CSS transform和opacity优化方案。

## 解决方案演进

### ❌ 方案1：滚动时隐藏Canvas
- **思路**：滚动时完全隐藏Canvas，显示占位图
- **问题**：用户体验太奇怪，滚动时图表消失不合理
- **结论**：被用户否决

### ✅ 方案2：CSS Transform + Opacity优化（最终方案）
- **思路**：滚动时应用CSS优化样式，保持Canvas可见但减少抖动
- **优势**：用户体验自然，性能优化明显

## 最终实施方案

### 1. 模板层面的条件样式
```vue
<!-- 运势趋势图 -->
<canvas
  :id="canvasId"
  :canvas-id="canvasId"
  class="chart-canvas"
  :class="{ 'canvas-scrolling': isScrolling || isPageScrolling }"
  :force-use-old-canvas="true"
/>

<!-- 五行图 -->
<canvas
  :id="canvasId"
  :canvas-id="canvasId"
  class="wuxing-canvas"
  :class="{ 'canvas-scrolling': isScrolling || isPageScrolling }"
  :force-use-old-canvas="true"
/>
```

### 2. CSS优化样式
```scss
.chart-canvas, .wuxing-canvas {
  // 基础优化
  will-change: transform, opacity;
  transform: translate3d(0, 0, 0);
  contain: layout style paint;
  transition: none !important;
  animation: none !important;
  
  // 强制硬件加速
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  -webkit-perspective: 1000px;
  perspective: 1000px;
}

// 滚动时应用的优化样式
.canvas-scrolling {
  // 关键：轻微缩放强制使用合成层
  transform: translate3d(0, 0, 0) scale(0.999) !important;
  
  // 降低透明度减少视觉冲击
  opacity: 0.95;
  
  // 禁用指针事件减少交互干扰
  pointer-events: none;
  
  // 强化硬件加速
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  -webkit-perspective: 1000px;
  perspective: 1000px;
}
```

### 3. 全局滚动状态管理
```typescript
// 页面级滚动状态管理
const isPageScrolling = ref(false)
let scrollTimer: number | null = null
let lastScrollTime = 0

function handleScroll() {
  const now = Date.now()
  
  // 节流：限制滚动状态更新频率（约60fps）
  if (now - lastScrollTime < 16) {
    return
  }
  lastScrollTime = now

  // 设置滚动状态
  if (!isPageScrolling.value) {
    isPageScrolling.value = true
  }

  if (scrollTimer) {
    clearTimeout(scrollTimer)
  }

  // 滚动结束后恢复正常状态
  scrollTimer = setTimeout(() => {
    isPageScrolling.value = false
  }, 150) // 快速恢复
}

// 提供给子组件
provide('isPageScrolling', isPageScrolling)
```

### 4. 组件级滚动优化
```typescript
function handleScroll(event: any) {
  const scrollLeft = event.detail.scrollLeft

  // 轻量级滚动状态管理
  if (!isScrolling.value) {
    isScrolling.value = true
  }

  // 提高滚动阈值，减少无效处理
  if (Math.abs(scrollLeft - lastScrollLeft) < 8) {
    return
  }

  lastScrollLeft = scrollLeft

  // 清除之前的滚动计时器
  if (scrollTimer) {
    clearTimeout(scrollTimer)
  }

  // 平衡的防抖延迟
  scrollTimer = setTimeout(() => {
    // 处理滚动逻辑...
    
    // 恢复正常状态
    isScrolling.value = false
    scrollTimer = null
  }, 200) // 平衡性能和响应性
}
```

## 核心优化原理

### 1. 强制合成层
- `transform: scale(0.999)` - 轻微缩放强制创建合成层
- `translate3d(0, 0, 0)` - 启用硬件加速
- `will-change: transform, opacity` - 提前告知浏览器优化

### 2. 减少重绘
- `contain: layout style paint` - 限制重绘范围
- `pointer-events: none` - 滚动时禁用交互
- `transition: none` - 禁用过渡动画

### 3. 视觉优化
- `opacity: 0.95` - 轻微降低透明度，减少视觉冲击
- 保持Canvas可见，用户体验自然

### 4. 性能优化
- 节流滚动事件（60fps）
- 防抖处理（200ms）
- 减少无效的状态切换

## 预期效果

1. **滚动抖动显著减少**：通过强制合成层和硬件加速
2. **用户体验自然**：Canvas始终可见，只是视觉效果略有变化
3. **性能提升明显**：减少重绘和无效处理
4. **兼容性良好**：适用于iOS和Android设备

## 应用范围

- ✅ OverallFortune组件（运势趋势图）
- ✅ WuxingCircle组件（五行图）
- ✅ 所有Canvas组件

这个方案在保持良好用户体验的同时，通过CSS和JavaScript优化显著减少了Canvas滚动抖动问题。
