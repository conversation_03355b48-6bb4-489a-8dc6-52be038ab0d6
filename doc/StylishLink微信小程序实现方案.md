# StylishLink微信小程序实现方案（2024 UniApp重构版）

## 一、主技术栈与项目结构

- **主技术栈**：UniApp（Vue2/3 + TypeScript），支持H5、微信/支付宝/百度/QQ/抖音/快手等主流小程序、App、快应用一套代码多端适配。
- **目录结构**（推荐）：
  - `/src/pages/`：主页面（首页、衣橱、搭配、个人中心等）
  - `/src/components/`：通用UI组件
  - `/src/store/`：状态管理（Vuex/Pinia）
  - `/src/api/`：接口封装
  - `/src/utils/`：工具函数
  - `/src/assets/`：静态资源
  - `/src/styles/`：全局样式、主题
  - `/src/config/`：环境配置
  - `/src/i18n/`：国际化
  - `/src/uni_modules/`：主流插件与三方模块
  - `/src/libs/`：第三方SDK集成（COS SDK等）
  - `/src/services/`：业务服务层（文件服务、推荐服务等）

## 二、跨端适配与兼容性

- 采用UniApp官方CLI，支持一套代码多端编译，主力平台为微信小程序与H5，兼容App、支付宝、百度、QQ、抖音、快手等。
- UI组件建议优先选用`@dcloudio/uni-ui`、`tuniao-ui`等主流生态，支持自定义主题与响应式布局。
- 兼容微信小程序API，推荐使用`uni`前缀API，避免平台差异。
- 支持TypeScript全量类型推断与IDE智能提示。

## 三、文件管理与COS集成设计

### 3.1 COS SDK集成策略

基于腾讯云COS小程序SDK的最佳实践，采用以下集成方案：

#### 方案选择
考虑到小程序主包大小限制（2MB），推荐采用**文件直传方案**而非完整SDK：

**推荐方案：COS直传（不依赖SDK）**
- 优势：包体积小，仅需核心签名逻辑
- 适用：基础文件上传、下载功能
- 实现：通过后端STS服务获取临时密钥，前端直接调用COS API

**备选方案：分包引入COS SDK**
- 适用：需要完整COS功能的场景
- 注意：SDK约200KB，必须放在分包中避免主包过大

#### 技术实现架构

```typescript
// 文件服务架构
src/services/
├── file-service.ts         // 文件服务核心类
├── cos-service.ts          // COS直传服务
├── upload-manager.ts       // 上传队列管理
└── types/
    ├── file-types.ts       // 文件类型定义
    └── cos-types.ts        // COS相关类型
```

### 3.2 文件服务核心实现

#### 文件服务类设计
```typescript
// src/services/file-service.ts
import { CosService } from './cos-service'
import { UploadManager } from './upload-manager'

export class FileService {
  private cosService: CosService
  private uploadManager: UploadManager
  
  constructor() {
    this.cosService = new CosService()
    this.uploadManager = new UploadManager()
  }

  // 单文件上传
  async uploadFile(options: UploadFileOptions): Promise<UploadResult> {
    try {
      // 1. 获取文件信息
      const fileInfo = await this.getFileInfo(options.filePath)
      
      // 2. 根据文件大小选择上传策略
      if (fileInfo.size > 5 * 1024 * 1024) {
        return await this.uploadLargeFile(options)
      } else {
        return await this.uploadSmallFile(options)
      }
    } catch (error) {
      throw new FileUploadError('文件上传失败', error)
    }
  }

  // 批量上传
  async uploadFiles(files: FileUploadItem[]): Promise<BatchUploadResult> {
    return this.uploadManager.batchUpload(files)
  }

  // 获取下载链接
  async getDownloadUrl(fileId: string): Promise<string> {
    const response = await uni.request({
      url: `${API_BASE_URL}/files/${fileId}/download-url`,
      method: 'GET',
      header: {
        'Authorization': `Bearer ${getToken()}`
      }
    })
    
    if (response.data.code === 0) {
      return response.data.data.downloadUrl
    }
    throw new Error(response.data.message)
  }

  // 删除文件
  async deleteFile(fileId: string): Promise<void> {
    await uni.request({
      url: `${API_BASE_URL}/files/${fileId}`,
      method: 'DELETE',
      header: {
        'Authorization': `Bearer ${getToken()}`
      }
    })
  }
}
```

#### COS直传服务实现
```typescript
// src/services/cos-service.ts
export class CosService {
  private stsCache: STSCredentials | null = null

  // 获取STS临时密钥
  async getStsCredentials(): Promise<STSCredentials> {
    if (this.stsCache && !this.isCredentialsExpired(this.stsCache)) {
      return this.stsCache
    }

    const response = await uni.request({
      url: `${API_BASE_URL}/sts/credentials`,
      method: 'GET',
      header: {
        'Authorization': `Bearer ${getToken()}`
      }
    })

    if (response.data.code === 0) {
      this.stsCache = response.data.data
      return this.stsCache
    }
    
    throw new Error('获取临时密钥失败')
  }

  // 直传到COS
  async uploadToCos(options: CosUploadOptions): Promise<CosUploadResult> {
    const credentials = await this.getStsCredentials()
    
    // 生成上传签名
    const signature = this.generateSignature({
      method: 'POST',
      pathname: '/',
      ...credentials
    })

    // 直接上传到COS
    return new Promise((resolve, reject) => {
      const uploadTask = uni.uploadFile({
        url: `https://${options.bucket}.cos.${options.region}.myqcloud.com/`,
        filePath: options.filePath,
        name: 'file',
        formData: {
          'key': options.key,
          'success_action_status': '200',
          'Signature': signature,
          'x-cos-security-token': credentials.sessionToken,
          'Content-Type': options.contentType || ''
        },
        success: (res) => {
          if (res.statusCode === 200) {
            resolve({
              url: `https://${options.bucket}.cos.${options.region}.myqcloud.com/${options.key}`,
              etag: res.header.etag
            })
          } else {
            reject(new Error('上传失败'))
          }
        },
        fail: reject
      })

      // 监听上传进度
      uploadTask.onProgressUpdate((progress) => {
        options.onProgress?.(progress.progress)
      })
    })
  }

  // 分片上传大文件
  async uploadLargeFile(options: LargeFileUploadOptions): Promise<UploadResult> {
    // 1. 初始化分片上传
    const initResult = await this.initMultipartUpload(options)
    
    // 2. 上传分片
    const parts = await this.uploadParts({
      ...options,
      uploadId: initResult.uploadId
    })

    // 3. 完成分片上传
    return await this.completeMultipartUpload({
      ...options,
      uploadId: initResult.uploadId,
      parts
    })
  }

  private generateSignature(options: SignatureOptions): string {
    // 实现COS签名算法
    // 注意：实际项目中建议使用后端签名，此处仅为示例
    return generateCosSignature(options)
  }
}
```

### 3.3 上传队列管理
```typescript
// src/services/upload-manager.ts
export class UploadManager {
  private uploadQueue: UploadTask[] = []
  private concurrentLimit = 3

  async batchUpload(files: FileUploadItem[]): Promise<BatchUploadResult> {
    const results: UploadResult[] = []
    const errors: UploadError[] = []

    // 创建上传任务
    const tasks = files.map(file => this.createUploadTask(file))
    this.uploadQueue.push(...tasks)

    // 并发控制上传
    await this.processQueue()

    return {
      success: results,
      failed: errors,
      total: files.length
    }
  }

  private async processQueue(): Promise<void> {
    const executing: Promise<void>[] = []

    for (const task of this.uploadQueue) {
      const promise = this.executeUploadTask(task)
      executing.push(promise)

      if (executing.length >= this.concurrentLimit) {
        await Promise.race(executing)
        executing.splice(0, 1)
      }
    }

    await Promise.all(executing)
  }

  pauseTask(taskId: string): void {
    const task = this.findTask(taskId)
    if (task) {
      task.pause()
    }
  }

  resumeTask(taskId: string): void {
    const task = this.findTask(taskId)
    if (task) {
      task.resume()
    }
  }

  cancelTask(taskId: string): void {
    const task = this.findTask(taskId)
    if (task) {
      task.cancel()
      this.removeTask(taskId)
    }
  }
}
```

### 3.4 文件类型定义
```typescript
// src/services/types/file-types.ts
export interface UploadFileOptions {
  filePath: string
  category: FileCategory
  accessLevel?: AccessLevel
  metadata?: Record<string, any>
  onProgress?: (progress: number) => void
  onTaskReady?: (taskId: string) => void
}

export interface UploadResult {
  fileId: string
  url: string
  thumbnailUrl?: string
  fileInfo: FileInfo
}

export interface FileInfo {
  id: string
  originalName: string
  fileSize: number
  mimeType: string
  md5Hash: string
  status: FileStatus
  createdAt: string
}

export enum FileCategory {
  USER_AVATAR = 'USER_AVATAR',
  USER_PHOTO = 'USER_PHOTO',
  CLOTHING_IMAGE = 'CLOTHING_IMAGE',
  ACCESSORY_IMAGE = 'ACCESSORY_IMAGE',
  OUTFIT_IMAGE = 'OUTFIT_IMAGE',
  AI_GENERATED_VIDEO = 'AI_GENERATED_VIDEO',
  AI_GENERATED_IMAGE = 'AI_GENERATED_IMAGE',
  TEMP_FILE = 'TEMP_FILE'
}

export enum AccessLevel {
  PUBLIC = 'PUBLIC',
  PRIVATE = 'PRIVATE',
  PROTECTED = 'PROTECTED',
  INTERNAL = 'INTERNAL'
}

export enum FileStatus {
  UPLOADING = 'UPLOADING',
  UPLOADED = 'UPLOADED',
  PROCESSING = 'PROCESSING',
  AVAILABLE = 'AVAILABLE',
  DELETED = 'DELETED',
  EXPIRED = 'EXPIRED'
}
```

### 3.5 业务组件封装

#### 图片上传组件
```vue
<!-- src/components/upload/ImageUploader.vue -->
<template>
  <view class="image-uploader">
    <view 
      class="upload-area"
      @tap="selectImage"
      :class="{ 'uploading': isUploading }"
    >
      <image 
        v-if="imageUrl"
        :src="imageUrl"
        class="preview-image"
        mode="aspectFill"
      />
      <view v-else class="upload-placeholder">
        <uni-icons type="camera" size="32" color="#999"></uni-icons>
        <text class="upload-text">选择图片</text>
      </view>
      
      <!-- 上传进度 -->
      <view v-if="isUploading" class="upload-progress">
        <uni-progress 
          :percent="uploadProgress"
          :show-info="true"
          active
        />
      </view>
    </view>
    
    <!-- 错误提示 -->
    <view v-if="errorMessage" class="error-message">
      {{ errorMessage }}
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { FileService } from '@/services/file-service'
import { FileCategory } from '@/services/types/file-types'

interface Props {
  modelValue?: string
  category?: FileCategory
  maxSize?: number // MB
  accept?: string[]
}

const props = withDefaults(defineProps<Props>(), {
  category: FileCategory.USER_PHOTO,
  maxSize: 10,
  accept: () => ['jpg', 'jpeg', 'png', 'gif']
})

const emit = defineEmits<{
  'update:modelValue': [value: string]
  'upload-success': [result: any]
  'upload-error': [error: Error]
}>()

const fileService = new FileService()

const imageUrl = ref(props.modelValue)
const isUploading = ref(false)
const uploadProgress = ref(0)
const errorMessage = ref('')

const selectImage = async () => {
  try {
    const result = await uni.chooseImage({
      count: 1,
      sizeType: ['original', 'compressed'],
      sourceType: ['album', 'camera']
    })

    const filePath = result.tempFilePaths[0]
    await uploadImage(filePath)
  } catch (error) {
    console.error('选择图片失败:', error)
  }
}

const uploadImage = async (filePath: string) => {
  try {
    errorMessage.value = ''
    isUploading.value = true
    uploadProgress.value = 0

    // 检查文件大小
    const fileInfo = await uni.getFileInfo({ filePath })
    if (fileInfo.size > props.maxSize * 1024 * 1024) {
      throw new Error(`文件大小不能超过${props.maxSize}MB`)
    }

    const result = await fileService.uploadFile({
      filePath,
      category: props.category,
      accessLevel: 'PRIVATE',
      onProgress: (progress) => {
        uploadProgress.value = progress
      }
    })

    imageUrl.value = result.url
    emit('update:modelValue', result.url)
    emit('upload-success', result)

  } catch (error) {
    errorMessage.value = error.message || '上传失败'
    emit('upload-error', error)
  } finally {
    isUploading.value = false
    uploadProgress.value = 0
  }
}

// 监听props变化
watch(() => props.modelValue, (newValue) => {
  imageUrl.value = newValue
})
</script>

<style scoped lang="scss">
.image-uploader {
  .upload-area {
    width: 200rpx;
    height: 200rpx;
    border: 2rpx dashed #ddd;
    border-radius: 16rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;

    &.uploading {
      border-color: var(--primary-color);
    }

    .preview-image {
      width: 100%;
      height: 100%;
    }

    .upload-placeholder {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 16rpx;

      .upload-text {
        font-size: 24rpx;
        color: #999;
      }
    }

    .upload-progress {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      background: rgba(255, 255, 255, 0.9);
      padding: 16rpx;
    }
  }

  .error-message {
    margin-top: 16rpx;
    color: #ff4757;
    font-size: 24rpx;
    text-align: center;
  }
}
</style>
```

## 四、主流插件生态与能力扩展

- 推荐集成：
  - uni-ui（官方UI组件库）
  - tuniao-ui（丰富的表单/展示/酷炫页面模板）
  - uview-plus、colorui等（可选）
  - uni-id（用户认证）、uni-cloud（云开发）、uni-request（网络请求）、uni-simple-router（路由增强）等
- 支持主流小程序SDK（微信支付、订阅消息、地图、扫码、分享等）
- 支持AI能力扩展（如AI识别、AI推荐、AI生成内容等）
- **COS文件管理**：基于腾讯云COS的完整文件管理解决方案

## 五、TypeScript与代码规范

- 推荐全量TypeScript开发，tsconfig.json按官方最佳实践配置，支持严格类型检查、路径别名（@/）、多端类型声明。
- 统一ESLint+Prettier代码风格，CI自动校验。
- 组件/页面/接口/状态/样式分层管理，便于团队协作与维护。

### TypeScript配置优化
```json
// tsconfig.json
{
  "compilerOptions": {
    "target": "ES2018",
    "module": "ES2015",
    "lib": ["ES2018", "DOM"],
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "moduleResolution": "node",
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"],
      "@/components/*": ["src/components/*"],
      "@/services/*": ["src/services/*"],
      "@/utils/*": ["src/utils/*"],
      "@/types/*": ["src/types/*"]
    }
  },
  "include": [
    "src/**/*",
    "types/**/*"
  ],
  "exclude": [
    "node_modules",
    "dist",
    "platforms"
  ]
}
```

## 六、团队协作与开发流程

- 推荐使用HBuilderX、VSCode等主流IDE，配合官方CLI工具链。
- 采用Git分支协作，主干/功能/修复分支规范。
- 支持Vite/webpack热更新，提升开发效率。
- 推荐CI/CD自动化（如GitHub Actions、阿里云流水线等），支持多端自动打包与发布。

## 七、性能优化与质量保障

- 图片懒加载、分包加载、骨架屏、虚拟列表等提升首屏与滚动性能。
- 组件按需引入，Tree-shaking优化包体积。
- 单元测试（Jest）、端到端测试（uni-automator）、UI自动化测试等全流程保障。
- 兼容Android/iOS主流机型，支持深色模式与无障碍。

### 文件上传性能优化
1. **智能压缩**：根据图片尺寸和质量自动压缩
2. **分片上传**：大文件自动分片，支持断点续传
3. **并发控制**：限制同时上传文件数量，避免内存溢出
4. **缓存策略**：本地缓存已上传文件信息，避免重复上传
5. **CDN加速**：使用腾讯云CDN加速文件分发

### 微信小程序特定优化

#### iOS真机兼容性问题与解决方案

**问题背景**：iOS真机环境下，页面可能出现无法点击、无法滚动等交互问题，而在Android设备和模拟器上工作正常。

**常见问题及解决方案**：

**1. scroll-view高级属性兼容性**
```vue
<!-- ❌ 可能导致iOS真机问题的写法 -->
<scroll-view
  enable-flex
  :enhanced="true"
  scroll-y
>

<!-- ✅ iOS兼容的写法 -->
<scroll-view
  scroll-y
  :bounces="false"
  :show-scrollbar="false"
>
```

**2. CSS层级干扰触摸事件（关键问题）**

**问题现象**：浮动背景色块在iOS真机上显示在内容上方，拦截触摸事件导致点击和滚动失效

**根本原因**：iOS真机上z-index层级上下文(stacking context)与模拟器表现不一致

```scss
/* ✅ 背景动画层优化 - 使用负z-index和层级上下文 */
.background-animation {
  position: fixed;
  z-index: -1; /* 负值确保始终在最底层 */
  
  // 强制创建层级上下文，确保iOS兼容性
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
  
  // 防止背景干扰触摸事件
  pointer-events: none;
  
  // iOS触摸优化
  -webkit-user-select: none;
  user-select: none;
}

/* ✅ 内容层触摸优化 - 提高z-index和创建层级上下文 */
.content-layer {
  position: relative;
  z-index: 10; /* 大幅提高确保在背景之上 */
  
  // 强制创建层级上下文，确保iOS兼容性
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
  
  // 确保内容区域可交互
  pointer-events: auto;
  
  // iOS触摸优化
  -webkit-user-select: auto;
  user-select: auto;
  touch-action: manipulation;
}

/* ✅ 页面根容器层级隔离 */
.page-container {
  // 创建新的层级上下文，解决iOS层级问题
  isolation: isolate;
}
```

**3. iOS专用触摸滚动优化**
```scss
.scroll-container {
  // iOS触摸滚动优化
  -webkit-overflow-scrolling: touch;
  touch-action: pan-y;
  
  // 确保触摸事件正常传递
  pointer-events: auto;
}
```

**4. 可交互元素触摸优化**
```scss
/* iOS专用触摸优化 */
button, 
.interactive-element {
  // iOS触摸优化
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
  tap-highlight-color: rgba(0, 0, 0, 0.1);
  -webkit-user-select: none;
  user-select: none;
  touch-action: manipulation;
  
  // 确保可交互元素有正确的指针事件
  pointer-events: auto;
  
  // iOS触摸延迟优化
  -webkit-touch-callout: none;
  -webkit-appearance: none;
  
  // 改善触摸响应
  cursor: pointer;
}
```

**5. WXSS选择器兼容性**
```scss
/* ❌ 微信小程序不支持的选择器 */
.container > * {
  // 通配符选择器不支持
}
[class*="button"] {
  // 属性选择器通配符不支持
}

/* ✅ 兼容的选择器写法 */
.container .child-element {
  // 使用具体类名
}
.button-primary,
.button-secondary {
  // 明确列出所有目标类名
}
```

**最佳实践总结**：
- 避免使用scroll-view的`enable-flex`和`enhanced`属性
- **关键**：使用负z-index(如z-index: -1)确保背景层始终在最底层
- 为背景动画元素添加`pointer-events: none`和`transform: translateZ(0)`
- 大幅提高内容层z-index(如z-index: 10)确保在背景之上
- 使用`isolation: isolate`为页面根容器创建层级上下文
- 使用`-webkit-overflow-scrolling: touch`优化iOS滚动
- 避免使用通配符和高级CSS选择器
- 为所有可交互元素添加iOS专用触摸优化属性
- 使用`touch-action`和`pointer-events`精确控制触摸行为

**层级问题诊断方法**：
- 对比模拟器和真机表现，观察浮动元素是否覆盖内容
- 使用Safari Web Inspector连接真机调试层级结构
- 检查z-index值是否在iOS真机上失效
- 验证transform属性是否正确创建了层级上下文

#### 2025年微信小程序胶囊按钮动态适配方案

**核心理念**：基于官方API的精确定位，动态计算胶囊按钮位置，零硬编码适配

**useMenuButton Composable 核心实现**：
```typescript
// src/composables/useMenuButton.ts
import { computed, onMounted, ref } from 'vue'

export function useMenuButton() {
  const menuButtonInfo = ref<any>(null)

  // 获取胶囊按钮位置信息
  function getMenuButtonInfo() {
    try {
      const info = uni.getMenuButtonBoundingClientRect()
      if (info && info.top !== undefined && info.height) {
        menuButtonInfo.value = info
      }
    }
    catch (error) {
      console.warn('获取胶囊按钮信息失败，使用默认值', error)
    }
  }

  // PageHeader高度：胶囊按钮底部 + 8px间距
  const headerHeight = computed(() => {
    if (menuButtonInfo.value) {
      return `${menuButtonInfo.value.top + menuButtonInfo.value.height + 8}px`
    }
    return 'max(calc(28px + env(safe-area-inset-top)), 100px)'
  })

  // 标题位置：与胶囊按钮垂直居中对齐
  const contentTop = computed(() => {
    if (menuButtonInfo.value) {
      const centerY = menuButtonInfo.value.top + menuButtonInfo.value.height / 2
      return `${centerY - 14}px` // 14px是内容高度的一半
    }
    return 'max(calc(env(safe-area-inset-top) + 12px), 56px)'
  })

  // 天气信息位置：与胶囊按钮顶部对齐
  const weatherAlignTop = computed(() => {
    if (menuButtonInfo.value) {
      return `${menuButtonInfo.value.top}px`
    }
    return 'calc(env(safe-area-inset-top) + 6px)'
  })

  // 内容区域偏移12px：胶囊按钮底部+12px
  const contentOffset12px = computed(() => {
    if (menuButtonInfo.value) {
      return `${menuButtonInfo.value.top + menuButtonInfo.value.height + 12}px`
    }
    return 'calc(44px + env(safe-area-inset-top))'
  })

  onMounted(() => {
    getMenuButtonInfo()
  })

  return {
    menuButtonInfo,
    headerHeight,
    contentTop,
    weatherAlignTop,
    contentOffset12px,
    getMenuButtonInfo,
  }
}
```

**业务场景适配策略**：

**1. 首页天气信息适配**：
```vue
<!-- 首页：天气信息与胶囊按钮水平对齐 -->
<script setup>
const { weatherAlignTop } = useMenuButton()
</script>

<template>
  <view class="main-content" :style="{ paddingTop: weatherAlignTop }">
    <WeatherEnergySection />
  </view>
</template>
```

**2. 次级页面内容适配**：
```vue
<!-- 衣橱/搭配/我的页面：内容区域比胶囊按钮低12px -->
<script setup>
const { contentTop, contentOffset12px } = useMenuButton()
</script>

<template>
  <view class="page-header" :style="{ top: contentTop }">
    <text class="page-title">页面标题</text>
  </view>
  <scroll-view :style="{ paddingTop: contentOffset12px }">
    <!-- 页面内容 -->
  </scroll-view>
</template>
```

**3. 特殊页面适配**：
```vue
<!-- 今日能量页面：内容区域比胶囊按钮低12px -->
<script setup>
const { contentOffset12px } = useMenuButton()
</script>

<template>
  <scroll-view :style="{ marginTop: contentOffset12px }">
    <!-- 页面内容 -->
  </scroll-view>
</template>
```

**底部安全区域适配**（保持不变）：
```scss
.tab-bar-safe {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

.floating-button-bottom-safe {
  bottom: calc(96px + constant(safe-area-inset-bottom));
  bottom: calc(96px + env(safe-area-inset-bottom));
}
```

**适配优势**：
- **精确定位**：基于微信官方API，获取准确的胶囊按钮位置
- **动态适配**：支持不同iOS设备自动适配，无需硬编码
- **业务灵活性**：不同页面可使用不同的计算属性满足UI需求
- **兼容性强**：提供fallback默认值，确保在API失效时的正常显示
- **维护简单**：统一的composable管理，易于调试和维护

#### 页面标题布局规范
基于useMenuButton动态计算，确保页面标题与胶囊按钮完美对齐：

**动态标题定位**：
```vue
<script setup>
const { contentTop } = useMenuButton()
</script>

<template>
  <view class="page-header" :style="{ top: contentTop }">
    <text class="page-title">页面标题</text>
  </view>
</template>
```

**标题样式规范**：
```scss
.page-header {
  position: fixed;
  left: 5%;
  z-index: 100;
  height: 28px;
  display: flex;
  align-items: center;
  // top 值通过 useMenuButton 动态计算
}
```

**设计规范**：
- 使用 `contentTop` 确保标题与胶囊按钮垂直居中对齐
- 首页通常无需页面标题，直接显示功能内容
- 次级页面建议添加页面标题，提供清晰的页面定位
- 避免标题与右上角微信菜单区域冲突
- 标题高度固定为28px，确保与胶囊按钮视觉协调

#### scroll-view 动态布局规范
基于useMenuButton实现响应式scroll-view布局，适配不同页面需求：

**标准scroll-view结构**：
```vue
<script setup>
const { contentTop, contentOffset12px } = useMenuButton()
</script>

<template>
  <view class="page-container">
    <!-- 固定标题（可选） -->
    <view class="page-header" :style="{ top: contentTop }">
      <text class="page-title">页面标题</text>
    </view>

    <!-- 滚动容器 -->
    <scroll-view 
      class="main-scroll-container" 
      enable-flex 
      scroll-y 
      :enhanced="true" 
      :bounces="false" 
      :show-scrollbar="false"
      :style="{ paddingTop: contentOffset12px }"
    >
      <!-- 滚动内容 -->
      <view class="main-content">
        <!-- 页面内容 -->
      </view>

      <!-- 全宽组件（如分类tab） -->
      <CategoryTabs />
    </scroll-view>
  </view>
</template>
```

**动态样式配置**：
```scss
.main-scroll-container {
  height: calc(100vh - var(--bottom-nav-height, 68px));
  // paddingTop 通过 :style 动态设置，无需固定CSS值
}

// 首页特殊布局
.index-main-content {
  // 使用 weatherAlignTop 与胶囊按钮顶部对齐
}

// 特殊页面布局（如今日能量）
.fortune-scroll-container {
  height: 100vh;
  // marginTop 通过 :style 动态设置
}
```

#### 滚动条隐藏规范
微信小程序中必须隐藏所有滚动条，提供纯净的用户体验：

```scss
.scrollable-container {
  // 微信小程序隐藏滚动条 - 多重方案确保兼容性
  ::-webkit-scrollbar {
    display: none !important;
    width: 0 !important;
    height: 0 !important;
    background: transparent !important;
    -webkit-appearance: none !important;
  }

  ::-webkit-scrollbar-track {
    display: none !important;
    width: 0 !important;
    background: transparent !important;
  }

  ::-webkit-scrollbar-thumb {
    display: none !important;
    width: 0 !important;
    background: transparent !important;
  }

  ::-webkit-scrollbar-corner {
    display: none !important;
    background: transparent !important;
  }

  // 额外隐藏滚动条样式
  scrollbar-width: none !important; // Firefox
  scrollbar-color: transparent transparent !important; // Firefox滚动条颜色
  -ms-overflow-style: none !important; // IE
  overflow: -moz-scrollbars-none !important; // 老版本Firefox

  // 真机特殊处理
  &::-webkit-scrollbar {
    width: 0px !important;
    background: transparent !important;
  }
}
```

#### 常见样式问题总结

**1. 分类tab延伸问题**
- **问题**：分类tab没有延伸到屏幕边缘
- **原因**：tab组件被包含在有padding的容器内
- **解决**：将CategoryTabs移出有边距的容器，直接放在scroll-view下

**2. 标题对齐问题**
- **问题**：页面标题与微信胶囊按钮没有对齐
- **原因**：使用固定CSS值，无法适配不同设备
- **解决**：使用 `useMenuButton` 的 `contentTop` 进行动态对齐

**3. 滚动条显示问题**
- **问题**：页面滚动时显示滚动条
- **原因**：使用普通view滚动，未正确隐藏滚动条
- **解决**：使用scroll-view组件，配合完整的滚动条隐藏CSS

**4. ESLint import 顺序问题**
- **问题**：TypeScript类型导入顺序不符合规范
- **原因**：外部库导入应在内部模块导入之前
- **解决**：调整import顺序，外部库(如vue) → 内部类型 → 内部组件

**5. iOS真机无法点击和滚动问题（重大兼容性问题）**
- **问题**：页面在iOS真机上无法点击、无法滚动，但在Android和模拟器正常
- **根本原因**：iOS真机上z-index层级上下文表现与模拟器不一致，背景元素覆盖内容层
- **症状识别**：浮动背景色块在真机上显示在内容上方，拦截所有触摸事件
- **解决方案**：
  - 使用负z-index(-1)确保背景层在最底层
  - 提高内容层z-index(10+)确保在最上层
  - 添加`transform: translateZ(0)`强制创建层级上下文
  - 使用`isolation: isolate`为根容器创建层级隔离
  - 移除scroll-view的`enable-flex`和`enhanced`属性
  - 添加完整的iOS触摸优化CSS属性

**6. WXSS编译错误 - 不支持的选择器**
- **问题**：`error at token '*'` 编译错误
- **原因**：微信小程序WXSS不支持通配符选择器和属性选择器通配符
- **解决**：使用具体的类名选择器替代通配符选择器

**7. CSS层级上下文(Stacking Context)跨设备不一致**
- **问题**：相同代码在iOS真机、Android、模拟器上层级表现不同
- **原因**：不同设备/浏览器对层级上下文的实现和优化策略不同
- **识别方法**：对比截图观察浮动元素相对位置，使用调试工具检查computed z-index
- **预防措施**：
  - 始终为背景层使用负z-index值
  - 避免依赖隐式层级上下文，主动创建明确的层级上下文
  - 使用`transform: translateZ(0)`强制硬件加速和层级分离
  - 在根容器使用`isolation: isolate`创建层级隔离

## 八、微信小程序域名配置与安全

### 8.1 域名白名单配置
微信小程序中使用COS服务需要配置域名白名单：

**必需配置的域名**：
```
request合法域名：
- https://your-api-domain.com（后端API域名）
- https://sts.tencentcloudapi.com（STS服务域名）

uploadFile合法域名：
- https://your-bucket.cos.your-region.myqcloud.com（COS域名）
- 支持泛域名：https://*.cos.*.myqcloud.com

downloadFile合法域名：
- https://your-cdn-domain.com（CDN域名，可选）
- https://your-bucket.cos.your-region.myqcloud.com
```

### 8.2 安全配置最佳实践

#### 临时密钥配置
```typescript
// src/config/cos-config.ts
export const COS_CONFIG = {
  // 不要在前端硬编码永久密钥
  STS_URL: '/api/v1/sts/credentials',
  
  // COS配置
  REGION: 'ap-guangzhou',
  
  // 安全配置
  MAX_FILE_SIZE: 100 * 1024 * 1024, // 100MB
  ALLOWED_MIME_TYPES: [
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/webp',
    'video/mp4',
    'video/quicktime'
  ],
  
  // 分片上传配置
  CHUNK_SIZE: 1024 * 1024 * 5, // 5MB
  MAX_CONCURRENT_UPLOADS: 3
}
```

#### 文件安全验证
```typescript
// src/utils/file-validator.ts
export class FileValidator {
  static validateFile(filePath: string): Promise<ValidationResult> {
    return new Promise(async (resolve, reject) => {
      try {
        // 1. 检查文件大小
        const fileInfo = await uni.getFileInfo({ filePath })
        if (fileInfo.size > COS_CONFIG.MAX_FILE_SIZE) {
          throw new Error('文件大小超出限制')
        }

        // 2. 检查文件类型
        const mimeType = this.getMimeType(filePath)
        if (!COS_CONFIG.ALLOWED_MIME_TYPES.includes(mimeType)) {
          throw new Error('不支持的文件类型')
        }

        // 3. 安全检查（如文件名、路径等）
        if (this.hasSecurityRisk(filePath)) {
          throw new Error('文件存在安全风险')
        }

        resolve({ valid: true })
      } catch (error) {
        reject(error)
      }
    })
  }

  private static getMimeType(filePath: string): string {
    const extension = filePath.split('.').pop()?.toLowerCase()
    const mimeMap = {
      'jpg': 'image/jpeg',
      'jpeg': 'image/jpeg',
      'png': 'image/png',
      'gif': 'image/gif',
      'webp': 'image/webp',
      'mp4': 'video/mp4',
      'mov': 'video/quicktime'
    }
    return mimeMap[extension] || 'application/octet-stream'
  }

  private static hasSecurityRisk(filePath: string): boolean {
    // 检查文件名是否包含危险字符
    const dangerousPatterns = ['../', '.\\', '<script', 'javascript:']
    return dangerousPatterns.some(pattern => 
      filePath.toLowerCase().includes(pattern)
    )
  }
}
```

## 九、国际化与数据安全

- 内置i18n国际化方案，支持多语言切换。
- 严格数据校验与加密，前后端接口安全、用户隐私保护。
- 支持云开发（uni-cloud）与传统RESTful后端对接。
- **文件安全**：完善的文件类型检查、大小限制、权限控制

## 十、2024最新特性与未来规划

- 支持Vue3+TypeScript+<script setup>新语法（可选升级）。
- 关注UniApp X、跨端原生渲染、AI能力深度集成等前沿方向。
- 持续跟进官方生态与主流插件，保障长期可维护性。
- **COS生态演进**：支持更多腾讯云服务集成（AI处理、CDN加速、数据万象等）

## 十一、CSS层级优化标准模板

### 11.1 iOS兼容的层级结构模板

```vue
<template>
  <view class="page-container">
    <!-- 背景装饰层 -->
    <view class="background-layer">
      <view class="floating-element"></view>
    </view>

    <!-- 主内容层 -->
    <view class="content-layer">
      <scroll-view class="scroll-container" scroll-y>
        <!-- 页面内容 -->
      </scroll-view>
    </view>

    <!-- 浮动操作层 -->
    <view class="floating-actions">
      <!-- 浮动按钮等 -->
    </view>
  </view>
</template>

<style lang="scss">
/* 页面根容器 - 创建层级隔离 */
.page-container {
  position: relative;
  min-height: 100vh;
  isolation: isolate; /* 关键：创建新的层级上下文 */
}

/* 背景装饰层 - 负z-index确保最底层 */
.background-layer {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1; /* 关键：负值确保在最底层 */
  
  // 强制创建层级上下文
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
  
  // 防止干扰触摸事件
  pointer-events: none;
  -webkit-user-select: none;
  user-select: none;
}

/* 主内容层 - 高z-index确保在最上层 */
.content-layer {
  position: relative;
  z-index: 10; /* 关键：高值确保在背景之上 */
  
  // 强制创建层级上下文
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
  
  // 确保可交互
  pointer-events: auto;
  touch-action: manipulation;
}

/* 滚动容器 - iOS优化 */
.scroll-container {
  -webkit-overflow-scrolling: touch;
  touch-action: pan-y;
  pointer-events: auto;
}

/* 浮动操作层 - 最高层级 */
.floating-actions {
  position: fixed;
  z-index: 100; /* 最高层级 */
  pointer-events: none; /* 容器不拦截事件 */
  
  // 子元素可交互
  > * {
    pointer-events: auto;
  }
}
</style>
```

## 十二、开发环境与调试

### 12.1 开发工具配置
```json
// .vscode/settings.json
{
  "typescript.preferences.importModuleSpecifier": "relative",
  "path-intellisense.mappings": {
    "@": "${workspaceRoot}/src"
  },
  "files.associations": {
    "*.vue": "vue"
  }
}
```

### 12.2 调试配置
```typescript
// src/utils/debug.ts
export class DebugLogger {
  static isDebug = process.env.NODE_ENV === 'development'

  static log(tag: string, message: any) {
    if (this.isDebug) {
      console.log(`[${tag}]`, message)
    }
  }

  static error(tag: string, error: any) {
    if (this.isDebug) {
      console.error(`[${tag}]`, error)
    }
  }

  // COS上传调试
  static logUpload(phase: string, data: any) {
    this.log('COS_UPLOAD', `${phase}: ${JSON.stringify(data)}`)
  }
}
```

---

> 本方案为StylishLink全平台小程序/APP/H5统一技术实现蓝图，特别加强了文件管理与COS集成的完整解决方案。后续如有需求/设计变更，将持续同步更新。 