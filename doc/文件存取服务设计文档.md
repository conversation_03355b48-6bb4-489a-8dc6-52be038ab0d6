# StylishLink 文件存取服务设计文档

## 一、服务概述

### 1.1 服务定位
文件存取服务（file-service）作为StylishLink后端微服务架构中的核心基础服务，负责统一管理所有文件的存储、访问、处理和生命周期管理。基于腾讯云COS（Cloud Object Storage）提供高可用、高性能的云存储能力。

### 1.2 核心价值
- **统一管理**：所有文件操作通过统一接口，便于管理和监控
- **安全可控**：细粒度权限控制，保障用户隐私和数据安全
- **高性能**：智能缓存策略，CDN加速，优化文件访问体验
- **可扩展**：支持多种存储后端，易于扩展新的文件处理能力
- **标准化**：规范化的文件命名、分类和元数据管理

### 1.3 适用场景
- 用户头像、照片上传和管理
- 衣物、饰品图片存储和处理
- 搭配效果图生成和存储
- AI生成内容（视频、图片）的存储
- 系统资源文件管理
- 临时文件处理

## 二、技术架构

### 2.1 整体架构
```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   前端应用       │───▶│   API网关        │───▶│  文件存取服务    │
│  (UniApp小程序) │    │ (Gateway Service)│    │ (File Service)  │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                                        │
                       ┌─────────────────────────────────┼─────────────────────────────────┐
                       │                                 │                                 │
                       ▼                                 ▼                                 ▼
              ┌─────────────────┐              ┌─────────────────┐              ┌─────────────────┐
              │   腾讯云COS     │              │   Redis缓存     │              │   MongoDB      │
              │ (主存储后端)     │              │  (元数据缓存)    │              │ (文件元数据)    │
              └─────────────────┘              └─────────────────┘              └─────────────────┘
```

### 2.2 核心技术栈
- **开发框架**：Spring Boot 3.x + Spring Cloud
- **云存储**：腾讯云COS Java SDK v5.6.x
- **数据库**：MongoDB（文件元数据存储）
- **缓存**：Redis（访问URLs缓存、热点文件缓存）
- **消息队列**：RabbitMQ（异步文件处理）
- **图片处理**：腾讯云数据万象 CI（可选）
- **监控**：Micrometer + Prometheus

### 2.3 部署架构
- **容器化**：Docker + Kubernetes
- **负载均衡**：支持水平扩展
- **存储**：分桶存储，按业务类型划分
- **CDN**：腾讯云CDN加速文件访问

## 三、功能设计

### 3.1 核心功能模块

#### 3.1.1 文件上传模块
- **单文件上传**：支持同步/异步上传
- **批量上传**：支持多文件批量处理
- **分片上传**：大文件分片上传，支持断点续传
- **格式校验**：文件类型、大小、内容校验
- **自动处理**：图片压缩、格式转换、水印添加

#### 3.1.2 文件下载模块
- **直接下载**：生成临时访问URL
- **流式下载**：支持大文件流式传输
- **权限控制**：基于用户权限的访问控制
- **访问统计**：下载次数、访问日志统计
- **CDN加速**：自动CDN分发

#### 3.1.3 文件管理模块
- **文件列表**：按条件查询文件列表
- **文件信息**：获取文件详细元数据
- **文件更新**：修改文件属性和权限
- **文件删除**：逻辑删除和物理删除
- **批量操作**：批量管理文件

#### 3.1.4 图片处理模块
- **尺寸调整**：智能裁剪、缩放
- **格式转换**：WebP、JPEG、PNG转换
- **质量压缩**：自适应质量压缩
- **水印处理**：动态水印添加
- **AI处理**：背景移除、图像增强

#### 3.1.5 文件分类管理
- **业务分类**：按业务模块分类存储
- **权限分类**：公开、私有、受限访问
- **生命周期**：临时、永久、定期清理
- **版本管理**：文件版本控制

### 3.2 业务文件分类

#### 3.2.1 用户相关文件
```
stylish-link-prod/
├── users/
│   ├── avatars/           # 用户头像
│   ├── photos/            # 用户照片
│   └── profiles/          # 用户档案文件
```

#### 3.2.2 衣橱相关文件
```
stylish-link-prod/
├── wardrobe/
│   ├── clothing/          # 衣物图片
│   ├── accessories/       # 饰品图片
│   ├── outfits/           # 搭配图片
│   └── processed/         # 处理后的图片
```

#### 3.2.3 AI生成内容
```
stylish-link-prod/
├── ai-generated/
│   ├── videos/            # AI生成视频
│   ├── images/            # AI生成图片
│   ├── recommendations/   # 推荐图片
│   └── templates/         # 模板文件
```

#### 3.2.4 系统资源
```
stylish-link-prod/
├── system/
│   ├── icons/             # 系统图标
│   ├── backgrounds/       # 背景图片
│   ├── templates/         # 模板文件
│   └── assets/            # 其他资源
```

#### 3.2.5 临时文件
```
stylish-link-prod/
├── temp/
│   ├── uploads/           # 上传中的文件
│   ├── processing/        # 处理中的文件
│   └── cache/             # 缓存文件
```

## 四、接口设计

### 4.1 核心接口定义

```java
// 文件服务接口定义
public interface FileService {
    
    // 文件上传
    UploadResponse uploadFile(UploadRequest request);
    UploadResponse uploadFileAsync(UploadRequest request);
    BatchUploadResponse uploadFiles(BatchUploadRequest request);
    ChunkUploadResponse uploadChunk(ChunkUploadRequest request);
    
    // 文件下载
    DownloadResponse downloadFile(DownloadRequest request);
    UrlResponse getDownloadUrl(GetUrlRequest request);
    UrlResponse getPresignedUrl(PresignedUrlRequest request);
    
    // 文件管理
    FileInfoResponse getFileInfo(GetFileInfoRequest request);
    FileListResponse listFiles(ListFilesRequest request);
    FileInfoResponse updateFileInfo(UpdateFileInfoRequest request);
    void deleteFile(DeleteFileRequest request);
    BatchOperationResponse batchOperation(BatchOperationRequest request);
    
    // 图片处理
    ProcessResponse processImage(ImageProcessRequest request);
    ProcessResponse resizeImage(ResizeImageRequest request);
    ProcessResponse addWatermark(WatermarkRequest request);
    
    // 文件分享
    ShareResponse shareFile(ShareFileRequest request);
    ShareInfoResponse getShareInfo(GetShareInfoRequest request);
    void cancelShare(CancelShareRequest request);
    
    // 统计信息
    FileStatsResponse getFileStats(FileStatsRequest request);
    UsageStatsResponse getUsageStats(UsageStatsRequest request);
}
```

### 4.2 数据模型设计

#### 4.2.1 文件实体模型
```java
// 文件基础信息
public class FileInfo {
    private String id;                    // 文件唯一ID
    private String userId;                // 所属用户ID
    private String originalName;          // 原始文件名
    private String storageName;           // 存储文件名
    private String bucketName;            // 存储桶名称
    private String objectKey;             // 对象键
    private FileCategory category;        // 文件分类
    private FileType fileType;            // 文件类型
    private String mimeType;              // MIME类型
    private Long fileSize;                // 文件大小（字节）
    private String md5Hash;               // MD5哈希值
    private String sha256Hash;            // SHA256哈希值
    private FileStatus status;            // 文件状态
    private AccessLevel accessLevel;      // 访问级别
    private Map<String, Object> metadata; // 扩展元数据
    private LocalDateTime createdAt;      // 创建时间
    private LocalDateTime updatedAt;      // 更新时间
    private LocalDateTime expiresAt;      // 过期时间
    private String createdBy;             // 创建者
    private String updatedBy;             // 更新者
}

// 文件处理记录
public class FileProcessRecord {
    private String id;                    // 处理记录ID
    private String fileId;                // 原文件ID
    private String processedFileId;       // 处理后文件ID
    private ProcessType processType;      // 处理类型
    private Map<String, Object> parameters; // 处理参数
    private ProcessStatus status;         // 处理状态
    private String errorMessage;          // 错误信息
    private LocalDateTime createdAt;      // 创建时间
    private LocalDateTime completedAt;    // 完成时间
}

// 文件访问记录
public class FileAccessLog {
    private String id;                    // 访问记录ID
    private String fileId;                // 文件ID
    private String userId;                // 访问用户ID
    private AccessType accessType;        // 访问类型
    private String clientIp;              // 客户端IP
    private String userAgent;             // 用户代理
    private Long downloadBytes;           // 下载字节数
    private LocalDateTime accessTime;     // 访问时间
    private Integer responseCode;         // 响应码
}

// 文件分享信息
public class FileShare {
    private String id;                    // 分享ID
    private String fileId;                // 文件ID
    private String userId;                // 分享用户ID
    private String shareCode;             // 分享码
    private String shareUrl;              // 分享链接
    private ShareType shareType;          // 分享类型
    private LocalDateTime expiresAt;      // 过期时间
    private Integer downloadLimit;        // 下载次数限制
    private Integer downloadCount;        // 已下载次数
    private Boolean isActive;             // 是否激活
    private LocalDateTime createdAt;      // 创建时间
}
```

#### 4.2.2 枚举定义
```java
// 文件分类
public enum FileCategory {
    USER_AVATAR,          // 用户头像
    USER_PHOTO,           // 用户照片
    CLOTHING_IMAGE,       // 衣物图片
    ACCESSORY_IMAGE,      // 饰品图片
    OUTFIT_IMAGE,         // 搭配图片
    AI_GENERATED_VIDEO,   // AI生成视频
    AI_GENERATED_IMAGE,   // AI生成图片
    SYSTEM_ICON,          // 系统图标
    SYSTEM_BACKGROUND,    // 系统背景
    TEMP_FILE             // 临时文件
}

// 访问级别
public enum AccessLevel {
    PUBLIC,               // 公开访问
    PRIVATE,              // 私有访问
    PROTECTED,            // 受保护访问
    INTERNAL              // 内部访问
}

// 文件状态
public enum FileStatus {
    UPLOADING,            // 上传中
    UPLOADED,             // 已上传
    PROCESSING,           // 处理中
    PROCESSED,            // 已处理
    AVAILABLE,            // 可用
    DELETED,              // 已删除
    EXPIRED               // 已过期
}

// 处理类型
public enum ProcessType {
    RESIZE,               // 尺寸调整
    COMPRESS,             // 压缩
    WATERMARK,            // 水印
    FORMAT_CONVERT,       // 格式转换
    THUMBNAIL,            // 缩略图
    BACKGROUND_REMOVAL    // 背景移除
}
```

### 4.3 RESTful API 设计

#### 4.3.1 文件上传 API
```http
POST /api/v1/files/upload
Content-Type: multipart/form-data

# 单文件上传
{
    "file": "文件内容",
    "category": "USER_AVATAR",
    "accessLevel": "PRIVATE",
    "metadata": {
        "description": "用户头像",
        "tags": ["avatar", "profile"]
    }
}

# 响应
{
    "code": 0,
    "message": "上传成功",
    "data": {
        "fileId": "file_123456789",
        "url": "https://cos.example.com/files/...",
        "thumbnailUrl": "https://cos.example.com/thumbnails/...",
        "fileInfo": { ... }
    }
}
```

#### 4.3.2 文件下载 API
```http
GET /api/v1/files/{fileId}/download
Authorization: Bearer {token}

# 响应
{
    "code": 0,
    "message": "获取下载链接成功",
    "data": {
        "downloadUrl": "https://cos.example.com/files/...",
        "expiresAt": "2024-01-01T12:00:00Z",
        "fileInfo": { ... }
    }
}
```

#### 4.3.3 文件管理 API
```http
# 获取文件信息
GET /api/v1/files/{fileId}

# 获取文件列表
GET /api/v1/files?category=USER_AVATAR&page=1&size=20

# 更新文件信息
PUT /api/v1/files/{fileId}
{
    "metadata": {
        "description": "更新的描述"
    },
    "accessLevel": "PUBLIC"
}

# 删除文件
DELETE /api/v1/files/{fileId}
```

#### 4.3.4 图片处理 API
```http
POST /api/v1/files/{fileId}/process
{
    "processType": "RESIZE",
    "parameters": {
        "width": 800,
        "height": 600,
        "quality": 80
    }
}
```

## 五、安全设计

### 5.1 访问控制
- **身份认证**：JWT Token验证
- **权限校验**：基于用户角色和文件访问级别
- **API限流**：防止恶意上传和下载
- **IP白名单**：关键操作IP限制

### 5.2 文件安全
- **文件校验**：类型、大小、内容安全检查
- **病毒扫描**：集成云安全扫描服务
- **内容审核**：图片内容智能审核
- **加密存储**：敏感文件加密存储

### 5.3 传输安全
- **HTTPS**：全程HTTPS加密传输
- **签名验证**：文件完整性校验
- **防盗链**：Referer验证和时效性控制
- **访问日志**：完整的访问审计日志

## 六、性能优化

### 6.1 上传优化
- **分片上传**：大文件自动分片
- **并发上传**：多文件并发处理
- **断点续传**：支持上传中断恢复
- **预签名URL**：减少服务器中转

### 6.2 下载优化
- **CDN加速**：全球CDN节点分发
- **缓存策略**：多级缓存机制
- **压缩传输**：图片自适应压缩
- **懒加载**：按需加载机制

### 6.3 存储优化
- **智能分层**：热冷数据分层存储
- **生命周期**：自动清理过期文件
- **重复检测**：避免重复存储
- **成本控制**：存储成本监控

## 七、监控运维

### 7.1 性能监控
- **上传成功率**：文件上传成功率统计
- **下载延迟**：文件下载响应时间
- **存储用量**：各类文件存储用量监控
- **带宽使用**：上传下载带宽监控

### 7.2 业务监控
- **用户活跃度**：文件操作用户活跃度
- **热点文件**：高频访问文件统计
- **错误率**：各类操作错误率监控
- **容量预警**：存储容量预警机制

### 7.3 运维管理
- **日志管理**：结构化日志收集
- **告警机制**：异常情况及时告警
- **备份恢复**：数据备份和恢复策略
- **故障处理**：故障快速定位和处理

## 八、扩展性设计

### 8.1 水平扩展
- **无状态设计**：服务无状态，支持水平扩展
- **分片策略**：按用户或文件类型分片
- **负载均衡**：智能负载分发
- **缓存分布**：分布式缓存支持

### 8.2 功能扩展
- **多云支持**：支持多个云存储提供商
- **插件架构**：文件处理插件化
- **API版本**：向后兼容的API版本管理
- **协议支持**：支持多种传输协议

### 8.3 业务扩展
- **多租户**：支持多租户隔离
- **国际化**：支持多区域部署
- **合规性**：满足不同地区法规要求
- **集成能力**：与第三方系统集成

## 九、部署配置

### 9.1 环境配置
```yaml
# application.yml
spring:
  application:
    name: file-service
  
# COS配置
cos:
  secretId: ${COS_SECRET_ID}
  secretKey: ${COS_SECRET_KEY}
  region: ${COS_REGION:ap-guangzhou}
  buckets:
    public: stylish-link-public
    private: stylish-link-private
    temp: stylish-link-temp
  
# 文件配置
file:
  upload:
    maxSize: 10MB
    allowedTypes: [jpg, jpeg, png, gif, webp, mp4]
    chunkSize: 1MB
  download:
    urlExpireSeconds: 3600
  storage:
    autoCleanup: true
    tempFileExpireHours: 24
```

### 9.2 Docker配置
```dockerfile
FROM openjdk:17-jdk-slim
COPY target/file-service.jar app.jar
EXPOSE 8080
ENTRYPOINT ["java", "-jar", "/app.jar"]
```

### 9.3 Kubernetes配置
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: file-service
spec:
  replicas: 3
  selector:
    matchLabels:
      app: file-service
  template:
    metadata:
      labels:
        app: file-service
    spec:
      containers:
      - name: file-service
        image: stylish-link/file-service:latest
        ports:
        - containerPort: 8080
        env:
        - name: COS_SECRET_ID
          valueFrom:
            secretKeyRef:
              name: cos-credentials
              key: secretId
        - name: COS_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: cos-credentials
              key: secretKey
```

## 十、开发指南

### 10.1 快速开始
1. 克隆项目代码
2. 配置COS密钥和存储桶
3. 启动MongoDB和Redis
4. 运行Spring Boot应用
5. 测试文件上传下载接口

### 10.2 开发规范
- **代码规范**：遵循阿里巴巴Java开发规范
- **接口设计**：RESTful API设计原则
- **错误处理**：统一错误码和异常处理
- **日志规范**：结构化日志输出
- **测试覆盖**：单元测试覆盖率>80%

### 10.3 最佳实践
- **安全第一**：所有文件操作必须通过权限验证
- **性能优先**：合理使用缓存和CDN
- **监控完善**：关键指标实时监控
- **文档齐全**：API文档和开发文档及时更新

---

本文档为StylishLink文件存取服务的设计蓝图，后续根据业务发展和技术演进持续优化更新。 