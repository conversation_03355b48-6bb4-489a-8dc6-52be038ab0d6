# 服务注册中心（eureka-server）API接口文档

## 1. 概述
Eureka注册中心负责所有微服务的注册、发现、健康检查，是服务治理的基础组件。

## 2. 认证与通用说明
- 认证方式：无（默认Eureka控制台和API开放，生产环境建议加权限控制）
- 通用请求头：Content-Type: application/json
- 通用响应结构：
    - 大部分接口为XML或JSON格式，部分为纯文本
- 错误码说明：
    | code | 含义         | 说明           |
    |------|--------------|----------------|
    | 200  | 成功         |                |
    | 401  | 未认证       |                |
    | 404  | 未找到       |                |
    | 500  | 服务异常     |                |
    | ...  | ...          |                |

## 3. 接口列表

### 3.1 服务注册
- **接口路径**：`/eureka/apps/{APP_NAME}`
- **请求方法**：POST
- **功能描述**：服务实例向注册中心注册自身信息
- **请求参数**：服务实例信息（XML/JSON）
- **响应参数**：无，200表示成功
- **备注**：由微服务自动完成，无需前端调用

---

### 3.2 服务下线
- **接口路径**：`/eureka/apps/{APP_NAME}/{INSTANCE_ID}`
- **请求方法**：DELETE
- **功能描述**：服务实例主动下线
- **请求参数**：无
- **响应参数**：无，200表示成功

---

### 3.3 服务发现
- **接口路径**：`/eureka/apps/{APP_NAME}`
- **请求方法**：GET
- **功能描述**：查询指定服务的所有实例信息
- **请求参数**：无
- **响应参数**：服务实例列表（XML/JSON）
- **响应示例**：
    ```xml
    <application>
      <name>USER-SERVICE</name>
      <instance>
        <instanceId>user-service:8080</instanceId>
        <hostName>127.0.0.1</hostName>
        <status>UP</status>
        ...
      </instance>
    </application>
    ```

---

### 3.4 查询所有服务
- **接口路径**：`/eureka/apps`
- **请求方法**：GET
- **功能描述**：查询所有已注册服务及其实例
- **响应参数**：服务列表（XML/JSON）

---

### 3.5 健康检查
- **接口路径**：`/eureka/status`
- **请求方法**：GET
- **功能描述**：获取Eureka自身健康状态
- **响应参数**：
    | 字段名 | 类型   | 说明         |
    |--------|--------|--------------|
    | status | string | 状态（UP/DOWN）|
    | ...    | ...    | ...          |
- **响应示例**：
    ```json
    {
      "status": "UP"
    }
    ```

---

## 4. 典型业务流程示例
- 微服务启动 -> 注册到Eureka -> 服务发现 -> 健康检查 -> 下线

---

## 5. 变更历史
- 2025-05-12 初稿 by AI 