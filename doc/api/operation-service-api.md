# 运营服务（operation-service）API接口文档

## 1. 概述
运营服务负责灵感值账户、任务系统、成就系统、活动管理、团队长管理、等级与特权、充值订阅等用户激励与商业化相关功能。

## 2. 认证与通用说明
- 认证方式：JWT（登录成功后在响应header中返回，后续接口需在请求header中携带Authorization: Bearer {token}）
- 通用请求头：
  - Content-Type: application/json
  - Authorization: Bearer {token} （需要认证的接口）
- 通用响应结构：
    ```json
    {
      "code": 0,
      "msg": "success",
      "data": { ... }
    }
    ```
- 错误码说明：
    | code | 含义         | 说明           |
    |------|--------------|----------------|
    | 0    | 成功         |                |
    | 1001 | 参数错误     |                |
    | 1002 | 未认证/Token无效 |           |
    | 1003 | 权限不足     |                |
    | 7001 | 灵感值异常   |                |
    | 7002 | 任务异常     |                |
    | 7003 | 成就异常     |                |
    | 7004 | 活动异常     |                |
    | ...  | ...          |                |

## 3. 接口列表

### 3.1 灵感值账户
- **接口路径**：`/api/operation/inspiration`
- **请求方法**：GET
- **功能描述**：获取当前用户灵感值账户信息
- **响应参数**：
    | 字段名 | 类型   | 说明         |
    |--------|--------|--------------|
    | totalInspiration | int | 总灵感值 |
    | chargedInspiration | int | 充值获得 |
    | activityInspiration | int | 活动获得 |
    | ...    | ...    | ...          |
- **响应示例**：
    ```json
    {
      "code": 0,
      "msg": "success",
      "data": {
        "totalInspiration": 1000,
        "chargedInspiration": 500,
        "activityInspiration": 200
      }
    }
    ```

---

### 3.2 灵感值交易记录
- **接口路径**：`/api/operation/inspiration/history`
- **请求方法**：GET
- **功能描述**：获取灵感值变动历史
- **响应参数**：交易列表

---

### 3.3 任务系统
- **接口路径**：`/api/operation/task`
- **请求方法**：GET/POST
- **功能描述**：获取可用任务、提交任务完成
- **请求参数**：任务ID、完成数据等
- **响应参数**：任务列表、进度、奖励等

---

### 3.4 成就系统
- **接口路径**：`/api/operation/achievement`
- **请求方法**：GET/POST
- **功能描述**：获取用户成就、解锁成就
- **请求参数/响应参数/示例**：同上

---

### 3.5 活动管理
- **接口路径**：`/api/operation/activity`
- **请求方法**：GET/POST
- **功能描述**：获取当前活动、参与活动、领奖励
- **请求参数/响应参数/示例**：同上

---

### 3.6 团队长管理
- **接口路径**：`/api/operation/team`
- **请求方法**：GET/POST
- **功能描述**：团队长注册、团队成员管理、团队统计
- **请求参数/响应参数/示例**：同上

---

### 3.7 等级与特权
- **接口路径**：`/api/operation/level`
- **请求方法**：GET
- **功能描述**：获取用户等级、特权信息
- **响应参数**：等级、特权列表等

---

### 3.8 充值与订阅
- **接口路径**：`/api/operation/recharge`、`/api/operation/subscription`
- **请求方法**：GET/POST
- **功能描述**：获取充值产品、创建充值订单、获取订阅状态、取消订阅等
- **请求参数/响应参数/示例**：同上

---

## 4. 典型业务流程示例
- 获取灵感值账户 -> 领取任务 -> 完成任务 -> 解锁成就 -> 参与活动 -> 充值/订阅

---

## 5. 变更历史
- 2025-05-12 初稿 by AI 