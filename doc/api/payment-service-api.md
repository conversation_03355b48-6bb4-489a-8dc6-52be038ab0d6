# 支付服务（payment-service）API接口文档

## 1. 概述
支付服务负责处理所有支付相关功能，包括支付处理、订单管理、充值管理、月卡/年卡订阅、支付通知、发票管理和退款处理等核心功能。

## 2. 认证与通用说明
- 认证方式：JWT（登录成功后在响应header中返回，后续接口需在请求header中携带Authorization: Bearer {token}）
- 通用请求头：
  - Content-Type: application/json
  - Authorization: Bearer {token} （需要认证的接口）
- 通用响应结构：
    ```json
    {
      "code": 0,
      "msg": "success",
      "data": { ... }
    }
    ```
- 错误码说明：
    | code | 含义         | 说明           |
    |------|--------------|----------------|
    | 0    | 成功         |                |
    | 1001 | 参数错误     |                |
    | 1002 | 未认证/Token无效 |           |
    | 1003 | 权限不足     |                |
    | 5001 | 支付失败     | 支付过程异常   |
    | 5002 | 订单不存在   |                |
    | 5003 | 余额不足     |                |
    | 5004 | 重复支付     |                |
    | 5005 | 订单已过期   |                |
    | 5006 | 退款失败     |                |
    | 5007 | 发票申请失败 |                |
    | 5008 | 订阅创建失败 |                |
    | 5009 | 订阅取消失败 |                |
    | 5010 | 支付渠道异常 |                |
    | 5011 | 支付金额无效 |                |
    | 5012 | 发票信息无效 |                |
    | 5013 | 退款金额超限 |                |
    | 5014 | 重复退款申请 |                |
    | 5015 | 订阅状态异常 |                |

## 3. 接口列表

### 3.1 支付处理
#### 3.1.1 创建支付订单
- **接口路径**：`/api/payment/order/create`
- **请求方法**：POST
- **功能描述**：创建新的支付订单
- **请求参数**：
    ```json
    {
      "userId": "string",
      "originOrderId": "string",
      "type": "RECHARGE/SUBSCRIPTION",
      "channel": "WECHAT/ALIPAY",
      "amount": 10000,
      "subject": "灵感值充值",
      "description": "充值1000灵感值",
      "clientIp": "string",
      "notifyUrl": "string",
      "returnUrl": "string"
    }
    ```
- **响应参数**：
    ```json
    {
      "orderId": "string",
      "paymentUrl": "string",
      "qrCode": "string",
      "expiredAt": "2025-05-12T10:00:00Z"
    }
    ```

#### 3.1.2 查询支付订单
- **接口路径**：`/api/payment/order/{orderId}`
- **请求方法**：GET
- **功能描述**：查询支付订单状态
- **响应参数**：
    ```json
    {
      "orderId": "string",
      "status": "PENDING/PAID/EXPIRED/FAILED",
      "amount": 10000,
      "paidAt": "2025-05-12T10:00:00Z",
      "channel": "WECHAT/ALIPAY",
      "transactionId": "string"
    }
    ```

### 3.2 充值管理
#### 3.2.1 获取充值产品列表
- **接口路径**：`/api/payment/recharge/products`
- **请求方法**：GET
- **功能描述**：获取可用的充值产品列表
- **响应参数**：
    ```json
    {
      "products": [{
        "id": "string",
        "name": "string",
        "price": 10000,
        "baseAmount": 1000,
        "bonusAmount": 100,
        "totalAmount": 1100,
        "isPopular": true,
        "isLimited": false,
        "validFrom": "2025-05-12T00:00:00Z",
        "validTo": "2025-06-12T00:00:00Z"
      }]
    }
    ```

#### 3.2.2 创建充值订单
- **接口路径**：`/api/payment/recharge/order`
- **请求方法**：POST
- **功能描述**：创建灵感值充值订单
- **请求参数**：
    ```json
    {
      "userId": "string",
      "productId": "string",
      "channel": "WECHAT/ALIPAY"
    }
    ```
- **响应参数**：同创建支付订单响应

### 3.3 订阅管理
#### 3.3.1 获取订阅产品列表
- **接口路径**：`/api/payment/subscription/products`
- **请求方法**：GET
- **功能描述**：获取可用的订阅产品列表
- **响应参数**：
    ```json
    {
      "products": [{
        "id": "string",
        "name": "string",
        "type": "MONTHLY/YEARLY",
        "price": 10000,
        "periodMonths": 1,
        "dailyReward": 100,
        "totalReward": 3000,
        "privileges": ["VIP_CHAT", "UNLIMITED_RECOMMEND"],
        "discount": 0.8
      }]
    }
    ```

#### 3.3.2 创建订阅
- **接口路径**：`/api/payment/subscription/create`
- **请求方法**：POST
- **功能描述**：创建新的订阅
- **请求参数**：
    ```json
    {
      "userId": "string",
      "productId": "string",
      "autoRenew": true,
      "channel": "WECHAT/ALIPAY"
    }
    ```
- **响应参数**：同创建支付订单响应

#### 3.3.3 取消订阅
- **接口路径**：`/api/payment/subscription/cancel`
- **请求方法**：POST
- **功能描述**：取消自动续费订阅
- **请求参数**：
    ```json
    {
      "userId": "string",
      "subscriptionId": "string"
    }
    ```

### 3.4 发票管理
#### 3.4.1 申请发票
- **接口路径**：`/api/payment/invoice/request`
- **请求方法**：POST
- **功能描述**：申请开具发票
- **请求参数**：
    ```json
    {
      "userId": "string",
      "orderId": "string",
      "type": "PERSONAL/COMPANY",
      "title": "string",
      "taxNumber": "string",
      "email": "string",
      "amount": 10000
    }
    ```
- **响应参数**：
    ```json
    {
      "invoiceId": "string",
      "status": "PENDING/PROCESSING/COMPLETED",
      "estimatedTime": "2025-05-13T10:00:00Z"
    }
    ```

#### 3.4.2 查询发票状态
- **接口路径**：`/api/payment/invoice/{invoiceId}`
- **请求方法**：GET
- **功能描述**：查询发票开具状态
- **响应参数**：
    ```json
    {
      "invoiceId": "string",
      "status": "PENDING/PROCESSING/COMPLETED",
      "invoiceUrl": "string",
      "issuedAt": "2025-05-12T10:00:00Z"
    }
    ```

### 3.5 退款处理
#### 3.5.1 申请退款
- **接口路径**：`/api/payment/refund/request`
- **请求方法**：POST
- **功能描述**：申请订单退款
- **请求参数**：
    ```json
    {
      "userId": "string",
      "orderId": "string",
      "amount": 10000,
      "reason": "string",
      "remarks": "string"
    }
    ```
- **响应参数**：
    ```json
    {
      "refundId": "string",
      "status": "PENDING/PROCESSING/COMPLETED/FAILED",
      "estimatedTime": "2025-05-13T10:00:00Z"
    }
    ```

#### 3.5.2 查询退款状态
- **接口路径**：`/api/payment/refund/{refundId}`
- **请求方法**：GET
- **功能描述**：查询退款处理状态
- **响应参数**：
    ```json
    {
      "refundId": "string",
      "status": "PENDING/PROCESSING/COMPLETED/FAILED",
      "amount": 10000,
      "processedAt": "2025-05-12T10:00:00Z",
      "completedAt": "2025-05-12T10:00:00Z"
    }
    ```

## 4. 典型业务流程示例

### 4.1 充值流程
1. 获取充值产品列表
2. 选择充值产品创建订单
3. 调用支付接口
4. 等待支付回调
5. 查询订单状态确认

### 4.2 订阅流程
1. 获取订阅产品列表
2. 创建订阅订单
3. 完成支付
4. 激活订阅特权
5. 自动续费管理

### 4.3 退款流程
1. 提交退款申请
2. 审核退款资格
3. 处理退款
4. 查询退款状态
5. 完成退款

## 5. 变更历史
- 2025-05-12 初稿 by AI 