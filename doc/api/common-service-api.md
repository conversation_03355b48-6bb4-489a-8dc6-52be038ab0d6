# 公共模块（common）API接口文档

## 1. 概述
common模块为各微服务提供通用工具、DTO、VO、异常处理、通用配置等基础能力，部分通用接口可供前端或其他服务调用。

## 2. 认证与通用说明
- 认证方式：JWT（登录成功后在响应header中返回，后续接口需在请求header中携带Authorization: Bearer {token}）
- 部分工具接口无需认证，部分需要认证
- 通用请求头：
  - Content-Type: application/json
  - Authorization: Bearer {token} （需要认证的接口）
- 通用响应结构：
    ```json
    {
      "code": 0,
      "msg": "success",
      "data": { ... }
    }
    ```
- 错误码说明：
    | code | 含义         | 说明           |
    |------|--------------|----------------|
    | 0    | 成功         |                |
    | 1001 | 参数错误     |                |
    | 1002 | 未认证/Token无效 |           |
    | 1003 | 权限不足     |                |
    | 9001 | 通用异常     |                |
    | ...  | ...          |                |

## 3. 接口列表

### 3.1 通用异常处理
- **接口路径**：全局异常捕获，无固定路径
- **功能描述**：所有服务接口发生异常时，统一返回如下结构
- **响应参数**：
    | 字段名 | 类型   | 说明         |
    |--------|--------|--------------|
    | code   | int    | 错误码       |
    | msg    | string | 错误信息     |
    | data   | object | 附加数据     |
- **响应示例**：
    ```json
    {
      "code": 9001,
      "msg": "系统异常，请稍后重试",
      "data": null
    }
    ```

---

### 3.2 通用DTO/VO说明
- **功能描述**：common模块定义了大量DTO/VO对象，供各服务接口复用
- **示例**：
    ```json
    {
      "userId": "u123456",
      "nickname": "小明",
      "avatar": "https://xxx.com/avatar.jpg"
    }
    ```
- **备注**：具体字段以各服务接口文档为准

---

### 3.3 通用配置获取
- **接口路径**：`/api/common/config`
- **请求方法**：GET
- **功能描述**：获取全局通用配置（如枚举、常量、前端下拉选项等）
- **请求参数**：
    | 参数名 | 类型   | 必填 | 说明         |
    |--------|--------|------|--------------|
    | key    | string | 否   | 配置项key    |
- **响应参数**：
    | 字段名 | 类型   | 说明         |
    |--------|--------|--------------|
    | config | object | 配置信息     |
- **响应示例**：
    ```json
    {
      "code": 0,
      "msg": "success",
      "data": {
        "config": {
          "gender": [
            {"value": 1, "label": "男"},
            {"value": 2, "label": "女"}
          ]
        }
      }
    }
    ```

---

## 4. 典型业务流程示例
- 前端启动 -> 获取通用配置 -> 业务接口调用 -> 异常统一处理

---

## 5. 变更历史
- 2025-05-12 初稿 by AI 