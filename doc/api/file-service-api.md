# 文件存取服务 API 文档

## 服务概述

文件存取服务（file-service）是StylishLink系统的核心基础服务，基于腾讯云COS提供统一的文件管理能力。

- **服务名称**: file-service
- **服务端口**: 8088  
- **API版本**: v1
- **认证方式**: JWT（登录成功后在响应header中返回，后续接口需在请求header中携带Authorization: Bearer {token}）

## 通用说明

### 请求格式
- Content-Type: application/json（除文件上传外）
- 文件上传: multipart/form-data
- 认证: Authorization: Bearer {token} （需要认证的接口）

### 响应格式
```json
{
    "code": 0,
    "message": "操作成功",
    "data": { ... },
    "timestamp": "2024-01-01T12:00:00Z"
}
```

### 错误码定义
基于后端技术架构设计文档的错误码规范，为文件服务分配专属错误码段：

#### 通用错误码（1xxx）
| 错误码 | 含义 | 说明 |
|--------|------|------|
| 0 | 成功 | 操作成功 |
| 1001 | 参数错误 | 请求参数验证失败 |
| 1002 | 未认证/Token无效 | 用户未登录或Token已过期 |
| 1003 | 权限不足 | 用户无权限执行该操作 |
| 1004 | 资源不存在 | 请求的资源未找到 |
| 1008 | 请求频率超限 | 触发限流规则 |

#### 文件服务错误码（10xxx）
| 错误码 | 含义 | 说明 |
|--------|------|------|
| 10001 | 文件不存在 | 文件记录未找到 |
| 10002 | 文件上传失败 | 文件上传过程异常 |
| 10003 | 文件下载失败 | 文件下载过程异常 |
| 10004 | 文件格式不支持 | 不支持的文件格式 |
| 10005 | 文件大小超限 | 文件大小超过限制 |
| 10006 | 存储配额不足 | 用户存储配额不足 |
| 10007 | 文件处理失败 | 图片处理或转码失败 |
| 10008 | 分片上传失败 | 分片上传过程异常 |
| 10009 | 文件校验失败 | 文件完整性校验失败 |
| 10010 | 存储服务异常 | 腾讯云COS服务异常 |

#### 系统级错误（9xxx）
| 错误码 | 含义 | 说明 |
|--------|------|------|
| 9001 | 通用异常 | 系统通用异常 |
| 9005 | 文件系统异常 | 文件存储异常 |

## API接口

### 1. 文件上传

#### 1.1 单文件上传
```http
POST /api/v1/files/upload
Content-Type: multipart/form-data
Authorization: Bearer {token}
```

**请求参数**
- `file` (File, required): 上传的文件
- `category` (String, required): 文件分类
- `accessLevel` (String, optional): 访问级别，默认PRIVATE
- `metadata` (JSON String, optional): 扩展元数据

**文件分类枚举**
- `USER_AVATAR`: 用户头像
- `USER_PHOTO`: 用户照片  
- `CLOTHING_IMAGE`: 衣物图片
- `ACCESSORY_IMAGE`: 饰品图片
- `OUTFIT_IMAGE`: 搭配图片
- `AI_GENERATED_VIDEO`: AI生成视频
- `AI_GENERATED_IMAGE`: AI生成图片
- `SYSTEM_ICON`: 系统图标
- `SYSTEM_BACKGROUND`: 系统背景
- `TEMP_FILE`: 临时文件

**访问级别枚举**
- `PUBLIC`: 公开访问
- `PRIVATE`: 私有访问
- `PROTECTED`: 受保护访问
- `INTERNAL`: 内部访问

**响应示例**
```json
{
    "code": 0,
    "message": "上传成功",
    "data": {
        "fileId": "file_123456789",
        "url": "https://stylish-link-cdn.com/files/abc123.jpg",
        "thumbnailUrl": "https://stylish-link-cdn.com/thumbnails/abc123_thumb.jpg",
        "fileInfo": {
            "id": "file_123456789",
            "originalName": "avatar.jpg",
            "fileSize": 1048576,
            "mimeType": "image/jpeg",
            "md5Hash": "d41d8cd98f00b204e9800998ecf8427e",
            "status": "AVAILABLE",
            "createdAt": "2024-01-01T12:00:00Z"
        }
    }
}
```

#### 1.2 批量文件上传
```http
POST /api/v1/files/upload-batch
Content-Type: multipart/form-data
Authorization: Bearer {token}
```

**请求参数**
- `files` (File[], required): 上传的文件列表
- `category` (String, required): 文件分类
- `accessLevel` (String, optional): 访问级别

**响应示例**
```json
{
    "code": 0,
    "message": "批量上传完成",
    "data": {
        "successCount": 3,
        "failedCount": 1,
        "results": [
            {
                "originalName": "image1.jpg",
                "success": true,
                "fileId": "file_001",
                "url": "https://...",
                "error": null
            },
            {
                "originalName": "image2.jpg", 
                "success": false,
                "fileId": null,
                "url": null,
                "error": "文件格式不支持"
            }
        ]
    }
}
```

#### 1.3 初始化分片上传
```http
POST /api/v1/files/multipart/initiate
Content-Type: application/json
Authorization: Bearer {token}
```

**请求体**
```json
{
    "fileName": "large_video.mp4",
    "fileSize": 104857600,
    "category": "AI_GENERATED_VIDEO",
    "accessLevel": "PRIVATE",
    "chunkSize": 1048576,
    "metadata": {
        "description": "AI生成视频"
    }
}
```

**响应示例**
```json
{
    "code": 0,
    "message": "初始化分片上传成功",
    "data": {
        "uploadId": "upload_123456789",
        "fileId": "file_987654321",
        "chunkSize": 1048576,
        "totalChunks": 100,
        "expiresAt": "2024-01-01T14:00:00Z"
    }
}
```

#### 1.4 分片上传
```http
POST /api/v1/files/multipart/upload
Content-Type: multipart/form-data
Authorization: Bearer {token}
```

**请求参数**
- `chunk` (File, required): 文件分片
- `uploadId` (String, required): 上传任务ID
- `chunkIndex` (Integer, required): 分片索引（从0开始）
- `chunkHash` (String, required): 分片MD5哈希

**响应示例**
```json
{
    "code": 0,
    "message": "分片上传成功",
    "data": {
        "uploadId": "upload_123456789",
        "chunkIndex": 1,
        "chunkHash": "abc123def456",
        "uploaded": true,
        "etag": "\"9bb58f26192e4ba00f01e2e7b136bbd8\""
    }
}
```

#### 1.5 获取已上传分片列表
```http
GET /api/v1/files/multipart/{uploadId}/parts
Authorization: Bearer {token}
```

**响应示例**
```json
{
    "code": 0,
    "message": "获取分片列表成功",
    "data": {
        "uploadId": "upload_123456789",
        "totalChunks": 100,
        "uploadedChunks": [0, 1, 2, 5, 10],
        "uploadedCount": 5,
        "parts": [
            {
                "chunkIndex": 0,
                "etag": "\"9bb58f26192e4ba00f01e2e7b136bbd8\"",
                "size": 1048576,
                "uploadedAt": "2024-01-01T12:01:00Z"
            }
        ]
    }
}
```

#### 1.6 完成分片上传
```http
POST /api/v1/files/multipart/complete
Content-Type: application/json
Authorization: Bearer {token}
```

**请求体**
```json
{
    "uploadId": "upload_123456789",
    "parts": [
        {
            "chunkIndex": 0,
            "etag": "\"9bb58f26192e4ba00f01e2e7b136bbd8\""
        },
        {
            "chunkIndex": 1,
            "etag": "\"8aa57e25191d3ba00f01e2e7b125aac7\""
        }
    ]
}
```

**响应示例**
```json
{
    "code": 0,
    "message": "分片上传完成",
    "data": {
        "fileId": "file_987654321",
        "url": "https://stylish-link-cdn.com/files/large_video.mp4",
        "fileInfo": {
            "id": "file_987654321",
            "originalName": "large_video.mp4",
            "fileSize": 104857600,
            "mimeType": "video/mp4",
            "md5Hash": "complete_file_md5_hash",
            "status": "AVAILABLE",
            "createdAt": "2024-01-01T12:30:00Z"
        }
    }
}
```

#### 1.7 取消分片上传
```http
DELETE /api/v1/files/multipart/{uploadId}
Authorization: Bearer {token}
```

### 2. 文件下载

#### 2.1 获取下载链接
```http
GET /api/v1/files/{fileId}/download-url
Authorization: Bearer {token}
```

**路径参数**
- `fileId` (String, required): 文件ID

**查询参数**
- `expiresIn` (Integer, optional): 链接有效期（秒），默认3600
- `attachment` (Boolean, optional): 是否作为附件下载，默认false

**响应示例**
```json
{
    "code": 0,
    "message": "获取下载链接成功",
    "data": {
        "downloadUrl": "https://stylish-link-cdn.com/files/abc123.jpg?expires=...",
        "expiresAt": "2024-01-01T13:00:00Z",
        "fileInfo": {
            "id": "file_123456789",
            "originalName": "avatar.jpg",
            "fileSize": 1048576,
            "mimeType": "image/jpeg"
        }
    }
}
```

#### 2.2 直接下载文件
```http
GET /api/v1/files/{fileId}/download
Authorization: Bearer {token}
```

**响应**: 文件二进制流

#### 2.3 获取预签名URL
```http
POST /api/v1/files/presigned-url
Content-Type: application/json
Authorization: Bearer {token}
```

**请求体**
```json
{
    "fileName": "upload.jpg",
    "category": "USER_AVATAR",
    "accessLevel": "PRIVATE",
    "expiresIn": 3600,
    "operation": "PUT"
}
```

**响应示例**
```json
{
    "code": 0,
    "message": "获取预签名URL成功",
    "data": {
        "presignedUrl": "https://stylish-link-1250000000.cos.ap-guangzhou.myqcloud.com/upload.jpg?sign=...",
        "method": "PUT",
        "expiresAt": "2024-01-01T13:00:00Z",
        "headers": {
            "Content-Type": "image/jpeg"
        }
    }
}
```

### 3. 文件管理

#### 3.1 获取文件信息
```http
GET /api/v1/files/{fileId}
Authorization: Bearer {token}
```

**响应示例**
```json
{
    "code": 0,
    "message": "获取文件信息成功",
    "data": {
        "id": "file_123456789",
        "userId": "user_001",
        "originalName": "avatar.jpg",
        "storageName": "abc123.jpg",
        "bucketName": "stylish-link-private",
        "objectKey": "users/user_001/avatars/abc123.jpg",
        "category": "USER_AVATAR",
        "fileType": "IMAGE",
        "mimeType": "image/jpeg",
        "fileSize": 1048576,
        "md5Hash": "d41d8cd98f00b204e9800998ecf8427e",
        "status": "AVAILABLE",
        "accessLevel": "PRIVATE",
        "url": "https://stylish-link-cdn.com/files/abc123.jpg",
        "thumbnailUrl": "https://stylish-link-cdn.com/thumbnails/abc123_thumb.jpg",
        "metadata": {
            "width": 800,
            "height": 600,
            "description": "用户头像"
        },
        "createdAt": "2024-01-01T12:00:00Z",
        "updatedAt": "2024-01-01T12:00:00Z"
    }
}
```

#### 3.2 获取文件列表
```http
GET /api/v1/files
Authorization: Bearer {token}
```

**查询参数**
- `category` (String, optional): 文件分类筛选
- `status` (String, optional): 文件状态筛选
- `page` (Integer, optional): 页码，默认1
- `size` (Integer, optional): 每页大小，默认20
- `sortBy` (String, optional): 排序字段，默认createdAt
- `sortOrder` (String, optional): 排序方向，ASC/DESC，默认DESC

**响应示例**
```json
{
    "code": 0,
    "message": "获取文件列表成功",
    "data": {
        "total": 100,
        "page": 1,
        "size": 20,
        "pages": 5,
        "files": [
            {
                "id": "file_001",
                "originalName": "avatar1.jpg",
                "category": "USER_AVATAR",
                "fileSize": 1048576,
                "status": "AVAILABLE",
                "url": "https://...",
                "createdAt": "2024-01-01T12:00:00Z"
            }
        ]
    }
}
```

#### 3.3 更新文件信息
```http
PUT /api/v1/files/{fileId}
Content-Type: application/json
Authorization: Bearer {token}
```

**请求体**
```json
{
    "accessLevel": "PUBLIC",
    "metadata": {
        "description": "更新的描述",
        "tags": ["新标签"]
    }
}
```

#### 3.4 删除文件
```http
DELETE /api/v1/files/{fileId}
Authorization: Bearer {token}
```

**查询参数**
- `force` (Boolean, optional): 是否物理删除，默认false（逻辑删除）

### 4. 图片处理

#### 4.1 图片处理
```http
POST /api/v1/files/{fileId}/process
Content-Type: application/json
Authorization: Bearer {token}
```

**请求体**
```json
{
    "processType": "RESIZE",
    "parameters": {
        "width": 800,
        "height": 600,
        "quality": 80,
        "format": "JPEG"
    },
    "saveAsNew": true
}
```

**处理类型**
- `RESIZE`: 尺寸调整
- `COMPRESS`: 压缩
- `WATERMARK`: 水印
- `FORMAT_CONVERT`: 格式转换
- `THUMBNAIL`: 缩略图
- `BACKGROUND_REMOVAL`: 背景移除

**响应示例**
```json
{
    "code": 0,
    "message": "图片处理完成",
    "data": {
        "originalFileId": "file_123456789",
        "processedFileId": "file_987654321",
        "processType": "RESIZE",
        "url": "https://stylish-link-cdn.com/files/processed_abc123.jpg",
        "parameters": {
            "width": 800,
            "height": 600,
            "quality": 80
        }
    }
}
```

#### 4.2 添加水印
```http
POST /api/v1/files/{fileId}/watermark
Content-Type: application/json
Authorization: Bearer {token}
```

**请求体**
```json
{
    "watermarkType": "TEXT",
    "content": "StylishLink",
    "position": "BOTTOM_RIGHT",
    "opacity": 0.7,
    "fontSize": 24,
    "color": "#FFFFFF"
}
```

#### 4.3 背景移除
```http
POST /api/v1/files/{fileId}/remove-background
Authorization: Bearer {token}
```

### 5. 文件分享

#### 5.1 创建分享链接
```http
POST /api/v1/files/{fileId}/share
Content-Type: application/json
Authorization: Bearer {token}
```

**请求体**
```json
{
    "shareType": "PUBLIC",
    "expiresIn": 3600,
    "downloadLimit": 100,
    "password": "optional_password"
}
```

**响应示例**
```json
{
    "code": 0,
    "message": "创建分享链接成功",
    "data": {
        "shareId": "share_123456",
        "shareCode": "ABC123",
        "shareUrl": "https://stylish-link.com/share/ABC123",
        "expiresAt": "2024-01-01T13:00:00Z",
        "downloadLimit": 100,
        "downloadCount": 0
    }
}
```

#### 5.2 获取分享信息
```http
GET /api/v1/shares/{shareCode}
```

#### 5.3 取消分享
```http
DELETE /api/v1/files/{fileId}/share/{shareId}
Authorization: Bearer {token}
```

### 6. 统计信息

#### 6.1 获取文件统计
```http
GET /api/v1/files/stats
Authorization: Bearer {token}
```

**查询参数**
- `startDate` (String, optional): 开始日期 YYYY-MM-DD
- `endDate` (String, optional): 结束日期 YYYY-MM-DD
- `category` (String, optional): 文件分类

**响应示例**
```json
{
    "code": 0,
    "message": "获取统计信息成功",
    "data": {
        "totalFiles": 1000,
        "totalSize": 1073741824,
        "categoryStats": {
            "USER_AVATAR": {
                "count": 100,
                "size": 104857600
            },
            "CLOTHING_IMAGE": {
                "count": 500,
                "size": 536870912
            }
        },
        "uploadTrend": [
            {
                "date": "2024-01-01",
                "count": 50,
                "size": 52428800
            }
        ]
    }
}
```

#### 6.2 获取用户使用统计
```http
GET /api/v1/files/usage
Authorization: Bearer {token}
```

**响应示例**
```json
{
    "code": 0,
    "message": "获取使用统计成功",
    "data": {
        "userId": "user_001",
        "totalFiles": 50,
        "totalSize": 52428800,
        "usedQuota": 52428800,
        "totalQuota": 1073741824,
        "quotaUsagePercent": 4.88,
        "monthlyUploads": 20,
        "monthlyDownloads": 100
    }
}
```

### 7. 文件搜索

#### 7.1 搜索文件
```http
GET /api/v1/files/search
Authorization: Bearer {token}
```

**查询参数**
- `keyword` (String, required): 搜索关键词
- `category` (String, optional): 文件分类
- `fileType` (String, optional): 文件类型
- `startDate` (String, optional): 开始日期
- `endDate` (String, optional): 结束日期
- `page` (Integer, optional): 页码
- `size` (Integer, optional): 每页大小

**响应示例**
```json
{
    "code": 0,
    "message": "搜索完成",
    "data": {
        "total": 10,
        "page": 1,
        "size": 20,
        "files": [
            {
                "id": "file_001",
                "originalName": "avatar.jpg",
                "category": "USER_AVATAR",
                "relevanceScore": 0.95,
                "url": "https://...",
                "createdAt": "2024-01-01T12:00:00Z"
            }
        ]
    }
}
```

### 8. 批量操作

#### 8.1 批量删除
```http
POST /api/v1/files/batch-delete
Content-Type: application/json
Authorization: Bearer {token}
```

**请求体**
```json
{
    "fileIds": ["file_001", "file_002", "file_003"],
    "force": false
}
```

#### 8.2 批量更新
```http
POST /api/v1/files/batch-update
Content-Type: application/json
Authorization: Bearer {token}
```

**请求体**
```json
{
    "fileIds": ["file_001", "file_002"],
    "updates": {
        "accessLevel": "PUBLIC",
        "metadata": {
            "batch": "2024-01-01"
        }
    }
}
```

### 9. 管理接口

#### 9.1 获取系统统计（管理员）
```http
GET /api/v1/admin/files/stats
Authorization: Bearer {admin_token}
```

#### 9.2 清理过期文件（管理员）
```http
POST /api/v1/admin/files/cleanup
Authorization: Bearer {admin_token}
```

**请求体**
```json
{
    "category": "TEMP_FILE",
    "olderThanDays": 7,
    "dryRun": true
}
```

## 数据模型

### FileInfo
```json
{
    "id": "string",
    "userId": "string", 
    "originalName": "string",
    "storageName": "string",
    "bucketName": "string",
    "objectKey": "string",
    "category": "FileCategory",
    "fileType": "FileType",
    "mimeType": "string",
    "fileSize": "number",
    "md5Hash": "string",
    "sha256Hash": "string",
    "status": "FileStatus",
    "accessLevel": "AccessLevel",
    "url": "string",
    "thumbnailUrl": "string",
    "etag": "string",
    "metadata": "object",
    "createdAt": "datetime",
    "updatedAt": "datetime",
    "expiresAt": "datetime"
}
```

### MultipartUpload
```json
{
    "uploadId": "string",
    "fileId": "string",
    "fileName": "string",
    "fileSize": "number",
    "chunkSize": "number",
    "totalChunks": "number",
    "uploadedChunks": "array",
    "status": "MultipartUploadStatus",
    "bucket": "string",
    "objectKey": "string",
    "createdAt": "datetime",
    "expiresAt": "datetime"
}
```

### 枚举值

#### FileCategory
- USER_AVATAR, USER_PHOTO, CLOTHING_IMAGE, ACCESSORY_IMAGE, OUTFIT_IMAGE
- AI_GENERATED_VIDEO, AI_GENERATED_IMAGE, SYSTEM_ICON, SYSTEM_BACKGROUND, TEMP_FILE

#### FileType  
- IMAGE, VIDEO, AUDIO, DOCUMENT, ARCHIVE, OTHER

#### FileStatus
- UPLOADING, UPLOADED, PROCESSING, PROCESSED, AVAILABLE, DELETED, EXPIRED

#### AccessLevel
- PUBLIC, PRIVATE, PROTECTED, INTERNAL

#### MultipartUploadStatus
- INITIATED, IN_PROGRESS, COMPLETED, CANCELLED, EXPIRED

## SDK使用示例

### JavaScript/TypeScript
```javascript
import { FileServiceClient } from '@stylishlink/file-service-sdk';

const client = new FileServiceClient({
    baseUrl: 'https://api.stylishlink.com',
    token: 'your-jwt-token'
});

// 单文件上传
const uploadResult = await client.uploadFile({
    file: fileInput.files[0],
    category: 'USER_AVATAR',
    accessLevel: 'PRIVATE'
});

// 分片上传大文件
const largeFileUpload = await client.uploadLargeFile({
    file: videoFile,
    category: 'AI_GENERATED_VIDEO',
    chunkSize: 1024 * 1024, // 1MB
    onProgress: (progress) => {
        console.log(`上传进度: ${progress}%`);
    }
});

// 获取下载链接
const downloadUrl = await client.getDownloadUrl(fileId);

// 处理图片
const processResult = await client.processImage(fileId, {
    processType: 'RESIZE',
    parameters: { width: 800, height: 600 }
});
```

---

## 腾讯云COS集成说明

### COS核心概念映射
- **APPID**: 通过配置文件管理，用于标识腾讯云账户
- **SecretId/SecretKey**: 服务端配置，用于COS API认证
- **Bucket**: 根据业务需要创建多个存储桶
  - `stylish-link-public`: 公开文件存储
  - `stylish-link-private`: 私有文件存储
  - `stylish-link-temp`: 临时文件存储
- **ObjectKey**: 文件在COS中的唯一标识，格式为: `{category}/{userId}/{fileName}`
- **Region**: 默认使用ap-guangzhou区域

### 文件存储路径规范
```
stylish-link-private/
├── users/{userId}/
│   ├── avatars/           # 用户头像
│   └── photos/            # 用户照片
├── wardrobe/{userId}/
│   ├── clothing/          # 衣物图片
│   ├── accessories/       # 饰品图片
│   └── outfits/           # 搭配图片
├── ai-generated/{userId}/
│   ├── videos/            # AI生成视频
│   └── images/            # AI生成图片
└── temp/
    └── uploads/           # 临时上传文件
```

## 注意事项

1. **文件大小限制**: 单文件最大100MB，分片上传支持更大文件
2. **支持格式**: 图片(jpg,jpeg,png,gif,webp)，视频(mp4,avi,mov)，文档(pdf,doc,docx)
3. **安全性**: 所有文件操作需要JWT认证，敏感文件需要权限验证
4. **配额限制**: 每用户默认1GB存储配额，超出需要购买扩容
5. **CDN缓存**: 公开文件自动CDN缓存，私有文件通过临时URL访问
6. **数据合规**: 严格遵循数据保护法规，支持数据导出和删除
7. **分片上传**: 大于5MB的文件建议使用分片上传，提高成功率
8. **ETag校验**: 支持ETag进行文件完整性校验 