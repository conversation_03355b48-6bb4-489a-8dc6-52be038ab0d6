# 通用服务字段对照清单

> 本清单用于核查通用服务（common-service）API接口文档字段与原型页面、需求文档的覆盖关系，确保无遗漏。

---

## 1. 字段对照表说明
- "接口字段"：API文档中定义的字段
- "原型字段"：相关原型页面涉及的所有字段
- "需求字段"：需求/设计文档中与本服务相关的字段
- "类型/结构"：字段类型或结构说明
- "可选值/约束"：枚举、约束、格式等
- "覆盖状态"：标注"已覆盖/未覆盖/接口冗余/需求遗漏/设计偏差"等
- "备注"：补充说明

---

## 2. 字段对照总览
| 接口字段（API文档） | 原型字段 | 需求字段 | 类型/结构 | 可选值/约束 | 覆盖状态 | 备注 |
|---------------------|----------|----------|-----------|-------------|----------|------|
| code                | 全局异常弹窗/错误提示 | 响应码、code | int        | 0/1001/1002/1003/9001... | 已覆盖   |      |
| msg                 | 全局异常弹窗/错误提示 | 响应消息、msg | string     | 任意      | 已覆盖   |      |
| data                | 全局异常弹窗/错误提示 | 响应数据、data | object     | 任意      | 已覆盖   |      |
| userId              | 用户信息表单/个人中心 | 用户ID、userId | string     | 非空      | 已覆盖   |      |
| nickname            | 用户信息表单/个人中心 | 昵称、nickname | string     | 非空      | 已覆盖   |      |
| avatar              | 用户信息表单/个人中心 | 头像、avatar | string     | URL      | 已覆盖   |      |
| key                 | 下拉选项/配置项选择 | 配置项key、key | string     | 任意      | 已覆盖   |      |
| config              | 下拉选项/配置项展示 | 配置信息、config | object     | 结构见下 | 已覆盖   |      |
| gender              | 性别选择/用户信息表单 | 性别、gender | array<object> | 结构见下 | 已覆盖   |      |
| value (gender)      | 性别选择 | 性别值、value | int        | 1/2/0    | 已覆盖   |      |
| label (gender)      | 性别选择 | 性别标签、label | string     | 男/女/未知 | 已覆盖   |      |

---

## 3. 复杂结构字段细化（如有）
| 字段/子字段 | 原型字段 | 需求字段 | 类型/结构 | 可选值/约束 | 覆盖状态 | 备注 |
|-------------|----------|----------|-----------|-------------|----------|------|
| config      | 下拉选项/配置项展示 | 配置信息、config | object | 见下 | 已覆盖 | |
| └ gender    | 性别选择 | 性别、gender | array<object> | 见下 | 已覆盖 | |
|   └ value   | 性别选择 | 性别值、value | int | 1/2/0 | 已覆盖 | |
|   └ label   | 性别选择 | 性别标签、label | string | 男/女/未知 | 已覆盖 | |

---

## 4. 衍生/推荐字段（如有）
- 记录后端推算、自动生成、推荐类字段。

---

## 5. 结论与建议
- 字段覆盖性结论：本次核查，通用服务相关接口字段与原型页面、需求/设计文档字段实现了高度一一对应，所有核心字段（通用响应结构、DTO/VO、通用配置、全局异常等）均已覆盖，无明显遗漏。
- 复杂结构（如config、gender等）已在接口、原型、需求三方文档中保持一致，字段类型、可选值、约束清晰。
- 未发现接口冗余或需求遗漏字段，接口设计与业务需求、前端原型高度对齐。
- 建议：
  1. 后续如有新通用配置项、DTO/VO结构扩展、全局常量变更等，需同步更新三方字段对照清单。
  2. 复杂结构字段如有扩展（如更多枚举、配置项等），建议在本清单"复杂结构字段细化"部分补充。
  3. 建议团队持续维护本对照清单，作为接口/需求/原型变更的同步依据，便于追溯和自动化校验。
- 结构优化建议：当前接口字段结构已较为规范，建议继续保持分组清晰、类型明确、约束标准化的风格。 