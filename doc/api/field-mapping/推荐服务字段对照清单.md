# 推荐服务字段对照清单

> 本清单用于核查推荐服务API接口文档字段与原型页面、需求文档的覆盖关系，确保无遗漏。

---

## 1. 字段对照表说明
- "接口字段"：API文档中定义的字段
- "原型字段"：相关原型页面涉及的所有字段
- "需求字段"：需求/设计文档中与本服务相关的字段
- "覆盖状态"：标注"已覆盖/未覆盖/接口冗余/需求遗漏"等

---

## 2. 字段对照总览
| 接口字段（API文档）   | 原型字段（页面/模块）         | 需求字段（文档/章节）         | 类型/结构   | 可选值/约束 | 覆盖状态   | 备注 |
|----------------------|-----------------------------|------------------------------|-------------|-------------|------------|------|
| id（outfits.id）     | 搭配ID（outfit_detail.html）| 推荐ID（主需求/3.21新增）    | string      | 唯一        | 已覆盖     |      |
| name（outfits.name） | 搭配名称（outfit.html）      | 搭配名称（主需求）            | string      |             | 已覆盖     |      |
| mainImageUrl         | 主图（outfit_detail.html）   | 搭配主图（原型/主需求）       | string      | url         | 已覆盖     |      |
| videoUrl             | 推荐视频（outfit_detail.html）| 推荐视频讲解（主需求/3.21新增）| string      | url         | 已覆盖     |      |
| score                | 评分（outfit_detail.html）   | 推荐评分（主需求/3.21新增）   | number      | 0-5         | 已覆盖     |      |
| multiScore           | 多维度评分（outfit_detail.html）| 多维度评分（主需求/3.21新增）| object      | 见下表      | 已覆盖     | energy/occasion/style/temperament |
| reason               | 推荐理由（outfit_detail.html）| 推荐理由（主需求/3.21新增） | string      |             | 已覆盖     | 建议结构化推荐依据      |
| occasions            | 推荐场合（outfit_detail.html）| 推荐场合（主需求/原型）      | array       |             | 已覆盖     |      |
| items                | 搭配单品（outfit_detail.html）| 推荐搭配单品（主需求）        | array       | 见下表      | 已覆盖     | 详见items结构         |
| wuxingAnalysis       | 五行分布（fortune_complete.html）| 命理/能量分析（3.21新增/命理文档）| object | 金/木/水/火/土 | 已覆盖   | 详见下表             |
| wuxingSummary        | 五行整体解读（outfit_detail.html）| 五行解读（主需求/原型）      | string      |             | 已覆盖     |      |
| wuxingDetails        | 各元素解析（outfit_detail.html）| 五行元素解读（主需求/原型）  | array       | 见下表      | 已覆盖     |      |
| energyAdvice         | 能量提升建议（outfit_detail.html）| 能量建议（主需求/原型）      | array       | 见下表      | 已覆盖     |      |
| likeCount            | 点赞数（outfit_detail.html）  | 点赞（主需求/原型）           | int         |             | 已覆盖     |      |
| favoriteCount        | 收藏数（outfit_detail.html）  | 收藏（主需求/原型）           | int         |             | 已覆盖     |      |
| shareCount           | 分享数（outfit_detail.html）  | 分享（主需求/原型）           | int         |             | 已覆盖     |      |
| feedback             | 用户反馈（outfit_detail.html）| 用户反馈（主需求/原型）       | object      | 见下表      | 已覆盖     |      |
| shoppingList         | 购物清单（outfit_detail.html）| 推荐购物清单（主需求/3.21新增）| array       | 见下表      | 已覆盖     |      |

---

## 3. 复杂结构字段细化

### items结构
| 字段名         | 原型字段 | 需求字段 | 类型/结构 | 可选值/约束 | 覆盖状态 | 备注 |
|----------------|----------|----------|-----------|-------------|----------|------|
| itemId         | 单品ID   | 单品ID   | string    | 唯一        | 已覆盖   |      |
| itemName       | 单品名称 | 单品名称 | string    |             | 已覆盖   |      |
| itemImageUrl   | 单品图片 | 单品图片 | string    | url         | 已覆盖   |      |
| itemType       | 单品类型 | 单品类型 | string    | shirt/skirt/shoes/bag/accessory | 已覆盖 |      |
| wuxing         | 五行属性 | 五行属性 | string    | 金/木/水/火/土 | 已覆盖 |      |
| itemDescription| 单品描述 | 单品描述 | string    |             | 已覆盖   |      |
| itemTags       | 单品标签 | 单品标签 | array     |             | 已覆盖   |      |
| source         | 来源     | 来源     | string    | wardrobe/recommended | 已覆盖 |      |
| score          | 推荐度   | 推荐度   | int       | 0-100       | 已覆盖   |      |

### multiScore结构
| 字段名       | 原型字段 | 需求字段 | 类型/结构 | 可选值/约束 | 覆盖状态 | 备注 |
|--------------|----------|----------|-----------|-------------|----------|------|
| energy       | 能量评分 | 能量评分 | number    | 0-5         | 已覆盖   |      |
| occasion     | 场合评分 | 场合评分 | number    | 0-5         | 已覆盖   |      |
| style        | 风格评分 | 风格评分 | number    | 0-5         | 已覆盖   |      |
| temperament  | 气质评分 | 气质评分 | number    | 0-5         | 已覆盖   |      |

### wuxingAnalysis结构
| 字段名 | 原型字段 | 需求字段 | 类型/结构 | 可选值/约束 | 覆盖状态 | 备注 |
|--------|----------|----------|-----------|-------------|----------|------|
| metal  | 金       | 金       | int       | 0-100       | 已覆盖   |      |
| wood   | 木       | 木       | int       | 0-100       | 已覆盖   |      |
| water  | 水       | 水       | int       | 0-100       | 已覆盖   |      |
| fire   | 火       | 火       | int       | 0-100       | 已覆盖   |      |
| earth  | 土       | 土       | int       | 0-100       | 已覆盖   |      |

### wuxingDetails结构
| 字段名   | 原型字段 | 需求字段 | 类型/结构 | 可选值/约束 | 覆盖状态 | 备注 |
|----------|----------|----------|-----------|-------------|----------|------|
| element  | 五行元素 | 五行元素 | string    | 金/木/水/火/土 | 已覆盖 |      |
| value    | 分值     | 分值     | int       | 0-100       | 已覆盖   |      |
| summary  | 解读文本 | 解读文本 | string    |             | 已覆盖   |      |

### energyAdvice结构
| 字段名   | 原型字段 | 需求字段 | 类型/结构 | 可选值/约束 | 覆盖状态 | 备注 |
|----------|----------|----------|-----------|-------------|----------|------|
| element  | 五行元素 | 五行元素 | string    | 金/木/水/火/土 | 已覆盖 |      |
| advice   | 建议文本 | 建议文本 | string    |             | 已覆盖   |      |

### feedback结构
| 字段名   | 原型字段 | 需求字段 | 类型/结构 | 可选值/约束 | 覆盖状态 | 备注 |
|----------|----------|----------|-----------|-------------|----------|------|
| liked    | 是否喜欢 | 是否喜欢 | bool      | true/false  | 已覆盖   |      |
| rating   | 用户评分 | 用户评分 | number    | 0-5         | 已覆盖   |      |
| comment  | 用户评论 | 用户评论 | string    |             | 已覆盖   |      |

### shoppingList结构
| 字段名      | 原型字段 | 需求字段 | 类型/结构 | 可选值/约束 | 覆盖状态 | 备注 |
|-------------|----------|----------|-----------|-------------|----------|------|
| itemId      | 单品ID   | 单品ID   | string    | 唯一        | 已覆盖   |      |
| itemName    | 单品名称 | 单品名称 | string    |             | 已覆盖   |      |
| itemImageUrl| 单品图片 | 单品图片 | string    | url         | 已覆盖   |      |
| buyUrl      | 购买链接 | 购买链接 | string    | url         | 已覆盖   |      |

---

## 4. 衍生/推荐字段（如有）
- 记录后端推算、自动生成、推荐类字段。

---

## 5. 结论与建议
- 推荐服务接口字段已全面覆盖原型与需求的主要推荐相关字段。
- 建议持续保持接口文档与需求/原型同步，便于后续追溯和复用。 