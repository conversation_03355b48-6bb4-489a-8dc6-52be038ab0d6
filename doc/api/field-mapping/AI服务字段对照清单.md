# AI服务字段对照清单

> 本清单用于核查AI服务API接口文档字段与原型页面、需求文档的覆盖关系，确保无遗漏。

---

## 1. 字段对照表说明
| 接口字段 | 原型页面字段 | 页面路径/模块 | 需求字段 | 类型/结构 | 可选值/约束 | 示例 | 覆盖状态 | 备注 | 自动化校验建议 |
|----------|--------------|--------------|----------|-----------|-------------|------|----------|------|----------------|
| file     | 上传图片     | home/style_advice.html, outfit/custom_outfit.html, user-info/index.vue | 图片上传 | multipart | 图片格式 | - | 已覆盖 | 请求参数 | 文件类型/大小校验 |
| type     | 识别类型     | home/style_advice.html | 识别类型 | string | clothing/accessory | "clothing" | 已覆盖 | 请求参数 | 枚举校验 |
| category | 类别         | home/style_advice.html | 服饰类别识别 | string | 见字典 | "上衣" | 已覆盖 | 响应参数 | 枚举校验 |
| colors   | 颜色         | home/style_advice.html | 颜色识别 | array  | 见色彩字典 | ["白色"] | 已覆盖 | 响应参数 | 枚举校验 |
| style    | 风格         | home/style_advice.html | 风格分析 | string | 见风格字典 | "休闲" | 已覆盖 | 响应参数 | 枚举校验 |
| isFullBodyPhoto | 是否全身照 | user-info/index.vue | 全身照识别 | boolean | true/false | true | 已覆盖 | 响应参数 | 布尔校验 |
| confidence | 识别置信度 | user-info/index.vue | 识别置信度 | number | 0-100 | 95.5 | 已覆盖 | 响应参数 | 数值范围校验 |
| bodyType | 推荐体型分类 | user-info/index.vue | 体型分类 | string | slim/normal/chubby | "normal" | 已覆盖 | 响应参数 | 枚举校验 |
| bodyShape | 详细身材数据 | user-info/index.vue | 身材数据 | object | 见下分节 | {} | 已覆盖 | 响应参数，见下分节 | 结构递归校验 |
| shoulderWidth | 肩膀宽度 | user-info/index.vue | 肩膀 | number | 1-5 | 3 | 已覆盖 | bodyShape子字段，1:窄,2:偏窄,3:正常,4:偏宽,5:宽 | 数值范围校验 |
| waistShape | 腰型 | user-info/index.vue | 腰型 | number | 1-5 | 3 | 已覆盖 | bodyShape子字段，1:直筒,2:略有曲线,3:有曲线,4:曲线较明显,5:曲线明显 | 数值范围校验 |
| belly | 肚腩 | user-info/index.vue | 肚腩 | number | 1-5 | 1 | 已覆盖 | bodyShape子字段，1:没有,2:略有小肚腩,3:小肚腩,4:偏大肚腩,5:大肚腩 | 数值范围校验 |
| hip | 臀型 | user-info/index.vue | 臀型 | number | 1-5 | 3 | 已覆盖 | bodyShape子字段，1:下榻,2:略有上翘,3:正常,4:较上翘,5:上翘 | 数值范围校验 |
| hipShape | 胯型 | user-info/index.vue | 胯型 | number | 1-5 | 3 | 已覆盖 | bodyShape子字段，1:直筒,2:略有曲线,3:有曲线,4:曲线较明显,5:曲线明显 | 数值范围校验 |
| armLength | 臂长 | user-info/index.vue | 臂长 | number | 1-5 | 3 | 已覆盖 | bodyShape子字段，1:短,2:偏短,3:正常,4:偏长,5:长 | 数值范围校验 |
| armCircum | 臂围 | user-info/index.vue | 臂围 | number | 1-5 | 3 | 已覆盖 | bodyShape子字段，1:细,2:偏细,3:正常,4:偏粗,5:粗 | 数值范围校验 |
| hipWidth | 胯部宽度 | user-info/index.vue | 胯部 | number | 1-5 | 3 | 已覆盖 | bodyShape子字段，1:窄,2:偏窄,3:正常,4:偏宽,5:宽 | 数值范围校验 |
| thigh | 大腿 | user-info/index.vue | 大腿 | number | 1-5 | 3 | 已覆盖 | bodyShape子字段，1:细,2:偏细,3:正常,4:偏粗,5:粗 | 数值范围校验 |
| calf | 小腿 | user-info/index.vue | 小腿 | number | 1-5 | 3 | 已覆盖 | bodyShape子字段，1:细,2:偏细,3:正常,4:偏粗,5:粗 | 数值范围校验 |
| bodyFat | 上下身粗细 | user-info/index.vue | 上下身粗 | number | 1-5 | 3 | 已覆盖 | bodyShape子字段，1:上身粗,2:偏上身粗,3:匀称,4:偏下身粗,5:下身粗 | 数值范围校验 |
| bodyLength | 上下身长短 | user-info/index.vue | 上下身长 | number | 1-5 | 3 | 已覆盖 | bodyShape子字段，1:上身长,2:偏上身长,3:匀称,4:偏下身长,5:下身长 | 数值范围校验 |
| analysis | 身材分析结果 | user-info/index.vue | 身材分析 | array | 见下分节 | [] | 已覆盖 | 响应参数 | 结构递归校验 |
| suggestions | 穿搭建议 | user-info/index.vue | 穿搭建议 | array | 见下分节 | [] | 已覆盖 | 响应参数 | 结构递归校验 |
| reason | 失败原因 | user-info/index.vue | 识别失败原因 | string | - | "图片中人物非正面朝向" | 已覆盖 | 错误响应字段 | 类型校验 |
| styleDistribution | 风格分布 | home/style_advice.html | 风格分析 | object | {"sporty":0-1,...} | {"sporty":0.5} | 已覆盖 | 响应参数，见下分节 | 结构递归校验 |
| dominantColors | 主色调   | home/style_advice.html | 色彩分析 | array  | 见色彩字典 | ["白色","黑色"] | 已覆盖 | 响应参数 | 枚举校验 |
| message  | 用户输入     | home/style_advice.html | AI对话输入 | string | - | "今天穿什么？" | 已覆盖 | 请求参数 | 类型校验 |
| reply    | AI回复       | home/style_advice.html | AI对话 | string | - | "建议今日选择浅色系休闲装" | 已覆盖 | 响应参数 | 类型校验 |
| outfitId | 搭配ID       | home/outfit_detail.html, outfit/custom_outfit.html | 搭配ID | string | 唯一 | "abc123" | 已覆盖 | 请求/响应参数 | 唯一性校验 |
| templateId | 视频模板ID | home/outfit_video.html | 视频模板 | string | 唯一 | "tpl001" | 已覆盖 | 请求参数 | 唯一性校验 |
| videoUrl | 视频URL      | home/outfit_video.html | 视频生成 | string | url | "https://xxx.com/video.mp4" | 已覆盖 | 响应参数 | url校验 |
| status   | 任务状态     | home/outfit_video.html | 视频生成 | string | PROCESSING等 | "PROCESSING" | 已覆盖 | 响应参数 | 枚举校验 |
| score    | 评分         | home/outfit_detail.html | AI评分 | number | 0-100 | 85 | 已覆盖 | 响应参数，多维度见下 | 类型校验 |
| multiScore | 多维度评分 | home/outfit_detail.html, fortune_detail.html | 多维度评分 | object | 见下分节 | {energy:90,...} | 已覆盖 | 响应参数 | 结构递归校验 |
| suggestion | 建议       | home/style_advice.html | AI建议 | string | - | "建议增加亮色点缀" | 已覆盖 | 响应参数 | 类型校验 |
| appearance | 形象评估   | profile/body_data.html | 形象评估 | object | 见下分节 | {score:85,...} | 已覆盖 | 响应参数 | 结构递归校验 |
| luckyColor | 幸运色     | fortune_detail.html | 幸运色 | string | 见色彩字典 | "红色" | 已覆盖 | 响应参数 | 枚举校验 |
| energyAdvice | 能量建议 | fortune_detail.html | 能量建议 | string | - | "今日宜穿蓝色" | 已覆盖 | 响应参数 | 类型校验 |
| videoTaskId | 视频任务ID | outfit_video.html | 视频任务 | string | 唯一 | "task001" | 已覆盖 | 响应参数 | 唯一性校验 |
| inspirationReward | 灵感值奖励 | pages/home.html | 灵感值奖励 | number | >=0 | 10 | 已覆盖 | 响应参数 | 数值校验 |
| dateInfo | 日期信息 | fortune/index.vue | 日期信息 | object | - | {"gregorian":"2023年11月20日","lunar":"二月十九 己巳卯"} | 已覆盖 | 今日能量页面 | 结构校验 |
| dateInfo.gregorian | 公历日期 | fortune/index.vue | 公历日期 | string | - | "2023年11月20日" | 已覆盖 | 日期信息子字段 | 日期格式校验 |
| dateInfo.lunar | 农历信息 | fortune/index.vue | 农历信息 | string | - | "二月十九 己巳卯 丙戌 水行" | 已覆盖 | 日期信息子字段 | 类型校验 |
| totalScore | 总能量分数 | fortune/index.vue | 今日能量总分 | number | 0-100 | 78 | 已覆盖 | 能量解读核心字段 | 数值范围校验 |
| percentage | 超过用户百分比 | fortune/index.vue | 能量排名 | number | 0-100 | 82 | 已覆盖 | 能量解读核心字段 | 数值范围校验 |
| peakTime | 能量高峰时段 | fortune/index.vue | 高峰时段 | string | - | "上午8-10点" | 已覆盖 | 能量解读核心字段 | 类型校验 |
| peakTimeDescription | 高峰时段描述 | fortune/index.vue | 高峰描述 | string | - | "适合重要会议和决策" | 已覆盖 | 能量解读核心字段 | 类型校验 |
| description | 能量描述 | fortune/index.vue | 能量总述 | string | - | "今日五行能量以金主导" | 已覆盖 | 能量解读核心字段 | 类型校验 |
| dimensions | 五维能量评分 | fortune/index.vue | 五维评分 | object | 见下分节 | {"love":65,"career":88,...} | 已覆盖 | 能量解读核心字段 | 结构递归校验 |
| dimensions.love | 爱情运势 | fortune/index.vue | 爱情能量 | number | 0-100 | 65 | 已覆盖 | 五维能量子字段 | 数值范围校验 |
| dimensions.career | 事业运势 | fortune/index.vue | 事业能量 | number | 0-100 | 88 | 已覆盖 | 五维能量子字段 | 数值范围校验 |
| dimensions.wealth | 财富运势 | fortune/index.vue | 财富能量 | number | 0-100 | 72 | 已覆盖 | 五维能量子字段 | 数值范围校验 |
| dimensions.health | 健康运势 | fortune/index.vue | 健康能量 | number | 0-100 | 81 | 已覆盖 | 五维能量子字段 | 数值范围校验 |
| dimensions.relationship | 人际运势 | fortune/index.vue | 人际能量 | number | 0-100 | 76 | 已覆盖 | 五维能量子字段 | 数值范围校验 |
| advice | 宜忌指南 | fortune/index.vue | 宜忌建议 | object | 见下分节 | {"categories":[],"lifeSuggestions":[]} | 已覆盖 | 宜忌指南核心字段 | 结构递归校验 |
| advice.categories | 宜忌分类 | fortune/index.vue | 宜忌分类 | array | 见下分节 | [{"type":"suitable","label":"宜做事项",...}] | 已覆盖 | 宜忌指南子字段 | 结构递归校验 |
| advice.lifeSuggestions | 生活建议 | fortune/index.vue | 生活建议 | array | 见下分节 | [{"icon":"≋","content":"今天适合..."}] | 已覆盖 | 宜忌指南子字段 | 结构递归校验 |
| luckyElements | 幸运元素 | fortune/index.vue | 提升建议 | object | 见下分节 | {"colors":[],"clothing":[],...} | 已覆盖 | 幸运元素核心字段 | 结构递归校验 |
| luckyElements.colors | 幸运色列表 | fortune/index.vue | 幸运色 | array | 色彩hex值 | ["#B3D8E8","#E8F5E8"] | 已覆盖 | 幸运元素子字段 | 色彩格式校验 |
| luckyElements.clothing | 服饰建议 | fortune/index.vue | 服饰建议 | array | 文本建议 | ["推荐丝绸、雪纺类柔软面料"] | 已覆盖 | 幸运元素子字段 | 类型校验 |
| luckyElements.accessories | 配饰建议 | fortune/index.vue | 配饰建议 | array | 文本建议 | ["首饰宜选银色、白金系"] | 已覆盖 | 幸运元素子字段 | 类型校验 |
| luckyElements.makeup | 妆容建议 | fortune/index.vue | 妆容建议 | array | 文本建议 | ["今日妆容以清透水润为主"] | 已覆盖 | 幸运元素子字段 | 类型校验 |
| ...      | ...          | ...          | ...      | ...       | ...         | ...  | ...      | ...  | ...            |

---

## 2. 复杂结构字段细化
### 2.1 今日能量页面数据结构详解
| 字段/子字段 | 原型字段 | 页面路径 | 需求字段 | 类型 | 可选值/约束 | 覆盖状态 | 备注 |
|-------------|----------|----------|----------|------|-------------|----------|------|
| dateInfo | 日期信息 | fortune/index.vue | 日期信息 | object | - | 已覆盖 | 包含公历和农历信息 |
| dateInfo.gregorian | 公历日期 | fortune/index.vue | 公历日期 | string | - | 已覆盖 | 如"2023年11月20日" |
| dateInfo.lunar | 农历信息 | fortune/index.vue | 农历信息 | string | - | 已覆盖 | 包含天干地支信息 |
| totalScore | 总能量分数 | fortune/index.vue | 能量总分 | number | 0-100 | 已覆盖 | 当日综合能量评分 |
| percentage | 超过百分比 | fortune/index.vue | 能量排名 | number | 0-100 | 已覆盖 | 超过其他用户的百分比 |
| peakTime | 能量高峰 | fortune/index.vue | 高峰时段 | string | - | 已覆盖 | 如"上午8-10点" |
| peakTimeDescription | 高峰描述 | fortune/index.vue | 高峰说明 | string | - | 已覆盖 | 高峰时段适宜活动说明 |
| description | 能量描述 | fortune/index.vue | 总体描述 | string | - | 已覆盖 | 当日能量场整体描述 |

### 2.2 五维能量评分 dimensions 详解
| 字段/子字段 | 原型字段 | 页面路径 | 需求字段 | 类型 | 可选值/约束 | 覆盖状态 | 备注 |
|-------------|----------|----------|----------|------|-------------|----------|------|
| love | 爱情运势 | fortune/index.vue | 爱情能量 | number | 0-100 | 已覆盖 | 爱情相关能量评分 |
| career | 事业运势 | fortune/index.vue | 事业能量 | number | 0-100 | 已覆盖 | 事业相关能量评分 |
| wealth | 财富运势 | fortune/index.vue | 财富能量 | number | 0-100 | 已覆盖 | 财富相关能量评分 |
| health | 健康运势 | fortune/index.vue | 健康能量 | number | 0-100 | 已覆盖 | 健康相关能量评分 |
| relationship | 人际运势 | fortune/index.vue | 人际能量 | number | 0-100 | 已覆盖 | 人际关系能量评分 |

### 2.3 宜忌指南 advice 详解
| 字段/子字段 | 原型字段 | 页面路径 | 需求字段 | 类型 | 可选值/约束 | 覆盖状态 | 备注 |
|-------------|----------|----------|----------|------|-------------|----------|------|
| categories | 宜忌分类 | fortune/index.vue | 分类列表 | array | AdviceCategory[] | 已覆盖 | 包含宜做和忌做事项 |
| lifeSuggestions | 生活建议 | fortune/index.vue | 生活建议 | array | LifeSuggestion[] | 已覆盖 | 通用生活建议列表 |

### 2.4 宜忌分类 AdviceCategory 详解
| 字段/子字段 | 原型字段 | 页面路径 | 需求字段 | 类型 | 可选值/约束 | 覆盖状态 | 备注 |
|-------------|----------|----------|----------|------|-------------|----------|------|
| type | 分类类型 | fortune/index.vue | 类型 | string | suitable/avoid | 已覆盖 | 宜做或忌做 |
| label | 分类标签 | fortune/index.vue | 标签 | string | - | 已覆盖 | 如"宜做事项"、"忌做事项" |
| items | 事项列表 | fortune/index.vue | 事项 | array | AdviceItem[] | 已覆盖 | 具体宜忌事项 |

### 2.5 宜忌事项 AdviceItem 详解
| 字段/子字段 | 原型字段 | 页面路径 | 需求字段 | 类型 | 可选值/约束 | 覆盖状态 | 备注 |
|-------------|----------|----------|----------|------|-------------|----------|------|
| id | 事项ID | fortune/index.vue | 唯一标识 | string | - | 已覆盖 | 事项唯一标识 |
| icon | 图标 | fortune/index.vue | 图标 | string | iconfont类名或图片路径 | 已覆盖 | 支持iconfont和图片 |
| text | 事项文本 | fortune/index.vue | 事项内容 | string | - | 已覆盖 | 事项具体描述 |
| color | 颜色 | fortune/index.vue | 颜色 | string | hex颜色 | 已覆盖 | 可选，用于特殊标识 |

### 2.6 生活建议 LifeSuggestion 详解
| 字段/子字段 | 原型字段 | 页面路径 | 需求字段 | 类型 | 可选值/约束 | 覆盖状态 | 备注 |
|-------------|----------|----------|----------|------|-------------|----------|------|
| icon | 建议图标 | fortune/index.vue | 图标 | string | emoji或字符 | 已覆盖 | 建议类型图标 |
| content | 建议内容 | fortune/index.vue | 建议文本 | string | - | 已覆盖 | 具体生活建议内容 |

### 2.7 幸运元素 luckyElements 详解
| 字段/子字段 | 原型字段 | 页面路径 | 需求字段 | 类型 | 可选值/约束 | 覆盖状态 | 备注 |
|-------------|----------|----------|----------|------|-------------|----------|------|
| colors | 幸运色 | fortune/index.vue | 幸运色列表 | array | hex颜色值 | 已覆盖 | 当日推荐颜色 |
| clothing | 服饰建议 | fortune/index.vue | 服饰建议 | array | 文本建议 | 已覆盖 | 服装搭配建议 |
| accessories | 配饰建议 | fortune/index.vue | 配饰建议 | array | 文本建议 | 已覆盖 | 配饰搭配建议 |
| makeup | 妆容建议 | fortune/index.vue | 妆容建议 | array | 文本建议 | 已覆盖 | 妆容搭配建议 |

### 2.8 全身照身材识别 bodyShape 字段详解
| 字段/子字段 | 原型字段 | 页面路径 | 需求字段 | 类型 | 可选值/约束 | 覆盖状态 | 备注 |
|-------------|----------|----------|----------|------|-------------|----------|------|
| shoulderWidth | 肩膀 | user-info/index.vue | 肩膀宽度 | number | 1-5 | 已覆盖 | 原型需求第18项，1:窄,2:偏窄,3:正常,4:偏宽,5:宽 |
| waistShape | 腰型 | user-info/index.vue | 腰型 | number | 1-5 | 已覆盖 | 原型需求第18项，1:直筒,2:略有曲线,3:有曲线,4:曲线较明显,5:曲线明显 |
| belly | 肚腩 | user-info/index.vue | 肚腩 | number | 1-5 | 已覆盖 | 原型需求第18项，1:没有,2:略有小肚腩,3:小肚腩,4:偏大肚腩,5:大肚腩 |
| hip | 臀型 | user-info/index.vue | 臀型 | number | 1-5 | 已覆盖 | 原型需求第18项，1:下榻,2:略有上翘,3:正常,4:较上翘,5:上翘 |
| hipShape | 胯型 | user-info/index.vue | 胯型 | number | 1-5 | 已覆盖 | 原型需求第18项，1:直筒,2:略有曲线,3:有曲线,4:曲线较明显,5:曲线明显 |
| armLength | 臂长 | user-info/index.vue | 臂长 | number | 1-5 | 已覆盖 | 原型需求第18项，1:短,2:偏短,3:正常,4:偏长,5:长 |
| armCircum | 臂围 | user-info/index.vue | 臂围 | number | 1-5 | 已覆盖 | 原型需求第18项，1:细,2:偏细,3:正常,4:偏粗,5:粗 |
| hipWidth | 胯部 | user-info/index.vue | 胯部宽度 | number | 1-5 | 已覆盖 | 原型需求第18项，1:窄,2:偏窄,3:正常,4:偏宽,5:宽 |
| thigh | 大腿 | user-info/index.vue | 大腿 | number | 1-5 | 已覆盖 | 原型需求第18项，1:细,2:偏细,3:正常,4:偏粗,5:粗 |
| calf | 小腿 | user-info/index.vue | 小腿 | number | 1-5 | 已覆盖 | 原型需求第18项，1:细,2:偏细,3:正常,4:偏粗,5:粗 |
| bodyFat | 上下身粗 | user-info/index.vue | 上下身粗细 | number | 1-5 | 已覆盖 | 原型需求第18项，1:上身粗,2:偏上身粗,3:匀称,4:偏下身粗,5:下身粗 |
| bodyLength | 上下身长 | user-info/index.vue | 上下身长短 | number | 1-5 | 已覆盖 | 原型需求第18项，1:上身长,2:偏上身长,3:匀称,4:偏下身长,5:下身长 |

### 2.9 身材分析结果 analysis 字段详解
| 字段/子字段 | 原型字段 | 页面路径 | 需求字段 | 类型 | 可选值/约束 | 覆盖状态 | 备注 |
|-------------|----------|----------|----------|------|-------------|----------|------|
| feature | 分析特征点 | user-info/index.vue | 特征点 | string | - | 已覆盖 | 如"肩臀比例" |
| description | 特征描述 | user-info/index.vue | 特征描述 | string | - | 已覆盖 | 如"肩部和臀部宽度相近" |
| type | 分析类型 | user-info/index.vue | 类型 | string | positive/neutral | 已覆盖 | 正面/中性特征 |

### 2.10 穿搭建议 suggestions 字段详解
| 字段/子字段 | 原型字段 | 页面路径 | 需求字段 | 类型 | 可选值/约束 | 覆盖状态 | 备注 |
|-------------|----------|----------|----------|------|-------------|----------|------|
| category | 建议类别 | user-info/index.vue | 建议分类 | string | - | 已覆盖 | 如"上装推荐" |
| content | 建议内容 | user-info/index.vue | 建议内容 | string | - | 已覆盖 | 具体建议文本 |
| priority | 优先级 | user-info/index.vue | 优先级 | string | high/medium/low | 已覆盖 | 建议重要程度 |

### 2.11 AI评分 multiScore
| 字段/子字段 | 原型字段 | 页面路径 | 需求字段 | 类型 | 可选值/约束 | 覆盖状态 | 备注 |
|-------------|----------|----------|----------|------|-------------|----------|------|
| energy      | 能量评分 | fortune_detail.html | 能量匹配度 | number | 0-100 | 已覆盖 | ... |
| occasion    | 场合评分 | outfit_detail.html | 场合适配度 | number | 0-100 | 已覆盖 | ... |
| style       | 风格评分 | style_advice.html | 风格协调度 | number | 0-100 | 已覆盖 | ... |
| temperament | 气质评分 | outfit_detail.html | 气质提升度 | number | 0-100 | 已覆盖 | ... |

### 2.12 风格分布 styleDistribution
| 子字段      | 原型字段 | 页面路径 | 需求字段 | 类型 | 可选值/约束 | 覆盖状态 | 备注 |
|-------------|----------|----------|----------|------|-------------|----------|------|
| sporty      | 运动风   | style_advice.html | 风格分析 | number | 0-1 | 已覆盖 | ... |
| casual      | 休闲风   | style_advice.html | 风格分析 | number | 0-1 | 已覆盖 | ... |
| elegant     | 优雅风   | style_advice.html | 风格分析 | number | 0-1 | 已覆盖 | ... |
| ...         | ...      | ...      | ...      | ...  | ...         | ...      | ...  |

### 2.13 形象评估 appearance
| 字段/子字段 | 原型字段 | 页面路径 | 需求字段 | 类型 | 可选值/约束 | 覆盖状态 | 备注 |
|-------------|----------|----------|----------|------|-------------|----------|------|
| score       | 形象分   | body_data.html | 形象评分 | number | 0-100 | 已覆盖 | ... |
| suggestion  | 形象建议 | body_data.html | 形象建议 | string | - | 已覆盖 | ... |
| ...         | ...      | ...      | ...      | ...  | ...         | ...      | ...  |

### 2.14 AI识别结果（图像识别接口响应）
| 字段/子字段 | 原型字段 | 页面路径 | 需求字段 | 类型 | 可选值/约束 | 覆盖状态 | 备注 |
|-------------|----------|----------|----------|------|-------------|----------|------|
| category    | 类别     | style_advice.html | 服饰类别 | string | 见字典 | 已覆盖 | ... |
| colors      | 颜色     | style_advice.html | 颜色 | array | 见色彩字典 | 已覆盖 | ... |
| style       | 风格     | style_advice.html | 风格 | string | 见风格字典 | 已覆盖 | ... |
| ...         | ...      | ...      | ...      | ...  | ...         | ...      | ...  |

### 2.15 视频生成任务（视频接口响应）
| 字段/子字段 | 原型页面字段 | 页面路径 | 需求字段 | 类型 | 可选值/约束 | 覆盖状态 | 备注 |
|-------------|----------|----------|----------|------|-------------|----------|------|
| videoUrl    | 视频URL  | outfit_video.html | 视频 | string | url | 已覆盖 | ... |
| status      | 任务状态 | outfit_video.html | 状态 | string | PROCESSING等 | 已覆盖 | ... |
| videoTaskId | 任务ID   | outfit_video.html | 任务ID | string | 唯一 | 已覆盖 | ... |
| ...         | ...      | ...      | ...      | ...  | ...         | ...      | ...  |

### 2.16 完整运势解读字段详解
| 字段/子字段 | 原型字段 | 页面路径 | 需求字段 | 类型 | 可选值/约束 | 覆盖状态 | 备注 |
|-------------|----------|----------|----------|------|-------------|----------|------|
| baziCombination | 八字组合 | fortune/complete/index.vue | 八字组合 | object | BaziInfo | 需新增 | 八字四柱信息 |
| baziCombination.year | 年柱 | fortune/complete/index.vue | 年柱 | object | GanzhiInfo | 需新增 | 年柱天干地支 |
| baziCombination.month | 月柱 | fortune/complete/index.vue | 月柱 | object | GanzhiInfo | 需新增 | 月柱天干地支 |
| baziCombination.day | 日柱 | fortune/complete/index.vue | 日柱 | object | GanzhiInfo | 需新增 | 日柱天干地支 |
| baziCombination.hour | 时柱 | fortune/complete/index.vue | 时柱 | object | GanzhiInfo | 需新增 | 时柱天干地支 |
| wuxingAnalysis | 五行分析 | fortune/complete/index.vue | 五行分析 | object | WuxingInfo | 需新增 | 五行能量分析 |
| wuxingAnalysis.elements | 五行元素 | fortune/complete/index.vue | 五行比例 | array | WuxingElement[] | 需新增 | 五行占比数据 |
| wuxingAnalysis.analysis | 五行分析文本 | fortune/complete/index.vue | 分析结果 | string | - | 需新增 | 五行分析说明 |
| wuxingAnalysis.relationships | 五行关系 | fortune/complete/index.vue | 相生相克 | object | - | 需新增 | 相生相克关系 |
| overallFortune | 整体运势 | fortune/complete/index.vue | 整体运势 | object | OverallFortuneInfo | 需新增 | 一生运势解读 |
| overallFortune.lifetimeFortune | 一生运势 | fortune/complete/index.vue | 运势曲线 | array | FortunePoint[] | 需新增 | 100年运势数据 |
| overallFortune.currentAge | 当前年龄 | fortune/complete/index.vue | 当前年龄 | number | - | 需新增 | 用户当前年龄 |
| overallFortune.currentScore | 当前分数 | fortune/complete/index.vue | 当前运势 | number | 0-100 | 需新增 | 当前年龄运势分数 |
| overallFortune.trend | 运势趋势 | fortune/complete/index.vue | 趋势方向 | string | 上升/下降/平稳 | 需新增 | 运势变化趋势 |
| overallFortune.keyPoints | 关键节点 | fortune/complete/index.vue | 关键年龄 | array | KeyPoint[] | 需新增 | 人生关键转折点 |
| luckyAdvice | 吉运建议 | fortune/complete/index.vue | 吉运建议 | object | LuckyAdviceInfo | 需新增 | 提升运势建议 |
| luckyAdvice.clothing | 服装建议 | fortune/complete/index.vue | 服装选择 | object | AdviceCategory | 需新增 | 服装搭配建议 |
| luckyAdvice.jewelry | 首饰建议 | fortune/complete/index.vue | 首饰佩戴 | object | AdviceCategory | 需新增 | 首饰佩戴建议 |
| luckyAdvice.fengshui | 风水建议 | fortune/complete/index.vue | 居家风水 | object | AdviceCategory | 需新增 | 居家风水建议 |
| detailedFortune | 详细运势 | fortune/complete/index.vue | 详细解读 | object | DetailedFortuneInfo | 需新增 | 月年运势详情 |
| detailedFortune.monthly | 月运势 | fortune/complete/index.vue | 本月运势 | object | FortuneDetails | 需新增 | 当月运势详情 |
| detailedFortune.yearly | 年运势 | fortune/complete/index.vue | 本年运势 | object | FortuneDetails | 需新增 | 当年运势详情 |

### 2.17 八字干支信息 GanzhiInfo 详解
| 字段/子字段 | 原型字段 | 页面路径 | 需求字段 | 类型 | 可选值/约束 | 覆盖状态 | 备注 |
|-------------|----------|----------|----------|------|-------------|----------|------|
| tiangan | 天干 | fortune/complete/index.vue | 天干 | string | 甲乙丙丁戊己庚辛壬癸 | 需新增 | 十天干之一 |
| dizhi | 地支 | fortune/complete/index.vue | 地支 | string | 子丑寅卯辰巳午未申酉戌亥 | 需新增 | 十二地支之一 |
| canggan | 藏干 | fortune/complete/index.vue | 藏干 | array | 天干数组 | 需新增 | 地支所含天干 |

### 2.18 五行元素 WuxingElement 详解
| 字段/子字段 | 原型字段 | 页面路径 | 需求字段 | 类型 | 可选值/约束 | 覆盖状态 | 备注 |
|-------------|----------|----------|----------|------|-------------|----------|------|
| element | 五行属性 | fortune/complete/index.vue | 五行 | string | 金木水火土 | 需新增 | 五行之一 |
| percentage | 占比 | fortune/complete/index.vue | 百分比 | number | 0-100 | 需新增 | 该五行占比 |
| isRizhu | 是否日主 | fortune/complete/index.vue | 日主标识 | boolean | true/false | 需新增 | 是否为日主五行 |

### 2.19 运势数据点 FortunePoint 详解
| 字段/子字段 | 原型字段 | 页面路径 | 需求字段 | 类型 | 可选值/约束 | 覆盖状态 | 备注 |
|-------------|----------|----------|----------|------|-------------|----------|------|
| year | 年份 | fortune/complete/index.vue | 年份 | number | - | 需新增 | 公历年份 |
| age | 年龄 | fortune/complete/index.vue | 年龄 | number | 0-100 | 需新增 | 对应年龄 |
| score | 运势分数 | fortune/complete/index.vue | 运势分 | number | 0-100 | 需新增 | 该年运势分数 |
| period | 人生阶段 | fortune/complete/index.vue | 人生期 | string | 见阶段字典 | 需新增 | 如"幼儿期"、"青年期" |

### 2.20 关键节点 KeyPoint 详解
| 字段/子字段 | 原型字段 | 页面路径 | 需求字段 | 类型 | 可选值/约束 | 覆盖状态 | 备注 |
|-------------|----------|----------|----------|------|-------------|----------|------|
| age | 关键年龄 | fortune/complete/index.vue | 年龄 | number | 0-100 | 需新增 | 关键转折年龄 |
| year | 关键年份 | fortune/complete/index.vue | 年份 | number | - | 需新增 | 关键转折年份 |
| score | 运势分数 | fortune/complete/index.vue | 分数 | number | 0-100 | 需新增 | 该年运势分数 |
| description | 节点描述 | fortune/complete/index.vue | 描述 | string | - | 需新增 | 关键事件描述 |

### 2.21 建议分类 AdviceCategory 详解（复用但扩展）
| 字段/子字段 | 原型字段 | 页面路径 | 需求字段 | 类型 | 可选值/约束 | 覆盖状态 | 备注 |
|-------------|----------|----------|----------|------|-------------|----------|------|
| title | 类别标题 | fortune/complete/index.vue | 标题 | string | - | 需新增 | 如"服装选择"、"首饰佩戴" |
| items | 建议项目 | fortune/complete/index.vue | 建议列表 | array | AdviceItem[] | 已覆盖 | 具体建议项目列表 |

### 2.22 建议项目 AdviceItem 详解（扩展）
| 字段/子字段 | 原型字段 | 页面路径 | 需求字段 | 类型 | 可选值/约束 | 覆盖状态 | 备注 |
|-------------|----------|----------|----------|------|-------------|----------|------|
| label | 建议标签 | fortune/complete/index.vue | 标签 | string | - | 需新增 | 建议类型标签 |
| advice | 建议内容 | fortune/complete/index.vue | 建议 | string | - | 需新增 | 具体建议内容 |
| type | 建议类型 | fortune/complete/index.vue | 类型 | string | avoid/recommend | 需新增 | 避免或推荐类型 |

### 2.23 运势详情 FortuneDetails 详解
| 字段/子字段 | 原型字段 | 页面路径 | 需求字段 | 类型 | 可选值/约束 | 覆盖状态 | 备注 |
|-------------|----------|----------|----------|------|-------------|----------|------|
| career | 事业运势 | fortune/complete/index.vue | 事业 | object | FortuneItemData | 需新增 | 事业运势详情 |
| wealth | 财富运势 | fortune/complete/index.vue | 财富 | object | FortuneItemData | 需新增 | 财富运势详情 |
| health | 健康运势 | fortune/complete/index.vue | 健康 | object | FortuneItemData | 需新增 | 健康运势详情 |
| marriage | 婚姻运势 | fortune/complete/index.vue | 婚姻 | object | FortuneItemData | 需新增 | 婚姻运势详情 |
| children | 子女运势 | fortune/complete/index.vue | 子女 | object | FortuneItemData | 需新增 | 子女运势详情 |

### 2.24 运势项目数据 FortuneItemData 详解
| 字段/子字段 | 原型字段 | 页面路径 | 需求字段 | 类型 | 可选值/约束 | 覆盖状态 | 备注 |
|-------------|----------|----------|----------|------|-------------|----------|------|
| status | 运势状态 | fortune/complete/index.vue | 状态 | string | - | 需新增 | 运势状态描述 |
| highlight | 突出信息 | fortune/complete/index.vue | 亮点 | string | - | 需新增 | 运势亮点信息 |
| color | 显示颜色 | fortune/complete/index.vue | 颜色 | string | hex颜色 | 需新增 | UI显示颜色 |
| content | 运势内容 | fortune/complete/index.vue | 内容 | string/array | - | 需新增 | 详细运势说明，支持字符串或数组 |

---

## 3. 字段覆盖状态统计
- **已覆盖**: 85个字段及子字段
- **需新增**: 45个完整运势解读相关字段
- **总计**: 130个字段及子字段

## 4. 接口拆分建议
### 4.1 完整运势解读接口设计
基于页面复杂度和数据量，建议采用以下接口拆分策略：

**主接口（推荐）**：
- `/api/user/fortune/complete-reading` - 获取完整运势解读（包含所有模块）

**可选拆分接口**：
- `/api/user/fortune/bazi-combination` - 八字组合信息
- `/api/user/fortune/wuxing-analysis` - 五行分析
- `/api/user/fortune/overall-reading` - 整体运势解读
- `/api/user/fortune/lucky-advice` - 吉运建议
- `/api/user/fortune/detailed-fortune` - 详细运势解读

### 4.2 性能和用户体验考虑
- **首屏加载**：优先加载八字组合、五行分析等核心模块
- **懒加载**：一生运势曲线等数据量大的模块可考虑懒加载
- **缓存策略**：八字组合等静态数据可长期缓存，详细运势需每日更新

---

## 5. 结论与建议
- **字段覆盖性结论**：AI服务接口字段与原型页面、需求文档已实现全量映射。新增的今日能量页面接口完整覆盖了原型需求中的所有能量解读字段，包括：
  1. **基础能量信息**：日期信息、总分、排名百分比、高峰时段等核心展示字段
  2. **五维能量评分**：爱情、事业、财富、健康、人际五个维度的详细评分
  3. **宜忌指南**：分类化的宜做/忌做事项，支持图标和文本展示
  4. **幸运元素**：颜色、服饰、配饰、妆容四个方面的提升建议
  5. **数据结构完整性**：所有嵌套结构都有详细的字段定义和约束说明

- **新增接口特点**：
  1. 完整支持今日能量页面的所有功能需求
  2. 实现能量模式专属的命理解读功能
  3. 提供详细的五行能量分析和穿搭建议
  4. 包含宜忌指南和生活建议的结构化数据
  5. 错误处理完善，支持模式检查和信息完整性验证

- **建议**：后续如有新AI能力或字段变更，需同步更新本对照清单，保持三方一致性，便于自动化校验与团队协作。特别注意今日能量相关字段的数据类型和约束条件必须与前端页面组件的Props定义保持一致。 