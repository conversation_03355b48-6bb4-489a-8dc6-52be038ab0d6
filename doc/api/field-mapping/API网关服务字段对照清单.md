# API网关服务字段对照清单

> 本清单用于核查API网关服务（gateway-service）API接口文档字段与原型页面、需求文档的覆盖关系，确保无遗漏。

---

## 1. 字段对照表说明
- "接口字段"：API文档中定义的字段
- "原型字段"：相关原型页面涉及的所有字段
- "需求字段"：需求/设计文档中与本服务相关的字段
- "覆盖状态"：标注"已覆盖/未覆盖/接口冗余/需求遗漏"等

---

## 2. 字段对照总览
| 接口字段（API文档） | 原型字段 | 需求字段 | 类型/结构 | 可选值/约束 | 覆盖状态 | 备注 |
|---------------------|----------|----------|-----------|-------------|----------|------|
| serviceId           | 服务ID   | 服务ID   | string    | 必填        | 已覆盖   | 路由/熔断 |
| path                | 路由路径 | 路由路径 | string    | 必填        | 已覆盖   | 路由 |
| filters             | 过滤器   | 过滤器   | array     | 可选        | 已覆盖   | 路由 |
| predicates          | 断言     | 断言     | array     | 可选        | 已覆盖   | 路由 |
| order               | 优先级   | 优先级   | int       | 可选        | 已覆盖   | 路由 |
| metadata            | 元数据   | 元数据   | object    | 可选        | 已覆盖   | 路由 |
| resource            | 资源     | 资源     | string    | 必填        | 已覆盖   | 限流 |
| type                | 类型     | 类型     | string    | 必填        | 已覆盖   | 限流/黑白名单 |
| count               | 阈值     | 阈值     | int       | 必填        | 已覆盖   | 限流 |
| interval            | 时间窗口 | 时间窗口 | int       | 必填        | 已覆盖   | 限流 |
| failureRate         | 失败率   | 失败率   | int       | 必填        | 已覆盖   | 熔断 |
| waitDuration        | 等待时间 | 等待时间 | int       | 必填        | 已覆盖   | 熔断 |
| ringBufferSize      | 缓冲区   | 缓冲区   | int       | 必填        | 已覆盖   | 熔断 |
| value               | IP/账号  | IP/账号  | string    | 必填        | 已覆盖   | 黑白名单 |
| expireTime          | 过期时间 | 过期时间 | string    | 可选        | 已覆盖   | 黑白名单 |
| reason              | 原因     | 原因     | string    | 可选        | 已覆盖   | 黑白名单 |
| qps                 | QPS      | QPS      | int       | 只读        | 已覆盖   | 监控 |
| rt                  | 响应时间 | 响应时间 | int       | 只读        | 已覆盖   | 监控/日志 |
| error               | 错误数   | 错误数   | int       | 只读        | 已覆盖   | 监控 |
| timestamp           | 时间戳   | 时间戳   | string    | 只读        | 已覆盖   | 日志 |
| method              | 方法     | 方法     | string    | 只读        | 已覆盖   | 日志 |
| status              | 状态码   | 状态码   | int       | 只读        | 已覆盖   | 日志 |
| code                | 响应码   | 响应码   | int       | 见错误码规范 | 已覆盖   | 通用 |
| msg                 | 响应消息 | 响应消息 | string    | 见错误码规范 | 已覆盖   | 通用 |
| data                | 数据体   | 数据体   | object    | 结构化      | 已覆盖   | 通用 |

---

## 3. 复杂结构字段细化（如有）
- 对于嵌套对象、枚举、数组等复杂结构，单独分节细化所有子字段及可选值。
- 示例：
| 字段/子字段 | 原型字段 | 需求字段 | 类型/结构 | 可选值/约束 | 覆盖状态 | 备注 |
|-------------|----------|----------|-----------|-------------|----------|------|
| filters     | 过滤器   | 过滤器   | array     | StripPrefix=1等 | 已覆盖 | 路由 |
| metrics.qps | QPS      | QPS      | int       | >=0         | 已覆盖   | 监控 |
| logs.status | 状态码   | 状态码   | int       | 200/400/500等 | 已覆盖 | 日志 |

---

## 4. 衍生/推荐字段（如有）
- 记录后端推算、自动生成、推荐类字段。
- 当前网关服务以配置和监控为主，无后端推算类字段。

---

## 5. 结论与建议
- 字段覆盖性结论、需补充细化的字段、结构优化建议等。
- gateway-service接口字段与需求/设计/原型高度一致，无遗漏或冗余，结构清晰，建议持续维护。
- 建议后续如有字段变更，优先同步更新本对照清单，便于团队协作和自动化校验。

---

## 6. 网关通用字段模板（可复用）
| 字段名 | 类型 | 说明 |
|--------|------|------|
| code   | int  | 响应码，见错误码规范 |
| msg    | string | 响应消息 |
| data   | object | 响应数据体 |

---

# 最终审查
实施与最终计划完全匹配。

如需进一步补充或有新需求，请随时告知。否则本次API网关服务字段对照清单核查归档任务已圆满完成。 