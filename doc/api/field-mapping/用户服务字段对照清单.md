# 用户服务（user-service）字段对照清单

> 本清单用于核查用户服务API接口文档字段与原型页面、需求文档的覆盖关系，确保无遗漏。

---

## 1. 字段对照表说明
- "接口字段"：user-service-api.md 中定义的字段
- "原型字段"：user-info.html、body_data.html 页面涉及的所有用户信息与体型字段
- "需求字段"：主需求文档、五行命理需求文档等中与用户信息相关的字段

---

## 2. 字段对照总览

| 接口字段（API文档） | 原型字段（user-info.html/body_data.html） | 需求字段（需求文档） | 备注 |
|---------------------|------------------------------------------|----------------------|------|
| userId              | -                                        | 用户唯一标识         |      |
| phone               | 手机号                                   | 手机号               |      |
| openId              | -                                        | 微信openId           |      |
| password            | 密码                                     | 密码                 |      |
| nickname            | 昵称                                     | 昵称                 |      |
| avatar              | 头像                                     | 头像                 |      |
| gender              | 性别                                     | 性别                 |      |
| photoUrl            | 全身照                                   | 全身照               |      |
| height              | 身高、身高类型                           | 身高                 | 原型有身高类型细分 |
| weight              | 体重                                     | 体重                 |      |
| bodyType            | 体型、体型特点                           | 体型                 | 体型分析自动生成   |
| skinTone            | 肤色                                     | 肤色                 |      |
| stylePreferences    | 风格偏好                                 | 风格偏好             |      |
| bodyShape           | 体型细分（见下表）                       | 体型细分             | 需细化结构         |
| mode                | 用户模式                                 | 用户模式             |      |
| fullName            | 姓名                                     | 姓名                 | 能量信息           |
| birthDate           | 出生日期                                 | 出生日期             |      |
| birthTime           | 出生时间                                 | 出生时间             |      |
| birthPlace          | 出生地点                                 | 出生地点             |      |
| createTime          | -                                        | 创建时间             |      |
| updateTime          | -                                        | 更新时间             |      |
| ...                 | ...                                      | ...                  |      |

---

## 3. 体型细分字段对照（bodyShape）

| bodyShape 子字段（API） | 原型字段（body_data.html） | 需求字段 | 备注 |
|------------------------|---------------------------|----------|------|
| shoulderWidth          | 肩膀                      | 肩宽     |      |
| waistShape             | 腰型                      | 腰型     |      |
| belly                  | 肚腩                      | 腹部     |      |
| hip                    | 臀型                      | 臀型     |      |
| hipShape               | 胯型                      | 胯型     |      |
| armLength              | 臂长                      | 臂长     |      |
| armCircum              | 臂围                      | 臂围     |      |
| hipWidth               | 胯部                      | 胯部     |      |
| thigh                  | 大腿                      | 大腿     |      |
| calf                   | 小腿                      | 小腿     |      |
| bodyFat                | 上下身粗                  | 身体粗   |      |
| bodyLength             | 上下身长                  | 身体长   |      |

---

## 4. 其他衍生/推荐字段

| 字段           | 原型字段         | 需求字段 | 备注 |
|----------------|------------------|----------|------|
| sizeTop        | 上装尺码         | 尺码建议 | 可由后端推算 |
| sizeBottom     | 下装尺码         | 尺码建议 | 可由后端推算 |
| shoeSize       | 鞋码             | 尺码建议 | 可由后端推算 |
| bodyTypeTag    | 体型特点标签     | 体型分析 | 自动生成     |
| dressingAdvice | 穿衣建议         | 穿搭建议 | 自动生成     |

---

## 5. 结论与建议
- user-service-api.md 字段已基本覆盖原型与需求所有用户信息、体型、档案相关字段。
- 建议在 bodyShape 字段中明确所有细分子字段的结构和示例。
- 衍生/推荐类字段建议通过 profile 扩展或单独接口返回。

---

> 如需进一步细化或补充，请在本清单基础上补充说明。 