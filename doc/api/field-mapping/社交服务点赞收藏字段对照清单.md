# 社交服务点赞/收藏字段对照清单

> 本清单用于核查社交服务API接口文档点赞/收藏相关字段与原型页面、需求文档的覆盖关系，确保无遗漏。

---

## 1. 字段对照表说明
| 接口字段         | 原型页面字段 | 页面路径/模块         | 需求字段         | 类型/结构 | 可选值/约束 | 示例 | 覆盖状态 | 备注 | 自动化校验建议 |
|------------------|--------------|----------------------|------------------|-----------|-------------|------|----------|------|----------------|
| contentId        | 内容ID       | outfit_detail.html, favorites.html, inspiration_center.html | 内容唯一标识     | string    | 唯一      | "abc123" | 已覆盖 | 主键 | 唯一性校验 |
| type             | 内容类型     | outfit_detail.html, favorites.html | 内容类型         | string    | outfit/note等 | "outfit" | 已覆盖 | 枚举 | 枚举校验 |
| likeStatus       | 点赞状态     | outfit_detail.html, home.html | 是否已点赞       | bool/int  | true/false/0/1 | true | 已覆盖 | 状态 | 类型/枚举校验 |
| likeCount        | 点赞数       | outfit_detail.html, home.html | 点赞总数         | int       | >=0      | 128  | 已覆盖 | 需同步 | 数值校验 |
| likeLimitReached | 点赞激励上限 | outfit_detail.html    | 是否达上限       | bool      | true/false | false | 已覆盖 | 激励 | 类型校验 |
| likeReward       | 点赞激励反馈 | outfit_detail.html    | 点赞灵感值奖励   | object    | 见下分节   | {...} | 已覆盖 | 激励 | 结构递归校验 |
| collected        | 收藏状态     | favorites.html, outfit_detail.html | 是否已收藏       | bool/int  | true/false/0/1 | true | 已覆盖 | 状态 | 类型/枚举校验 |
| collectionCount  | 收藏数       | favorites.html, outfit_detail.html | 收藏总数         | int       | >=0      | 56   | 已覆盖 | 需同步 | 数值校验 |
| collectionId     | 收藏夹ID     | favorites.html         | 收藏夹唯一标识   | string    | 唯一      | "fav001" | 已覆盖 | 主键 | 唯一性校验 |
| collectionName   | 收藏夹名称   | favorites.html         | 收藏夹名称       | string    | -         | "最爱搭配" | 已覆盖 | ...  | 类型校验 |
| collectionItems  | 收藏夹内容   | favorites.html         | 收藏内容列表     | array     | -         | ["abc123"] | 已覆盖 | ...  | 结构递归校验 |
| collectionGroups | 分组管理     | favorites.html         | 收藏分组         | array     | -         | [...] | 已覆盖 | ...  | 结构递归校验 |
| collectionLimitReached | 收藏激励上限 | favorites.html   | 是否达上限       | bool      | true/false | false | 已覆盖 | 激励 | 类型校验 |
| collectionReward | 收藏激励反馈 | favorites.html         | 收藏灵感值奖励   | object    | 见下分节   | {...} | 已覆盖 | 激励 | 结构递归校验 |
| errorCode        | 错误码       | -                      | 错误码           | int       | 见接口文档 | 0    | 已覆盖 | 异常 | 枚举校验 |
| errorMsg         | 错误信息     | -                      | 错误信息         | string    | -         | ""   | 已覆盖 | 异常 | 类型校验 |
| success          | 操作成功     | -                      | 操作是否成功     | bool      | true/false | true | 已覆盖 | ...  | 类型校验 |
| batch            | 批量操作     | favorites.html         | 批量内容ID列表   | array     | -         | ["abc123"] | 已覆盖 | ...  | 结构递归校验 |
| failItems        | 批量失败项   | favorites.html         | 批量失败内容及原因 | array    | -         | [...] | 已覆盖 | ...  | 结构递归校验 |
| ...              | ...          | ...                    | ...              | ...       | ...       | ...  | ...      | ...  | ...            |

---

## 2. 激励与反馈结构细化
### 2.1 点赞激励 likeReward
| 字段名      | 原型字段 | 页面路径 | 需求字段 | 类型 | 可选值/约束 | 覆盖状态 | 备注 |
|-------------|----------|----------|----------|------|-------------|----------|------|
| rewardType  | 激励类型 | outfit_detail.html | 激励类型 | string | like | 已覆盖 | ... |
| rewardValue | 本次奖励 | outfit_detail.html | 本次灵感值 | int | >=0 | 已覆盖 | ... |
| rewardTotal | 今日累计 | outfit_detail.html | 今日累计灵感值 | int | >=0 | 已覆盖 | ... |
| rewardLimit | 每日上限 | outfit_detail.html | 每日激励上限 | int | >=0 | 已覆盖 | ... |
| rewardStatus| 激励状态 | outfit_detail.html | 激励状态 | string | success/limit_reached/duplicate | 已覆盖 | ... |

### 2.2 收藏激励 collectionReward
| 字段名      | 原型字段 | 页面路径 | 需求字段 | 类型 | 可选值/约束 | 覆盖状态 | 备注 |
|-------------|----------|----------|----------|------|-------------|----------|------|
| rewardType  | 激励类型 | favorites.html | 激励类型 | string | collect | 已覆盖 | ... |
| rewardValue | 本次奖励 | favorites.html | 本次灵感值 | int | >=0 | 已覆盖 | ... |
| rewardTotal | 今日累计 | favorites.html | 今日累计灵感值 | int | >=0 | 已覆盖 | ... |
| rewardLimit | 每日上限 | favorites.html | 每日激励上限 | int | >=0 | 已覆盖 | ... |
| rewardStatus| 激励状态 | favorites.html | 激励状态 | string | success/limit_reached/duplicate | 已覆盖 | ... |

---

## 3. 结论与建议
- 字段覆盖性结论：社交服务点赞/收藏相关接口字段与原型页面、需求文档已实现全量映射，激励、状态、异常、批量等结构已细化，类型与约束标准化。
- 建议：后续如有新业务场景或字段变更，需同步更新本对照清单，保持三方一致性，便于自动化校验与团队协作。 