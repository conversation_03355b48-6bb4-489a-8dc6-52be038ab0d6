# 支付服务字段对照清单

> 本清单用于核查支付服务 API接口文档字段与原型页面、需求文档的覆盖关系，确保无遗漏。

---

## 1. 字段对照表说明
- "接口字段"：API文档中定义的字段
- "原型字段"：相关原型页面涉及的所有字段
- "需求字段"：需求/设计文档中与本服务相关的字段
- "类型/结构"：字段类型或结构说明
- "可选值/约束"：枚举、约束、格式等
- "覆盖状态"：标注"已覆盖/未覆盖/接口冗余/需求遗漏/设计偏差"等
- "备注"：补充说明

---

## 2. 字段对照总览
| 接口字段（API文档） | 原型字段 | 需求字段 | 类型/结构 | 可选值/约束 | 覆盖状态 | 备注 |
|---------------------|----------|----------|-----------|-------------|----------|------|
| userId              | 用户ID输入/自动获取 | 用户ID、user_id | string      | 非空，唯一 | 已覆盖   |      |
| originOrderId       | 订单来源标识 | 订单来源、originOrderId | string      | 可空      | 已覆盖   |      |
| type                | 充值类型选择（充值/订阅） | 灵感值类型、充值类型 | string      | RECHARGE/SUBSCRIPTION | 已覆盖   |      |
| channel             | 支付渠道选择（微信/支付宝） | 支付渠道、channel | string      | WECHAT/ALIPAY | 已覆盖   |      |
| amount              | 金额输入/产品价格展示 | 金额、price、充值金额 | int         | >0，单位分 | 已覆盖   |      |
| subject             | 产品名称/充值说明 | 产品名称、subject | string      | 非空      | 已覆盖   |      |
| description         | 订单描述/产品详情 | 订单描述、description | string      | 可空      | 已覆盖   |      |
| clientIp            | 隐藏字段/自动获取 | 客户端IP、clientIp | string      | IPv4/IPv6 | 已覆盖   |      |
| notifyUrl           | 隐藏字段/自动配置 | 支付回调地址、notifyUrl | string      | URL      | 已覆盖   |      |
| returnUrl           | 隐藏字段/自动配置 | 支付完成跳转、returnUrl | string      | URL      | 已覆盖   |      |
| orderId             | 订单号展示/订单详情页 | 订单ID、order_id | string      | 非空，唯一 | 已覆盖   |      |
| paymentUrl          | 跳转支付/二维码链接 | 支付链接、paymentUrl | string      | URL      | 已覆盖   |      |
| qrCode              | 支付二维码展示 | 支付二维码、qrCode | string      | base64/URL | 已覆盖   |      |
| expiredAt           | 订单有效期展示 | 订单过期时间、expiredAt | string      | ISO8601时间 | 已覆盖   |      |
| status              | 订单状态展示 | 订单状态、status | string      | PENDING/PAID/EXPIRED/FAILED | 已覆盖   |      |
| paidAt              | 支付时间展示 | 支付时间、paidAt | string      | ISO8601时间 | 已覆盖   |      |
| transactionId       | 支付流水号展示 | 支付流水号、transactionId | string      | 第三方支付流水 | 已覆盖   |      |
| products            | 充值/订阅产品列表 | 充值产品、订阅产品、products | array<object> | 见下方复杂结构 | 已覆盖   |      |
| id (product)        | 产品ID | 产品ID、id | string      | 非空，唯一 | 已覆盖   |      |
| name (product)      | 产品名称 | 产品名称、name | string      | 非空      | 已覆盖   |      |
| price (product)     | 产品价格 | 产品价格、price | int         | >0，单位分 | 已覆盖   |      |
| baseAmount          | 基础灵感值展示 | 基础灵感值、baseAmount | int         | >=0      | 已覆盖   |      |
| bonusAmount         | 赠送灵感值展示 | 赠送灵感值、bonusAmount | int         | >=0      | 已覆盖   |      |
| totalAmount         | 总灵感值展示 | 总灵感值、totalAmount | int         | >=0      | 已覆盖   |      |
| isPopular           | 热门标识/推荐标签 | 是否热门、isPopular | boolean     | true/false | 已覆盖   |      |
| isLimited           | 限时标识/活动标签 | 是否限时、isLimited | boolean     | true/false | 已覆盖   |      |
| validFrom           | 有效期起始展示 | 有效期起始、validFrom | string      | ISO8601时间 | 已覆盖   |      |
| validTo             | 有效期结束展示 | 有效期结束、validTo | string      | ISO8601时间 | 已覆盖   |      |
| productId           | 产品ID | 产品ID、productId | string      | 非空      | 已覆盖   |      |
| autoRenew           | 自动续费开关 | 自动续费、autoRenew | boolean     | true/false | 已覆盖   |      |
| type (subscription) | 订阅类型选择（月卡/年卡） | 订阅类型、type | string      | MONTHLY/YEARLY | 已覆盖   |      |
| periodMonths        | 订阅周期展示 | 订阅周期、periodMonths | int         | >0       | 已覆盖   |      |
| dailyReward         | 每日奖励灵感值展示 | 每日奖励、dailyReward | int         | >=0      | 已覆盖   |      |
| totalReward         | 总奖励灵感值展示 | 总奖励、totalReward | int         | >=0      | 已覆盖   |      |
| privileges          | 订阅特权列表 | 订阅特权、privileges | array<string> | VIP_CHAT/UNLIMITED_RECOMMEND | 已覆盖   |      |
| discount            | 折扣信息展示 | 折扣、discount | float       | 0~1      | 已覆盖   |      |
| subscriptionId      | 订阅ID | 订阅ID、subscriptionId | string      | 非空      | 已覆盖   |      |
| invoiceId           | 发票ID | 发票ID、invoiceId | string      | 非空      | 已覆盖   |      |
| title               | 发票抬头输入 | 发票抬头、title | string      | 非空      | 已覆盖   |      |
| taxNumber           | 税号输入 | 税号、taxNumber | string      | 可空      | 已覆盖   |      |
| email               | 邮箱输入 | 邮箱、email | string      | 邮箱格式   | 已覆盖   |      |
| estimatedTime       | 预计完成时间展示 | 预计完成时间、estimatedTime | string      | ISO8601时间 | 已覆盖   |      |
| invoiceUrl          | 发票下载/查看链接 | 发票链接、invoiceUrl | string      | URL      | 已覆盖   |      |
| issuedAt            | 发票开具时间展示 | 发票开具时间、issuedAt | string      | ISO8601时间 | 已覆盖   |      |
| refundId            | 退款ID | 退款ID、refundId | string      | 非空      | 已覆盖   |      |
| reason              | 退款原因输入 | 退款原因、reason | string      | 可空      | 已覆盖   |      |
| remarks             | 退款备注输入 | 退款备注、remarks | string      | 可空      | 已覆盖   |      |
| processedAt         | 退款处理时间展示 | 退款处理时间、processedAt | string      | ISO8601时间 | 已覆盖   |      |
| completedAt         | 退款完成时间展示 | 退款完成时间、completedAt | string      | ISO8601时间 | 已覆盖   |      |
| code                | 响应码展示/错误提示 | 响应码、code | int         | 0/1001/1002/1003/5001-5015 | 已覆盖   |      |
| msg                 | 响应消息/错误提示 | 响应消息、msg | string      | 任意      | 已覆盖   |      |
| data                | 响应数据体 | 响应数据、data | object      | 结构见各接口 | 已覆盖   |      |

---

## 3. 复杂结构字段细化（如有）
| 字段/子字段 | 原型字段 | 需求字段 | 类型/结构 | 可选值/约束 | 覆盖状态 | 备注 |
|-------------|----------|----------|-----------|-------------|----------|------|
| products    | 充值/订阅产品列表 | 充值产品、订阅产品 | array<object> | 见下 | 已覆盖 | |
| └ id        | 产品ID | 产品ID、id | string | 非空 | 已覆盖 | |
| └ name      | 产品名称 | 产品名称、name | string | 非空 | 已覆盖 | |
| └ price     | 产品价格 | 产品价格、price | int | >0 | 已覆盖 | 单位分 |
| └ baseAmount| 基础灵感值展示 | 基础灵感值、baseAmount | int | >=0 | 已覆盖 | |
| └ bonusAmount| 赠送灵感值展示 | 赠送灵感值、bonusAmount | int | >=0 | 已覆盖 | |
| └ totalAmount| 总灵感值展示 | 总灵感值、totalAmount | int | >=0 | 已覆盖 | |
| └ isPopular | 热门标识/推荐标签 | 是否热门、isPopular | boolean | true/false | 已覆盖 | |
| └ isLimited | 限时标识/活动标签 | 是否限时、isLimited | boolean | true/false | 已覆盖 | |
| └ validFrom | 有效期起始展示 | 有效期起始、validFrom | string | ISO8601时间 | 已覆盖 | |
| └ validTo   | 有效期结束展示 | 有效期结束、validTo | string | ISO8601时间 | 已覆盖 | |
| privileges  | 订阅特权列表 | 订阅特权、privileges | array<string> | VIP_CHAT/UNLIMITED_RECOMMEND | 已覆盖 | |
| status      | 订单状态展示 | 订单状态、status | string | PENDING/PAID/EXPIRED/FAILED | 已覆盖 | |

---

## 4. 衍生/推荐字段（如有）
- 记录后端推算、自动生成、推荐类字段。

---

## 5. 结论与建议
- 字段覆盖性结论：本次核查，支付服务相关接口字段与原型页面、需求/设计文档字段实现了高度一一对应，所有核心业务（充值、订阅、发票、退款等）字段均已覆盖，无明显遗漏。
- 复杂结构（如产品列表、特权、订单状态等）已在接口、原型、需求三方文档中保持一致，字段类型、可选值、约束清晰。
- 未发现接口冗余或需求遗漏字段，接口设计与业务需求、前端原型高度对齐。
- 建议：
  1. 后续如有新业务（如积分兑换、更多支付渠道、发票类型扩展等），需同步更新三方字段对照清单。
  2. 复杂结构字段如有扩展（如产品属性、订阅特权类型等），建议在本清单"复杂结构字段细化"部分补充。
  3. 建议团队持续维护本对照清单，作为接口/需求/原型变更的同步依据，便于追溯和自动化校验。
- 结构优化建议：当前接口字段结构已较为规范，建议继续保持分组清晰、类型明确、约束标准化的风格。 