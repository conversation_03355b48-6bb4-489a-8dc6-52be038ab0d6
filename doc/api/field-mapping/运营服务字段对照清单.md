# 运营服务字段对照清单

> 本清单用于核查运营服务API接口文档字段与原型页面、需求文档的覆盖关系，确保无遗漏。

---

## 1. 字段对照表说明
- "接口字段"：API文档中定义的字段
- "原型字段"：相关原型页面涉及的所有字段
- "需求字段"：需求/设计文档中与本服务相关的字段
- "类型/结构"：字段类型、结构说明
- "可选值/约束"：字段可选值、取值范围、约束条件
- "覆盖状态"：已覆盖/未覆盖/接口冗余/需求遗漏等
- "备注"：其他补充说明

---

## 2. 字段对照总览
| 接口字段（API文档） | 原型字段 | 需求字段 | 类型/结构 | 可选值/约束 | 覆盖状态 | 备注 |
|---------------------|----------|----------|-----------|-------------|----------|------|
| totalInspiration    | 余额     | balance  | int       | >=0         | 部分覆盖 | 字段含义需确认 |
| chargedInspiration  | 充值     | charged  | int       | >=0         | 部分覆盖 | 字段含义需确认 |
| activityInspiration | 活动     | activity | int       | >=0         | 部分覆盖 | 字段含义需确认 |
| accountId           | 账户ID   | accountId| string    |             | 未覆盖   | 接口文档需补充 |
| userId              | 用户ID   | userId   | string    |             | 未覆盖   | 接口文档需补充 |
| frozenBalance       | 冻结金额 | frozen   | int       | >=0         | 未覆盖   | 接口文档需补充 |
| status              | 状态     | status   | string    |             | 未覆盖   | 接口文档需补充 |
| createTime          | 创建时间 | createTime| datetime |             | 未覆盖   | 接口文档需补充 |
| updateTime          | 更新时间 | updateTime| datetime |             | 未覆盖   | 接口文档需补充 |
| ...                 | ...      | ...      | ...       | ...         | ...      | ...          |

---

## 3. 复杂结构字段细化（如有）
- 任务、成就、活动、团队、等级、充值等模块字段，因接口文档未详列，全部标记为"未覆盖/接口文档需补充"。
- 示例：
| 字段/子字段 | 原型字段 | 需求字段 | 类型/结构 | 可选值/约束 | 覆盖状态 | 备注 |
|-------------|----------|----------|-----------|-------------|----------|------|
| taskId      | 任务ID   | taskId   | string    |             | 未覆盖   | 接口文档需补充 |
| progress    | 进度     | progress | int       | 0-100       | 未覆盖   | 接口文档需补充 |
| reward      | 奖励     | reward   | int/obj   | >=0         | 未覆盖   | 接口文档需补充 |
| ...         | ...      | ...      | ...       | ...         | ...      | ...          |

---

## 4. 衍生/推荐字段（如有）
- 记录后端推算、自动生成、推荐类字段。

---

## 5. 结论与建议
- 当前接口文档字段覆盖率较低，建议尽快补全所有接口的请求/响应字段定义，特别是复杂结构。
- 三方字段对照清单将随接口文档补全动态更新，确保字段无遗漏、结构清晰、便于自动化校验与团队协作。

---

> 本清单为标准化模板，后续请持续补全与维护。 