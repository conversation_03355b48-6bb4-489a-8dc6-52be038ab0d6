# 衣橱服务字段对照清单（需求字段补全版）

> 本清单用于核查 wardrobe-service API 接口文档字段与原型页面、需求文档的覆盖关系，确保无遗漏。

---

## 1. 字段对照表说明
- "接口字段"：wardrobe-service-api.md 中定义的字段
- "原型字段"：相关原型页面涉及的所有字段（add_clothing.html、clothing_detail.html、wardrobe_analysis.html、wardrobe.html 等）
- "需求字段"：需求/设计文档中与衣橱服务相关的字段（已自动提取主需求、五行命理、原型优化、灵感值运营、3.18/3.21新增需求等）

---

## 2. 字段对照总览

| 接口字段（API文档） | 原型字段（已梳理） | 需求字段（已补全） | 类型/结构 | 可选值/约束 | 备注 |
|---------------------|--------------------|--------------------|-----------|-------------|------|
| id                  | 详情页隐含/卡片点击传递 | 衣物唯一ID、饰品唯一ID、搭配唯一ID | string    |             | 衣物/饰品/搭配ID |
| name                | 衣物名称（添加/详情/卡片） | 衣物名称、饰品名称、搭配名称、AI识别结果确认与修改 | string    | 必填         | 名称 |
| category            | 服装种类（AI识别/选择）、类别标签、分类标签 | 服饰分类体系（上装、下装、外套、鞋靴、帽子、围巾、首饰、包袋、配饰）、AI识别衣物类别 | string    | 上衣/下装/外套/裙子/鞋/配饰等 | 衣物类别 |
| subCategory         | 子类别（如衬衫、T恤等，AI识别/选择） | 具体类别（T恤、衬衫、牛仔裤等）、AI识别结果确认与修改 | string    | T恤/衬衫/牛仔裤等 | 衣物子类别 |
| colors              | 主要颜色（AI识别/选择）、颜色标签 | 颜色列表、AI识别颜色、颜色分布分析 | array     | ["白色","黑色",...] | 颜色列表 |
| tags                | 风格标签、材质标签、图案标签 | 标签化管理（季节、场合、颜色、材质、风格、图案等）、AI识别标签 | array     | ["基础款","通勤",...] | 标签 |
| seasons             | 季节适应性（春/夏/秋/冬多选）、卡片描述 | 季节标签、季节均衡度分析、季节性衣橱整理 | array     | ["春","夏","秋","冬"] | 适合季节 |
| occasions           | 场合适用性（日常休闲、职场商务等多选）、卡片描述 | 场景/场合标签、活动类型、场景化穿搭建议 | array     | ["日常","职场",...] | 适合场合 |
| materials           | 材质（AI识别/选择）、材质标签 | 材质列表、AI识别材质、材质与五行属性映射 | array     | ["棉","麻",...] | 材质 |
| styles              | 风格标签（休闲、商务、运动等多选）、风格分析 | 风格标签、风格定位、风格趋势分析、AI识别风格 | array     | ["休闲","商务",...] | 风格标签 |
| wuxing              | 五行属性（金/木/水/火/土，AI识别/选择）、分析卡片 | 五行属性、五行分布、五行能量分析、五行属性映射、五行调和建议 | object    | 见下表        | 五行属性 |
| mainImageUrl        | 主图（图片上传/预览/详情/卡片） | 图片上传、主图、AI识别图片、图片去背景 | string    | url           | 主图URL |
| imageUrls           | 图片上传/多图（添加/详情） | 多角度拍摄、图片素材、图片去背景、图片类型（主图/细节/模特） | array     | [url1, url2, ...] | 其他图片URL |
| brand               | 品牌（添加/详情） | 品牌、购买渠道、AI识别品牌 | string    |               | 品牌 |
| description         | 备注信息（添加/详情）、搭配建议、洗涤/收纳建议 | 备注、搭配建议、洗涤建议、收纳建议、AI生成优化建议 | string    |               | 描述 |
| purchaseDate        | 购买日期（添加/详情） | 购买日期、购买信息、购买渠道 | string    | yyyy-MM-dd    | 购买日期 |
| price               | 价格（添加/详情） | 价格、购买信息 | number    |               | 价格(元) |
| wearCount           | 穿着次数（分析页/详情） | 穿着频率、穿着次数统计、穿搭日记 | int       |               | 穿着次数 |
| lastWornAt          | 最后穿着时间（分析页/详情） | 最后穿着时间、穿着历史、穿搭日记 | string    | yyyy-MM-dd    | 最后穿着时间 |
| createdAt           | 添加时间（详情页） | 创建时间、上传时间 | string    | yyyy-MM-dd    | 创建时间 |
| updatedAt           | 无（可补充） | 更新时间 | string    | yyyy-MM-dd    | 更新时间 |
| type                | 饰品类型/穿着类型（如帽子/围巾/衣物/搭配） | 饰品类型、穿着类型、AI识别类型 | string    | 饰品类型/穿着类型 | 饰品/穿着记录 |
| subType             | 饰品子类型 | 饰品子类别、AI识别子类别 | string    |               | 饰品子类型 |
| clothingIds         | 推荐搭配包含衣物ID（分析/详情） | 搭配单品ID、搭配历史、AI生成搭配 | array     | [衣物ID, ...] | 搭配 |
| accessoryIds        | 推荐搭配包含饰品ID | 搭配饰品ID、搭配历史、AI生成搭配 | array     | [饰品ID, ...] | 搭配 |
| images              | 搭配效果图（分析/详情） | 搭配效果图、AI生成图片、图片类型 | array     | [url1, url2, ...] | 搭配效果图 |
| isUserCreated       | 无（可补充） | 用户自定义搭配、系统推荐标识 | bool      |               | 用户创建/系统推荐 |
| styleAnalysis       | 风格分析（分析页雷达图/分布数据/风格定位） | 风格分析、风格趋势、AI风格分析 | object    | {风格:占比,...} | 风格分析 |
| colorAnalysis       | 色彩分析（主色分布/多样性/推荐） | 颜色分析、颜色分布、AI色彩分析 | object    | {颜色:数量,...} | 色彩分析 |
| wuxingBalance       | 五行分析（分布/均衡性/建议/分析卡片） | 五行平衡分析、五行均衡度、五行优化建议 | object    | 见下表        | 五行平衡 |
| wearFrequency       | 穿着频率分析（高频/低频/未穿单品/分析卡片） | 穿着频率分析、穿着频率热力图、穿搭日记 | object    | {衣物ID:次数,...} | 穿着频率 |
| list                | 列表/历史记录（分析/详情/卡片） | 衣物/饰品/搭配列表、历史记录、批量导入 | array     |               | 列表/历史记录 |
| date                | 购买日期/穿着日期 | 购买日期、穿着日期、日历搭配、活动日期 | string    | yyyy-MM-dd    | 日期 |
| url                 | 图片URL（主图/多图/搭配图/卡片） | 图片URL、图片素材、AI生成图片 | string    | url           | 图片URL |

---

## 3. 复杂结构字段细化（示例）

### wuxing（五行属性）
| 字段/子字段 | 原型字段 | 需求字段 | 类型/结构 | 可选值/约束 | 备注 |
|-------------|----------|----------|-----------|-------------|------|
| metal       | 五行标签/分析卡片 | 金属性、五行分布、AI识别金 | int       | 0-5         | 金   |
| wood        | 五行标签/分析卡片 | 木属性、五行分布、AI识别木 | int       | 0-5         | 木   |
| water       | 五行标签/分析卡片 | 水属性、五行分布、AI识别水 | int       | 0-5         | 水   |
| fire        | 五行标签/分析卡片 | 火属性、五行分布、AI识别火 | int       | 0-5         | 火   |
| earth       | 五行标签/分析卡片 | 土属性、五行分布、AI识别土 | int       | 0-5         | 土   |

### styleAnalysis/colorAnalysis/wearFrequency 等
- 结构与可选值已结合需求文档补充，后续如有新需求可继续细化。

---

## 4. 衍生/推荐字段（如有）
- 推荐搭配、收纳建议、洗涤指南、优化建议、AI生成搭配、衣橱优化报告、闲置单品处理建议、成就徽章、衣橱里程碑等（分析页/详情页/AI生成/运营奖励）。

---

## 5. 结论与建议
- 当前已补全接口字段、原型字段与需求字段，三方对照完整，便于团队协作与复核。
- 建议接口、前端、产品、设计多方共同补充对照，确保无遗漏。
- 复杂结构建议单独细化所有子字段及可选值/约束，便于自动化校验与前后端对齐。

---

> 如需进一步细化或补充，请在本清单基础上补充说明。 