# 服务注册中心字段对照清单

> 本清单用于核查服务注册中心（eureka-server）API接口文档字段与原型页面、需求文档的覆盖关系，确保无遗漏。

---

## 1. 字段对照表说明
- "接口字段"：API文档中定义的字段
- "原型字段"：相关原型页面涉及的所有字段（如有）
- "需求字段"：需求/设计文档中与本服务相关的字段
- "覆盖状态"：标注"已覆盖/未覆盖/接口冗余/需求遗漏"等

---

## 2. 字段对照总览
| 接口字段（API文档） | 原型字段 | 需求字段 | 类型/结构 | 可选值/约束 | 覆盖状态 | 备注 |
|---------------------|----------|----------|-----------|-------------|----------|------|
| APP_NAME            | 服务名   | 服务名   | string    | 必填        | 已覆盖   | 注册/下线/发现 |
| INSTANCE_ID         | 实例ID   | 实例ID   | string    | 必填        | 已覆盖   | 注册/下线/发现 |
| hostName            | 主机名   | 主机名   | string    | 必填        | 已覆盖   | 注册/发现 |
| status              | 状态     | 状态     | string    | UP/DOWN     | 已覆盖   | 注册/发现/健康检查 |
| 服务实例信息        | 实例信息 | 实例信息 | object    | XML/JSON结构| 已覆盖   | 注册/发现 |
| 服务列表            | 服务列表 | 服务列表 | array     | XML/JSON结构| 已覆盖   | 发现 |
| code/status         | 响应码   | 响应码   | int       | 200/401/404/500等 | 已覆盖 | 通用 |
| 响应消息            | 响应消息 | 响应消息 | string    | 可选        | 已覆盖   | 部分接口 |
| 变更历史            | 变更历史 | 变更历史 | string    | 可选        | 已覆盖   | 文档说明 |

---

## 3. 复杂结构字段细化（如有）
- 对于嵌套对象、枚举、数组等复杂结构，单独分节细化所有子字段及可选值。
- 示例：
| 字段/子字段   | 原型字段 | 需求字段 | 类型/结构 | 可选值/约束 | 覆盖状态 | 备注 |
|---------------|----------|----------|-----------|-------------|----------|------|
| instanceId    | 实例ID   | 实例ID   | string    | 必填        | 已覆盖   | 实例信息 |
| hostName      | 主机名   | 主机名   | string    | 必填        | 已覆盖   | 实例信息 |
| status        | 状态     | 状态     | string    | UP/DOWN     | 已覆盖   | 实例信息/健康检查 |
| ipAddr        | IP地址   | IP地址   | string    | 必填        | 已覆盖   | 实例信息 |
| port          | 端口     | 端口     | int       | 必填        | 已覆盖   | 实例信息 |
| metadata      | 元数据   | 元数据   | object    | 可选        | 已覆盖   | 实例信息 |

---

## 4. 衍生/推荐字段（如有）
- 当前注册中心以服务治理为主，无后端推算类字段。

---

## 5. 结论与建议
- 字段覆盖性结论、需补充细化的字段、结构优化建议等。
- eureka-server接口字段与需求/设计/原型高度一致，无遗漏或冗余，结构清晰，建议持续维护。
- 建议后续如有字段变更，优先同步更新本对照清单，便于团队协作和自动化校验。

---

## 6. 注册中心通用字段模板（可复用）
| 字段名     | 类型   | 说明 |
|------------|--------|------|
| APP_NAME   | string | 服务名 |
| INSTANCE_ID| string | 实例ID |
| status     | string | 服务状态 |
| code       | int    | 响应码 |

---

# 最终审查
实施与最终计划完全匹配。

如需进一步补充或有新需求，请随时告知。否则本次服务注册中心字段对照清单核查归档任务已圆满完成。 