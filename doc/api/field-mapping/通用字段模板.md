# 通用字段模板

> 本模板抽象了多服务通用的激励、状态、异常、批量等字段，建议各服务接口文档、三方字段对照清单直接引用，提升一致性与复用性。

---

## 1. 激励相关字段
| 字段名         | 类型     | 可选值/约束         | 说明           |
| -------------- | -------- | ------------------- | -------------- |
| reward         | int/obj  | >=0                 | 激励数值/对象  |
| rewardType     | string   | 金币/积分/道具等    | 激励类型       |
| rewardStatus   | string   | 待领取/已领取/失效  | 激励状态       |
| rewardTime     | datetime |                     | 激励发放时间   |

## 2. 状态相关字段
| 字段名         | 类型     | 可选值/约束         | 说明           |
| -------------- | -------- | ------------------- | -------------- |
| status         | string   | 各业务自定义        | 业务状态       |
| isActive       | bool     | true/false          | 是否激活       |
| isDeleted      | bool     | true/false          | 是否已删除     |

## 3. 异常相关字段
| 字段名         | 类型     | 可选值/约束         | 说明           |
| -------------- | -------- | ------------------- | -------------- |
| errorCode      | string   | 见错误码表          | 错误码         |
| errorMessage   | string   |                     | 错误描述       |
| exception      | string   |                     | 异常信息       |

## 4. 批量操作相关字段
| 字段名         | 类型     | 可选值/约束         | 说明           |
| -------------- | -------- | ------------------- | -------------- |
| ids            | array    |                     | 批量ID列表     |
| batchResult    | array    |                     | 批量操作结果   |
| totalCount     | int      | >=0                 | 总数量         |
| successCount   | int      | >=0                 | 成功数量       |
| failCount      | int      | >=0                 | 失败数量       |

---

> 如有其他通用字段需求，可在本模板基础上补充完善。 