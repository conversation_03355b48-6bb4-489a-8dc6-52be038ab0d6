# 三方字段对照清单标准模板

> 本模板用于规范各服务API接口字段与原型、需求文档的三方对照，提升字段追溯、变更管理与团队协作效率。

---

## 1. 字段对照表说明
- "接口字段"：API文档中定义的字段
- "原型字段"：相关原型页面涉及的所有字段
- "需求字段"：需求/设计文档中与本服务相关的字段
- "类型/结构"：字段类型、结构说明
- "可选值/约束"：字段可选值、取值范围、约束条件
- "覆盖状态"：已覆盖/未覆盖/接口冗余/需求遗漏等
- "字段分组/标签"：如"必填"、"推荐"、"自动生成"、"前端专用"、"后端专用"等
- "字段来源追溯"：首次引入的文档及位置
- "变更历史"：主要变更节点（如新增、修改、废弃等）
- "备注"：其他补充说明

---

## 2. 字段对照总览
| 接口字段 | 原型字段 | 需求字段 | 类型/结构 | 可选值/约束 | 覆盖状态 | 字段分组/标签 | 字段来源追溯 | 变更历史 | 备注 |
|----------|----------|----------|-----------|-------------|----------|---------------|--------------|----------|------|
|          |          |          |           |             |          |               |              |          |      |

---

## 3. 复杂结构字段细化（如有）

- 对于嵌套对象、枚举、数组等复杂结构，统一采用如下表格模板，细化所有主字段、子字段及可选值、约束、标签、来源、变更历史等信息。

| 主字段/子字段 | 原型字段 | 需求字段 | 类型/结构 | 可选值/约束 | 覆盖状态 | 字段分组/标签 | 字段来源追溯 | 变更历史 | 备注 |
|---------------|----------|----------|-----------|-------------|----------|---------------|--------------|----------|------|
| outfits       |          |          | array     |             | 已覆盖   | 必填          | 设计v2.0     | 新增     | 搭配列表 |
| outfits.id    |          |          | string    |             | 已覆盖   | 必填          | 设计v2.0     | 新增     | 搭配ID |
| outfits.items |          |          | array     |             | 已覆盖   | 必填          | 设计v2.0     | 新增     | 搭配明细 |
| outfits.items.productId | |         | string    |             | 已覆盖   | 必填          | 设计v2.0     | 新增     | 商品ID |
| outfits.items.color     | |         | string    | 见色彩枚举  | 已覆盖   | 推荐          | 设计v2.0     | 新增     | 颜色 |
| ...           |          |          |           |             |          |               |              |          |      |

- 枚举类型建议在"可选值/约束"列中详细列出所有可选值。
- 数组类型建议明确每个元素的结构。
- 嵌套对象建议逐层细化所有子字段，保持结构清晰。

---

## 4. 衍生/推荐字段（如有）
- 记录后端推算、自动生成、推荐类字段。

---

## 5. 结论与建议
- 字段覆盖性结论、需补充细化的字段、结构优化建议等。

---

> 建议各服务对照清单直接复用本模板，保持结构一致，便于自动化校验与团队协作。

> 所有服务涉及复杂结构字段时，均应采用本模板，便于自动化校验与团队协作。 