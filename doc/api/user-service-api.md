# 用户服务（user-service）API接口文档

## 1. 概述
用户服务负责用户注册、登录、档案管理、五行命理、等级特权、用户设置等核心功能。

## 2. 认证与通用说明
- 认证方式：JWT（登录成功后在响应header中返回，后续接口需在请求header中携带Authorization: Bearer {token}）
- 通用请求头：
  - Content-Type: application/json
  - Authorization: Bearer {token} （需要认证的接口）
- 通用响应结构：
    ```json
    {
      "code": 0,
      "msg": "success",
      "data": { ... }
    }
    ```
- 错误码说明：
    | code | 含义         | 说明           |
    |------|--------------|----------------|
    | 0    | 成功         |                |
    | 1001 | 参数错误     |                |
    | 1002 | 未认证/Token无效 |           |
    | 1003 | 权限不足     |                |
    | 2001 | 用户不存在   |                |
    | 2002 | 密码错误     |                |
    | 2003 | 账号已存在   |                |
    | 2004 | 手机号已绑定 |                |
    | 2005 | 验证码错误   |                |
    | 2006 | 验证码已过期 |                |
    | 2007 | 账号已禁用   |                |

### bodyShape 字段结构与选项说明

- bodyShape 字段为体型细分信息，类型为 JSON 对象，包含如下子字段及可选值：

| 子字段         | 说明     | 可选值（建议枚举）                   |
|----------------|----------|--------------------------------------|
| shoulderWidth  | 肩膀宽度 | 窄、偏窄、正常、偏宽、宽             |
| waistShape     | 腰型     | 直筒、略有曲线、有曲线、曲线较明显、曲线明显 |
| belly          | 肚腩     | 没有、略有小肚腩、小肚腩、偏大肚腩、大肚腩 |
| hip            | 臀型     | 下榻、略有上翘、正常、较上翘、上翘   |
| hipShape       | 胯型     | 直筒、略有曲线、有曲线、曲线较明显、曲线明显 |
| armLength      | 臂长     | 短、偏短、正常、偏长、长             |
| armCircum      | 臂围     | 细、偏细、正常、偏粗、粗             |
| hipWidth       | 胯部     | 窄、偏窄、正常、偏宽、宽             |
| thigh          | 大腿     | 细、偏细、正常、偏粗、粗             |
| calf           | 小腿     | 细、偏细、正常、偏粗、粗             |
| bodyFat        | 上下身粗 | 上身粗、偏上身粗、匀称、偏下身粗、下身粗 |
| bodyLength     | 上下身长 | 上身长、偏上身长、匀称、偏下身长、下身长 |

- 示例：
    ```json
    {
      "shoulderWidth": "正常",
      "waistShape": "直筒",
      "belly": "没有",
      "hip": "下榻",
      "hipShape": "直筒",
      "armLength": "正常",
      "armCircum": "正常",
      "hipWidth": "正常",
      "thigh": "正常",
      "calf": "正常",
      "bodyFat": "匀称",
      "bodyLength": "匀称"
    }
    ```

---

## 3. 接口列表

### 3.1 发送验证码

- **接口路径**：`/api/user/send-code`
- **请求方法**：POST
- **功能描述**：发送手机验证码（登录用）
- **请求参数**：
    | 参数名 | 类型   | 必填 | 说明   |
    |--------|--------|------|--------|
    | phone  | string | 是   | 手机号 |
- **请求示例**：
    ```json
    {
      "phone": "13800138000"
    }
    ```
- **响应参数**：
    | 字段名    | 类型   | 说明             |
    |-----------|--------|------------------|
    | codeId    | string | 验证码标识       |
    | expiresIn | int    | 有效期（秒）     |
- **响应示例**：
    ```json
    {
      "code": 0,
      "msg": "success",
      "data": {
        "codeId": "code_123456",
        "expiresIn": 300
      }
    }
    ```
- **备注**：
    - 同一手机号60秒内只能发送一次验证码
    - 验证码5分钟内有效
    - 开发环境固定验证码为：123456

---

### 3.2 用户登录

- **接口路径**：`/api/user/login`
- **请求方法**：POST
- **功能描述**：验证码登录（自动注册新用户）
- **请求参数**：
    | 参数名 | 类型   | 必填 | 说明         |
    |--------|--------|------|--------------|
    | phone  | string | 是   | 手机号       |
    | code   | string | 是   | 验证码       |
    | codeId | string | 是   | 验证码标识   |
- **请求示例**：
    ```json
    {
      "phone": "13800138000",
      "code": "123456",
      "codeId": "code_123456"
    }
    ```
- **响应参数**：
    | 字段名        | 类型   | 说明                     |
    |---------------|--------|--------------------------|
    | userId        | string | 用户ID                   |
    | userInfo      | object | 用户基础信息             |
    | token         | string | JWT访问令牌              |
    | expiresIn     | int    | token过期时间（秒）      |
    | hasBasicInfo  | bool   | 是否已完善基础信息       |
    | hasWuxingInfo | bool   | 是否已完善八字信息       |
    | isNewUser     | bool   | 是否为新注册用户         |
- **响应示例**：
    ```json
    {
      "code": 0,
      "msg": "success",
      "data": {
        "userId": "u123456",
        "userInfo": {
          "userId": "u123456",
          "nickname": "用户_138000",
          "phone": "13800138000",
          "avatar": "",
          "gender": null
        },
        "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        "expiresIn": 7200,
        "hasBasicInfo": false,
        "hasWuxingInfo": false,
        "isNewUser": true
      }
    }
    ```
- **备注**：
    - 后端自动判断用户是否存在，不存在则自动注册
    - 新用户默认昵称为"用户_手机号后6位"
    - 根据hasBasicInfo和hasWuxingInfo判断是否需要跳转完善信息页面

---

### 3.3 获取用户档案

- **接口路径**：`/api/user/profile`
- **请求方法**：GET
- **功能描述**：获取当前用户档案信息
- **请求参数**：无（需携带token）
- **响应参数**：
    | 字段名   | 类型   | 说明         |
    |----------|--------|--------------|
    | userId   | string | 用户ID       |
    | nickname | string | 昵称         |
    | avatar   | string | 头像         |
    | gender   | int    | 性别         |
    | phone    | string | 手机号       |
    | photoUrl | string | 全身照URL    |
    | height   | int    | 身高(cm)     |
    | weight   | int    | 体重(kg)     |
    | bodyType | string | 体型         |
    | skinTone | string | 肤色         |
    | stylePreferences | array | 风格偏好 |
    | bodyShape| object | 体型细分     |
    | mode     | string | 用户模式     |
    | fullName | string | 姓名         |
    | birthDate| string | 出生日期     |
    | birthTime| string | 出生时间     |
    | birthPlace| string| 出生地点     |
    | createTime| string| 创建时间     |
    | updateTime| string| 更新时间     |
- **响应示例**：
    ```json
    {
      "code": 0,
      "msg": "success",
      "data": {
        "userId": "u123456",
        "nickname": "小明",
        "avatar": "https://xxx.com/avatar.jpg",
        "gender": 2,
        "phone": "13800138000",
        "photoUrl": "https://xxx.com/photo.jpg",
        "height": 165,
        "weight": 52,
        "bodyType": "normal",
        "skinTone": "fair",
        "stylePreferences": ["casual", "elegant"],
        "bodyShape": {"shoulderWidth":"正常","waistShape":"直筒",...},
        "mode": "energy",
        "fullName": "张三",
        "birthDate": "1995-05-01",
        "birthTime": "08:30",
        "birthPlace": "上海",
        "createTime": "2025-05-12T10:00:00Z",
        "updateTime": "2025-05-12T10:00:00Z"
      }
    }
    ```

---

### 3.4 完善用户信息

- **接口路径**：`/api/user/complete-profile`
- **请求方法**：PUT
- **功能描述**：完善/更新用户档案信息（原注册接口功能）
- **请求参数**：
    | 参数名     | 类型   | 必填 | 说明         |
    |------------|--------|------|--------------|
    | nickname   | string | 否   | 昵称         |
    | avatar     | string | 否   | 头像URL      |
    | gender     | int    | 否   | 性别（0未知/1男/2女）|
    | photoUrl   | string | 否   | 全身照URL    |
    | height     | int    | 否   | 身高(cm)     |
    | weight     | int    | 否   | 体重(kg)     |
    | bodyType   | string | 否   | 体型（标准/偏瘦/偏胖）|
    | skinTone   | string | 否   | 肤色         |
    | stylePreferences | array | 否 | 风格偏好（多选）|
    | bodyShape  | object | 否   | 体型细分（JSON对象，详见bodyShape结构）|
    | mode       | string | 否   | 用户模式（natural/energy）|
    | fullName   | string | 否   | 姓名（能量信息）|
    | birthDate  | string | 否   | 出生日期      |
    | birthTime  | string | 否   | 出生时间      |
    | birthPlace | string | 否   | 出生地点      |
- **请求示例**：
    ```json
    {
      "nickname": "小明",
      "gender": 2,
      "avatar": "https://xxx.com/avatar.jpg",
      "photoUrl": "https://xxx.com/photo.jpg",
      "height": 165,
      "weight": 52,
      "bodyType": "normal",
      "skinTone": "fair",
      "stylePreferences": ["casual", "elegant"],
      "bodyShape": {"shoulderWidth":"正常","waistShape":"直筒"},
      "mode": "energy",
      "fullName": "张三",
      "birthDate": "1995-05-01",
      "birthTime": "08:30",
      "birthPlace": "上海"
    }
    ```
- **响应参数**：
    | 字段名        | 类型   | 说明                     |
    |---------------|--------|--------------------------|
    | userInfo      | object | 更新后的用户信息         |
    | hasBasicInfo  | bool   | 是否已完善基础信息       |
    | hasWuxingInfo | bool   | 是否已完善八字信息       |
- **响应示例**：
    ```json
    {
      "code": 0,
      "msg": "success",
      "data": {
        "userInfo": {
          "userId": "u123456",
          "nickname": "小明",
          "phone": "13800138000",
          "gender": 2,
          "height": 165,
          "weight": 52,
          "...": "..."
        },
        "hasBasicInfo": true,
        "hasWuxingInfo": false
      }
    }
    ```

---

### 3.5 更新用户档案

- **接口路径**：`/api/user/profile`
- **请求方法**：PUT
- **功能描述**：更新当前用户档案信息（日常更新用）
- **请求参数**：同3.4所有字段均可选
- **请求示例**：
    ```json
    {
      "nickname": "小明2",
      "avatar": "https://xxx.com/avatar2.jpg",
      "height": 170,
      "weight": 55,
      "bodyType": "chubby",
      "skinTone": "medium",
      "stylePreferences": ["casual"],
      "bodyShape": {"shoulderWidth":"偏宽","waistShape":"曲线明显"}
    }
    ```
- **响应参数**：同3.4

---

### 3.6 获取/更新五行命理档案

- **接口路径**：`/api/user/wuxing`
- **请求方法**：GET/PUT
- **功能描述**：获取/更新用户五行命理信息
- **请求参数**（PUT）：
    | 参数名     | 类型   | 必填 | 说明         |
    |------------|--------|------|--------------|
    | birthTime  | string | 是   | 出生时间     |
    | birthPlace | string | 否   | 出生地点     |
    | fullName   | string | 否   | 姓名         |
    | gender     | int    | 否   | 性别         |
    | birthDate  | string | 否   | 出生日期     |
- **响应参数**：
    | 字段名           | 类型   | 说明         |
    |------------------|--------|--------------|
    | wuxingDistribution | object | 五行分布   |
    | favorable        | string | 喜用神       |
    | unfavorable      | string | 忌神         |
    | baZi             | object | 八字信息     |
    | ...              | ...    | ...          |
- **响应示例**：
    ```json
    {
      "code": 0,
      "msg": "success",
      "data": {
        "wuxingDistribution": {
          "metal": 2,
          "wood": 1,
          "water": 3,
          "fire": 1,
          "earth": 1
        },
        "favorable": "水",
        "unfavorable": "火",
        "baZi": {
          "year": "甲子",
          "month": "乙丑",
          "day": "丙寅",
          "hour": "丁卯"
        }
      }
    }
    ```

---

### 3.7 八字计算

- **接口路径**：`/api/user/bazi`
- **请求方法**：POST
- **功能描述**：根据出生信息计算八字，返回基础八字信息和详细的天干地支五行属性
- **请求参数**：
    | 参数名     | 类型   | 必填 | 说明                    |
    |------------|--------|------|-------------------------|
    | fullName   | string | 否   | 姓名                    |
    | birthDate  | string | 是   | 出生日期 (YYYY-MM-DD)   |
    | birthTime  | string | 是   | 出生时间 (HH:mm)        |
    | birthPlace | string | 否   | 出生地点                |
    | gender     | int    | 否   | 性别 (1:男, 2:女)       |
- **请求示例**：
    ```json
    {
      "fullName": "张三",
      "birthDate": "1995-05-01",
      "birthTime": "08:30",
      "birthPlace": "上海",
      "gender": 1
    }
    ```
- **响应参数**：
    | 字段名              | 类型   | 说明                    |
    |---------------------|--------|-------------------------|
    | yearPillar          | string | 年柱 (如"甲子")         |
    | monthPillar         | string | 月柱 (如"乙丑")         |
    | dayPillar           | string | 日柱 (如"丙寅")         |
    | hourPillar          | string | 时柱 (如"丁卯")         |
    | dayMaster           | string | 日主 (如"甲")           |
    | elements            | array  | 五行元素列表            |
    | **detailInfo**      | object | **详细信息（新增）**    |
- **详细信息对象 (detailInfo)**：
    | 字段名              | 类型   | 说明                    |
    |---------------------|--------|-------------------------|
    | yearPillarDetail    | object | 年柱详细信息            |
    | monthPillarDetail   | object | 月柱详细信息            |
    | dayPillarDetail     | object | 日柱详细信息            |
    | hourPillarDetail    | object | 时柱详细信息            |
    | wuxingStatistics    | object | 五行统计信息            |
- **柱详细信息对象**：
    | 字段名              | 类型   | 说明                    |
    |---------------------|--------|-------------------------|
    | heavenlyStem        | object | 天干信息                |
    | earthlyBranch       | object | 地支信息                |
- **天干信息**：
    | 字段名         | 类型   | 说明                    |
    |----------------|--------|-------------------------|
    | character      | string | 天干字符 (如"甲")       |
    | element        | string | 五行属性 (金木水火土)   |
- **地支信息**：
    | 字段名         | 类型   | 说明                    |
    |----------------|--------|-------------------------|
    | character      | string | 地支字符 (如"子")       |
    | element        | string | 五行属性 (金木水火土)   |
    | hiddenStems    | array  | 藏干信息列表            |
- **藏干信息**：
    | 字段名         | 类型   | 说明                        |
    |----------------|--------|-----------------------------|
    | character      | string | 藏干字符 (如"癸")           |
    | element        | string | 五行属性 (金木水火土)       |
    | strength       | string | 强度 (主气/中气/余气)       |
- **五行统计信息 (wuxingStatistics)**：
    | 字段名              | 类型   | 说明                    |
    |---------------------|--------|-------------------------|
    | distribution        | object | 五行分布统计            |
    | dominantElement     | string | 主导五行                |
    | weakestElement      | string | 最弱五行                |
    | totalCount          | int    | 总计数（含藏干）        |
- **响应示例**：
    ```json
    {
      "code": 0,
      "msg": "success",
      "data": {
        "yearPillar": "甲子",
        "monthPillar": "乙丑", 
        "dayPillar": "丙寅",
        "hourPillar": "丁卯",
        "dayMaster": "丙",
        "elements": ["木", "水", "土", "火"],
        "detailInfo": {
          "yearPillarDetail": {
            "heavenlyStem": {
              "character": "甲",
              "element": "木"
            },
            "earthlyBranch": {
              "character": "子",
              "element": "水",
              "hiddenStems": [
                {
                  "character": "癸",
                  "element": "水",
                  "strength": "主气"
                }
              ]
            }
          },
          "monthPillarDetail": {
            "heavenlyStem": {
              "character": "乙",
              "element": "木"
            },
            "earthlyBranch": {
              "character": "丑",
              "element": "土",
              "hiddenStems": [
                {
                  "character": "己",
                  "element": "土",
                  "strength": "主气"
                },
                {
                  "character": "癸",
                  "element": "水",
                  "strength": "中气"
                },
                {
                  "character": "辛",
                  "element": "金",
                  "strength": "余气"
                }
              ]
            }
          },
          "dayPillarDetail": {
            "heavenlyStem": {
              "character": "丙",
              "element": "火"
            },
            "earthlyBranch": {
              "character": "寅",
              "element": "木",
              "hiddenStems": [
                {
                  "character": "甲",
                  "element": "木",
                  "strength": "主气"
                },
                {
                  "character": "丙",
                  "element": "火",
                  "strength": "中气"
                },
                {
                  "character": "戊",
                  "element": "土",
                  "strength": "余气"
                }
              ]
            }
          },
          "hourPillarDetail": {
            "heavenlyStem": {
              "character": "丁",
              "element": "火"
            },
            "earthlyBranch": {
              "character": "卯",
              "element": "木",
              "hiddenStems": [
                {
                  "character": "乙",
                  "element": "木",
                  "strength": "主气"
                }
              ]
            }
          },
          "wuxingStatistics": {
            "distribution": {
              "金": 1,
              "木": 5, 
              "水": 2,
              "火": 3,
              "土": 2
            },
            "dominantElement": "木",
            "weakestElement": "金",
            "totalCount": 13
          }
        }
      }
    }
    ```
- **备注**：
    - 接口保持向后兼容，原有字段格式不变
    - 新增的 `detailInfo` 字段包含天干、地支、藏干的详细五行信息
    - 天干、地支、藏干的五行属性基于传统命理学对照表查询
    - `wuxingStatistics` 统计所有天干、地支、藏干的五行分布
    - AI服务提供基础八字计算，本地枚举提供五行属性查询

---

### 3.8 刷新JWT Token

- **接口路径**：`/api/user/refresh-token`
- **请求方法**：POST
- **功能描述**：使用refresh token换取新的access token
- **请求参数**：
    | 参数名      | 类型   | 必填 | 说明               |
    |-------------|--------|------|--------------------|
    | refreshToken| string | 是   | 刷新令牌           |
- **请求示例**：
    ```json
    {
      "refreshToken": "refresh_eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
    }
    ```
- **响应参数**：
    | 字段名      | 类型   | 说明                     |
    |-------------|--------|--------------------------|
    | token       | string | 新的JWT访问令牌          |
    | expiresIn   | int    | token过期时间（秒）      |
    | refreshToken| string | 新的刷新令牌（可选）     |
- **响应示例**：
    ```json
    {
      "code": 0,
      "msg": "success",
      "data": {
        "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        "expiresIn": 7200,
        "refreshToken": "refresh_eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
      }
    }
    ```
- **备注**：
    - refresh token有效期通常为7-30天
    - 使用refresh token后，旧的refresh token失效（可选策略）
    - 如果refresh token过期或无效，返回1002错误，需要重新登录

---

### 3.9 用户退出登录

- **接口路径**：`/api/user/logout`
- **请求方法**：POST
- **功能描述**：用户退出登录，清除服务器端认证信息
- **请求参数**：无（需携带token）
- **请求示例**：
    ```json
    {}
    ```
- **响应参数**：
    | 字段名  | 类型   | 说明     |
    |---------|--------|----------|
    | message | string | 退出消息 |
- **响应示例**：
    ```json
    {
      "code": 0,
      "msg": "success",
      "data": {
        "message": "退出登录成功"
      }
    }
    ```
- **备注**：
    - 接口会清除服务器端的认证信息（token加入黑名单或删除认证记录）
    - 客户端需要同时清除本地存储的token和用户信息
    - 退出后token立即失效，无法再用于访问需要认证的接口

---

### 3.11 获取能量历史趋势

- **接口路径**：`/api/user/energy-trend`
- **请求方法**：GET
- **功能描述**：获取用户能量变化趋势数据
- **请求参数**：
    | 参数名    | 类型   | 必填 | 说明                        |
    |-----------|--------|------|-----------------------------|
    | period    | string | 否   | 时间周期：week/month，默认week |
    | startDate | string | 否   | 开始日期，格式YYYY-MM-DD    |
    | endDate   | string | 否   | 结束日期，格式YYYY-MM-DD    |
- **响应参数**：
    | 字段名      | 类型   | 说明                 |
    |-------------|--------|---------------------|
    | period      | string | 查询的时间周期       |
    | dateRange   | object | 查询的日期范围       |
    | trendData   | array  | 趋势数据点           |
    | avgScore    | number | 平均能量分数         |
    | maxScore    | number | 最高能量分数         |
    | minScore    | number | 最低能量分数         |
    | bestDimension | object | 表现最佳的维度     |
- **响应示例**：
    ```json
    {
      "code": 0,
      "msg": "success", 
      "data": {
        "period": "week",
        "dateRange": {
          "startDate": "2023-11-14",
          "endDate": "2023-11-20"
        },
        "trendData": [
          {
            "date": "2023-11-14",
            "totalScore": 75,
            "dimensions": {
              "love": 70,
              "career": 85,
              "wealth": 68,
              "health": 78,
              "relationship": 74
            }
          }
        ],
        "avgScore": 76,
        "maxScore": 85,
        "minScore": 65,
        "bestDimension": {
          "key": "career",
          "label": "事业运势",
          "avgScore": 88
        }
      }
    }
    ```

---

### 3.12 获取今日能量简要信息

- **接口路径**：`/api/user/today-energy-brief`
- **请求方法**：GET
- **功能描述**：获取用户当日的简化能量信息（仅能量模式用户可用），适用于首页展示或小卡片场景
- **请求参数**：
    | 参数名 | 类型   | 必填 | 说明               |
    |--------|--------|------|--------------------|
    | date   | string | 否   | 指定日期，格式YYYY-MM-DD，默认今日 |
- **请求示例**：
    ```
    GET /api/user/today-energy-brief?date=2023-11-20
    ```
- **响应参数**：
    | 字段名           | 类型   | 说明                   |
    |------------------|--------|------------------------|
    | dateInfo         | object | 日期信息               |
    | dateInfo.gregorian | string | 公历日期             |
    | dateInfo.lunar   | string | 农历信息               |
    | totalScore       | number | 今日总能量分数(0-100)  |
    | percentage       | number | 超过的用户百分比       |
    | luckyElements    | object | 幸运元素简要           |
    | luckyElements.clothingSummary | string | 服饰建议总结     |
    | luckyElements.accessoriesSummary | string | 配饰建议总结 |
    | luckyElements.makeupSummary | string | 妆容建议总结     |
- **响应示例**：
    ```json
    {
      "code": 0,
      "msg": "success",
      "data": {
        "dateInfo": {
          "gregorian": "2023年11月20日",
          "lunar": "二月十九 己巳卯 丙戌 水行"
        },
        "totalScore": 78,
        "percentage": 82,
        "luckyElements": {
          "clothingSummary": "今日宜选择丝质面料，上装推荐衬衫或针织款，柔软质感有助提升运势。",
          "accessoriesSummary": "银色系首饰为佳，简约水滴造型设计能增强能量流动。",
          "makeupSummary": "清透水润妆感最适宜，眼部可点缀淡雅蓝紫色调。"
        }
      }
    }
    ```
- **错误码说明**：
    | code | 含义                 |
    |------|---------------------|
    | 3001 | 用户未开启能量模式   |
    | 3002 | 八字信息不完整       |
    | 3003 | 能量计算服务异常     |
- **备注**：
    - 只有开启能量模式且完善八字信息的用户可使用此接口
    - 数据来源于已保存的今日能量信息，进行简化和总结处理
    - 幸运元素的三个总结字段由后端基于详细数据进行语义压缩生成
    - 适用于首页能量卡片、消息推送等需要简洁展示的场景
    - 支持查询历史日期的简要能量信息（最多查询过去30天）

---

### 3.13 获取天气信息

- **接口路径**：`/api/user/weather`
- **请求方法**：GET
- **功能描述**：根据城市名称或经纬度获取天气信息，同时更新用户居住地区信息
- **请求参数**：
    | 参数名    | 类型   | 必填 | 说明                        |
    |-----------|--------|------|-----------------------------|
    | city      | string | 否   | 城市名称（如：北京、上海）   |
    | longitude | double | 否   | 经度                        |
    | latitude  | double | 否   | 纬度                        |
- **请求示例**：
    ```
    GET /api/user/weather?city=北京
    GET /api/user/weather?longitude=116.4074&latitude=39.9042
    ```
- **响应参数**：
    | 字段名       | 类型   | 说明                   |
    |--------------|--------|------------------------|
    | location     | object | 位置信息               |
    | current      | object | 当前天气（实况天气）   |
    | forecast     | array  | 预报天气列表（未来几天）|
    | lastUpdate   | string | 数据更新时间           |
    | dataSource   | string | 数据来源               |
    | timestamp    | long   | 查询时间戳             |
    | success      | bool   | 是否成功               |
    | errorMessage | string | 错误信息（如果有）     |
- **位置信息对象（location）**：
    | 字段名         | 类型   | 说明         |
    |----------------|--------|--------------|
    | id             | string | 位置ID       |
    | name           | string | 城市名称     |
    | country        | string | 国家代码     |
    | path           | string | 完整路径     |
    | timezone       | string | 时区         |
    | timezoneOffset | string | 时区偏移     |
    | province       | string | 省份         |
    | adcode         | string | 区域编码     |
- **天气数据对象（current/forecast 数组元素）**：
    | 字段名             | 类型   | 说明                     |
    |--------------------|--------|--------------------------|
    | text               | string | 天气现象描述             |
    | code               | string | 天气现象代码             |
    | temperature        | string | 温度（摄氏度）           |
    | highTemperature    | string | 最高温度                 |
    | lowTemperature     | string | 最低温度                 |
    | windDirection      | string | 风向                     |
    | windPower          | string | 风力等级                 |
    | windSpeed          | string | 风速                     |
    | humidity           | string | 湿度（百分比）           |
    | pressure           | string | 气压                     |
    | visibility         | string | 能见度                   |
    | uvIndex            | string | 紫外线指数               |
    | precipitation      | string | 降水量                   |
    | date               | string | 日期（用于预报天气）     |
    | week               | string | 星期                     |
    | dayWeather         | string | 白天天气（用于预报）     |
    | nightWeather       | string | 夜间天气（用于预报）     |
    | dayTemperature     | string | 白天温度                 |
    | nightTemperature   | string | 夜间温度                 |
- **响应示例**：
    ```json
    {
      "code": 0,
      "msg": "success",
      "data": {
        "location": {
          "id": "WX4FBXXFKE4F",
          "name": "北京",
          "country": "CN",
          "path": "北京,北京,北京,中国",
          "timezone": "Asia/Shanghai",
          "timezoneOffset": "+08:00",
          "province": "北京",
          "adcode": "110000"
        },
        "current": {
          "text": "晴",
          "code": "0",
          "temperature": "15",
          "windDirection": "北",
          "windPower": "3",
          "windSpeed": "12",
          "humidity": "45",
          "pressure": "1020",
          "visibility": "10",
          "uvIndex": "5"
        },
        "forecast": [
          {
            "date": "2023-11-20",
            "week": "1",
            "dayWeather": "晴",
            "nightWeather": "晴",
            "highTemperature": "18",
            "lowTemperature": "5",
            "windDirection": "北",
            "windPower": "3-4"
          }
        ],
        "lastUpdate": "2023-11-20T14:30:00+08:00",
        "dataSource": "心知天气",
        "timestamp": 1700462400000,
        "success": true
      }
    }
    ```
- **错误码说明**：
    | code | 含义                 |
    |------|---------------------|
    | 5000 | 天气服务暂时不可用   |
    | 5001 | 天气查询失败         |
- **备注**：
    - 至少需要提供城市名称或经纬度信息之一
    - 优先使用城市名称查询，如果未提供则使用经纬度
    - 查询成功后会自动更新用户的居住地区信息（residence_name和residence_code字段）
    - 支持心知天气和高德天气等多个数据源，自动选择可用的数据源
    - 数据源不可用时会自动切换到备用数据源
    - **当前接入的是免费版天气接口**，主要提供基础的天气现象和温度数据，其他字段（如湿度、气压、风速、紫外线指数等）可能为空或无数据

---

### 3.14 用户设置、等级、特权、模式切换等接口

- **接口路径**：`/api/user/settings`、`/api/user/level`、`/api/user/privilege`、`/api/user/mode`
- **请求方法**：GET/PUT/POST
- **功能描述**：获取/更新用户设置、等级、特权、切换/获取用户模式
- **请求参数/响应参数**：
    - 用户设置：通知偏好、隐私设置、个性化推荐开关等
    - 等级：当前等级、成长值、升级所需、历史等级记录等
    - 特权：特权列表、特权使用记录等
    - 模式切换：mode（natural/energy），自动判定或手动切换

---

## 4. 典型业务流程示例

### 4.1 新用户流程
1. 发送验证码 (`/api/user/send-code`)
2. 验证码登录 (`/api/user/login`) - 后端自动注册新用户
3. 根据 `hasBasicInfo` 判断是否需要完善基础信息
4. 完善用户信息 (`/api/user/complete-profile`)
5. 根据 `hasWuxingInfo` 判断是否需要完善八字信息
6. 更新五行命理 (`/api/user/wuxing`)

### 4.2 老用户流程
1. 发送验证码 (`/api/user/send-code`)
2. 验证码登录 (`/api/user/login`) - 返回已有用户信息
3. 根据返回状态判断是否需要补充信息
4. 进入首页或相应功能页面

### 4.3 信息更新流程
- 日常信息更新 (`/api/user/profile`)
- 完善信息更新 (`/api/user/complete-profile`)
- 五行信息更新 (`/api/user/wuxing`)
- 八字计算 (`/api/user/bazi`)

---

## 5. 今日能量、完整运势解读相关接口


### 5.1 获取今日能量信息

- **接口路径**：`/api/user/today-energy`
- **请求方法**：GET
- **功能描述**：获取用户当日的能量解读信息（仅能量模式用户可用）
- **请求参数**：
    | 参数名 | 类型   | 必填 | 说明               |
    |--------|--------|------|--------------------|
    | date   | string | 否   | 指定日期，格式YYYY-MM-DD，默认今日 |
- **请求示例**：
    ```
    GET /api/user/today-energy?date=2023-11-20
    ```
- **响应参数**：
    | 字段名           | 类型   | 说明                   |
    |------------------|--------|------------------------|
    | dateInfo         | object | 日期信息               |
    | dateInfo.gregorian | string | 公历日期             |
    | dateInfo.lunar   | string | 农历信息               |
    | totalScore       | number | 今日总能量分数(0-100)  |
    | percentage       | number | 超过的用户百分比       |
    | peakTime         | string | 能量高峰时段           |
    | peakTimeDescription | string | 高峰时段描述        |
    | description      | string | 今日能量总体描述       |
    | dimensions       | object | 五维能量评分           |
    | dimensions.love  | number | 爱情运势(0-100)        |
    | dimensions.career | number | 事业运势(0-100)       |
    | dimensions.wealth | number | 财富运势(0-100)       |
    | dimensions.health | number | 健康运势(0-100)       |
    | dimensions.relationship | number | 人际运势(0-100) |
    | advice           | object | 宜忌指南               |
    | advice.categories | array | 宜忌分类列表          |
    | advice.lifeSuggestions | array | 生活建议列表       |
    | luckyElements    | object | 幸运元素               |
    | luckyElements.colors | array | 幸运色列表           |
    | luckyElements.clothing | array | 服饰建议列表       |
    | luckyElements.accessories | array | 配饰建议列表     |
    | luckyElements.makeup | array | 妆容建议列表         |
- **响应示例**：
    ```json
    {
      "code": 0,
      "msg": "success",
      "data": {
        "dateInfo": {
          "gregorian": "2023年11月20日",
          "lunar": "二月十九 己巳卯 丙戌 水行"
        },
        "totalScore": 78,
        "percentage": 82,
        "peakTime": "上午8-10点",
        "peakTimeDescription": "适合重要会议和决策，思维活跃度高。",
        "description": "今日五行能量以金主导，水行偏弱，整体能量场偏向稳定。",
        "dimensions": {
          "love": 65,
          "career": 88,
          "wealth": 72,
          "health": 81,
          "relationship": 76
        },
        "advice": {
          "categories": [
            {
              "type": "suitable",
              "label": "宜做事项",
              "items": [
                {
                  "id": "meeting",
                  "icon": "icon-huiyi",
                  "text": "重要会议"
                },
                {
                  "id": "decision",
                  "icon": "icon-juece",
                  "text": "重大决策"
                }
              ]
            },
            {
              "type": "avoid",
              "label": "忌做事项",
              "items": [
                {
                  "id": "argument",
                  "icon": "icon-zhengchao",
                  "text": "争吵冲突"
                }
              ]
            }
          ],
          "lifeSuggestions": [
            {
              "icon": "≋",
              "content": "今天适合进行深度思考和规划。"
            }
          ]
        },
        "luckyElements": {
          "colors": ["#B3D8E8", "#E8F5E8", "#333333"],
          "clothing": [
            "推荐丝绸、雪纺类柔软面料。",
            "上装可选丝质衬衫或针织款。"
          ],
          "accessories": [
            "首饰宜选银色、白金系。",
            "建议佩戴简约的水滴造型耳环或项链。"
          ],
          "makeup": [
            "今日妆容以清透水润为主。",
            "眼部可点缀淡蓝或淡紫色眼影。"
          ]
        }
      }
    }
    ```
- **错误码说明**：
    | code | 含义                 |
    |------|---------------------|
    | 3001 | 用户未开启能量模式   |
    | 3002 | 八字信息不完整       |
    | 3003 | 能量计算服务异常     |
- **备注**：
    - 只有开启能量模式且完善八字信息的用户可使用此接口
    - 能量数据基于用户八字、当日五行属性、天气等因素综合计算
    - 首次查看每日能量奖励10灵感值
    - 支持查询历史日期的能量信息（最多查询过去30天）

---


### 5.2 获取用户完整运势解读

**接口描述：** 获取用户的完整运势解读信息，包含八字组合、五行分析、整体运势、吉运建议和详细运势解读

**请求方式：** GET

**请求路径：** `/api/user/fortune/complete-reading`

**请求参数：** 无（需携带token）

**响应数据：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "baziCombination": {
      "year": {
        "tiangan": "乙",
        "dizhi": "巳",
        "canggan": ["丙", "戊", "庚"]
      },
      "month": {
        "tiangan": "己",
        "dizhi": "卯",
        "canggan": ["乙"]
      },
      "day": {
        "tiangan": "庚",
        "dizhi": "子",
        "canggan": ["癸"]
      },
      "hour": {
        "tiangan": "乙",
        "dizhi": "酉",
        "canggan": ["辛"]
      }
    },
    "wuxingAnalysis": {
      "elements": [
        {
          "element": "火",
          "percentage": 8,
          "isRizhu": false
        },
        {
          "element": "土",
          "percentage": 22,
          "isRizhu": false
        },
        {
          "element": "金",
          "percentage": 22,
          "isRizhu": false
        },
        {
          "element": "水",
          "percentage": 0,
          "isRizhu": false
        },
        {
          "element": "木",
          "percentage": 46,
          "isRizhu": true
        }
      ],
      "analysis": "五行木旺，火弱水缺，需要平衡五行能量"
    },
    "overallFortune": {
      "currentAge": 0,
      "decadeFortunes": [
        {
          "decade": 1,
          "ageRange": "0-9岁",
          "yearRange": "2025-2034",
          "score": 78,
          "theme": "成长奠基",
          "description": "童年时期，身体健康，家庭和睦，为未来发展奠定良好基础。",
          "keyEvents": ["启蒙教育", "性格养成"]
        },
        {
          "decade": 2,
          "ageRange": "10-19岁", 
          "yearRange": "2035-2044",
          "score": 84,
          "theme": "求学成长",
          "description": "学业运势良好，智慧开启，有贵人相助。建议专注学习，培养兴趣。",
          "keyEvents": ["学业成就", "才能显现"]
        },
        {
          "decade": 3,
          "ageRange": "20-29岁",
          "yearRange": "2045-2054", 
          "score": 85,
          "theme": "事业起步",
          "description": "踏入社会，事业起步，虽有挑战但机遇并存。需积极进取，把握机会。",
          "keyEvents": ["职场初入", "人际拓展"]
        },
        {
          "decade": 4,
          "ageRange": "30-39岁",
          "yearRange": "2055-2064",
          "score": 92,
          "theme": "发展建设",
          "description": "事业发展期，运势上升，有重要突破。感情生活也将有所收获。",
          "keyEvents": ["事业突破", "感情收获"]
        },
        {
          "decade": 5,
          "ageRange": "40-49岁",
          "yearRange": "2065-2074",
          "score": 93,
          "theme": "事业巅峰",
          "description": "人生巅峰期，事业财运双收，家庭美满。是人生最辉煌的十年。",
          "keyEvents": ["事业巅峰", "财富积累"]
        },
        {
          "decade": 6,
          "ageRange": "50-59岁",
          "yearRange": "2075-2084",
          "score": 79,
          "theme": "成熟稳定",
          "description": "成熟稳定期，事业稳固，财富积累。注重健康，享受家庭生活。",
          "keyEvents": ["家庭稳定", "事业成熟"]
        },
        {
          "decade": 7,
          "ageRange": "60-69岁",
          "yearRange": "2085-2094",
          "score": 85,
          "theme": "收获智慧",
          "description": "智慧收获期，经验丰富，德高望重。可考虑传承知识，培养后进。",
          "keyEvents": ["智慧传承", "声望提升"]
        },
        {
          "decade": 8,
          "ageRange": "70-79岁",
          "yearRange": "2095-2104",
          "score": 71,
          "theme": "传承经验",
          "description": "传承经验期，享受天伦之乐，关注健康养生。运势平稳安康。",
          "keyEvents": ["天伦之乐", "健康养生"]
        },
        {
          "decade": 9,
          "ageRange": "80-89岁",
          "yearRange": "2105-2114",
          "score": 57,
          "theme": "享受人生",
          "description": "享受人生期，儿孙满堂，生活安逸。运势平和，健康是重点。",
          "keyEvents": ["安享晚年", "儿孙满堂"]
        },
        {
          "decade": 10,
          "ageRange": "90-99岁",
          "yearRange": "2115-2124",
          "score": 59,
          "theme": "圆满安康",
          "description": "圆满安康期，人生圆满，长寿安康。晚年生活幸福美满。",
          "keyEvents": ["人生圆满", "长寿安康"]
        }
      ],
      "lifePhase": "成长期",
      "currentDecade": 1,
      "lifetimeOverview": "综合八字分析，您的一生运势呈波浪式发展趋势，中年期达到人生巅峰，晚年平稳安康。重要转折点出现在30-40岁期间，需把握机遇。"
    },
    "luckyAdvice": {
      "clothing": {
        "title": "服装选择",
        "items": [
          {
            "label": "亮色系服装:",
            "advice": "提升火运势"
          }
        ]
      },
      "jewelry": {
        "title": "首饰佩戴",
        "items": [
          {
            "label": "木质饰品：",
            "advice": "檀木/沉香，木能疏土，平衡命局土旺"
          },
          {
            "label": "红色宝石：",
            "advice": "红玛瑙等，补火"
          },
          {
            "label": "金属饰品：",
            "advice": "金可泄土生水"
          }
        ]
      },
      "fengshui": {
        "title": "居家风水",
        "items": [
          {
            "type": "avoid",
            "advice": "避免土属性过重的物品（如减少黄色陶瓷或装饰)"
          },
          {
            "type": "recommend",
            "advice": "多用红色装饰（红地毯等）"
          },
          {
            "type": "recommend",
            "advice": "采用木质家具，增强木气疏土"
          }
        ]
      }
    },
    "detailedFortune": {
      "monthly": {
        "career": {
          "status": "本月事业运势上升",
          "highlight": "有贵人相助",
          "color": "#fbbf24",
          "content": "本月适合主动出击，把握机会。中旬有贵人带来机遇，建议积极争取。"
        },
        "wealth": {
          "status": "本月财运上升",
          "highlight": "",
          "color": "#10b981",
          "content": "本月财运良好，适合投资理财。中旬可能有意外收获。"
        },
        "health": {
          "status": "",
          "highlight": "",
          "color": "#3b82f6",
          "content": "本月身体状况良好，但要注意作息规律，避免过度劳累。"
        },
        "marriage": {
          "status": "",
          "highlight": "",
          "color": "#ec4899",
          "content": "本月婚姻运势平稳，但需要注意沟通方式，避免因小事引发争执。建议多关心对方，增进感情。"
        },
        "children": {
          "status": "",
          "highlight": "",
          "color": "#8b5cf6",
          "content": "本月子女运势良好，学习进步明显。建议多陪伴交流，关注其身心健康发展。"
        }
      },
      "yearly": {
        "career": {
          "status": "八字中"时辰带贵"",
          "highlight": "整体事业运不错",
          "color": "#fbbf24",
          "content": "今年事业运势为平淡，小人妨害多，建议把握2027-2029年机遇，事业上可获得新成就。"
        },
        "wealth": {
          "status": "属于"天干地支有财"命格",
          "highlight": "",
          "color": "#10b981",
          "content": "今年财运平稳，但需防范"岁破大耗"煞的影响，避免借贷和不必要的支出。"
        },
        "health": {
          "status": "",
          "highlight": "",
          "color": "#3b82f6",
          "content": "今年受刑破太岁影响，需要特别注意身体状况，保持良好的作息习惯。建议定期体检，预防为主。"
        },
        "marriage": {
          "status": "",
          "highlight": "",
          "color": "#ec4899",
          "content": [
            "此命格可得贵妻，妻旺财运。但因命带"寡宿"煞，导致夫妻间容易争吵，互不相让。",
            "加上五行中"土"多，易生情性，经常引发妻子不满，加剧矛盾。建议克服因"土"多滋生的情性。"
          ]
        },
        "children": {
          "status": "带有"妨害煞"",
          "highlight": "",
          "color": "#ef4444",
          "content": [
            "容易影响子女运势，可能子女数量不多，或者需多注意子女健康问题。",
            "未来子女相对独立，个性较强，需要适当引导。预示子女有较大潜力，需要多加培养。"
          ]
        }
      }
    }
  }
}
```

**错误码说明：**
| code | 含义 |
|------|------|
| 3001 | 用户未开启能量模式 |
| 3002 | 八字信息不完整 |
| 3003 | 能量计算服务异常 |

**备注：**
- 只有开启能量模式且完善八字信息的用户可使用此接口
- 一次接口调用返回完整的运势解读信息，包含所有模块数据
- lifetimeFortune 数组包含100年的运势数据点，用于绘制运势曲线图
- 接口响应数据量较大，建议做适当的缓存处理
- detailedFortune.*.content 字段支持字符串或字符串数组格式 

## 6. 变更历史

- 2025-01-24 新增天气查询接口（3.13节 获取天气信息），支持根据城市名称或经纬度获取天气信息，同时更新用户居住地区 - AI
- 2025-01-24 新增今日能量简要信息接口（3.12节 获取今日能量简要信息） - AI
- 2025-01-24 新增今日能量接口（3.10节 获取今日能量信息，3.11节 获取能量历史趋势） - AI  
- 2025-01-24 新增用户退出登录接口（3.9节） - AI
- 2025-05-12 初稿 by AI 