# 衣橱服务（wardrobe-service）API接口文档

## 1. 概述
衣橱服务负责衣物、饰品、搭配的增删改查，图片上传与处理，衣橱分析，穿着记录等核心功能。

## 2. 认证与通用说明
- 认证方式：JWT（登录成功后在响应header中返回，后续接口需在请求header中携带Authorization: Bearer {token}）
- 通用请求头：
  - Content-Type: application/json
  - Authorization: Bearer {token} （需要认证的接口）
- 通用响应结构：
    ```json
    {
      "code": 0,
      "msg": "success",
      "data": { ... }
    }
    ```
- 错误码说明：
    | code | 含义         | 说明           |
    |------|--------------|----------------|
    | 0    | 成功         |                |
    | 1001 | 参数错误     |                |
    | 1002 | 未认证/Token无效 |           |
    | 1003 | 权限不足     |                |
    | 3001 | 衣物不存在   | 衣物记录未找到 |
    | 3002 | 饰品不存在   | 饰品记录未找到 |
    | 3003 | 搭配不存在   | 搭配记录未找到 |
    | 3004 | 图片上传失败 | 衣物/饰品图片上传异常 |
    | 3005 | 衣橱容量超限 | 达到衣橱最大容量限制 |
    | 3006 | 搭配数量超限 | 达到搭配最大数量限制 |
    | 3007 | 图片格式错误 | 不支持的图片格式 |
    | 3008 | 分类不存在   | 衣物/饰品分类未找到 |
    | 3009 | 标签不存在   | 衣物/饰品标签未找到 |
    | 3010 | 季节不存在   | 衣物/饰品季节未找到 |
    | 3011 | 场合不存在   | 衣物/饰品场合未找到 |
    | 3012 | 五行属性无效 | 衣物/饰品五行属性设置无效 |
    | 3013 | 穿着记录异常 | 穿着记录操作失败 |
    | 3014 | 分析生成失败 | 衣橱分析数据生成失败 |

## 3. 接口列表

### 3.1 新增衣物
- **接口路径**：`/api/wardrobe/clothing`
- **请求方法**：POST
- **功能描述**：新增衣物信息
- **请求参数**：
    | 参数名       | 类型   | 必填 | 说明         | 选项/结构 |
    |--------------|--------|------|--------------|-----------|
    | name         | string | 是   | 衣物名称     |           |
    | category     | string | 是   | 衣物类别     | 上衣/下装/外套/裙子/鞋/配饰等 |
    | subCategory  | string | 否   | 衣物子类别   | T恤/衬衫/牛仔裤/半身裙等 |
    | colors       | array  | 否   | 颜色列表     | ["白色","黑色",...]
    | tags         | array  | 否   | 标签         | ["基础款","通勤","百搭",...]
    | seasons      | array  | 否   | 适合季节     | ["春","夏","秋","冬"]
    | occasions    | array  | 否   | 适合场合     | ["日常","职场","约会",...]
    | materials    | array  | 否   | 材质         | ["棉","麻","羊毛",...]
    | styles       | array  | 否   | 风格标签     | ["休闲","商务","运动",...]
    | wuxing       | object | 否   | 五行属性     | {"metal":0-5,"wood":0-5,"water":0-5,"fire":0-5,"earth":0-5}
    | mainImageUrl | string | 否   | 主图URL      |           |
    | imageUrls    | array  | 否   | 其他图片URL  | ["url1","url2",...]
    | brand        | string | 否   | 品牌         |           |
    | description  | string | 否   | 描述         |           |
    | purchaseDate | string | 否   | 购买日期     | yyyy-MM-dd|
    | price        | number | 否   | 价格(元)     |           |
    | wearCount    | int    | 否   | 穿着次数     |           |
    | lastWornAt   | string | 否   | 最后穿着时间 | yyyy-MM-dd|
    | createdAt    | string | 否   | 创建时间     | yyyy-MM-dd|
- **请求示例**：
    ```json
    {
      "name": "白色T恤",
      "category": "上衣",
      "subCategory": "T恤",
      "colors": ["白色"],
      "tags": ["基础款"],
      "seasons": ["夏"],
      "occasions": ["日常"],
      "materials": ["棉"],
      "styles": ["休闲"],
      "wuxing": {"metal": 1, "wood": 0, "water": 0, "fire": 0, "earth": 0},
      "mainImageUrl": "https://xxx.com/tshirt.jpg",
      "imageUrls": ["https://xxx.com/tshirt1.jpg"],
      "brand": "优衣库",
      "description": "基础百搭T恤",
      "purchaseDate": "2025-05-01",
      "price": 99.0
    }
    ```
- **响应参数**：
    | 字段名      | 类型   | 说明         | 结构/选项 |
    |-------------|--------|--------------|-----------|
    | id          | string | 衣物ID       |           |
    | name        | string | 衣物名称     |           |
    | category    | string | 衣物类别     |           |
    | subCategory | string | 衣物子类别   |           |
    | colors      | array  | 颜色列表     |           |
    | tags        | array  | 标签         |           |
    | seasons     | array  | 适合季节     |           |
    | occasions   | array  | 适合场合     |           |
    | materials   | array  | 材质         |           |
    | styles      | array  | 风格标签     |           |
    | wuxing      | object | 五行属性     | {"metal":0-5,"wood":0-5,"water":0-5,"fire":0-5,"earth":0-5}
    | mainImageUrl| string | 主图URL      |           |
    | imageUrls   | array  | 其他图片URL  |           |
    | brand       | string | 品牌         |           |
    | description | string | 描述         |           |
    | purchaseDate| string | 购买日期     | yyyy-MM-dd|
    | price       | number | 价格(元)     |           |
    | wearCount   | int    | 穿着次数     |           |
    | lastWornAt  | string | 最后穿着时间 | yyyy-MM-dd|
    | createdAt   | string | 创建时间     | yyyy-MM-dd|
    | updatedAt   | string | 更新时间     | yyyy-MM-dd|
- **响应示例**：
    ```json
    {
      "code": 0,
      "msg": "success",
      "data": {
        "id": "c123456",
        "name": "白色T恤",
        "category": "上衣",
        "subCategory": "T恤",
        "colors": ["白色"],
        "tags": ["基础款"],
        "seasons": ["夏"],
        "occasions": ["日常"],
        "materials": ["棉"],
        "styles": ["休闲"],
        "wuxing": {"metal": 1, "wood": 0, "water": 0, "fire": 0, "earth": 0},
        "mainImageUrl": "https://xxx.com/tshirt.jpg",
        "imageUrls": ["https://xxx.com/tshirt1.jpg"],
        "brand": "优衣库",
        "description": "基础百搭T恤",
        "purchaseDate": "2025-05-01",
        "price": 99.0,
        "wearCount": 5,
        "lastWornAt": "2025-05-10",
        "createdAt": "2025-05-01",
        "updatedAt": "2025-05-10"
      }
    }
    ```

---

### 3.2 编辑衣物
- **接口路径**：`/api/wardrobe/clothing`
- **请求方法**：PUT
- **功能描述**：编辑衣物信息
- **请求参数**：同3.1，需包含衣物ID
- **响应参数**：同3.1

---

### 3.3 删除衣物
- **接口路径**：`/api/wardrobe/clothing`
- **请求方法**：DELETE
- **功能描述**：删除指定衣物
- **请求参数**：
    | 参数名 | 类型   | 必填 | 说明   |
    |--------|--------|------|--------|
    | id     | string | 是   | 衣物ID |
- **请求示例**：
    ```json
    {
      "id": "c123456"
    }
    ```
- **响应参数**：无

---

### 3.4 查询衣物列表
- **接口路径**：`/api/wardrobe/clothing/list`
- **请求方法**：GET
- **功能描述**：获取当前用户所有衣物
- **请求参数**：可选过滤条件（如category、season、tag等）
- **响应参数**：
    | 字段名 | 类型   | 说明         |
    |--------|--------|--------------|
    | list   | array  | 衣物列表     |
- **响应示例**：
    ```json
    {
      "code": 0,
      "msg": "success",
      "data": {
        "list": [
          {"id": "c123456", "name": "白色T恤", "category": "上衣"}
        ]
      }
    }
    ```

---

### 3.5 获取衣物五行属性
- **接口路径**：`/api/wardrobe/clothing/wuxing`
- **请求方法**：GET
- **功能描述**：获取指定衣物的五行属性
- **请求参数**：
    | 参数名 | 类型   | 必填 | 说明   |
    |--------|--------|------|--------|
    | id     | string | 是   | 衣物ID |
- **响应参数**：
    | 字段名 | 类型   | 说明         |
    |--------|--------|--------------|
    | wuxing | object | 五行属性     |
- **响应示例**：
    ```json
    {
      "code": 0,
      "msg": "success",
      "data": {
        "wuxing": {"metal": 1, "wood": 0, "water": 0, "fire": 0, "earth": 0}
      }
    }
    ```

---

### 3.6 饰品管理相关接口
- **接口路径**：`/api/wardrobe/accessory` (POST/PUT/DELETE/GET)
- **功能描述**：增删改查饰品，参数结构与衣物类似，字段如name、type、colors、tags、wuxing等。
- **响应参数/示例**：同衣物接口

---

### 3.7 搭配管理相关接口
- **接口路径**：`/api/wardrobe/outfit` (POST/PUT/DELETE/GET)
- **功能描述**：增删改查搭配，字段如name、clothingIds、accessoryIds、images、wuxing等。
- **响应参数/示例**：同衣物接口

---

### 3.8 图片上传/处理
- **接口路径**：`/api/wardrobe/image`
- **请求方法**：POST
- **功能描述**：上传衣物/饰品图片，支持图片处理（如去背景、风格分析等）
- **请求参数**：
    | 参数名 | 类型         | 必填 | 说明     |
    |--------|--------------|------|----------|
    | file   | multipart    | 是   | 图片文件 |
    | type   | string       | 否   | 图片类型 |
- **响应参数**：
    | 字段名 | 类型   | 说明         |
    |--------|--------|--------------|
    | url    | string | 图片URL      |
    | ...    | ...    | ...          |
- **响应示例**：
    ```json
    {
      "code": 0,
      "msg": "success",
      "data": {
        "url": "https://xxx.com/tshirt.jpg"
      }
    }
    ```

---

### 3.9 衣橱分析
- **接口路径**：`/api/wardrobe/analysis`
- **请求方法**：GET
- **功能描述**：获取当前用户衣橱的风格、色彩、五行、穿着频率等分析结果
- **响应参数**：
    | 字段名 | 类型   | 说明         |
    |--------|--------|--------------|
    | styleAnalysis | object | 风格分析 |
    | colorAnalysis | object | 色彩分析 |
    | wuxingBalance | object | 五行平衡 |
    | wearFrequency | object | 穿着频率 |
    | ...           | ...    | ...      |
- **响应示例**：
    ```json
    {
      "code": 0,
      "msg": "success",
      "data": {
        "styleAnalysis": {"sporty": 0.5, "casual": 0.5},
        "colorAnalysis": {"white": 3, "black": 2},
        "wuxingBalance": {"metal": 2, "wood": 1, "water": 1, "fire": 1, "earth": 1},
        "wearFrequency": {"c123456": 10}
      }
    }
    ```

---

### 3.10 穿着记录
- **接口路径**：`/api/wardrobe/wear`
- **请求方法**：POST
- **功能描述**：记录用户穿着某件衣物/搭配
- **请求参数**：
    | 参数名 | 类型   | 必填 | 说明     |
    |--------|--------|------|----------|
    | id     | string | 是   | 衣物/搭配ID |
    | type   | string | 是   | 类型（clothing/outfit） |
    | date   | string | 否   | 穿着日期 |
- **响应参数**：无

---

### 3.11 获取穿着历史
- **接口路径**：`/api/wardrobe/wear/history`
- **请求方法**：GET
- **功能描述**：获取用户穿着历史记录
- **响应参数**：
    | 字段名 | 类型   | 说明         |
    |--------|--------|--------------|
    | list   | array  | 穿着记录列表 |
- **响应示例**：
    ```json
    {
      "code": 0,
      "msg": "success",
      "data": {
        "list": [
          {"id": "c123456", "type": "clothing", "date": "2025-05-12"}
        ]
      }
    }
    ```

---

## 4. 典型业务流程示例
- 新增衣物 -> 上传图片 -> 编辑/删除衣物 -> 查询衣物列表 -> 记录穿着 -> 获取分析

---

## 5. 变更历史
- 2025-05-12 初稿 by AI 