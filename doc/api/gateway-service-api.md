# API网关服务（gateway-service）API接口文档

## 1. 概述
API网关服务是系统的统一入口，负责请求路由、认证授权、限流熔断、请求日志、监控等核心功能。作为系统的"门面"，它对外提供统一的API接入点，并实现了微服务的统一管理。

## 2. 认证与通用说明
- 认证方式：JWT（登录成功后在响应header中返回，后续接口需在请求header中携带Authorization: Bearer {token}）
- 部分接口可开放访问，部分需要认证
- 通用请求头：
  - Content-Type: application/json
  - Authorization: Bearer {token} （需要认证的接口）
- 通用响应结构：
    ```json
    {
      "code": 0,
      "msg": "success",
      "data": { ... }
    }
    ```
- 错误码说明：
    | code | 含义         | 说明           |
    |------|--------------|----------------|
    | 0    | 成功         |                |
    | 1001 | 参数错误     |                |
    | 1002 | 未认证/Token无效 |           |
    | 1003 | 权限不足     |                |
    | 9001 | 网关异常     | 网关服务异常   |
    | 9002 | 路由异常     | 路由配置异常   |
    | 9003 | 限流异常     | 触发限流规则   |
    | 9004 | 熔断异常     | 触发熔断规则   |
    | 9005 | 超时异常     | 请求超时       |
    | 9006 | 黑名单限制   | IP/账号被限制  |

## 3. 接口列表

### 3.1 路由管理
- **接口路径**：`/api/gateway/routes`
- **请求方法**：GET/POST/PUT/DELETE
- **功能描述**：管理API路由配置
- **请求参数**：
    | 参数名     | 类型   | 必填 | 说明         |
    |------------|--------|------|--------------|
    | serviceId  | string | 是   | 服务ID       |
    | path       | string | 是   | 路由路径     |
    | filters    | array  | 否   | 过滤器配置   |
    | predicates | array  | 否   | 断言配置     |
    | order      | int    | 否   | 优先级       |
    | metadata   | object | 否   | 元数据       |
- **响应示例**：
    ```json
    {
      "code": 0,
      "msg": "success",
      "data": {
        "routes": [{
          "serviceId": "user-service",
          "path": "/api/user/**",
          "filters": ["StripPrefix=1"],
          "predicates": ["Path=/api/user/**"],
          "order": 1,
          "metadata": {}
        }]
      }
    }
    ```

### 3.2 限流配置
- **接口路径**：`/api/gateway/rate-limit`
- **请求方法**：GET/POST/PUT/DELETE
- **功能描述**：管理限流规则配置
- **请求参数**：
    | 参数名     | 类型   | 必填 | 说明         |
    |------------|--------|------|--------------|
    | resource   | string | 是   | 资源名称     |
    | type       | string | 是   | 限流类型     |
    | count      | int    | 是   | 限流阈值     |
    | interval   | int    | 是   | 时间窗口(秒) |
- **响应示例**：
    ```json
    {
      "code": 0,
      "msg": "success",
      "data": {
        "rules": [{
          "resource": "/api/user/login",
          "type": "IP",
          "count": 100,
          "interval": 60
        }]
      }
    }
    ```

### 3.3 熔断配置
- **接口路径**：`/api/gateway/circuit-breaker`
- **请求方法**：GET/POST/PUT/DELETE
- **功能描述**：管理熔断规则配置
- **请求参数**：
    | 参数名        | 类型   | 必填 | 说明         |
    |---------------|--------|------|--------------|
    | serviceId     | string | 是   | 服务ID       |
    | failureRate   | int    | 是   | 失败率阈值   |
    | waitDuration  | int    | 是   | 等待时间(秒) |
    | ringBufferSize| int    | 是   | 环形缓冲大小 |
- **响应示例**：
    ```json
    {
      "code": 0,
      "msg": "success",
      "data": {
        "rules": [{
          "serviceId": "user-service",
          "failureRate": 50,
          "waitDuration": 60,
          "ringBufferSize": 100
        }]
      }
    }
    ```

### 3.4 黑白名单
- **接口路径**：`/api/gateway/blacklist`、`/api/gateway/whitelist`
- **请求方法**：GET/POST/PUT/DELETE
- **功能描述**：管理IP和账号黑白名单
- **请求参数**：
    | 参数名     | 类型   | 必填 | 说明         |
    |------------|--------|------|--------------|
    | type       | string | 是   | IP/ACCOUNT   |
    | value      | string | 是   | IP/账号      |
    | expireTime | string | 否   | 过期时间     |
    | reason     | string | 否   | 原因         |
- **响应示例**：
    ```json
    {
      "code": 0,
      "msg": "success",
      "data": {
        "blacklist": [{
          "type": "IP",
          "value": "***********",
          "expireTime": "2025-06-12T10:00:00Z",
          "reason": "异常访问"
        }]
      }
    }
    ```

### 3.5 监控指标
- **接口路径**：`/api/gateway/metrics`
- **请求方法**：GET
- **功能描述**：获取网关监控指标
- **响应参数**：
    | 字段名 | 类型   | 说明         |
    |--------|--------|--------------|
    | qps    | int    | 每秒请求数   |
    | rt     | int    | 平均响应时间 |
    | error  | int    | 错误数       |
    | ...    | ...    | ...          |
- **响应示例**：
    ```json
    {
      "code": 0,
      "msg": "success",
      "data": {
        "metrics": {
          "qps": 100,
          "rt": 50,
          "error": 5
        }
      }
    }
    ```

### 3.6 日志查询
- **接口路径**：`/api/gateway/logs`
- **请求方法**：GET
- **功能描述**：查询网关请求日志
- **请求参数**：
    | 参数名     | 类型   | 必填 | 说明         |
    |------------|--------|------|--------------|
    | startTime  | string | 否   | 开始时间     |
    | endTime    | string | 否   | 结束时间     |
    | serviceId  | string | 否   | 服务ID       |
    | status     | int    | 否   | 状态码       |
    | path       | string | 否   | 请求路径     |
- **响应示例**：
    ```json
    {
      "code": 0,
      "msg": "success",
      "data": {
        "logs": [{
          "timestamp": "2025-05-12T10:00:00Z",
          "serviceId": "user-service",
          "path": "/api/user/login",
          "method": "POST",
          "status": 200,
          "rt": 50
        }]
      }
    }
    ```

## 4. 典型业务流程示例
- 配置路由 -> 设置限流规则 -> 配置熔断策略 -> 管理黑白名单 -> 监控指标

## 5. 变更历史
- 2025-05-12 初稿 by AI 