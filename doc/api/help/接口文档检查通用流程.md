# 通用接口文档检查流程

本流程适用于所有服务的 API 接口文档与需求/原型/设计文档的系统性核查，确保字段无遗漏、结构清晰、文档易于对接。

---

## 1. 明确检查对象
- 明确本次需核查的接口文档（如 user-service-api.md, 在 `doc/api/` 目录下）。
- 明确该服务对应的原型页面、需求文档、设计文档（可从 [doc/文档目录.md] 查找）。
- 原型页面建议通过 [doc/prototype_structure.md] 查找和定位，确保不遗漏任何相关页面。
- **在梳理需求/原型/设计文档前，先列出即将读取的文件名，提交给用户检查确认，确保不遗漏关键文档。**

## 2. 梳理需求/原型/设计文档字段（正向清单）
- 先系统梳理需求文档、原型页面、设计文档，列出所有功能点、字段、交互项、数据结构，形成"正向清单"。
- 推荐以表格或结构化清单形式输出，便于后续核查。

## 3. 梳理接口文档所有字段
- 读取接口文档，列出所有接口及其请求/响应字段。
- 对比正向清单，标注接口文档中未覆盖的需求/原型字段。

## 4. 双向核查字段覆盖性
- 正向核查：逐项核查需求/原型/设计文档中的每个字段是否在接口文档中有体现，标注遗漏项。
- 反向核查：逐项核查接口文档中的每个字段是否在需求/原型/设计文档中有出处，标注冗余或设计偏差。
- 标注遗漏、歧义或不一致之处，提出补充或修正建议。

## 5. 结构与示例标准化
- 检查接口文档是否对复杂字段结构和可选值有明确说明。
- 检查是否有标准的请求/响应示例，便于前后端对齐。

## 6. 结论与建议输出
- 输出核查结论：字段是否无遗漏、结构是否清晰、文档是否易于对接。
- 如有遗漏或不规范，提出具体补充/修正建议。

## 7. 归档与复用
- 将本次核查的三方字段对照清单归档到 field-mapping 目录，便于后续追溯和复用。
- 建议每个服务都建立独立的字段对照清单，形成标准化文档资产。

---

## 【标准化执行Checklist】

1. 明确接口文档与需求/原型/设计文档范围
2. 梳理需求/原型/设计文档所有字段（正向清单）
3. 梳理接口文档所有字段
4. 正向核查：需求/原型/设计字段→接口文档字段
5. 反向核查：接口文档字段→需求/原型/设计字段
6. 构建"接口-原型-需求"三方字段对照表，增加"覆盖状态"列
7. 检查复杂结构字段的详细说明与示例
8. 输出核查结论与建议
9. 归档对照清单，便于后续复用

---

> 推荐采用"双向核查法"：先以需求/原型/设计文档为主，梳理全量功能点和字段，形成"正向清单"；再以接口文档为主，做"反向映射"，确保接口文档无遗漏、无冗余。该方法能最大程度发现遗漏和冗余，提升接口文档质量，适用于所有服务。

---

## 三方字段对照清单标准模板与归档规范（建议全员遵循）

### 1. 模板结构与内容要求
- 建议每个服务建立独立的三方字段对照清单，归档于 `doc/api/field-mapping/` 目录，命名为"[服务名]字段对照清单.md"。
- 推荐结构如下：

```
# [服务名称]字段对照清单

> 本清单用于核查[服务名称]API接口文档字段与原型页面、需求文档的覆盖关系，确保无遗漏。

---

## 1. 字段对照表说明
- "接口字段"：API文档中定义的字段
- "原型字段"：相关原型页面涉及的所有字段
- "需求字段"：需求/设计文档中与本服务相关的字段
- "覆盖状态"：标注"已覆盖/未覆盖/接口冗余/需求遗漏"等

---

## 2. 字段对照总览
| 接口字段（API文档） | 原型字段 | 需求字段 | 类型/结构 | 可选值/约束 | 覆盖状态 | 备注 |
|---------------------|----------|----------|-----------|-------------|----------|------|
|                     |          |          |           |             |          |      |

---

## 3. 复杂结构字段细化（如有）
- 对于嵌套对象、枚举、数组等复杂结构，单独分节细化所有子字段及可选值。
- 示例：
| 字段/子字段 | 原型字段 | 需求字段 | 类型/结构 | 可选值/约束 | 覆盖状态 | 备注 |
|-------------|----------|----------|-----------|-------------|----------|------|
| wuxing.metal|          |          | int       | 0-100       | 已覆盖   | 金   |
| ...         |          |          |           |             |          |      |

---

## 4. 衍生/推荐字段（如有）
- 记录后端推算、自动生成、推荐类字段。

---

## 5. 结论与建议
- 字段覆盖性结论、需补充细化的字段、结构优化建议等。

```

### 2. 归档与维护规范
- 每次接口/原型/需求变更时，优先更新三方字段对照清单，确保文档与实现同步。
- 建议团队成员直接复用本模板，形成标准化文档资产。

### 3. 复杂结构字段处理要求
- 对如五行属性、图片数组、风格标签等复杂结构，单独分节细化所有子字段及可选值。
- 建议在表格中增加"类型/结构"、"可选值/约束"、"覆盖状态"三列，便于前后端对齐和自动化校验。

### 4. 自动化与复用建议
- 鼓励基于标准表格结构开发自动化校验工具，辅助检测字段遗漏、类型不一致等问题。
- 三方字段对照清单的建立与维护为接口文档检查流程的必经步骤。
- 变更流程中，需同步更新对照清单，确保文档、原型、需求、实现的一致性和可追溯性。

--- 
