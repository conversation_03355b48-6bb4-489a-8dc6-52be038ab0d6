# 推荐服务（recommendation-service）API接口文档

## 1. 概述
推荐服务负责穿搭推荐（多种模式）、五行命理分析、天气数据处理、个性化推荐、推荐反馈与历史等核心功能。

## 2. 认证与通用说明
- 认证方式：JWT（登录成功后在响应header中返回，后续接口需在请求header中携带Authorization: Bearer {token}）
- 通用请求头：
  - Content-Type: application/json
  - Authorization: Bearer {token} （需要认证的接口）
- 通用响应结构：
    ```json
    {
      "code": 0,
      "msg": "success",
      "data": { ... }
    }
    ```
- 错误码说明：
    | code | 含义         | 说明           |
    |------|--------------|----------------|
    | 0    | 成功         |                |
    | 1001 | 参数错误     |                |
    | 1002 | 未认证/Token无效 |           |
    | 1003 | 权限不足     |                |
    | 4001 | 推荐不存在   | 推荐记录未找到 |
    | 4002 | 推荐生成失败 | 推荐算法执行异常 |
    | 4003 | 用户偏好缺失 | 缺少用户偏好数据 |
    | 4004 | 天气数据异常 | 天气服务调用失败 |
    | 4005 | 场景不支持   | 不支持的推荐场景 |
    | 4006 | 推荐评分失败 | 推荐评分处理异常 |
    | 4007 | 反馈提交失败 | 用户反馈提交异常 |
    | 4008 | 推荐已过期   | 推荐数据已过期   |

## 3. 接口列表

### 3.1 获取每日推荐
- **接口路径**：`/api/recommend/daily`
- **请求方法**：GET
- **功能描述**：获取用户每日穿搭推荐
- **请求参数**：可选日期、场景等
- **响应参数**：
    | 字段名 | 类型   | 说明         |
    |--------|--------|--------------|
    | outfits | array | 推荐搭配列表 |
    | reason  | string| 推荐理由     |
    | ...     | ...   | ...          |
- **响应示例**：
    ```json
    {
      "code": 0,
      "msg": "success",
      "data": {
        "outfits": [
          {"id": "o123", "name": "夏日清新", "items": ["c123", "a456"]}
        ],
        "reason": "今日天气晴朗，推荐清新风格"
      }
    }
    ```

---

### 3.2 场合/多场景推荐
- **接口路径**：`/api/recommend/occasion`、`/api/recommend/multi`
- **请求方法**：GET/POST
- **功能描述**：获取指定场合或多场景下的推荐搭配
- **请求参数**：场合、日期、用户偏好等
- **响应参数/示例**：同3.1

---

### 3.3 购物/旅行推荐
- **接口路径**：`/api/recommend/shopping`、`/api/recommend/travel`
- **请求方法**：GET/POST
- **功能描述**：获取购物或旅行场景下的推荐搭配
- **请求参数/响应参数/示例**：同3.1

---

### 3.4 五行命理分析
- **接口路径**：`/api/recommend/wuxing`
- **请求方法**：POST
- **功能描述**：根据用户信息进行五行命理分析
- **请求参数**：
    | 参数名     | 类型   | 必填 | 说明         |
    |------------|--------|------|--------------|
    | birthTime  | string | 是   | 出生时间     |
    | birthPlace | string | 否   | 出生地点     |
- **响应参数**：
    | 字段名           | 类型   | 说明         |
    |------------------|--------|--------------|
    | wuxingAnalysis   | object | 五行分析结果 |
    | ...              | ...    | ...          |
- **响应示例**：
    ```json
    {
      "code": 0,
      "msg": "success",
      "data": {
        "wuxingAnalysis": {"metal": 2, "wood": 1, "water": 3, "fire": 1, "earth": 1}
      }
    }
    ```

---

### 3.5 天气服务相关接口
- **接口路径**：`/api/recommend/weather`
- **请求方法**：GET
- **功能描述**：获取当前天气、天气指数、预警等
- **请求参数**：地理位置、日期等
- **响应参数**：
    | 字段名 | 类型   | 说明         |
    |--------|--------|--------------|
    | weather | object | 天气信息     |
    | ...     | ...    | ...          |
- **响应示例**：
    ```json
    {
      "code": 0,
      "msg": "success",
      "data": {
        "weather": {"temperature": 28, "condition": "晴"}
      }
    }
    ```

---

### 3.6 个性化推荐
- **接口路径**：`/api/recommend/personal`
- **请求方法**：GET/POST
- **功能描述**：根据用户历史、偏好等生成个性化推荐
- **请求参数/响应参数/示例**：同3.1

---

### 3.7 推荐反馈
- **接口路径**：`/api/recommend/feedback`
- **请求方法**：POST
- **功能描述**：提交用户对推荐的反馈（如喜欢/不喜欢、评分、意见等）
- **请求参数**：
    | 参数名   | 类型   | 必填 | 说明         |
    |----------|--------|------|--------------|
    | recommendId | string | 是 | 推荐ID      |
    | rating   | int    | 否   | 评分         |
    | feedback | string | 否   | 反馈内容     |
- **响应参数**：无

---

### 3.8 推荐历史与统计
- **接口路径**：`/api/recommend/history`、`/api/recommend/stats`
- **请求方法**：GET
- **功能描述**：获取用户历史推荐记录与统计信息
- **响应参数**：
    | 字段名 | 类型   | 说明         |
    |--------|--------|--------------|
    | list   | array  | 推荐历史列表 |
    | stats  | object | 推荐统计     |
- **响应示例**：
    ```json
    {
      "code": 0,
      "msg": "success",
      "data": {
        "list": [
          {"id": "r123", "date": "2025-05-12", "outfits": ["o123"]}
        ],
        "stats": {"total": 30, "liked": 20}
      }
    }
    ```

---

### 3.9 获取搭配详情
- **接口路径**：`/api/recommend/detail/{outfitId}`
- **请求方法**：GET
- **功能描述**：获取指定搭配的完整详情，包含主图、视频、评分、推荐理由、场合、单品明细、五行解读、能量建议、交互数据、购物清单等。
- **请求参数**：
    | 参数名   | 类型   | 必填 | 说明         |
    |----------|--------|------|--------------|
    | outfitId | string | 是   | 搭配ID       |
- **响应参数**：
    | 字段名           | 类型     | 说明         |
    |------------------|----------|--------------|
    | id               | string   | 搭配ID       |
    | name             | string   | 搭配名称     |
    | mainImageUrl     | string   | 主图URL      |
    | videoUrl         | string   | 视频URL      |
    | score            | number   | 总评分       |
    | multiScore       | object   | 多维度评分（energy/occasion/style/temperament）|
    | reason           | string   | 推荐理由     |
    | occasions        | array    | 推荐场合     |
    | items            | array    | 搭配单品明细 |
    | wuxingAnalysis   | object   | 五行分布     |
    | wuxingSummary    | string   | 五行整体解读 |
    | wuxingDetails    | array    | 各元素解析   |
    | energyAdvice     | array    | 能量提升建议 |
    | likeCount        | int      | 点赞数       |
    | favoriteCount    | int      | 收藏数       |
    | shareCount       | int      | 分享数       |
    | feedback         | object   | 用户反馈     |
    | shoppingList     | array    | 购物清单     |

- **items字段结构**：
    | 字段名         | 类型     | 说明         |
    |----------------|----------|--------------|
    | itemId         | string   | 单品ID       |
    | itemName       | string   | 单品名称     |
    | itemImageUrl   | string   | 单品图片     |
    | itemType       | string   | 单品类型（shirt/skirt/shoes/bag/accessory）|
    | wuxing         | string   | 五行属性     |
    | itemDescription| string   | 单品描述     |
    | itemTags       | array    | 单品标签     |
    | source         | string   | 来源（wardrobe/recommended）|
    | score          | int      | 推荐度       |

- **wuxingAnalysis字段结构**：
    | 字段名 | 类型 | 说明 |
    |--------|------|------|
    | metal  | int  | 金   |
    | wood   | int  | 木   |
    | water  | int  | 水   |
    | fire   | int  | 火   |
    | earth  | int  | 土   |

- **multiScore字段结构**：
    | 字段名       | 类型   | 说明         |
    |--------------|--------|--------------|
    | energy       | number | 能量匹配度   |
    | occasion     | number | 场合适配度   |
    | style        | number | 风格协调度   |
    | temperament  | number | 气质提升度   |

- **wuxingDetails字段结构**：
    | 字段名   | 类型   | 说明         |
    |----------|--------|--------------|
    | element  | string | 五行元素     |
    | value    | int    | 分值         |
    | summary  | string | 解读文本     |

- **energyAdvice字段结构**：
    | 字段名   | 类型   | 说明         |
    |----------|--------|--------------|
    | element  | string | 五行元素     |
    | advice   | string | 建议文本     |

- **feedback字段结构**：
    | 字段名   | 类型   | 说明         |
    |----------|--------|--------------|
    | liked    | bool   | 是否喜欢     |
    | rating   | number | 用户评分     |
    | comment  | string | 用户评论     |

- **shoppingList字段结构**：
    | 字段名      | 类型   | 说明         |
    |-------------|--------|--------------|
    | itemId      | string | 单品ID       |
    | itemName    | string | 单品名称     |
    | itemImageUrl| string | 单品图片     |
    | buyUrl      | string | 购买链接     |

- **响应示例**：
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "id": "o123",
    "name": "清新白裙搭配",
    "mainImageUrl": "https://...jpg",
    "videoUrl": "https://...mp4",
    "score": 4.5,
    "multiScore": {"energy": 4.5, "occasion": 4.0, "style": 5.0, "temperament": 4.8},
    "reason": "这套穿搭以清爽的白色为基调...",
    "occasions": ["商务会议", "职场通勤"],
    "items": [
      {"itemId": "c1", "itemName": "白色简约衬衫", "itemImageUrl": "https://...jpg", "itemType": "shirt", "wuxing": "金", "itemDescription": "轻薄透气棉质面料...", "itemTags": ["清爽", "百搭"], "source": "wardrobe", "score": 95}
      // ...
    ],
    "wuxingAnalysis": {"metal": 40, "wood": 10, "water": 20, "fire": 0, "earth": 30},
    "wuxingSummary": "此搭配以金(白色系)为主...",
    "wuxingDetails": [
      {"element": "金", "value": 40, "summary": "白色调衬衫和裙子..."}
      // ...
    ],
    "energyAdvice": [
      {"element": "木", "advice": "搭配绿色或青色小饰品..."}
      // ...
    ],
    "likeCount": 24,
    "favoriteCount": 16,
    "shareCount": 0,
    "feedback": {"liked": true, "rating": 4.5, "comment": "很喜欢这套搭配"},
    "shoppingList": [
      {"itemId": "c1", "itemName": "白色简约衬衫", "itemImageUrl": "https://...jpg", "buyUrl": "https://..."}
      // ...
    ]
  }
}
```

---

## 4. 典型业务流程示例
- 获取每日推荐 -> 提交反馈 -> 查询历史推荐 -> 获取个性化推荐

---

## 5. 变更历史
- 2025-05-12 初稿 by AI 