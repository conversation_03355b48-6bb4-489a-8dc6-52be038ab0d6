# 社交服务（social-service）API接口文档

## 1. 概述
社交服务负责内容分享、点赞、评论、收藏、排行榜、互动激励、内容审核等用户互动与内容管理相关功能。

## 2. 认证与通用说明
- 认证方式：JWT（登录成功后在响应header中返回，后续接口需在请求header中携带Authorization: Bearer {token}）
- 通用请求头：
  - Content-Type: application/json
  - Authorization: Bearer {token} （需要认证的接口）
- 通用响应结构：
    ```json
    {
      "code": 0,
      "msg": "success",
      "data": { ... }
    }
    ```
- 错误码说明：
    | code | 含义         | 说明           |
    |------|--------------|----------------|
    | 0    | 成功         |                |
    | 1001 | 参数错误     |                |
    | 1002 | 未认证/Token无效 |           |
    | 1003 | 权限不足     |                |
    | 8001 | 内容异常     |                |
    | 8002 | 互动异常     |                |
    | 9001 | 达到激励上限 |                |
    | 9002 | 重复操作     |                |
    | ...  | ...          |                |

## 3. 接口列表

### 3.1 内容分享
- **接口路径**：`/api/social/share`
- **请求方法**：POST
- **功能描述**：分享搭配、推荐、活动等内容
- **请求参数**：内容ID、类型、平台等
- **响应参数**：分享URL、状态等

---

### 3.2 获取分享历史
- **接口路径**：`/api/social/share/history`
- **请求方法**：GET
- **功能描述**：获取用户历史分享记录
- **响应参数**：分享记录列表

---

### 3.3 点赞/取消点赞
- **接口路径**：`/api/social/like`、`/api/social/unlike`
- **请求方法**：POST
- **功能描述**：对内容点赞或取消点赞
- **请求参数**：
    | 字段名    | 类型   | 必填 | 说明         |
    |-----------|--------|------|--------------|
    | contentId | string | 是   | 内容ID       |
    | type      | string | 是   | 内容类型     |
- **响应参数**：
    | 字段名         | 类型     | 说明                         |
    |----------------|----------|------------------------------|
    | likeStatus     | bool     | 当前用户是否已点赞           |
    | likeCount      | int      | 内容累计点赞数               |
    | likeLimitReached | bool   | 是否达到每日点赞激励上限     |
    | likeReward     | object   | 点赞激励反馈（见下）         |
    | errorCode      | int      | 错误码                       |
    | errorMsg       | string   | 错误信息                     |
    | success        | bool     | 操作是否成功                 |
- **likeReward结构**：
    | 字段名      | 类型   | 说明                 |
    |-------------|--------|----------------------|
    | rewardType  | string | 激励类型（like等）   |
    | rewardValue | int    | 本次激励获得灵感值   |
    | rewardTotal | int    | 今日累计灵感值       |
    | rewardLimit | int    | 每日激励上限         |
    | rewardStatus| string | 激励状态             |
- **响应示例**：
    ```json
    {
      "code": 0,
      "msg": "success",
      "data": {
        "contentId": "abc123",
        "type": "outfit",
        "likeStatus": true,
        "likeCount": 128,
        "likeLimitReached": false,
        "likeReward": {
          "rewardType": "like",
          "rewardValue": 2,
          "rewardTotal": 18,
          "rewardLimit": 30,
          "rewardStatus": "success"
        },
        "errorCode": 0,
        "errorMsg": "",
        "success": true
      }
    }
    ```

---

### 3.4 获取点赞列表
- **接口路径**：`/api/social/like/list`
- **请求方法**：GET
- **功能描述**：获取用户点赞内容列表
- **响应参数**：
    | 字段名    | 类型   | 说明         |
    |-----------|--------|--------------|
    | contentId | string | 内容ID       |
    | type      | string | 内容类型     |
    | likeTime  | string | 点赞时间     |
    | ...       | ...    | ...          |

---

### 3.5 评论相关接口
- **接口路径**：`/api/social/comment` (POST/GET)
- **功能描述**：添加评论、获取评论列表
- **请求参数**：内容ID、评论内容等
- **响应参数**：评论列表

---

### 3.6 收藏管理
- **接口路径**：`/api/social/collection` (POST/PUT/DELETE/GET)
- **功能描述**：创建、更新、删除、查询收藏夹及收藏项目
- **请求参数**：
    | 字段名        | 类型   | 必填 | 说明         |
    |---------------|--------|------|--------------|
    | contentId     | string | 是   | 内容ID       |
    | type          | string | 是   | 内容类型     |
    | collectionId  | string | 否   | 收藏夹ID     |
    | collectionName| string | 否   | 收藏夹名称   |
    | batch         | array  | 否   | 批量操作内容ID列表 |
- **响应参数**：
    | 字段名            | 类型     | 说明                         |
    |-------------------|----------|------------------------------|
    | collected         | bool     | 当前用户是否已收藏           |
    | collectionCount   | int      | 内容累计被收藏数             |
    | collectionId      | string   | 收藏夹ID                     |
    | collectionName    | string   | 收藏夹名称                   |
    | collectionItems   | array    | 收藏夹内容列表               |
    | collectionGroups  | array    | 分组管理相关字段             |
    | collectionLimitReached | bool | 是否达到每日收藏激励上限     |
    | collectionReward  | object   | 收藏激励反馈（见下）         |
    | errorCode         | int      | 错误码                       |
    | errorMsg          | string   | 错误信息                     |
    | success           | bool     | 操作是否成功                 |
- **collectionReward结构**：
    | 字段名      | 类型   | 说明                 |
    |-------------|--------|----------------------|
    | rewardType  | string | 激励类型（collect等）|
    | rewardValue | int    | 本次激励获得灵感值   |
    | rewardTotal | int    | 今日累计灵感值       |
    | rewardLimit | int    | 每日激励上限         |
    | rewardStatus| string | 激励状态             |
- **响应示例**：
    ```json
    {
      "code": 0,
      "msg": "success",
      "data": {
        "contentId": "abc123",
        "type": "outfit",
        "collected": true,
        "collectionCount": 56,
        "collectionId": "fav001",
        "collectionName": "最爱搭配",
        "collectionItems": ["abc123", "def456"],
        "collectionLimitReached": false,
        "collectionReward": {
          "rewardType": "collect",
          "rewardValue": 3,
          "rewardTotal": 12,
          "rewardLimit": 20,
          "rewardStatus": "success"
        },
        "errorCode": 0,
        "errorMsg": "",
        "success": true
      }
    }
    ```

---

### 3.7 排行榜
- **接口路径**：`/api/social/rank`
- **请求方法**：GET
- **功能描述**：获取风格、热度、创意等排行榜
- **响应参数**：排行榜列表

---

### 3.8 互动激励
- **接口路径**：`/api/social/reward`
- **请求方法**：POST
- **功能描述**：处理用户互动激励（如点赞、评论、分享奖励）
- **请求参数**：互动类型、内容ID等
- **响应参数**：奖励信息

---

### 3.9 内容审核与举报
- **接口路径**：`/api/social/review`、`/api/social/report`
- **请求方法**：POST
- **功能描述**：内容审核、举报违规内容
- **请求参数**：内容ID、原因等
- **响应参数**：审核/举报状态

---

## 4. 典型业务流程示例
- 分享内容 -> 点赞/评论/收藏 -> 参与排行榜 -> 获得互动奖励 -> 举报/审核内容

---

## 5. 变更历史
- 2025-05-12 初稿 by AI 