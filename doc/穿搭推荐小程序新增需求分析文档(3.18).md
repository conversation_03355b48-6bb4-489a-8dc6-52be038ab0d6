# 穿搭推荐小程序新增需求分析文档（2025.3.20更新）

## 一、首页功能模块

### 1. 天气与五行命理信息模块
- **核心调整**：减小天气信息占比，突出五行命理特色
- **具体需求**：
  - 天气信息精简至仅保留天气图标和必要数据
  - 增加宜忌信息，提供基于命理的生活建议和穿搭指导
  - 采用五行色系（金/白、木/青、水/黑、火/红、土/黄）和传统元素设计
  - 运势雷达图（五维指标：爱情、事业、财富、学习、健康）
  - 宜忌信息使用标签式设计，突出关键词
  - 穿搭建议区域使用分类列表，包含服饰、配饰、妆容三个方面

### 2. 今日推荐搭配
- **核心功能**：
  - 增加搭配评分系统（五星制）
  - 支持多套搭配横向滑动切换
  - 互动功能：收藏、分享、转发到朋友圈/下载相册
  - 用户互动行为影响未来推荐算法
- **详情页内容**：
  - 穿搭图片展示
  - 搭配中使用的所有单品清单
  - 五行搭配原理专业解读
  - 适合场景和场合说明
  - 评分维度：五行匹配度、场合适用度、整体协调性、气质匹配度

### 3. 案例分享
- **展示内容**：
  - 以列表形式展示其他用户分享的穿搭案例(AI生成的)
  - 每个案例包含穿搭效果图（带AI生成水印）、五行搭配说明、场景标签
  - 支持点赞、"我也试试"功能
  - "我也试试"：AI将案例穿搭替换为用户本人形象
  - 生成图片支持收藏、分享、转发功能

### 4. 拍照浮窗
- **功能描述**：
  - 添加全局浮窗按钮，设计融入中国传统元素
  - 一键调用拍照功能，上传当前穿搭
  - AI分析维度：五行匹配度、场合适用性、整体协调性
  - 提供具体可操作的优化建议
- **应用场景举例**：
  - 服装店试穿时获取评分和购买建议
  - 出席重要场合前获取评分和调整建议

## 二、我的衣橱功能模块

### 1. 衣橱分析增强
- **数据展示**：
  - 顶部增加衣橱统计分析卡片
  - 展示数据：衣物总量、用户排名（打败x%用户）
  - 风格定位（如"洒脱自然风格"）及风格特点解读
- **界面优化**：
  - 衣物展示采用三列网格布局
  - 简化单品展示信息，移除类型标签
  - 移除单品右下角的五行标签
  - 将使用频率标签（如"常穿"、"百搭"）移至标题行
  - 分类标签添加图标并显示数量统计
  - 统一分类标签的字体大小和颜色

## 三、搭配功能模块

### 1. 日历搭配与自定义条件融合系统
- **核心思路**：
  - 将日期作为搭配推荐的核心参数，整合自定义条件与日历规划功能
  - 不同日期的天气预报、五行命理影响会自动纳入推荐算法
- **功能描述**：
  - 主界面采用日历视图设计，直观展示每日搭配状态
  - 用户选择特定日期后，可设置该日的场景、场合、风格等条件
  - 日历上已规划搭配日期用小图标标记，便于快速识别
  - 支持批量规划模式：为一周或假期等时间段设置统一条件
  - 系统自动获取所选日期的天气预报和五行命理信息，作为核心推荐依据
- **搭配条件维度**：
  - 基础条件：场合、活动类型、风格偏好、必选单品
  - 五行条件：特定日期的命理优化建议（系统自动生成）
  - 天气条件：温度、降水、风力等（系统自动获取）
  - 特殊条件：重要场合优先级、舒适度偏好等
- **界面设计**：
  - 上部：日历视图，可横向滑动切换月份
  - 中部：选定日期的天气与命理简报
  - 下部：自定义条件设置区域
  - 底部：生成搭配按钮
- **交互流程**：
  - 选择日期 → 查看天气命理简报 → 设置自定义条件 → 生成搭配方案 → 保存至日历
  - 支持快速复制上一日设置，减少重复操作
  - 提供常用条件模板（如"工作日"、"周末休闲"、"重要会议"等）

### 2. 搭配规划高级功能
- **活动穿搭规划**：
  - 支持导入日历中的活动，自动提供穿搭建议
  - 对连续多日活动（如旅行）提供整体协调的穿搭方案
  - 考虑行李箱空间限制，优化多日穿搭单品复用率
- **穿搭提醒系统**：
  - 每晚推送次日穿搭建议，附带天气提示
  - 特殊日期（如重要会议）提前多日提醒准备
  - 反馈闭环：记录用户实际穿搭选择，持续优化推荐

### 3. 搭配历史与数据分析
- **穿搭日记**：
  - 记录每日实际穿着与搭配满意度
  - 生成个人穿搭风格报告
  - 分析穿着频率，识别高效单品与闲置单品
- **季节性回顾**：
  - 季度搭配总结，提供风格趋势分析
  - 识别季节性搭配模式，辅助下季度规划
  - 建议需添加或更新的单品类别

## 四、个人中心功能模块

### 1. 个人形象展示
- **功能描述**：
  - 个人形象图片占据整个卡片宽度，保持等比例缩放
  - 基础信息面板悬浮在左上角
  - 功能入口以图标形式竖向排列在右侧
  - 简化界面，去除多余文字说明
- **信息展示**：
  - 基础信息包含身高、体重、胸围、年龄等数据
  - 支持快速修改个人信息
  - 功能入口包含更换性别、拍照检测、造型优化等

### 2. 功能菜单优化
- **结构调整**：
  - 移除功能入口标题
  - 功能图标采用圆形设计
  - 保持视觉层次简洁明确
  - 确保关键功能易于访问
- **统一性要求**：
  - 所有页面使用相同的底部导航栏设计
  - 保持页面间的视觉一致性

## 五、实施优先级规划

### 第一阶段（1个月）：命理特色打造
1. 天气与五行命理信息模块优化
2. 搭配推荐系统的五行算法完善
3. 命理主题视觉设计系统建立

### 第二阶段（1-2个月）：核心功能完善
1. 今日推荐搭配升级（多套展示与评分系统）
2. AI生成的穿搭案例展示
3. 拍照浮窗功能实现

### 第三阶段（2-3个月）：体验优化
1. 衣橱分析功能增强
2. 自定义搭配条件与日历规划
3. 个人中心改版

## 六、注意事项

1. **命理元素应用**：
   - 作为产品核心特色突出展示
   - 保持专业性与文化底蕴
   - 视觉设计融入传统元素同时保持现代感

2. **AI生成内容规范**：
   - 所有AI生成图片添加明显但不影响视觉效果的水印
   - 确保生成质量的稳定性和真实感
   - 优化生成速度和用户交互体验

3. **界面布局优化**：
   - 采用渐进式展示方式
   - 重要功能放置在易触达位置
   - 相关功能进行合理分组
   - 保持界面简洁清晰

