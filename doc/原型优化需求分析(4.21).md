# 原型优化需求分析（4.21）

> 本文档基于《原型优化需求(4.21).md》，对所有优化项进行结构化归纳，并与《穿搭推荐小程序产品需求分析.md》《穿搭推荐小程序新增需求分析文档(3.21).md》《穿搭推荐小程序新增需求分析文档(3.18).md》等主需求文档逐条比对，标注冲突或需同步项。建议后续原型/设计/开发以本优化分析为基线。

---

## 1. 字段与数据结构优化

### 1.1 个人信息字段细化
- 字段包括：腰型、肚腩、臀型、胯型、肩膀、臂长、臂围、胯部、大腿、小腿、上下身粗、上下身长
- 每个字段有五个值，每两个中间有个中间值（如：细、正常、粗等）
- 枚举值详见原型优化文档
- 【冲突/需同步】：主需求文档仅提"身材数据"或"基础信息"，未细化字段与枚举，建议主需求文档同步更新或引用本优化文档

### 1.2 字段枚举值标准化
- 详见原型优化文档18条及后续字段枚举
- 【冲突/需同步】：主需求文档未覆盖，建议同步

---

## 2. 页面结构与交互优化

### 2.1 个人信息页面结构
- 拆分为两页，能量信息独立展示
- 上面文字去掉，底部间距收窄
- 【冲突/需同步】：主需求文档未体现，建议同步

### 2.2 能量详情页
- 能量值展示同首页，今日属性与日期合并，不再左右分布
- 完整报告左上角日期去掉，最下方两个按钮去掉，吉运提升建议上移
- 【冲突/需同步】：主需求文档未体现，建议同步

### 2.3 首页能量板块
- 灰色字颜色适配，视觉优化
- 【冲突/需同步】：主需求文档未体现，建议同步

### 2.4 风格诊断
- 搭配推荐去掉，仅推荐两种风格，每种下设两个分类
- 【冲突/需同步】：主需求文档未体现，建议同步

---

## 3. 视觉与组件规范

### 3.1 下拉选择器优化
- 按钮宽度、溢出处理、编辑状态切换等详见原型优化文档5.1、5.2节
- 【冲突/需同步】：主需求文档未体现，建议同步

### 3.2 五行元素视觉系统
- 统一色彩、样式、交互，详见原型优化文档4.1、4.2节
- 【冲突/需同步】：主需求文档未体现，建议同步

---

## 4. 未修改/待定项与后续建议
- 进入小程序衣橱动效、字号调整、相生相克图样式、趋势图缩短、编辑状态按钮、体型入口调整、传统样式、登录后展示更多字段等，详见原型优化文档"未修改"部分
- 【冲突/需同步】：主需求文档多未覆盖，建议作为后续优化建议单独追踪

---

## 5. 维护建议
- 本文档为原型优化需求的结构化归纳与主需求文档的补充，后续所有原型/设计/开发需以本分析为基线。
- 建议主需求文档同步更新或引用本优化文档，避免需求割裂。 