# StylishLink 微服务开发规范

基于现有gateway、user、ai、file等服务的框架分析，形成统一的微服务开发规范。

## 目录
- [项目概述](#项目概述)
- [技术栈规范](#技术栈规范)
- [项目结构规范](#项目结构规范)
- [依赖管理规范](#依赖管理规范)
- [命名规范](#命名规范)
- [代码结构规范](#代码结构规范)
- [配置文件规范](#配置文件规范)
- [API设计规范](#api设计规范)
- [数据库规范](#数据库规范)
- [部署规范](#部署规范)
- [开发流程](#开发流程)

---

## 项目概述

**项目名称**: StylishLink - 时尚穿搭推荐系统  
**架构模式**: Spring Cloud微服务架构  
**Java版本**: Java 17  
**Spring Boot版本**: 3.2.2  
**Spring Cloud版本**: 2023.0.0  

---

## 技术栈规范

### 核心框架
- **Java**: 17 (LTS版本)
- **Spring Boot**: 3.2.2
- **Spring Cloud**: 2023.0.0
- **服务发现**: Nacos 2.2.0 (替代Eureka)
- **配置中心**: Nacos Config
- **API网关**: Spring Cloud Gateway
- **负载均衡**: Spring Cloud LoadBalancer

### 数据存储
- **关系型数据库**: MySQL 8.0+
- **文档数据库**: MongoDB 4.4+ (特定场景)
- **ORM框架**: MyBatis Plus 3.5.5 / Spring Data MongoDB
- **缓存**: Redis + Redisson 3.26.0
- **对象存储**: 腾讯云COS / 阿里云OSS

### 核心工具库
- **Lombok**: 1.18.30 (简化代码)
- **MapStruct**: 1.5.5.Final (对象映射)
- **Apache Commons**: Lang3, Collections4, IO
- **Google Guava**: 32.1.3-jre (集合工具)
- **Hutool**: 5.8.25 (Java工具库)
- **Jackson**: JSON序列化/反序列化
- **JWT**: 0.12.5 (认证令牌)

### 监控与文档
- **Spring Actuator**: 应用监控和健康检查
- **SpringDoc OpenAPI**: 3.x (API文档生成)
- **Micrometer**: 指标监控

### 特殊技术
- **AI服务**: Spring AI + DeepSeek模型
- **图片处理**: Thumbnailator 0.4.20
- **文件类型检测**: Apache Tika 2.9.1
- **HTTP客户端**: WebFlux (响应式)

---

## 项目结构规范

### 父项目结构
```
backend/
├── pom.xml                    # 父POM，统一依赖版本管理
├── common/                    # 公共模块(实体、工具、异常等)
├── gateway-service/           # API网关服务
├── user-service/              # 用户服务
├── wardrobe-service/          # 衣橱管理服务
├── ai-service/                # AI推理服务
├── file-service/              # 文件存储服务
├── recommendation-service/    # 推荐算法服务
├── operation-service/         # 运营管理服务
├── social-service/            # 社交功能服务
├── payment-service/           # 支付服务
└── eureka-server/            # 服务注册中心(备用)
```

### 微服务内部标准结构
```
{service-name}/
├── pom.xml                    # 项目依赖配置
├── deploy.sh                  # 部署脚本
├── start.sh                   # 启动脚本  
├── README.md                  # 服务说明文档
└── src/
    ├── main/
    │   ├── java/
    │   │   └── com/stylishlink/{service}/
    │   │       ├── {Service}Application.java    # 启动类
    │   │       ├── controller/                  # 控制器层
    │   │       ├── service/                     # 服务层
    │   │       │   └── impl/                    # 服务实现类
    │   │       ├── repository/                  # 数据访问层
    │   │       ├── entity/                      # 实体类
    │   │       ├── dto/                         # 数据传输对象
    │   │       │   ├── request/                 # 请求DTO
    │   │       │   └── response/                # 响应DTO
    │   │       ├── config/                      # 配置类
    │   │       ├── exception/                   # 异常处理
    │   │       ├── enums/                       # 枚举定义
    │   │       ├── client/                      # 外部服务客户端(Feign)
    │   │       └── mapper/                      # 对象映射器
    │   └── resources/
    │       ├── application.yml                  # 主配置文件
    │       └── db/                              # 数据库脚本
    │           └── mysql-init.sql               # MySQL初始化脚本
    └── test/                                    # 测试代码
        └── java/
```

---

## 依赖管理规范

### 父POM版本管理
所有版本在父POM的`<properties>`中统一管理：

```xml
<properties>
    <!-- Java和编码 -->
    <java.version>17</java.version>
    <maven.compiler.source>17</maven.compiler.source>
    <maven.compiler.target>17</maven.compiler.target>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    
    <!-- 核心框架版本 -->
    <spring-boot.version>3.2.2</spring-boot.version>
    <spring-cloud.version>2023.0.0</spring-cloud.version>
    
    <!-- 数据库版本 -->
    <mybatis-plus.version>3.5.5</mybatis-plus.version>
    <mysql.version>8.3.0</mysql.version>
    <mongodb-driver.version>4.11.1</mongodb-driver.version>
    
    <!-- 工具库版本 -->
    <lombok.version>1.18.30</lombok.version>
    <mapstruct.version>1.5.5.Final</mapstruct.version>
    <hutool.version>5.8.25</hutool.version>
    <guava.version>32.1.3-jre</guava.version>
    
    <!-- 安全和缓存 -->
    <jjwt.version>0.12.5</jjwt.version>
    <redisson.version>3.26.0</redisson.version>
</properties>
```

### 子项目POM标准格式
```xml
<?xml version="1.0" encoding="UTF-8"?>
<project>
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.stylishlink</groupId>
        <artifactId>stylishlink-parent</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>stylishlink-{service-name}-service</artifactId>
    <name>StylishLink - {Service Name} Service</name>
    <description>{服务功能描述}</description>

    <dependencies>
        <!-- 基础依赖组合 -->
    </dependencies>

    <build>
        <plugins>
            <!-- 标准构建插件 -->
        </plugins>
    </build>
</project>
```

### 标准依赖组合

**通用Web服务依赖**:
```xml
<!-- Spring Boot基础 -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-web</artifactId>
</dependency>
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-validation</artifactId>
</dependency>
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-actuator</artifactId>
</dependency>

<!-- Nacos服务发现和配置 -->
<dependency>
    <groupId>com.alibaba.cloud</groupId>
    <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
    <version>2022.0.0.0</version>
</dependency>
<dependency>
    <groupId>com.alibaba.cloud</groupId>
    <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
    <version>2022.0.0.0</version>
</dependency>

<!-- 公共模块 -->
<dependency>
    <groupId>com.stylishlink</groupId>
    <artifactId>stylishlink-common</artifactId>
    <version>${project.version}</version>
</dependency>

<!-- API文档 -->
<dependency>
    <groupId>org.springdoc</groupId>
    <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
    <version>2.2.0</version>
</dependency>
```

**数据库相关依赖**:
```xml
<!-- MySQL + MyBatis Plus -->
<dependency>
    <groupId>com.mysql</groupId>
    <artifactId>mysql-connector-j</artifactId>
</dependency>
<dependency>
    <groupId>com.baomidou</groupId>
    <artifactId>mybatis-plus-boot-starter</artifactId>
</dependency>

<!-- Redis缓存 -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-data-redis</artifactId>
</dependency>
```

**网关特有依赖**:
```xml
<!-- Spring Cloud Gateway -->
<dependency>
    <groupId>org.springframework.cloud</groupId>
    <artifactId>spring-cloud-starter-gateway</artifactId>
</dependency>

<!-- 响应式Redis -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-data-redis-reactive</artifactId>
</dependency>

<!-- WebFlux文档 -->
<dependency>
    <groupId>org.springdoc</groupId>
    <artifactId>springdoc-openapi-starter-webflux-ui</artifactId>
    <version>2.5.0</version>
</dependency>
```

### API网关路由配置规范
网关需要配置对应的路由规则，将不同服务前缀的请求路由到对应的微服务：

```yaml
spring:
  cloud:
    gateway:
      routes:
        # 用户服务路由
        - id: user-service
          uri: lb://user-service
          predicates:
            - Path=/api/user/**
          filters:
            - RewritePath=/api/user/(?<segment>.*), /$\{segment}
            
        # 衣橱服务路由  
        - id: wardrobe-service
          uri: lb://wardrobe-service
          predicates:
            - Path=/api/wardrobe/**
          filters:
            - RewritePath=/api/wardrobe/(?<segment>.*), /$\{segment}
            
        # AI服务路由
        - id: ai-service
          uri: lb://ai-service
          predicates:
            - Path=/api/ai/**
          filters:
            - RewritePath=/api/ai/(?<segment>.*), /$\{segment}
            
        # 文件服务路由
        - id: file-service
          uri: lb://file-service
          predicates:
            - Path=/api/file/**
          filters:
            - RewritePath=/api/file/(?<segment>.*), /$\{segment}
```

#### 路由说明
- **外部访问**: 客户端调用 `/api/{service}/{resource}`
- **网关路由**: 自动转发到对应的微服务
- **服务内部**: Controller实际监听 `/{resource}` 路径

例如：
- 客户端调用: `GET /api/user/users/123`
- 网关路由到: `user-service` 的 `GET /users/123`
- Controller映射: `@RequestMapping("/users")` + `@GetMapping("/{id}")`

---

## 命名规范

### 项目和模块命名
- **父项目**: `stylishlink-parent`
- **公共模块**: `stylishlink-common`
- **微服务**: `stylishlink-{service-name}-service`
  - 示例: `stylishlink-user-service`, `stylishlink-wardrobe-service`

### 包命名
- **根包**: `com.stylishlink.{service}`
- **示例**: `com.stylishlink.user`, `com.stylishlink.wardrobe`, `com.stylishlink.gateway`

### 类命名规范

#### 启动类
- **格式**: `{Service}ServiceApplication`
- **示例**: `UserServiceApplication`, `WardrobeServiceApplication`, `GatewayServiceApplication`

#### Controller类
- **格式**: `{Business}Controller`
- **示例**: `UserController`, `ClothingController`, `OutfitController`, `FileController`

#### Service类
- **接口**: `{Business}Service`
- **实现**: `{Business}ServiceImpl`
- **示例**: `UserService/UserServiceImpl`, `WardrobeAnalysisService/WardrobeAnalysisServiceImpl`

#### Repository/Mapper类
- **MyBatis Plus**: `{Entity}Repository` (继承BaseMapper)
- **MongoDB**: `{Entity}Repository` (继承MongoRepository)
- **示例**: `UserRepository`, `ClothingRepository`, `WearRecordRepository`

#### Entity/实体类
- **格式**: `{Entity}`
- **示例**: `User`, `Clothing`, `Outfit`, `WearRecord`, `WardrobeAnalysis`

#### DTO类
- **请求**: `{Action}{Entity}Request`
- **响应**: `{Entity}Response` 或 `{Action}{Entity}Response`
- **示例**: 
  - 请求: `CreateUserRequest`, `UpdateProfileRequest`, `WearingRecordRequest`
  - 响应: `UserResponse`, `LoginResponse`, `WearingHistoryResponse`

#### Configuration类
- **格式**: `{Technology}Config`
- **示例**: `RedisConfig`, `MybatisPlusConfig`, `SecurityConfig`, `NacosConfig`

#### Exception类
- **格式**: `{Business}Exception` 或 `{Error}Exception`
- **示例**: `UserNotFoundException`, `InvalidPasswordException`, `FileUploadException`

### 方法命名规范

#### Controller方法
- **创建**: `create{Entity}()`, `add{Entity}()`
- **删除**: `delete{Entity}()`, `remove{Entity}()`
- **更新**: `update{Entity}()`, `modify{Entity}()`
- **查询**: `get{Entity}()`, `list{Entity}()`, `search{Entity}()`

#### Service方法
- **创建**: `create{Entity}()`, `save{Entity}()`
- **删除**: `delete{Entity}ById()`, `remove{Entity}()`
- **更新**: `update{Entity}()`, `modify{Entity}()`
- **查询**: `get{Entity}ById()`, `find{Entity}By{Condition}()`, `list{Entity}()`

#### Repository方法 (MyBatis Plus)
- **查询**: `select{Entity}By{Condition}()`, `selectByUserId()`
- **统计**: `countBy{Condition}()`, `countByUserId()`
- **存在性检查**: `existsBy{Condition}()`, `existsByIdAndUserId()`

---

## 代码结构规范

### Controller层标准结构
```java
@RestController
@RequestMapping("/{entities}")  // 服务内部路径，外部通过网关访问 /api/{service}/{entities}
@RequiredArgsConstructor
@Slf4j
@Tag(name = "{Entity} API", description = "{实体}相关接口")
public class {Entity}Controller {
    
    private final {Entity}Service {entity}Service;
    
    @PostMapping
    @Operation(summary = "创建{实体}")
    public ApiResponse<{Entity}Response> create{Entity}(@Valid @RequestBody Create{Entity}Request request) {
        {Entity}Response response = {entity}Service.create{Entity}(request);
        return ApiResponse.success(response);
    }
    
    @GetMapping("/{id}")
    @Operation(summary = "获取{实体}详情")
    public ApiResponse<{Entity}Response> get{Entity}(@PathVariable String id) {
        {Entity}Response response = {entity}Service.get{Entity}ById(id);
        return ApiResponse.success(response);
    }
    
    @PutMapping("/{id}")
    @Operation(summary = "更新{实体}")
    public ApiResponse<{Entity}Response> update{Entity}(
            @PathVariable String id, 
            @Valid @RequestBody Update{Entity}Request request) {
        {Entity}Response response = {entity}Service.update{Entity}(id, request);
        return ApiResponse.success(response);
    }
    
    @DeleteMapping("/{id}")
    @Operation(summary = "删除{实体}")
    public ApiResponse<Void> delete{Entity}(@PathVariable String id) {
        {entity}Service.delete{Entity}ById(id);
        return ApiResponse.success(null);
    }
}

// 具体示例 - 用户服务内的用户控制器 (user-service)
@RestController
@RequestMapping("/users")  // 服务内部路径，外部通过网关访问 /api/user/users
@RequiredArgsConstructor
@Slf4j
@Tag(name = "User API", description = "用户管理相关接口")
public class UserController {
    
    private final UserService userService;
    
    @PostMapping("/register")  // 外部访问: POST /api/user/users/register
    public ApiResponse<UserResponse> register(@Valid @RequestBody RegisterRequest request) {
        // 注册逻辑
    }
    
    @PostMapping("/login")  // 外部访问: POST /api/user/users/login
    public ApiResponse<LoginResponse> login(@Valid @RequestBody LoginRequest request) {
        // 登录逻辑
    }
    
    @GetMapping("/{id}")  // 外部访问: GET /api/user/users/{id}
    public ApiResponse<UserResponse> getUser(@PathVariable String id) {
        // 获取用户信息
    }
}

// 具体示例 - 衣橱服务内的衣物控制器 (wardrobe-service)
@RestController
@RequestMapping("/clothing")  // 服务内部路径，外部通过网关访问 /api/wardrobe/clothing
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Clothing API", description = "衣物管理相关接口")
public class ClothingController {
    
    private final ClothingService clothingService;
    
    @PostMapping  // 外部访问: POST /api/wardrobe/clothing
    public ApiResponse<ClothingResponse> addClothing(@Valid @RequestBody AddClothingRequest request) {
        // 添加衣物
    }
    
    @GetMapping  // 外部访问: GET /api/wardrobe/clothing
    public ApiResponse<PageResponse<ClothingResponse>> listClothing(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String category) {
        // 分页查询衣物
    }
}
```

### Service层标准结构
```java
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional(readOnly = true)
public class {Entity}ServiceImpl implements {Entity}Service {
    
    private final {Entity}Repository {entity}Repository;
    
    @Override
    @Transactional
    public {Entity}Response create{Entity}(Create{Entity}Request request) {
        log.info("创建{实体}: {}", request);
        
        try {
            // 业务逻辑实现
            {Entity} entity = new {Entity}();
            // 设置属性...
            
            {entity}Repository.insert(entity);
            
            log.info("{实体}创建成功: {}", entity.getId());
            return convert{Entity}ToResponse(entity);
            
        } catch (Exception e) {
            log.error("创建{实体}失败: {}", request, e);
            throw new BusinessException("创建{实体}失败: " + e.getMessage());
        }
    }
    
    @Override
    public {Entity}Response get{Entity}ById(String id) {
        {Entity} entity = {entity}Repository.selectById(id);
        if (entity == null) {
            throw new {Entity}NotFoundException("未找到ID为 " + id + " 的{实体}");
        }
        return convert{Entity}ToResponse(entity);
    }
    
    private {Entity}Response convert{Entity}ToResponse({Entity} entity) {
        // 对象转换逻辑
        return {Entity}Response.builder()
            .id(entity.getId())
            .createdAt(entity.getCreatedAt())
            .build();
    }
}
```

### Entity层标准结构
**MyBatis Plus实体**:
```java
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("{table_name}")
public class {Entity} {
    
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    @TableField("user_id")
    @NotNull(message = "用户ID不能为空")
    private Long userId;
    
    @TableField("name")
    @NotBlank(message = "名称不能为空")
    private String name;
    
    @TableField("description")
    private String description;
    
    @TableField("status")
    private Integer status;
    
    @TableField(value = "created_by", fill = FieldFill.INSERT)
    private Long createdBy;
    
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;
    
    @TableField(value = "updated_by", fill = FieldFill.INSERT_UPDATE)
    private Long updatedBy;
    
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;
}
```

### 基础实体类（建议）
为了减少重复代码，建议创建基础实体类：
```java
@Data
@EqualsAndHashCode(callSuper = false)
public abstract class BaseEntity {
    
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    @TableField("status")
    private Integer status;
    
    @TableField(value = "created_by", fill = FieldFill.INSERT)
    private Long createdBy;
    
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;
    
    @TableField(value = "updated_by", fill = FieldFill.INSERT_UPDATE)
    private Long updatedBy;
    
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;
}

// 具体实体继承基础类
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("{table_name}")
public class {Entity} extends BaseEntity {
    
    @TableField("user_id")
    @NotNull(message = "用户ID不能为空")
    private Long userId;
    
    @TableField("name")
    @NotBlank(message = "名称不能为空")
    private String name;
    
    // 其他业务字段...
}
```

### Repository层标准结构
```java
@Mapper
public interface {Entity}Repository extends BaseMapper<{Entity}> {
    
    /**
     * 根据用户ID查询{实体}列表（只查询正常状态）
     */
    @Select("SELECT * FROM {table_name} WHERE user_id = #{userId} AND status = 1")
    List<{Entity}> selectByUserId(@Param("userId") Long userId);
    
    /**
     * 分页查询用户的{实体}（只查询正常状态）
     */
    @Select("SELECT * FROM {table_name} WHERE user_id = #{userId} AND status = 1 ORDER BY created_at DESC")
    Page<{Entity}> selectByUserId(Page<{Entity}> page, @Param("userId") Long userId);
    
    /**
     * 统计用户{实体}数量（只统计正常状态）
     */
    @Select("SELECT COUNT(*) FROM {table_name} WHERE user_id = #{userId} AND status = 1")
    int countByUserId(@Param("userId") Long userId);
    
    /**
     * 检查{实体}是否属于指定用户且状态正常
     */
    @Select("SELECT COUNT(*) > 0 FROM {table_name} WHERE id = #{id} AND user_id = #{userId} AND status = 1")
    boolean existsByIdAndUserId(@Param("id") Long id, @Param("userId") Long userId);
    
    /**
     * 根据状态查询{实体}列表
     */
    @Select("SELECT * FROM {table_name} WHERE status = #{status}")
    List<{Entity}> selectByStatus(@Param("status") Integer status);
}
```

---

## 配置文件规范

### application.yml标准结构
本地配置文件保持简洁，主要配置放在Nacos：
```yaml
spring:
  application:
    name: {service-name}
  config:
    import: nacos:${spring.application.name}
  cloud:
    nacos:
      discovery:
        server-addr: ************:8848
        namespace: public
        enabled: true
      config:
        server-addr: ************:8848
        file-extension: yaml
        namespace: public
        group: DEFAULT_GROUP
        enabled: true
```

### Nacos配置中心标准配置
在Nacos配置中心维护详细配置：
```yaml
# 服务端口配置
server:
  port: 808{x}

# 数据源配置
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ***************************/{service}?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=false&serverTimezone=GMT%2B8
    username: ${MYSQL_USERNAME:root}
    password: ${MYSQL_PASSWORD:password}
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      auto-commit: true
      idle-timeout: 30000
      pool-name: HikariCP
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: SELECT 1
  
  # Redis配置
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:}
    timeout: 3000ms
    jedis:
      pool:
        max-active: 100
        max-idle: 10
        min-idle: 5
        max-wait: 3000ms

# MyBatis Plus配置
mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*.xml
  type-aliases-package: com.stylishlink.{service}.entity
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
  global-config:
    db-config:
      id-type: AUTO
      logic-delete-field: status
      logic-delete-value: -1
      logic-not-delete-value: 1

### 自动填充配置
需要配置MyBatis Plus的自动填充处理器：
```java
@Component
public class AuditMetaObjectHandler implements MetaObjectHandler {
    
    @Override
    public void insertFill(MetaObject metaObject) {
        Long userId = getCurrentUserId(); // 从上下文获取当前用户ID
        LocalDateTime now = LocalDateTime.now();
        
        this.strictInsertFill(metaObject, "createdBy", Long.class, userId);
        this.strictInsertFill(metaObject, "createdAt", LocalDateTime.class, now);
        this.strictInsertFill(metaObject, "updatedBy", Long.class, userId);
        this.strictInsertFill(metaObject, "updatedAt", LocalDateTime.class, now);
    }
    
    @Override
    public void updateFill(MetaObject metaObject) {
        Long userId = getCurrentUserId(); // 从上下文获取当前用户ID
        LocalDateTime now = LocalDateTime.now();
        
        this.strictUpdateFill(metaObject, "updatedBy", Long.class, userId);
        this.strictUpdateFill(metaObject, "updatedAt", LocalDateTime.class, now);
    }
    
    private Long getCurrentUserId() {
        // 从UserContext获取当前用户ID
        return UserContextHolder.getCurrentUserId();
    }
}
```

# 日志配置
logging:
  level:
    com.stylishlink.{service}: DEBUG
    com.baomidou.mybatisplus: DEBUG
  pattern:
    console: '%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n'

# Actuator配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always
```

---

## API设计规范

### RESTful API路径规范
- **基础路径**: `/api/{service-name}/{resources}`
- **服务名**: 小写，与微服务名对应
- **资源命名**: 复数形式，小写

#### 服务路径映射表
| 微服务 | API路径前缀 | 说明 |
|--------|-------------|------|
| user-service | `/api/user/` | 用户认证、个人信息管理 |
| wardrobe-service | `/api/wardrobe/` | 衣橱管理、衣物搭配 |
| ai-service | `/api/ai/` | AI分析、推荐算法 |
| file-service | `/api/file/` | 文件上传、图片处理 |
| recommendation-service | `/api/recommendation/` | 穿搭推荐 |
| operation-service | `/api/operation/` | 运营管理 |
| social-service | `/api/social/` | 社交功能 |
| payment-service | `/api/payment/` | 支付功能 |

#### API路径示例
- **用户服务**: 
  - `/api/user/auth/login` - 用户登录
  - `/api/user/users/{id}` - 用户信息
  - `/api/user/profile` - 个人资料
- **衣橱服务**: 
  - `/api/wardrobe/clothing` - 衣物管理
  - `/api/wardrobe/outfits` - 搭配管理
  - `/api/wardrobe/analysis` - 衣橱分析
- **AI服务**: 
  - `/api/ai/analysis/style` - 风格分析
  - `/api/ai/recognition/clothing` - 服装识别
- **文件服务**: 
  - `/api/file/upload` - 文件上传
  - `/api/file/images/{id}` - 图片访问

### HTTP方法使用规范
- **GET**: 查询资源(幂等)
- **POST**: 创建资源(非幂等)
- **PUT**: 更新资源(幂等，全量更新)
- **PATCH**: 部分更新资源(幂等)
- **DELETE**: 删除资源(幂等)

### 统一响应格式
**重要**: 所有服务必须使用common包中的`ApiResponse<T>`和`PageResponse<T>`，不允许自定义响应格式。

```java
// ✅ 正确使用方式 - 引用common包
import com.stylishlink.common.dto.ApiResponse;
import com.stylishlink.common.dto.PageResponse;

@RestController
public class ExampleController {
    
    @GetMapping("/example")
    public ApiResponse<ExampleResponse> getExample() {
        ExampleResponse data = exampleService.getExample();
        return ApiResponse.success(data);
    }
    
    @GetMapping("/examples")
    public ApiResponse<PageResponse<ExampleResponse>> listExamples() {
        PageResponse<ExampleResponse> data = exampleService.listExamples();
        return ApiResponse.success(data);
    }
}
```

### OpenAPI文档规范
每个Controller都要添加完整的API文档：
```java
@Tag(name = "用户管理", description = "用户相关的API接口")
@RestController
public class UserController {
    
    @Operation(summary = "用户注册", description = "创建新用户账户")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "注册成功"),
        @ApiResponse(responseCode = "400", description = "参数错误"),
        @ApiResponse(responseCode = "409", description = "用户已存在")
    })
    @PostMapping("/register")
    public ApiResponse<UserResponse> register(@Valid @RequestBody RegisterRequest request) {
        // 实现逻辑
    }
}
```

---

## 数据库规范

### 数据库命名规范
- **数据库名**: `{service}`
- **格式**: 直接使用服务名，小写字母

#### 服务数据库映射表
| 微服务 | 数据库名 | 说明 |
|--------|----------|------|
| user-service | `user` | 用户信息、认证数据 |
| wardrobe-service | `wardrobe` | 衣橱、衣物、搭配数据 |
| ai-service | `ai` | AI模型配置、分析记录 |
| file-service | `file` | 文件信息、上传记录 |
| recommendation-service | `recommendation` | 推荐算法、用户偏好 |
| operation-service | `operation` | 运营数据、统计信息 |
| social-service | `social` | 社交关系、互动数据 |
| payment-service | `payment` | 支付订单、交易记录 |

### 表命名规范
- **格式**: `{service}_` + 表名，小写字母 + 下划线，复数形式
- **示例**: `user_users`, `wardrobe_clothing`, `wardrobe_accessories`, `wardrobe_outfits`, `wardrobe_wear_records`
- **说明**: 使用服务名前缀可以：
  - 避免不同微服务之间的表名冲突
  - 明确标识表属于哪个服务，便于数据库管理
  - 符合微服务架构的数据隔离原则

### 字段命名规范
- **格式**: 小写字母 + 下划线
- **主键**: `id` (BIGINT AUTO_INCREMENT)
- **外键**: `{table}_id` (如: `user_id`, `clothing_id`)
- **必需审计字段**:
  - `created_by` (BIGINT, 创建人ID)
  - `created_at` (TIMESTAMP DEFAULT CURRENT_TIMESTAMP, 创建时间)
  - `updated_by` (BIGINT, 更新人ID)
  - `updated_at` (TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, 更新时间)
- **状态字段**: `status` (TINYINT DEFAULT 1, 状态：1-正常 0-禁用 -1-删除)
- **逻辑删除**: 使用`status = -1`表示删除，不单独设置deleted字段

### 索引命名规范
- **主键索引**: `PRIMARY KEY (id)`
- **普通索引**: `idx_{field_name}` (如: `idx_user_id`)
- **复合索引**: `idx_{field1}_{field2}` (如: `idx_user_category`)
- **唯一索引**: `uk_{field_name}` (如: `uk_username`)

### 建表SQL规范
```sql
CREATE TABLE `{service}_{table_name}` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `user_id` BIGINT NOT NULL COMMENT '用户ID',
    `name` VARCHAR(255) NOT NULL COMMENT '名称',
    `description` TEXT COMMENT '描述',
    `status` TINYINT DEFAULT 1 COMMENT '状态(1:正常 0:禁用 -1:删除)',
    `created_by` BIGINT NOT NULL COMMENT '创建人ID',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_by` BIGINT NOT NULL COMMENT '更新人ID',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_status` (`status`),
    INDEX `idx_created_by` (`created_by`),
    INDEX `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='{表中文说明}';
```

### 状态字段值规范
- **1**: 正常/启用状态
- **0**: 禁用状态  
- **-1**: 删除状态（逻辑删除）

### 审计字段说明
- **created_by**: 记录创建该数据的用户ID
- **created_at**: 记录创建时间，数据库自动设置
- **updated_by**: 记录最后更新该数据的用户ID
- **updated_at**: 记录最后更新时间，数据库自动更新

---

## 部署规范

### 端口分配标准
| 服务 | 端口 | 说明 |
|------|------|------|
| Gateway Service | 8080 | API网关 |
| User Service | 8081 | 用户服务 |
| Wardrobe Service | 8082 | 衣橱服务 |
| AI Service | 8083 | AI服务 |
| File Service | 8084 | 文件服务 |
| Recommendation Service | 8085 | 推荐服务 |
| Operation Service | 8086 | 运营服务 |
| Social Service | 8087 | 社交服务 |
| Payment Service | 8088 | 支付服务 |

### 部署脚本规范
每个服务必须包含以下脚本：

**deploy.sh** - 部署脚本:
```bash
#!/bin/bash
# 部署{service-name}服务

SERVICE_NAME="{service-name}"
JAR_NAME="{service-name}-1.0.0-SNAPSHOT.jar"
PID_FILE="${SERVICE_NAME}.pid"

# 停止现有服务
if [ -f $PID_FILE ]; then
    kill -9 `cat $PID_FILE`
    rm $PID_FILE
fi

# 启动新服务
nohup java -jar $JAR_NAME > $SERVICE_NAME.log 2>&1 &
echo $! > $PID_FILE

echo "$SERVICE_NAME 部署完成"
```

**start.sh** - 启动脚本:
```bash
#!/bin/bash
# 启动{service-name}服务

java -Xms512m -Xmx1024m \
     -Dspring.profiles.active=prod \
     -jar {service-name}-1.0.0-SNAPSHOT.jar
```

### 环境变量规范
```bash
# MySQL数据库配置
export MYSQL_USERNAME=root
export MYSQL_PASSWORD=password
export MYSQL_HOST=localhost
export MYSQL_PORT=3306

# Redis配置
export REDIS_HOST=localhost
export REDIS_PORT=6379
export REDIS_PASSWORD=

# Nacos配置
export NACOS_SERVER_ADDR=************:8848

# 具体服务的数据库连接示例
# user-service: ********************************
# wardrobe-service: ************************************
# ai-service: ******************************
```

---

## 开发流程

### 新增微服务开发步骤

#### 1. 项目初始化
```bash
# 创建服务目录
mkdir backend/{service-name}-service
cd backend/{service-name}-service

# 创建标准目录结构
mkdir -p src/main/java/com/stylishlink/{service}
mkdir -p src/main/resources/db
mkdir -p src/test/java
```

#### 2. 配置POM文件
复制标准POM模板，修改以下内容：
- `<artifactId>stylishlink-{service-name}-service</artifactId>`
- `<name>StylishLink - {Service Name} Service</name>`
- `<description>{服务功能描述}</description>`
- 添加特定的依赖项

#### 3. 创建启动类
```java
@SpringBootApplication
@EnableDiscoveryClient
@EnableOpenApi  // 启用API文档
public class {Service}ServiceApplication {
    public static void main(String[] args) {
        SpringApplication.run({Service}ServiceApplication.class, args);
    }
}
```

#### 4. 配置文件
- 创建`application.yml`（使用标准模板）
- 在Nacos配置中心添加详细配置

#### 5. 数据库初始化
- 创建数据库：`CREATE DATABASE {service} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;`
- 创建`src/main/resources/db/mysql-init.sql`
- 定义表结构、索引、初始数据

**数据库创建示例**：
```sql
-- 创建用户服务数据库
CREATE DATABASE `user` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建衣橱服务数据库  
CREATE DATABASE `wardrobe` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建AI服务数据库
CREATE DATABASE `ai` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

#### 6. 实现业务功能
按照分层架构实现：
- Entity → Repository → Service → Controller
- 遵循命名规范和代码结构规范

#### 7. 添加到父项目
在父POM的`<modules>`中添加新模块：
```xml
<modules>
    <module>{service-name}-service</module>
</modules>
```

---

## 权限验证与服务间调用规范

### 网关层权限验证
网关作为统一入口，负责JWT令牌验证和用户信息传递：

#### JWT过滤器配置
```java
@Component
public class JwtAuthenticationFilter implements GlobalFilter, Ordered {
    
    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        String token = extractToken(request);
        
        if (token != null && validateToken(token)) {
            // 解析JWT获取用户信息
            Claims claims = parseToken(token);
            Long userId = claims.get("userId", Long.class);
            String username = claims.get("username", String.class);
            String roles = claims.get("roles", String.class);
            
            // 设置用户信息到请求头
            ServerHttpRequest mutatedRequest = request.mutate()
                .header("X-User-Id", String.valueOf(userId))
                .header("X-Username", username)
                .header("X-User-Roles", roles)
                .header("X-Request-Source", "gateway")
                .build();
                
            return chain.filter(exchange.mutate().request(mutatedRequest).build());
        }
        
        // 未认证请求返回401
        return handleUnauthorized(exchange);
    }
}
```

#### 网关设置的标准Header
| Header名称 | 说明 | 示例值 |
|-----------|------|--------|
| `X-User-Id` | 当前用户ID | `123456` |
| `X-Username` | 用户名 | `zhangsan` |
| `X-User-Roles` | 用户角色 | `USER,ADMIN` |
| `X-Request-Source` | 请求来源标识 | `gateway` |
| `X-Request-Id` | 请求追踪ID | `uuid` |

### 微服务权限验证
各微服务需要从Header中获取用户信息，并保持传递：

#### 用户上下文管理
```java
// 用户上下文类 - 放在common包中
@Data
@Builder
public class UserContext {
    private Long userId;
    private String username;
    private Set<String> roles;
    private String requestId;
    
    public boolean hasRole(String role) {
        return roles != null && roles.contains(role);
    }
    
    public boolean hasAnyRole(String... roles) {
        return Arrays.stream(roles).anyMatch(this::hasRole);
    }
}

// 用户上下文持有者 - 放在common包中
public class UserContextHolder {
    private static final ThreadLocal<UserContext> CONTEXT = new ThreadLocal<>();
    
    public static void setContext(UserContext context) {
        CONTEXT.set(context);
    }
    
    public static UserContext getContext() {
        return CONTEXT.get();
    }
    
    public static Long getCurrentUserId() {
        UserContext context = getContext();
        return context != null ? context.getUserId() : null;
    }
    
    public static void clear() {
        CONTEXT.remove();
    }
}
```

#### 请求拦截器
```java
// Header解析拦截器 - 各服务实现
@Component
@Slf4j
public class UserContextInterceptor implements HandlerInterceptor {
    
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        try {
            // 解析网关传递的用户信息
            String userIdStr = request.getHeader("X-User-Id");
            String username = request.getHeader("X-Username");
            String rolesStr = request.getHeader("X-User-Roles");
            String requestId = request.getHeader("X-Request-Id");
            String requestSource = request.getHeader("X-Request-Source");
            
            // 验证请求来源
            if (!"gateway".equals(requestSource)) {
                log.warn("请求未通过网关，拒绝访问");
                response.setStatus(HttpStatus.FORBIDDEN.value());
                return false;
            }
            
            if (userIdStr != null) {
                Set<String> roles = rolesStr != null ? 
                    Arrays.stream(rolesStr.split(",")).collect(Collectors.toSet()) : 
                    new HashSet<>();
                    
                UserContext context = UserContext.builder()
                    .userId(Long.valueOf(userIdStr))
                    .username(username)
                    .roles(roles)
                    .requestId(requestId)
                    .build();
                    
                UserContextHolder.setContext(context);
            }
            
            return true;
        } catch (Exception e) {
            log.error("解析用户上下文失败", e);
            response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
            return false;
        }
    }
    
    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        UserContextHolder.clear();
    }
}
```

### 服务间调用规范

#### Feign客户端配置
所有服务间调用必须使用Feign，并自动传递用户上下文：

```java
// Feign请求拦截器 - 放在common包中
@Component
public class FeignRequestInterceptor implements RequestInterceptor {
    
    @Override
    public void apply(RequestTemplate template) {
        UserContext context = UserContextHolder.getContext();
        if (context != null) {
            template.header("X-User-Id", String.valueOf(context.getUserId()));
            template.header("X-Username", context.getUsername());
            template.header("X-User-Roles", String.join(",", context.getRoles()));
            template.header("X-Request-Id", context.getRequestId());
            template.header("X-Request-Source", "feign");
        }
    }
}

// Feign配置类 - 各服务实现
@Configuration
public class FeignConfig {
    
    @Bean
    public RequestInterceptor requestInterceptor() {
        return new FeignRequestInterceptor();
    }
    
    @Bean
    public ErrorDecoder errorDecoder() {
        return new FeignErrorDecoder();
    }
}
```

#### Feign客户端示例
```java
// 用户服务客户端
@FeignClient(name = "user-service", configuration = FeignConfig.class)
public interface UserServiceClient {
    
    @GetMapping("/users/{id}")
    ApiResponse<UserResponse> getUser(@PathVariable("id") Long id);
    
    @PostMapping("/users/batch")
    ApiResponse<List<UserResponse>> getBatchUsers(@RequestBody List<Long> userIds);
}

// 文件服务客户端
@FeignClient(name = "file-service", configuration = FeignConfig.class)
public interface FileServiceClient {
    
    @PostMapping("/files/upload")
    ApiResponse<FileUploadResponse> uploadFile(@RequestBody MultipartFile file);
    
    @GetMapping("/files/{id}")
    ApiResponse<FileInfoResponse> getFileInfo(@PathVariable("id") Long id);
}
```

### 统一响应格式规范

#### ApiResponse定义（common包）
```java
// 统一响应类 - 必须放在common包中
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ApiResponse<T> {
    
    /**
     * 响应码：200-成功，其他-失败
     */
    private Integer code;
    
    /**
     * 响应消息
     */
    private String message;
    
    /**
     * 响应数据
     */
    private T data;
    
    /**
     * 时间戳
     */
    private Long timestamp;
    
    /**
     * 请求追踪ID
     */
    private String requestId;
    
    // 成功响应
    public static <T> ApiResponse<T> success(T data) {
        return ApiResponse.<T>builder()
            .code(200)
            .message("success")
            .data(data)
            .timestamp(System.currentTimeMillis())
            .requestId(getCurrentRequestId())
            .build();
    }
    
    public static <T> ApiResponse<T> success() {
        return success(null);
    }
    
    // 失败响应
    public static <T> ApiResponse<T> error(Integer code, String message) {
        return ApiResponse.<T>builder()
            .code(code)
            .message(message)
            .data(null)
            .timestamp(System.currentTimeMillis())
            .requestId(getCurrentRequestId())
            .build();
    }
    
    public static <T> ApiResponse<T> error(String message) {
        return error(500, message);
    }
    
    private static String getCurrentRequestId() {
        UserContext context = UserContextHolder.getContext();
        return context != null ? context.getRequestId() : UUID.randomUUID().toString();
    }
}
```

#### 分页响应类（common包）
```java
// 分页响应类 - 必须放在common包中
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PageResponse<T> {
    
    /**
     * 数据列表
     */
    private List<T> records;
    
    /**
     * 总记录数
     */
    private Long total;
    
    /**
     * 总页数
     */
    private Long pages;
    
    /**
     * 当前页码
     */
    private Long current;
    
    /**
     * 每页大小
     */
    private Long size;
    
    /**
     * 是否有下一页
     */
    private Boolean hasNext;
    
    /**
     * 是否有上一页
     */
    private Boolean hasPrevious;
    
    public static <T> PageResponse<T> of(IPage<T> page) {
        return PageResponse.<T>builder()
            .records(page.getRecords())
            .total(page.getTotal())
            .pages(page.getPages())
            .current(page.getCurrent())
            .size(page.getSize())
            .hasNext(page.getCurrent() < page.getPages())
            .hasPrevious(page.getCurrent() > 1)
            .build();
    }
}
```

### 必须使用Common包的组件

#### 核心公共组件列表
| 组件类型 | 类名 | 位置 | 说明 |
|---------|------|------|------|
| **响应类** | `ApiResponse<T>` | `com.stylishlink.common.dto` | 统一API响应格式 |
| **响应类** | `PageResponse<T>` | `com.stylishlink.common.dto` | 统一分页响应格式 |
| **实体基类** | `BaseEntity` | `com.stylishlink.common.entity` | 基础实体类（审计字段） |
| **用户上下文** | `UserContext` | `com.stylishlink.common.context` | 用户信息上下文 |
| **上下文持有者** | `UserContextHolder` | `com.stylishlink.common.context` | 用户上下文管理 |
| **异常类** | `BusinessException` | `com.stylishlink.common.exception` | 业务异常基类 |
| **异常类** | `SystemException` | `com.stylishlink.common.exception` | 系统异常基类 |
| **工具类** | `JwtUtils` | `com.stylishlink.common.util` | JWT工具类 |
| **常量类** | `Constants` | `com.stylishlink.common.constant` | 系统常量 |
| **枚举类** | `StatusEnum` | `com.stylishlink.common.enums` | 状态枚举 |
| **Feign拦截器** | `FeignRequestInterceptor` | `com.stylishlink.common.feign` | Feign请求拦截器 |

#### 强制使用规范
- ✅ **Controller必须使用**: `ApiResponse<T>` 和 `PageResponse<T>`
- ✅ **Entity必须继承**: `BaseEntity`
- ✅ **异常必须继承**: `BusinessException` 或 `SystemException`
- ✅ **服务调用必须使用**: Feign客户端 + `FeignRequestInterceptor`
- ✅ **用户信息必须使用**: `UserContext` 和 `UserContextHolder`

#### 使用示例
```java
// ✅ 正确使用方式
@RestController
public class UserController {
    
    @GetMapping("/users/{id}")
    public ApiResponse<UserResponse> getUser(@PathVariable Long id) {
        UserResponse user = userService.getUserById(id);
        return ApiResponse.success(user);
    }
    
    @GetMapping("/users")
    public ApiResponse<PageResponse<UserResponse>> listUsers(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size) {
        PageResponse<UserResponse> result = userService.listUsers(page, size);
        return ApiResponse.success(result);
    }
}

// ❌ 错误使用方式 - 自定义响应格式
@RestController
public class UserController {
    
    @GetMapping("/users/{id}")
    public CustomResponse<UserInfo> getUser(@PathVariable Long id) { // ❌ 不要自定义响应类
        // ...
    }
}
```

### 权限注解支持
```java
// 权限注解 - 放在common包中
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface RequiredRole {
    String[] value() default {};
    boolean requireAll() default false; // true=需要所有角色，false=需要任一角色
}

// 权限切面 - 各服务实现
@Aspect
@Component
@Slf4j
public class AuthorizationAspect {
    
    @Around("@annotation(requiredRole)")
    public Object checkPermission(ProceedingJoinPoint joinPoint, RequiredRole requiredRole) throws Throwable {
        UserContext context = UserContextHolder.getContext();
        
        if (context == null) {
            throw new BusinessException("用户未登录");
        }
        
        String[] roles = requiredRole.value();
        if (roles.length > 0) {
            boolean hasPermission = requiredRole.requireAll() ? 
                context.hasAllRoles(roles) : 
                context.hasAnyRole(roles);
                
            if (!hasPermission) {
                throw new BusinessException("权限不足");
            }
        }
        
        return joinPoint.proceed();
    }
}

 // 使用示例
 @RequiredRole({"ADMIN"})
 @DeleteMapping("/users/{id}")
 public ApiResponse<Void> deleteUser(@PathVariable Long id) {
     userService.deleteUser(id);
     return ApiResponse.success();
 }
 ```

### 服务间调用完整流程

#### 调用链路图
```
客户端请求 
    ↓ (携带JWT Token)
API网关 
    ↓ (解析JWT，设置Header: X-User-Id, X-Username, X-User-Roles, X-Request-Source=gateway)
微服务A (UserContextInterceptor解析Header → UserContextHolder)
    ↓ (Feign调用，FeignRequestInterceptor自动传递Header: X-Request-Source=feign)
微服务B (UserContextInterceptor验证来源 → UserContextHolder)
    ↓ (返回ApiResponse格式)
微服务A 
    ↓ (返回ApiResponse格式)
API网关
    ↓ (返回统一格式)
客户端
```

#### 各服务配置要点

**Gateway Service配置**:
```yaml
# 不需要用户上下文拦截器，但需要JWT过滤器
spring:
  cloud:
    gateway:
      globalcors:
        cors-configurations:
          '[/**]':
            allowed-origins: "*"
            allowed-methods: "*"
            allowed-headers: "*"
```

**其他微服务配置**:
```java
// 1. 添加拦截器配置
@Configuration
public class WebConfig implements WebMvcConfigurer {
    
    @Autowired
    private UserContextInterceptor userContextInterceptor;
    
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(userContextInterceptor)
               .addPathPatterns("/**")
               .excludePathPatterns("/actuator/**", "/v3/api-docs/**");
    }
}

// 2. 启用Feign客户端
@EnableFeignClients
@SpringBootApplication
public class ServiceApplication {
    public static void main(String[] args) {
        SpringApplication.run(ServiceApplication.class, args);
    }
}
```

#### 依赖要求
所有微服务（除Gateway外）必须添加：
```xml
<!-- Feign客户端 -->
<dependency>
    <groupId>org.springframework.cloud</groupId>
    <artifactId>spring-cloud-starter-openfeign</artifactId>
</dependency>

<!-- 负载均衡 -->
<dependency>
    <groupId>org.springframework.cloud</groupId>
    <artifactId>spring-cloud-starter-loadbalancer</artifactId>
</dependency>
```

### 安全注意事项

#### 请求来源验证
- **gateway**: 来自API网关的请求
- **feign**: 来自其他微服务的请求
- **其他**: 非法请求，应拒绝访问

#### Header传递链路
1. **客户端** → **网关**: Authorization: Bearer {token}
2. **网关** → **微服务**: X-User-Id, X-Username, X-User-Roles, X-Request-Source=gateway
3. **微服务A** → **微服务B**: 自动传递所有用户Header + X-Request-Source=feign

#### 安全检查点
- ✅ 网关验证JWT token有效性
- ✅ 微服务验证请求来源(X-Request-Source)
- ✅ 微服务验证用户权限(基于角色注解)
- ✅ 所有调用链都有请求追踪ID

---

## 注意事项与最佳实践

### 开发注意事项
1. **版本统一**: 所有依赖版本必须在父POM中统一管理
2. **配置外化**: 敏感配置信息使用环境变量或Nacos配置中心
3. **日志规范**: 使用SLF4J + Logback，统一日志格式和级别
4. **异常处理**: 使用全局异常处理器，统一异常响应格式
5. **安全规范**: 所有API需要进行认证和授权检查
6. **数据校验**: 使用JSR-303注解进行参数校验
7. **事务管理**: 合理使用@Transactional注解

### 性能最佳实践
1. **缓存策略**: 合理使用Redis缓存热点数据
2. **数据库优化**: 避免N+1查询，合理使用索引
3. **分页查询**: 大数据量查询必须分页
4. **连接池**: 合理配置数据库连接池参数
5. **异步处理**: 耗时操作使用异步处理

### 安全最佳实践
1. **输入校验**: 严格校验所有用户输入
2. **SQL注入防护**: 使用参数化查询
3. **XSS防护**: 对输出内容进行转义
4. **认证授权**: 使用JWT进行用户认证
5. **HTTPS**: 生产环境必须使用HTTPS


---

## 总结

**核心特点**:
- ✅ **技术栈统一**: Spring Boot 3.2.2 + Spring Cloud 2023.0.0 + Nacos
- ✅ **架构标准**: 分层架构 + 微服务模式
- ✅ **命名一致**: 严格的命名规范确保代码可读性
- ✅ **配置规范**: Nacos配置中心 + 环境变量管理
- ✅ **数据库规范**: MyBatis Plus + 统一表结构设计
- ✅ **部署标准**: 标准化的部署脚本和端口分配

遵循此规范可以确保：
- 🚀 **开发效率**: 标准化模板快速搭建新服务
- 🔧 **维护性**: 一致的代码结构便于维护
- 👥 **团队协作**: 统一规范减少沟通成本
- 📈 **可扩展性**: 标准化架构便于功能扩展

新增服务时严格按照此规范执行，确保整体项目的一致性和可维护性。 