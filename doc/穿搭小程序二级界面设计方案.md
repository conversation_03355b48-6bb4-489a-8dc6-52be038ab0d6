# 一、穿搭推荐小程序二级页面设计方案

## 1. 用户体验（UX）分析

### 主要用户旅程和交互逻辑

**首页模块二级交互**：
- 运势详情浏览：用户从首页运势卡片进入 → 查看五行能量详情 → 查看穿搭建议 → 返回
- 搭配详情查看：用户点击推荐搭配 → 查看搭配详情 → 可选择试穿、收藏或分享 → 查看视频展示 → 返回
- 分享内容浏览：用户点击分享内容 → 查看详情 → 尝试"我也试试"功能 → 返回

**衣橱模块二级交互**：
- 衣物上传：点击添加按钮 → 拍照/选择图片 → AI识别分类 → 确认/修改信息 → 保存
- 衣物详情查看：点击衣物卡片 → 查看详情 → 编辑信息/删除/分享 → 返回
- 衣橱分析：点击分析卡片 → 查看详细报告 → 查看优化建议 → 返回

**搭配模块二级交互**：
- 搭配创建：点击创建按钮 → 选择日期 → 设置场景条件 → 选择单品 → 生成搭配 → 保存
- 日历规划：进入日历视图 → 查看已规划搭配 → 点击日期设置搭配 → 查看搭配详情 → 返回
- 搭配视频观看：点击搭配卡片的视频按钮 → 观看视频 → 查看详情 → 收藏/分享 → 返回

**个人中心二级交互**：
- 设置操作：进入设置 → 修改个人信息/账号设置/通知设置 → 保存返回
- 灵感值管理：进入灵感值中心 → 查看余额和流水 → 充值/赠送操作 → 完成返回
- 收藏管理：进入收藏夹 → 查看收藏内容 → 删除/分享/使用 → 返回
- 体型数据管理：点击体型数据 → 查看/修改身材数据 → 保存返回

## 2. 产品界面规划

### 二级界面清单

#### 首页模块
1. **运势详情页** (`fortune_detail.html`)
   - 展示当日运势五行分析
   - 能量指数可视化图表
   - 宜忌事项详细清单
   - 提升能量建议
   
2. **搭配详情页** (`outfit_detail.html`)
   - 搭配整体效果展示
   - 单品清单及详情
   - 五行搭配原理解读
   - 搭配场合建议
   - 收藏/分享/试穿功能按钮
   
3. **搭配视频页** (`outfit_video.html`)
   - 视频播放区域
   - 视频控制条
   - 搭配信息简介
   - 生成自定义视频选项
   
4. **风格建议页** (`style_advice.html`)
   - 服饰建议详情
   - 配饰建议详情
   - 妆容建议详情
   - 场合适用性分析

#### 衣橱模块
1. **衣物上传页** (`add_clothing.html`)
   - 拍照/从相册选择入口
   - AI识别结果展示
   - 属性编辑表单
   - 确认保存按钮
   
2. **衣物详情页** (`clothing_detail.html`)
   - 衣物图片展示
   - 基本信息展示
   - 穿着历史记录
   - 搭配推荐
   - 编辑/删除按钮
   
3. **衣橱分析页** (`wardrobe_analysis.html`)
   - 衣橱统计数据可视化
   - 风格分析报告
   - 五行属性分布
   - 优化建议列表
   - 缺失单品推荐

#### 搭配模块
1. **日历搭配页** (`calendar_outfit.html`)
   - 月历视图
   - 日期选择区域
   - 天气与命理提示
   - 自定义搭配条件设置
   
2. **搭配创建页** (`create_outfit.html`)
   - 单品选择网格
   - 整体效果预览
   - 搭配评分反馈
   - 保存/分享按钮
   
3. **场景搭配页** (`scene_outfit.html`)
   - 场景选择
   - 活动类型设置
   - 风格偏好选择
   - 推荐搭配展示
   - 搭配生成按钮

#### 个人中心模块
1. **设置页** (`settings.html`)
   - 个人信息设置
   - 账号安全设置
   - 通知设置
   - 隐私设置
   - 关于与帮助
   
2. **灵感值中心页** (`inspiration_center.html`)
   - 灵感值余额展示
   - 等级与特权说明
   - 获取记录与消费明细
   - 充值入口
   - 赠送功能
   
3. **收藏夹页** (`favorites.html`)
   - 分类标签栏
   - 收藏内容网格
   - 批量管理功能
   - 快捷操作按钮
   
4. **体型数据页** (`body_data.html`)
   - 3D形象展示
   - 详细身材数据表格
   - 编辑修改功能
   - 测量指导

5. **完善资料页** (`user-info.html`)
   - 分步采集全身照、体型、基础信息、能量信息
   - 入口位于个人中心右侧功能图标区，图标为fa-id-card，风格与其他图标一致
   - 支持随时补全或修改个人资料，资料不完整时入口高亮提示
   - 完成后可自动返回个人中心或首页

### 界面层级规划

**一级页面**:
- 首页 (home.html)
- 衣橱 (wardrobe.html)
- 搭配 (outfit.html)
- 个人中心 (profile.html)

**二级页面**:
- 各一级页面的深入功能页面

**三级页面**:
- 特定功能的操作页面
- 详情查看页面

### 完整代码库结构

/doc/prototype/
├── index.html                 # 主入口，使用iframe展示所有页面
├── common/                    # 公共资源
│   ├── styles/                # 样式文件
│   └── images/                # 图片资源
├── pages/                     # 一级页面
│   ├── home.html             # 首页
│   ├── wardrobe.html         # 衣橱页
│   ├── outfit.html           # 搭配页
│   └── profile.html          # 个人中心页
├── home/                      # 首页相关二级页面
│   ├── fortune_detail.html   # 运势详情页
│   ├── outfit_detail.html    # 搭配详情页
│   ├── outfit_video.html     # 搭配视频页
│   └── style_advice.html     # 风格建议页
├── wardrobe/                  # 衣橱相关二级页面
│   ├── add_clothing.html     # 衣物上传页
│   ├── clothing_detail.html  # 衣物详情页
│   └── wardrobe_analysis.html# 衣橱分析页
├── outfit/                    # 搭配相关二级页面
│   ├── calendar_outfit.html  # 日历搭配页
│   ├── create_outfit.html    # 搭配创建页
│   └── scene_outfit.html     # 场景搭配页
└── profile/                   # 个人中心相关二级页面
    ├── settings.html         # 设置页
    ├── inspiration_center.html # 灵感值中心页
    ├── favorites.html        # 收藏夹页
    └── body_data.html        # 体型数据页
```

## 二级页面全局设计补充规范（2025）

### 1. 分步引导与进度指示
- 适用场景：衣物上传、搭配创建、体型数据管理等多步操作页面。
- 规范：顶部进度条、分步切换动画、动态标题、分步按钮布局，按钮与主界面风格一致。

### 2. 五行视觉系统
- 适用场景：运势详情、衣橱分析、搭配详情等。
- 规范：五行色彩、图标、分析卡片统一，色值、渐变、圆角、阴影、icon风格一致。

### 3. 辅助说明与标签系统
- 适用所有二级页面、弹窗、批量操作界面。
- 规范：辅助说明、标签、icon-label等信息字号、色彩、间距标准。

### 4. 批量操作与弹窗风格
- 适用批量管理、弹窗、反馈提示等。
- 规范：毛玻璃、圆角、阴影、辅助色，按钮与主界面风格一致。

### 5. 下拉选择器与分步流程组件化
- 交互模式、毛玻璃、圆角、hover、只读/编辑状态切换等细节。

### 6. 无障碍与动效
- 色彩对比、ARIA标签、键盘可达性。
- 动效参数（过渡时长、缓动曲线），所有可交互组件均需动效。

### 7. 适配与个性化
- 响应式断点、控件尺寸、内容区边距、主题色自定义、深色模式等。

### 8. 原型创新实践反哺
- 辅助说明、icon-label、分步引导、五行分析卡片等创新实践，明确哪些二级页面/场景必须采用。