# 穿搭小程序原型结构说明

## 原型页面目录结构概览

```
prototype/
├── index.html          # 主入口文件，展示所有页面
├── home/              # 首页相关页面
├── outfit/            # 搭配相关页面
├── profile/           # 个人中心相关页面
├── wardrobe/          # 衣柜管理相关页面
├── pages/            # 通用页面
├── components/        # 公共组件
├── styles/           # 样式文件
└── assets/           # 静态资源
```

## 原型功能模块说明

### 1. 首页模块 (home/)
- `style_advice.html`: 穿搭建议页面
  - 展示个性化的穿搭推荐
  - AI风格顾问互动界面
  
- `fortune_detail.html`: 穿搭运势详情
  - 展示每日穿搭运势
  - 提供详细的搭配建议
  
- `fortune_complete.html`: 完整运势解读
  - 全方位运势分析和解读
  - 个性化穿搭建议和指导
  - 运势趋势预测
  
- `outfit_detail.html`: 搭配详情页
  - 展示完整搭配信息
  - 搭配评分和评论
  
- `outfit_video.html`: 搭配视频页
  - 展示穿搭视频内容
  - 视频互动功能
  
- `wardrobe.html`: 衣柜快捷入口
  - 衣柜概览
  - 快速访问功能

### 2. 搭配模块 (outfit/)

- `custom_outfit.html`: 自定义搭配
  - 个性化搭配设置
  - 高级搭配选项
  
- `outfit_preview.html`: 搭配预览
  - 搭配效果预览
  - 微调和修改功能


### 3. 个人中心模块 (profile/)
- `body_data.html`: 身材数据
  - 个人身材信息管理
  - 尺寸记录和更新
  
- `favorites.html`: 收藏中心
  - 收藏的搭配和单品
  - 收藏夹管理
  
- `inspiration_center.html`: 灵感中心
  - 收集的穿搭灵感
  - 风格板功能
  
- `settings.html`: 设置页面
  - 个人信息设置
  - 应用偏好设置

- `user-info.html`: 用户信息补充页
  - 基础个人信息填写
  - 个人风格偏好设置
  - 穿搭习惯调查
  - 身材数据采集
  - 个性化标签选择
  - **入口说明：**
    - 新用户首次进入时由新用户引导功能自动打开
    - 在个人中心 profile.html 页面 UI/交互设计中，增加"完善资料"按钮或入口，便于老用户随时补充或修改个人信息

### 4. 衣柜模块 (wardrobe/)
- `add_clothing.html`: 添加衣物
  - 新增衣物表单
  - 衣物分类和标签
  
- `clothing_detail.html`: 衣物详情
  - 单件衣物详细信息
  - 搭配历史记录
  
- `wardrobe_analysis.html`: 衣柜分析
  - 衣柜统计数据
  - 穿搭建议和优化

### 5. 通用页面 (pages/)
- `home.html`: 首页
  - 今日运势展示
  - 穿搭推荐
  - 流行趋势
  - 搭配灵感
  - 能量提示

- `wardrobe.html`: 衣橱页面
  - 衣物分类展示
  - 衣物管理
  - 衣橱分析
  - 搭配建议

- `outfit.html`: 搭配页面
  - 创建搭配
  - 场景搭配
  - 日历搭配
  - 搭配灵感

- `profile.html`: 我的页面
  - 个人信息
  - 收藏中心
  - 穿搭记录
  - 设置

## 原型技术规范

1. 所有页面均采用 HTML + Tailwind CSS 开发
2. 页面尺寸遵循 iPhone 15 Pro 规格
3. 使用 FontAwesome 图标库
4. 采用模块化的开发方式，确保代码复用性
5. 保持统一的设计风格和交互模式

## 注意事项

1. 所有页面需要保持响应式设计
2. 确保交互流程符合用户习惯
3. 保持视觉设计的一致性
4. 代码结构清晰，便于后续维护
5. 确保所有功能模块之间的联动正常 