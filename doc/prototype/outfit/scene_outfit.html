<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <title>场景搭配 - StylishLink</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../styles/main.css" rel="stylesheet">
    <style>
        ::-webkit-scrollbar {
            display: none;
        }
        
        * {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        
        .content-container {
            padding-left: 5%;
            padding-right: 5%;
        }

        .gradient-background {
            background: linear-gradient(135deg, #c2e9fb 0%, #d8c6fd 100%);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: -1;
        }
        
        .enhanced-glass {
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.05);
        }
        
        .glass-button {
            background: rgba(255, 255, 255, 0.4);
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.5);
            transition: all 0.3s ease;
        }
        
        .glass-button:hover {
            background: rgba(255, 255, 255, 0.6);
        }
        
        .glow-icon {
            box-shadow: 0 0 15px rgba(255, 255, 255, 0.8);
        }
        
        /* 选择卡片样式 */
        .scene-card {
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .scene-card.selected {
            border: 2px solid rgba(168, 230, 207, 1);
        }
        
        .scene-card.selected::after {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 0;
            height: 0;
            border-style: solid;
            border-width: 0 25px 25px 0;
            border-color: transparent rgba(168, 230, 207, 1) transparent transparent;
        }
        
        /* 标签样式 */
        .tag-item {
            display: inline-flex;
            align-items: center;
            padding: 4px 10px;
            border-radius: 16px;
            font-size: 10px;
            margin-right: 8px;
            margin-bottom: 8px;
            white-space: nowrap;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .tag-item.selected {
            background: rgba(168, 230, 207, 0.7);
            color: var(--color-text-primary);
            font-weight: 500;
        }
        
        .tag-item.unselected {
            background: rgba(255, 255, 255, 0.3);
            color: var(--color-text-secondary);
        }
        
        /* 滚动区域 */
        .scrolling-area {
            overflow-x: auto;
            white-space: nowrap;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }
        
        .scrolling-area::-webkit-scrollbar {
            display: none;
        }
        
        /* 推荐搭配 */
        .outfit-card {
            position: relative;
            border-radius: 12px;
            overflow: hidden;
            transition: transform 0.3s ease;
        }
        
        .outfit-card:active {
            transform: scale(0.98);
        }
        
        .outfit-detail {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.5);
            backdrop-filter: blur(5px);
            padding: 8px;
        }
    </style>
</head>
<body>
    <!-- 渐变背景 -->
    <div class="gradient-background"></div>

    <!-- 主内容区 -->
    <div class="pt-2 pb-20 h-full overflow-y-scroll">
        <div class="content-container py-2">
            <!-- 顶部导航栏 -->
            <div class="flex items-center justify-between mb-4">
                <div class="flex items-center">
                    <a href="../pages/outfit.html" class="mr-3">
                        <i class="fas fa-arrow-left text-[var(--color-text-primary)]"></i>
                    </a>
                    <h1 class="text-sm font-semibold text-[var(--color-text-primary)]">场景搭配</h1>
                </div>
                <button class="glass-button px-3 py-1 rounded-full text-[var(--color-text-primary)] text-xs">
                    重置
                </button>
            </div>

            <!-- 场景选择 -->
            <div class="mb-4">
                <h3 class="text-xs font-medium text-[var(--color-text-primary)] mb-2">选择场景</h3>
                <div class="grid grid-cols-4 gap-2">
                    <div class="scene-card enhanced-glass rounded-xl overflow-hidden selected">
                        <div class="aspect-square flex flex-col items-center justify-center">
                            <i class="fas fa-briefcase text-[var(--color-text-primary)] text-lg mb-1"></i>
                            <span class="text-[10px] text-[var(--color-text-secondary)]">职场</span>
                        </div>
                    </div>
                    
                    <div class="scene-card enhanced-glass rounded-xl overflow-hidden">
                        <div class="aspect-square flex flex-col items-center justify-center">
                            <i class="fas fa-champagne-glasses text-[var(--color-text-primary)] text-lg mb-1"></i>
                            <span class="text-[10px] text-[var(--color-text-secondary)]">约会</span>
                        </div>
                    </div>
                    
                    <div class="scene-card enhanced-glass rounded-xl overflow-hidden">
                        <div class="aspect-square flex flex-col items-center justify-center">
                            <i class="fas fa-coffee text-[var(--color-text-primary)] text-lg mb-1"></i>
                            <span class="text-[10px] text-[var(--color-text-secondary)]">休闲</span>
                        </div>
                    </div>
                    
                    <div class="scene-card enhanced-glass rounded-xl overflow-hidden">
                        <div class="aspect-square flex flex-col items-center justify-center">
                            <i class="fas fa-plane text-[var(--color-text-primary)] text-lg mb-1"></i>
                            <span class="text-[10px] text-[var(--color-text-secondary)]">旅行</span>
                        </div>
                    </div>
                    
                    <div class="scene-card enhanced-glass rounded-xl overflow-hidden">
                        <div class="aspect-square flex flex-col items-center justify-center">
                            <i class="fas fa-graduation-cap text-[var(--color-text-primary)] text-lg mb-1"></i>
                            <span class="text-[10px] text-[var(--color-text-secondary)]">学院</span>
                        </div>
                    </div>
                    
                    <div class="scene-card enhanced-glass rounded-xl overflow-hidden">
                        <div class="aspect-square flex flex-col items-center justify-center">
                            <i class="fas fa-utensils text-[var(--color-text-primary)] text-lg mb-1"></i>
                            <span class="text-[10px] text-[var(--color-text-secondary)]">宴会</span>
                        </div>
                    </div>
                    
                    <div class="scene-card enhanced-glass rounded-xl overflow-hidden">
                        <div class="aspect-square flex flex-col items-center justify-center">
                            <i class="fas fa-dumbbell text-[var(--color-text-primary)] text-lg mb-1"></i>
                            <span class="text-[10px] text-[var(--color-text-secondary)]">运动</span>
                        </div>
                    </div>
                    
                    <div class="scene-card enhanced-glass rounded-xl overflow-hidden">
                        <div class="aspect-square flex flex-col items-center justify-center">
                            <i class="fas fa-plus text-[var(--color-text-primary)] text-lg mb-1"></i>
                            <span class="text-[10px] text-[var(--color-text-secondary)]">更多</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 活动类型 -->
            <div class="mb-4">
                <h3 class="text-xs font-medium text-[var(--color-text-primary)] mb-2">活动类型</h3>
                <div class="flex flex-wrap">
                    <div class="tag-item selected">
                        <i class="fas fa-handshake mr-1"></i>商务会议
                    </div>
                    <div class="tag-item unselected">
                        <i class="fas fa-users mr-1"></i>团队协作
                    </div>
                    <div class="tag-item unselected">
                        <i class="fas fa-presentation mr-1"></i>项目汇报
                    </div>
                    <div class="tag-item unselected">
                        <i class="fas fa-user-tie mr-1"></i>客户洽谈
                    </div>
                    <div class="tag-item unselected">
                        <i class="fas fa-edit mr-1"></i>签约仪式
                    </div>
                    <div class="tag-item unselected">
                        <i class="fas fa-crown mr-1"></i>晋升面谈
                    </div>
                </div>
            </div>

            <!-- 风格偏好 -->
            <div class="mb-4">
                <h3 class="text-xs font-medium text-[var(--color-text-primary)] mb-2">风格偏好</h3>
                <div class="flex flex-wrap">
                    <div class="tag-item selected">
                        <i class="fas fa-briefcase mr-1"></i>商务正式
                    </div>
                    <div class="tag-item unselected">
                        <i class="fas fa-tshirt mr-1"></i>商务休闲
                    </div>
                    <div class="tag-item unselected">
                        <i class="fas fa-gem mr-1"></i>精致优雅
                    </div>
                    <div class="tag-item unselected">
                        <i class="fas fa-star mr-1"></i>时尚前卫
                    </div>
                    <div class="tag-item unselected">
                        <i class="fas fa-leaf mr-1"></i>自然舒适
                    </div>
                </div>
            </div>

            <!-- 五行偏好 -->
            <div class="mb-4">
                <h3 class="text-xs font-medium text-[var(--color-text-primary)] mb-2">五行偏好</h3>
                <div class="flex flex-wrap">
                    <div class="tag-item selected" style="background: linear-gradient(135deg, #ffffff, #f0f0f0); color: #1a1a1a;">
                        <i class="fas fa-circle mr-1"></i>金
                    </div>
                    <div class="tag-item unselected" style="background: linear-gradient(135deg, #a8e6cf, #73c1a8); color: #1a1a1a;">
                        <i class="fas fa-leaf mr-1"></i>木
                    </div>
                    <div class="tag-item unselected" style="background: linear-gradient(135deg, #b8c6db, #648dae); color: white;">
                        <i class="fas fa-water mr-1"></i>水
                    </div>
                    <div class="tag-item unselected" style="background: linear-gradient(135deg, #ff9a9e, #ff5458); color: white;">
                        <i class="fas fa-fire mr-1"></i>火
                    </div>
                    <div class="tag-item unselected" style="background: linear-gradient(135deg, #ffeaa7, #ffc25c); color: #1a1a1a;">
                        <i class="fas fa-mountain mr-1"></i>土
                    </div>
                </div>
            </div>

            <!-- 生成搭配按钮 -->
            <button class="glass-button w-full py-3 rounded-full text-sm font-medium text-[var(--color-text-primary)] mb-4 flex items-center justify-center">
                <i class="fas fa-wand-magic-sparkles mr-2"></i>生成搭配建议
            </button>

            <!-- 搭配推荐结果 -->
            <div class="mb-4">
                <div class="flex items-center justify-between mb-2">
                    <h3 class="text-xs font-medium text-[var(--color-text-primary)]">推荐搭配</h3>
                    <span class="text-[10px] text-[var(--color-text-secondary)]">3套搭配</span>
                </div>
                
                <!-- 搭配1 -->
                <div class="outfit-card enhanced-glass rounded-xl mb-3">
                    <img src="https://images.unsplash.com/photo-1515886657613-9f3515b0c78f" 
                         class="w-full aspect-[2/3] object-cover" alt="职场正装搭配">
                    <div class="outfit-detail">
                        <div class="flex justify-between items-start">
                            <div>
                                <h4 class="text-xs font-medium text-[var(--color-text-primary)]">职场精英系列</h4>
                                <p class="text-[10px] text-[var(--color-text-secondary)]">适合商务会议，给人专业干练的形象</p>
                            </div>
                            <div class="flex items-center text-[#ffc25c]">
                                <i class="fas fa-star text-[10px]"></i>
                                <i class="fas fa-star text-[10px]"></i>
                                <i class="fas fa-star text-[10px]"></i>
                                <i class="fas fa-star text-[10px]"></i>
                                <i class="fas fa-star-half-alt text-[10px]"></i>
                            </div>
                        </div>
                        <div class="flex justify-end mt-1">
                            <button class="glass-button px-2 py-1 rounded-md text-[10px] text-[var(--color-text-primary)] flex items-center">
                                <i class="fas fa-eye mr-1"></i>查看详情
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 搭配2 -->
                <div class="outfit-card enhanced-glass rounded-xl mb-3">
                    <img src="https://images.unsplash.com/photo-1539109136881-3be0616acf4b" 
                         class="w-full aspect-[2/3] object-cover" alt="职场时尚搭配">
                    <div class="outfit-detail">
                        <div class="flex justify-between items-start">
                            <div>
                                <h4 class="text-xs font-medium text-[var(--color-text-primary)]">都市丽人系列</h4>
                                <p class="text-[10px] text-[var(--color-text-secondary)]">适合商务会议，融合时尚与专业元素</p>
                            </div>
                            <div class="flex items-center text-[#ffc25c]">
                                <i class="fas fa-star text-[10px]"></i>
                                <i class="fas fa-star text-[10px]"></i>
                                <i class="fas fa-star text-[10px]"></i>
                                <i class="fas fa-star text-[10px]"></i>
                                <i class="far fa-star text-[10px]"></i>
                            </div>
                        </div>
                        <div class="flex justify-end mt-1">
                            <button class="glass-button px-2 py-1 rounded-md text-[10px] text-[var(--color-text-primary)] flex items-center">
                                <i class="fas fa-eye mr-1"></i>查看详情
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 搭配3 -->
                <div class="outfit-card enhanced-glass rounded-xl">
                    <img src="https://images.unsplash.com/photo-1485230895905-ec40ba36b9bc" 
                         class="w-full aspect-[2/3] object-cover" alt="商务休闲搭配">
                    <div class="outfit-detail">
                        <div class="flex justify-between items-start">
                            <div>
                                <h4 class="text-xs font-medium text-[var(--color-text-primary)]">优雅商务系列</h4>
                                <p class="text-[10px] text-[var(--color-text-secondary)]">适合商务会议，展现女性柔美与专业</p>
                            </div>
                            <div class="flex items-center text-[#ffc25c]">
                                <i class="fas fa-star text-[10px]"></i>
                                <i class="fas fa-star text-[10px]"></i>
                                <i class="fas fa-star text-[10px]"></i>
                                <i class="fas fa-star-half-alt text-[10px]"></i>
                                <i class="far fa-star text-[10px]"></i>
                            </div>
                        </div>
                        <div class="flex justify-end mt-1">
                            <button class="glass-button px-2 py-1 rounded-md text-[10px] text-[var(--color-text-primary)] flex items-center">
                                <i class="fas fa-eye mr-1"></i>查看详情
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 消费灵感值提示 -->
            <div class="enhanced-glass rounded-xl p-3 flex justify-between items-center">
                <div>
                    <h3 class="text-xs font-medium text-[var(--color-text-primary)]">消耗灵感值</h3>
                    <p class="text-[10px] text-[var(--color-text-secondary)]">生成场景搭配建议</p>
                </div>
                <div class="flex items-center">
                    <span class="text-xs font-semibold text-[var(--color-text-primary)]">30</span>
                    <i class="fas fa-sparkles text-[#ffc25c] text-xs ml-1"></i>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 场景卡片选择
        const sceneCards = document.querySelectorAll('.scene-card');
        sceneCards.forEach(card => {
            card.addEventListener('click', function() {
                sceneCards.forEach(c => c.classList.remove('selected'));
                this.classList.add('selected');
            });
        });
        
        // 标签选择
        const tagItems = document.querySelectorAll('.tag-item');
        tagItems.forEach(tag => {
            tag.addEventListener('click', function() {
                if (this.classList.contains('selected')) {
                    this.classList.remove('selected');
                    this.classList.add('unselected');
                } else {
                    this.classList.remove('unselected');
                    this.classList.add('selected');
                }
            });
        });
    </script>
</body>
</html> 