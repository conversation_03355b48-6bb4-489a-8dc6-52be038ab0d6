<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <title>自定义搭配 - 穿搭推荐小程序</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --color-text-primary: #333333;
            --color-text-secondary: #666666;
            --color-text-tertiary: #999999;
        }
        
        /* 隐藏滚动条 */
        ::-webkit-scrollbar {
            display: none;
        }
        
        * {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        
        body {
            overflow-y: scroll;
            -webkit-overflow-scrolling: touch;
        }

        /* 内容区域统一间距 */
        .content-container {
            padding-left: 5%;
            padding-right: 5%;
        }
        
        .gradient-background {
            background: linear-gradient(135deg, #c2e9fb 0%, #d8c6fd 100%);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: -1;
        }
        
        .enhanced-glass {
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.05);
        }
        
        .glass-button {
            background: rgba(255, 255, 255, 0.4);
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.5);
            transition: all 0.3s ease;
        }
        
        .glass-button:hover {
            background: rgba(255, 255, 255, 0.6);
        }
        
        /* 日历样式 */
        .calendar-header {
            display: none;
        }
        
        .calendar-grid {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 4px;
        }
        
        .calendar-day {
            position: relative;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .calendar-day:hover {
            background: none;
        }
        
        .calendar-day:hover span:nth-child(2) {
            background: rgba(255, 255, 255, 0.4);
        }
        
        .calendar-day.disabled {
            opacity: 0.4;
            cursor: default;
        }
        
        /* 统一日期样式 */
        .calendar-day span:nth-child(1) {
            font-size: 10px;
            color: var(--color-text-secondary);
        }
        
        .calendar-day span:nth-child(2) {
            width: 28px;
            height: 28px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 2px 0;
            font-size: 12px;
            color: var(--color-text-primary);
        }
        
        .calendar-day span:nth-child(3) {
            font-size: 8px;
            color: var(--color-text-tertiary);
        }
        
        /* 选中状态样式 */
        .calendar-day.today span:nth-child(2),
        .calendar-day.selected span:nth-child(2) {
            background: rgba(216, 198, 253, 0.4);
            border: 1px solid rgba(216, 198, 253, 0.8);
            color: var(--color-text-primary);
            font-weight: 500;
        }
        
        /* 选择卡片样式 */
        .scene-card {
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .scene-card.selected {
            border: 2px solid rgba(216, 198, 253, 1);
        }
        
        .scene-card.selected::after {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 0;
            height: 0;
            border-style: solid;
            border-width: 0 25px 25px 0;
            border-color: transparent rgba(216, 198, 253, 1) transparent transparent;
        }
        
        /* 分类标签 */
        .category-tab {
            padding: 6px 14px;
            border-radius: 16px;
            font-size: 12px;
            display: inline-flex;
            align-items: center;
            margin-right: 8px;
            white-space: nowrap;
        }
        
        .category-tab.active {
            background: rgba(255, 255, 255, 0.5);
            color: var(--color-text-primary);
            font-weight: 500;
        }
        
        .category-tab.inactive {
            background: rgba(255, 255, 255, 0.3);
            color: var(--color-text-secondary);
        }
        
        /* 滚动区域 */
        .scrolling-area {
            overflow-x: auto;
            white-space: nowrap;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }
        
        .scrolling-area::-webkit-scrollbar {
            display: none;
        }
        
        /* 五行标识色彩 */
        .wuxing-jin {
            background: linear-gradient(135deg, #ffffff, #f0f0f0);
            color: #1a1a1a;
        }
        
        .wuxing-mu {
            background: linear-gradient(135deg, #a8e6cf, #73c1a8);
            color: #1a1a1a;
        }
        
        .wuxing-shui {
            background: linear-gradient(135deg, #b8c6db, #648dae);
            color: #ffffff;
        }
        
        .wuxing-huo {
            background: linear-gradient(135deg, #ff9a9e, #ff5458);
            color: #ffffff;
        }
        
        .wuxing-tu {
            background: linear-gradient(135deg, #ffeaa7, #ffc25c);
            color: #1a1a1a;
        }
        
        /* 搭配评分 */
        .score-item {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .score-bar {
            flex-grow: 1;
            height: 6px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
            overflow: hidden;
            margin: 0 10px;
        }
        
        .score-fill {
            height: 100%;
            border-radius: 3px;
        }
        
        /* 能量条指示器 */
        .energy-bar {
            height: 5px;
            border-radius: 2.5px;
            background: rgba(255, 255, 255, 0.3);
            overflow: hidden;
        }
        
        .energy-bar .value {
            height: 100%;
            border-radius: 2.5px;
        }
        
        /* 区块标题 */
        .section-title {
            font-size: 14px;
            font-weight: 600;
            color: var(--color-text-primary);
            margin-bottom: 12px;
            display: flex;
            align-items: center;
        }
        
        .section-title i {
            margin-right: 6px;
        }
        
        /* 可折叠内容 */
        .collapsible-content {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }
        
        .collapsible-content.expanded {
            max-height: 600px;
        }
        
        @keyframes sparkle {
            0% { transform: scale(1) rotate(0deg); }
            50% { transform: scale(1.1) rotate(5deg); }
            100% { transform: scale(1) rotate(0deg); }
        }
        
        .fa-wand-magic-sparkles {
            animation: sparkle 2s infinite;
            transform-origin: bottom right;
        }
    </style>
</head>
<body>
    <!-- 渐变背景 -->
    <div class="gradient-background"></div>

    <!-- 顶部导航 -->
    <div class="sticky top-0 bg-white/30 backdrop-blur-md z-40 shadow-sm">
        <div class="flex items-center justify-between p-4">
            <div class="flex items-center">
            <a href="../pages/outfit.html" class="mr-4">
                <i class="fas fa-arrow-left text-[var(--color-text-primary)]"></i>
            </a>
            <h1 class="text-sm font-semibold text-[var(--color-text-primary)]">自定义搭配</h1>
            </div>
            <!-- 城市选择移到右上角 -->
            <div class="flex items-center text-xs text-[var(--color-text-secondary)]">
                <i class="fas fa-map-marker-alt mr-1"></i>
                <span class="mr-1">深圳市</span>
                <i class="fas fa-chevron-right text-[10px]"></i>
            </div>
        </div>
    </div>

    <!-- 主内容区 -->
    <div class="content-container py-4 pb-20">
        <!-- 1. 日期选择部分 -->
        <div class="mb-4">
            <div class="section-title">
                <i class="fas fa-calendar-alt"></i>选择日期
            </div>
            
            <!-- 日期选择器 - 显示最近7天 -->
            <div class="enhanced-glass rounded-xl p-1 mb-2">
                <div class="flex justify-between items-center mb-1">
                    <h3 class="text-xs font-medium text-[var(--color-text-primary)]">
                        2023年11月
                    </h3>
                    <button id="calendar-toggle" class="text-xs text-[var(--color-text-primary)] flex items-center">
                        <i class="fas fa-expand-alt mr-1"></i>
                        展开日历
                    </button>
                </div>
                
                <!-- 最近7天日期显示 -->
                <div class="flex justify-between">
                    <div class="calendar-day flex flex-col items-center">
                        <span class="text-[10px] text-[var(--color-text-secondary)]">一</span>
                        <span class="w-7 h-7 rounded-full flex items-center justify-center my-0.5 text-xs text-[var(--color-text-primary)]">17</span>
                        <span class="text-[8px] text-[var(--color-text-tertiary)]">初四</span>
                    </div>
                    <div class="calendar-day flex flex-col items-center">
                        <span class="text-[10px] text-[var(--color-text-secondary)]">二</span>
                        <span class="w-7 h-7 rounded-full flex items-center justify-center my-0.5 text-xs text-[var(--color-text-primary)]">18</span>
                        <span class="text-[8px] text-[var(--color-text-tertiary)]">初五</span>
                    </div>
                    <div class="calendar-day flex flex-col items-center">
                        <span class="text-[10px] text-[var(--color-text-secondary)]">三</span>
                        <span class="w-7 h-7 rounded-full flex items-center justify-center my-0.5 text-xs text-[var(--color-text-primary)]">19</span>
                        <span class="text-[8px] text-[var(--color-text-tertiary)]">初六</span>
                    </div>
                    <div class="calendar-day today flex flex-col items-center">
                        <span class="text-[10px] text-[var(--color-text-secondary)]">四</span>
                        <span class="w-7 h-7 rounded-full bg-[#d8c6fd]/40 border border-[#d8c6fd]/80 flex items-center justify-center my-0.5 text-xs text-[var(--color-text-primary)]">20</span>
                        <span class="text-[8px] text-[var(--color-text-tertiary)]">初七</span>
                    </div>
                    <div class="calendar-day flex flex-col items-center">
                        <span class="text-[10px] text-[var(--color-text-secondary)]">五</span>
                        <span class="w-7 h-7 rounded-full flex items-center justify-center my-0.5 text-xs text-[var(--color-text-primary)]">21</span>
                        <span class="text-[8px] text-[var(--color-text-tertiary)]">初八</span>
                    </div>
                    <div class="calendar-day flex flex-col items-center">
                        <span class="text-[10px] text-[var(--color-text-secondary)]">六</span>
                        <span class="w-7 h-7 rounded-full flex items-center justify-center my-0.5 text-xs text-[var(--color-text-primary)]">22</span>
                        <span class="text-[8px] text-[var(--color-text-tertiary)]">初九</span>
                    </div>
                    <div class="calendar-day flex flex-col items-center">
                        <span class="text-[10px] text-[var(--color-text-secondary)] text-red-500">日</span>
                        <span class="w-7 h-7 rounded-full flex items-center justify-center my-0.5 text-xs text-[var(--color-text-primary)]">23</span>
                        <span class="text-[8px] text-[var(--color-text-tertiary)]">初十</span>
                    </div>
                </div>
                
                <!-- 展开后的完整日历 -->
                <div id="full-calendar" class="collapsible-content mt-3">
                    <!-- 日历格子 -->
                    <div class="calendar-grid">
                        <!-- 第1行 -->
                        <div class="calendar-day flex flex-col items-center">
                            <span class="w-7 h-7 rounded-full flex items-center justify-center my-0.5 text-xs text-[var(--color-text-primary)]">24</span>
                            <span class="text-[8px] text-[var(--color-text-tertiary)]">初四</span>
                        </div>
                        <div class="calendar-day flex flex-col items-center">
                            <span class="w-7 h-7 rounded-full flex items-center justify-center my-0.5 text-xs text-[var(--color-text-primary)]">25</span>
                            <span class="text-[8px] text-[var(--color-text-tertiary)]">初五</span>
                        </div>
                        <div class="calendar-day flex flex-col items-center">
                            <span class="w-7 h-7 rounded-full flex items-center justify-center my-0.5 text-xs text-[var(--color-text-primary)]">26</span>
                            <span class="text-[8px] text-[var(--color-text-tertiary)]">初六</span>
                        </div>
                        <div class="calendar-day flex flex-col items-center">
                            <span class="w-7 h-7 rounded-full flex items-center justify-center my-0.5 text-xs text-[var(--color-text-primary)]">27</span>
                            <span class="text-[8px] text-[var(--color-text-tertiary)]">初七</span>
                        </div>
                        <div class="calendar-day flex flex-col items-center">
                            <span class="w-7 h-7 rounded-full flex items-center justify-center my-0.5 text-xs text-[var(--color-text-primary)]">28</span>
                            <span class="text-[8px] text-[var(--color-text-tertiary)]">初八</span>
                        </div>
                        <div class="calendar-day flex flex-col items-center">
                            <span class="w-7 h-7 rounded-full flex items-center justify-center my-0.5 text-xs text-[var(--color-text-primary)]">29</span>
                            <span class="text-[8px] text-[var(--color-text-tertiary)]">初九</span>
                        </div>
                        <div class="calendar-day flex flex-col items-center">
                            <span class="w-7 h-7 rounded-full flex items-center justify-center my-0.5 text-xs text-[var(--color-text-primary)]">30</span>
                            <span class="text-[8px] text-[var(--color-text-tertiary)]">初十</span>
                        </div>

                        <!-- 第2行 -->
                        <div class="calendar-day flex flex-col items-center">
                            <span class="w-7 h-7 rounded-full flex items-center justify-center my-0.5 text-xs text-[var(--color-text-primary)]">31</span>
                            <span class="text-[8px] text-[var(--color-text-tertiary)]">十一</span>
                        </div>
                        <div class="calendar-day flex flex-col items-center">
                            <span class="w-7 h-7 rounded-full flex items-center justify-center my-0.5 text-xs text-[var(--color-text-primary)]">1</span>
                            <span class="text-[8px] text-[var(--color-text-tertiary)]">十二</span>
                        </div>
                        <div class="calendar-day flex flex-col items-center">
                            <span class="w-7 h-7 rounded-full flex items-center justify-center my-0.5 text-xs text-[var(--color-text-primary)]">2</span>
                            <span class="text-[8px] text-[var(--color-text-tertiary)]">十三</span>
                        </div>
                        <div class="calendar-day flex flex-col items-center">
                            <span class="w-7 h-7 rounded-full flex items-center justify-center my-0.5 text-xs text-[var(--color-text-primary)]">3</span>
                            <span class="text-[8px] text-[var(--color-text-tertiary)]">十四</span>
                        </div>
                        <div class="calendar-day flex flex-col items-center">
                            <span class="w-7 h-7 rounded-full flex items-center justify-center my-0.5 text-xs text-[var(--color-text-primary)]">4</span>
                            <span class="text-[8px] text-[var(--color-text-tertiary)]">十五</span>
                            </div>
                        <div class="calendar-day flex flex-col items-center">
                            <span class="w-7 h-7 rounded-full flex items-center justify-center my-0.5 text-xs text-[var(--color-text-primary)]">5</span>
                            <span class="text-[8px] text-[var(--color-text-tertiary)]">十六</span>
                        </div>
                        <div class="calendar-day flex flex-col items-center">
                            <span class="w-7 h-7 rounded-full flex items-center justify-center my-0.5 text-xs text-[var(--color-text-primary)]">6</span>
                            <span class="text-[8px] text-[var(--color-text-tertiary)]">十七</span>
                        </div>
                        
                        <!-- 第3行 -->
                        <div class="calendar-day flex flex-col items-center">
                            <span class="w-7 h-7 rounded-full flex items-center justify-center my-0.5 text-xs text-[var(--color-text-primary)]">7</span>
                            <span class="text-[8px] text-[var(--color-text-tertiary)]">十八</span>
                        </div>
                        <div class="calendar-day flex flex-col items-center">
                            <span class="w-7 h-7 rounded-full flex items-center justify-center my-0.5 text-xs text-[var(--color-text-primary)]">8</span>
                            <span class="text-[8px] text-[var(--color-text-tertiary)]">十九</span>
                        </div>
                        <div class="calendar-day flex flex-col items-center">
                            <span class="w-7 h-7 rounded-full flex items-center justify-center my-0.5 text-xs text-[var(--color-text-primary)]">9</span>
                            <span class="text-[8px] text-[var(--color-text-tertiary)]">二十</span>
                        </div>
                        <div class="calendar-day flex flex-col items-center">
                            <span class="w-7 h-7 rounded-full flex items-center justify-center my-0.5 text-xs text-[var(--color-text-primary)]">10</span>
                            <span class="text-[8px] text-[var(--color-text-tertiary)]">廿一</span>
                        </div>
                        <div class="calendar-day flex flex-col items-center">
                            <span class="w-7 h-7 rounded-full flex items-center justify-center my-0.5 text-xs text-[var(--color-text-primary)]">11</span>
                            <span class="text-[8px] text-[var(--color-text-tertiary)]">廿二</span>
                        </div>
                        <div class="calendar-day flex flex-col items-center">
                            <span class="w-7 h-7 rounded-full flex items-center justify-center my-0.5 text-xs text-[var(--color-text-primary)]">12</span>
                            <span class="text-[8px] text-[var(--color-text-tertiary)]">廿三</span>
                        </div>
                        <div class="calendar-day flex flex-col items-center">
                            <span class="w-7 h-7 rounded-full flex items-center justify-center my-0.5 text-xs text-[var(--color-text-primary)]">13</span>
                            <span class="text-[8px] text-[var(--color-text-tertiary)]">廿四</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 2. 场景选择部分 -->
        <div class="mb-4">
            <div class="section-title">
                <i class="fas fa-map-marker-alt"></i>选择场景
            </div>
            <div class="enhanced-glass rounded-xl p-2 mb-2">
                <div class="flex space-x-2 overflow-x-auto no-scrollbar">
                    <div class="scene-card enhanced-glass rounded-xl overflow-hidden selected flex-shrink-0">
                        <div class="w-24 h-8 flex items-center px-2">
                            <i class="fas fa-briefcase text-[var(--color-text-primary)] text-sm"></i>
                            <span class="text-xs text-[var(--color-text-secondary)] ml-2">职场</span>
                        </div>
                    </div>
                    
                    <div class="scene-card enhanced-glass rounded-xl overflow-hidden flex-shrink-0">
                        <div class="w-24 h-8 flex items-center px-2">
                            <i class="fas fa-champagne-glasses text-[var(--color-text-primary)] text-sm"></i>
                            <span class="text-xs text-[var(--color-text-secondary)] ml-2">约会</span>
                        </div>
                    </div>
                    
                    <div class="scene-card enhanced-glass rounded-xl overflow-hidden flex-shrink-0">
                        <div class="w-24 h-8 flex items-center px-2">
                            <i class="fas fa-coffee text-[var(--color-text-primary)] text-sm"></i>
                            <span class="text-xs text-[var(--color-text-secondary)] ml-2">休闲</span>
                        </div>
                    </div>
                    
                    <div class="scene-card enhanced-glass rounded-xl overflow-hidden flex-shrink-0">
                        <div class="w-24 h-8 flex items-center px-2">
                            <i class="fas fa-plane text-[var(--color-text-primary)] text-sm"></i>
                            <span class="text-xs text-[var(--color-text-secondary)] ml-2">旅行</span>
                        </div>
                    </div>
                    
                    <div class="scene-card enhanced-glass rounded-xl overflow-hidden flex-shrink-0">
                        <div class="w-24 h-8 flex items-center px-2">
                            <i class="fas fa-graduation-cap text-[var(--color-text-primary)] text-sm"></i>
                            <span class="text-xs text-[var(--color-text-secondary)] ml-2">学院</span>
                        </div>
                    </div>
                    
                    <div class="scene-card enhanced-glass rounded-xl overflow-hidden flex-shrink-0">
                        <div class="w-24 h-8 flex items-center px-2">
                            <i class="fas fa-utensils text-[var(--color-text-primary)] text-sm"></i>
                            <span class="text-xs text-[var(--color-text-secondary)] ml-2">宴会</span>
                        </div>
                    </div>
                    
                    <div class="scene-card enhanced-glass rounded-xl overflow-hidden flex-shrink-0">
                        <div class="w-24 h-8 flex items-center px-2">
                            <i class="fas fa-dumbbell text-[var(--color-text-primary)] text-sm"></i>
                            <span class="text-xs text-[var(--color-text-secondary)] ml-2">运动</span>
                        </div>
                    </div>
                    
                    <div class="scene-card enhanced-glass rounded-xl overflow-hidden flex-shrink-0">
                        <div class="w-24 h-8 flex items-center px-2">
                            <i class="fas fa-plus text-[var(--color-text-primary)] text-sm"></i>
                            <span class="text-xs text-[var(--color-text-secondary)] ml-2">更多</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 3. 修改单品选择部分 -->
        <div class="mb-4">
            <div class="flex items-center justify-between mb-2">
                <div class="section-title mb-0">
                    <i class="fas fa-tshirt"></i>指定单品
                    <span class="text-xs text-[var(--color-text-tertiary)] ml-2">(可选)</span>
            </div>
                <button id="toggle-clothing-section" class="text-[var(--color-text-secondary)] text-sm">
                    <i class="fas fa-chevron-down transform transition-transform duration-300"></i>
                </button>
            </div>
            <div id="clothing-section" class="hidden">
            <div class="enhanced-glass rounded-xl p-3 mb-2">
                <!-- 已选单品展示网格 -->
                <div class="flex flex-wrap gap-2">
                    <!-- 添加单品按钮 -->
                    <div class="clothing-slot enhanced-glass rounded-xl overflow-hidden cursor-pointer w-[calc(33.33%-0.5rem)]" data-category="all">
                        <div class="aspect-square bg-white/20 relative flex items-center justify-center">
                            <i class="fas fa-plus text-[var(--color-text-secondary)]"></i>
                            </div>
                        </div>
                    </div>
                </div>
                        </div>
                    </div>
                    
        <!-- 单品选择弹窗 -->
        <div id="clothing-modal" class="fixed inset-0 bg-black/50 z-50 hidden">
            <div class="absolute bottom-0 left-0 right-0 bg-white rounded-t-2xl transform transition-transform duration-300 ease-out">
                <!-- 弹窗头部 -->
                <div class="sticky top-0 bg-white px-4 pt-4 pb-2 z-10">
                    <div class="flex justify-between items-center mb-3">
                        <h3 class="text-sm font-medium text-[var(--color-text-primary)]">选择单品</h3>
                        <button id="close-modal" class="text-[var(--color-text-secondary)]">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    
                    <!-- 分类标签 -->
                    <div class="scrolling-area">
                        <div class="inline-flex pb-2">
                            <button class="category-tab active" data-category="all">
                                <i class="fas fa-border-all mr-1.5"></i>全部
                            </button>
                            <button class="category-tab inactive" data-category="上装">
                                <i class="fas fa-tshirt mr-1.5"></i>上装
                            </button>
                            <button class="category-tab inactive" data-category="下装">
                                <i class="fas fa-socks mr-1.5"></i>下装
                            </button>
                            <button class="category-tab inactive" data-category="外套">
                                <i class="fas fa-vest mr-1.5"></i>外套
                            </button>
                            <button class="category-tab inactive" data-category="鞋子">
                                <i class="fas fa-shoe-prints mr-1.5"></i>鞋子
                            </button>
                            <button class="category-tab inactive" data-category="配饰">
                                <i class="fas fa-gem mr-1.5"></i>配饰
                            </button>
                        </div>
                        </div>
                    </div>
                    
                <!-- 弹窗内容 -->
                <div class="px-4 pb-4 overflow-y-auto" style="max-height: 280px">
                    <!-- 单品网格 -->
                    <div class="grid grid-cols-3 gap-2" id="modal-clothing-grid">
                        <!-- 动态加载的单品内容 -->
                        </div>
                    
                    <!-- 加载更多提示 -->
                    <div id="load-more-hint" class="text-center py-3 text-xs text-[var(--color-text-secondary)]">
                        <span>上拉加载更多</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 预览搭配按钮 -->
        <div class="mt-8 mb-4">
            <button class="w-full py-4 rounded-xl text-sm font-medium text-white flex items-center justify-center shadow-lg transition-all duration-300 bg-gradient-to-r from-[#8B5CF6] to-[#6366F1] hover:shadow-xl hover:opacity-90 active:transform active:scale-[0.98]">
                <i class="fas fa-wand-magic-sparkles mr-2 text-lg"></i>
                智能搭配
            </button>
            <div class="text-center mt-2">
                <span class="text-xs text-[var(--color-text-tertiary)]">AI 将为您推荐最适合的搭配方案</span>
            </div>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 日历展开/收起
            const calendarToggle = document.getElementById('calendar-toggle');
            const fullCalendar = document.getElementById('full-calendar');
            
            calendarToggle.addEventListener('click', function() {
                fullCalendar.classList.toggle('expanded');
                
                if (fullCalendar.classList.contains('expanded')) {
                    calendarToggle.innerHTML = '<i class="fas fa-compress-alt mr-1"></i>收起日历';
                } else {
                    calendarToggle.innerHTML = '<i class="fas fa-expand-alt mr-1"></i>展开日历';
                }
            });
            
            // 日历日期点击
            const calendarDays = document.querySelectorAll('.calendar-day:not(.disabled)');
            calendarDays.forEach(day => {
                day.addEventListener('click', function() {
                    // 移除所有选中状态
                    calendarDays.forEach(d => {
                        d.classList.remove('selected');
                        const dateSpan = d.querySelector('span:nth-child(2)');
                        dateSpan.style.background = 'none';
                        dateSpan.style.border = 'none';
                        dateSpan.style.color = 'var(--color-text-primary)';
                        dateSpan.style.fontWeight = 'normal';
                    });
                    
                    // 设置当前日期为选中
                    this.classList.add('selected');
                    const dateSpan = this.querySelector('span:nth-child(2)');
                    dateSpan.style.background = 'rgba(216, 198, 253, 0.4)';
                    dateSpan.style.border = '1px solid rgba(216, 198, 253, 0.8)';
                    dateSpan.style.color = 'var(--color-text-primary)';
                    dateSpan.style.fontWeight = '500';
                });
            });
            
            // 场景卡片选择
            const sceneCards = document.querySelectorAll('.scene-card');
            sceneCards.forEach(card => {
                card.addEventListener('click', function() {
                    sceneCards.forEach(c => c.classList.remove('selected'));
                    this.classList.add('selected');
                });
            });
            
            // 单品选择相关
            const clothingModal = document.getElementById('clothing-modal');
            const closeModal = document.getElementById('close-modal');
            const clothingSlot = document.querySelector('.clothing-slot');
            const modalClothingGrid = document.getElementById('modal-clothing-grid');
            const toggleClothingSection = document.getElementById('toggle-clothing-section');
            const clothingSection = document.getElementById('clothing-section');
            let currentCategory = 'all';
            let selectedItemsByCategory = {}; // 用于跟踪每个类别已选择的单品

            // 切换指定单品区域显示/隐藏
            toggleClothingSection.addEventListener('click', function() {
                const icon = this.querySelector('i');
                icon.classList.toggle('rotate-180');
                clothingSection.classList.toggle('hidden');
            });

            // 打开弹窗
            clothingSlot.addEventListener('click', function() {
                openModal(currentCategory);
            });

            // 关闭弹窗
            closeModal.addEventListener('click', () => {
                clothingModal.classList.add('hidden');
            });

            // 点击弹窗外部关闭
            clothingModal.addEventListener('click', (e) => {
                if (e.target === clothingModal) {
                    clothingModal.classList.add('hidden');
                }
            });
            
            // 分类标签切换
            const categoryTabs = document.querySelectorAll('.category-tab');
            categoryTabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    categoryTabs.forEach(t => {
                        t.classList.remove('active');
                        t.classList.add('inactive');
                    });
                    this.classList.remove('inactive');
                    this.classList.add('active');
                    currentCategory = this.dataset.category;
                    loadCategoryItems(currentCategory);
                });
            });
            
            function openModal(category) {
                clothingModal.classList.remove('hidden');
                loadCategoryItems(category);
            }

            function loadCategoryItems(category) {
                // 清空现有内容
                modalClothingGrid.innerHTML = '';
                
                // 显示加载提示
                document.getElementById('load-more-hint').classList.remove('hidden');
                
                // 模拟加载该分类的单品数据
                const mockItems = [
                    {
                        img: 'https://images.unsplash.com/photo-1580618672591-eb180b1a973f',
                        alt: '白色衬衫',
                        category: '上装'
                    },
                    {
                        img: 'https://images.unsplash.com/photo-1527628217451-b2414a1ee733',
                        alt: '蓝色条纹衬衫',
                        category: '上装'
                    },
                    {
                        img: 'https://images.unsplash.com/photo-1554568218-0f1715e72254',
                        alt: '灰色T恤',
                        category: '上装'
                    },
                    {
                        img: 'https://images.unsplash.com/photo-1618517351616-38fb9c5210c6',
                        alt: '黑色牛仔裤',
                        category: '下装'
                    },
                    {
                        img: 'https://images.unsplash.com/photo-1624378439575-d8705ad7ae80',
                        alt: '运动鞋',
                        category: '鞋子'
                    }
                ];

                // 根据分类筛选
                const filteredItems = category === 'all' ? 
                    mockItems : 
                    mockItems.filter(item => item.category === category);

                // 如果没有数据，显示空状态
                if (filteredItems.length === 0) {
                    modalClothingGrid.innerHTML = `
                        <div class="col-span-3 flex items-center justify-center py-8">
                            <button class="glass-button px-8 py-3 rounded-full text-sm text-[var(--color-text-primary)] flex items-center justify-center shadow-sm hover:shadow-md transition-all duration-300">
                                <i class="fas fa-camera text-lg mr-2"></i>
                                <span class="font-medium">添加单品</span>
                            </button>
                        </div>
                    `;
                    document.getElementById('load-more-hint').classList.add('hidden');
                    return;
                }

                // 有数据时的正常显示逻辑
                filteredItems.forEach(item => {
                    const itemDiv = document.createElement('div');
                    itemDiv.className = 'enhanced-glass rounded-xl overflow-hidden cursor-pointer';
                    
                    // 检查该单品是否已被选中
                    const isSelected = selectedItemsByCategory[item.category]?.img === item.img;
                    
                    itemDiv.innerHTML = `
                        <div class="aspect-square bg-white/20 relative">
                            <img src="${item.img}" class="w-full h-full object-cover" alt="${item.alt}">
                            ${isSelected ? `
                            <div class="absolute top-1 right-1 bg-[#d8c6fd] rounded-full w-5 h-5 flex items-center justify-center">
                                <i class="fas fa-check text-white text-[10px]"></i>
                            </div>
                            ` : ''}
                        </div>
                    `;

                    // 添加点击事件监听器
                    itemDiv.addEventListener('click', () => {
                        selectItem(item);
                    });

                    modalClothingGrid.appendChild(itemDiv);
                });

                // 显示加载更多提示
                document.getElementById('load-more-hint').classList.remove('hidden');
            }

            function selectItem(item) {
                const container = document.querySelector('.flex.flex-wrap');
                
                // 更新选中状态
                selectedItemsByCategory[item.category] = item;
                
                // 清除现有的指定单品（除了添加按钮）
                const existingItems = Array.from(container.children).filter(child => 
                    !child.classList.contains('clothing-slot')
                );
                existingItems.forEach(item => item.remove());

                // 创建所有已选中单品的元素
                Object.values(selectedItemsByCategory).forEach(selectedItem => {
                const newItem = document.createElement('div');
                newItem.className = 'w-[calc(33.33%-0.5rem)]';
                newItem.innerHTML = `
                    <div class="enhanced-glass rounded-xl overflow-hidden">
                        <div class="aspect-square bg-white/20 relative">
                                <img src="${selectedItem.img}" class="w-full h-full object-cover" alt="${selectedItem.alt}">
                            <div class="absolute top-1 right-1 bg-[#d8c6fd] rounded-full w-5 h-5 flex items-center justify-center">
                                <i class="fas fa-check text-white text-[10px]"></i>
                            </div>
                                <div class="absolute bottom-0 left-0 right-0 bg-black/50 py-1 px-2">
                                    <span class="text-[10px] text-white">${selectedItem.category}</span>
                            </div>
                        </div>
                    </div>
                `;

                // 将新单品插入到加号按钮之前
                    const addButton = container.querySelector('.clothing-slot');
                    if (addButton) {
                        container.insertBefore(newItem, addButton);
                    } else {
                        container.appendChild(newItem);
                    }
                });

                // 关闭弹窗
                clothingModal.classList.add('hidden');
                
                // 重新加载当前类别的单品列表以更新选中状态
                loadCategoryItems(currentCategory);
            }
        });
    </script>
</body>
</html> 