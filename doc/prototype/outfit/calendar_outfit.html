<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <title>日历搭配 - 穿搭推荐小程序</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --color-text-primary: #333333;
            --color-text-secondary: #666666;
            --color-text-tertiary: #999999;
        }
        
        /* 隐藏滚动条 */
        ::-webkit-scrollbar {
            display: none;
        }
        
        * {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        
        body {
            overflow-y: scroll;
            -webkit-overflow-scrolling: touch;
        }

        /* 内容区域统一间距 */
        .content-container {
            padding-left: 5%;
            padding-right: 5%;
        }
        
        .gradient-background {
            background: linear-gradient(135deg, #c2e9fb 0%, #d8c6fd 100%);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: -1;
        }
        
        .enhanced-glass {
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.05);
        }
        
        .glass-button {
            background: rgba(255, 255, 255, 0.4);
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.5);
            transition: all 0.3s ease;
        }
        
        .glass-button:hover {
            background: rgba(255, 255, 255, 0.6);
        }
        
        /* 日历样式 */
        .calendar-header {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            margin-bottom: 8px;
        }
        
        .calendar-header span {
            text-align: center;
            font-size: 12px;
            font-weight: 500;
            color: var(--color-text-primary);
        }
        
        .calendar-grid {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 4px;
        }
        
        .calendar-day {
            aspect-ratio: 1/1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            border-radius: 8px;
            font-size: 12px;
            position: relative;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .calendar-day:hover {
            background: rgba(255, 255, 255, 0.4);
        }
        
        .calendar-day.disabled {
            opacity: 0.4;
            cursor: default;
        }
        
        .calendar-day.today {
            background: rgba(168, 230, 207, 0.4);
            border: 1px solid rgba(168, 230, 207, 0.8);
        }
        
        .calendar-day.selected {
            background: rgba(168, 230, 207, 0.6);
            border: 1px solid rgba(168, 230, 207, 0.9);
        }
        
        .calendar-day.planned {
            border: 1px solid rgba(184, 198, 219, 0.9);
        }
        
        .calendar-day .date {
            font-size: 14px;
            font-weight: 500;
            color: var(--color-text-primary);
        }
        
        .calendar-day .lunar {
            font-size: 8px;
            color: var(--color-text-secondary);
            margin-top: 2px;
        }
        
        .calendar-day .indicator {
            position: absolute;
            bottom: 4px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 2px;
        }
        
        .calendar-day .indicator span {
            width: 4px;
            height: 4px;
            border-radius: 50%;
        }
        
        /* 模态框 */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 50;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.3s ease;
        }
        
        .modal.active {
            opacity: 1;
            pointer-events: auto;
        }
        
        .modal-content {
            width: 90%;
            max-width: 320px;
            max-height: 90vh;
            overflow-y: auto;
            border-radius: 16px;
            transition: transform 0.3s ease;
            transform: translateY(20px);
        }
        
        .modal.active .modal-content {
            transform: translateY(0);
        }
        
        /* 能量条指示器 */
        .energy-bar {
            height: 5px;
            border-radius: 2.5px;
            background: rgba(255, 255, 255, 0.3);
            overflow: hidden;
        }
        
        .energy-bar .value {
            height: 100%;
            border-radius: 2.5px;
        }
    </style>
</head>
<body>
    <!-- 渐变背景 -->
    <div class="gradient-background"></div>

    <!-- 顶部导航 -->
    <div class="sticky top-0 bg-white/30 backdrop-blur-md z-40 shadow-sm">
        <div class="flex items-center p-4">
            <a href="../pages/outfit.html" class="mr-4">
                <i class="fas fa-arrow-left text-[var(--color-text-primary)]"></i>
            </a>
            <h1 class="text-sm font-semibold text-[var(--color-text-primary)]">日历搭配</h1>
        </div>
    </div>

    <!-- 主内容区 -->
    <div class="content-container py-4 pb-20">
        <!-- 月份选择器 -->
        <div class="flex justify-between items-center mb-4">
            <button class="w-8 h-8 rounded-full enhanced-glass flex items-center justify-center">
                <i class="fas fa-chevron-left text-xs text-[var(--color-text-primary)]"></i>
            </button>
            
            <h2 class="text-base font-semibold text-[var(--color-text-primary)]">2023年11月</h2>
            
            <button class="w-8 h-8 rounded-full enhanced-glass flex items-center justify-center">
                <i class="fas fa-chevron-right text-xs text-[var(--color-text-primary)]"></i>
            </button>
        </div>
        
        <!-- 日历视图 -->
        <div class="enhanced-glass rounded-xl p-4 mb-4">
            <!-- 星期表头 -->
            <div class="calendar-header">
                <span class="text-red-500">日</span>
                <span>一</span>
                <span>二</span>
                <span>三</span>
                <span>四</span>
                <span>五</span>
                <span>六</span>
            </div>
            
            <!-- 日历格子 -->
            <div class="calendar-grid">
                <!-- 第1行 -->
                <div class="calendar-day disabled">
                    <span class="date">29</span>
                    <span class="lunar">十五</span>
                </div>
                <div class="calendar-day disabled">
                    <span class="date">30</span>
                    <span class="lunar">十六</span>
                </div>
                <div class="calendar-day disabled">
                    <span class="date">31</span>
                    <span class="lunar">十七</span>
                </div>
                <div class="calendar-day">
                    <span class="date">1</span>
                    <span class="lunar">十八</span>
                </div>
                <div class="calendar-day">
                    <span class="date">2</span>
                    <span class="lunar">十九</span>
                </div>
                <div class="calendar-day planned">
                    <span class="date">3</span>
                    <span class="lunar">二十</span>
                    <div class="indicator">
                        <span class="bg-[#a8e6cf]"></span>
                    </div>
                </div>
                <div class="calendar-day">
                    <span class="date">4</span>
                    <span class="lunar">廿一</span>
                </div>
                
                <!-- 第2行 -->
                <div class="calendar-day">
                    <span class="date">5</span>
                    <span class="lunar">廿二</span>
                </div>
                <div class="calendar-day planned">
                    <span class="date">6</span>
                    <span class="lunar">廿三</span>
                    <div class="indicator">
                        <span class="bg-[#a8e6cf]"></span>
                    </div>
                </div>
                <div class="calendar-day">
                    <span class="date">7</span>
                    <span class="lunar">廿四</span>
                </div>
                <div class="calendar-day">
                    <span class="date">8</span>
                    <span class="lunar">廿五</span>
                </div>
                <div class="calendar-day">
                    <span class="date">9</span>
                    <span class="lunar">廿六</span>
                </div>
                <div class="calendar-day planned">
                    <span class="date">10</span>
                    <span class="lunar">廿七</span>
                    <div class="indicator">
                        <span class="bg-[#a8e6cf]"></span>
                    </div>
                </div>
                <div class="calendar-day">
                    <span class="date">11</span>
                    <span class="lunar">廿八</span>
                </div>
                
                <!-- 第3行 -->
                <div class="calendar-day">
                    <span class="date">12</span>
                    <span class="lunar">廿九</span>
                </div>
                <div class="calendar-day">
                    <span class="date">13</span>
                    <span class="lunar">三十</span>
                </div>
                <div class="calendar-day planned">
                    <span class="date">14</span>
                    <span class="lunar">初一</span>
                    <div class="indicator">
                        <span class="bg-[#a8e6cf]"></span>
                    </div>
                </div>
                <div class="calendar-day">
                    <span class="date">15</span>
                    <span class="lunar">初二</span>
                </div>
                <div class="calendar-day">
                    <span class="date">16</span>
                    <span class="lunar">初三</span>
                </div>
                <div class="calendar-day">
                    <span class="date">17</span>
                    <span class="lunar">初四</span>
                </div>
                <div class="calendar-day">
                    <span class="date">18</span>
                    <span class="lunar">初五</span>
                </div>
                
                <!-- 第4行 -->
                <div class="calendar-day">
                    <span class="date">19</span>
                    <span class="lunar">初六</span>
                </div>
                <div class="calendar-day today planned selected">
                    <span class="date">20</span>
                    <span class="lunar">初七</span>
                    <div class="indicator">
                        <span class="bg-[#a8e6cf]"></span>
                    </div>
                </div>
                <div class="calendar-day">
                    <span class="date">21</span>
                    <span class="lunar">初八</span>
                </div>
                <div class="calendar-day">
                    <span class="date">22</span>
                    <span class="lunar">初九</span>
                </div>
                <div class="calendar-day">
                    <span class="date">23</span>
                    <span class="lunar">初十</span>
                </div>
                <div class="calendar-day">
                    <span class="date">24</span>
                    <span class="lunar">十一</span>
                </div>
                <div class="calendar-day">
                    <span class="date">25</span>
                    <span class="lunar">十二</span>
                </div>
                
                <!-- 第5行 -->
                <div class="calendar-day">
                    <span class="date">26</span>
                    <span class="lunar">十三</span>
                </div>
                <div class="calendar-day">
                    <span class="date">27</span>
                    <span class="lunar">十四</span>
                </div>
                <div class="calendar-day">
                    <span class="date">28</span>
                    <span class="lunar">十五</span>
                </div>
                <div class="calendar-day">
                    <span class="date">29</span>
                    <span class="lunar">十六</span>
                </div>
                <div class="calendar-day">
                    <span class="date">30</span>
                    <span class="lunar">十七</span>
                </div>
                <div class="calendar-day disabled">
                    <span class="date">1</span>
                    <span class="lunar">十八</span>
                </div>
                <div class="calendar-day disabled">
                    <span class="date">2</span>
                    <span class="lunar">十九</span>
                </div>
            </div>
            
            <!-- 日历图例 -->
            <div class="flex items-center justify-center mt-4 text-xs text-[var(--color-text-secondary)]">
                <span class="inline-block w-3 h-3 bg-[rgba(168,230,207,0.4)] rounded-full mr-1"></span>
                <span class="mr-3">今日</span>
                
                <span class="inline-block w-3 h-3 border border-[rgba(184,198,219,0.9)] rounded-full mr-1"></span>
                <span>已规划搭配</span>
            </div>
        </div>
        
        <!-- 选中日期信息 -->
        <div class="enhanced-glass rounded-xl p-4 mb-4">
            <h3 class="text-sm font-medium text-[var(--color-text-primary)] mb-2">2023年11月20日・星期一</h3>
            
            <div class="flex items-center">
                <!-- 天气信息 -->
                <div class="flex items-center mr-4">
                    <i class="fas fa-cloud-sun text-[#ffd57e] mr-1"></i>
                    <span class="text-xs text-[var(--color-text-secondary)]">多云/15-22°C</span>
                </div>
                
                <!-- 农历 -->
                <div class="flex items-center">
                    <i class="fas fa-moon text-[#b8c6db] mr-1"></i>
                    <span class="text-xs text-[var(--color-text-secondary)]">十月初七</span>
                </div>
            </div>
            
            <!-- 五行能量 -->
            <div class="mt-3 pt-3 border-t border-white/20">
                <div class="flex justify-between items-center mb-1">
                    <span class="text-xs text-[var(--color-text-secondary)]">今日能量指数</span>
                    <span class="text-xs font-medium text-[var(--color-text-primary)]">78</span>
                </div>
                
                <div class="flex flex-wrap gap-2 mb-2">
                    <div class="flex items-center px-2 py-0.5 rounded-full bg-white/40">
                        <span class="w-2 h-2 rounded-full bg-white mr-1"></span>
                        <span class="text-[10px] text-[var(--color-text-secondary)]">金 82%</span>
                    </div>
                    
                    <div class="flex items-center px-2 py-0.5 rounded-full bg-[#a8e6cf]/40">
                        <span class="w-2 h-2 rounded-full bg-[#a8e6cf] mr-1"></span>
                        <span class="text-[10px] text-[var(--color-text-secondary)]">木 65%</span>
                    </div>
                    
                    <div class="flex items-center px-2 py-0.5 rounded-full bg-[#b8c6db]/40">
                        <span class="w-2 h-2 rounded-full bg-[#b8c6db] mr-1"></span>
                        <span class="text-[10px] text-[var(--color-text-secondary)]">水 48%</span>
                    </div>
                    
                    <div class="flex items-center px-2 py-0.5 rounded-full bg-[#ff9a9e]/40">
                        <span class="w-2 h-2 rounded-full bg-[#ff9a9e] mr-1"></span>
                        <span class="text-[10px] text-[var(--color-text-secondary)]">火 52%</span>
                    </div>
                    
                    <div class="flex items-center px-2 py-0.5 rounded-full bg-[#ffeaa7]/40">
                        <span class="w-2 h-2 rounded-full bg-[#ffeaa7] mr-1"></span>
                        <span class="text-[10px] text-[var(--color-text-secondary)]">土 78%</span>
                    </div>
                </div>
                
                <p class="text-xs text-[var(--color-text-secondary)]">
                    今日五行以金土为主，水行偏弱。穿搭宜选择蓝色调服饰，提升水行能量，增强智慧与灵感。
                </p>
            </div>
        </div>
        
        <!-- 已规划搭配 -->
        <div class="enhanced-glass rounded-xl p-4 mb-4">
            <div class="flex justify-between items-center mb-3">
                <h3 class="text-sm font-medium text-[var(--color-text-primary)]">已规划搭配</h3>
                <button class="text-xs text-[var(--color-text-primary)] flex items-center">
                    <i class="fas fa-edit mr-1"></i>
                    修改
                </button>
            </div>
            
            <div class="flex mb-3">
                <div class="w-20 h-20 rounded-xl enhanced-glass overflow-hidden flex-shrink-0">
                    <img src="https://images.unsplash.com/photo-1515886657613-9f3515b0c78f" 
                         alt="清新白裙搭配" class="w-full h-full object-cover">
                </div>
                
                <div class="ml-3 flex-1">
                    <h4 class="text-xs font-medium text-[var(--color-text-primary)]">清新白裙搭配</h4>
                    <div class="flex items-center mt-1">
                        <div class="star-rating flex mr-1">
                            <i class="fas fa-star text-[8px] text-yellow-400"></i>
                            <i class="fas fa-star text-[8px] text-yellow-400"></i>
                            <i class="fas fa-star text-[8px] text-yellow-400"></i>
                            <i class="fas fa-star text-[8px] text-yellow-400"></i>
                            <i class="fas fa-star-half-alt text-[8px] text-yellow-400"></i>
                        </div>
                        <span class="text-[10px] text-[var(--color-text-secondary)]">4.5</span>
                    </div>
                    
                    <div class="flex flex-wrap mt-1">
                        <span class="text-[8px] px-1.5 py-0.5 rounded-full bg-white/30 text-[var(--color-text-secondary)] mr-1 mb-1">
                            商务会议
                        </span>
                        <span class="text-[8px] px-1.5 py-0.5 rounded-full bg-white/30 text-[var(--color-text-secondary)] mb-1">
                            五行均衡
                        </span>
                    </div>
                </div>
            </div>
            
            <!-- 单品列表 -->
            <div class="flex overflow-x-auto pb-2 -mx-1">
                <img src="https://images.unsplash.com/photo-1434389677669-e08b4cac3105" alt="白色衬衫" class="w-12 h-12 object-cover rounded-lg mx-1">
                <img src="https://images.unsplash.com/photo-1577900232427-18219b9166a0" alt="白色A字裙" class="w-12 h-12 object-cover rounded-lg mx-1">
                <img src="https://images.unsplash.com/photo-1543163521-1bf539c55dd2" alt="米色高跟鞋" class="w-12 h-12 object-cover rounded-lg mx-1">
                <img src="https://images.unsplash.com/photo-1584917865442-de89df76afd3" alt="蓝色手提包" class="w-12 h-12 object-cover rounded-lg mx-1">
            </div>
        </div>
        
        <!-- 搭配规划按钮 -->
        <button class="w-full glass-button py-3 rounded-xl mb-4 text-sm text-[var(--color-text-primary)]" id="plan-outfit-btn">
            <i class="fas fa-magic mr-1"></i>
            规划新搭配
        </button>
    </div>
    
    <!-- 规划搭配模态框 -->
    <div class="modal" id="plan-modal">
        <div class="modal-content enhanced-glass">
            <div class="p-5">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-sm font-medium text-[var(--color-text-primary)]">规划搭配</h3>
                    <button id="close-modal" class="text-lg text-[var(--color-text-primary)]">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <p class="text-xs text-[var(--color-text-secondary)] mb-4">
                    为2023年11月20日创建搭配方案
                </p>
                
                <!-- 场景选择 -->
                <div class="mb-4">
                    <label class="block text-xs text-[var(--color-text-secondary)] mb-2">
                        场景选择
                    </label>
                    <select class="w-full bg-white/20 border border-white/30 rounded-xl px-3 py-2 text-sm text-[var(--color-text-primary)]">
                        <option value="work">工作通勤</option>
                        <option value="business">商务会议</option>
                        <option value="casual">日常休闲</option>
                        <option value="dating">约会社交</option>
                        <option value="party">派对活动</option>
                        <option value="formal">正式场合</option>
                    </select>
                </div>
                
                <!-- 风格偏好 -->
                <div class="mb-4">
                    <label class="block text-xs text-[var(--color-text-secondary)] mb-2">
                        风格偏好
                    </label>
                    <select class="w-full bg-white/20 border border-white/30 rounded-xl px-3 py-2 text-sm text-[var(--color-text-primary)]">
                        <option value="classic">经典简约</option>
                        <option value="elegant">优雅知性</option>
                        <option value="casual">舒适休闲</option>
                        <option value="fashion">时尚潮流</option>
                        <option value="retro">复古文艺</option>
                    </select>
                </div>
                
                <!-- 五行能量偏好 -->
                <div class="mb-4">
                    <label class="block text-xs text-[var(--color-text-secondary)] mb-2">
                        五行能量偏好
                    </label>
                    <div class="grid grid-cols-5 gap-2">
                        <div class="enhanced-glass bg-white/10 rounded-xl p-2 text-center relative">
                            <input type="checkbox" id="wuxing-jin" class="absolute opacity-0" checked>
                            <label for="wuxing-jin" class="cursor-pointer block">
                                <div class="w-8 h-8 rounded-full bg-white/40 flex items-center justify-center mx-auto mb-1">
                                    <span class="text-xs font-medium text-[var(--color-text-primary)]">金</span>
                                </div>
                                <div class="energy-bar w-full">
                                    <div class="value bg-white" style="width: 70%"></div>
                                </div>
                            </label>
                        </div>
                        
                        <div class="enhanced-glass bg-white/10 rounded-xl p-2 text-center relative">
                            <input type="checkbox" id="wuxing-mu" class="absolute opacity-0" checked>
                            <label for="wuxing-mu" class="cursor-pointer block">
                                <div class="w-8 h-8 rounded-full bg-white/40 flex items-center justify-center mx-auto mb-1">
                                    <span class="text-xs font-medium text-[var(--color-text-primary)]">木</span>
                                </div>
                                <div class="energy-bar w-full">
                                    <div class="value bg-[#a8e6cf]" style="width: 50%"></div>
                                </div>
                            </label>
                        </div>
                        
                        <div class="enhanced-glass bg-white/10 rounded-xl p-2 text-center relative">
                            <input type="checkbox" id="wuxing-shui" class="absolute opacity-0" checked>
                            <label for="wuxing-shui" class="cursor-pointer block">
                                <div class="w-8 h-8 rounded-full bg-white/40 flex items-center justify-center mx-auto mb-1">
                                    <span class="text-xs font-medium text-[var(--color-text-primary)]">水</span>
                                </div>
                                <div class="energy-bar w-full">
                                    <div class="value bg-[#b8c6db]" style="width: 80%"></div>
                                </div>
                            </label>
                        </div>
                        
                        <div class="enhanced-glass bg-white/10 rounded-xl p-2 text-center relative">
                            <input type="checkbox" id="wuxing-huo" class="absolute opacity-0">
                            <label for="wuxing-huo" class="cursor-pointer block">
                                <div class="w-8 h-8 rounded-full bg-white/40 flex items-center justify-center mx-auto mb-1">
                                    <span class="text-xs font-medium text-[var(--color-text-primary)]">火</span>
                                </div>
                                <div class="energy-bar w-full">
                                    <div class="value bg-[#ff9a9e]" style="width: 30%"></div>
                                </div>
                            </label>
                        </div>
                        
                        <div class="enhanced-glass bg-white/10 rounded-xl p-2 text-center relative">
                            <input type="checkbox" id="wuxing-tu" class="absolute opacity-0" checked>
                            <label for="wuxing-tu" class="cursor-pointer block">
                                <div class="w-8 h-8 rounded-full bg-white/40 flex items-center justify-center mx-auto mb-1">
                                    <span class="text-xs font-medium text-[var(--color-text-primary)]">土</span>
                                </div>
                                <div class="energy-bar w-full">
                                    <div class="value bg-[#ffeaa7]" style="width: 60%"></div>
                                </div>
                            </label>
                        </div>
                    </div>
                </div>
                
                <!-- 命理匹配选项 -->
                <div class="mb-4">
                    <div class="flex items-center justify-between">
                        <label class="text-xs text-[var(--color-text-secondary)]">
                            命理匹配优先
                        </label>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" class="sr-only peer" checked>
                            <div class="w-9 h-5 bg-white/30 rounded-full peer peer-checked:after:translate-x-full after:content-[''] after:absolute after:top-0.5 after:left-0.5 after:bg-white after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-[#a8e6cf]"></div>
                        </label>
                    </div>
                    
                    <p class="text-[10px] text-[var(--color-text-tertiary)] mt-1">
                        开启后，系统将根据今日五行能量，匹配最适合的服饰组合
                    </p>
                </div>
                
                <!-- 提交按钮 -->
                <div class="flex gap-3">
                    <button class="flex-1 glass-button py-2 rounded-xl text-sm text-[var(--color-text-primary)]" id="cancel-plan">
                        取消
                    </button>
                    
                    <button class="flex-1 glass-button py-2 rounded-xl text-sm text-[var(--color-text-primary)] bg-[#a8e6cf]/40">
                        生成搭配
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const planOutfitBtn = document.getElementById('plan-outfit-btn');
            const planModal = document.getElementById('plan-modal');
            const closeModal = document.getElementById('close-modal');
            const cancelPlan = document.getElementById('cancel-plan');
            
            // 打开模态框
            planOutfitBtn.addEventListener('click', function() {
                planModal.classList.add('active');
            });
            
            // 关闭模态框
            closeModal.addEventListener('click', function() {
                planModal.classList.remove('active');
            });
            
            cancelPlan.addEventListener('click', function() {
                planModal.classList.remove('active');
            });
            
            // 点击模态框空白区域关闭
            planModal.addEventListener('click', function(e) {
                if (e.target === planModal) {
                    planModal.classList.remove('active');
                }
            });
            
            // 日历日期点击
            const calendarDays = document.querySelectorAll('.calendar-day:not(.disabled)');
            calendarDays.forEach(day => {
                day.addEventListener('click', function() {
                    // 先移除所有选中状态
                    calendarDays.forEach(d => d.classList.remove('selected'));
                    // 设置当前日期为选中
                    this.classList.add('selected');
                    
                    // 这里可以添加加载对应日期数据的逻辑
                    // 示例中仅用alert演示
                    if (!this.classList.contains('today')) {
                        alert(`选择了 2023年11月${this.querySelector('.date').textContent}日`);
                    }
                });
            });
            
            // 五行选择器效果
            const wuxingCheckboxes = document.querySelectorAll('[id^="wuxing-"]');
            wuxingCheckboxes.forEach(checkbox => {
                const card = checkbox.parentElement;
                
                // 初始化选中状态
                if (checkbox.checked) {
                    card.classList.add('bg-white/20');
                }
                
                checkbox.addEventListener('change', function() {
                    if (this.checked) {
                        card.classList.add('bg-white/20');
                    } else {
                        card.classList.remove('bg-white/20');
                    }
                });
            });
        });
    </script>
</body>
</html> 