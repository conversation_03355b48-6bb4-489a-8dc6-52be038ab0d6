<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <title>搭配预览 - 穿搭推荐小程序</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --color-text-primary: #333333;
            --color-text-secondary: #666666;
            --color-text-tertiary: #999999;
        }
        
        /* 隐藏滚动条 */
        ::-webkit-scrollbar {
            display: none;
        }
        
        * {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        
        body {
            overflow-y: scroll;
            -webkit-overflow-scrolling: touch;
        }

        /* 内容区域统一间距 */
        .content-container {
            padding-left: 5%;
            padding-right: 5%;
        }
        
        .gradient-background {
            background: linear-gradient(135deg, #c2e9fb 0%, #d8c6fd 100%);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: -1;
        }
        
        .enhanced-glass {
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.05);
        }
        
        .glass-button {
            background: rgba(255, 255, 255, 0.4);
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.5);
            transition: all 0.3s ease;
        }
        
        .glass-button:hover {
            background: rgba(255, 255, 255, 0.6);
        }
        
        /* 五行标识色彩 */
        .wuxing-jin {
            background: linear-gradient(135deg, #ffffff, #f0f0f0);
            color: #1a1a1a;
        }
        
        .wuxing-mu {
            background: linear-gradient(135deg, #a8e6cf, #73c1a8);
            color: #1a1a1a;
        }
        
        .wuxing-shui {
            background: linear-gradient(135deg, #b8c6db, #648dae);
            color: #ffffff;
        }
        
        .wuxing-huo {
            background: linear-gradient(135deg, #ff9a9e, #ff5458);
            color: #ffffff;
        }
        
        .wuxing-tu {
            background: linear-gradient(135deg, #ffeaa7, #ffc25c);
            color: #1a1a1a;
        }
        
        /* 搭配评分 */
        .score-item {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .score-bar {
            flex-grow: 1;
            height: 6px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
            overflow: hidden;
            margin: 0 10px;
        }
        
        .score-fill {
            height: 100%;
            border-radius: 3px;
        }
        
        /* 滚动区域 */
        .scrolling-area {
            overflow-x: auto;
            white-space: nowrap;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }
        
        .scrolling-area::-webkit-scrollbar {
            display: none;
        }
        
        /* 搭配详情卡片 */
        .detail-card {
            border-radius: 12px;
            margin-bottom: 16px;
        }
        
        /* 分享按钮 */
        .share-btn {
            position: fixed;
            right: 20px;
            bottom: 80px;
            width: 50px;
            height: 50px;
            border-radius: 25px;
            background: rgba(168, 230, 207, 0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            z-index: 30;
        }
    </style>
</head>
<body>
    <!-- 渐变背景 -->
    <div class="gradient-background"></div>

    <!-- 顶部导航 -->
    <div class="sticky top-0 bg-white/30 backdrop-blur-md z-40 shadow-sm">
        <div class="flex items-center p-4">
            <a href="custom_outfit.html" class="mr-4">
                <i class="fas fa-arrow-left text-[var(--color-text-primary)]"></i>
            </a>
            <h1 class="text-sm font-semibold text-[var(--color-text-primary)]">搭配预览</h1>
        </div>
    </div>

    <!-- 主内容区 -->
    <div class="content-container py-4 pb-20">
        <!-- 搭配预览区 -->
        <div class="enhanced-glass rounded-xl p-4 mb-4">
            <div class="aspect-square rounded-lg bg-white/20 relative mb-3">
                <img src="https://images.unsplash.com/photo-1515886657613-9f3515b0c78f" 
                     class="w-full h-full object-cover rounded-lg" alt="搭配效果">
                
                <div class="absolute bottom-3 right-3 bg-white/70 backdrop-blur-sm px-2 py-1 rounded-lg">
                    <div class="flex items-center text-[#ffc25c]">
                        <i class="fas fa-star text-xs"></i>
                        <i class="fas fa-star text-xs"></i>
                        <i class="fas fa-star text-xs"></i>
                        <i class="fas fa-star text-xs"></i>
                        <i class="fas fa-star-half-alt text-xs"></i>
                        <span class="text-[var(--color-text-primary)] text-xs ml-1">4.5</span>
                    </div>
                </div>
            </div>
            
            <!-- 搭配名称和日期 -->
            <div class="mb-3">
                <h2 class="text-base font-medium text-[var(--color-text-primary)]">商务精英搭配</h2>
                <p class="text-xs text-[var(--color-text-secondary)]">
                    <i class="fas fa-calendar-alt mr-1"></i>2023年11月20日・星期一
                </p>
            </div>
            
            <!-- 适用场景 -->
            <div class="flex flex-wrap gap-1 mb-3">
                <span class="px-2 py-1 bg-white/30 rounded-full text-[10px] text-[var(--color-text-secondary)]">
                    <i class="fas fa-briefcase mr-1"></i>职场通勤
                </span>
                <span class="px-2 py-1 bg-white/30 rounded-full text-[10px] text-[var(--color-text-secondary)]">
                    <i class="fas fa-utensils mr-1"></i>商务晚宴
                </span>
                <span class="px-2 py-1 bg-white/30 rounded-full text-[10px] text-[var(--color-text-secondary)]">
                    <i class="fas fa-handshake mr-1"></i>商务会议
                </span>
            </div>
        </div>
        
        <!-- 搭配单品列表 -->
        <div class="enhanced-glass rounded-xl p-4 mb-4">
            <h3 class="text-sm font-medium text-[var(--color-text-primary)] mb-3">搭配单品</h3>
            
            <!-- 单品项目 -->
            <div class="flex mb-3 items-center">
                <img src="https://images.unsplash.com/photo-1580618672591-eb180b1a973f" 
                     alt="白色衬衫" class="w-16 h-16 object-cover rounded-lg">
                <div class="ml-3">
                    <h4 class="text-xs font-medium text-[var(--color-text-primary)]">白色衬衫</h4>
                    <p class="text-[10px] text-[var(--color-text-secondary)]">简约优雅，适合商务场合</p>
                </div>
            </div>
            
            <div class="flex mb-3 items-center">
                <img src="https://images.unsplash.com/photo-1577900232427-18219b9166a0" 
                     alt="黑色裙装" class="w-16 h-16 object-cover rounded-lg">
                <div class="ml-3">
                    <h4 class="text-xs font-medium text-[var(--color-text-primary)]">黑色裙装</h4>
                    <p class="text-[10px] text-[var(--color-text-secondary)]">A字剪裁，修身显瘦</p>
                </div>
            </div>
            
            <div class="flex mb-3 items-center">
                <img src="https://images.unsplash.com/photo-1543163521-1bf539c55dd2" 
                     alt="高跟鞋" class="w-16 h-16 object-cover rounded-lg">
                <div class="ml-3">
                    <h4 class="text-xs font-medium text-[var(--color-text-primary)]">米色高跟鞋</h4>
                    <p class="text-[10px] text-[var(--color-text-secondary)]">舒适高度，提升气场</p>
                </div>
            </div>
            
            <div class="flex items-center">
                <img src="https://images.unsplash.com/photo-1584917865442-de89df76afd3" 
                     alt="手提包" class="w-16 h-16 object-cover rounded-lg">
                <div class="ml-3">
                    <h4 class="text-xs font-medium text-[var(--color-text-primary)]">蓝色手提包</h4>
                    <p class="text-[10px] text-[var(--color-text-secondary)]">点缀亮色，增加活力</p>
                </div>
            </div>
        </div>
        
        <!-- 搭配评分 -->
        <div class="enhanced-glass rounded-xl p-4 mb-4">
            <h3 class="text-sm font-medium text-[var(--color-text-primary)] mb-3">搭配评分</h3>
            
            <div class="mb-3">
                <div class="score-item">
                    <span class="text-[10px] text-[var(--color-text-secondary)] w-20">能量匹配度</span>
                    <div class="score-bar">
                        <div class="score-fill wuxing-jin" style="width: 90%"></div>
                    </div>
                    <span class="text-[10px] text-[var(--color-text-primary)]">90%</span>
                </div>
                
                <div class="score-item">
                    <span class="text-[10px] text-[var(--color-text-secondary)] w-20">场合适配度</span>
                    <div class="score-bar">
                        <div class="score-fill wuxing-huo" style="width: 85%"></div>
                    </div>
                    <span class="text-[10px] text-[var(--color-text-primary)]">85%</span>
                </div>
                
                <div class="score-item">
                    <span class="text-[10px] text-[var(--color-text-secondary)] w-20">风格协调度</span>
                    <div class="score-bar">
                        <div class="score-fill wuxing-mu" style="width: 95%"></div>
                    </div>
                    <span class="text-[10px] text-[var(--color-text-primary)]">95%</span>
                </div>
                
                <div class="score-item">
                    <span class="text-[10px] text-[var(--color-text-secondary)] w-20">个人气质提升</span>
                    <div class="score-bar">
                        <div class="score-fill wuxing-tu" style="width: 88%"></div>
                    </div>
                    <span class="text-[10px] text-[var(--color-text-primary)]">88%</span>
                </div>
            </div>
        </div>
        
        <!-- 搭配建议 -->
        <div class="enhanced-glass rounded-xl p-4 mb-4">
            <h3 class="text-sm font-medium text-[var(--color-text-primary)] mb-2">
                <i class="fas fa-lightbulb text-[#ffc25c] mr-1"></i>
                搭配建议
            </h3>
            
            <p class="text-xs leading-relaxed text-[var(--color-text-secondary)]">
                此搭配适合职场正式场合，整体给人干练专业的形象。白色衬衫搭配黑色A字裙，展现简约利落的职业气质。蓝色手提包作为点睛之笔，增添活力。建议佩戴简约款式的珍珠耳环，平衡整体造型，增加女性柔美气质。
            </p>
        </div>
        
        <!-- 操作按钮 -->
        <div class="flex space-x-3 mb-4">
            <a href="custom_outfit.html" class="flex-1 glass-button py-3 rounded-xl text-center text-sm text-[var(--color-text-primary)]">
                <i class="fas fa-edit mr-1"></i>
                继续编辑
            </a>
            <button class="flex-1 glass-button py-3 rounded-xl text-sm text-[var(--color-text-primary)] bg-[#a8e6cf]/30">
                <i class="fas fa-save mr-1"></i>
                保存搭配
            </button>
        </div>
        
        <!-- 视频生成按钮 -->
        <button class="w-full glass-button py-3 rounded-xl mb-4 text-sm text-[var(--color-text-primary)]">
            <i class="fas fa-video mr-1"></i>
            生成搭配视频 (消耗50灵感值)
        </button>
    </div>
    
    <!-- 分享按钮 -->
    <div class="share-btn">
        <i class="fas fa-share-alt text-white"></i>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 分享按钮点击
            const shareBtn = document.querySelector('.share-btn');
            shareBtn.addEventListener('click', function() {
                alert('分享功能开发中...');
            });
        });
    </script>
</body>
</html> 