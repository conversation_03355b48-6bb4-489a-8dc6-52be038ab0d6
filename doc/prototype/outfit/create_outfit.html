<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <title>创建搭配 - StylishLink</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../styles/main.css" rel="stylesheet">
    <style>
        ::-webkit-scrollbar {
            display: none;
        }
        
        * {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        
        .content-container {
            padding-left: 5%;
            padding-right: 5%;
        }

        .gradient-background {
            background: linear-gradient(135deg, #c2e9fb 0%, #d8c6fd 100%);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: -1;
        }
        
        .enhanced-glass {
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.05);
        }
        
        .glass-button {
            background: rgba(255, 255, 255, 0.4);
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.5);
            transition: all 0.3s ease;
        }
        
        .glass-button:hover {
            background: rgba(255, 255, 255, 0.6);
        }
        
        .glow-icon {
            box-shadow: 0 0 15px rgba(255, 255, 255, 0.8);
        }
        
        /* 分类标签 */
        .category-tab {
            padding: 6px 14px;
            border-radius: 16px;
            font-size: 12px;
            display: inline-flex;
            align-items: center;
            margin-right: 8px;
            white-space: nowrap;
        }
        
        .category-tab.active {
            background: rgba(255, 255, 255, 0.5);
            color: var(--color-text-primary);
            font-weight: 500;
        }
        
        .category-tab.inactive {
            background: rgba(255, 255, 255, 0.3);
            color: var(--color-text-secondary);
        }
        
        /* 滚动区域 */
        .scrolling-area {
            overflow-x: auto;
            white-space: nowrap;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }
        
        .scrolling-area::-webkit-scrollbar {
            display: none;
        }
        
        /* 五行标识色彩 */
        .wuxing-jin {
            background: linear-gradient(135deg, #ffffff, #f0f0f0);
            color: #1a1a1a;
        }
        
        .wuxing-mu {
            background: linear-gradient(135deg, #a8e6cf, #73c1a8);
            color: #1a1a1a;
        }
        
        .wuxing-shui {
            background: linear-gradient(135deg, #b8c6db, #648dae);
            color: #ffffff;
        }
        
        .wuxing-huo {
            background: linear-gradient(135deg, #ff9a9e, #ff5458);
            color: #ffffff;
        }
        
        .wuxing-tu {
            background: linear-gradient(135deg, #ffeaa7, #ffc25c);
            color: #1a1a1a;
        }
        
        /* 搭配评分 */
        .score-item {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .score-bar {
            flex-grow: 1;
            height: 6px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
            overflow: hidden;
            margin: 0 10px;
        }
        
        .score-fill {
            height: 100%;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <!-- 渐变背景 -->
    <div class="gradient-background"></div>

    <!-- 主内容区 -->
    <div class="pt-2 pb-20 h-full overflow-y-scroll">
        <div class="content-container py-2">
            <!-- 顶部导航栏 -->
            <div class="flex items-center justify-between mb-4">
                <div class="flex items-center">
                    <a href="../pages/outfit.html" class="mr-3">
                        <i class="fas fa-arrow-left text-[var(--color-text-primary)]"></i>
                    </a>
                    <h1 class="text-sm font-semibold text-[var(--color-text-primary)]">创建搭配</h1>
                </div>
                <button class="glass-button px-3 py-1 rounded-full text-[var(--color-text-primary)] text-xs">
                    保存
                </button>
            </div>

            <!-- 搭配预览区 -->
            <div class="enhanced-glass rounded-xl p-3 mb-4">
                <h3 class="text-xs font-medium text-[var(--color-text-primary)] mb-2">搭配效果</h3>
                <div class="aspect-square rounded-lg bg-white/20 relative mb-2">
                    <img src="https://images.unsplash.com/photo-1515886657613-9f3515b0c78f" 
                         class="w-full h-full object-cover rounded-lg" alt="搭配效果">
                    
                    <div class="absolute bottom-3 right-3 bg-white/70 backdrop-blur-sm px-2 py-1 rounded-lg">
                        <div class="flex items-center text-[#ffc25c]">
                            <i class="fas fa-star text-xs"></i>
                            <i class="fas fa-star text-xs"></i>
                            <i class="fas fa-star text-xs"></i>
                            <i class="fas fa-star text-xs"></i>
                            <i class="fas fa-star-half-alt text-xs"></i>
                            <span class="text-[var(--color-text-primary)] text-xs ml-1">4.5</span>
                        </div>
                    </div>
                </div>
                
                <!-- 搭配评分 -->
                <div class="mb-3">
                    <div class="score-item">
                        <span class="text-[10px] text-[var(--color-text-secondary)] w-20">能量匹配度</span>
                        <div class="score-bar">
                            <div class="score-fill wuxing-jin" style="width: 90%"></div>
                        </div>
                        <span class="text-[10px] text-[var(--color-text-primary)]">90%</span>
                    </div>
                    
                    <div class="score-item">
                        <span class="text-[10px] text-[var(--color-text-secondary)] w-20">场合适配度</span>
                        <div class="score-bar">
                            <div class="score-fill wuxing-huo" style="width: 85%"></div>
                        </div>
                        <span class="text-[10px] text-[var(--color-text-primary)]">85%</span>
                    </div>
                    
                    <div class="score-item">
                        <span class="text-[10px] text-[var(--color-text-secondary)] w-20">风格协调度</span>
                        <div class="score-bar">
                            <div class="score-fill wuxing-mu" style="width: 95%"></div>
                        </div>
                        <span class="text-[10px] text-[var(--color-text-primary)]">95%</span>
                    </div>
                    
                    <div class="score-item">
                        <span class="text-[10px] text-[var(--color-text-secondary)] w-20">个人气质提升</span>
                        <div class="score-bar">
                            <div class="score-fill wuxing-tu" style="width: 88%"></div>
                        </div>
                        <span class="text-[10px] text-[var(--color-text-primary)]">88%</span>
                    </div>
                </div>
                
                <!-- 适用场景 -->
                <div class="flex flex-wrap gap-1 mb-2">
                    <span class="px-2 py-1 bg-white/30 rounded-full text-[10px] text-[var(--color-text-secondary)]">
                        <i class="fas fa-briefcase mr-1"></i>职场通勤
                    </span>
                    <span class="px-2 py-1 bg-white/30 rounded-full text-[10px] text-[var(--color-text-secondary)]">
                        <i class="fas fa-utensils mr-1"></i>商务晚宴
                    </span>
                    <span class="px-2 py-1 bg-white/30 rounded-full text-[10px] text-[var(--color-text-secondary)]">
                        <i class="fas fa-handshake mr-1"></i>商务会议
                    </span>
                </div>
                
                <!-- 搭配建议 -->
                <div class="bg-white/20 rounded-lg p-2">
                    <p class="text-[10px] text-[var(--color-text-secondary)]">
                        <i class="fas fa-lightbulb text-[#ffc25c] mr-1"></i>
                        此搭配适合职场正式场合，整体给人干练专业的形象。建议搭配简约款式的珍珠耳环，增加女性柔美气质。
                    </p>
                </div>
            </div>

            <!-- 分类标签 -->
            <div class="scrolling-area mb-3">
                <div class="inline-flex">
                    <button class="category-tab active">
                        <i class="fas fa-tshirt mr-1.5"></i>上装
                    </button>
                    <button class="category-tab inactive">
                        <i class="fas fa-socks mr-1.5"></i>下装
                    </button>
                    <button class="category-tab inactive">
                        <i class="fas fa-vest mr-1.5"></i>外套
                    </button>
                    <button class="category-tab inactive">
                        <i class="fas fa-shoe-prints mr-1.5"></i>鞋子
                    </button>
                    <button class="category-tab inactive">
                        <i class="fas fa-gem mr-1.5"></i>配饰
                    </button>
                    <button class="category-tab inactive">
                        <i class="fas fa-shopping-bag mr-1.5"></i>推荐
                    </button>
                </div>
            </div>

            <!-- 单品选择网格 -->
            <div class="grid grid-cols-3 gap-2 mb-4">
                <!-- 已选中的单品 -->
                <div class="enhanced-glass rounded-xl overflow-hidden border-2 border-[#a8e6cf]">
                    <div class="aspect-square bg-white/20 relative">
                        <img src="https://images.unsplash.com/photo-1580618672591-eb180b1a973f" 
                             class="w-full h-full object-cover" alt="白色衬衫">
                        <div class="absolute top-1 right-1 bg-[#a8e6cf] rounded-full w-5 h-5 flex items-center justify-center">
                            <i class="fas fa-check text-white text-[10px]"></i>
                        </div>
                    </div>
                </div>
                
                <!-- 其他单品 -->
                <div class="enhanced-glass rounded-xl overflow-hidden">
                    <div class="aspect-square bg-white/20 relative">
                        <img src="https://images.unsplash.com/photo-1527628217451-b2414a1ee733" 
                             class="w-full h-full object-cover" alt="蓝色条纹衬衫">
                    </div>
                </div>
                
                <div class="enhanced-glass rounded-xl overflow-hidden">
                    <div class="aspect-square bg-white/20 relative">
                        <img src="https://images.unsplash.com/photo-1554568218-0f1715e72254" 
                             class="w-full h-full object-cover" alt="灰色T恤">
                    </div>
                </div>
                
                <div class="enhanced-glass rounded-xl overflow-hidden">
                    <div class="aspect-square bg-white/20 relative">
                        <img src="https://images.unsplash.com/photo-1618517351616-38fb9c5210c6" 
                             class="w-full h-full object-cover" alt="黑色T恤">
                    </div>
                </div>
                
                <div class="enhanced-glass rounded-xl overflow-hidden">
                    <div class="aspect-square bg-white/20 relative">
                        <img src="https://images.unsplash.com/photo-1626497764746-6dc36546b388" 
                             class="w-full h-full object-cover" alt="白色纯棉T恤">
                    </div>
                </div>
                
                <div class="enhanced-glass rounded-xl overflow-hidden">
                    <div class="aspect-square bg-white/20 relative">
                        <img src="https://images.unsplash.com/photo-1564584217132-2271feaeb3c5" 
                             class="w-full h-full object-cover" alt="格纹衬衫">
                    </div>
                </div>
                
                <div class="enhanced-glass rounded-xl overflow-hidden">
                    <div class="aspect-square bg-white/20 relative">
                        <img src="https://images.unsplash.com/photo-1596755094514-f87e34085b2c" 
                             class="w-full h-full object-cover" alt="粉色T恤">
                    </div>
                </div>
                
                <div class="enhanced-glass rounded-xl overflow-hidden">
                    <div class="aspect-square bg-white/20 relative">
                        <img src="https://images.unsplash.com/photo-1578587018452-892bacefd3f2" 
                             class="w-full h-full object-cover" alt="浅蓝色衬衫">
                    </div>
                </div>
                
                <div class="enhanced-glass rounded-xl overflow-hidden">
                    <div class="aspect-square bg-white/20 relative">
                        <img src="https://images.unsplash.com/photo-1588359348347-9bc6cbbb689e" 
                             class="w-full h-full object-cover" alt="白色V领T恤">
                    </div>
                </div>
            </div>
            
            <!-- 功能按钮 -->
            <div class="flex space-x-2 mb-4">
                <button class="glass-button flex-1 py-2 rounded-full text-xs text-[var(--color-text-primary)] flex items-center justify-center">
                    <i class="fas fa-magic mr-1.5"></i>一键搭配
                </button>
                <button class="glass-button flex-1 py-2 rounded-full text-xs text-[var(--color-text-primary)] flex items-center justify-center">
                    <i class="fas fa-wand-magic-sparkles mr-1.5"></i>局部调整
                </button>
            </div>
            
            <!-- 消费灵感值提示 -->
            <div class="enhanced-glass rounded-xl p-3 flex justify-between items-center">
                <div>
                    <h3 class="text-xs font-medium text-[var(--color-text-primary)]">消耗灵感值</h3>
                    <p class="text-[10px] text-[var(--color-text-secondary)]">创建自定义搭配</p>
                </div>
                <div class="flex items-center">
                    <span class="text-xs font-semibold text-[var(--color-text-primary)]">10</span>
                    <i class="fas fa-sparkles text-[#ffc25c] text-xs ml-1"></i>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 分类标签切换
        const categoryTabs = document.querySelectorAll('.category-tab');
        categoryTabs.forEach(tab => {
            tab.addEventListener('click', function() {
                categoryTabs.forEach(t => {
                    t.classList.remove('active');
                    t.classList.add('inactive');
                });
                this.classList.remove('inactive');
                this.classList.add('active');
            });
        });
        
        // 单品选择
        const clothingItems = document.querySelectorAll('.grid.grid-cols-3 > div');
        clothingItems.forEach(item => {
            item.addEventListener('click', function() {
                // 移除其他物品的选中状态
                clothingItems.forEach(i => {
                    i.classList.remove('border-2', 'border-[#a8e6cf]');
                    // 移除选中图标
                    const checkIcon = i.querySelector('.fa-check');
                    if (checkIcon) {
                        checkIcon.parentElement.remove();
                    }
                });
                
                // 添加当前物品的选中状态
                this.classList.add('border-2', 'border-[#a8e6cf]');
                
                // 添加选中图标，如果没有的话
                if (!this.querySelector('.fa-check')) {
                    const checkDiv = document.createElement('div');
                    checkDiv.className = 'absolute top-1 right-1 bg-[#a8e6cf] rounded-full w-5 h-5 flex items-center justify-center';
                    checkDiv.innerHTML = '<i class="fas fa-check text-white text-[10px]"></i>';
                    this.querySelector('.aspect-square').appendChild(checkDiv);
                }
            });
        });
    </script>
</body>
</html> 