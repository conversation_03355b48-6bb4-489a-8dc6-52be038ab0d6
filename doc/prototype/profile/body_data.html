<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <title>体型数据 - StylishLink</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../styles/main.css" rel="stylesheet">
    <style>
        ::-webkit-scrollbar {
            display: none;
        }
        
        * {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        
        .content-container {
            padding-left: 5%;
            padding-right: 5%;
        }

        .gradient-background {
            background: linear-gradient(135deg, #c2e9fb 0%, #d8c6fd 100%);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: -1;
        }
        
        .enhanced-glass {
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.05);
            position: relative;
            z-index: 10;
        }
        
        .glass-button {
            background: rgba(255, 255, 255, 0.4);
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.5);
            transition: all 0.3s ease;
        }
        
        .glass-button:hover {
            background: rgba(255, 255, 255, 0.6);
        }
        
        /* 3D模型容器样式 */
        .model-container {
            position: relative;
            width: 100%;
            aspect-ratio: 1/1.5;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(5px);
            border-radius: 12px;
            overflow: hidden;
        }
        
        /* 数据项样式 */
        .data-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .data-item:last-child {
            border-bottom: none;
        }
        
        /* 下拉选择器样式 */
        .selector-wrapper {
            position: relative;
            z-index: 20;
        }
        
        .selector-wrapper.active {
            z-index: 40;
        }
        
        .selector-button {
            background: rgba(255, 255, 255, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.5);
            border-radius: 8px;
            padding: 6px 12px;
            font-size: 12px;
            color: var(--color-text-primary);
            text-align: left;
            transition: all 0.3s ease;
            width: 120px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .selector-button:disabled {
            background: transparent;
            border: none;
            padding: 0;
            opacity: 1;
            width: auto;
            justify-content: flex-end;
        }
        
        .selector-button:disabled .fa-chevron-down {
            display: none;
        }
        
        .selector-button:not(:disabled):hover {
            background: rgba(255, 255, 255, 0.4);
        }
        
        .dropdown-menu {
            position: absolute;
            top: calc(100% + 4px);
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.5);
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            max-height: 200px;
            overflow-y: auto;
            z-index: 30;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.2s ease;
            width: 120px;
        }
        
        .dropdown-menu.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }
        
        .dropdown-item {
            padding: 8px 12px;
            font-size: 12px;
            color: var(--color-text-primary);
            transition: all 0.2s ease;
            cursor: pointer;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        .dropdown-item:hover {
            background: rgba(255, 255, 255, 0.5);
        }
        
        .dropdown-item.selected {
            background: rgba(168, 230, 207, 0.3);
        }
        
        /* 标签样式 */
        .tag {
            background: rgba(168, 230, 207, 0.3);
            border-radius: 12px;
            padding: 4px 8px;
            font-size: 10px;
            color: var(--color-text-secondary);
        }
        
        /* 旋转控制器样式 */
        .rotate-control {
            position: absolute;
            bottom: 16px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(255, 255, 255, 0.4);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 6px 12px;
            display: flex;
            align-items: center;
        }
        
        .rotate-button {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 8px;
        }
    </style>
</head>
<body>
    <!-- 渐变背景 -->
    <div class="gradient-background"></div>

    <!-- 主内容区 -->
    <div class="pt-2 pb-20 h-full overflow-y-scroll">
        <div class="content-container py-2">
            <!-- 顶部导航栏 -->
            <div class="flex items-center justify-between mb-4">
                <div class="flex items-center">
                    <a href="../pages/profile.html" class="mr-3">
                        <i class="fas fa-arrow-left text-[var(--color-text-primary)]"></i>
                    </a>
                    <h1 class="text-sm font-semibold text-[var(--color-text-primary)]">体型数据</h1>
                </div>
                <button id="editButton" class="text-xs text-[var(--color-text-primary)]">
                    编辑
                </button>
            </div>

                <!-- 3D模型展示 -->
                <div class="model-container mb-4">
                    <img src="../assets/images/2.png" class="w-full h-full object-contain" alt="3D体型模型">
                    
                    <!-- 旋转控制器 -->
                    <div class="rotate-control">
                        <button class="rotate-button">
                            <i class="fas fa-undo-alt text-[var(--color-text-primary)] text-xs"></i>
                        </button>
                        <span class="text-xs text-[var(--color-text-primary)]">旋转查看</span>
                        <button class="rotate-button">
                            <i class="fas fa-redo-alt text-[var(--color-text-primary)] text-xs"></i>
                        </button>
                    </div>
                </div>
                
            <!-- 基础体型数据 -->
                <div class="enhanced-glass rounded-xl p-4 mb-4">
                <h3 class="text-xs font-medium text-[var(--color-text-primary)] mb-3">基础体型</h3>
                    
                    <div class="data-item">
                    <span class="text-xs text-[var(--color-text-primary)]">身高类型</span>
                    <div class="selector-wrapper">
                        <button class="selector-button" data-type="height" disabled>
                            <span class="text-xs text-[var(--color-text-secondary)]">标准身高</span>
                            <i class="fas fa-chevron-down text-[10px] ml-2"></i>
                        </button>
                        <div class="dropdown-menu" id="heightDropdown">
                            <!-- 选项将通过 JavaScript 动态添加 -->
                        </div>
                        </div>
                    </div>
                    
                    <div class="data-item">
                        <span class="text-xs text-[var(--color-text-primary)]">腰型</span>
                        <div class="selector-wrapper">
                            <button class="selector-button" data-type="waistShape" disabled>
                                <span class="text-xs text-[var(--color-text-secondary)]">直筒</span>
                                <i class="fas fa-chevron-down text-[10px] ml-2"></i>
                            </button>
                            <div class="dropdown-menu" id="waistShapeDropdown"></div>
                        </div>
                    </div>
                    <div class="data-item">
                        <span class="text-xs text-[var(--color-text-primary)]">肚腩</span>
                        <div class="selector-wrapper">
                            <button class="selector-button" data-type="belly" disabled>
                                <span class="text-xs text-[var(--color-text-secondary)]">没有</span>
                                <i class="fas fa-chevron-down text-[10px] ml-2"></i>
                            </button>
                            <div class="dropdown-menu" id="bellyDropdown"></div>
                        </div>
                    </div>
                    <div class="data-item">
                        <span class="text-xs text-[var(--color-text-primary)]">臀型</span>
                        <div class="selector-wrapper">
                            <button class="selector-button" data-type="hip" disabled>
                                <span class="text-xs text-[var(--color-text-secondary)]">下榻</span>
                                <i class="fas fa-chevron-down text-[10px] ml-2"></i>
                            </button>
                            <div class="dropdown-menu" id="hipDropdown"></div>
                        </div>
                    </div>
                    <div class="data-item">
                        <span class="text-xs text-[var(--color-text-primary)]">胯型</span>
                        <div class="selector-wrapper">
                            <button class="selector-button" data-type="hipShape" disabled>
                                <span class="text-xs text-[var(--color-text-secondary)]">直筒</span>
                                <i class="fas fa-chevron-down text-[10px] ml-2"></i>
                            </button>
                            <div class="dropdown-menu" id="hipShapeDropdown"></div>
                        </div>
                    </div>
                    <div class="data-item">
                        <span class="text-xs text-[var(--color-text-primary)]">肩膀</span>
                        <div class="selector-wrapper">
                            <button class="selector-button" data-type="shoulderWidth" disabled>
                                <span class="text-xs text-[var(--color-text-secondary)]">正常</span>
                                <i class="fas fa-chevron-down text-[10px] ml-2"></i>
                            </button>
                            <div class="dropdown-menu" id="shoulderWidthDropdown"></div>
                        </div>
                    </div>
                    <div class="data-item">
                        <span class="text-xs text-[var(--color-text-primary)]">臂长</span>
                        <div class="selector-wrapper">
                            <button class="selector-button" data-type="armLength" disabled>
                                <span class="text-xs text-[var(--color-text-secondary)]">正常</span>
                                <i class="fas fa-chevron-down text-[10px] ml-2"></i>
                            </button>
                            <div class="dropdown-menu" id="armLengthDropdown"></div>
                        </div>
                    </div>
                    <div class="data-item">
                        <span class="text-xs text-[var(--color-text-primary)]">臂围</span>
                        <div class="selector-wrapper">
                            <button class="selector-button" data-type="armCircum" disabled>
                                <span class="text-xs text-[var(--color-text-secondary)]">正常</span>
                                <i class="fas fa-chevron-down text-[10px] ml-2"></i>
                            </button>
                            <div class="dropdown-menu" id="armCircumDropdown"></div>
                        </div>
                    </div>
                    <div class="data-item">
                        <span class="text-xs text-[var(--color-text-primary)]">胯部</span>
                        <div class="selector-wrapper">
                            <button class="selector-button" data-type="hipWidth" disabled>
                                <span class="text-xs text-[var(--color-text-secondary)]">正常</span>
                                <i class="fas fa-chevron-down text-[10px] ml-2"></i>
                            </button>
                            <div class="dropdown-menu" id="hipWidthDropdown"></div>
                        </div>
                    </div>
                    <div class="data-item">
                        <span class="text-xs text-[var(--color-text-primary)]">大腿</span>
                        <div class="selector-wrapper">
                            <button class="selector-button" data-type="thigh" disabled>
                                <span class="text-xs text-[var(--color-text-secondary)]">正常</span>
                                <i class="fas fa-chevron-down text-[10px] ml-2"></i>
                            </button>
                            <div class="dropdown-menu" id="thighDropdown"></div>
                        </div>
                    </div>
                    <div class="data-item">
                        <span class="text-xs text-[var(--color-text-primary)]">小腿</span>
                        <div class="selector-wrapper">
                            <button class="selector-button" data-type="calf" disabled>
                                <span class="text-xs text-[var(--color-text-secondary)]">正常</span>
                                <i class="fas fa-chevron-down text-[10px] ml-2"></i>
                            </button>
                            <div class="dropdown-menu" id="calfDropdown"></div>
                        </div>
                    </div>
                    <div class="data-item">
                        <span class="text-xs text-[var(--color-text-primary)]">上下身粗</span>
                        <div class="selector-wrapper">
                            <button class="selector-button" data-type="bodyFat" disabled>
                                <span class="text-xs text-[var(--color-text-secondary)]">匀称</span>
                                <i class="fas fa-chevron-down text-[10px] ml-2"></i>
                            </button>
                            <div class="dropdown-menu" id="bodyFatDropdown"></div>
                        </div>
                    </div>
                    <div class="data-item">
                        <span class="text-xs text-[var(--color-text-primary)]">上下身长</span>
                        <div class="selector-wrapper">
                            <button class="selector-button" data-type="bodyLength" disabled>
                                <span class="text-xs text-[var(--color-text-secondary)]">匀称</span>
                                <i class="fas fa-chevron-down text-[10px] ml-2"></i>
                            </button>
                            <div class="dropdown-menu" id="bodyLengthDropdown"></div>
                        </div>
                    </div>
                </div>
                
                <!-- 体型分析 -->
                <div class="enhanced-glass rounded-xl p-4 mb-4">
                    <div class="flex justify-between items-center mb-3">
                    <h3 class="text-xs font-medium text-[var(--color-text-primary)]">体型特点</h3>
                    <span id="bodyTypeTag" class="tag">
                        沙漏型体型
                        </span>
                    </div>
                    
                <div class="space-y-3">
                    <p class="text-[10px] text-[var(--color-text-secondary)]">
                        <i class="fas fa-check-circle text-[#a8e6cf] mr-2"></i>
                        肩部和臀部宽度相近，腰部明显收窄
                    </p>
                    <p class="text-[10px] text-[var(--color-text-secondary)]">
                        <i class="fas fa-check-circle text-[#a8e6cf] mr-2"></i>
                        身材比例协调，曲线优美
                    </p>
                    <p class="text-[10px] text-[var(--color-text-secondary)]">
                        <i class="fas fa-check-circle text-[#a8e6cf] mr-2"></i>
                        适合展现腰部曲线的服装款式
                    </p>
                </div>
            </div>

            <!-- 穿衣建议 -->
                <div class="enhanced-glass rounded-xl p-4 mb-4">
                <div class="flex justify-between items-center mb-3">
                    <h3 class="text-xs font-medium text-[var(--color-text-primary)]">穿衣建议</h3>
                    <span class="tag">
                        个性化推荐
                    </span>
                </div>
                
                <div class="space-y-4">
                    <div>
                        <h4 class="text-xs text-[var(--color-text-primary)] mb-2">适合的款式</h4>
                        <div class="space-y-2">
                            <p class="text-[10px] text-[var(--color-text-secondary)]">
                                <i class="fas fa-tshirt text-[#a8e6cf] mr-2"></i>
                                收腰设计的上衣和外套
                            </p>
                            <p class="text-[10px] text-[var(--color-text-secondary)]">
                                <i class="fas fa-female text-[#a8e6cf] mr-2"></i>
                                高腰裤装和裙装
                            </p>
                            <p class="text-[10px] text-[var(--color-text-secondary)]">
                                <i class="fas fa-vest text-[#a8e6cf] mr-2"></i>
                                V领、一字领等展现锁骨的领型
                            </p>
                        </div>
                    </div>
                    
                    <div>
                        <h4 class="text-xs text-[var(--color-text-primary)] mb-2">搭配技巧</h4>
                        <div class="space-y-2">
                            <p class="text-[10px] text-[var(--color-text-secondary)]">
                                <i class="fas fa-magic text-[#a8e6cf] mr-2"></i>
                                使用腰带突出腰部曲线
                            </p>
                            <p class="text-[10px] text-[var(--color-text-secondary)]">
                                <i class="fas fa-ruler text-[#a8e6cf] mr-2"></i>
                                选择贴合但不紧绷的版型
                            </p>
                            <p class="text-[10px] text-[var(--color-text-secondary)]">
                                <i class="fas fa-balance-scale text-[#a8e6cf] mr-2"></i>
                                注意上下装比例平衡
                            </p>
                        </div>
                    </div>
                    
                    <div>
                        <h4 class="text-xs text-[var(--color-text-primary)] mb-2">建议避免</h4>
                        <div class="space-y-2">
                            <p class="text-[10px] text-[var(--color-text-secondary)]">
                                <i class="fas fa-times-circle text-[#ffb5b5] mr-2"></i>
                                过于宽大或直筒型的单品
                            </p>
                            <p class="text-[10px] text-[var(--color-text-secondary)]">
                                <i class="fas fa-times-circle text-[#ffb5b5] mr-2"></i>
                                不显腰线的连身装
                            </p>
                        </div>
                        </div>
                    </div>
                </div>
                
            <!-- 尺码建议 -->
                <div class="enhanced-glass rounded-xl p-4 mb-4">
                <h3 class="text-xs font-medium text-[var(--color-text-primary)] mb-3">尺码建议</h3>
                    
                <div class="space-y-3">
                    <div class="flex justify-between items-center">
                        <span class="text-xs text-[var(--color-text-primary)]">上装尺码</span>
                        <span class="text-xs text-[var(--color-text-primary)]">M码 (中码)</span>
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <span class="text-xs text-[var(--color-text-primary)]">下装尺码</span>
                        <span class="text-xs text-[var(--color-text-primary)]">S码 (小码)</span>
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <span class="text-xs text-[var(--color-text-primary)]">鞋码</span>
                        <span class="text-xs text-[var(--color-text-primary)]">37码</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 旋转控制
        const rotateButtons = document.querySelectorAll('.rotate-button');
        const modelImage = document.querySelector('.model-container img');
        let rotationDegree = 0;
        
        rotateButtons.forEach((button, index) => {
            button.addEventListener('click', function() {
                // 左旋转按钮
                if (index === 0) {
                    rotationDegree -= 90;
                } 
                // 右旋转按钮
                else {
                    rotationDegree += 90;
                }
                
                modelImage.style.transform = `rotateY(${rotationDegree}deg)`;
                modelImage.style.transition = 'transform 0.5s ease';
            });
        });
        
        // 选择器数据
        const selectorData = {
            height: {
                options: [
                    '娇小型',
                    '偏矮',
                    '中等偏下',
                    '标准身高',
                    '中等偏上',
                    '偏高',
                    '高挑型'
                ]
            },
            waistShape: {
                options: ['直筒', '略有曲线', '有曲线', '曲线较明显', '曲线明显']
            },
            belly: {
                options: ['没有', '略有小肚腩', '小肚腩', '偏大肚腩', '大肚腩']
            },
            hip: {
                options: ['下榻', '略有上翘', '正常', '较上翘', '上翘']
            },
            hipShape: {
                options: ['直筒', '略有曲线', '有曲线', '曲线较明显', '曲线明显']
            },
            shoulderWidth: {
                options: ['窄', '偏窄', '正常', '偏宽', '宽']
            },
            armLength: {
                options: ['短', '偏短', '正常', '偏长', '长']
            },
            armCircum: {
                options: ['细', '偏细', '正常', '偏粗', '粗']
            },
            hipWidth: {
                options: ['窄', '偏窄', '正常', '偏宽', '宽']
            },
            thigh: {
                options: ['细', '偏细', '正常', '偏粗', '粗']
            },
            calf: {
                options: ['细', '偏细', '正常', '偏粗', '粗']
            },
            bodyFat: {
                options: ['上身粗', '偏上身粗', '匀称', '偏下身粗', '下身粗']
            },
            bodyLength: {
                options: ['上身长', '偏上身长', '匀称', '偏下身长', '下身长']
            }
        };

        // 初始化下拉菜单
        function initializeDropdowns() {
            const selectorButtons = document.querySelectorAll('.selector-button');
            let activeWrapper = null;
            
            selectorButtons.forEach(button => {
                const type = button.getAttribute('data-type');
                const wrapper = button.closest('.selector-wrapper');
                const dropdown = button.nextElementSibling;
                
                // 创建下拉选项
                selectorData[type].options.forEach(option => {
                    const item = document.createElement('div');
                    item.className = 'dropdown-item';
                    item.textContent = option;
                    if (option === button.querySelector('span').textContent) {
                        item.classList.add('selected');
                    }
                    
                    item.addEventListener('click', (e) => {
                        e.stopPropagation();
                        // 更新按钮文本
                        button.querySelector('span').textContent = option;
                        
                        // 更新选中状态
                        dropdown.querySelectorAll('.dropdown-item').forEach(item => {
                            item.classList.remove('selected');
                        });
                        item.classList.add('selected');
                        
                        // 关闭下拉菜单
                        dropdown.classList.remove('show');
                        wrapper.classList.remove('active');
                        activeWrapper = null;
                        
                        // 更新体型分析
                        updateBodyTypeAnalysis();
                    });
                    
                    dropdown.appendChild(item);
                });
                
                // 点击按钮显示/隐藏下拉菜单
                button.addEventListener('click', (e) => {
                    e.stopPropagation();
                    if (button.disabled) return;
                    
                    // 如果当前有活动的下拉框，先关闭它
                    if (activeWrapper && activeWrapper !== wrapper) {
                        activeWrapper.querySelector('.dropdown-menu').classList.remove('show');
                        activeWrapper.classList.remove('active');
                    }
                    
                    // 切换当前下拉框的状态
                    const isShowing = dropdown.classList.toggle('show');
                    wrapper.classList.toggle('active');
                    activeWrapper = isShowing ? wrapper : null;
                });
            });
            
            // 点击其他区域关闭下拉菜单
            document.addEventListener('click', () => {
                if (activeWrapper) {
                    activeWrapper.querySelector('.dropdown-menu').classList.remove('show');
                    activeWrapper.classList.remove('active');
                    activeWrapper = null;
                }
            });
        }

        // 更新体型分析
        function updateBodyTypeAnalysis() {
            const height = document.querySelector('[data-type="height"] span').textContent;
            const waistShape = document.querySelector('[data-type="waistShape"] span').textContent;
            const belly = document.querySelector('[data-type="belly"] span').textContent;
            const hip = document.querySelector('[data-type="hip"] span').textContent;
            const hipShape = document.querySelector('[data-type="hipShape"] span').textContent;
            const shoulderWidth = document.querySelector('[data-type="shoulderWidth"] span').textContent;
            const armLength = document.querySelector('[data-type="armLength"] span').textContent;
            const armCircum = document.querySelector('[data-type="armCircum"] span').textContent;
            const hipWidth = document.querySelector('[data-type="hipWidth"] span').textContent;
            const thigh = document.querySelector('[data-type="thigh"] span').textContent;
            const calf = document.querySelector('[data-type="calf"] span').textContent;
            const bodyFat = document.querySelector('[data-type="bodyFat"] span').textContent;
            const bodyLength = document.querySelector('[data-type="bodyLength"] span').textContent;
            
            // 更新体型特点标签
            document.querySelector('#bodyTypeTag').textContent = `${waistShape}体型`;
            
            // 更新体型特点描述
            const features = document.querySelector('.space-y-3');
            features.innerHTML = '';
            
            // 添加体型特点描述
            const descriptions = [
                `整体：${height}，${waistShape}，${belly}肚腩，${hip}臀型，${hipShape}胯型，${shoulderWidth}肩宽，${armLength}臂长，${armCircum}臂围，${hipWidth}胯部，${thigh}大腿，${calf}小腿，${bodyFat}身体粗，${bodyLength}身体长`
            ];
            
            descriptions.forEach(desc => {
                const p = document.createElement('p');
                p.className = 'text-[10px] text-[var(--color-text-secondary)]';
                p.innerHTML = `<i class="fas fa-check-circle text-[#a8e6cf] mr-2"></i>${desc}`;
                features.appendChild(p);
            });
        }

        // 编辑模式切换
        const editButton = document.getElementById('editButton');
        let isEditMode = false;
        
        editButton.addEventListener('click', function() {
            isEditMode = !isEditMode;
            
            if (isEditMode) {
                // 进入编辑模式
                editButton.textContent = '完成';
                document.body.classList.add('edit-mode');
                
                // 启用所有选择器按钮
                document.querySelectorAll('.selector-button').forEach(button => {
                    button.disabled = false;
                });
            } else {
                // 退出编辑模式
                editButton.textContent = '编辑';
                document.body.classList.remove('edit-mode');
                
                // 禁用所有选择器按钮
                document.querySelectorAll('.selector-button').forEach(button => {
                    button.disabled = true;
                });
                
                // 关闭所有下拉菜单
                document.querySelectorAll('.dropdown-menu').forEach(menu => {
                    menu.classList.remove('show');
                });
            }
        });

        // 初始化下拉菜单
        initializeDropdowns();
    </script>
</body>
</html> 