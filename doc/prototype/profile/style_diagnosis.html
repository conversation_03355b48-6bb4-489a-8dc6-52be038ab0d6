<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <title>风格诊断 - StylishLink</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../styles/main.css" rel="stylesheet">
    <style>
        ::-webkit-scrollbar {
            display: none;
        }
        
        * {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            color: var(--color-text-primary);
            line-height: 1.5;
        }
        
        .gradient-background {
            background: linear-gradient(135deg, #c2e9fb 0%, #d8c6fd 100%);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: -1;
            opacity: 0.8;
        }
        
        .enhanced-glass {
            background: rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(12px);
            border: 1px solid rgba(255, 255, 255, 0.4);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
        }
        
        .glass-button {
            background: rgba(255, 255, 255, 0.5);
            backdrop-filter: blur(8px);
            border: 1px solid rgba(255, 255, 255, 0.6);
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }
        
        .glass-button:hover {
            background: rgba(255, 255, 255, 0.7);
            transform: translateY(-1px);
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
        }
        
        .tab-button {
            position: relative;
            padding: 10px 20px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 0;
            font-size: 14px;
            font-weight: 500;
            color: rgba(0, 0, 0, 0.6);
            transition: all 0.3s ease;
        }
        
        .tab-button.active {
            background: rgba(255, 255, 255, 0.8);
            color: rgba(0, 0, 0, 0.8);
            font-weight: 600;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .style-card {
            background: rgba(255, 255, 255, 0.85);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 16px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        }
        
        .style-label {
            display: inline-block;
            padding: 4px 10px;
            margin-right: 6px;
            margin-bottom: 6px;
            border-radius: 16px;
            font-size: 12px;
            background: rgba(0, 0, 0, 0.05);
        }
        
        .style-example {
            border-radius: 16px;
            overflow: hidden;
            margin-bottom: 12px;
            background: rgba(255, 255, 255, 0.4);
            backdrop-filter: blur(8px);
            border: 1px solid rgba(255, 255, 255, 0.4);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06);
            transition: all 0.3s ease;
        }
        
        .style-example:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
        }
        
        .style-example img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .star-example {
            width: 88px;
            height: 88px;
            border-radius: 50%;
            overflow: hidden;
            margin-bottom: 8px;
            border: 2px solid rgba(255, 255, 255, 0.6);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
        }
        
        .star-example:hover {
            transform: scale(1.05);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
        }
        
        .star-example img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .recommendation-card {
            background: rgba(255, 255, 255, 0.5);
            backdrop-filter: blur(8px);
            border: 1px solid rgba(255, 255, 255, 0.4);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06);
            border-radius: 16px;
            overflow: hidden;
            margin-bottom: 16px;
            transition: all 0.3s ease;
        }
        
        .recommendation-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
        }
        
        .status-indicator {
            position: absolute;
            bottom: 12px;
            right: 12px;
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            font-size: 14px;
            backdrop-filter: blur(4px);
            border: 1px solid rgba(255, 255, 255, 0.4);
        }
        
        .status-positive {
            background: rgba(72, 187, 120, 0.85);
            color: white;
        }
        
        .status-negative {
            background: rgba(245, 101, 101, 0.85);
            color: white;
        }
        
        .diamond-tag {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 14px 28px;
            margin: 24px auto;
            background: rgba(255, 255, 255, 0.4);
            backdrop-filter: blur(8px);
            border: 1px solid rgba(255, 255, 255, 0.5);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
            width: 80%;
            text-align: center;
            font-size: 15px;
            font-weight: 600;
            color: var(--color-text-primary);
            border-radius: 12px;
        }
        
        .diamond-tag::before,
        .diamond-tag::after {
            content: '✦';
            position: absolute;
            font-size: 16px;
            color: #333;
        }
        
        .diamond-tag::before {
            left: -5px;
            top: 50%;
            transform: translateY(-50%);
        }
        
        .diamond-tag::after {
            right: -5px;
            top: 50%;
            transform: translateY(-50%);
        }
        
        .content-container {
            padding-left: 6%;
            padding-right: 6%;
            padding-bottom: 100px;
            max-width: 768px;
            margin: 0 auto;
        }
    </style>
</head>
<body>
    <!-- 渐变背景 -->
    <div class="gradient-background"></div>
    
    <!-- 主内容区 -->
    <div class="pt-2 pb-20 h-full overflow-y-scroll">
        <div class="content-container py-2">
            <!-- 顶部导航栏 -->
            <div class="flex items-center justify-between mb-4">
                <div class="flex items-center">
                    <a href="../pages/profile.html" class="mr-3">
                        <i class="fas fa-arrow-left text-[var(--color-text-primary)]"></i>
                    </a>
                    <h1 class="text-sm font-semibold text-[var(--color-text-primary)]">风格诊断</h1>
                </div>
                <button id="shareButton" class="text-xs text-[var(--color-text-primary)]">
                    <i class="fas fa-share-alt"></i>
                </button>
                        </div>

            <!-- 身材分析与风格卡片 -->
            <div class="diamond-tag">
                <span class="text-[15px] font-semibold text-[var(--color-text-primary)]">你是X型身材</span>
            </div>
            
            <p class="text-sm leading-relaxed mb-6 text-center text-[var(--color-text-secondary)] px-4">
                X型身材的特征是丰满的胸部，纤细的腰，圆润的臀部，身材非常有曲线。穿搭建议：选择凸显腰线的衣服，不要穿的太过宽松，会显得虎背熊腰。
                </p>
                
                <!-- 风格卡片 -->
            <div class="enhanced-glass rounded-xl p-6 mb-8">
                <div class="flex mb-4 justify-center gap-4">
                    <div class="flex-1">
                        <span class="block text-center text-sm font-medium mb-2 text-[var(--color-text-primary)]">体型</span>
                        <div class="bg-white/40 backdrop-blur-sm rounded-lg py-2 px-4 text-center text-sm text-[var(--color-text-primary)]">X型身材</div>
                        </div>
                    <div class="flex-1">
                        <span class="block text-center text-sm font-medium mb-2 text-[var(--color-text-primary)]">风格</span>
                        <div class="bg-white/40 backdrop-blur-sm rounded-lg py-2 px-4 text-center text-sm text-[var(--color-text-primary)]">酒脱自然</div>
                        </div>
                    </div>
                    <div>
                    <span class="block text-sm font-medium mb-2 text-center text-[var(--color-text-primary)]">关键词</span>
                    <div class="flex flex-wrap justify-center gap-2">
                        <div class="bg-white/40 backdrop-blur-sm rounded-lg py-2 px-4 text-xs text-[var(--color-text-primary)]">随性</div>
                        <div class="bg-white/40 backdrop-blur-sm rounded-lg py-2 px-4 text-xs text-[var(--color-text-primary)]">不羁</div>
                        <div class="bg-white/40 backdrop-blur-sm rounded-lg py-2 px-4 text-xs text-[var(--color-text-primary)]">酒脱</div>
                        <div class="bg-white/40 backdrop-blur-sm rounded-lg py-2 px-4 text-xs text-[var(--color-text-primary)]">自然</div>
                    </div>
                </div>
            </div>
            
            <!-- 穿搭小贴士 -->
            <div class="mb-10">
                <h3 class="font-semibold text-base mb-6 flex items-center gap-2 text-[var(--color-text-primary)]">
                    <i class="fas fa-sparkles text-sm"></i>
                    <span>穿搭小贴士</span>
                </h3>
                
                <!-- 第一组推荐 -->
                <div class="grid grid-cols-2 gap-4 mb-6">
                    <div class="recommendation-card">
                        <div class="p-3">
                            <span class="inline-block px-3 py-1 rounded-full text-xs font-medium bg-red-100/80 text-red-500 mb-2">不推荐</span>
                            <div class="flex justify-center mt-2">
                                <img src="https://images.pexels.com/photos/2887766/pexels-photo-2887766.jpeg" 
                                     alt="垫肩上衣和低腰裤" class="h-64 w-full object-cover rounded-lg">
                            </div>
                            <div class="text-center text-sm mt-3 pb-2 text-[var(--color-text-secondary)]">垫肩上衣 低腰裤</div>
                        </div>
                        <div class="status-indicator status-negative">
                            <i class="fas fa-times"></i>
                        </div>
                    </div>
                    <div class="recommendation-card">
                        <div class="p-3">
                            <span class="inline-block px-3 py-1 rounded-full text-xs font-medium bg-green-100/80 text-green-500 mb-2">推荐</span>
                            <div class="flex justify-center mt-2">
                                <img src="https://images.pexels.com/photos/2043590/pexels-photo-2043590.jpeg" 
                                     alt="高腰裤 一字领上衣" class="h-64 w-full object-cover rounded-lg">
                            </div>
                            <div class="text-center text-sm mt-3 pb-2 text-[var(--color-text-secondary)]">高腰裤 一字领上衣</div>
                        </div>
                        <div class="status-indicator status-positive">
                            <i class="fas fa-check"></i>
                        </div>
                    </div>
                </div>
                
                <!-- 第二组推荐 -->
                <div class="grid grid-cols-2 gap-4 mb-6">
                    <div class="recommendation-card">
                        <div class="p-3">
                            <span class="inline-block px-3 py-1 rounded-full text-xs font-medium bg-red-100/80 text-red-500 mb-2">不推荐</span>
                            <div class="flex justify-center mt-2">
                                <img src="https://images.pexels.com/photos/2681751/pexels-photo-2681751.jpeg" 
                                     alt="宽松连衣裙 蛋糕裙" class="h-64 w-full object-cover rounded-lg">
                            </div>
                            <div class="text-center text-sm mt-3 pb-2 text-[var(--color-text-secondary)]">宽松连衣裙 蛋糕裙</div>
                        </div>
                        <div class="status-indicator status-negative">
                            <i class="fas fa-times"></i>
                        </div>
                    </div>
                    <div class="recommendation-card">
                        <div class="p-3">
                            <span class="inline-block px-3 py-1 rounded-full text-xs font-medium bg-green-100/80 text-green-500 mb-2">推荐</span>
                            <div class="flex justify-center mt-2">
                                <img src="https://images.pexels.com/photos/2235071/pexels-photo-2235071.jpeg" 
                                     alt="紧身上衣 包臀裙" class="h-64 w-full object-cover rounded-lg">
                            </div>
                            <div class="text-center text-sm mt-3 pb-2 text-[var(--color-text-secondary)]">紧身上衣 包臀裙</div>
                        </div>
                        <div class="status-indicator status-positive">
                            <i class="fas fa-check"></i>
                        </div>
                    </div>
                </div>
                
                <!-- 第三组推荐 -->
                <div class="grid grid-cols-2 gap-4">
                    <div class="recommendation-card">
                        <div class="p-3">
                            <span class="inline-block px-3 py-1 rounded-full text-xs font-medium bg-red-100/80 text-red-500 mb-2">不推荐</span>
                            <div class="flex justify-center mt-2">
                                <img src="https://images.pexels.com/photos/2866119/pexels-photo-2866119.jpeg" 
                                     alt="宽松上衣 高领上衣" class="h-64 w-full object-cover rounded-lg">
                            </div>
                            <div class="text-center text-sm mt-3 pb-2 text-[var(--color-text-secondary)]">宽松上衣 高领上衣</div>
                        </div>
                        <div class="status-indicator status-negative">
                            <i class="fas fa-times"></i>
                        </div>
                    </div>
                    <div class="recommendation-card">
                        <div class="p-3">
                            <span class="inline-block px-3 py-1 rounded-full text-xs font-medium bg-green-100/80 text-green-500 mb-2">推荐</span>
                            <div class="flex justify-center mt-2">
                                <img src="https://images.pexels.com/photos/2229490/pexels-photo-2229490.jpeg" 
                                     alt="X型连衣裙 包臀裙" class="h-64 w-full object-cover rounded-lg">
                            </div>
                            <div class="text-center text-sm mt-3 pb-2 text-[var(--color-text-secondary)]">X型连衣裙 包臀裙</div>
                        </div>
                        <div class="status-indicator status-positive">
                            <i class="fas fa-check"></i>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 风格tab切换栏 -->
            <div class="flex justify-center mb-6 border-b border-white/30">
                <button class="tab-button active" data-tab="style1">酒脱自然风格</button>
                <button class="tab-button" data-tab="style2">优雅知性风格</button>
            </div>

            <!-- 风格tab内容区 -->
            <div id="style1" class="tab-content active">
                <!-- 你是酒脱自然风格及其下方所有内容（原有内容整体移动到这里） -->
                <p class="text-sm leading-relaxed mt-4 mb-6 text-center text-[var(--color-text-secondary)] px-4">
                    随性、不羁、酒脱的自然感之美，同是酒脱自然型的人，也会因为风格基因占比的不同展现出不同的气质。
                </p>
                
                <!-- 风格类型 - 清新自然型 -->
                <div class="mb-10">
                    <h3 class="font-semibold text-base mb-6 flex items-center gap-2 text-[var(--color-text-primary)]">
                        <i class="fas fa-leaf text-sm"></i>
                        <span>清新自然型</span>
                    </h3>
                    <p class="text-sm leading-relaxed mb-4 text-[var(--color-text-secondary)]">清新自然型-轻松柔切的随性酒脱感</p>
                    <div class="grid grid-cols-2 gap-4">
                        <div class="style-example">
                            <img src="https://images.pexels.com/photos/2896853/pexels-photo-2896853.jpeg" 
                                 alt="简约松弛感" class="w-full h-64 object-cover">
                            <div class="text-center text-sm py-3 text-[var(--color-text-secondary)]">简约松弛感</div>
                        </div>
                        <div class="style-example">
                            <img src="https://images.pexels.com/photos/2681751/pexels-photo-2681751.jpeg" 
                                 alt="森系清新风" class="w-full h-64 object-cover">
                            <div class="text-center text-sm py-3 text-[var(--color-text-secondary)]">森系清新风</div>
                        </div>
                    </div>
                </div>
                
                <!-- 风格类型 - 随性慵懒风 -->
                <div class="mb-8">
                    <h3 class="font-semibold text-base mb-3">✦ 随性慵懒风</h3>
                    <p class="text-sm mb-3">随性慵懒风-轻松柔切的随性酒脱感</p>
                    <div class="grid grid-cols-2 gap-3">
                        <div class="style-example h-64">
                            <img src="https://images.pexels.com/photos/2983464/pexels-photo-2983464.jpeg" alt="随性慵懒风">
                            <div class="text-center text-xs mt-1">随性慵懒风</div>
                        </div>
                        <div class="style-example h-64">
                            <img src="https://images.pexels.com/photos/2755612/pexels-photo-2755612.jpeg" alt="文艺休闲风">
                            <div class="text-center text-xs mt-1">文艺休闲风</div>
                        </div>
                    </div>
                </div>
                
                <!-- 参考明星 -->
                <div class="mb-10">
                    <h3 class="font-semibold text-base mb-6 flex items-center gap-2 text-[var(--color-text-primary)]">
                        <i class="fas fa-star text-sm"></i>
                        <span>参考明星</span>
                    </h3>
                    <div class="grid grid-cols-3 gap-6 mb-6">
                        <div class="flex flex-col items-center">
                            <div class="w-full h-32 overflow-hidden rounded-lg mb-2 shadow-md hover:shadow-lg transition-all">
                                <img src="https://images.pexels.com/photos/2867904/pexels-photo-2867904.jpeg" 
                                     alt="高圆圆穿搭" class="w-full h-full object-cover">
                            </div>
                            <span class="text-sm text-center text-[var(--color-text-secondary)]">高圆圆</span>
                        </div>
                        <div class="flex flex-col items-center">
                            <div class="w-full h-32 overflow-hidden rounded-lg mb-2 shadow-md hover:shadow-lg transition-all">
                                <img src="https://images.pexels.com/photos/2681751/pexels-photo-2681751.jpeg" 
                                     alt="汤唯穿搭" class="w-full h-full object-cover">
                            </div>
                            <span class="text-sm text-center text-[var(--color-text-secondary)]">汤唯</span>
                        </div>
                        <div class="flex flex-col items-center">
                            <div class="w-full h-32 overflow-hidden rounded-lg mb-2 shadow-md hover:shadow-lg transition-all">
                                <img src="https://images.pexels.com/photos/2896840/pexels-photo-2896840.jpeg" 
                                     alt="金高银穿搭" class="w-full h-full object-cover">
                            </div>
                            <span class="text-sm text-center text-[var(--color-text-secondary)]">金高银</span>
                    </div>
                </div>
                
                    <div class="grid grid-cols-3 gap-6">
                        <div class="flex flex-col items-center">
                            <div class="w-full h-32 overflow-hidden rounded-lg mb-2 shadow-md hover:shadow-lg transition-all">
                                <img src="https://images.pexels.com/photos/2850487/pexels-photo-2850487.jpeg" 
                                     alt="阿朵穿搭" class="w-full h-full object-cover">
                            </div>
                            <span class="text-sm text-center text-[var(--color-text-secondary)]">阿朵</span>
                        </div>
                        <div class="flex flex-col items-center">
                            <div class="w-full h-32 overflow-hidden rounded-lg mb-2 shadow-md hover:shadow-lg transition-all">
                                <img src="https://images.pexels.com/photos/2887766/pexels-photo-2887766.jpeg" 
                                     alt="刘雯穿搭" class="w-full h-full object-cover">
                            </div>
                            <span class="text-sm text-center text-[var(--color-text-secondary)]">刘雯</span>
                        </div>
                        <div class="flex flex-col items-center">
                            <div class="w-full h-32 overflow-hidden rounded-lg mb-2 shadow-md hover:shadow-lg transition-all">
                                <img src="https://images.pexels.com/photos/2896853/pexels-photo-2896853.jpeg" 
                                     alt="王紫璇穿搭" class="w-full h-full object-cover">
                            </div>
                            <span class="text-sm text-center text-[var(--color-text-secondary)]">王紫璇</span>
                        </div>
                    </div>
                </div>
            </div>
            <div id="style2" class="tab-content">
                <!-- 优雅知性风格内容，结构与style1一致，内容适当调整 -->
                <p class="text-sm leading-relaxed mt-4 mb-6 text-center text-[var(--color-text-secondary)] px-4">
                    优雅知性风格，气质温婉、知性大方，适合简约线条和高级感面料，展现女性柔美与自信。
                </p>
                <div class="mb-10">
                    <h3 class="font-semibold text-base mb-6 flex items-center gap-2 text-[var(--color-text-primary)]">
                        <i class="fas fa-leaf text-sm"></i>
                        <span>简约优雅型</span>
                    </h3>
                    <p class="text-sm leading-relaxed mb-4 text-[var(--color-text-secondary)]">简约优雅型-线条流畅、色彩柔和，突出知性美。</p>
                    <div class="grid grid-cols-2 gap-4">
                        <div class="style-example">
                            <img src="https://images.pexels.com/photos/2100063/pexels-photo-2100063.jpeg" alt="优雅连衣裙" class="w-full h-64 object-cover">
                            <div class="text-center text-sm py-3 text-[var(--color-text-secondary)]">优雅连衣裙</div>
                        </div>
                        <div class="style-example">
                            <img src="https://images.pexels.com/photos/1488463/pexels-photo-1488463.jpeg" alt="知性套装" class="w-full h-64 object-cover">
                            <div class="text-center text-sm py-3 text-[var(--color-text-secondary)]">知性套装</div>
                        </div>
                    </div>
                </div>
                <div class="mb-8">
                    <h3 class="font-semibold text-base mb-3">✦ 温婉气质型</h3>
                    <p class="text-sm mb-3">温婉气质型-柔和色彩与细腻面料，展现女性温柔。</p>
                    <div class="grid grid-cols-2 gap-3">
                        <div class="style-example h-64">
                            <img src="https://images.pexels.com/photos/415829/pexels-photo-415829.jpeg" alt="温婉连衣裙">
                            <div class="text-center text-xs mt-1">温婉连衣裙</div>
                        </div>
                        <div class="style-example h-64">
                            <img src="https://images.pexels.com/photos/733872/pexels-photo-733872.jpeg" alt="气质衬衫">
                            <div class="text-center text-xs mt-1">气质衬衫</div>
                        </div>
                    </div>
                </div>
                <div class="mb-10">
                    <h3 class="font-semibold text-base mb-6 flex items-center gap-2 text-[var(--color-text-primary)]">
                        <i class="fas fa-star text-sm"></i>
                        <span>参考明星</span>
                    </h3>
                    <div class="grid grid-cols-3 gap-6 mb-6">
                        <div class="flex flex-col items-center">
                            <div class="w-full h-32 overflow-hidden rounded-lg mb-2 shadow-md hover:shadow-lg transition-all">
                                <img src="https://images.pexels.com/photos/733872/pexels-photo-733872.jpeg" alt="刘诗诗穿搭" class="w-full h-full object-cover">
                            </div>
                            <span class="text-sm text-center text-[var(--color-text-secondary)]">刘诗诗</span>
                        </div>
                        <div class="flex flex-col items-center">
                            <div class="w-full h-32 overflow-hidden rounded-lg mb-2 shadow-md hover:shadow-lg transition-all">
                                <img src="https://images.pexels.com/photos/2100063/pexels-photo-2100063.jpeg" alt="高圆圆穿搭" class="w-full h-full object-cover">
                            </div>
                            <span class="text-sm text-center text-[var(--color-text-secondary)]">高圆圆</span>
                        </div>
                        <div class="flex flex-col items-center">
                            <div class="w-full h-32 overflow-hidden rounded-lg mb-2 shadow-md hover:shadow-lg transition-all">
                                <img src="https://images.pexels.com/photos/1488463/pexels-photo-1488463.jpeg" alt="倪妮穿搭" class="w-full h-full object-cover">
                            </div>
                            <span class="text-sm text-center text-[var(--color-text-secondary)]">倪妮</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 重新诊断按钮 -->
            <div class="flex justify-center mb-12">
                <button class="glass-button px-10 py-3 rounded-full text-sm font-medium text-[var(--color-text-primary)] flex items-center gap-2">
                    <i class="fas fa-redo text-sm"></i>
                    <span>重新诊断</span>
                </button>
            </div>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 添加按钮点击效果
            const buttons = document.querySelectorAll('button');
            buttons.forEach(button => {
                button.addEventListener('click', function(e) {
                    const ripple = document.createElement('div');
                    ripple.classList.add('ripple');
                    this.appendChild(ripple);
                    
                    const rect = this.getBoundingClientRect();
                    const x = e.clientX - rect.left;
                    const y = e.clientY - rect.top;
                    
                    ripple.style.left = x + 'px';
                    ripple.style.top = y + 'px';
                    
                    setTimeout(() => {
                        ripple.remove();
                    }, 600);
                });
            });

            // 新增tab切换逻辑
            const styleTabButtons = document.querySelectorAll('.tab-button[data-tab]');
            const styleTabContents = document.querySelectorAll('.tab-content');
            styleTabButtons.forEach(btn => {
                btn.addEventListener('click', function() {
                    styleTabButtons.forEach(b => b.classList.remove('active'));
                    this.classList.add('active');
                    const tab = this.getAttribute('data-tab');
                    styleTabContents.forEach(c => {
                        if (c.id === tab) {
                            c.classList.add('active');
                        } else {
                            c.classList.remove('active');
                        }
                    });
                });
            });
        });
    </script>
</body>
</html>