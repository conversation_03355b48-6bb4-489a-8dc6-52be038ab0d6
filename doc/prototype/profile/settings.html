<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <title>设置 - StylishLink</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../styles/main.css" rel="stylesheet">
    <style>
        ::-webkit-scrollbar {
            display: none;
        }
        
        * {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        
        .content-container {
            padding-left: 5%;
            padding-right: 5%;
        }

        .gradient-background {
            background: linear-gradient(135deg, #c2e9fb 0%, #d8c6fd 100%);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: -1;
        }
        
        .enhanced-glass {
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.05);
        }
        
        .glass-button {
            background: rgba(255, 255, 255, 0.4);
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.5);
            transition: all 0.3s ease;
        }
        
        .glass-button:hover {
            background: rgba(255, 255, 255, 0.6);
        }
        
        /* 设置项样式 */
        .setting-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .setting-item:last-child {
            border-bottom: none;
        }
        
        .setting-left {
            display: flex;
            align-items: center;
        }
        
        .setting-icon {
            width: 28px;
            height: 28px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
        }
        
        /* 开关按钮样式 */
        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 36px;
            height: 20px;
        }
        
        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(255, 255, 255, 0.3);
            transition: .4s;
            border-radius: 20px;
        }
        
        .toggle-slider:before {
            position: absolute;
            content: "";
            height: 16px;
            width: 16px;
            left: 2px;
            bottom: 2px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        
        input:checked + .toggle-slider {
            background-color: rgba(168, 230, 207, 0.8);
        }
        
        input:checked + .toggle-slider:before {
            transform: translateX(16px);
        }
        
        /* 分组标题 */
        .setting-group-title {
            font-size: 12px;
            font-weight: 500;
            color: var(--color-text-primary);
            margin: 20px 0 8px 0;
        }
    </style>
</head>
<body>
    <!-- 渐变背景 -->
    <div class="gradient-background"></div>

    <!-- 主内容区 -->
    <div class="pt-2 pb-20 h-full overflow-y-scroll">
        <div class="content-container py-2">
            <!-- 顶部导航栏 -->
            <div class="flex items-center justify-between mb-4">
                <div class="flex items-center">
                    <a href="../pages/profile.html" class="mr-3">
                        <i class="fas fa-arrow-left text-[var(--color-text-primary)]"></i>
                    </a>
                    <h1 class="text-sm font-semibold text-[var(--color-text-primary)]">设置</h1>
                </div>
            </div>

            <!-- 个人信息设置 -->
            <div class="setting-group-title">个人信息</div>
            <div class="enhanced-glass rounded-xl p-3 mb-4">
                <div class="setting-item">
                    <div class="setting-left">
                        <div class="setting-icon bg-[rgba(168,230,207,0.3)]">
                            <i class="fas fa-user text-[var(--color-text-primary)]"></i>
                        </div>
                        <div>
                            <div class="text-xs font-medium text-[var(--color-text-primary)]">编辑个人资料</div>
                            <div class="text-[10px] text-[var(--color-text-secondary)]">修改姓名、联系方式等基本信息</div>
                        </div>
                    </div>
                    <i class="fas fa-chevron-right text-[var(--color-text-secondary)] text-xs"></i>
                </div>
                
                <div class="setting-item">
                    <div class="setting-left">
                        <div class="setting-icon bg-[rgba(168,230,207,0.3)]">
                            <i class="fas fa-tshirt text-[var(--color-text-primary)]"></i>
                        </div>
                        <div>
                            <div class="text-xs font-medium text-[var(--color-text-primary)]">身材数据</div>
                            <div class="text-[10px] text-[var(--color-text-secondary)]">管理身高、体重等数据</div>
                        </div>
                    </div>
                    <i class="fas fa-chevron-right text-[var(--color-text-secondary)] text-xs"></i>
                </div>
                
                <div class="setting-item">
                    <div class="setting-left">
                        <div class="setting-icon bg-[rgba(168,230,207,0.3)]">
                            <i class="fas fa-palette text-[var(--color-text-primary)]"></i>
                        </div>
                        <div>
                            <div class="text-xs font-medium text-[var(--color-text-primary)]">风格偏好</div>
                            <div class="text-[10px] text-[var(--color-text-secondary)]">设置个人穿搭风格偏好</div>
                        </div>
                    </div>
                    <i class="fas fa-chevron-right text-[var(--color-text-secondary)] text-xs"></i>
                </div>
                
                <div class="setting-item">
                    <div class="setting-left">
                        <div class="setting-icon bg-[rgba(168,230,207,0.3)]">
                            <i class="fas fa-fire text-[var(--color-text-primary)]"></i>
                        </div>
                        <div>
                            <div class="text-xs font-medium text-[var(--color-text-primary)]">五行能量配置</div>
                            <div class="text-[10px] text-[var(--color-text-secondary)]">管理生日与命理信息</div>
                        </div>
                    </div>
                    <i class="fas fa-chevron-right text-[var(--color-text-secondary)] text-xs"></i>
                </div>
            </div>

            <!-- 账号安全 -->
            <div class="setting-group-title">账号安全</div>
            <div class="enhanced-glass rounded-xl p-3 mb-4">
                <div class="setting-item">
                    <div class="setting-left">
                        <div class="setting-icon bg-[rgba(255,154,158,0.3)]">
                            <i class="fas fa-lock text-[var(--color-text-primary)]"></i>
                        </div>
                        <div>
                            <div class="text-xs font-medium text-[var(--color-text-primary)]">修改密码</div>
                            <div class="text-[10px] text-[var(--color-text-secondary)]">更新账号登录密码</div>
                        </div>
                    </div>
                    <i class="fas fa-chevron-right text-[var(--color-text-secondary)] text-xs"></i>
                </div>
                
                <div class="setting-item">
                    <div class="setting-left">
                        <div class="setting-icon bg-[rgba(255,154,158,0.3)]">
                            <i class="fas fa-mobile-alt text-[var(--color-text-primary)]"></i>
                        </div>
                        <div>
                            <div class="text-xs font-medium text-[var(--color-text-primary)]">绑定手机</div>
                            <div class="text-[10px] text-[var(--color-text-secondary)]">已绑定：138****5678</div>
                        </div>
                    </div>
                    <i class="fas fa-chevron-right text-[var(--color-text-secondary)] text-xs"></i>
                </div>
                
                <div class="setting-item">
                    <div class="setting-left">
                        <div class="setting-icon bg-[rgba(255,154,158,0.3)]">
                            <i class="fas fa-shield-alt text-[var(--color-text-primary)]"></i>
                        </div>
                        <div>
                            <div class="text-xs font-medium text-[var(--color-text-primary)]">账号安全</div>
                            <div class="text-[10px] text-[var(--color-text-secondary)]">登录验证与保护设置</div>
                        </div>
                    </div>
                    <i class="fas fa-chevron-right text-[var(--color-text-secondary)] text-xs"></i>
                </div>
            </div>

            <!-- 通知设置 -->
            <div class="setting-group-title">通知设置</div>
            <div class="enhanced-glass rounded-xl p-3 mb-4">
                <div class="setting-item">
                    <div class="setting-left">
                        <div class="setting-icon bg-[rgba(255,234,167,0.3)]">
                            <i class="fas fa-bell text-[var(--color-text-primary)]"></i>
                        </div>
                        <div>
                            <div class="text-xs font-medium text-[var(--color-text-primary)]">每日穿搭推荐</div>
                            <div class="text-[10px] text-[var(--color-text-secondary)]">接收每日穿搭推荐通知</div>
                        </div>
                    </div>
                    <label class="toggle-switch">
                        <input type="checkbox" checked>
                        <span class="toggle-slider"></span>
                    </label>
                </div>
                
                <div class="setting-item">
                    <div class="setting-left">
                        <div class="setting-icon bg-[rgba(255,234,167,0.3)]">
                            <i class="fas fa-calendar-alt text-[var(--color-text-primary)]"></i>
                        </div>
                        <div>
                            <div class="text-xs font-medium text-[var(--color-text-primary)]">活动提醒</div>
                            <div class="text-[10px] text-[var(--color-text-secondary)]">重要活动前一天提醒</div>
                        </div>
                    </div>
                    <label class="toggle-switch">
                        <input type="checkbox" checked>
                        <span class="toggle-slider"></span>
                    </label>
                </div>
                
                <div class="setting-item">
                    <div class="setting-left">
                        <div class="setting-icon bg-[rgba(255,234,167,0.3)]">
                            <i class="fas fa-comment text-[var(--color-text-primary)]"></i>
                        </div>
                        <div>
                            <div class="text-xs font-medium text-[var(--color-text-primary)]">互动通知</div>
                            <div class="text-[10px] text-[var(--color-text-secondary)]">点赞、评论等互动提醒</div>
                        </div>
                    </div>
                    <label class="toggle-switch">
                        <input type="checkbox">
                        <span class="toggle-slider"></span>
                    </label>
                </div>
                
                <div class="setting-item">
                    <div class="setting-left">
                        <div class="setting-icon bg-[rgba(255,234,167,0.3)]">
                            <i class="fas fa-gift text-[var(--color-text-primary)]"></i>
                        </div>
                        <div>
                            <div class="text-xs font-medium text-[var(--color-text-primary)]">营销信息</div>
                            <div class="text-[10px] text-[var(--color-text-secondary)]">优惠活动、新功能等推送</div>
                        </div>
                    </div>
                    <label class="toggle-switch">
                        <input type="checkbox">
                        <span class="toggle-slider"></span>
                    </label>
                </div>
            </div>

            <!-- 隐私设置 -->
            <div class="setting-group-title">隐私设置</div>
            <div class="enhanced-glass rounded-xl p-3 mb-4">
                <div class="setting-item">
                    <div class="setting-left">
                        <div class="setting-icon bg-[rgba(184,198,219,0.3)]">
                            <i class="fas fa-eye text-[var(--color-text-primary)]"></i>
                        </div>
                        <div>
                            <div class="text-xs font-medium text-[var(--color-text-primary)]">穿搭可见性</div>
                            <div class="text-[10px] text-[var(--color-text-secondary)]">谁可以看到你的穿搭</div>
                        </div>
                    </div>
                    <span class="text-xs text-[var(--color-text-secondary)]">所有人</span>
                </div>
                
                <div class="setting-item">
                    <div class="setting-left">
                        <div class="setting-icon bg-[rgba(184,198,219,0.3)]">
                            <i class="fas fa-tshirt text-[var(--color-text-primary)]"></i>
                        </div>
                        <div>
                            <div class="text-xs font-medium text-[var(--color-text-primary)]">衣橱可见性</div>
                            <div class="text-[10px] text-[var(--color-text-secondary)]">谁可以看到你的衣橱</div>
                        </div>
                    </div>
                    <span class="text-xs text-[var(--color-text-secondary)]">仅自己</span>
                </div>
                
                <div class="setting-item">
                    <div class="setting-left">
                        <div class="setting-icon bg-[rgba(184,198,219,0.3)]">
                            <i class="fas fa-location-arrow text-[var(--color-text-primary)]"></i>
                        </div>
                        <div>
                            <div class="text-xs font-medium text-[var(--color-text-primary)]">位置信息</div>
                            <div class="text-[10px] text-[var(--color-text-secondary)]">允许访问位置用于天气推荐</div>
                        </div>
                    </div>
                    <label class="toggle-switch">
                        <input type="checkbox" checked>
                        <span class="toggle-slider"></span>
                    </label>
                </div>
            </div>

            <!-- 其他设置 -->
            <div class="setting-group-title">其他设置</div>
            <div class="enhanced-glass rounded-xl p-3 mb-4">
                <div class="setting-item">
                    <div class="setting-left">
                        <div class="setting-icon bg-[rgba(255,255,255,0.3)]">
                            <i class="fas fa-question-circle text-[var(--color-text-primary)]"></i>
                        </div>
                        <div>
                            <div class="text-xs font-medium text-[var(--color-text-primary)]">帮助与反馈</div>
                        </div>
                    </div>
                    <i class="fas fa-chevron-right text-[var(--color-text-secondary)] text-xs"></i>
                </div>
                
                <div class="setting-item">
                    <div class="setting-left">
                        <div class="setting-icon bg-[rgba(255,255,255,0.3)]">
                            <i class="fas fa-info-circle text-[var(--color-text-primary)]"></i>
                        </div>
                        <div>
                            <div class="text-xs font-medium text-[var(--color-text-primary)]">关于我们</div>
                        </div>
                    </div>
                    <i class="fas fa-chevron-right text-[var(--color-text-secondary)] text-xs"></i>
                </div>
                
                <div class="setting-item">
                    <div class="setting-left">
                        <div class="setting-icon bg-[rgba(255,255,255,0.3)]">
                            <i class="fas fa-trash-alt text-[var(--color-text-primary)]"></i>
                        </div>
                        <div>
                            <div class="text-xs font-medium text-[var(--color-text-primary)]">清除缓存</div>
                        </div>
                    </div>
                    <span class="text-xs text-[var(--color-text-secondary)]">23.5MB</span>
                </div>
            </div>

            <!-- 退出登录按钮 -->
            <button class="glass-button w-full py-3 rounded-full text-sm font-medium text-[#ff5458] mb-4">
                退出登录
            </button>
        </div>
    </div>
    
    <script>
        // 设置项点击
        const settingItems = document.querySelectorAll('.setting-item');
        settingItems.forEach(item => {
            if (item.querySelector('.fa-chevron-right')) {
                item.addEventListener('click', function() {
                    // 这里可以添加跳转逻辑
                    console.log('点击了设置项: ' + item.querySelector('.text-xs.font-medium').textContent);
                });
            }
        });
        
        // 开关切换
        const toggleSwitches = document.querySelectorAll('.toggle-switch input');
        toggleSwitches.forEach(toggle => {
            toggle.addEventListener('change', function() {
                const settingName = this.closest('.setting-item').querySelector('.text-xs.font-medium').textContent;
                console.log('切换了设置: ' + settingName + ' => ' + (this.checked ? '开启' : '关闭'));
            });
        });
    </script>
</body>
</html> 