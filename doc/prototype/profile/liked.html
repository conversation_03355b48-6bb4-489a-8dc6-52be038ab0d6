<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <title>赞过 - StylishLink</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../styles/main.css" rel="stylesheet">
    <style>
        ::-webkit-scrollbar {
            display: none;
        }
        
        * {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        
        .content-container {
            padding-left: 5%;
            padding-right: 5%;
        }

        .gradient-background {
            background: linear-gradient(135deg, #c2e9fb 0%, #d8c6fd 100%);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: -1;
        }
        
        .enhanced-glass {
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.05);
        }
        
        .glass-button {
            background: rgba(255, 255, 255, 0.4);
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.5);
            transition: all 0.3s ease;
        }
        
        .glass-button:hover {
            background: rgba(255, 255, 255, 0.6);
        }
        
        /* 标签样式 */
        .tab-button {
            padding: 8px 16px;
            font-size: 12px;
            border-radius: 16px;
            transition: all 0.3s ease;
            white-space: nowrap;
        }
        
        .tab-button.active {
            background: rgba(255, 255, 255, 0.5);
            color: var(--color-text-primary);
            font-weight: 500;
        }
        
        .tab-button.inactive {
            background: rgba(255, 255, 255, 0.2);
            color: var(--color-text-secondary);
        }
        
        /* 滚动区域 */
        .scrolling-area {
            overflow-x: auto;
            white-space: nowrap;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }
        
        .scrolling-area::-webkit-scrollbar {
            display: none;
        }
        
        /* 赞过卡片 */
        .liked-item {
            position: relative;
            border-radius: 12px;
            overflow: hidden;
            transition: transform 0.3s ease;
        }
        
        .liked-item:active {
            transform: scale(0.98);
        }
        
        .liked-detail {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.5);
            backdrop-filter: blur(5px);
            padding: 8px;
        }
        
        /* 管理模式样式 */
        .liked-item .select-circle {
            position: absolute;
            top: 8px;
            right: 8px;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            border: 2px solid white;
            background: rgba(255, 255, 255, 0.3);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 10;
        }
        
        .liked-item.selected .select-circle {
            background: rgba(168, 230, 207, 1);
            border-color: white;
        }
        
        .manage-mode .select-circle {
            display: flex;
        }
        
        .manage-mode .liked-item.selected .select-circle:after {
            content: '✓';
            color: white;
            font-size: 12px;
        }
        
        /* 空状态样式 */
        .empty-state {
            text-align: center;
            padding: 40px 0;
        }
        
        .empty-icon {
            width: 60px;
            height: 60px;
            margin: 0 auto 16px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* 点赞按钮样式 */
        .like-button {
            transition: transform 0.2s ease;
        }

        .like-button:active {
            transform: scale(1.2);
        }

        .like-button.active {
            color: #ff4f7d;
        }
    </style>
</head>
<body>
    <!-- 渐变背景 -->
    <div class="gradient-background"></div>

    <!-- 主内容区 -->
    <div class="pt-2 pb-20 h-full overflow-y-scroll">
        <div class="content-container py-2">
            <!-- 顶部导航栏 -->
            <div class="flex items-center justify-between mb-4">
                <div class="flex items-center">
                    <a href="../pages/profile.html" class="mr-3">
                        <i class="fas fa-arrow-left text-[var(--color-text-primary)]"></i>
                    </a>
                    <h1 class="text-sm font-semibold text-[var(--color-text-primary)]">赞过</h1>
                </div>
                <button id="manageButton" class="text-xs text-[var(--color-text-primary)]">
                    管理
                </button>
            </div>

            <!-- 分类标签栏 -->
            <div class="scrolling-area mb-4">
                <div class="inline-flex space-x-2">
                    <button class="tab-button active" data-tab="all">
                        全部
                    </button>
                    <button class="tab-button inactive" data-tab="outfit">
                        <i class="fas fa-tshirt mr-1.5"></i>穿搭
                    </button>
                    <button class="tab-button inactive" data-tab="items">
                        <i class="fas fa-shopping-bag mr-1.5"></i>单品
                    </button>
                    <button class="tab-button inactive" data-tab="posts">
                        <i class="fas fa-comment-alt mr-1.5"></i>动态
                    </button>
                    <button class="tab-button inactive" data-tab="videos">
                        <i class="fas fa-play-circle mr-1.5"></i>视频
                    </button>
                </div>
            </div>

            <!-- 赞过内容展示 -->
            <div id="allContent" class="tab-content">
                <!-- 赞过统计 -->
                <div class="flex justify-between items-center mb-3">
                    <h3 class="text-xs font-medium text-[var(--color-text-primary)]">赞过内容 (8)</h3>
                    <div class="flex items-center">
                        <button class="text-[10px] text-[var(--color-text-secondary)] mr-2">
                            <i class="fas fa-sort-amount-down mr-1"></i>排序
                        </button>
                        <button class="text-[10px] text-[var(--color-text-secondary)]">
                            <i class="fas fa-filter mr-1"></i>筛选
                        </button>
                    </div>
                </div>
                
                <!-- 赞过网格 -->
                <div class="grid grid-cols-2 gap-3 mb-4">
                    <!-- 赞过项1 -->
                    <div class="liked-item enhanced-glass">
                        <div class="select-circle"></div>
                        <img src="https://images.unsplash.com/photo-1515886657613-9f3515b0c78f" 
                             class="w-full aspect-[2/3] object-cover" alt="休闲穿搭">
                        <div class="liked-detail">
                            <h4 class="text-xs font-medium text-[var(--color-text-primary)] truncate">休闲时尚系列</h4>
                            <div class="flex justify-between items-center mt-1">
                                <span class="text-[10px] text-[var(--color-text-secondary)]">穿搭</span>
                                <div class="flex items-center space-x-1">
                                    <button class="w-6 h-6 rounded-full bg-white/30 flex items-center justify-center like-button active">
                                        <i class="fas fa-heart text-[10px]"></i>
                                    </button>
                                    <button class="w-6 h-6 rounded-full bg-white/30 flex items-center justify-center">
                                        <i class="fas fa-eye text-[var(--color-text-secondary)] text-[10px]"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 赞过项2 -->
                    <div class="liked-item enhanced-glass">
                        <div class="select-circle"></div>
                        <img src="https://images.unsplash.com/photo-1539109136881-3be0616acf4b" 
                             class="w-full aspect-[2/3] object-cover" alt="运动穿搭">
                        <div class="liked-detail">
                            <h4 class="text-xs font-medium text-[var(--color-text-primary)] truncate">运动活力系列</h4>
                            <div class="flex justify-between items-center mt-1">
                                <span class="text-[10px] text-[var(--color-text-secondary)]">穿搭</span>
                                <div class="flex items-center space-x-1">
                                    <button class="w-6 h-6 rounded-full bg-white/30 flex items-center justify-center like-button active">
                                        <i class="fas fa-heart text-[10px]"></i>
                                    </button>
                                    <button class="w-6 h-6 rounded-full bg-white/30 flex items-center justify-center">
                                        <i class="fas fa-eye text-[var(--color-text-secondary)] text-[10px]"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 赞过项3 -->
                    <div class="liked-item enhanced-glass">
                        <div class="select-circle"></div>
                        <img src="https://images.unsplash.com/photo-1580618672591-eb180b1a973f" 
                             class="w-full aspect-[2/3] object-cover" alt="牛仔外套">
                        <div class="liked-detail">
                            <h4 class="text-xs font-medium text-[var(--color-text-primary)] truncate">复古牛仔外套</h4>
                            <div class="flex justify-between items-center mt-1">
                                <span class="text-[10px] text-[var(--color-text-secondary)]">单品</span>
                                <div class="flex items-center space-x-1">
                                    <button class="w-6 h-6 rounded-full bg-white/30 flex items-center justify-center like-button active">
                                        <i class="fas fa-heart text-[10px]"></i>
                                    </button>
                                    <button class="w-6 h-6 rounded-full bg-white/30 flex items-center justify-center">
                                        <i class="fas fa-eye text-[var(--color-text-secondary)] text-[10px]"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 赞过项4 -->
                    <div class="liked-item enhanced-glass">
                        <div class="select-circle"></div>
                        <div class="w-full aspect-[2/3] relative bg-black/20">
                            <img src="https://images.unsplash.com/photo-1515886657613-9f3515b0c78f" 
                                 class="w-full h-full object-cover opacity-70" alt="视频缩略图">
                            <div class="absolute inset-0 flex items-center justify-center">
                                <div class="w-12 h-12 rounded-full bg-white/30 backdrop-blur-sm flex items-center justify-center">
                                    <i class="fas fa-play text-white text-lg"></i>
                                </div>
                            </div>
                        </div>
                        <div class="liked-detail">
                            <h4 class="text-xs font-medium text-[var(--color-text-primary)] truncate">春季搭配技巧</h4>
                            <div class="flex justify-between items-center mt-1">
                                <span class="text-[10px] text-[var(--color-text-secondary)]">视频</span>
                                <div class="flex items-center space-x-1">
                                    <button class="w-6 h-6 rounded-full bg-white/30 flex items-center justify-center like-button active">
                                        <i class="fas fa-heart text-[10px]"></i>
                                    </button>
                                    <button class="w-6 h-6 rounded-full bg-white/30 flex items-center justify-center">
                                        <i class="fas fa-eye text-[var(--color-text-secondary)] text-[10px]"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 其他标签页的内容，默认隐藏 -->
            <div id="outfitContent" class="tab-content hidden">
                <!-- 穿搭赞过内容 -->
            </div>
            
            <div id="itemsContent" class="tab-content hidden">
                <!-- 单品赞过内容 -->
            </div>
            
            <div id="postsContent" class="tab-content hidden">
                <!-- 动态赞过内容 -->
            </div>
            
            <div id="videosContent" class="tab-content hidden">
                <!-- 视频赞过内容 -->
            </div>
            
            <!-- 管理栏，默认隐藏 -->
            <div id="managementBar" class="fixed bottom-20 left-0 right-0 bg-white/60 backdrop-blur-md border-t border-white/60 py-3 px-5 hidden">
                <div class="flex justify-between items-center">
                    <div class="flex items-center">
                        <input type="checkbox" id="selectAll" class="mr-2 rounded h-4 w-4">
                        <label for="selectAll" class="text-xs text-[var(--color-text-primary)]">全选</label>
                        <span id="selectedCount" class="text-xs text-[var(--color-text-secondary)] ml-3">已选择 0 项</span>
                    </div>
                    <div class="flex space-x-3">
                        <button class="glass-button text-xs text-[var(--color-text-primary)] px-3 py-1 rounded-full">
                            <i class="fas fa-share-alt mr-1"></i>分享
                        </button>
                        <button class="glass-button text-xs text-[#ff5458] px-3 py-1 rounded-full">
                            <i class="fas fa-trash-alt mr-1"></i>取消点赞
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 标签切换
        const tabButtons = document.querySelectorAll('.tab-button');
        const tabContents = document.querySelectorAll('.tab-content');
        
        tabButtons.forEach(button => {
            button.addEventListener('click', function() {
                // 切换按钮样式
                tabButtons.forEach(btn => {
                    btn.classList.remove('active');
                    btn.classList.add('inactive');
                });
                this.classList.remove('inactive');
                this.classList.add('active');
                
                // 显示对应内容
                const tabName = this.getAttribute('data-tab');
                tabContents.forEach(content => {
                    content.classList.add('hidden');
                });
                
                // 显示对应标签内容，如果是"全部"则显示allContent
                if (tabName === 'all') {
                    document.getElementById('allContent').classList.remove('hidden');
                } else {
                    document.getElementById(tabName + 'Content').classList.remove('hidden');
                }
            });
        });
        
        // 管理模式切换
        const manageButton = document.getElementById('manageButton');
        const managementBar = document.getElementById('managementBar');
        const likedItems = document.querySelectorAll('.liked-item');
        const selectAllCheckbox = document.getElementById('selectAll');
        const selectedCountElement = document.getElementById('selectedCount');
        
        let isManageMode = false;
        
        manageButton.addEventListener('click', function() {
            isManageMode = !isManageMode;
            
            if (isManageMode) {
                // 进入管理模式
                document.body.classList.add('manage-mode');
                managementBar.classList.remove('hidden');
                this.textContent = '完成';
                // 重置选择状态
                selectAllCheckbox.checked = false;
                updateSelectedCount();
            } else {
                // 退出管理模式
                document.body.classList.remove('manage-mode');
                managementBar.classList.add('hidden');
                this.textContent = '管理';
                // 清除所有选择状态
                likedItems.forEach(item => {
                    item.classList.remove('selected');
                });
            }
        });
        
        // 选择赞过项
        likedItems.forEach(item => {
            item.addEventListener('click', function(e) {
                if (isManageMode) {
                    // 在管理模式下，点击切换选中状态
                    this.classList.toggle('selected');
                    updateSelectedCount();
                    
                    // 更新全选按钮状态
                    const allSelected = Array.from(likedItems).every(item => item.classList.contains('selected'));
                    const noneSelected = Array.from(likedItems).every(item => !item.classList.contains('selected'));
                    
                    selectAllCheckbox.checked = allSelected;
                    selectAllCheckbox.indeterminate = !allSelected && !noneSelected;
                    
                    // 防止触发正常的点击事件（如查看详情）
                    e.preventDefault();
                    e.stopPropagation();
                }
            });
        });
        
        // 全选/取消全选
        selectAllCheckbox.addEventListener('change', function() {
            likedItems.forEach(item => {
                if (this.checked) {
                    item.classList.add('selected');
                } else {
                    item.classList.remove('selected');
                }
            });
            
            updateSelectedCount();
        });
        
        // 更新已选数量
        function updateSelectedCount() {
            const selectedItems = document.querySelectorAll('.liked-item.selected');
            selectedCountElement.textContent = `已选择 ${selectedItems.length} 项`;
        }

        // 点赞按钮交互
        const likeButtons = document.querySelectorAll('.like-button');
        likeButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                if (!isManageMode) {
                    this.classList.toggle('active');
                    e.stopPropagation();
                }
            });
        });
    </script>
</body>
</html>