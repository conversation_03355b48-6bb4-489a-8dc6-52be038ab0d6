<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <title>灵感值中心 - StylishLink</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../styles/main.css" rel="stylesheet">
    <style>
        ::-webkit-scrollbar {
            display: none;
        }
        
        * {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        
        .content-container {
            padding-left: 5%;
            padding-right: 5%;
        }

        .gradient-background {
            background: linear-gradient(135deg, #c2e9fb 0%, #d8c6fd 100%);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: -1;
        }
        
        .enhanced-glass {
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.05);
        }
        
        .glass-button {
            background: rgba(255, 255, 255, 0.4);
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.5);
            transition: all 0.3s ease;
        }
        
        .glass-button:hover {
            background: rgba(255, 255, 255, 0.6);
        }
        
        /* 灵感值余额卡片 */
        .inspiration-card {
            background: linear-gradient(135deg, rgba(255, 194, 92, 0.7), rgba(255, 154, 158, 0.7));
            border-radius: 16px;
            padding: 16px;
            position: relative;
            overflow: hidden;
        }
        
        .inspiration-card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 10%, transparent 10.5%);
            background-size: 20px 20px;
            opacity: 0.3;
            z-index: 0;
        }
        
        /* 标签页样式 */
        .tab-button {
            padding: 8px 16px;
            font-size: 12px;
            border-radius: 16px;
            transition: all 0.3s ease;
        }
        
        .tab-button.active {
            background: rgba(255, 255, 255, 0.5);
            color: var(--color-text-primary);
            font-weight: 500;
        }
        
        .tab-button.inactive {
            background: rgba(255, 255, 255, 0.2);
            color: var(--color-text-secondary);
        }
        
        /* 进度条 */
        .progress-bar {
            height: 6px;
            border-radius: 3px;
            background: rgba(255, 255, 255, 0.3);
            overflow: hidden;
            position: relative;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #a8e6cf, #73c1a8);
            border-radius: 3px;
        }
        
        /* 流水记录项 */
        .record-item {
            display: flex;
            justify-content: space-between;
            padding: 12px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .record-item:last-child {
            border-bottom: none;
        }
        
        .record-left {
            display: flex;
        }
        
        .record-icon {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
        }
        
        /* 充值选项卡 */
        .recharge-option {
            border-radius: 12px;
            padding: 12px;
            position: relative;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .recharge-option.selected {
            border: 2px solid rgba(168, 230, 207, 1);
        }
        
        .recharge-option.selected::after {
            content: '';
            position: absolute;
            top: -5px;
            right: -5px;
            width: 20px;
            height: a20px;
            border-radius: 50%;
            background: rgba(168, 230, 207, 1);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 10px;
        }
        
        .sparkle-icon {
            color: #ffc25c;
            text-shadow: 0 0 5px rgba(255, 194, 92, 0.5);
        }
    </style>
</head>
<body>
    <!-- 渐变背景 -->
    <div class="gradient-background"></div>

    <!-- 主内容区 -->
    <div class="pt-2 pb-20 h-full overflow-y-scroll">
        <div class="content-container py-2">
            <!-- 顶部导航栏 -->
            <div class="flex items-center justify-between mb-4">
                <div class="flex items-center">
                    <a href="../pages/profile.html" class="mr-3">
                        <i class="fas fa-arrow-left text-[var(--color-text-primary)]"></i>
                    </a>
                    <h1 class="text-sm font-semibold text-[var(--color-text-primary)]">灵感值中心</h1>
                </div>
                <a href="#" class="text-xs text-[var(--color-text-primary)]">
                    帮助
                </a>
            </div>

            <!-- 灵感值余额卡片 -->
            <div class="inspiration-card mb-4">
                <div class="relative z-10">
                    <div class="flex justify-between items-start mb-2">
                        <div>
                            <p class="text-xs text-white/70">灵感值余额</p>
                            <div class="flex items-center">
                                <span class="text-2xl font-bold text-white">2,850</span>
                                <i class="fas fa-sparkles sparkle-icon ml-1"></i>
                            </div>
                        </div>
                        <div class="px-2 py-1 bg-white/20 backdrop-blur-sm rounded-lg">
                            <div class="flex items-center">
                                <span class="text-xs font-medium text-white">Lv.3</span>
                                <span class="text-[10px] text-white/70 ml-1">风格专家</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <div class="flex justify-between text-[10px] text-white/70 mb-1">
                            <span>下一等级：Lv.4 灵感大师</span>
                            <span>2,850/5,000</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 57%"></div>
                        </div>
                    </div>
                    
                    <div class="flex space-x-2">
                        <button class="glass-button flex-1 py-2 rounded-full text-xs font-medium text-white/90 flex items-center justify-center">
                            <i class="fas fa-plus-circle mr-1"></i>充值
                        </button>
                        <button class="glass-button flex-1 py-2 rounded-full text-xs font-medium text-white/90 flex items-center justify-center">
                            <i class="fas fa-gift mr-1"></i>赠送
                        </button>
                    </div>
                </div>
            </div>

            <!-- 标签页切换 -->
            <div class="flex space-x-2 mb-4">
                <button class="tab-button active" data-tab="records">消费记录</button>
                <button class="tab-button inactive" data-tab="recharge">充值中心</button>
                <button class="tab-button inactive" data-tab="privileges">会员特权</button>
            </div>

            <!-- 消费记录内容 -->
            <div class="tab-content" id="records-content">
                <!-- 灵感值分类统计 -->
                <div class="enhanced-glass rounded-xl p-3 mb-4">
                    <h3 class="text-xs font-medium text-[var(--color-text-primary)] mb-2">灵感值分类</h3>
                    <div class="flex justify-between mb-1">
                        <div class="flex items-center">
                            <div class="w-3 h-3 rounded-full bg-[#ffc25c] mr-2"></div>
                            <span class="text-[10px] text-[var(--color-text-secondary)]">充值获得</span>
                        </div>
                        <span class="text-[10px] font-medium text-[var(--color-text-primary)]">1,500</span>
                    </div>
                    <div class="flex justify-between mb-1">
                        <div class="flex items-center">
                            <div class="w-3 h-3 rounded-full bg-[#a8e6cf] mr-2"></div>
                            <span class="text-[10px] text-[var(--color-text-secondary)]">活动奖励</span>
                        </div>
                        <span class="text-[10px] font-medium text-[var(--color-text-primary)]">980</span>
                    </div>
                    <div class="flex justify-between mb-1">
                        <div class="flex items-center">
                            <div class="w-3 h-3 rounded-full bg-[#ff9a9e] mr-2"></div>
                            <span class="text-[10px] text-[var(--color-text-secondary)]">好友赠送</span>
                        </div>
                        <span class="text-[10px] font-medium text-[var(--color-text-primary)]">370</span>
                    </div>
                </div>
                
                <!-- 近期记录 -->
                <div class="enhanced-glass rounded-xl p-3 mb-4">
                    <div class="flex justify-between items-center mb-3">
                        <h3 class="text-xs font-medium text-[var(--color-text-primary)]">消费记录</h3>
                        <button class="text-[10px] text-[var(--color-text-secondary)]">筛选</button>
                    </div>
                    
                    <!-- 记录项1 -->
                    <div class="record-item">
                        <div class="record-left">
                            <div class="record-icon bg-[rgba(255,154,158,0.3)]">
                                <i class="fas fa-wand-magic-sparkles text-[var(--color-text-primary)]"></i>
                            </div>
                            <div>
                                <div class="text-xs font-medium text-[var(--color-text-primary)]">场景搭配建议</div>
                                <div class="text-[10px] text-[var(--color-text-secondary)]">今天 10:25</div>
                            </div>
                        </div>
                        <div class="text-xs font-medium text-[#ff5458]">-30</div>
                    </div>
                    
                    <!-- 记录项2 -->
                    <div class="record-item">
                        <div class="record-left">
                            <div class="record-icon bg-[rgba(168,230,207,0.3)]">
                                <i class="fas fa-sign-in-alt text-[var(--color-text-primary)]"></i>
                            </div>
                            <div>
                                <div class="text-xs font-medium text-[var(--color-text-primary)]">每日登录奖励</div>
                                <div class="text-[10px] text-[var(--color-text-secondary)]">今天 09:15</div>
                            </div>
                        </div>
                        <div class="text-xs font-medium text-[#73c1a8]">+20</div>
                    </div>
                    
                    <!-- 记录项3 -->
                    <div class="record-item">
                        <div class="record-left">
                            <div class="record-icon bg-[rgba(255,154,158,0.3)]">
                                <i class="fas fa-eye text-[var(--color-text-primary)]"></i>
                            </div>
                            <div>
                                <div class="text-xs font-medium text-[var(--color-text-primary)]">搭配视频查看</div>
                                <div class="text-[10px] text-[var(--color-text-secondary)]">昨天 16:42</div>
                            </div>
                        </div>
                        <div class="text-xs font-medium text-[#ff5458]">-40</div>
                    </div>
                    
                    <!-- 记录项4 -->
                    <div class="record-item">
                        <div class="record-left">
                            <div class="record-icon bg-[rgba(255,234,167,0.3)]">
                                <i class="fas fa-coins text-[var(--color-text-primary)]"></i>
                            </div>
                            <div>
                                <div class="text-xs font-medium text-[var(--color-text-primary)]">充值</div>
                                <div class="text-[10px] text-[var(--color-text-secondary)]">昨天 14:30</div>
                            </div>
                        </div>
                        <div class="text-xs font-medium text-[#73c1a8]">+300</div>
                    </div>
                    
                    <!-- 记录项5 -->
                    <div class="record-item">
                        <div class="record-left">
                            <div class="record-icon bg-[rgba(168,230,207,0.3)]">
                                <i class="fas fa-heart text-[var(--color-text-primary)]"></i>
                            </div>
                            <div>
                                <div class="text-xs font-medium text-[var(--color-text-primary)]">点赞搭配</div>
                                <div class="text-[10px] text-[var(--color-text-secondary)]">昨天 10:15</div>
                            </div>
                        </div>
                        <div class="text-xs font-medium text-[#73c1a8]">+2</div>
                    </div>
                </div>
            </div>

            <!-- 充值中心内容 -->
            <div class="tab-content hidden" id="recharge-content">
                <!-- 充值选项 -->
                <div class="enhanced-glass rounded-xl p-3 mb-4">
                    <h3 class="text-xs font-medium text-[var(--color-text-primary)] mb-3">选择充值金额</h3>
                    
                    <div class="grid grid-cols-2 gap-3 mb-3">
                        <!-- 小灵感 -->
                        <div class="recharge-option enhanced-glass">
                            <div class="text-xs font-medium text-[var(--color-text-primary)] mb-1">小灵感</div>
                            <div class="flex items-center text-[var(--color-text-secondary)] mb-2">
                                <span class="text-base font-medium">¥6</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-sparkles text-[#ffc25c] text-xs mr-1"></i>
                                <span class="text-[10px] text-[var(--color-text-secondary)]">60灵感值</span>
                            </div>
                        </div>
                        
                        <!-- 中灵感 -->
                        <div class="recharge-option enhanced-glass selected">
                            <div class="text-xs font-medium text-[var(--color-text-primary)] mb-1">中灵感</div>
                            <div class="flex items-center text-[var(--color-text-secondary)] mb-2">
                                <span class="text-base font-medium">¥30</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-sparkles text-[#ffc25c] text-xs mr-1"></i>
                                <span class="text-[10px] text-[var(--color-text-secondary)]">300灵感值+30赠送</span>
                            </div>
                            <div class="absolute top-1 right-1 px-2 py-0.5 bg-[#ff9a9e] rounded-full text-[8px] text-white">
                                性价比高
                            </div>
                        </div>
                        
                        <!-- 大灵感 -->
                        <div class="recharge-option enhanced-glass">
                            <div class="text-xs font-medium text-[var(--color-text-primary)] mb-1">大灵感</div>
                            <div class="flex items-center text-[var(--color-text-secondary)] mb-2">
                                <span class="text-base font-medium">¥68</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-sparkles text-[#ffc25c] text-xs mr-1"></i>
                                <span class="text-[10px] text-[var(--color-text-secondary)]">680灵感值+88赠送</span>
                            </div>
                        </div>
                        
                        <!-- 超级灵感 -->
                        <div class="recharge-option enhanced-glass">
                            <div class="text-xs font-medium text-[var(--color-text-primary)] mb-1">超级灵感</div>
                            <div class="flex items-center text-[var(--color-text-secondary)] mb-2">
                                <span class="text-base font-medium">¥128</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-sparkles text-[#ffc25c] text-xs mr-1"></i>
                                <span class="text-[10px] text-[var(--color-text-secondary)]">1280灵感值+220赠送</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 月卡选项 -->
                    <div class="enhanced-glass rounded-xl p-3 mb-3 flex justify-between items-center">
                        <div>
                            <div class="text-xs font-medium text-[var(--color-text-primary)]">灵感月卡</div>
                            <div class="text-[10px] text-[var(--color-text-secondary)]">30天内每日领取40灵感值，共1200灵感值</div>
                        </div>
                        <div class="flex items-center">
                            <span class="text-xs font-medium text-[var(--color-text-primary)]">¥30</span>
                            <div class="ml-2 px-2 py-0.5 bg-[#ffc25c] rounded-full text-[8px] text-white">
                                超值
                            </div>
                        </div>
                    </div>
                    
                    <!-- 支付按钮 -->
                    <button class="glass-button w-full py-3 rounded-full text-sm font-medium text-[var(--color-text-primary)] flex items-center justify-center">
                        <i class="fab fa-weixin mr-2 text-[#07c160]"></i>微信支付 ¥30
                    </button>
                </div>
            </div>

            <!-- 会员特权内容 -->
            <div class="tab-content hidden" id="privileges-content">
                <!-- 当前等级 -->
                <div class="enhanced-glass rounded-xl p-3 mb-4">
                    <h3 class="text-xs font-medium text-[var(--color-text-primary)] mb-2">当前等级</h3>
                    <div class="flex items-center mb-3">
                        <div class="w-12 h-12 rounded-full bg-[rgba(184,198,219,0.5)] flex items-center justify-center mr-3">
                            <i class="fas fa-crown text-[#648dae] text-xl"></i>
                        </div>
                        <div>
                            <div class="text-sm font-medium text-[var(--color-text-primary)]">Lv.3 风格专家</div>
                            <div class="text-[10px] text-[var(--color-text-secondary)]">还需2,150灵感值升级到Lv.4</div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="flex justify-between text-[10px] text-[var(--color-text-secondary)] mb-1">
                            <span>Lv.3</span>
                            <span>Lv.4</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 57%"></div>
                        </div>
                    </div>
                </div>
                
                <!-- 特权列表 -->
                <div class="enhanced-glass rounded-xl p-3 mb-4">
                    <div class="flex justify-between items-center mb-3">
                        <h3 class="text-xs font-medium text-[var(--color-text-primary)]">当前特权</h3>
                        <span class="text-[10px] px-2 py-0.5 bg-[rgba(168,230,207,0.3)] rounded-full text-[var(--color-text-secondary)]">
                            Lv.3专属
                        </span>
                    </div>
                    
                    <!-- 特权1 -->
                    <div class="record-item">
                        <div class="record-left">
                            <div class="record-icon bg-[rgba(255,234,167,0.3)]">
                                <i class="fas fa-percentage text-[var(--color-text-primary)]"></i>
                            </div>
                            <div>
                                <div class="text-xs font-medium text-[var(--color-text-primary)]">消耗折扣20%</div>
                                <div class="text-[10px] text-[var(--color-text-secondary)]">所有灵感值消费享受8折优惠</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 特权2 -->
                    <div class="record-item">
                        <div class="record-left">
                            <div class="record-icon bg-[rgba(255,234,167,0.3)]">
                                <i class="fas fa-comment-alt text-[var(--color-text-primary)]"></i>
                            </div>
                            <div>
                                <div class="text-xs font-medium text-[var(--color-text-primary)]">专属气泡框</div>
                                <div class="text-[10px] text-[var(--color-text-secondary)]">个人主页专属气泡边框</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 特权3 -->
                    <div class="record-item">
                        <div class="record-left">
                            <div class="record-icon bg-[rgba(255,234,167,0.3)]">
                                <i class="fas fa-chart-line text-[var(--color-text-primary)]"></i>
                            </div>
                            <div>
                                <div class="text-xs font-medium text-[var(--color-text-primary)]">深度分析</div>
                                <div class="text-[10px] text-[var(--color-text-secondary)]">服饰与五行能量的深度匹配分析</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 特权4 -->
                    <div class="record-item">
                        <div class="record-left">
                            <div class="record-icon bg-[rgba(255,234,167,0.3)]">
                                <i class="fas fa-shopping-bag text-[var(--color-text-primary)]"></i>
                            </div>
                            <div>
                                <div class="text-xs font-medium text-[var(--color-text-primary)]">商城折扣10%</div>
                                <div class="text-[10px] text-[var(--color-text-secondary)]">商城内所有商品享受9折优惠</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 下一级特权 -->
                <div class="enhanced-glass rounded-xl p-3 mb-4">
                    <div class="flex justify-between items-center mb-3">
                        <h3 class="text-xs font-medium text-[var(--color-text-primary)]">下一级特权</h3>
                        <span class="text-[10px] px-2 py-0.5 bg-[rgba(168,230,207,0.3)] rounded-full text-[var(--color-text-secondary)]">
                            Lv.4专属
                        </span>
                    </div>
                    
                    <!-- 特权1 -->
                    <div class="record-item">
                        <div class="record-left">
                            <div class="record-icon bg-[rgba(184,198,219,0.3)]">
                                <i class="fas fa-percentage text-[var(--color-text-primary)]"></i>
                            </div>
                            <div>
                                <div class="text-xs font-medium text-[var(--color-text-primary)]">消耗折扣30%</div>
                                <div class="text-[10px] text-[var(--color-text-secondary)]">所有灵感值消费享受7折优惠</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 特权2 -->
                    <div class="record-item">
                        <div class="record-left">
                            <div class="record-icon bg-[rgba(184,198,219,0.3)]">
                                <i class="fas fa-sparkles text-[var(--color-text-primary)]"></i>
                            </div>
                            <div>
                                <div class="text-xs font-medium text-[var(--color-text-primary)]">专属动态特效</div>
                                <div class="text-[10px] text-[var(--color-text-secondary)]">个人主页专属动态特效</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 标签页切换
        const tabButtons = document.querySelectorAll('.tab-button');
        const tabContents = document.querySelectorAll('.tab-content');
        
        tabButtons.forEach(button => {
            button.addEventListener('click', function() {
                // 切换按钮样式
                tabButtons.forEach(btn => {
                    btn.classList.remove('active');
                    btn.classList.add('inactive');
                });
                this.classList.remove('inactive');
                this.classList.add('active');
                
                // 显示对应内容
                const tabName = this.getAttribute('data-tab');
                tabContents.forEach(content => {
                    content.classList.add('hidden');
                });
                document.getElementById(tabName + '-content').classList.remove('hidden');
            });
        });
        
        // 充值选项选择
        const rechargeOptions = document.querySelectorAll('.recharge-option');
        rechargeOptions.forEach(option => {
            option.addEventListener('click', function() {
                rechargeOptions.forEach(opt => {
                    opt.classList.remove('selected');
                });
                this.classList.add('selected');
                
                // 更新支付按钮金额
                const price = this.querySelector('.text-base.font-medium').textContent;
                document.querySelector('.glass-button').innerHTML = `<i class="fab fa-weixin mr-2 text-[#07c160]"></i>微信支付 ${price}`;
            });
        });
    </script>
</body>
</html> 