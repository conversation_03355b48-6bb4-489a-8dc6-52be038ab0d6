<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <title>运势完整解读 - 穿搭推荐小程序</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../styles/wuxing.css" rel="stylesheet">
    <style>
        :root {
            --color-text-primary: #333333;
            --color-text-secondary: #666666;
            --color-text-tertiary: #999999;
        }
        
        /* 隐藏滚动条 */
        ::-webkit-scrollbar {
            display: none;
        }
        
        * {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        
        body {
            overflow-y: scroll;
            -webkit-overflow-scrolling: touch;
        }

        /* 内容区域统一间距 */
        .content-container {
            padding-left: 5%;
            padding-right: 5%;
        }
        
        .gradient-background {
            background: linear-gradient(135deg, #c2e9fb 0%, #d8c6fd 100%);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: -1;
        }
        
        .enhanced-glass {
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.05);
        }
        
        .glass-button {
            background: rgba(255, 255, 255, 0.4);
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.5);
            transition: all 0.3s ease;
        }
        
        .glass-button:hover {
            background: rgba(255, 255, 255, 0.6);
        }

        /* 新增样式 */
        .tab-active {
            background: rgba(255, 255, 255, 0.4);
            border-bottom: 2px solid #6366f1;
        }

        .feedback-button {
            transition: all 0.2s ease;
        }

        .feedback-button.active {
            transform: scale(1.1);
        }

        .feedback-button:hover {
            transform: scale(1.05);
        }

        .divider {
            height: 1px;
            background: rgba(255, 255, 255, 0.2);
            margin: 1rem 0;
        }

        .content-section {
            margin-bottom: 1rem;
            padding: 0;
        }

        .content-section:not(:last-child) {
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        /* 自定义滚动条样式 */
        .scrollbar-hide::-webkit-scrollbar {
            height: 4px;
        }
        
        .scrollbar-hide::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 2px;
        }
        
        .scrollbar-hide::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 2px;
        }
        
        .scrollbar-hide::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        /* 五行图节点通用样式 */
        .wuxing-node {
            position: relative;
            width: 3rem;    /* 设置相同的宽高以成为正圆形 */
            height: 3rem;   /* 设置相同的宽高以成为正圆形 */
            border: 1px solid; /* 边框颜色由具体类指定 */
            border-radius: 50%; /* 使用50%边框半径创建圆形 */
            padding: 0;
            text-align: center;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            gap: 1px; /* 控制两行文字之间的间距 */
        }

        /* 节点内部文字布局 */
        .wuxing-element-name {
            font-size: 1rem;
            font-weight: 500;
            line-height: 1;
            margin: 0;
        }
        .wuxing-percentage {
            font-size: 0.75rem;
            color: #4B5563;
            line-height: 1;
            margin: 0;
        }

        /* 五行背景色和边框色 - 扁平风格 */
        .wuxing-bg-mu { 
            background-color: #e0f2e9;
            border-color: #6ee7b7;
        } 
        .wuxing-bg-huo { 
            background-color: #fee2e2;
            border-color: #fca5a5;
        } 
        .wuxing-bg-tu { 
            background-color: #fef3c7;
            border-color: #fcd34d;
        } 
        .wuxing-bg-jin { 
            background-color: #f3f4f6;
            border-color: #d1d5db;
        } 
        .wuxing-bg-shui { 
            background-color: #dbeafe;
            border-color: #93c5fd;
        }

        /* 十神外部标注样式 */
        .wuxing-shishen-label {
             @apply text-[10px] text-gray-500 whitespace-nowrap;
             /* 定位交给父级 flex 容器 */
        }

        /* 移除日主样式 */

    </style>
</head>
<body>
    <!-- 渐变背景 -->
    <div class="gradient-background"></div>

    <!-- 顶部导航 -->
    <div class="sticky top-0 bg-white/30 backdrop-blur-md z-40 shadow-sm">
        <div class="flex items-center p-4">
            <a href="fortune_detail.html" class="mr-4">
                <i class="fas fa-arrow-left text-[var(--color-text-primary)]"></i>
            </a>
            <h1 class="text-sm font-semibold text-[var(--color-text-primary)]">运势完整解读</h1>
        </div>
    </div>

    <!-- 主内容区 -->
    <div class="content-container py-4 pb-20">
        <!-- 八字组合 -->
        <div class="enhanced-glass rounded-xl p-4 mb-4">
            <h3 class="text-sm font-medium text-[var(--color-text-primary)] mb-3">八字组合</h3>
            <!-- 八字表格 -->
            <div class="w-full border-collapse">
                <!-- 表头行 -->
                <div class="grid grid-cols-5 gap-2 mb-2">
                    <div class="text-center">
                        <span class="text-[10px] text-[var(--color-text-secondary)]">日期</span>
                    </div>
                    <div class="text-center">
                    <span class="text-[10px] text-[var(--color-text-secondary)]">年柱</span>
                </div>
                    <div class="text-center">
                        <span class="text-[10px] text-[var(--color-text-secondary)]">月柱</span>
                    </div>
                    <div class="text-center">
                        <span class="text-[10px] text-[var(--color-text-secondary)]">日柱</span>
                    </div>
                    <div class="text-center">
                        <span class="text-[10px] text-[var(--color-text-secondary)]">时柱</span>
                    </div>
                </div>

                <!-- 天干行 -->
                <div class="grid grid-cols-5 gap-2 mb-2">
                    <div class="text-center">
                        <span class="text-[10px] text-[var(--color-text-secondary)]">天干</span>
                    </div>
                    <div class="text-center">
                        <span class="text-[10px] text-[var(--color-text-primary)] wuxing-mu">
                            <i class="fas fa-leaf wuxing-icon"></i>乙
                        </span>
                    </div>
                    <div class="text-center">
                        <span class="text-[10px] text-[var(--color-text-primary)] wuxing-tu">
                            <i class="fas fa-mountain wuxing-icon"></i>己
                        </span>
                    </div>
                    <div class="text-center">
                        <span class="text-[10px] text-[var(--color-text-primary)] wuxing-jin">
                            <i class="fas fa-coins wuxing-icon"></i>庚
                        </span>
                    </div>
                    <div class="text-center">
                        <span class="text-[10px] text-[var(--color-text-primary)] wuxing-mu">
                            <i class="fas fa-leaf wuxing-icon"></i>乙
                        </span>
                    </div>
                </div>

                <!-- 地支行 -->
                <div class="grid grid-cols-5 gap-2 mb-2">
                    <div class="text-center">
                        <span class="text-[10px] text-[var(--color-text-secondary)]">地支</span>
                    </div>
                    <div class="text-center">
                        <span class="text-[10px] text-[var(--color-text-primary)] wuxing-huo">
                            <i class="fas fa-fire wuxing-icon"></i>巳
                        </span>
                    </div>
                    <div class="text-center">
                        <span class="text-[10px] text-[var(--color-text-primary)] wuxing-mu">
                            <i class="fas fa-leaf wuxing-icon"></i>卯
                        </span>
                    </div>
                    <div class="text-center">
                        <span class="text-[10px] text-[var(--color-text-primary)] wuxing-shui">
                            <i class="fas fa-water wuxing-icon"></i>子
                        </span>
                    </div>
                    <div class="text-center">
                        <span class="text-[10px] text-[var(--color-text-primary)] wuxing-jin">
                            <i class="fas fa-coins wuxing-icon"></i>酉
                        </span>
                    </div>
                </div>

                <!-- 藏干行 -->
                <div class="grid grid-cols-5 gap-2">
                    <div class="text-center">
                        <span class="text-[10px] text-[var(--color-text-secondary)]">藏干</span>
                    </div>
                    <div class="text-center">
                        <div class="flex flex-col items-center gap-1">
                            <span class="text-[10px] text-[var(--color-text-primary)] wuxing-huo">
                                <i class="fas fa-fire wuxing-icon"></i>丙
                            </span>
                            <span class="text-[10px] text-[var(--color-text-primary)] wuxing-tu">
                                <i class="fas fa-mountain wuxing-icon"></i>戊
                            </span>
                            <span class="text-[10px] text-[var(--color-text-primary)] wuxing-jin">
                                <i class="fas fa-coins wuxing-icon"></i>庚
                            </span>
                        </div>
                    </div>
                    <div class="text-center">
                        <div class="flex flex-col items-center gap-1">
                            <span class="text-[10px] text-[var(--color-text-primary)] wuxing-mu">
                                <i class="fas fa-leaf wuxing-icon"></i>乙
                            </span>
                        </div>
                    </div>
                    <div class="text-center">
                        <div class="flex flex-col items-center gap-1">
                            <span class="text-[10px] text-[var(--color-text-primary)] wuxing-shui">
                                <i class="fas fa-water wuxing-icon"></i>癸
                            </span>
                        </div>
                    </div>
                    <div class="text-center">
                        <div class="flex flex-col items-center gap-1">
                            <span class="text-[10px] text-[var(--color-text-primary)] wuxing-jin">
                                <i class="fas fa-coins wuxing-icon"></i>辛
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 五行分析 (圆形节点，外部标注，还原图示) -->
        <div class="enhanced-glass rounded-xl p-4 mb-4">
            <h3 class="text-sm font-medium text-[var(--color-text-primary)] mb-3">五行分析</h3>
            
            <div class="relative w-full aspect-square max-w-[280px] mx-auto mt-8 mb-10"> <!-- 调整边距 -->

                <!-- 火 (Top vertex) -->
                 <div class="absolute top-[1%] left-1/2 transform -translate-x-1/2 z-10">
                     <div class="wuxing-node wuxing-bg-huo">
                        <span class="wuxing-element-name">火</span>
                        <span class="wuxing-percentage">8%</span>
                    </div>
                </div>
                
                <!-- 土 (Top-right vertex) -->
                <div class="absolute top-[43%] right-[0%] transform -translate-y-1/2 z-10">
                     <div class="wuxing-node wuxing-bg-tu">
                        <span class="wuxing-element-name">土</span>
                        <span class="wuxing-percentage">22%</span>
                    </div>
                </div>
                
                <!-- 金 (Bottom-right vertex) -->
                 <div class="absolute bottom-[4%] right-[20%] transform translate-x-1/2 z-10">
                     <div class="wuxing-node wuxing-bg-jin">
                        <span class="wuxing-element-name">金</span>
                        <span class="wuxing-percentage">22%</span>
                    </div>
                </div>
                
                 <!-- 水 (Bottom-left vertex) -->
                 <div class="absolute bottom-[4%] left-[20%] transform -translate-x-1/2 z-10">
                     <div class="wuxing-node wuxing-bg-shui opacity-80">
                        <span class="wuxing-element-name">水</span>
                        <span class="wuxing-percentage">0%</span>
                    </div>
                </div>
                
                <!-- 木 (Top-left vertex) - 日主 -->
                <div class="absolute top-[43%] left-[0%] transform -translate-y-1/2 z-10">
                     <div class="wuxing-node wuxing-bg-mu border-green-500 border-2">
                        <span class="wuxing-element-name">木</span>
                        <span class="wuxing-percentage">46%</span>
                    </div>
                    </div>

                <!-- 相生箭头 & 相克线条 (保持不变) -->
                <svg class="absolute inset-0 w-full h-full opacity-70" viewbox="0 0 100 100" preserveAspectRatio="xMidYMid meet">
                     <defs>
                        <!-- 添加网格图案定义 -->
                        <pattern id="grid" width="5" height="5" patternUnits="userSpaceOnUse">
                            <path d="M 5 0 L 0 0 0 5" fill="none" stroke="rgba(0,0,0,0.1)" stroke-width="0.5"/>
                        </pattern>
                        <!-- 原有的箭头定义 -->
                        <marker id="arrowhead-sheng" markerWidth="4" markerHeight="2.8" refX="4" refY="1.4" orient="auto">
                            <polygon points="0 0, 4 1.4, 0 2.8" fill="#666" />
                        </marker>
                        <!-- 更新坐标 -->
                        <g id="fireCoord"><circle cx="50" cy="7" r="1"/></g>
                        <g id="earthCoord"><circle cx="94" cy="38" r="1"/></g> <!-- right 0% -> cx=94? -->
                        <g id="metalCoord"><circle cx="80" cy="90" r="1"/></g> <!-- bottom 4%, right 18% -> cx=80, cy=90? -->
                        <g id="waterCoord"><circle cx="20" cy="90" r="1"/></g> <!-- bottom 4%, left 18% -> cx=20, cy=90? -->
                        <g id="woodCoord"><circle cx="6" cy="38" r="1"/></g>   <!-- left 0% -> cx=6? -->
                    </defs>
                    
                    <!-- 添加网格背景 -->
                    <rect width="100" height="100" fill="url(#grid)" />
                    
                    <!-- 原有的相生路径和相克线条 -->
                    <path d="M 15 38 A 45 45 0 0 1 48 13" stroke="#888" stroke-width="0.6" fill="none" marker-end="url(#arrowhead-sheng)" /> <!-- Wood to Fire -->
                    <path d="M 52 13 A 45 45 0 0 1 85 38" stroke="#888" stroke-width="0.6" fill="none" marker-end="url(#arrowhead-sheng)" /> <!-- Fire to Earth -->
                    <path d="M 85 43 A 45 45 0 0 1 75 83" stroke="#888" stroke-width="0.6" fill="none" marker-end="url(#arrowhead-sheng)" /> <!-- Earth to Metal -->
                    <path d="M 70 86 A 45 45 0 0 1 30 86" stroke="#888" stroke-width="0.6" fill="none" marker-end="url(#arrowhead-sheng)" /> <!-- Metal to Water -->
                    <path d="M 25 83 A 45 45 0 0 1 15 43" stroke="#888" stroke-width="0.6" fill="none" marker-end="url(#arrowhead-sheng)" /> <!-- Water to Wood -->
                    <!-- 相克线条 (调整坐标) -->
                    <line x1="20" y1="40" x2="80" y2="40" stroke="#aaa" stroke-width="0.5" stroke-dasharray="2,3" /> <!-- Wood to Earth -->
                    <line x1="50" y1="15" x2="75" y2="85" stroke="#aaa" stroke-width="0.5" stroke-dasharray="2,3" /> <!-- Fire to Metal -->
                    <line x1="85" y1="45" x2="25" y2="85" stroke="#aaa" stroke-width="0.5" stroke-dasharray="2,3" /> <!-- Earth to Water -->
                    <line x1="75" y1="88" x2="15" y2="45" stroke="#aaa" stroke-width="0.5" stroke-dasharray="2,3" /> <!-- Metal to Wood -->
                    <line x1="25" y1="88" x2="50" y2="15" stroke="#aaa" stroke-width="0.5" stroke-dasharray="2,3" /> <!-- Water to Fire -->
                </svg>

                <!-- 五行关系文字 (保持样式) -->
                 <div class="absolute inset-0 flex items-center justify-center text-gray-400 text-[8px] font-medium pointer-events-none">
                     <!-- 生 -->
                     <span class=\"absolute\" style=\"top: 16%; left: 28%; transform: rotate(-65deg);\">生</span>
                     <span class=\"absolute\" style=\"top: 16%; right: 28%; transform: rotate(65deg);\">生</span>
                     <span class=\"absolute\" style=\"top: 59%; right: 12%;\">生</span>
                     <span class=\"absolute\" style=\"bottom: 2%; left: 50%; transform: translateX(-50%);\">生</span> <!-- 微调 -->
                     <span class=\"absolute\" style=\"top: 59%; left: 12%;\">生</span>
                     <!-- 克 -->
                     <span class=\"absolute\" style=\"top: 40%; left: 50%; transform: translateX(-50%);\">克</span>
                     <span class=\"absolute\" style=\"top: 35%; right: 38%; transform: rotate(70deg);\">克</span>
                     <span class=\"absolute\" style=\"bottom: 20%; right: 36%; transform: rotate(-35deg);\">克</span> <!-- 微调 -->
                     <span class=\"absolute\" style=\"bottom: 20%; left: 36%; transform: rotate(35deg);\">克</span> <!-- 微调 -->
                     <span class=\"absolute\" style=\"top: 35%; left: 38%; transform: rotate(-70deg);\">克</span>
                </div>
            </div>
            
            <!-- 喜用神与幸运信息 -->
            <p class="text-xs text-[var(--color-text-secondary)] mt-4">
                喜用木、水，宜着绿色、蓝色，忌着红色、黄色。
            </p>
        </div>
        
        <!-- 整体运势解读 -->
        <div class="enhanced-glass rounded-xl p-6 mb-6">
            <h3 class="text-sm font-medium text-[var(--color-text-primary)] mb-4">整体运势</h3>
            
            <!-- 运势趋势图 -->
            <div class="mb-6">
                <h4 class="text-xs font-medium text-[var(--color-text-primary)] mb-3">一生运势趋势</h4>
                <!-- 图表容器 -->
                <div class="overflow-x-auto overflow-y-hidden scrollbar-hide">
                    <div style="min-width: 1600px;">
                        <div style="height: 180px;">
                            <canvas id="fortuneChart"></canvas>
                        </div>
                        
                        <!-- 运势解读列表 -->
                        <div class="mt-4">
                            <div class="grid grid-cols-10 gap-4">
                                <div class="text-center">
                                    <span class="text-[10px] text-[var(--color-text-secondary)]">1990-2000</span>
                                    <p class="text-[10px] text-[var(--color-text-secondary)] mt-1">童年时期，家庭环境较好，但因木火不足，个性较为内向。</p>
                                </div>
                                <div class="text-center">
                                    <span class="text-[10px] text-[var(--color-text-secondary)]">2000-2010</span>
                                    <p class="text-[10px] text-[var(--color-text-secondary)] mt-1">求学阶段，学习能力突出，但人际关系有待提升。</p>
                                </div>
                                <div class="text-center">
                                    <span class="text-[10px] text-[var(--color-text-secondary)]">2010-2020</span>
                                    <p class="text-[10px] text-[var(--color-text-secondary)] mt-1">事业起步期，有贵人相助，但需克服困难。</p>
                                </div>
                                <div class="text-center">
                                    <span class="text-[10px] text-[var(--color-text-secondary)]">2020-2030</span>
                                    <p class="text-[10px] text-[var(--color-text-secondary)] mt-1">发展上升期，事业财运双丰收，尤其2027-2029年。</p>
                                </div>
                                <div class="text-center">
                                    <span class="text-[10px] text-[var(--color-text-secondary)]">2030-2040</span>
                                    <p class="text-[10px] text-[var(--color-text-secondary)] mt-1">稳定成熟期，事业达到顶峰，家庭幸福美满。</p>
                                </div>
                                <div class="text-center">
                                    <span class="text-[10px] text-[var(--color-text-secondary)]">2040-2050</span>
                                    <p class="text-[10px] text-[var(--color-text-secondary)] mt-1">事业稳健发展期，有新的投资机会，注意把握。</p>
                                </div>
                                <div class="text-center">
                                    <span class="text-[10px] text-[var(--color-text-secondary)]">2050-2060</span>
                                    <p class="text-[10px] text-[var(--color-text-secondary)] mt-1">平稳发展期，家庭和睦，有机会开创新事业。</p>
                                </div>
                                <div class="text-center">
                                    <span class="text-[10px] text-[var(--color-text-secondary)]">2060-2070</span>
                                    <p class="text-[10px] text-[var(--color-text-secondary)] mt-1">享受生活期，子女事业有成，家庭美满。</p>
                                </div>
                                <div class="text-center">
                                    <span class="text-[10px] text-[var(--color-text-secondary)]">2070-2080</span>
                                    <p class="text-[10px] text-[var(--color-text-secondary)] mt-1">颐养天年期，健康运势良好，家族兴旺。</p>
                                </div>
                                <div class="text-center">
                                    <span class="text-[10px] text-[var(--color-text-secondary)]">2080-2090</span>
                                    <p class="text-[10px] text-[var(--color-text-secondary)] mt-1">福寿双全期，享受天伦之乐，家族昌盛。</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- 滑动提示 -->
                <div class="mt-2 text-center">
                    <span class="text-[10px] text-[var(--color-text-tertiary)]">
                        <i class="fas fa-arrows-left-right mr-1"></i>左右滑动查看百年运势
                    </span>
                </div>
            </div>
            
            <!-- 当前运势 -->
            <div class="content-section">
                <div class="flex items-start gap-3">
                    <div class="w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center flex-shrink-0">
                        <i class="fas fa-chart-line text-[12px] text-blue-400"></i>
                    </div>
                    <div class="space-y-2">
                        <p class="text-xs text-[var(--color-text-secondary)] leading-relaxed">当前行脱丙运，表现为：今年有"探神"贵人运，整体仍算不错，但因今年犯刑破太岁，要预防小人半路破坏，切勿逞强。</p>
                        <p class="text-xs text-[var(--color-text-secondary)] leading-relaxed">同时，有"官符"煞影响，要预防有官司缠身，避免损失钱财。</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 吉运提升建议（独立卡片） -->
        <div class="enhanced-glass rounded-xl p-6 mb-6">
            <div class="flex items-center gap-2 mb-4">
                <div class="w-6 h-6 rounded-full bg-yellow-100 flex items-center justify-center">
                    <i class="fas fa-star text-[12px] text-yellow-400"></i>
                </div>
                <h3 class="text-sm font-medium text-[var(--color-text-primary)]">吉运提升建议</h3>
            </div>
            <div class="space-y-6">
                <!-- 服装选择 -->
                <div>
                    <h4 class="text-xs font-medium text-[var(--color-text-primary)] mb-3">服装选择</h4>
                            <div class="flex flex-wrap gap-2">
                        <span class="text-[10px] text-[var(--color-text-secondary)]">亮色系服装:</span>
                        <span class="text-[10px] text-[var(--color-text-secondary)]">提升火运势</span>
                    </div>
                </div>
                <!-- 首饰佩戴 -->
                <div>
                    <h4 class="text-xs font-medium text-[var(--color-text-primary)] mb-3">首饰佩戴</h4>
                        <div class="space-y-2">
                                <div class="flex items-center gap-2">
                            <span class="text-[10px] text-[var(--color-text-secondary)]">木质饰品：</span>
                                    <span class="text-[10px] text-[var(--color-text-secondary)]">檀木/沉香，木能疏土，平衡命局土旺</span>
                                </div>
                                <div class="flex items-center gap-2">
                            <span class="text-[10px] text-[var(--color-text-secondary)]">红色宝石：</span>
                                    <span class="text-[10px] text-[var(--color-text-secondary)]">红玛瑙等，补火</span>
                                </div>
                                <div class="flex items-center gap-2">
                            <span class="text-[10px] text-[var(--color-text-secondary)]">金属饰品：</span>
                                    <span class="text-[10px] text-[var(--color-text-secondary)]">金可泄土生水</span>
                        </div>
                    </div>
            </div>
                <!-- 居家风水 -->
                <div>
                    <h4 class="text-xs font-medium text-[var(--color-text-primary)] mb-3">居家风水</h4>
                            <div class="space-y-2">
                                <div class="flex items-center gap-2">
                                    <i class="fas fa-times-circle text-[10px] text-red-400"></i>
                                    <span class="text-[10px] text-[var(--color-text-secondary)]">避免土属性过重的物品（如减少黄色陶瓷或装饰)</span>
                                </div>
                                <div class="flex items-center gap-2">
                                    <i class="fas fa-plus-circle text-[10px] text-green-400"></i>
                                    <span class="text-[10px] text-[var(--color-text-secondary)]">多用红色装饰（红地毯等）</span>
                                </div>
                                <div class="flex items-center gap-2">
                                    <i class="fas fa-plus-circle text-[10px] text-green-400"></i>
                                    <span class="text-[10px] text-[var(--color-text-secondary)]">采用木质家具，增强木气疏土</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 运势详细解读 -->
        <div class="enhanced-glass rounded-xl mb-6">
            <!-- Tab 切换 -->
            <div class="flex border-b border-white/20">
                <button class="tab-button flex-1 py-3 text-xs text-center tab-active" data-tab="month">月运势</button>
                <button class="tab-button flex-1 py-3 text-xs text-center" data-tab="year">年运势</button>
            </div>
            
            <!-- Tab 内容区域 -->
            <div class="tab-content p-6" id="monthContent">
                <!-- 事业运势 -->
                <div class="mb-6 last:mb-0">
                    <div class="flex items-center gap-2 mb-2">
                        <div class="w-6 h-6 rounded-full bg-yellow-100 flex items-center justify-center">
                        <i class="fas fa-briefcase text-[12px] text-yellow-400"></i>
                        </div>
                        <h3 class="text-sm font-medium text-[var(--color-text-primary)]">事业运势</h3>
                    </div>
                    
                    <div class="content-section">
                        <div class="flex items-start gap-3">
                            <div class="space-y-2">
                        <div class="flex items-center gap-2">
                                    <span class="text-xs font-medium text-yellow-500">本月事业运势上升</span>
                                    <span class="text-xs text-[var(--color-text-secondary)]">有贵人相助</span>
                        </div>
                                <p class="text-xs text-[var(--color-text-secondary)] leading-relaxed">本月适合主动出击，把握机会。中旬有贵人带来机遇，建议积极争取。</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 财富运势 -->
                <div class="mb-6 last:mb-0">
                    <div class="flex items-center gap-2 mb-2">
                        <div class="w-6 h-6 rounded-full bg-green-100 flex items-center justify-center">
                            <i class="fas fa-coins text-[12px] text-green-400"></i>
                        </div>
                        <h3 class="text-sm font-medium text-[var(--color-text-primary)]">财富运势</h3>
                    </div>
                    
                    <div class="content-section">
                        <div class="flex items-start gap-2">
                            <div class="space-y-1">
                            <div class="flex items-center gap-2">
                                    <span class="text-xs font-medium text-green-500">本月财运上升</span>
                                </div>
                                <p class="text-xs text-[var(--color-text-secondary)] leading-relaxed">本月财运良好，适合投资理财。中旬可能有意外收获。</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 健康运势 -->
                <div class="mb-6 last:mb-0">
                    <div class="flex items-center gap-2 mb-2">
                        <div class="w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center">
                            <i class="fas fa-heartbeat text-[12px] text-blue-400"></i>
                        </div>
                        <h3 class="text-sm font-medium text-[var(--color-text-primary)]">健康运势</h3>
                    </div>
                    
                    <div class="content-section">
                        <div class="flex items-start gap-3">
                        <div class="space-y-3">
                                <p class="text-xs text-[var(--color-text-secondary)] leading-relaxed">本月身体状况良好，但要注意作息规律，避免过度劳累。</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 婚姻运势 -->
                <div class="mb-6 last:mb-0">
                    <div class="flex items-center gap-2 mb-2">
                        <div class="w-6 h-6 rounded-full bg-pink-100 flex items-center justify-center">
                            <i class="fas fa-heart text-[12px] text-pink-400"></i>
            </div>
                        <h3 class="text-sm font-medium text-[var(--color-text-primary)]">婚姻运势</h3>
        </div>
        
                    <div class="content-section">
                        <div class="flex items-start gap-3">
                            <div class="space-y-2">
                                <p class="text-xs text-[var(--color-text-secondary)] leading-relaxed">本月婚姻运势平稳，但需要注意沟通方式，避免因小事引发争执。建议多关心对方，增进感情。</p>
            </div>
                        </div>
                    </div>
                </div>

                <!-- 子女运势 -->
                <div class="mb-6 last:mb-0">
                    <div class="flex items-center gap-2 mb-2">
                        <div class="w-6 h-6 rounded-full bg-purple-100 flex items-center justify-center">
                            <i class="fas fa-child text-[12px] text-purple-400"></i>
                        </div>
                        <h3 class="text-sm font-medium text-[var(--color-text-primary)]">子女运势</h3>
                    </div>
                    
                    <div class="content-section">
                        <div class="flex items-start gap-3">
                        <div class="space-y-2">
                                <p class="text-xs text-[var(--color-text-secondary)] leading-relaxed">本月子女运势良好，学习进步明显。建议多陪伴交流，关注其身心健康发展。</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="tab-content hidden p-6" id="yearContent">
                <!-- 事业运势 -->
                <div class="mb-6 last:mb-0">
                    <div class="flex items-center gap-2 mb-2">
                        <div class="w-6 h-6 rounded-full bg-yellow-100 flex items-center justify-center">
                            <i class="fas fa-briefcase text-[12px] text-yellow-400"></i>
                        </div>
                        <h3 class="text-sm font-medium text-[var(--color-text-primary)]">事业运势</h3>
                    </div>
                    
                    <div class="content-section">
                        <div class="flex items-start gap-3">
                        <div class="space-y-2">
                                <div class="flex items-center gap-2">
                                    <span class="text-xs font-medium text-yellow-500">八字中"时辰带贵"</span>
                                    <span class="text-xs text-[var(--color-text-secondary)]">整体事业运不错</span>
                                </div>
                                <p class="text-xs text-[var(--color-text-secondary)] leading-relaxed">今年事业运势为平淡，小人妨害多，建议把握2027-2029年机遇，事业上可获得新成就。</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 财富运势 -->
                <div class="mb-6 last:mb-0">
                    <div class="flex items-center gap-2 mb-2">
                        <div class="w-6 h-6 rounded-full bg-green-100 flex items-center justify-center">
                            <i class="fas fa-coins text-[12px] text-green-400"></i>
                        </div>
                        <h3 class="text-sm font-medium text-[var(--color-text-primary)]">财富运势</h3>
                    </div>
                    
                    <div class="content-section">
                        <div class="flex items-start gap-2">
                            <div class="space-y-1">
                                <div class="flex items-center gap-2">
                                    <span class="text-xs font-medium text-green-500">属于"天干地支有财"命格</span>
                                </div>
                                <p class="text-xs text-[var(--color-text-secondary)] leading-relaxed">今年财运平稳，但需防范"岁破大耗"煞的影响，避免借贷和不必要的支出。</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 健康运势 -->
                <div class="mb-6 last:mb-0">
                    <div class="flex items-center gap-2 mb-2">
                        <div class="w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center">
                            <i class="fas fa-heartbeat text-[12px] text-blue-400"></i>
            </div>
                        <h3 class="text-sm font-medium text-[var(--color-text-primary)]">健康运势</h3>
        </div>
        
                    <div class="content-section">
                        <div class="flex items-start gap-2">
                            <div class="space-y-1">
                                <p class="text-xs text-[var(--color-text-secondary)] leading-relaxed">今年受刑破太岁影响，需要特别注意身体状况，保持良好的作息习惯。建议定期体检，预防为主。</p>
                            </div>
                        </div>
                    </div>
            </div>
            
                <!-- 婚姻运势 -->
                <div class="mb-6 last:mb-0">
                    <div class="flex items-center gap-2 mb-2">
                        <div class="w-6 h-6 rounded-full bg-pink-100 flex items-center justify-center">
                            <i class="fas fa-heart text-[12px] text-pink-400"></i>
                        </div>
                        <h3 class="text-sm font-medium text-[var(--color-text-primary)]">婚姻运势</h3>
                    </div>
                    
                    <div class="content-section">
                        <div class="flex items-start gap-2">
                            <div class="space-y-1">
                                <p class="text-xs text-[var(--color-text-secondary)] leading-relaxed">此命格可得贵妻，妻旺财运。但因命带"寡宿"煞，导致夫妻间容易争吵，互不相让。</p>
                                <p class="text-xs text-[var(--color-text-secondary)] leading-relaxed">加上五行中"土"多，易生情性，经常引发妻子不满，加剧矛盾。建议克服因"土"多滋生的情性。</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 子女运势 -->
                <div class="mb-6 last:mb-0">
                    <div class="flex items-center gap-2 mb-2">
                        <div class="w-6 h-6 rounded-full bg-purple-100 flex items-center justify-center">
                            <i class="fas fa-child text-[12px] text-purple-400"></i>
                        </div>
                        <h3 class="text-sm font-medium text-[var(--color-text-primary)]">子女运势</h3>
                    </div>
                    
                    <div class="content-section">
                        <div class="flex items-start gap-3">
                        <div class="space-y-2">
                                <div class="flex items-center gap-2">
                                    <span class="text-xs font-medium text-red-400">带有"妨害煞"</span>
                                </div>
                                <p class="text-xs text-[var(--color-text-secondary)] leading-relaxed">容易影响子女运势，可能子女数量不多，或者需多注意子女健康问题。</p>
                                <p class="text-xs text-[var(--color-text-secondary)] leading-relaxed">未来子女相对独立，个性较强，需要适当引导。预示子女有较大潜力，需要多加培养。</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化运势趋势图
            const ctx = document.getElementById('fortuneChart').getContext('2d');
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: [
                        '1990-2000', '2000-2010', '2010-2020', '2020-2030', '2030-2040',
                        '2040-2050', '2050-2060', '2060-2070', '2070-2080', '2080-2090'
                    ],
                    datasets: [{
                        label: '运势指数',
                        data: [65, 75, 70, 85, 90, 88, 85, 82, 80, 78],
                        borderColor: 'rgba(99, 102, 241, 0.8)',
                        backgroundColor: 'rgba(99, 102, 241, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return `运势指数: ${context.parsed.y}`;
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            grid: {
                                color: 'rgba(255, 255, 255, 0.1)'
                            },
                            ticks: {
                                color: 'rgba(102, 102, 102, 0.9)',
                                font: {
                                    size: 10,
                                    weight: '500'
                                }
                            }
                        },
                        x: {
                            grid: {
                                color: 'rgba(255, 255, 255, 0.1)'
                            },
                            ticks: {
                                color: 'rgba(102, 102, 102, 0.9)',
                                font: {
                                    size: 10,
                                    weight: '500'
                                }
                            }
                        }
                    }
                }
            });

            // Tab 切换功能
            const tabButtons = document.querySelectorAll('.tab-button');
            const tabContents = document.querySelectorAll('.tab-content');

            tabButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const tab = button.dataset.tab;
                    
                    // 更新按钮状态
                    tabButtons.forEach(btn => btn.classList.remove('tab-active'));
                    button.classList.add('tab-active');
                    
                    // 更新内容显示
                    tabContents.forEach(content => {
                        if (content.id === tab + 'Content') {
                            content.classList.remove('hidden');
                        } else {
                            content.classList.add('hidden');
                        }
                    });
                });
            });
        });
    </script>
</body>
</html> 