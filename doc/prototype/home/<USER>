<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <title>搭配视频 - 穿搭推荐小程序</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --color-text-primary: #333333;
            --color-text-secondary: #666666;
            --color-text-tertiary: #999999;
        }
        
        /* 隐藏滚动条 */
        ::-webkit-scrollbar {
            display: none;
        }
        
        * {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        
        body {
            overflow-y: scroll;
            -webkit-overflow-scrolling: touch;
        }

        /* 内容区域统一间距 */
        .content-container {
            padding-left: 5%;
            padding-right: 5%;
        }
        
        .gradient-background {
            background: linear-gradient(135deg, #c2e9fb 0%, #d8c6fd 100%);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: -1;
        }
        
        .enhanced-glass {
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.05);
        }
        
        .glass-button {
            background: rgba(255, 255, 255, 0.4);
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.5);
            transition: all 0.3s ease;
        }
        
        .glass-button:hover {
            background: rgba(255, 255, 255, 0.6);
        }
        
        /* 视频播放相关样式 */
        .video-container {
            position: relative;
            width: 100%;
            border-radius: 12px;
            overflow: hidden;
            background-color: #000;
        }
        
        .video-controls {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(0, 0, 0, 0.4);
            backdrop-filter: blur(3px);
            padding: 8px 16px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            opacity: 1;
            transition: opacity 0.3s;
        }
        
        .video-controls.hidden {
            opacity: 0;
        }
        
        .play-button {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            cursor: pointer;
        }
        
        .progress-container {
            flex-grow: 1;
            margin: 0 10px;
            height: 4px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 2px;
            overflow: hidden;
            position: relative;
        }
        
        .progress-bar {
            height: 100%;
            background: #fff;
            width: 0;
        }
        
        .loading-animation {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-top-color: #fff;
            animation: spin 1s infinite linear;
        }
        
        @keyframes spin {
            to { transform: translate(-50%, -50%) rotate(360deg); }
        }
        
        /* 声音提示动画 */
        .sound-wave {
            display: flex;
            align-items: center;
            height: 16px;
        }
        
        .sound-wave span {
            display: inline-block;
            width: 2px;
            height: 100%;
            margin: 0 1px;
            background-color: white;
            border-radius: 1px;
            animation: sound-wave-animation 1.2s infinite ease-in-out;
        }
        
        .sound-wave span:nth-child(2) {
            animation-delay: 0.2s;
        }
        
        .sound-wave span:nth-child(3) {
            animation-delay: 0.4s;
        }
        
        .sound-wave span:nth-child(4) {
            animation-delay: 0.6s;
        }
        
        .sound-wave span:nth-child(5) {
            animation-delay: 0.8s;
        }
        
        @keyframes sound-wave-animation {
            0%, 100% { height: 4px; }
            50% { height: 16px; }
        }
        
        /* 标签项 */
        .info-tag {
            display: inline-flex;
            align-items: center;
            background: rgba(255, 255, 255, 0.3);
            padding: 4px 8px;
            border-radius: 100px;
            font-size: 10px;
            color: var(--color-text-secondary);
            margin-right: 4px;
            margin-bottom: 4px;
        }
        
        .info-tag i {
            margin-right: 4px;
            font-size: 8px;
        }
    </style>
</head>
<body>
    <!-- 渐变背景 -->
    <div class="gradient-background"></div>

    <!-- 顶部导航 -->
    <div class="sticky top-0 bg-white/30 backdrop-blur-md z-40 shadow-sm">
        <div class="flex items-center p-4">
            <a href="outfit_detail.html" class="mr-4">
                <i class="fas fa-arrow-left text-[var(--color-text-primary)]"></i>
            </a>
            <h1 class="text-sm font-semibold text-[var(--color-text-primary)]">搭配视频</h1>
        </div>
    </div>

    <!-- 主内容区 -->
    <div class="content-container py-4 pb-20">
        <!-- 视频标题 -->
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-base font-semibold text-[var(--color-text-primary)]">清新白裙职场穿搭</h2>
            <div class="flex items-center">
                <span class="text-[10px] text-[var(--color-text-secondary)] mr-1">消耗</span>
                <div class="flex items-center bg-white/30 px-2 py-0.5 rounded-full">
                    <i class="fas fa-bolt text-[10px] text-yellow-500 mr-1"></i>
                    <span class="text-xs text-[var(--color-text-primary)]">40</span>
                </div>
            </div>
        </div>
        
        <!-- 视频播放器 -->
        <div class="video-container mb-4 aspect-[9/16]">
            <!-- 视频元素 -->
            <video id="outfit-video" class="w-full h-full object-cover" poster="https://images.unsplash.com/photo-1515886657613-9f3515b0c78f" preload="auto" playsinline>
                <source src="../assets/videos/9e0ad5bb344f4821b10a9c168b4bcb28.mp4" type="video/mp4">
                您的浏览器不支持视频播放
            </video>
            
            <!-- 加载动画 -->
            <div class="loading-animation" id="loading-animation"></div>
            
            <!-- 播放按钮覆盖层 -->
            <div class="absolute inset-0 flex items-center justify-center bg-black/30" id="play-overlay">
                <div class="w-16 h-16 rounded-full bg-white/30 backdrop-blur-sm flex items-center justify-center">
                    <i class="fas fa-play text-white text-xl"></i>
                </div>
            </div>
            
            <!-- 声音提示 -->
            <div class="absolute top-4 right-4 flex items-center bg-black/40 backdrop-blur-sm rounded-full px-3 py-1.5">
                <i class="fas fa-volume-up text-white text-xs mr-2"></i>
                <div class="sound-wave">
                    <span></span>
                    <span></span>
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
            
            <!-- 控制条 -->
            <div class="video-controls" id="video-controls">
                <div class="play-button" id="play-button">
                    <i class="fas fa-play" id="play-icon"></i>
                </div>
                
                <div class="progress-container">
                    <div class="progress-bar" id="progress-bar"></div>
                </div>
                
                <div class="time text-white text-xs">
                    <span id="current-time">00:00</span>
                    <span>/</span>
                    <span id="duration">00:05</span>
                </div>
            </div>
        </div>
        
        <!-- 搭配信息卡片 -->
        <div class="enhanced-glass rounded-xl p-4 mb-4">
            <div class="flex justify-between items-start mb-3">
                <h3 class="text-sm font-medium text-[var(--color-text-primary)]">搭配详情</h3>
                <a href="outfit_detail.html" class="text-xs text-[var(--color-text-primary)] underline">
                    查看完整详情
                </a>
            </div>
            
            <p class="text-xs text-[var(--color-text-secondary)] mb-3">
                这套穿搭以清爽的白色为基调，简约大气，适合春夏季节。米色高跟鞋中和整体偏冷的色调，蓝色包包作为点缀，为整体造型增添亮点。
            </p>
            
            <div class="flex flex-wrap mb-2">
                <span class="info-tag">
                    <i class="fas fa-briefcase"></i>职场通勤
                </span>
                <span class="info-tag">
                    <i class="fas fa-sun"></i>春夏季节
                </span>
                <span class="info-tag">
                    <i class="fas fa-tint"></i>清爽感
                </span>
                <span class="info-tag">
                    <i class="fas fa-star"></i>4.5分
                </span>
            </div>
            
            <!-- 五行标签 -->
            <div class="flex flex-wrap">
                <div class="mr-2 mb-2 flex items-center p-1 px-2 rounded-full bg-white/40">
                    <span class="w-3 h-3 rounded-full bg-white mr-1"></span>
                    <span class="text-[10px] text-[var(--color-text-secondary)]">金(40%)</span>
                </div>
                <div class="mr-2 mb-2 flex items-center p-1 px-2 rounded-full bg-[#a8e6cf]/40">
                    <span class="w-3 h-3 rounded-full bg-[#a8e6cf] mr-1"></span>
                    <span class="text-[10px] text-[var(--color-text-secondary)]">木(10%)</span>
                </div>
                <div class="mr-2 mb-2 flex items-center p-1 px-2 rounded-full bg-[#b8c6db]/40">
                    <span class="w-3 h-3 rounded-full bg-[#b8c6db] mr-1"></span>
                    <span class="text-[10px] text-[var(--color-text-secondary)]">水(20%)</span>
                </div>
                <div class="mr-2 mb-2 flex items-center p-1 px-2 rounded-full bg-[#ffeaa7]/40">
                    <span class="w-3 h-3 rounded-full bg-[#ffeaa7] mr-1"></span>
                    <span class="text-[10px] text-[var(--color-text-secondary)]">土(30%)</span>
                </div>
            </div>
        </div>
        
        <!-- 单品信息 -->
        <div class="enhanced-glass rounded-xl p-4 mb-4">
            <h3 class="text-sm font-medium text-[var(--color-text-primary)] mb-3">单品清单</h3>
            
            <!-- 单品水平滚动列表 -->
            <div class="flex overflow-x-auto pb-2 -mx-1">
                <!-- 单品1 -->
                <div class="flex-shrink-0 w-20 mx-1">
                    <div class="w-full aspect-square rounded-lg overflow-hidden mb-1">
                        <img src="https://images.unsplash.com/photo-1597843664423-e547e596dee4" 
                             class="w-full h-full object-cover" alt="白色衬衫">
                    </div>
                    <p class="text-[10px] text-[var(--color-text-secondary)] truncate">
                        白色衬衫
                    </p>
                </div>
                
                <!-- 单品2 -->
                <div class="flex-shrink-0 w-20 mx-1">
                    <div class="w-full aspect-square rounded-lg overflow-hidden mb-1">
                        <img src="https://images.unsplash.com/photo-1577900232427-18219b9166a0" 
                             class="w-full h-full object-cover" alt="白色A字裙">
                    </div>
                    <p class="text-[10px] text-[var(--color-text-secondary)] truncate">
                        白色A字裙
                    </p>
                </div>
                
                <!-- 单品3 -->
                <div class="flex-shrink-0 w-20 mx-1">
                    <div class="w-full aspect-square rounded-lg overflow-hidden mb-1">
                        <img src="https://images.unsplash.com/photo-1543163521-1bf539c55dd2" 
                             class="w-full h-full object-cover" alt="米色高跟鞋">
                    </div>
                    <p class="text-[10px] text-[var(--color-text-secondary)] truncate">
                        米色高跟鞋
                    </p>
                </div>
                
                <!-- 单品4 -->
                <div class="flex-shrink-0 w-20 mx-1">
                    <div class="w-full aspect-square rounded-lg overflow-hidden mb-1">
                        <img src="https://images.unsplash.com/photo-1584917865442-de89df76afd3" 
                             class="w-full h-full object-cover" alt="蓝色手提包">
                    </div>
                    <p class="text-[10px] text-[var(--color-text-secondary)] truncate">
                        蓝色手提包
                    </p>
                </div>
            </div>
        </div>
        
        <!-- 视频服务说明 -->
        <div class="flex items-center bg-white/10 rounded-xl p-3 mb-2">
            <i class="fas fa-info-circle text-[#b8c6db] mr-3"></i>
            <p class="text-[10px] text-[var(--color-text-secondary)]">
                视频观看每次消耗40灵感值，Lv3以上用户每日可免费观看1次
            </p>
        </div>
        
        <!-- 功能按钮 -->
        <div class="grid grid-cols-3 gap-3 mb-10">
            <button class="glass-button py-3 rounded-xl flex flex-col items-center">
                <i class="fas fa-redo text-[var(--color-text-primary)]"></i>
                <span class="text-[10px] text-[var(--color-text-secondary)] mt-1">重新播放</span>
            </button>
            
            <button class="glass-button py-3 rounded-xl flex flex-col items-center">
                <i class="fas fa-share-alt text-[var(--color-text-primary)]"></i>
                <span class="text-[10px] text-[var(--color-text-secondary)] mt-1">分享视频</span>
            </button>
            
            <a href="outfit_detail.html" class="glass-button py-3 rounded-xl flex flex-col items-center">
                <i class="fas fa-info-circle text-[var(--color-text-primary)]"></i>
                <span class="text-[10px] text-[var(--color-text-secondary)] mt-1">搭配详情</span>
            </a>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const video = document.getElementById('outfit-video');
            const playButton = document.getElementById('play-button');
            const playIcon = document.getElementById('play-icon');
            const progressBar = document.getElementById('progress-bar');
            const currentTimeDisplay = document.getElementById('current-time');
            const durationDisplay = document.getElementById('duration');
            const videoControls = document.getElementById('video-controls');
            const playOverlay = document.getElementById('play-overlay');
            const loadingAnimation = document.getElementById('loading-animation');
            
            // 视频加载完成时隐藏加载动画
            video.addEventListener('loadeddata', function() {
                loadingAnimation.style.display = 'none';
                // 设置总时长
                durationDisplay.textContent = formatTime(video.duration);
            });
            
            // 视频加载错误处理
            video.addEventListener('error', function() {
                console.error('视频加载失败:', video.error);
                loadingAnimation.style.display = 'none';
                alert('视频加载失败，请稍后重试');
            });
            
            // 播放/暂停控制
            function togglePlay() {
                if (video.paused) {
                    playVideo();
                } else {
                    pauseVideo();
                }
            }
            
            function playVideo() {
                if (video.readyState >= 2) {
                    video.play().catch(function(error) {
                        console.error('播放失败:', error);
                        alert('视频播放失败，请稍后重试');
                    });
                    playIcon.classList.remove('fa-play');
                    playIcon.classList.add('fa-pause');
                    playOverlay.style.display = 'none';
                } else {
                    loadingAnimation.style.display = 'block';
                    video.addEventListener('canplay', function onCanPlay() {
                        loadingAnimation.style.display = 'none';
                        video.play().catch(function(error) {
                            console.error('播放失败:', error);
                            alert('视频播放失败，请稍后重试');
                        });
                        video.removeEventListener('canplay', onCanPlay);
                    });
                }
            }
            
            function pauseVideo() {
                video.pause();
                playIcon.classList.remove('fa-pause');
                playIcon.classList.add('fa-play');
                playOverlay.style.display = 'flex';
            }
            
            // 更新进度条和时间显示
            video.addEventListener('timeupdate', function() {
                let progressPercent = (video.currentTime / video.duration) * 100;
                progressBar.style.width = `${progressPercent}%`;
                currentTimeDisplay.textContent = formatTime(video.currentTime);
            });
            
            // 视频结束时重置
            video.addEventListener('ended', function() {
                pauseVideo();
                video.currentTime = 0;
                progressBar.style.width = '0%';
                currentTimeDisplay.textContent = '00:00';
            });
            
            function formatTime(timeInSeconds) {
                const minutes = Math.floor(timeInSeconds / 60);
                const seconds = Math.floor(timeInSeconds % 60);
                return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            }
            
            // 点击播放按钮
            playButton.addEventListener('click', togglePlay);
            
            // 点击视频区域
            video.addEventListener('click', function() {
                // 切换控制条显示
                videoControls.classList.toggle('hidden');
            });
            
            // 点击覆盖层播放按钮
            playOverlay.addEventListener('click', playVideo);
            
            // 进度条点击跳转
            document.querySelector('.progress-container').addEventListener('click', function(e) {
                const progressContainer = this;
                const rect = progressContainer.getBoundingClientRect();
                const clickPosition = e.clientX - rect.left;
                const containerWidth = rect.width;
                
                // 计算点击位置占总宽度的比例
                const clickRatio = clickPosition / containerWidth;
                
                // 设置新的当前时间
                video.currentTime = video.duration * clickRatio;
            });
            
            // 重新播放按钮
            document.querySelector('.glass-button:first-child').addEventListener('click', function() {
                video.currentTime = 0;
                playVideo();
            });
            
            // 初始化时间显示
            currentTimeDisplay.textContent = '00:00';
        });
    </script>
</body>
</html> 