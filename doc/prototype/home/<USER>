<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <title>搭配详情 - 穿搭推荐小程序</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --color-text-primary: #333333;
            --color-text-secondary: #666666;
            --color-text-tertiary: #999999;
        }
        
        /* 隐藏滚动条 */
        ::-webkit-scrollbar {
            display: none;
        }
        
        * {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        
        body {
            overflow-y: scroll;
            -webkit-overflow-scrolling: touch;
        }

        /* 内容区域统一间距 */
        .content-container {
            padding-left: 5%;
            padding-right: 5%;
        }
        
        .gradient-background {
            background: linear-gradient(135deg, #c2e9fb 0%, #d8c6fd 100%);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: -1;
        }
        
        .enhanced-glass {
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.05);
        }
        
        .glass-button {
            background: rgba(255, 255, 255, 0.4);
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.5);
            transition: all 0.3s ease;
        }
        
        .glass-button:hover {
            background: rgba(255, 255, 255, 0.6);
            transform: translateY(-1px);
        }
        
        .glass-button:active {
            transform: scale(0.98);
        }
        
        .glass-button i {
            transition: transform 0.2s ease;
        }
        
        .glass-button.liked i {
            color: #ff5458;
        }
        
        .glass-button.favorited i {
            color: #ffd700;
        }
        
        /* 五行标识色彩 */
        .wuxing-jin {
            background: linear-gradient(135deg, #ffffff, #f0f0f0);
            color: #1a1a1a;
        }
        
        .wuxing-mu {
            background: linear-gradient(135deg, #a8e6cf, #73c1a8);
            color: #1a1a1a;
        }
        
        .wuxing-shui {
            background: linear-gradient(135deg, #b8c6db, #648dae);
            color: #ffffff;
        }
        
        .wuxing-huo {
            background: linear-gradient(135deg, #ff9a9e, #ff5458);
            color: #ffffff;
        }
        
        .wuxing-tu {
            background: linear-gradient(135deg, #ffeaa7, #ffc25c);
            color: #1a1a1a;
        }
        
        /* 评分星星 */
        .star-rating {
            color: rgba(255, 215, 0, 0.9);
        }
        
        /* 3D旋转效果 */
        .rotate-container {
            perspective: 1000px;
        }
        
        .rotate-item {
            transform-style: preserve-3d;
            transition: transform 0.5s;
        }
        
        /* 标签页切换 */
        .tab-active {
            color: var(--color-text-primary);
            border-bottom: 2px solid #73c1a8;
            font-weight: 500;
        }
        
        .tab-inactive {
            color: var(--color-text-secondary);
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        /* 收藏按钮动画 */
        @keyframes heart-pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.2); }
            100% { transform: scale(1); }
        }
        
        .heart-active {
            color: #ff5458;
            animation: heart-pulse 0.6s ease;
        }
        
        /* 视频弹窗样式 */
        .video-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            z-index: 50;
            display: none;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .video-modal.active {
            display: flex;
            opacity: 1;
        }
        
        .video-container {
            position: relative;
            width: 100%;
            max-width: 90%;
            margin: auto;
            background: #000;
            border-radius: 12px;
            overflow: hidden;
        }
        
        .video-controls {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(0, 0, 0, 0.4);
            backdrop-filter: blur(3px);
            padding: 8px 16px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            opacity: 1;
            transition: opacity 0.3s;
        }
        
        .video-controls.hidden {
            opacity: 0;
        }
        
        .play-button {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            cursor: pointer;
        }
        
        .progress-container {
            flex-grow: 1;
            margin: 0 10px;
            height: 4px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 2px;
            overflow: hidden;
            position: relative;
        }
        
        .progress-bar {
            height: 100%;
            background: #fff;
            width: 0;
        }
        
        .loading-animation {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-top-color: #fff;
            animation: spin 1s infinite linear;
        }
        
        @keyframes spin {
            to { transform: translate(-50%, -50%) rotate(360deg); }
        }

        /* 更换单品弹窗 */
        .item-replace-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            z-index: 50;
            display: none;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .item-replace-modal.active {
            display: flex;
            opacity: 1;
        }

        .item-replace-container {
            width: 100%;
            max-height: 80vh;
            margin: auto 0 0 0;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px 20px 0 0;
            overflow: hidden;
        }

        .alternative-item {
            transition: transform 0.2s ease;
        }

        .alternative-item:active {
            transform: scale(0.98);
        }

        .replace-button {
            width: 24px;
            height: 24px;
            border-radius: 12px;
            background: rgba(255, 255, 255, 0.9);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            transition: transform 0.2s ease;
        }

        .replace-button:active {
            transform: scale(0.95);
        }

        /* 来源标识样式 */
        .source-badge {
            position: absolute;
            left: 8px;
            top: 8px;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 10px;
            backdrop-filter: blur(4px);
        }

        .source-badge.wardrobe {
            background: rgba(168, 230, 207, 0.9);
            color: #1a1a1a;
        }

        .source-badge.recommended {
            background: rgba(255, 154, 158, 0.9);
            color: #ffffff;
        }

        /* 点赞、收藏、分享按钮样式 */
        .action-button {
            background: rgba(255, 255, 255, 0.4);
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.5);
            border-radius: 4px;
            padding: 2px 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
            font-size: 12px;
            transition: all 0.3s ease;
        }

        .action-button:hover {
            background: rgba(255, 255, 255, 0.6);
        }

        .action-button i {
            font-size: 14px;
        }

        .action-button.liked i {
            color: #ff5458;
        }

        .action-button.favorited i {
            color: #ffd700;
        }

        .action-button.shared i {
            color: #38BDF8;
        }

        /* 按钮激活状态样式 */
        button.active i.fa-heart {
            color: #ff5458 !important;
        }
        
        button.active i.fa-star {
            color: #ffd700 !important;
        }
        
        /* 按钮动画效果 */
        button i {
            transition: transform 0.2s ease, color 0.2s ease;
        }
        
        button:active {
            transform: scale(0.95);
        }
    </style>
</head>
<body>
    <!-- 渐变背景 -->
    <div class="gradient-background"></div>

    <!-- 顶部导航 -->
    <div class="sticky top-0 bg-white/30 backdrop-blur-md z-40 shadow-sm">
        <div class="flex items-center p-4">
            <button onclick="history.back()" class="mr-4">
                <i class="fas fa-arrow-left text-[var(--color-text-primary)]"></i>
            </button>
            <h1 class="text-sm font-semibold text-[var(--color-text-primary)]">清新白裙搭配</h1>
        </div>
    </div>

    <!-- 主内容区 -->
    <div class="content-container py-4 pb-20">
        <!-- 搭配图片展示 -->
        <div class="enhanced-glass rounded-xl overflow-hidden mb-4">
            <div class="rotate-container w-full relative">
                <div class="rotate-item w-full">
                    <img src="https://images.unsplash.com/photo-1515886657613-9f3515b0c78f" 
                         class="w-full aspect-[4/5] object-cover" alt="搭配整体效果" id="outfit-image">
                    
                    <!-- 评分悬浮组件 -->
                    <div class="absolute top-4 right-4 bg-black/40 backdrop-blur-sm rounded-lg px-3 py-2 flex items-center">
                <div class="star-rating flex mr-1">
                    <i class="fas fa-star text-xs"></i>
                    <i class="fas fa-star text-xs"></i>
                    <i class="fas fa-star text-xs"></i>
                    <i class="fas fa-star text-xs"></i>
                    <i class="fas fa-star-half-alt text-xs"></i>
                </div>
                        <span class="text-xs text-white">4.5</span>
            </div>
                </div>
                
                <!-- 控制按钮 -->
                <div class="absolute left-4 right-4 bottom-4 flex items-end justify-between">
                    <button class="w-10 h-10 rounded-full bg-white/40 backdrop-blur-sm flex items-center justify-center" id="play-video-btn">
                        <i class="fas fa-play text-[var(--color-text-primary)]"></i>
                    </button>
                    
                    <div class="flex items-end space-x-1.5">
                        <button class="text-xs flex items-center glass-button px-2 py-0.5 rounded-full min-w-[52px] justify-center" onclick="toggleLike(this)">
                            <i class="far fa-heart text-pink-400 mr-1"></i>
                            <span class="text-[var(--color-text-primary)]">24</span>
                        </button>
                        <button class="text-xs flex items-center glass-button px-2 py-0.5 rounded-full min-w-[52px] justify-center" onclick="toggleFavorite(this)">
                            <i class="far fa-star text-amber-400 mr-1"></i>
                            <span class="text-[var(--color-text-primary)]">16</span>
                        </button>
                        <button class="text-xs flex items-center glass-button px-2 py-0.5 rounded-full min-w-[52px] justify-center" onclick="shareOutfit()">
                            <i class="fas fa-share-alt text-sky-400 mr-1"></i>
                            <span class="text-[var(--color-text-primary)]">分享</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 搭配推荐理由和使用场合 -->
        <div class="enhanced-glass rounded-xl p-4 mb-4">
            <div class="space-y-3">
                <!-- 搭配推荐理由 -->
                <div>
                    <h3 class="text-xs font-medium text-[var(--color-text-primary)] mb-2">搭配推荐理由</h3>
                    <p class="text-xs text-[var(--color-text-secondary)]">
                        这套穿搭以清爽的白色为基调，简约大气，适合春夏季节。蓝色包包作为点缀，为整体造型增添亮点，同时补充了水行能量。米色高跟鞋中和了整体偏冷的色调，带来温暖质感。
                    </p>
                </div>

                <!-- 使用场合 -->
                <div class="pt-3 border-t border-white/20">
                    <h3 class="text-xs font-medium text-[var(--color-text-primary)] mb-2">推荐场合</h3>
                    <div class="flex flex-wrap gap-2">
                        <span class="text-[10px] px-3 py-1 rounded-full bg-white/30 text-[var(--color-text-secondary)]">
                            <i class="fas fa-building mr-1"></i>商务会议
                        </span>
                        <span class="text-[10px] px-3 py-1 rounded-full bg-white/30 text-[var(--color-text-secondary)]">
                            <i class="fas fa-briefcase mr-1"></i>职场通勤
                        </span>
                        <span class="text-[10px] px-3 py-1 rounded-full bg-white/30 text-[var(--color-text-secondary)]">
                            <i class="fas fa-handshake mr-1"></i>客户洽谈
                        </span>
                        <span class="text-[10px] px-3 py-1 rounded-full bg-white/30 text-[var(--color-text-secondary)]">
                            <i class="fas fa-utensils mr-1"></i>商务午餐
                        </span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 标签页导航 -->
        <div class="flex border-b border-white/30 mb-4">
            <button class="flex-1 py-2 text-xs tab-active" data-tab="tab1">搭配单品</button>
            <button class="flex-1 py-2 text-xs tab-inactive" data-tab="tab2">五行解读</button>
        </div>
        
        <!-- 标签页内容 -->
        <div id="tab1" class="tab-content active">
            <!-- 搭配单品列表 -->
            <div class="space-y-3 mb-4">
                <!-- 上衣 -->
                <div class="enhanced-glass rounded-xl overflow-hidden">
                    <div class="flex">
                        <div class="w-1/3 relative">
                            <img src="https://images.unsplash.com/photo-1597843664423-e547e596dee4" 
                                 class="w-full aspect-square object-cover" alt="白色衬衫">
                            <span class="text-[10px] px-2 py-0.5 rounded-full wuxing-jin absolute top-2 right-2 flex items-center">
                                <i class="fas fa-coins text-[8px] mr-1"></i>金
                            </span>
                        </div>
                        <div class="w-2/3 p-3">
                            <div class="flex justify-between items-start">
                                <div>
                                    <h3 class="text-xs font-medium text-[var(--color-text-primary)]">白色简约衬衫</h3>
                                    <p class="text-[10px] text-[var(--color-text-secondary)] mt-1">
                                        轻薄透气棉质面料，极简风格，适合多种场合搭配
                                    </p>
                                </div>
                                <button class="replace-button" data-item-type="shirt">
                                    <i class="fas fa-random text-xs text-[var(--color-text-primary)]"></i>
                                </button>
                            </div>
                            
                            <!-- 标签 -->
                            <div class="flex flex-wrap gap-1 mt-2">
                                <span class="text-[8px] px-1.5 py-0.5 rounded-full bg-white/40 text-[var(--color-text-secondary)]">
                                    清爽
                                </span>
                                <span class="text-[8px] px-1.5 py-0.5 rounded-full bg-white/40 text-[var(--color-text-secondary)]">
                                    百搭
                                </span>
                                <span class="text-[8px] px-1.5 py-0.5 rounded-full bg-white/40 text-[var(--color-text-secondary)]">
                                    职场
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 裙子 -->
                <div class="enhanced-glass rounded-xl overflow-hidden">
                    <div class="flex">
                        <div class="w-1/3 relative">
                            <img src="https://images.unsplash.com/photo-1577900232427-18219b9166a0" 
                                 class="w-full aspect-square object-cover" alt="白色A字裙">
                            <span class="text-[10px] px-2 py-0.5 rounded-full wuxing-jin absolute top-2 right-2 flex items-center">
                                <i class="fas fa-coins text-[8px] mr-1"></i>金
                            </span>
                        </div>
                        <div class="w-2/3 p-3">
                            <div class="flex justify-between items-start">
                                <div>
                                    <h3 class="text-xs font-medium text-[var(--color-text-primary)]">白色A字半身裙</h3>
                                    <p class="text-[10px] text-[var(--color-text-secondary)] mt-1">
                                        优雅流畅线条，高腰设计，修饰身材比例
                                    </p>
                                </div>
                                <button class="replace-button" data-item-type="skirt">
                                    <i class="fas fa-random text-xs text-[var(--color-text-primary)]"></i>
                                </button>
                            </div>
                            
                            <!-- 标签 -->
                            <div class="flex flex-wrap gap-1 mt-2">
                                <span class="text-[8px] px-1.5 py-0.5 rounded-full bg-white/40 text-[var(--color-text-secondary)]">
                                    优雅
                                </span>
                                <span class="text-[8px] px-1.5 py-0.5 rounded-full bg-white/40 text-[var(--color-text-secondary)]">
                                    简约
                                </span>
                                <span class="text-[8px] px-1.5 py-0.5 rounded-full bg-white/40 text-[var(--color-text-secondary)]">
                                    通勤
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 鞋子 -->
                <div class="enhanced-glass rounded-xl overflow-hidden">
                    <div class="flex">
                        <div class="w-1/3 relative">
                            <img src="https://images.unsplash.com/photo-1543163521-1bf539c55dd2" 
                                 class="w-full aspect-square object-cover" alt="米色高跟鞋">
                            <span class="text-[10px] px-2 py-0.5 rounded-full wuxing-tu absolute top-2 right-2 flex items-center">
                                <i class="fas fa-mountain text-[8px] mr-1"></i>土
                            </span>
                        </div>
                        <div class="w-2/3 p-3">
                            <div class="flex justify-between items-start">
                                <div>
                                    <h3 class="text-xs font-medium text-[var(--color-text-primary)]">米色尖头高跟鞋</h3>
                                    <p class="text-[10px] text-[var(--color-text-secondary)] mt-1">
                                        中跟设计，舒适修饰腿部线条，正式场合首选
                                    </p>
                                </div>
                                <button class="replace-button" data-item-type="shoes">
                                    <i class="fas fa-random text-xs text-[var(--color-text-primary)]"></i>
                                </button>
                            </div>
                            
                            <!-- 标签 -->
                            <div class="flex flex-wrap gap-1 mt-2">
                                <span class="text-[8px] px-1.5 py-0.5 rounded-full bg-white/40 text-[var(--color-text-secondary)]">
                                    气质
                                </span>
                                <span class="text-[8px] px-1.5 py-0.5 rounded-full bg-white/40 text-[var(--color-text-secondary)]">
                                    正式
                                </span>
                                <span class="text-[8px] px-1.5 py-0.5 rounded-full bg-white/40 text-[var(--color-text-secondary)]">
                                    舒适
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 包包 -->
                <div class="enhanced-glass rounded-xl overflow-hidden">
                    <div class="flex">
                        <div class="w-1/3 relative">
                            <img src="https://images.unsplash.com/photo-1584917865442-de89df76afd3" 
                                 class="w-full aspect-square object-cover" alt="蓝色手提包">
                            <span class="text-[10px] px-2 py-0.5 rounded-full wuxing-shui absolute top-2 right-2 flex items-center">
                                <i class="fas fa-water text-[8px] mr-1"></i>水
                            </span>
                        </div>
                        <div class="w-2/3 p-3">
                            <div class="flex justify-between items-start">
                                <div>
                                    <h3 class="text-xs font-medium text-[var(--color-text-primary)]">蓝色简约手提包</h3>
                                    <p class="text-[10px] text-[var(--color-text-secondary)] mt-1">
                                        结构感设计，色彩点缀整体造型，实用美观兼具
                                    </p>
                                </div>
                                <button class="replace-button" data-item-type="bag">
                                    <i class="fas fa-random text-xs text-[var(--color-text-primary)]"></i>
                                </button>
                            </div>
                            
                            <!-- 标签 -->
                            <div class="flex flex-wrap gap-1 mt-2">
                                <span class="text-[8px] px-1.5 py-0.5 rounded-full bg-white/40 text-[var(--color-text-secondary)]">
                                    通勤
                                </span>
                                <span class="text-[8px] px-1.5 py-0.5 rounded-full bg-white/40 text-[var(--color-text-secondary)]">
                                    实用
                                </span>
                                <span class="text-[8px] px-1.5 py-0.5 rounded-full bg-white/40 text-[var(--color-text-secondary)]">
                                    亮点
                                </span>
                        </div>
                    </div>
                </div>
            </div>
            
                <!-- 饰品 -->
                <div class="enhanced-glass rounded-xl overflow-hidden">
                    <div class="flex">
                        <div class="w-1/3 relative">
                            <img src="https://images.unsplash.com/photo-1599643478518-a784e5dc4c8f" 
                                 class="w-full aspect-square object-cover" alt="珍珠项链">
                            <span class="text-[10px] px-2 py-0.5 rounded-full wuxing-jin absolute top-2 right-2 flex items-center">
                                <i class="fas fa-coins text-[8px] mr-1"></i>金
                            </span>
                        </div>
                        <div class="w-2/3 p-3">
                            <div class="flex justify-between items-start">
                                <div>
                                    <h3 class="text-xs font-medium text-[var(--color-text-primary)]">优雅珍珠项链</h3>
                                    <p class="text-[10px] text-[var(--color-text-secondary)] mt-1">
                                        天然珍珠材质，简约大方，提升整体气质
                                    </p>
                                </div>
                                <button class="replace-button" data-item-type="accessory">
                                    <i class="fas fa-random text-xs text-[var(--color-text-primary)]"></i>
                                </button>
                            </div>
                            
                            <!-- 标签 -->
                            <div class="flex flex-wrap gap-1 mt-2">
                                <span class="text-[8px] px-1.5 py-0.5 rounded-full bg-white/40 text-[var(--color-text-secondary)]">
                                    优雅
                                </span>
                                <span class="text-[8px] px-1.5 py-0.5 rounded-full bg-white/40 text-[var(--color-text-secondary)]">
                                    百搭
                                </span>
                                <span class="text-[8px] px-1.5 py-0.5 rounded-full bg-white/40 text-[var(--color-text-secondary)]">
                                    气质
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div id="tab2" class="tab-content">
            <!-- 五行分析 -->
            <div class="enhanced-glass rounded-xl p-4 mb-4">
                <h3 class="text-sm font-medium text-[var(--color-text-primary)] mb-4 flex items-center">
                    <i class="fas fa-yin-yang mr-2 text-purple-400"></i>
                    五行能量解读
                </h3>
                
                <!-- 五行分布图 -->
                <div class="mb-6">
                    <div class="relative w-full max-w-xs mx-auto">
                        <!-- 五行能量条 -->
                        <div class="flex h-3 rounded-full overflow-hidden mb-4">
                            <div class="w-[40%] wuxing-jin"></div>
                            <div class="w-[10%] wuxing-mu"></div>
                            <div class="w-[20%] wuxing-shui"></div>
                            <div class="w-[0%] wuxing-huo"></div>
                            <div class="w-[30%] wuxing-tu"></div>
                        </div>
                        
                        <!-- 五行标签 -->
                        <div class="flex justify-between text-[10px] text-[var(--color-text-secondary)]">
                            <div class="text-center">
                                <span class="inline-block w-4 h-4 rounded-full wuxing-jin mb-1"></span>
                                <p>金(40%)</p>
                            </div>
                            <div class="text-center">
                                <span class="inline-block w-4 h-4 rounded-full wuxing-mu mb-1"></span>
                                <p>木(10%)</p>
                            </div>
                            <div class="text-center">
                                <span class="inline-block w-4 h-4 rounded-full wuxing-shui mb-1"></span>
                                <p>水(20%)</p>
                            </div>
                            <div class="text-center">
                                <span class="inline-block w-4 h-4 rounded-full wuxing-huo mb-1"></span>
                                <p>火(0%)</p>
                            </div>
                            <div class="text-center">
                                <span class="inline-block w-4 h-4 rounded-full wuxing-tu mb-1"></span>
                                <p>土(30%)</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 整体解读 -->
                <div class="mb-6 p-3 rounded-lg bg-white/20">
                    <p class="text-xs leading-relaxed text-[var(--color-text-primary)]">
                        <i class="fas fa-circle-info text-purple-400 mr-2"></i>
                        此搭配以金(白色系)为主，辅以土(米色)和水(蓝色)，形成金生水、土生金的良性循环，整体能量和谐流畅。适合性格稳重，需要提升沟通能力和思维敏捷度的场合。
                    </p>
                </div>
                
                <!-- 各元素解析 -->
                <div class="space-y-4">
                    <!-- 金 -->
                    <div class="p-3 rounded-lg wuxing-jin">
                        <div class="flex items-center mb-2">
                            <i class="fas fa-coins mr-2"></i>
                            <span class="text-xs font-medium">金 (40%)</span>
                        </div>
                        <p class="text-xs leading-relaxed">
                            白色调衬衫和裙子，带来清爽利落的气质，提升个人形象与专业感。
                        </p>
                    </div>
                    
                    <!-- 土 -->
                    <div class="p-3 rounded-lg wuxing-tu">
                        <div class="flex items-center mb-2">
                            <i class="fas fa-mountain mr-2"></i>
                            <span class="text-xs font-medium">土 (30%)</span>
                        </div>
                        <p class="text-xs leading-relaxed">
                            米色高跟鞋提供稳定、温暖的能量，平衡整体偏冷的色调，增强踏实感。
                        </p>
                    </div>
                    
                    <!-- 水 -->
                    <div class="p-3 rounded-lg wuxing-shui">
                        <div class="flex items-center mb-2">
                            <i class="fas fa-water mr-2"></i>
                            <span class="text-xs font-medium">水 (20%)</span>
                        </div>
                        <p class="text-xs leading-relaxed">
                            蓝色手提包点缀，提升智慧与沟通能力，增添灵活和适应能力。
                        </p>
                    </div>
                    
                    <!-- 木 -->
                    <div class="p-3 rounded-lg wuxing-mu">
                        <div class="flex items-center mb-2">
                            <i class="fas fa-tree mr-2"></i>
                            <span class="text-xs font-medium">木 (10%)</span>
                        </div>
                        <p class="text-xs leading-relaxed">
                            整体设计中的流畅线条，带来成长能量，促进个人发展和创新思维。
                        </p>
                    </div>
                </div>
            </div>
            
            <!-- 能量提升建议 -->
            <div class="enhanced-glass rounded-xl p-4 mb-4">
                <h3 class="text-sm font-medium text-[var(--color-text-primary)] mb-4 flex items-center">
                    <i class="fas fa-wand-sparkles mr-2 text-amber-400"></i>
                    能量提升建议
                </h3>
                
                <div class="space-y-3">
                    <p class="text-xs leading-relaxed text-[var(--color-text-primary)]">
                        如需进一步优化五行平衡，可考虑添加以下元素：
                    </p>
                    
                    <!-- 木属性建议 -->
                    <div class="p-3 rounded-lg bg-white/20">
                        <div class="flex items-center text-xs text-[var(--color-text-primary)]">
                            <span class="w-4 h-4 rounded-full wuxing-mu flex items-center justify-center mr-2">
                                <i class="fas fa-tree text-[8px]"></i>
                            </span>
                            搭配绿色或青色小饰品，增添生机与活力
                        </div>
                    </div>
                    
                    <!-- 火属性建议 -->
                    <div class="p-3 rounded-lg bg-white/20">
                        <div class="flex items-center text-xs text-[var(--color-text-primary)]">
                            <span class="w-4 h-4 rounded-full wuxing-huo flex items-center justify-center mr-2">
                                <i class="fas fa-fire text-[8px]"></i>
                            </span>
                            添加暖红色或粉色唇妆，提升个人魅力
                        </div>
                    </div>
                    
                    <!-- 混合属性建议 -->
                    <div class="p-3 rounded-lg bg-white/20">
                        <div class="flex items-center text-xs text-[var(--color-text-primary)]">
                            <span class="w-4 h-4 rounded-full bg-gradient-to-r from-[#a8e6cf] to-[#ff5458] flex items-center justify-center mr-2">
                                <i class="fas fa-gem text-[8px] text-white"></i>
                            </span>
                            佩戴木质或红色系首饰，平衡整体能量
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 视频播放弹窗 -->
    <div class="video-modal" id="video-modal">
        <div class="video-container aspect-[9/16]">
            <!-- 关闭按钮 -->
            <button class="absolute top-4 right-4 z-10 w-8 h-8 rounded-full bg-black/40 backdrop-blur-sm flex items-center justify-center text-white" id="close-video">
                <i class="fas fa-times"></i>
            </button>
            
            <!-- 视频元素 -->
            <video id="outfit-video" class="w-full h-full object-cover" poster="https://images.unsplash.com/photo-1515886657613-9f3515b0c78f" preload="auto" playsinline>
                <source src="../assets/videos/9e0ad5bb344f4821b10a9c168b4bcb28.mp4" type="video/mp4">
                您的浏览器不支持视频播放
            </video>
            
            <!-- 加载动画 -->
            <div class="loading-animation" id="loading-animation"></div>
            
            <!-- 播放按钮覆盖层 -->
            <div class="absolute inset-0 flex items-center justify-center bg-black/30" id="play-overlay">
                <div class="w-16 h-16 rounded-full bg-white/30 backdrop-blur-sm flex items-center justify-center">
                    <i class="fas fa-play text-white text-xl"></i>
                </div>
            </div>
            
            <!-- 控制条 -->
            <div class="video-controls" id="video-controls">
                <div class="play-button" id="play-button">
                    <i class="fas fa-play" id="play-icon"></i>
                </div>
                
                <div class="progress-container">
                    <div class="progress-bar" id="progress-bar"></div>
                </div>
                
                <div class="time text-white text-xs">
                    <span id="current-time">00:00</span>
                    <span>/</span>
                    <span id="duration">00:00</span>
                </div>
            </div>
        </div>
    </div>

    <!-- 更换单品弹窗 -->
    <div class="item-replace-modal" id="replace-modal">
        <div class="item-replace-container">
            <!-- 弹窗头部 -->
            <div class="p-4 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h3 class="text-sm font-medium text-[var(--color-text-primary)]">更换单品</h3>
                    <button class="w-8 h-8 flex items-center justify-center" id="close-replace-modal">
                        <i class="fas fa-times text-[var(--color-text-primary)]"></i>
                    </button>
                </div>
                <p class="text-xs text-[var(--color-text-secondary)] mt-1">选择一个替代品来更新搭配</p>
            </div>
            
            <!-- 替代品列表 -->
            <div class="p-4 overflow-y-auto max-h-[60vh]">
                <div class="grid grid-cols-3 gap-2" id="alternatives-container">
                    <!-- 动态生成的替代品 -->
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 标签页切换
            const tabButtons = document.querySelectorAll('[data-tab]');
            tabButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // 更新标签样式
                    tabButtons.forEach(btn => {
                        btn.classList.remove('tab-active');
                        btn.classList.add('tab-inactive');
                    });
                    this.classList.add('tab-active');
                    this.classList.remove('tab-inactive');
                    
                    // 显示对应内容
                    const tabId = this.getAttribute('data-tab');
                    document.querySelectorAll('.tab-content').forEach(content => {
                        content.classList.remove('active');
                    });
                    document.getElementById(tabId).classList.add('active');
                });
            });
            
            // 3D旋转效果
            let rotateX = 0;
            let rotateY = 0;
            const rotateItem = document.querySelector('.rotate-item');
            const outfitImage = document.getElementById('outfit-image');
            
            // 触摸滑动旋转
            let startX, startY;
            outfitImage.addEventListener('touchstart', function(e) {
                startX = e.touches[0].clientX;
                startY = e.touches[0].clientY;
            });
            
            outfitImage.addEventListener('touchmove', function(e) {
                if (!startX || !startY) return;
                
                const deltaX = e.touches[0].clientX - startX;
                const deltaY = e.touches[0].clientY - startY;
                
                rotateY += deltaX * 0.2;
                rotateX -= deltaY * 0.2;
                
                rotateX = Math.max(-20, Math.min(20, rotateX));
                rotateY = Math.max(-20, Math.min(20, rotateY));
                
                updateRotation();
                
                startX = e.touches[0].clientX;
                startY = e.touches[0].clientY;
            });
            
            outfitImage.addEventListener('touchend', function() {
                startX = null;
                startY = null;
                
                // 缓慢恢复到原位
                resetRotation();
            });
            
        
            
            function updateRotation() {
                rotateItem.style.transform = `rotateX(${rotateX}deg) rotateY(${rotateY}deg)`;
            }
            
            function resetRotation() {
                // 动画恢复原位
                rotateItem.style.transition = 'transform 0.5s ease-out';
                rotateX = 0;
                rotateY = 0;
                updateRotation();
                
                setTimeout(() => {
                    rotateItem.style.transition = 'transform 0.1s';
                }, 500);
            }
            
            // 视频相关元素
            const videoModal = document.getElementById('video-modal');
            const video = document.getElementById('outfit-video');
            const playButton = document.getElementById('play-button');
            const playIcon = document.getElementById('play-icon');
            const progressBar = document.getElementById('progress-bar');
            const currentTimeDisplay = document.getElementById('current-time');
            const durationDisplay = document.getElementById('duration');
            const videoControls = document.getElementById('video-controls');
            const playOverlay = document.getElementById('play-overlay');
            const loadingAnimation = document.getElementById('loading-animation');
            const closeVideo = document.getElementById('close-video');
            const playVideoBtn = document.getElementById('play-video-btn');
            
            // 检查视频相关元素是否存在
            if (playVideoBtn && videoModal && video) {
            // 打开视频弹窗
                playVideoBtn.addEventListener('click', function() {
                videoModal.classList.add('active');
                // 自动开始播放
                playVideo();
            });
            }
            
            // 检查关闭按钮是否存在
            if (closeVideo) {
            closeVideo.addEventListener('click', function() {
                videoModal.classList.remove('active');
                pauseVideo();
                video.currentTime = 0;
            });
            }
            
            // 视频播放控制
            if (video) {
            // 视频加载完成时隐藏加载动画
            video.addEventListener('loadeddata', function() {
                    if (loadingAnimation) {
                loadingAnimation.style.display = 'none';
                    }
                    if (durationDisplay) {
                durationDisplay.textContent = formatTime(video.duration);
                    }
            });
            
            // 视频加载错误处理
            video.addEventListener('error', function() {
                console.error('视频加载失败:', video.error);
                    if (loadingAnimation) {
                loadingAnimation.style.display = 'none';
                    }
                alert('视频加载失败，请稍后重试');
            });

                // 更新进度条和时间显示
                video.addEventListener('timeupdate', function() {
                    if (progressBar && currentTimeDisplay) {
                        let progressPercent = (video.currentTime / video.duration) * 100;
                        progressBar.style.width = `${progressPercent}%`;
                        currentTimeDisplay.textContent = formatTime(video.currentTime);
                    }
                });

                // 视频结束时重置
                video.addEventListener('ended', function() {
                    pauseVideo();
                    video.currentTime = 0;
                    if (progressBar) {
                        progressBar.style.width = '0%';
                    }
                    if (currentTimeDisplay) {
                        currentTimeDisplay.textContent = '00:00';
                    }
                });

                // 点击视频区域
                video.addEventListener('click', function() {
                    if (videoControls) {
                        videoControls.classList.toggle('hidden');
                    }
                });
            }

            // 播放按钮点击事件
            if (playButton) {
                playButton.addEventListener('click', togglePlay);
            }

            // 覆盖层点击事件
            if (playOverlay) {
                playOverlay.addEventListener('click', playVideo);
            }

            // 进度条点击事件
            const progressContainer = document.querySelector('.progress-container');
            if (progressContainer) {
                progressContainer.addEventListener('click', function(e) {
                    const rect = this.getBoundingClientRect();
                    const clickPosition = e.clientX - rect.left;
                    const containerWidth = rect.width;
                    
                    // 计算点击位置占总宽度的比例
                    const clickRatio = clickPosition / containerWidth;
                    
                    // 设置新的当前时间
                    if (video) {
                        video.currentTime = video.duration * clickRatio;
                    }
                });
            }

            // 更换单品相关功能
            const replaceModal = document.getElementById('replace-modal');
            const alternativesContainer = document.getElementById('alternatives-container');
            const closeReplaceModalBtn = document.getElementById('close-replace-modal');
            let currentItemType = '';

            // 检查替换按钮是否存在
            const replaceButtons = document.querySelectorAll('.replace-button');
            if (replaceButtons.length > 0) {
                replaceButtons.forEach(button => {
                    button.addEventListener('click', function() {
                        const itemType = this.getAttribute('data-item-type');
                        openReplaceModal(itemType);
                    });
                });
            }

            // 检查关闭按钮是否存在
            if (closeReplaceModalBtn) {
                closeReplaceModalBtn.addEventListener('click', closeReplaceModal);
            }

            // 检查弹窗是否存在
            if (replaceModal) {
                replaceModal.addEventListener('click', (e) => {
                    if (e.target === replaceModal) {
                        closeReplaceModal();
                    }
                });
            }
            
            function playVideo() {
                if (!video) return;
                
                if (video.readyState >= 2) {
                    video.play().catch(function(error) {
                        console.error('播放失败:', error);
                        alert('视频播放失败，请稍后重试');
                    });
                    if (playIcon) {
                    playIcon.classList.remove('fa-play');
                    playIcon.classList.add('fa-pause');
                    }
                    if (playOverlay) {
                    playOverlay.style.display = 'none';
                    }
                } else {
                    if (loadingAnimation) {
                    loadingAnimation.style.display = 'block';
                    }
                    video.addEventListener('canplay', function onCanPlay() {
                        if (loadingAnimation) {
                        loadingAnimation.style.display = 'none';
                        }
                        video.play().catch(function(error) {
                            console.error('播放失败:', error);
                            alert('视频播放失败，请稍后重试');
                        });
                        video.removeEventListener('canplay', onCanPlay);
                    });
                }
            }
            
            function pauseVideo() {
                if (!video) return;
                
                video.pause();
                if (playIcon) {
                playIcon.classList.remove('fa-pause');
                playIcon.classList.add('fa-play');
                }
                if (playOverlay) {
                playOverlay.style.display = 'flex';
                }
            }
            
            function togglePlay() {
                if (!video) return;
                
                if (video.paused) {
                    playVideo();
                } else {
                    pauseVideo();
                }
            }
            
            function formatTime(timeInSeconds) {
                const minutes = Math.floor(timeInSeconds / 60);
                const seconds = Math.floor(timeInSeconds % 60);
                return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            }
            
            // 替代品数据
            const alternatives = {
                shirt: [
                    { 
                        image: 'https://images.unsplash.com/photo-1598961942613-ba897716405b',
                        title: '白色泡泡袖衬衫',
                        description: '清新甜美风格，适合各种场合',
                        element: '金',
                        tags: ['甜美', '清新', '百搭'],
                        source: 'wardrobe',
                        score: 95
                    },
                    { 
                        image: 'https://images.unsplash.com/photo-1604695573706-53170668f6a6',
                        title: '米白色真丝衬衫',
                        description: '高级真丝面料，垂坠感极佳',
                        element: '金',
                        tags: ['高级', '优雅', '职场'],
                        source: 'wardrobe',
                        score: 88
                    },
                    {
                        image: 'https://images.unsplash.com/photo-1608234807905-4466023792f5',
                        title: '白色蕾丝衬衫',
                        description: '精致蕾丝点缀，优雅浪漫',
                        element: '金',
                        tags: ['浪漫', '精致', '约会'],
                        source: 'recommended',
                        score: 92
                    }
                ],
                skirt: [
                    {
                        image: 'https://images.unsplash.com/photo-1583496661160-fb5886a0aaaa',
                        title: '白色百褶裙',
                        description: '轻盈飘逸，活力减龄',
                        element: '金',
                        tags: ['活力', '减龄', '百搭'],
                        source: 'wardrobe',
                        score: 94
                    },
                    {
                        image: 'https://images.unsplash.com/photo-1551163943-3f6a855d1153',
                        title: '米色褶皱半身裙',
                        description: '立体褶皱设计，优雅大方',
                        element: '土',
                        tags: ['优雅', '知性', '职场'],
                        source: 'wardrobe',
                        score: 86
                    },
                    {
                        image: 'https://images.unsplash.com/photo-1594633312681-425c7b97ccd1',
                        title: '珍珠白蓬蓬裙',
                        description: '轻奢珍珠装饰，约会首选',
                        element: '金',
                        tags: ['轻奢', '约会', '优雅'],
                        source: 'recommended',
                        score: 96
                    }
                ],
                shoes: [
                    {
                        image: 'https://images.unsplash.com/photo-1543163521-1bf539c55dd2',
                        title: '米色尖头高跟鞋',
                        description: '经典款式，百搭实用',
                        element: '土',
                        tags: ['经典', '百搭', '职场'],
                        source: 'wardrobe',
                        score: 90
                    },
                    {
                        image: 'https://images.unsplash.com/photo-1573100925118-870b8efc799d',
                        title: '裸色方头中跟鞋',
                        description: '舒适优雅，日常通勤首选',
                        element: '土',
                        tags: ['舒适', '通勤', '百搭'],
                        source: 'wardrobe',
                        score: 85
                    },
                    {
                        image: 'https://images.unsplash.com/photo-1563341591-ad0a750911cf',
                        title: '珍珠装饰高跟鞋',
                        description: '精致珍珠点缀，约会必备',
                        element: '金',
                        tags: ['精致', '约会', '优雅'],
                        source: 'recommended',
                        score: 93
                    }
                ],
                bag: [
                    {
                        image: 'https://images.unsplash.com/photo-1584917865442-de89df76afd3',
                        title: '蓝色手提包',
                        description: '简约大方，实用百搭',
                        element: '水',
                        tags: ['简约', '百搭', '通勤'],
                        source: 'wardrobe',
                        score: 88
                    },
                    {
                        image: 'https://images.unsplash.com/photo-1591561954557-26941169b49e',
                        title: '天蓝色链条包',
                        description: '优雅小巧，约会逛街必备',
                        element: '水',
                        tags: ['优雅', '约会', '休闲'],
                        source: 'wardrobe',
                        score: 92
                    },
                    {
                        image: 'https://images.unsplash.com/photo-1628149455678-16f37bc392f4',
                        title: '湖蓝色珍珠包',
                        description: '珍珠链条设计，优雅精致',
                        element: '水',
                        tags: ['精致', '约会', '轻奢'],
                        source: 'recommended',
                        score: 95
                    }
                ],
                accessory: [
                    {
                        image: 'https://images.unsplash.com/photo-1599643478518-a784e5dc4c8f',
                        title: '优雅珍珠项链',
                        description: '天然珍珠材质，简约大方，提升整体气质',
                        element: '金',
                        tags: ['优雅', '百搭', '气质'],
                        source: 'wardrobe',
                        score: 94
                    },
                    {
                        image: 'https://images.unsplash.com/photo-1602752275349-a1608167d9b2',
                        title: '金色细链项链',
                        description: '18K金链条，简约时尚，适合日常佩戴',
                        element: '金',
                        tags: ['简约', '通勤', '百搭'],
                        source: 'wardrobe',
                        score: 86
                    },
                    {
                        image: 'https://images.unsplash.com/photo-1635767798638-3665c671c0bb',
                        title: '水晶蝴蝶项链',
                        description: '精致水晶吊坠，浪漫甜美，约会必备',
                        element: '水',
                        tags: ['浪漫', '约会', '甜美'],
                        source: 'recommended',
                        score: 91
                    }
                ]
            };

            function openReplaceModal(itemType) {
                currentItemType = itemType;
                replaceModal.classList.add('active');
                document.body.style.overflow = 'hidden';
                
                // 清空并重新生成替代品列表
                alternativesContainer.innerHTML = '';
                
                // 获取当前类型的替代品并按推荐值排序
                const items = alternatives[itemType] || [];
                items.sort((a, b) => b.score - a.score);
                
                // 生成替代品卡片
                items.forEach(item => {
                    const itemCard = document.createElement('div');
                    itemCard.className = 'alternative-item enhanced-glass rounded-xl overflow-hidden';
                    itemCard.innerHTML = `
                        <div class="relative">
                            <img src="${item.image}" class="w-full aspect-square object-cover" alt="${item.title}">
                            <span class="absolute top-2 right-2 text-[8px] px-1.5 py-0.5 rounded-full wuxing-${getWuxingClass(item.element)} flex items-center">
                                <i class="fas ${getWuxingIcon(item.element)} text-[6px] mr-1"></i>${item.element}
                            </span>
                            <span class="source-badge ${item.source === 'wardrobe' ? 'wardrobe' : 'recommended'}">
                                ${item.source === 'wardrobe' ? '衣橱' : '推荐'}
                            </span>
                            <span class="absolute bottom-2 right-2 text-[8px] px-1.5 py-0.5 rounded-full bg-black/60 text-white">
                                推荐度 ${item.score}
                            </span>
                        </div>
                        <div class="p-2">
                            <h4 class="text-[10px] font-medium text-[var(--color-text-primary)]">${item.title}</h4>
                            <p class="text-[8px] text-[var(--color-text-secondary)] mt-0.5">${item.description}</p>
                            <div class="flex flex-wrap gap-0.5 mt-1">
                                ${item.tags.map(tag => `
                                    <span class="text-[6px] px-1 py-0.5 rounded-full bg-white/40 text-[var(--color-text-secondary)]">
                                        ${tag}
                                    </span>
                                `).join('')}
                            </div>
                        </div>
                    `;
                    
                    // 点击替换
                    itemCard.addEventListener('click', () => {
                        replaceItem(itemType, item);
                        closeReplaceModal();
                    });
                    
                    alternativesContainer.appendChild(itemCard);
                });
            }

            function closeReplaceModal() {
                replaceModal.classList.remove('active');
                document.body.style.overflow = '';
                currentItemType = '';
            }

            function replaceItem(itemType, newItem) {
                // 获取所有单品卡片
                const itemCards = document.querySelectorAll('.enhanced-glass');
                let targetCard = null;
                
                // 遍历所有卡片找到对应的单品卡片
                itemCards.forEach(card => {
                    const replaceButton = card.querySelector('.replace-button');
                    if (replaceButton && replaceButton.getAttribute('data-item-type') === itemType) {
                        targetCard = card;
                    }
                });
                
                if (targetCard) {
                    // 更新图片
                    const img = targetCard.querySelector('img');
                    img.src = newItem.image;
                    img.alt = newItem.title;
                    
                    // 更新文字内容
                    const title = targetCard.querySelector('h3');
                    const description = targetCard.querySelector('p');
                    const element = targetCard.querySelector('.rounded-full');
                    const tagsContainer = targetCard.querySelector('.flex.flex-wrap.gap-1');
                    
                    title.textContent = newItem.title;
                    description.textContent = newItem.description;
                    element.textContent = newItem.element;
                    
                    // 更新标签
                    tagsContainer.innerHTML = newItem.tags.map(tag => `
                        <span class="text-[8px] px-1.5 py-0.5 rounded-full bg-white/40 text-[var(--color-text-secondary)]">
                            ${tag}
                        </span>
                    `).join('');
                }
            }

            // 添加五行辅助函数
            function getWuxingClass(element) {
                const map = {
                    '金': 'jin',
                    '木': 'mu',
                    '水': 'shui',
                    '火': 'huo',
                    '土': 'tu'
                };
                return map[element] || 'jin';
            }

            function getWuxingIcon(element) {
                const map = {
                    '金': 'fa-coins',
                    '木': 'fa-tree',
                    '水': 'fa-water',
                    '火': 'fa-fire',
                    '土': 'fa-mountain'
                };
                return map[element] || 'fa-coins';
            }

            // 点赞功能
            window.toggleLike = function(button) {
                const icon = button.querySelector('i');
                const count = button.querySelector('span');
                const currentCount = parseInt(count.textContent);
                
                button.classList.toggle('active');
                
                if (button.classList.contains('active')) {
                    icon.classList.remove('far');
                    icon.classList.add('fas');
                    icon.style.transform = 'scale(1.2)';
                    setTimeout(() => icon.style.transform = 'scale(1)', 200);
                    count.textContent = currentCount + 1;
                } else {
                    icon.classList.remove('fas');
                    icon.classList.add('far');
                    count.textContent = currentCount - 1;
                }
            }

            // 收藏功能
            window.toggleFavorite = function(button) {
                const icon = button.querySelector('i');
                const count = button.querySelector('span');
                const currentCount = parseInt(count.textContent);
                
                button.classList.toggle('active');
                
                if (button.classList.contains('active')) {
                    icon.classList.remove('far');
                    icon.classList.add('fas');
                    icon.style.transform = 'scale(1.2)';
                    setTimeout(() => icon.style.transform = 'scale(1)', 200);
                    count.textContent = currentCount + 1;
                } else {
                    icon.classList.remove('fas');
                    icon.classList.add('far');
                    count.textContent = currentCount - 1;
                }
            }

            // 分享功能
            window.shareOutfit = function() {
                const button = event.currentTarget;
                const icon = button.querySelector('i');
                
                // 添加动画效果
                icon.style.transform = 'scale(1.2)';
                setTimeout(() => icon.style.transform = 'scale(1)', 200);
                
                // TODO: 实现分享功能
                console.log('分享功能待实现');
            }

            // 初始化按钮状态
            function initializeActionButtons() {
                try {
                    // 获取所有按钮
                    const likeButton = document.querySelector('button:has(i.fa-heart)');
                    const favoriteButton = document.querySelector('button:has(i.fa-star)');
                    const shareButton = document.querySelector('button:has(i.fa-share-alt)');

                    if (likeButton) {
                        const likeIcon = likeButton.querySelector('i');
                        if (likeIcon.classList.contains('fas')) {
                            likeButton.classList.add('active');
                        }
                    }

                    if (favoriteButton) {
                        const favoriteIcon = favoriteButton.querySelector('i');
                        if (favoriteIcon.classList.contains('fas')) {
                            favoriteButton.classList.add('active');
                        }
                    }
                } catch (error) {
                    console.error('初始化按钮状态时发生错误:', error);
                }
            }

            // 页面加载完成后初始化按钮状态
            document.addEventListener('DOMContentLoaded', initializeActionButtons);
        });
    </script>
</body>
</html> 