<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <title>能量详情 - 穿搭推荐小程序</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../styles/wuxing.css" rel="stylesheet">
    <style>
        :root {
            --color-text-primary: #333333;
            --color-text-secondary: #666666;
            --color-text-tertiary: #999999;
        }
        
        /* 隐藏滚动条 */
        ::-webkit-scrollbar {
            display: none;
        }
        
        * {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        
        body {
            overflow-y: scroll;
            -webkit-overflow-scrolling: touch;
        }

        /* 内容区域统一间距 */
        .content-container {
            padding-left: 5%;
            padding-right: 5%;
        }
        
        .gradient-background {
            background: linear-gradient(135deg, #c2e9fb 0%, #d8c6fd 100%);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: -1;
        }
        
        .enhanced-glass {
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.05);
        }
        
        .glass-button {
            background: rgba(255, 255, 255, 0.4);
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.5);
            transition: all 0.3s ease;
        }
        
        .glass-button:hover {
            background: rgba(255, 255, 255, 0.6);
        }

        /* 雷达图旋转动画 */
        .radar-animation {
            animation: pulse 2s infinite ease-in-out;
        }
        
        @keyframes pulse {
            0% { opacity: 0.5; }
            50% { opacity: 0.8; }
            100% { opacity: 0.5; }
        }
    </style>
</head>
<body>
    <!-- 渐变背景 -->
    <div class="gradient-background"></div>

    <!-- 顶部导航 -->
    <div class="sticky top-0 bg-white/30 backdrop-blur-md z-40 shadow-sm">
        <div class="flex items-center justify-between p-4">
            <div class="flex items-center">
            <a href="../pages/home.html" class="mr-4">
                <i class="fas fa-arrow-left text-[var(--color-text-primary)]"></i>
            </a>
                <h1 class="text-sm font-semibold text-[var(--color-text-primary)]">今日能量</h1>
            </div>
        </div>
    </div>

    <!-- 主内容区 -->
    <div class="content-container py-6 pb-24 space-y-6">
        <!-- 日期和五行信息 -->
        <div class="mb-6">
            <div class="flex flex-row items-center gap-x-3 gap-y-1">
                <div class="flex flex-col justify-center">
                    <h2 class="text-sm font-semibold text-[var(--color-text-primary)]">2023年11月20日</h2>
                    <p class="text-xs text-[var(--color-text-secondary)] mt-1">二月十九 己巳卯 丙成</p>
                </div>
                <div class="flex flex-col items-center justify-center bg-white/20 px-1.5 py-0.5 rounded-lg min-w-[36px] min-h-[36px]">
                    <i class="fas fa-water text-blue-400 text-sm mb-0"></i>
                    <span class="text-xs text-[var(--color-text-secondary)]">水行</span>
                </div>
            </div>
        </div>
        
        <!-- 能量解读卡片 -->
        <div class="enhanced-glass rounded-xl p-6 mb-6">
            <h3 class="text-sm font-medium text-[var(--color-text-primary)] mb-4">能量解读</h3>
            
            <!-- 总分展示 -->
            <div class="flex flex-col items-center mb-8">
                <div class="w-32 aspect-square relative flex flex-col items-center justify-center">
                    <!-- 圆形进度条 SVG -->
                    <svg viewBox="0 0 100 100" class="w-full">
                        <defs>
                            <linearGradient id="progressGradient" gradientUnits="userSpaceOnUse" x1="50" y1="8" x2="15.8" y2="29.5">
                                <stop offset="0%" stop-color="#d8b4fe" />
                                <stop offset="33%" stop-color="#c4a6fa" />
                                <stop offset="67%" stop-color="#b095f8" />
                                <stop offset="100%" stop-color="#9f84f7" />
                            </linearGradient>
                            <filter id="progressGlow" x="-20%" y="-20%" width="140%" height="140%">
                                <feGaussianBlur stdDeviation="1.5" result="blur" />
                                <feComposite in="SourceGraphic" in2="blur" operator="over" />
                            </filter>
                        </defs>
                        <circle cx="50" cy="50" r="40" fill="none" stroke="rgba(255,255,255,0.15)" stroke-width="4" stroke-linecap="round" />
                        <circle cx="50" cy="50" r="40" fill="none" stroke="url(#progressGradient)" stroke-width="5" stroke-linecap="round" stroke-dasharray="251.33 251.33" stroke-dashoffset="55.29" filter="url(#progressGlow)" transform="rotate(-90, 50, 50)" />
                    </svg>
                    <!-- 分数显示 -->
                    <div class="absolute inset-0 flex flex-col items-center justify-center">
                        <span class="text-[10px] font-medium text-[var(--color-text-secondary)]">今日能量</span>
                        <p class="text-2xl font-bold text-[var(--color-text-primary)]">78</p>
                    </div>
                </div>
                <div class="mt-1 text-center">
                    <span class="text-xs text-[var(--color-text-secondary)] opacity-80">超过了82%的用户</span>
                    <div class="text-xs text-[var(--color-text-secondary)]">中上能量</div>
                </div>
            </div>

            <!-- 五个能量指标 -->
            <div class="flex justify-between px-2 mb-6">
                <!-- 爱情能量 -->
                <div class="flex flex-col items-center">
                    <div class="relative flex items-center justify-center mb-1">
                        <i class="fas fa-heart text-pink-400/20 text-3xl absolute"></i>
                        <span class="text-xl font-semibold text-pink-400 relative z-10">65</span>
                    </div>
                    <span class="text-[10px]">爱情</span>
                </div>
                
                <!-- 事业能量 -->
                <div class="flex flex-col items-center">
                    <div class="relative flex items-center justify-center mb-1">
                        <i class="fas fa-briefcase text-yellow-400/20 text-3xl absolute"></i>
                        <span class="text-xl font-semibold text-yellow-400 relative z-10">88</span>
                    </div>
                    <span class="text-[10px]">事业</span>
                </div>
                
                <!-- 财富能量 -->
                <div class="flex flex-col items-center">
                    <div class="relative flex items-center justify-center mb-1">
                        <i class="fas fa-coins text-green-400/20 text-3xl absolute"></i>
                        <span class="text-xl font-semibold text-green-400 relative z-10">72</span>
                    </div>
                    <span class="text-[10px]">财富</span>
                </div>
                
                <!-- 健康能量 -->
                <div class="flex flex-col items-center">
                    <div class="relative flex items-center justify-center mb-1">
                        <i class="fas fa-heartbeat text-blue-400/20 text-3xl absolute"></i>
                        <span class="text-xl font-semibold text-blue-400 relative z-10">81</span>
                    </div>
                    <span class="text-[10px]">健康</span>
                </div>
                
                <!-- 人际能量 -->
                <div class="flex flex-col items-center">
                    <div class="relative flex items-center justify-center mb-1">
                        <i class="fas fa-users text-purple-400/20 text-3xl absolute"></i>
                        <span class="text-xl font-semibold text-purple-400 relative z-10">76</span>
                    </div>
                    <span class="text-[10px]">人际</span>
                </div>
            </div>
            
            <!-- 能量高峰期 -->
            <div class="mb-4">
                <div class="flex items-center justify-between mb-3">
                    <div class="flex items-center">
                        <i class="fas fa-clock text-blue-300 mr-2"></i>
                        <h4 class="text-xs font-medium text-[var(--color-text-primary)]">能量高峰时段</h4>
                    </div>
                    <div class="flex items-center gap-2 bg-white/30 px-3 py-1 rounded-full">
                        <div class="w-2 h-2 bg-yellow-300 rounded-full"></div>
                        <span class="text-xs font-medium text-[var(--color-text-primary)]">上午8-10点</span>
                    </div>
                </div>
                <p class="text-[10px] text-[var(--color-text-secondary)]">
                    适合重要会议和决策，思维灵活度高
                </p>
            </div>
            
            <p class="text-xs text-[var(--color-text-secondary)]">
                今日五行能量以金土为主，水行偏弱。整体能量场偏阳性，利于事业和社交活动，但需注意与家人的沟通。
            </p>
        </div>
        
        <!-- 宜忌事项卡片 -->
        <div class="enhanced-glass rounded-xl p-6 mb-6">
            <h3 class="text-sm font-medium text-[var(--color-text-primary)] mb-4">宜忌指南</h3>
            
            <!-- 宜 -->
            <div class="mb-6">
                <div class="flex items-center gap-2 mb-3">
                    <div class="w-1 h-6 bg-green-400 rounded-full"></div>
                    <h4 class="text-sm font-medium text-[var(--color-text-primary)]">宜做事项</h4>
                </div>
                <div class="grid grid-cols-2 gap-2">
                    <div class="flex items-center gap-2 bg-white/20 rounded-lg py-2 px-3">
                        <i class="fas fa-handshake text-[12px] text-green-400"></i>
                        <span class="text-xs text-[var(--color-text-primary)]">开展合作</span>
                    </div>
                    <div class="flex items-center gap-2 bg-white/20 rounded-lg py-2 px-3">
                        <i class="fas fa-comments-dollar text-[12px] text-green-400"></i>
                        <span class="text-xs text-[var(--color-text-primary)]">商务谈判</span>
                    </div>
                    <div class="flex items-center gap-2 bg-white/20 rounded-lg py-2 px-3">
                        <i class="fas fa-book-reader text-[12px] text-green-400"></i>
                        <span class="text-xs text-[var(--color-text-primary)]">学习进修</span>
                    </div>
                    <div class="flex items-center gap-2 bg-white/20 rounded-lg py-2 px-3">
                        <i class="fas fa-boxes text-[12px] text-green-400"></i>
                        <span class="text-xs text-[var(--color-text-primary)]">整理空间</span>
                    </div>
                </div>
            </div>
            
            <!-- 忌 -->
            <div class="mb-6">
                <div class="flex items-center gap-2 mb-3">
                    <div class="w-1 h-6 bg-red-400 rounded-full"></div>
                    <h4 class="text-sm font-medium text-[var(--color-text-primary)]">忌做事项</h4>
                </div>
                <div class="grid grid-cols-2 gap-2">
                    <div class="flex items-center gap-2 bg-white/20 rounded-lg py-2 px-3">
                        <i class="fas fa-fire text-[12px] text-red-400"></i>
                        <span class="text-xs text-[var(--color-text-primary)]">激烈争执</span>
                    </div>
                    <div class="flex items-center gap-2 bg-white/20 rounded-lg py-2 px-3">
                        <i class="fas fa-shopping-cart text-[12px] text-red-400"></i>
                        <span class="text-xs text-[var(--color-text-primary)]">冲动消费</span>
                    </div>
                    <div class="flex items-center gap-2 bg-white/20 rounded-lg py-2 px-3">
                        <i class="fas fa-moon text-[12px] text-red-400"></i>
                        <span class="text-xs text-[var(--color-text-primary)]">熬夜加班</span>
                    </div>
                    <div class="flex items-center gap-2 bg-white/20 rounded-lg py-2 px-3">
                        <i class="fas fa-exclamation-circle text-[12px] text-red-400"></i>
                        <span class="text-xs text-[var(--color-text-primary)]">重大决定</span>
                    </div>
                </div>
            </div>

            <!-- 生活建议 -->
            <div class="space-y-4">
                <div class="flex items-center gap-2">
                    <div class="w-1 h-6 bg-blue-400 rounded-full"></div>
                    <h4 class="text-sm font-medium text-[var(--color-text-primary)]">生活建议</h4>
                </div>
                <div class="bg-white/20 rounded-lg p-3 space-y-3">
                    <div class="flex items-start gap-3">
                        <div class="w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center flex-shrink-0">
                            <i class="fas fa-water text-[12px] text-blue-400"></i>
                        </div>
                        <p class="text-xs text-[var(--color-text-secondary)] leading-relaxed">
                            今天会让你觉得生活中充满了不顺遂，很多来自外界的质疑挑战，缺乏安全感。
                        </p>
                    </div>
                    <div class="flex items-start gap-3">
                        <div class="w-6 h-6 rounded-full bg-yellow-100 flex items-center justify-center flex-shrink-0">
                            <i class="fas fa-mountain text-[12px] text-yellow-400"></i>
                        </div>
                        <p class="text-xs text-[var(--color-text-secondary)] leading-relaxed">
                            突然间对关系本质是什么的怀疑，会导致你情绪的明显波动。建议一个人多想想静静，不要太任性而为。
                        </p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 提升能量建议 -->
        <div class="enhanced-glass rounded-xl p-6 mb-6">
            <h3 class="text-sm font-medium text-[var(--color-text-primary)] mb-4">提升能量建议</h3>
            
            <!-- 今日幸运色 -->
            <div class="mb-6">
                <div class="flex items-center gap-2 mb-3">
                    <div class="w-1 h-6 bg-[#b8c6db] rounded-full"></div>
                    <h4 class="text-sm font-medium text-[var(--color-text-primary)]">今日幸运色</h4>
                </div>
                <div class="flex gap-3 bg-white/20 rounded-lg p-3">
                    <div class="flex items-center">
                        <div class="w-6 h-6 rounded-full bg-[#b8c6db] border border-white/60"></div>
                        <span class="text-xs text-[var(--color-text-secondary)] ml-2">水蓝色</span>
                    </div>
                    <div class="flex items-center">
                        <div class="w-6 h-6 rounded-full bg-[#edf7fc] border border-white/60"></div>
                        <span class="text-xs text-[var(--color-text-secondary)] ml-2">浅青色</span>
                    </div>
                    <div class="flex items-center">
                        <div class="w-6 h-6 rounded-full bg-[#333333] border border-white/60"></div>
                        <span class="text-xs text-[var(--color-text-secondary)] ml-2">深黑色</span>
                    </div>
                </div>
            </div>
            
            <!-- 服饰建议 -->
            <div class="mb-6">
                <div class="flex items-center gap-2 mb-3">
                    <div class="w-1 h-6 bg-[#b8c6db] rounded-full"></div>
                    <h4 class="text-sm font-medium text-[var(--color-text-primary)]">服饰建议</h4>
                </div>
                <div class="bg-white/20 rounded-lg p-3">
                    <div class="flex items-start gap-3">
                        <div class="w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center flex-shrink-0">
                            <i class="fas fa-tshirt text-[12px] text-blue-400"></i>
                        </div>
                        <div class="space-y-2">
                            <p class="text-xs text-[var(--color-text-secondary)] leading-relaxed">推荐丝绸、雪纺类柔软面料。</p>
                            <p class="text-xs text-[var(--color-text-secondary)] leading-relaxed">上装可选丝质衬衫或针织衫，下装适合休闲裤或裙装。</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 配饰建议 -->
            <div class="mb-6">
                <div class="flex items-center gap-2 mb-3">
                    <div class="w-1 h-6 bg-[#b8c6db] rounded-full"></div>
                    <h4 class="text-sm font-medium text-[var(--color-text-primary)]">配饰建议</h4>
                </div>
                <div class="bg-white/20 rounded-lg p-3">
                    <div class="flex items-start gap-3">
                        <div class="w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center flex-shrink-0">
                            <i class="fas fa-gem text-[12px] text-blue-400"></i>
                        </div>
                        <div class="space-y-2">
                            <p class="text-xs text-[var(--color-text-secondary)] leading-relaxed">首饰宜选银色、白金系，搭配蓝色水晶或珍珠点缀。</p>
                            <p class="text-xs text-[var(--color-text-secondary)] leading-relaxed">建议佩戴简约水滴造型耳环或项链，手提包选深蓝色或黑色。</p>
                            <p class="text-xs text-[var(--color-text-secondary)] leading-relaxed">配饰数量控制在2-3件为佳，避免过多干扰能量流动。</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 妆容建议 -->
        <div>
            <div class="flex items-center gap-2 mb-3">
                <div class="w-1 h-6 bg-[#b8c6db] rounded-full"></div>
                <h4 class="text-sm font-medium text-[var(--color-text-primary)]">妆容建议</h4>
            </div>
            <div class="bg-white/20 rounded-lg p-3">
                <div class="flex items-start gap-3">
                    <div class="w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center flex-shrink-0">
                        <i class="fas fa-magic text-[12px] text-blue-400"></i>
                    </div>
                    <div class="space-y-2">
                        <p class="text-xs text-[var(--color-text-secondary)] leading-relaxed">今日妆容以清透水润为主，底妆轻薄通透。</p>
                        <p class="text-xs text-[var(--color-text-secondary)] leading-relaxed">眼部可点缀淡蓝或淡紫色眼影，增添深邃感。</p>
                        <p class="text-xs text-[var(--color-text-secondary)] leading-relaxed">唇妆宜选水润质感的淡粉色或珊瑚色，腮红以自然粉嫩为主，打造清新气色。</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 底部按钮 -->
    <div class="flex justify-center mt-8 mb-12">
        <a href="fortune_complete.html" class="glass-button py-2.5 px-8 rounded-full text-xs font-medium text-[var(--color-text-primary)] mr-4">
            <i class="fas fa-star mr-2"></i>
            能量完整解读
        </a>
        <a href="../pages/home.html" class="glass-button py-2.5 px-8 rounded-full text-xs font-medium text-[var(--color-text-primary)]">
            查看搭配建议
        </a>
    </div>
    
    <script>
        // 简单的页面交互效果
        document.addEventListener('DOMContentLoaded', function() {
            // 页面加载动画
            setTimeout(function() {
                document.querySelector('.radar-animation').classList.add('opacity-100');
            }, 300);
            
            // 按钮点击效果
            const buttons = document.querySelectorAll('.glass-button');
            buttons.forEach(button => {
                button.addEventListener('click', function() {
                    this.classList.add('bg-white/60');
                    setTimeout(() => {
                        this.classList.remove('bg-white/60');
                    }, 200);
                });
            });
        });
    </script>
</body>
</html> 