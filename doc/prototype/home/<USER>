<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <title>风格建议 - 穿搭推荐小程序</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --color-text-primary: #333333;
            --color-text-secondary: #666666;
            --color-text-tertiary: #999999;
        }
        
        /* 隐藏滚动条 */
        ::-webkit-scrollbar {
            display: none;
        }
        
        * {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        
        body {
            overflow-y: scroll;
            -webkit-overflow-scrolling: touch;
        }

        /* 内容区域统一间距 */
        .content-container {
            padding-left: 5%;
            padding-right: 5%;
        }
        
        .gradient-background {
            background: linear-gradient(135deg, #c2e9fb 0%, #d8c6fd 100%);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: -1;
        }
        
        .enhanced-glass {
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.05);
        }
        
        .glass-button {
            background: rgba(255, 255, 255, 0.4);
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.5);
            transition: all 0.3s ease;
        }
        
        .glass-button:hover {
            background: rgba(255, 255, 255, 0.6);
        }
        
        /* 五行标识色彩 */
        .wuxing-jin {
            background: linear-gradient(135deg, #ffffff, #f0f0f0);
            color: #1a1a1a;
        }
        
        .wuxing-mu {
            background: linear-gradient(135deg, #a8e6cf, #73c1a8);
            color: #1a1a1a;
        }
        
        .wuxing-shui {
            background: linear-gradient(135deg, #b8c6db, #648dae);
            color: #ffffff;
        }
        
        .wuxing-huo {
            background: linear-gradient(135deg, #ff9a9e, #ff5458);
            color: #ffffff;
        }
        
        .wuxing-tu {
            background: linear-gradient(135deg, #ffeaa7, #ffc25c);
            color: #1a1a1a;
        }
        
        /* 滑动切换 */
        .tab-content {
            transition: transform 0.3s ease;
        }
        
        .tab-active {
            color: var(--color-text-primary);
            border-bottom: 2px solid #73c1a8;
            font-weight: 500;
        }
        
        .tab-inactive {
            color: var(--color-text-secondary);
        }
    </style>
</head>
<body>
    <!-- 渐变背景 -->
    <div class="gradient-background"></div>

    <!-- 顶部导航 -->
    <div class="sticky top-0 bg-white/30 backdrop-blur-md z-40 shadow-sm">
        <div class="flex items-center p-4">
            <a href="../pages/home.html" class="mr-4">
                <i class="fas fa-arrow-left text-[var(--color-text-primary)]"></i>
            </a>
            <h1 class="text-sm font-semibold text-[var(--color-text-primary)]">风格建议</h1>
        </div>
    </div>

    <!-- 主内容区 -->
    <div class="content-container py-4 pb-20">
        <!-- 日期信息 -->
        <div class="mb-4">
            <h2 class="text-sm font-semibold text-[var(--color-text-primary)] mb-1">2023年11月20日</h2>
            <p class="text-xs text-[var(--color-text-secondary)]">二月十九 己巳卯 丙成</p>
        </div>
        
        <!-- 能量状态卡片 -->
        <div class="enhanced-glass rounded-xl p-4 mb-4">
            <div class="flex justify-between items-center mb-3">
                <h3 class="text-sm font-medium text-[var(--color-text-primary)]">今日能量状态</h3>
                <div class="flex items-center">
                    <span class="text-[10px] text-[var(--color-text-secondary)] mr-1">能量值</span>
                    <div class="w-6 h-6 rounded-full bg-white/40 flex items-center justify-center">
                        <span class="text-xs font-medium text-[var(--color-text-primary)]">78</span>
                    </div>
                </div>
            </div>
            
            <p class="text-xs text-[var(--color-text-secondary)] mb-3">
                今日五行能量以金土为主，水行偏弱。建议在穿搭上注意补足水属性，提升整体能量平衡。
            </p>
            
            <!-- 五行能量小标签 -->
            <div class="flex flex-wrap gap-2">
                <div class="flex items-center px-2 py-1 rounded-full bg-white/40">
                    <div class="w-2 h-2 rounded-full bg-white mr-1"></div>
                    <span class="text-[10px] text-[var(--color-text-secondary)]">金 82%</span>
                </div>
                <div class="flex items-center px-2 py-1 rounded-full bg-[#a8e6cf]/40">
                    <div class="w-2 h-2 rounded-full bg-[#a8e6cf] mr-1"></div>
                    <span class="text-[10px] text-[var(--color-text-secondary)]">木 65%</span>
                </div>
                <div class="flex items-center px-2 py-1 rounded-full bg-[#b8c6db]/40">
                    <div class="w-2 h-2 rounded-full bg-[#b8c6db] mr-1"></div>
                    <span class="text-[10px] text-[var(--color-text-secondary)]">水 48%</span>
                </div>
                <div class="flex items-center px-2 py-1 rounded-full bg-[#ff9a9e]/40">
                    <div class="w-2 h-2 rounded-full bg-[#ff9a9e] mr-1"></div>
                    <span class="text-[10px] text-[var(--color-text-secondary)]">火 52%</span>
                </div>
                <div class="flex items-center px-2 py-1 rounded-full bg-[#ffeaa7]/40">
                    <div class="w-2 h-2 rounded-full bg-[#ffeaa7] mr-1"></div>
                    <span class="text-[10px] text-[var(--color-text-secondary)]">土 78%</span>
                </div>
            </div>
        </div>
        
        <!-- 分类标签导航 -->
        <div class="flex border-b border-white/30 mb-4">
            <button class="flex-1 py-2 text-xs tab-active" id="tab-1">服饰建议</button>
            <button class="flex-1 py-2 text-xs tab-inactive" id="tab-2">配饰建议</button>
            <button class="flex-1 py-2 text-xs tab-inactive" id="tab-3">妆容建议</button>
        </div>
        
        <!-- 内容区域 -->
        <div class="relative overflow-hidden mb-4">
            <!-- 滑动内容容器 -->
            <div class="tab-content flex transition-transform duration-300" id="tab-content" style="width: 300%;">
                <!-- 服饰建议内容 -->
                <div class="w-full px-1" id="content-1">
                    <!-- 颜色推荐 -->
                    <div class="enhanced-glass rounded-xl p-4 mb-4">
                        <h3 class="text-sm font-medium text-[var(--color-text-primary)] mb-3">
                            <i class="fas fa-palette mr-2 text-[#b8c6db]"></i>
                            推荐颜色
                        </h3>
                        
                        <div class="flex space-x-3 mb-3">
                            <!-- 颜色1 -->
                            <div class="flex flex-col items-center">
                                <div class="w-12 h-12 rounded-full bg-[#b8c6db] border border-white/60"></div>
                                <span class="text-[10px] text-[var(--color-text-secondary)] mt-1">水蓝色</span>
                            </div>
                            
                            <!-- 颜色2 -->
                            <div class="flex flex-col items-center">
                                <div class="w-12 h-12 rounded-full bg-[#edf7fc] border border-white/60"></div>
                                <span class="text-[10px] text-[var(--color-text-secondary)] mt-1">浅青色</span>
                            </div>
                            
                            <!-- 颜色3 -->
                            <div class="flex flex-col items-center">
                                <div class="w-12 h-12 rounded-full bg-[#a6c1ee] border border-white/60"></div>
                                <span class="text-[10px] text-[var(--color-text-secondary)] mt-1">淡紫蓝</span>
                            </div>
                            
                            <!-- 颜色4 -->
                            <div class="flex flex-col items-center">
                                <div class="w-12 h-12 rounded-full bg-[#333333] border border-white/60"></div>
                                <span class="text-[10px] text-[var(--color-text-secondary)] mt-1">深黑色</span>
                            </div>
                        </div>
                        
                        <p class="text-xs text-[var(--color-text-secondary)]">
                            今日宜穿蓝色系、黑色系服装，水属性色彩可提升智慧与灵感，调和水的不足，平衡五行能量。
                        </p>
                    </div>
                    
                    <!-- 材质推荐 -->
                    <div class="enhanced-glass rounded-xl p-4 mb-4">
                        <h3 class="text-sm font-medium text-[var(--color-text-primary)] mb-3">
                            <i class="fas fa-feather-alt mr-2 text-[#b8c6db]"></i>
                            推荐材质
                        </h3>
                        
                        <div class="flex flex-wrap gap-2 mb-3">
                            <span class="text-[10px] px-3 py-1 rounded-full bg-white/30 text-[var(--color-text-secondary)]">
                                丝绸
                            </span>
                            <span class="text-[10px] px-3 py-1 rounded-full bg-white/30 text-[var(--color-text-secondary)]">
                                雪纺
                            </span>
                            <span class="text-[10px] px-3 py-1 rounded-full bg-white/30 text-[var(--color-text-secondary)]">
                                柔软针织
                            </span>
                            <span class="text-[10px] px-3 py-1 rounded-full bg-white/30 text-[var(--color-text-secondary)]">
                                光滑面料
                            </span>
                            <span class="text-[10px] px-3 py-1 rounded-full bg-white/30 text-[var(--color-text-secondary)]">
                                透明质感
                            </span>
                        </div>
                        
                        <p class="text-xs text-[var(--color-text-secondary)]">
                            流动感、柔软质地的面料符合水行特性，帮助调和命理中水行不足，提升智慧与人际关系。
                        </p>
                    </div>
                    
                    <!-- 服装搭配建议 -->
                    <div class="enhanced-glass rounded-xl p-4 mb-4">
                        <h3 class="text-sm font-medium text-[var(--color-text-primary)] mb-3">
                            <i class="fas fa-tshirt mr-2 text-[#b8c6db]"></i>
                            搭配建议
                        </h3>
                        
                        <div class="mb-3">
                            <div class="flex mb-2">
                                <div class="w-6 h-6 rounded-full bg-white/20 flex items-center justify-center mr-2 flex-shrink-0">
                                    <span class="text-xs font-medium text-[var(--color-text-primary)]">上</span>
                                </div>
                                <p class="text-xs text-[var(--color-text-secondary)]">
                                    深蓝色丝质衬衫或浅青色针织上衣，柔软舒适的面料有助于提升水行能量。
                                </p>
                            </div>
                            
                            <div class="flex mb-2">
                                <div class="w-6 h-6 rounded-full bg-white/20 flex items-center justify-center mr-2 flex-shrink-0">
                                    <span class="text-xs font-medium text-[var(--color-text-primary)]">下</span>
                                </div>
                                <p class="text-xs text-[var(--color-text-secondary)]">
                                    黑色休闲裤或深蓝色A字裙，流畅的线条有助于增强气场流动性。
                                </p>
                            </div>
                            
                            <div class="flex">
                                <div class="w-6 h-6 rounded-full bg-white/20 flex items-center justify-center mr-2 flex-shrink-0">
                                    <span class="text-xs font-medium text-[var(--color-text-primary)]">外</span>
                                </div>
                                <p class="text-xs text-[var(--color-text-secondary)]">
                                    可选择白色或米色轻薄外套，平衡整体色系，增加层次感。
                                </p>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <h4 class="text-xs font-medium text-[var(--color-text-primary)] mb-2">适合场合</h4>
                            <div class="flex flex-wrap gap-2">
                                <span class="text-[10px] px-3 py-1 rounded-full bg-white/30 text-[var(--color-text-secondary)]">
                                    <i class="fas fa-briefcase mr-1"></i>工作
                                </span>
                                <span class="text-[10px] px-3 py-1 rounded-full bg-white/30 text-[var(--color-text-secondary)]">
                                    <i class="fas fa-coffee mr-1"></i>日常社交
                                </span>
                                <span class="text-[10px] px-3 py-1 rounded-full bg-white/30 text-[var(--color-text-secondary)]">
                                    <i class="fas fa-book mr-1"></i>学习
                                </span>
                            </div>
                        </div>
                        
                        <div>
                            <h4 class="text-xs font-medium text-[var(--color-text-primary)] mb-2">能量提升</h4>
                            <p class="text-xs text-[var(--color-text-secondary)]">
                                此搭配有助于提升今日水行能量，增强智慧与人际关系，尤其适合需要与人沟通、谈判或学习的场合。
                            </p>
                        </div>
                    </div>
                    
                    <!-- 示例图 -->
                    <div class="enhanced-glass rounded-xl overflow-hidden mb-4">
                        <img src="https://images.unsplash.com/photo-1485230895905-ec40ba36b9bc" 
                             class="w-full aspect-[4/5] object-cover" alt="穿搭示例">
                        <div class="p-3">
                            <h4 class="text-xs font-medium text-[var(--color-text-primary)] mb-1">推荐穿搭示例</h4>
                            <p class="text-[10px] text-[var(--color-text-secondary)]">
                                水蓝色系穿搭能增强智慧、提升今日人际运势，柔和面料有助于能量流动。
                            </p>
                        </div>
                    </div>
                </div>
                
                <!-- 配饰建议内容 -->
                <div class="w-full px-1" id="content-2">
                    <!-- 配饰颜色 -->
                    <div class="enhanced-glass rounded-xl p-4 mb-4">
                        <h3 class="text-sm font-medium text-[var(--color-text-primary)] mb-3">
                            <i class="fas fa-palette mr-2 text-[#b8c6db]"></i>
                            推荐颜色
                        </h3>
                        
                        <div class="flex space-x-3 mb-3">
                            <!-- 颜色1 -->
                            <div class="flex flex-col items-center">
                                <div class="w-12 h-12 rounded-full bg-[#b8c6db] border border-white/60"></div>
                                <span class="text-[10px] text-[var(--color-text-secondary)] mt-1">水蓝色</span>
                            </div>
                            
                            <!-- 颜色2 -->
                            <div class="flex flex-col items-center">
                                <div class="w-12 h-12 rounded-full bg-[#333333] border border-white/60"></div>
                                <span class="text-[10px] text-[var(--color-text-secondary)] mt-1">黑色</span>
                            </div>
                            
                            <!-- 颜色3 -->
                            <div class="flex flex-col items-center">
                                <div class="w-12 h-12 rounded-full bg-[#c0c0c0] border border-white/60"></div>
                                <span class="text-[10px] text-[var(--color-text-secondary)] mt-1">银色</span>
                            </div>
                            
                            <!-- 颜色4 -->
                            <div class="flex flex-col items-center">
                                <div class="w-12 h-12 rounded-full bg-[#ffffff] border border-white/60"></div>
                                <span class="text-[10px] text-[var(--color-text-secondary)] mt-1">白色</span>
                            </div>
                        </div>
                        
                        <p class="text-xs text-[var(--color-text-secondary)]">
                            今日首饰宜选择蓝色、黑色系，或银色、白金等金属色，增强水行能量，提升智慧与创造力。
                        </p>
                    </div>
                    
                    <!-- 材质推荐 -->
                    <div class="enhanced-glass rounded-xl p-4 mb-4">
                        <h3 class="text-sm font-medium text-[var(--color-text-primary)] mb-3">
                            <i class="fas fa-gem mr-2 text-[#b8c6db]"></i>
                            推荐材质
                        </h3>
                        
                        <div class="flex flex-wrap gap-2 mb-3">
                            <span class="text-[10px] px-3 py-1 rounded-full bg-white/30 text-[var(--color-text-secondary)]">
                                白银
                            </span>
                            <span class="text-[10px] px-3 py-1 rounded-full bg-white/30 text-[var(--color-text-secondary)]">
                                白金
                            </span>
                            <span class="text-[10px] px-3 py-1 rounded-full bg-white/30 text-[var(--color-text-secondary)]">
                                水晶
                            </span>
                            <span class="text-[10px] px-3 py-1 rounded-full bg-white/30 text-[var(--color-text-secondary)]">
                                玻璃质感
                            </span>
                            <span class="text-[10px] px-3 py-1 rounded-full bg-white/30 text-[var(--color-text-secondary)]">
                                珍珠
                            </span>
                        </div>
                        
                        <p class="text-xs text-[var(--color-text-secondary)]">
                            光滑、透亮的材质代表水的流动性，金属中白银和白金属水，选择这类材质有助提升水行能量。
                        </p>
                    </div>
                    
                    <!-- 配饰搭配建议 -->
                    <div class="enhanced-glass rounded-xl p-4 mb-4">
                        <h3 class="text-sm font-medium text-[var(--color-text-primary)] mb-3">
                            <i class="fas fa-list-ul mr-2 text-[#b8c6db]"></i>
                            配饰建议
                        </h3>
                        
                        <div class="space-y-3 mb-3">
                            <div class="flex">
                                <div class="w-6 h-6 rounded-full bg-white/20 flex items-center justify-center mr-2 flex-shrink-0">
                                    <i class="fas fa-gem text-[10px] text-[#b8c6db]"></i>
                                </div>
                                <div>
                                    <h4 class="text-xs font-medium text-[var(--color-text-primary)] mb-1">首饰</h4>
                                    <p class="text-xs text-[var(--color-text-secondary)]">
                                        简约水滴形耳环或项链，以银色、白金为主，可搭配蓝色水晶或珍珠点缀。
                                    </p>
                                </div>
                            </div>
                            
                            <div class="flex">
                                <div class="w-6 h-6 rounded-full bg-white/20 flex items-center justify-center mr-2 flex-shrink-0">
                                    <i class="fas fa-shopping-bag text-[10px] text-[#b8c6db]"></i>
                                </div>
                                <div>
                                    <h4 class="text-xs font-medium text-[var(--color-text-primary)] mb-1">包包</h4>
                                    <p class="text-xs text-[var(--color-text-secondary)]">
                                        深蓝色或黑色手提包，简约流畅线条，质地光滑，金属配件宜选银色。
                                    </p>
                                </div>
                            </div>
                            
                            <div class="flex">
                                <div class="w-6 h-6 rounded-full bg-white/20 flex items-center justify-center mr-2 flex-shrink-0">
                                    <i class="fas fa-glasses text-[10px] text-[#b8c6db]"></i>
                                </div>
                                <div>
                                    <h4 class="text-xs font-medium text-[var(--color-text-primary)] mb-1">其他配饰</h4>
                                    <p class="text-xs text-[var(--color-text-secondary)]">
                                        深色或蓝色系围巾，银框眼镜，腕表选择银色金属表带。
                                    </p>
                                </div>
                            </div>
                        </div>
                        
                        <p class="text-xs text-[var(--color-text-secondary)]">
                            今日适合佩戴款式简约的配饰，不宜过于繁复。配饰数量控制在2-3件为宜，避免过多干扰能量流动。
                        </p>
                    </div>
                    
                    <!-- 配饰示例 -->
                    <div class="grid grid-cols-2 gap-3 mb-4">
                        <div class="enhanced-glass rounded-xl overflow-hidden">
                            <img src="https://images.unsplash.com/photo-1599643477877-530eb83abc8e" 
                                 class="w-full aspect-square object-cover" alt="首饰示例">
                            <div class="p-2">
                                <p class="text-[10px] text-[var(--color-text-secondary)]">
                                    银色简约项链
                                </p>
                            </div>
                        </div>
                        
                        <div class="enhanced-glass rounded-xl overflow-hidden">
                            <img src="https://images.unsplash.com/photo-1617627143750-d86bc21e42bb" 
                                 class="w-full aspect-square object-cover" alt="包包示例">
                            <div class="p-2">
                                <p class="text-[10px] text-[var(--color-text-secondary)]">
                                    深色优雅手提包
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 妆容建议内容 -->
                <div class="w-full px-1" id="content-3">
                    <!-- 整体妆容风格 -->
                    <div class="enhanced-glass rounded-xl p-4 mb-4">
                        <h3 class="text-sm font-medium text-[var(--color-text-primary)] mb-3">
                            <i class="fas fa-sparkles mr-2 text-[#b8c6db]"></i>
                            整体妆容风格
                        </h3>
                        
                        <p class="text-xs text-[var(--color-text-secondary)] mb-3">
                            今日妆容宜以清透自然为主，强调水润感，突出肌肤的通透与光泽，避免厚重感。妆容中可适当融入蓝色、紫色等水属性色彩，增强水行能量。
                        </p>
                        
                        <div class="flex flex-wrap gap-2">
                            <span class="text-[10px] px-3 py-1 rounded-full bg-white/30 text-[var(--color-text-secondary)]">
                                水润通透
                            </span>
                            <span class="text-[10px] px-3 py-1 rounded-full bg-white/30 text-[var(--color-text-secondary)]">
                                清新自然
                            </span>
                            <span class="text-[10px] px-3 py-1 rounded-full bg-white/30 text-[var(--color-text-secondary)]">
                                轻薄透亮
                            </span>
                            <span class="text-[10px] px-3 py-1 rounded-full bg-white/30 text-[var(--color-text-secondary)]">
                                冷色调点缀
                            </span>
                        </div>
                    </div>
                    
                    <!-- 部位妆容建议 -->
                    <div class="enhanced-glass rounded-xl p-4 mb-4">
                        <h3 class="text-sm font-medium text-[var(--color-text-primary)] mb-3">
                            <i class="fas fa-magic mr-2 text-[#b8c6db]"></i>
                            部位妆容建议
                        </h3>
                        
                        <div class="space-y-3 mb-3">
                            <div class="flex">
                                <div class="w-6 h-6 rounded-full bg-white/20 flex items-center justify-center mr-2 flex-shrink-0">
                                    <i class="fas fa-camera-retro text-[10px] text-[#b8c6db]"></i>
                                </div>
                                <div>
                                    <h4 class="text-xs font-medium text-[var(--color-text-primary)] mb-1">底妆</h4>
                                    <p class="text-xs text-[var(--color-text-secondary)]">
                                        清透水润的妆感，使用保湿型粉底，少量遮瑕，突出肌肤通透感。定妆以保湿喷雾替代粉饼，增加水润效果。
                                    </p>
                                </div>
                            </div>
                            
                            <div class="flex">
                                <div class="w-6 h-6 rounded-full bg-white/20 flex items-center justify-center mr-2 flex-shrink-0">
                                    <i class="fas fa-eye text-[10px] text-[#b8c6db]"></i>
                                </div>
                                <div>
                                    <h4 class="text-xs font-medium text-[var(--color-text-primary)] mb-1">眼妆</h4>
                                    <p class="text-xs text-[var(--color-text-secondary)]">
                                        淡蓝色或淡紫色眼影打底，深蓝色或灰色加深眼尾，增加深邃感。睫毛膏选择纤长型，避免浓密感，保持灵动。
                                    </p>
                                </div>
                            </div>
                            
                            <div class="flex">
                                <div class="w-6 h-6 rounded-full bg-white/20 flex items-center justify-center mr-2 flex-shrink-0">
                                    <i class="fas fa-smile text-[10px] text-[#b8c6db]"></i>
                                </div>
                                <div>
                                    <h4 class="text-xs font-medium text-[var(--color-text-primary)] mb-1">唇妆</h4>
                                    <p class="text-xs text-[var(--color-text-secondary)]">
                                        选择带有蓝调的淡粉色或淡珊瑚色唇膏，质地偏润泽型或水润型，避免哑光质地。可以叠加透明唇彩增加水润感。
                                    </p>
                                </div>
                            </div>
                            
                            <div class="flex">
                                <div class="w-6 h-6 rounded-full bg-white/20 flex items-center justify-center mr-2 flex-shrink-0">
                                    <i class="fas fa-paint-brush text-[10px] text-[#b8c6db]"></i>
                                </div>
                                <div>
                                    <h4 class="text-xs font-medium text-[var(--color-text-primary)] mb-1">腮红</h4>
                                    <p class="text-xs text-[var(--color-text-secondary)]">
                                        选择淡粉色或淡紫色调腮红，轻轻带过苹果肌，手法自然向外晕染，打造清新气色。奶油质地比粉质更适合今日妆容。
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 妆容色板 -->
                    <div class="enhanced-glass rounded-xl p-4 mb-4">
                        <h3 class="text-sm font-medium text-[var(--color-text-primary)] mb-3">
                            <i class="fas fa-palette mr-2 text-[#b8c6db]"></i>
                            推荐色板
                        </h3>
                        
                        <div class="grid grid-cols-5 gap-2 mb-3">
                            <!-- 底妆色 -->
                            <div class="flex flex-col items-center">
                                <div class="w-10 h-10 rounded-full bg-[#f5e6d3] border border-white/60"></div>
                                <span class="text-[10px] text-[var(--color-text-secondary)] mt-1">底妆</span>
                            </div>
                            
                            <!-- 眼影色1 -->
                            <div class="flex flex-col items-center">
                                <div class="w-10 h-10 rounded-full bg-[#d6e6ff] border border-white/60"></div>
                                <span class="text-[10px] text-[var(--color-text-secondary)] mt-1">眼影1</span>
                            </div>
                            
                            <!-- 眼影色2 -->
                            <div class="flex flex-col items-center">
                                <div class="w-10 h-10 rounded-full bg-[#8ca3c9] border border-white/60"></div>
                                <span class="text-[10px] text-[var(--color-text-secondary)] mt-1">眼影2</span>
                            </div>
                            
                            <!-- 腮红色 -->
                            <div class="flex flex-col items-center">
                                <div class="w-10 h-10 rounded-full bg-[#edc5cd] border border-white/60"></div>
                                <span class="text-[10px] text-[var(--color-text-secondary)] mt-1">腮红</span>
                            </div>
                            
                            <!-- 唇色 -->
                            <div class="flex flex-col items-center">
                                <div class="w-10 h-10 rounded-full bg-[#e6a4a6] border border-white/60"></div>
                                <span class="text-[10px] text-[var(--color-text-secondary)] mt-1">唇色</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 妆容示例 -->
                    <div class="enhanced-glass rounded-xl overflow-hidden mb-4">
                        <img src="https://images.unsplash.com/photo-1503104834685-7205e8607eb9" 
                             class="w-full aspect-[4/5] object-cover" alt="妆容示例">
                        <div class="p-3">
                            <h4 class="text-xs font-medium text-[var(--color-text-primary)] mb-1">参考妆容</h4>
                            <p class="text-[10px] text-[var(--color-text-secondary)]">
                                水润通透底妆，淡蓝紫色调眼影，自然粉嫩腮红，水润质感唇妆。
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 能量提升提示 -->
        <div class="enhanced-glass rounded-xl p-4 mb-10">
            <h3 class="text-sm font-medium text-[var(--color-text-primary)] mb-2 flex items-center">
                <i class="fas fa-lightbulb text-[#ffeaa7] mr-2"></i>
                能量提升小贴士
            </h3>
            <p class="text-xs text-[var(--color-text-secondary)]">
                今日佩戴蓝色系配饰时，可以在内心默念"流水潺潺，智慧源源不断"，增强与水行的能量连接。
            </p>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const tabs = document.querySelectorAll('[id^="tab-"]');
            const tabContent = document.getElementById('tab-content');
            let activeTab = 1;
            
            // 初始化
            updateTabUI();
            
            // 添加标签点击事件
            tabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    activeTab = parseInt(this.id.split('-')[1]);
                    updateTabUI();
                });
            });
            
            function updateTabUI() {
                // 更新标签样式
                tabs.forEach((tab, index) => {
                    if (index + 1 === activeTab) {
                        tab.classList.add('tab-active');
                        tab.classList.remove('tab-inactive');
                    } else {
                        tab.classList.remove('tab-active');
                        tab.classList.add('tab-inactive');
                    }
                });
                
                // 滑动内容
                tabContent.style.transform = `translateX(-${(activeTab - 1) * 33.333}%)`;
            }
        });
    </script>
</body>
</html> 