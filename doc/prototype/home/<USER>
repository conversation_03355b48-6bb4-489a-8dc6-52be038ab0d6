                    <div class="flex justify-end mt-2">
                        <button class="glass-button text-xs px-3 py-1 rounded-full" data-action="view-detail">
                            查看详情
                        </button>
                    </div>

                // 查看详情按钮点击事件
                const detailButtons = document.querySelectorAll('button.glass-button[data-action="view-detail"]');
                detailButtons.forEach(button => {
                    button.addEventListener('click', function() {
                        const card = this.closest('.outfit-card');
                        if (card) {
                            const outfitId = card.getAttribute('data-outfit-id');
                            window.location.href = `outfit_detail.html?id=${outfitId}`;
                        }
                    });
                }); 