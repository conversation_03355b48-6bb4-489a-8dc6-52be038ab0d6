<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <title>衣物详情 - 穿搭推荐小程序</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --color-text-primary: #333333;
            --color-text-secondary: #666666;
            --color-text-tertiary: #999999;
        }
        
        /* 隐藏滚动条 */
        ::-webkit-scrollbar {
            display: none;
        }
        
        * {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        
        body {
            overflow-y: scroll;
            -webkit-overflow-scrolling: touch;
        }

        /* 内容区域统一间距 */
        .content-container {
            padding-left: 5%;
            padding-right: 5%;
        }
        
        .gradient-background {
            background: linear-gradient(135deg, #c2e9fb 0%, #d8c6fd 100%);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: -1;
        }
        
        .enhanced-glass {
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.05);
        }
        
        .glass-button {
            background: rgba(255, 255, 255, 0.4);
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.5);
            transition: all 0.3s ease;
        }
        
        .glass-button:hover {
            background: rgba(255, 255, 255, 0.6);
        }
        
        .glass-button:active {
            transform: scale(0.95);
        }
        
        /* 标签样式 */
        .tag {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 100px;
            background: rgba(255, 255, 255, 0.3);
            font-size: 10px;
            margin-right: 4px;
            margin-bottom: 4px;
            color: var(--color-text-secondary);
        }
        
        /* 五行标识统一样式 */
        .wuxing-jin {
            background: linear-gradient(135deg, #ffffff, #f0f0f0);
            color: #1a1a1a;
        }
        
        .wuxing-mu {
            background: linear-gradient(135deg, #a8e6cf, #73c1a8);
            color: #1a1a1a;
        }
        
        .wuxing-shui {
            background: linear-gradient(135deg, #b8c6db, #648dae);
            color: #ffffff;
        }
        
        .wuxing-huo {
            background: linear-gradient(135deg, #ff9a9e, #ff5458);
            color: #ffffff;
        }
        
        .wuxing-tu {
            background: linear-gradient(135deg, #ffeaa7, #ffc25c);
            color: #1a1a1a;
        }
        
        /* 场合标签统一样式 */
        .occasion-tag {
            display: inline-flex;
            align-items: center;
            padding: 4px 8px;
            border-radius: 100px;
            background: rgba(255, 255, 255, 0.3);
            font-size: 10px;
            margin-right: 4px;
            margin-bottom: 4px;
            color: var(--color-text-secondary);
        }
        
        .occasion-tag i {
            margin-right: 4px;
            font-size: 8px;
        }
        
        /* 标签页样式 */
        .tab-active {
            color: var(--color-text-primary);
            border-bottom: 2px solid #73c1a8;
            font-weight: 500;
        }
        
        .tab-inactive {
            color: var(--color-text-secondary);
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        /* 图片缩放效果 */
        .zoom-container {
            position: relative;
            overflow: hidden;
            border-radius: 12px;
        }
        
        .zoom-image {
            transition: transform 0.3s ease-out;
        }
        
        .zoom-container:hover .zoom-image {
            transform: scale(1.05);
        }
        
        /* 确认删除模态框 */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 50;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.3s ease;
        }
        
        /* 悬浮按钮样式 */
        .floating-button {
            position: fixed;
            right: 20px;
            bottom: 96px;
            width: 56px;
            height: 56px;
            border-radius: 28px;
            background: rgba(255, 255, 255, 0.4);
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.5);
            display: flex;
            align-items: center;
            justify-center: center;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            z-index: 40;
        }
        
        .floating-button:hover {
            transform: scale(1.05);
            box-shadow: 0 12px 36px rgba(0, 0, 0, 0.15);
        }
        
        .floating-button:active {
            transform: scale(0.95);
        }

        .glow-icon {
            box-shadow: 0 0 15px rgba(255, 255, 255, 0.8);
        }
        
        @keyframes modal-pop {
            0% { transform: scale(0.9); opacity: 0; }
            100% { transform: scale(1); opacity: 1; }
        }
    </style>
</head>
<body>
    <!-- 渐变背景 -->
    <div class="gradient-background"></div>

    <!-- AI搭配悬浮按钮 -->
    <a href="../outfit/custom_outfit.html" class="floating-button glass-button glow-icon flex items-center justify-center">
        <i class="fas fa-wand-magic-sparkles text-[var(--color-text-primary)] text-xl"></i>
    </a>

    <!-- 顶部导航 -->
    <div class="sticky top-0 bg-white/30 backdrop-blur-md z-40 shadow-sm">
        <div class="flex items-center justify-between p-4">
            <div class="flex items-center">
                <a href="../pages/wardrobe.html" class="mr-4">
                    <i class="fas fa-arrow-left text-[var(--color-text-primary)]"></i>
                </a>
                <h1 class="text-sm font-semibold text-[var(--color-text-primary)]">衣物详情</h1>
            </div>
          
        </div>
    </div>

    <!-- 主内容区 -->
    <div class="content-container py-4 pb-20">
        <!-- 衣物图片展示 -->
        <div class="mb-4">
            <div class="zoom-container enhanced-glass overflow-hidden">
                <img src="https://images.unsplash.com/photo-1434389677669-e08b4cac3105" 
                     alt="白色棉质衬衫" 
                     class="w-full aspect-square object-cover zoom-image">
                     
                <!-- 五行标签 -->
                <div class="absolute top-3 right-3">
                    <span class="text-xs px-2 py-1 rounded-full wuxing-jin flex items-center">
                        <i class="fas fa-coins text-[8px] mr-1"></i>金
                    </span>
                </div>
            </div>
        </div>
        
        <!-- 衣物基本信息 -->
        <div class="enhanced-glass rounded-xl p-4 mb-4">
            <h2 class="text-base font-semibold text-[var(--color-text-primary)] mb-2">白色棉质衬衫</h2>
            
            <div class="flex items-center mb-2">
                <span class="tag">衬衫</span>
                <span class="tag">上装</span>
                <span class="tag">棉质</span>
                <span class="tag">白色</span>
            </div>
            
            <!-- 简化后的详细信息 -->
            <div class="border-t border-white/30 pt-3 mb-3">
                <div class="grid grid-cols-1 gap-3 text-xs">
                    <div class="flex items-start">
                        <span class="text-[var(--color-text-secondary)] w-14">材质：</span>
                        <span class="text-[var(--color-text-primary)] flex-1">棉质 / 薄款 / 无弹性</span>
                    </div>
                    <div class="flex items-start">
                        <span class="text-[var(--color-text-secondary)] w-14">图案：</span>
                        <span class="text-[var(--color-text-primary)] flex-1">纯色</span>
                    </div>
                    <div class="col-span-1">
                        <span class="text-[var(--color-text-secondary)]">适用场合：</span>
                        <div class="flex flex-wrap mt-1">
                            <span class="occasion-tag">
                                <i class="fas fa-coffee"></i>日常休闲
                            </span>
                            <span class="occasion-tag">
                                <i class="fas fa-briefcase"></i>职场商务
                            </span>
                            <span class="occasion-tag">
                                <i class="fas fa-utensils"></i>商务午餐
                            </span>
                            <span class="occasion-tag">
                                <i class="fas fa-handshake"></i>客户洽谈
                            </span>
                        </div>
                    </div>
                    <!-- 季节适应性 -->
                    <div class="col-span-1">
                        <span class="text-[var(--color-text-secondary)]">适合季节：</span>
                        <div class="flex flex-wrap mt-1">
                            <span class="occasion-tag">
                                <i class="fas fa-seedling text-[#a8e6cf]"></i>春季
                            </span>
                            <span class="occasion-tag">
                                <i class="fas fa-sun text-[#ffd57e]"></i>夏季
                            </span>
                            <span class="occasion-tag opacity-30">
                                <i class="fas fa-leaf text-[#ff9a9e]"></i>秋季
                            </span>
                            <span class="occasion-tag opacity-30">
                                <i class="fas fa-snowflake text-[#b8c6db]"></i>冬季
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 备注信息 -->
            <div class="text-xs text-[var(--color-text-secondary)] border-t border-white/30 pt-3">
                <p>2023年6月在UNIQLO购买，面料舒适透气，适合多种场合。建议与深色下装搭配，可增加视觉效果。洗涤时建议低温手洗，避免变形。</p>
                <div class="text-right mt-2 text-[var(--color-text-tertiary)]">添加于：2023-06-15</div>
            </div>
        </div>
        
    
        
        <!-- 标签页内容 -->
        <div id="tab2" class="tab-content active">
            <!-- 搭配推荐 -->
            <div class="enhanced-glass rounded-xl p-4 mb-4">
                <h3 class="text-sm font-medium text-[var(--color-text-primary)] mb-3">推荐搭配</h3>
                
                <!-- 搭配1 -->
                <div class="mb-4">
                    <div class="flex mb-2">
                        <div class="zoom-container w-full aspect-[4/3] overflow-hidden rounded-xl relative">
                            <img src="https://images.unsplash.com/photo-1515886657613-9f3515b0c78f" 
                                 alt="清新白裙搭配" 
                                 class="w-full h-full object-cover zoom-image">
                                 
                            <div class="absolute bottom-2 left-2 right-2 flex justify-between items-center">
                                <div class="px-2 py-1 rounded-full bg-white/60 backdrop-blur-sm">
                                    <span class="text-[10px] text-[var(--color-text-primary)]">清新白裙搭配</span>
                                </div>
                                
                                <div class="star-rating flex items-center px-2 py-1 rounded-full bg-white/60 backdrop-blur-sm">
                                    <i class="fas fa-star text-[8px] text-yellow-400"></i>
                                    <span class="text-[10px] text-[var(--color-text-primary)] ml-1">4.5</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="flex overflow-x-auto -mx-1 pb-2">
                        <img src="https://images.unsplash.com/photo-1434389677669-e08b4cac3105" alt="白色衬衫" class="w-12 h-12 object-cover rounded-lg mx-1 border-2 border-[#a8e6cf]">
                        <img src="https://images.unsplash.com/photo-1577900232427-18219b9166a0" alt="白色A字裙" class="w-12 h-12 object-cover rounded-lg mx-1">
                        <img src="https://images.unsplash.com/photo-1543163521-1bf539c55dd2" alt="米色高跟鞋" class="w-12 h-12 object-cover rounded-lg mx-1">
                        <img src="https://images.unsplash.com/photo-1584917865442-de89df76afd3" alt="蓝色手提包" class="w-12 h-12 object-cover rounded-lg mx-1">
                    </div>
                </div>
                
                <!-- 搭配2 -->
                <div class="mb-4">
                    <div class="flex mb-2">
                        <div class="zoom-container w-full aspect-[4/3] overflow-hidden rounded-xl relative">
                            <img src="https://images.unsplash.com/photo-1601046668428-94ea13437736" 
                                 alt="休闲牛仔裤搭配" 
                                 class="w-full h-full object-cover zoom-image">
                                 
                            <div class="absolute bottom-2 left-2 right-2 flex justify-between items-center">
                                <div class="px-2 py-1 rounded-full bg-white/60 backdrop-blur-sm">
                                    <span class="text-[10px] text-[var(--color-text-primary)]">休闲牛仔裤搭配</span>
                                </div>
                                
                                <div class="star-rating flex items-center px-2 py-1 rounded-full bg-white/60 backdrop-blur-sm">
                                    <i class="fas fa-star text-[8px] text-yellow-400"></i>
                                    <span class="text-[10px] text-[var(--color-text-primary)] ml-1">4.2</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="flex overflow-x-auto -mx-1 pb-2">
                        <img src="https://images.unsplash.com/photo-1434389677669-e08b4cac3105" alt="白色衬衫" class="w-12 h-12 object-cover rounded-lg mx-1 border-2 border-[#a8e6cf]">
                        <img src="https://images.unsplash.com/photo-1591195853828-11db59a44f6b" alt="牛仔裤" class="w-12 h-12 object-cover rounded-lg mx-1">
                        <img src="https://images.unsplash.com/photo-1527736947477-2790e28f3443" alt="黑色平底鞋" class="w-12 h-12 object-cover rounded-lg mx-1">
                        <img src="https://images.unsplash.com/photo-1547949003-9792a18a2645" alt="棕色皮带" class="w-12 h-12 object-cover rounded-lg mx-1">
                    </div>
                </div>
                
                <!-- 搭配3 -->
                <div>
                    <div class="flex mb-2">
                        <div class="zoom-container w-full aspect-[4/3] overflow-hidden rounded-xl relative">
                            <img src="https://images.unsplash.com/photo-1549062572-544a64fb0c56" 
                                 alt="职场西装裤搭配" 
                                 class="w-full h-full object-cover zoom-image">
                                 
                            <div class="absolute bottom-2 left-2 right-2 flex justify-between items-center">
                                <div class="px-2 py-1 rounded-full bg-white/60 backdrop-blur-sm">
                                    <span class="text-[10px] text-[var(--color-text-primary)]">职场西装裤搭配</span>
                                </div>
                                
                                <div class="star-rating flex items-center px-2 py-1 rounded-full bg-white/60 backdrop-blur-sm">
                                    <i class="fas fa-star text-[8px] text-yellow-400"></i>
                                    <span class="text-[10px] text-[var(--color-text-primary)] ml-1">4.7</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="flex overflow-x-auto -mx-1 pb-2">
                        <img src="https://images.unsplash.com/photo-1434389677669-e08b4cac3105" alt="白色衬衫" class="w-12 h-12 object-cover rounded-lg mx-1 border-2 border-[#a8e6cf]">
                        <img src="https://images.unsplash.com/photo-1551163943-3f7418e24885" alt="黑色西装裤" class="w-12 h-12 object-cover rounded-lg mx-1">
                        <img src="https://images.unsplash.com/photo-1543163521-1bf539c55dd2" alt="米色高跟鞋" class="w-12 h-12 object-cover rounded-lg mx-1">
                        <img src="https://images.unsplash.com/photo-1559563458-527698bf5295" alt="黑色手表" class="w-12 h-12 object-cover rounded-lg mx-1">
                    </div>
                </div>
                
                <!-- 查看更多按钮 -->
                <button class="w-full text-center text-xs text-[var(--color-text-primary)] mt-4">
                    查看更多搭配
                    <i class="fas fa-chevron-right ml-1"></i>
                </button>
            </div>
            
            <!-- 收纳和洗涤指南 -->
            <div class="enhanced-glass rounded-xl p-4 mb-4">
                <h3 class="text-sm font-medium text-[var(--color-text-primary)] mb-3">收纳与护理指南</h3>
                
                <!-- 收纳指南 -->
                <div class="mb-4">
                    <div class="flex items-center mb-2">
                        <i class="fas fa-box text-sm text-[#73c1a8] mr-2"></i>
                        <h4 class="text-sm text-[var(--color-text-primary)]">收纳建议</h4>
                    </div>
                    <div class="text-xs text-[var(--color-text-secondary)] pl-6">
                        <div class="flex items-start mb-1">
                            <i class="fas fa-check text-[8px] text-[#73c1a8] mt-1 mr-2"></i>
                            <span>建议使用衣架悬挂，避免折叠产生褶皱</span>
                        </div>
                        <div class="flex items-start mb-1">
                            <i class="fas fa-check text-[8px] text-[#73c1a8] mt-1 mr-2"></i>
                            <span>衣架选用实木或加厚塑料，防止衣服变形</span>
                        </div>
                        <div class="flex items-start">
                            <i class="fas fa-check text-[8px] text-[#73c1a8] mt-1 mr-2"></i>
                            <span>建议使用防尘罩保护，保持通风干燥</span>
                        </div>
                    </div>
                </div>
                
                <!-- 洗涤指南 -->
                <div>
                    <div class="flex items-center mb-2">
                        <i class="fas fa-tint text-sm text-[#73c1a8] mr-2"></i>
                        <h4 class="text-sm text-[var(--color-text-primary)]">洗涤指南</h4>
                    </div>
                    <div class="grid grid-cols-4 gap-2 mb-3">
                        <div class="flex flex-col items-center">
                            <div class="w-8 h-8 rounded-full bg-white/30 flex items-center justify-center mb-1">
                                <i class="fas fa-temperature-low text-xs text-[var(--color-text-secondary)]"></i>
                            </div>
                            <span class="text-[10px] text-[var(--color-text-secondary)]">30℃水温</span>
                        </div>
                        <div class="flex flex-col items-center">
                            <div class="w-8 h-8 rounded-full bg-white/30 flex items-center justify-center mb-1">
                                <i class="fas fa-hand-sparkles text-xs text-[var(--color-text-secondary)]"></i>
                            </div>
                            <span class="text-[10px] text-[var(--color-text-secondary)]">轻柔手洗</span>
                        </div>
                        <div class="flex flex-col items-center">
                            <div class="w-8 h-8 rounded-full bg-white/30 flex items-center justify-center mb-1">
                                <i class="fas fa-wind text-xs text-[var(--color-text-secondary)]"></i>
                            </div>
                            <span class="text-[10px] text-[var(--color-text-secondary)]">自然晾干</span>
                        </div>
                        <div class="flex flex-col items-center">
                            <div class="w-8 h-8 rounded-full bg-white/30 flex items-center justify-center mb-1">
                                <i class="fas fa-ban text-xs text-[var(--color-text-secondary)]"></i>
                            </div>
                            <span class="text-[10px] text-[var(--color-text-secondary)]">禁止烘干</span>
                        </div>
                    </div>
                    <div class="text-xs text-[var(--color-text-secondary)] pl-6">
                        <div class="flex items-start mb-1">
                            <i class="fas fa-exclamation-circle text-[8px] text-[#ff9a9e] mt-1 mr-2"></i>
                            <span>建议使用中性洗涤剂，避免漂白</span>
                        </div>
                        <div class="flex items-start">
                            <i class="fas fa-exclamation-circle text-[8px] text-[#ff9a9e] mt-1 mr-2"></i>
                            <span>深浅衣物分开洗涤，防止染色</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 编辑和删除按钮 -->
        <div class="grid grid-cols-2 gap-3 mb-10 mt-6">
            <button class="glass-button py-3 rounded-xl text-sm text-[var(--color-text-primary)]">
                <i class="fas fa-edit mr-1"></i>
                编辑衣物
            </button>
            
            <button class="glass-button py-3 rounded-xl text-sm text-red-500" id="delete-button">
                <i class="fas fa-trash-alt mr-1"></i>
                删除衣物
            </button>
        </div>
    </div>
    
    <!-- 确认删除模态框 -->
    <div class="modal" id="delete-modal">
        <div class="modal-content enhanced-glass p-5">
            <h3 class="text-base font-medium text-[var(--color-text-primary)] mb-4 text-center">确认删除</h3>
            
            <p class="text-sm text-[var(--color-text-secondary)] mb-4 text-center">
                确定要删除「白色棉质衬衫」吗？此操作不可恢复。
            </p>
            
            <div class="flex gap-3">
                <button class="flex-1 glass-button py-2 rounded-xl text-sm text-[var(--color-text-primary)]" id="cancel-delete">
                    取消
                </button>
                
                <button class="flex-1 glass-button py-2 rounded-xl text-sm text-red-500 font-medium">
                    确认删除
                </button>
            </div>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 删除模态框
            const deleteButton = document.getElementById('delete-button');
            const deleteModal = document.getElementById('delete-modal');
            const cancelDelete = document.getElementById('cancel-delete');
            
            deleteButton.addEventListener('click', function() {
                deleteModal.classList.add('active');
            });
            
            cancelDelete.addEventListener('click', function() {
                deleteModal.classList.remove('active');
            });
            
            // 关闭模态框点击空白处
            deleteModal.addEventListener('click', function(e) {
                if (e.target === deleteModal) {
                    deleteModal.classList.remove('active');
                }
            });
            
            // 更多选项按钮
            const moreOptionsButton = document.getElementById('more-options');
            moreOptionsButton.addEventListener('click', function() {
                alert('更多选项：分享、添加到收藏夹、复制衣物等');
            });
        });
    </script>
</body>
</html> 