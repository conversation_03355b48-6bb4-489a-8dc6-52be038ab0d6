<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <title>衣橱分析 - 穿搭推荐小程序</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --color-text-primary: #333333;
            --color-text-secondary: #666666;
            --color-text-tertiary: #999999;
        }
        
        /* 隐藏滚动条 */
        ::-webkit-scrollbar {
            display: none;
        }
        
        * {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        
        body {
            overflow-y: scroll;
            -webkit-overflow-scrolling: touch;
        }

        /* 内容区域统一间距 */
        .content-container {
            padding-left: 5%;
            padding-right: 5%;
        }
        
        .gradient-background {
            background: linear-gradient(135deg, #c2e9fb 0%, #d8c6fd 100%);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: -1;
        }
        
        .enhanced-glass {
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.05);
        }
        
        .glass-button {
            background: rgba(255, 255, 255, 0.4);
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.5);
            transition: all 0.3s ease;
        }
        
        .glass-button:hover {
            background: rgba(255, 255, 255, 0.6);
        }
        
        /* 饼图样式 */
        .pie-chart {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: conic-gradient(
                rgba(255, 255, 255, 0.7) 0% 30%,
                rgba(168, 230, 207, 0.7) 30% 55%,
                rgba(184, 198, 219, 0.7) 55% 75%,
                rgba(255, 154, 158, 0.7) 75% 85%,
                rgba(255, 234, 167, 0.7) 85% 100%
            );
            position: relative;
        }
        
        .pie-chart::before {
            content: "";
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 50px;
            height: 50px;
            background: rgba(255, 255, 255, 0.7);
            border-radius: 50%;
        }
        
        /* 标签页样式 */
        .tab-active {
            color: var(--color-text-primary);
            border-bottom: 2px solid #73c1a8;
            font-weight: 500;
        }
        
        .tab-inactive {
            color: var(--color-text-secondary);
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        /* 进度条样式 */
        .progress-bar {
            height: 6px;
            border-radius: 3px;
            background: rgba(255, 255, 255, 0.3);
            overflow: hidden;
        }
        
        .progress-value {
            height: 100%;
            border-radius: 3px;
        }
        
        /* 雷达图样式 */
        .radar-chart {
            position: relative;
            width: 100%;
            height: 200px;
        }
        
        .radar-polygon {
            fill: rgba(168, 230, 207, 0.3);
            stroke: rgba(168, 230, 207, 0.8);
            stroke-width: 2;
        }
        
        .radar-axis {
            stroke: rgba(255, 255, 255, 0.2);
            stroke-width: 1;
            stroke-dasharray: 4, 4;
        }
        
        /* 季节圆环图 */
        .season-ring {
            width: 120px;
            height: 120px;
            position: relative;
        }
        
        .season-ring .circle {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            background: conic-gradient(
                rgba(168, 230, 207, 0.7) 0% 30%,
                rgba(255, 213, 126, 0.7) 30% 65%,
                rgba(255, 154, 158, 0.7) 65% 85%,
                rgba(184, 198, 219, 0.7) 85% 100%
            );
            transform: rotate(-90deg);
        }
        
        .season-ring .inner-circle {
            position: absolute;
            width: 80px;
            height: 80px;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.7);
        }
    </style>
</head>
<body>
    <!-- 渐变背景 -->
    <div class="gradient-background"></div>

    <!-- 顶部导航 -->
    <div class="sticky top-0 bg-white/30 backdrop-blur-md z-40 shadow-sm">
        <div class="flex items-center p-4">
            <a href="../pages/wardrobe.html" class="mr-4">
                <i class="fas fa-arrow-left text-[var(--color-text-primary)]"></i>
            </a>
            <h1 class="text-sm font-semibold text-[var(--color-text-primary)]">衣橱分析</h1>
        </div>
    </div>

    <!-- 主内容区 -->
    <div class="content-container py-4 pb-20">
        <!-- 衣橱概况卡片 -->
        <div class="enhanced-glass rounded-xl p-4 mb-4">
            <h2 class="text-sm font-semibold text-[var(--color-text-primary)] mb-3">衣橱概况</h2>
            
            <div class="flex">
                <div class="flex flex-col items-center justify-center">
                    <div class="pie-chart"></div>
                    <p class="text-xs text-[var(--color-text-secondary)] mt-2">类型分布</p>
                </div>
                
                <div class="ml-6 flex-1">
                    <div class="grid grid-cols-2 gap-3">
                        <div>
                            <p class="text-2xl font-bold text-[var(--color-text-primary)]">68</p>
                            <p class="text-xs text-[var(--color-text-secondary)]">衣物总数</p>
                        </div>
                        
                        <div>
                            <p class="text-2xl font-bold text-[var(--color-text-primary)]">15</p>
                            <p class="text-xs text-[var(--color-text-secondary)]">搭配方案</p>
                        </div>
                        
                        <div>
                            <p class="text-2xl font-bold text-[var(--color-text-primary)]">78%</p>
                            <p class="text-xs text-[var(--color-text-secondary)]">利用率</p>
                        </div>
                        
                        <div>
                            <p class="text-2xl font-bold text-[var(--color-text-primary)]">82</p>
                            <p class="text-xs text-[var(--color-text-secondary)]">搭配潜力</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 类型分布图例 -->
            <div class="flex flex-wrap mt-3 pt-3 border-t border-white/20">
                <div class="flex items-center mr-3 mb-1">
                    <span class="inline-block w-3 h-3 rounded-full bg-white mr-1"></span>
                    <span class="text-[10px] text-[var(--color-text-secondary)]">上装(30%)</span>
                </div>
                
                <div class="flex items-center mr-3 mb-1">
                    <span class="inline-block w-3 h-3 rounded-full bg-[#a8e6cf] mr-1"></span>
                    <span class="text-[10px] text-[var(--color-text-secondary)]">下装(25%)</span>
                </div>
                
                <div class="flex items-center mr-3 mb-1">
                    <span class="inline-block w-3 h-3 rounded-full bg-[#b8c6db] mr-1"></span>
                    <span class="text-[10px] text-[var(--color-text-secondary)]">外套(20%)</span>
                </div>
                
                <div class="flex items-center mr-3 mb-1">
                    <span class="inline-block w-3 h-3 rounded-full bg-[#ff9a9e] mr-1"></span>
                    <span class="text-[10px] text-[var(--color-text-secondary)]">鞋履(10%)</span>
                </div>
                
                <div class="flex items-center mb-1">
                    <span class="inline-block w-3 h-3 rounded-full bg-[#ffeaa7] mr-1"></span>
                    <span class="text-[10px] text-[var(--color-text-secondary)]">配饰(15%)</span>
                </div>
            </div>
        </div>
        
        <!-- 标签页导航 -->
        <div class="flex border-b border-white/30 mb-4">
            <button class="flex-1 py-2 text-xs tab-active" data-tab="tab1">五行分析</button>
            <button class="flex-1 py-2 text-xs tab-inactive" data-tab="tab2">季节分布</button>
            <button class="flex-1 py-2 text-xs tab-inactive" data-tab="tab3">优化建议</button>
        </div>
        
        <!-- 标签页内容 -->
        <div id="tab1" class="tab-content active">
            <!-- 五行分析 -->
            <div class="enhanced-glass rounded-xl p-4 mb-4">
                <h3 class="text-sm font-medium text-[var(--color-text-primary)] mb-3">五行能量分布</h3>
                
                <!-- 雷达图 -->
                <div class="radar-chart mb-4">
                    <svg viewBox="0 0 200 200" width="100%" height="100%">
                        <!-- 雷达图背景 -->
                        <polygon points="100,10 190,78 154,182 46,182 10,78" 
                                 fill="none" stroke="rgba(255,255,255,0.2)" stroke-width="1" />
                        <polygon points="100,40 160,88 134,162 66,162 40,88" 
                                 fill="none" stroke="rgba(255,255,255,0.2)" stroke-width="1" />
                        <polygon points="100,70 130,98 114,142 86,142 70,98" 
                                 fill="none" stroke="rgba(255,255,255,0.2)" stroke-width="1" />
                        
                        <!-- 能量填充区域 -->
                        <polygon points="100,25 140,82 125,152 75,152 60,82" 
                                 class="radar-polygon" />
                        
                        <!-- 五个维度轴线 -->
                        <line x1="100" y1="10" x2="100" y2="182" class="radar-axis" />
                        <line x1="10" y1="78" x2="190" y2="78" class="radar-axis" />
                        <line x1="46" y1="182" x2="154" y2="182" class="radar-axis" />
                        <line x1="190" y1="78" x2="46" y2="182" class="radar-axis" />
                        <line x1="10" y1="78" x2="154" y2="182" class="radar-axis" />
                        
                        <!-- 五行标签 -->
                        <text x="100" y="8" text-anchor="middle" fill="rgba(255,255,255,0.9)" 
                              font-size="8">金(35%)</text>
                        <text x="195" y="78" text-anchor="start" fill="rgba(255,255,255,0.9)" 
                              font-size="8">木(25%)</text>
                        <text x="154" y="194" text-anchor="middle" fill="rgba(255,255,255,0.9)" 
                              font-size="8">水(20%)</text>
                        <text x="46" y="194" text-anchor="middle" fill="rgba(255,255,255,0.9)" 
                              font-size="8">火(15%)</text>
                        <text x="5" y="78" text-anchor="end" fill="rgba(255,255,255,0.9)" 
                              font-size="8">土(22%)</text>
                    </svg>
                </div>
                
                <!-- 五行能量条 -->
                <div class="space-y-3">
                    <!-- 金 -->
                    <div>
                        <div class="flex justify-between items-center mb-1">
                            <span class="text-xs text-[var(--color-text-primary)]">金</span>
                            <span class="text-xs text-[var(--color-text-secondary)]">35%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-value bg-white" style="width: 35%"></div>
                        </div>
                    </div>
                    
                    <!-- 木 -->
                    <div>
                        <div class="flex justify-between items-center mb-1">
                            <span class="text-xs text-[var(--color-text-primary)]">木</span>
                            <span class="text-xs text-[var(--color-text-secondary)]">25%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-value bg-[#a8e6cf]" style="width: 25%"></div>
                        </div>
                    </div>
                    
                    <!-- 水 -->
                    <div>
                        <div class="flex justify-between items-center mb-1">
                            <span class="text-xs text-[var(--color-text-primary)]">水</span>
                            <span class="text-xs text-[var(--color-text-secondary)]">20%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-value bg-[#b8c6db]" style="width: 20%"></div>
                        </div>
                    </div>
                    
                    <!-- 火 -->
                    <div>
                        <div class="flex justify-between items-center mb-1">
                            <span class="text-xs text-[var(--color-text-primary)]">火</span>
                            <span class="text-xs text-[var(--color-text-secondary)]">15%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-value bg-[#ff9a9e]" style="width: 15%"></div>
                        </div>
                    </div>
                    
                    <!-- 土 -->
                    <div>
                        <div class="flex justify-between items-center mb-1">
                            <span class="text-xs text-[var(--color-text-primary)]">土</span>
                            <span class="text-xs text-[var(--color-text-secondary)]">22%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-value bg-[#ffeaa7]" style="width: 22%"></div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 五行分析解读 -->
            <div class="enhanced-glass rounded-xl p-4 mb-4">
                <h3 class="text-sm font-medium text-[var(--color-text-primary)] mb-3">五行能量解读</h3>
                
                <p class="text-xs text-[var(--color-text-secondary)] mb-3">
                    您的衣橱以金属性单品为主，整体偏向金和土，水火能量偏弱。这意味着您的穿搭风格偏向干练、整洁，但可能缺乏活力和变化。
                </p>
                
                <div class="space-y-2">
                    <div class="flex">
                        <div class="w-5 h-5 rounded-full bg-white/20 flex items-center justify-center mr-2 flex-shrink-0">
                            <i class="fas fa-plus text-[10px] text-[#a8e6cf]"></i>
                        </div>
                        <p class="text-xs text-[var(--color-text-secondary)]">
                            <span class="font-medium text-[var(--color-text-primary)]">优势：</span>
                            金土搭配有助于打造稳重、专业的形象，适合职场和正式场合。
                        </p>
                    </div>
                    
                    <div class="flex">
                        <div class="w-5 h-5 rounded-full bg-white/20 flex items-center justify-center mr-2 flex-shrink-0">
                            <i class="fas fa-minus text-[10px] text-[#ff9a9e]"></i>
                        </div>
                        <p class="text-xs text-[var(--color-text-secondary)]">
                            <span class="font-medium text-[var(--color-text-primary)]">不足：</span>
                            火行能量不足，可能缺乏活力和热情；水行能量不足，灵活度和变化性略显不足。
                        </p>
                    </div>
                </div>
            </div>
        </div>
        
        <div id="tab2" class="tab-content">
            <!-- 季节分布分析 -->
            <div class="enhanced-glass rounded-xl p-4 mb-4">
                <h3 class="text-sm font-medium text-[var(--color-text-primary)] mb-3">季节适应性分布</h3>
                
                <div class="flex">
                    <div class="flex flex-col items-center justify-center">
                        <div class="season-ring">
                            <div class="circle"></div>
                            <div class="inner-circle flex items-center justify-center">
                                <div class="text-center">
                                    <p class="text-xs font-medium text-[var(--color-text-primary)]">118</p>
                                    <p class="text-[10px] text-[var(--color-text-secondary)]">季节标记</p>
                                </div>
                            </div>
                        </div>
                        <p class="text-xs text-[var(--color-text-secondary)] mt-2">季节分布</p>
                    </div>
                    
                    <div class="ml-6 flex-1">
                        <div class="space-y-3">
                            <!-- 春季 -->
                            <div>
                                <div class="flex justify-between items-center mb-1">
                                    <div class="flex items-center">
                                        <i class="fas fa-seedling text-xs text-[#a8e6cf] mr-2"></i>
                                        <span class="text-xs text-[var(--color-text-primary)]">春季</span>
                                    </div>
                                    <span class="text-xs text-[var(--color-text-secondary)]">30%</span>
                                </div>
                                <div class="progress-bar">
                                    <div class="progress-value bg-[#a8e6cf]" style="width: 30%"></div>
                                </div>
                            </div>
                            
                            <!-- 夏季 -->
                            <div>
                                <div class="flex justify-between items-center mb-1">
                                    <div class="flex items-center">
                                        <i class="fas fa-sun text-xs text-[#ffd57e] mr-2"></i>
                                        <span class="text-xs text-[var(--color-text-primary)]">夏季</span>
                                    </div>
                                    <span class="text-xs text-[var(--color-text-secondary)]">35%</span>
                                </div>
                                <div class="progress-bar">
                                    <div class="progress-value bg-[#ffd57e]" style="width: 35%"></div>
                                </div>
                            </div>
                            
                            <!-- 秋季 -->
                            <div>
                                <div class="flex justify-between items-center mb-1">
                                    <div class="flex items-center">
                                        <i class="fas fa-leaf text-xs text-[#ff9a9e] mr-2"></i>
                                        <span class="text-xs text-[var(--color-text-primary)]">秋季</span>
                                    </div>
                                    <span class="text-xs text-[var(--color-text-secondary)]">20%</span>
                                </div>
                                <div class="progress-bar">
                                    <div class="progress-value bg-[#ff9a9e]" style="width: 20%"></div>
                                </div>
                            </div>
                            
                            <!-- 冬季 -->
                            <div>
                                <div class="flex justify-between items-center mb-1">
                                    <div class="flex items-center">
                                        <i class="fas fa-snowflake text-xs text-[#b8c6db] mr-2"></i>
                                        <span class="text-xs text-[var(--color-text-primary)]">冬季</span>
                                    </div>
                                    <span class="text-xs text-[var(--color-text-secondary)]">15%</span>
                                </div>
                                <div class="progress-bar">
                                    <div class="progress-value bg-[#b8c6db]" style="width: 15%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 季节分析解读 -->
            <div class="enhanced-glass rounded-xl p-4 mb-4">
                <h3 class="text-sm font-medium text-[var(--color-text-primary)] mb-3">季节分布解读</h3>
                
                <p class="text-xs text-[var(--color-text-secondary)] mb-3">
                    您的衣橱以春夏季单品为主，占比达到65%，秋冬季单品较少。这与您所在的气候区域基本匹配，但可能面临季节转换时的穿搭困难。
                </p>
                
                <div class="space-y-2">
                    <div class="flex">
                        <div class="w-5 h-5 rounded-full bg-white/20 flex items-center justify-center mr-2 flex-shrink-0">
                            <i class="fas fa-balance-scale text-[10px] text-[#b8c6db]"></i>
                        </div>
                        <p class="text-xs text-[var(--color-text-secondary)]">
                            <span class="font-medium text-[var(--color-text-primary)]">平衡性：</span>
                            春夏季衣物比例合理，但秋季和冬季衣物明显不足，建议适当增加过渡季节和寒冷季节的单品。
                        </p>
                    </div>
                    
                    <div class="flex">
                        <div class="w-5 h-5 rounded-full bg-white/20 flex items-center justify-center mr-2 flex-shrink-0">
                            <i class="fas fa-retweet text-[10px] text-[#a8e6cf]"></i>
                        </div>
                        <p class="text-xs text-[var(--color-text-secondary)]">
                            <span class="font-medium text-[var(--color-text-primary)]">跨季适应性：</span>
                            您的衣橱中有42%的单品可以跨季节穿着，灵活性较好，但仍建议增加一些秋冬季基础单品。
                        </p>
                    </div>
                </div>
            </div>
        </div>
        
        <div id="tab3" class="tab-content">
            <!-- 衣橱优化建议 -->
            <div class="enhanced-glass rounded-xl p-4 mb-4">
                <h3 class="text-sm font-medium text-[var(--color-text-primary)] mb-3">优化建议</h3>
                
                <div class="space-y-3">
                    <div class="flex">
                        <div class="w-8 h-8 rounded-full bg-white/20 flex items-center justify-center mr-3 flex-shrink-0">
                            <i class="fas fa-tint text-[#b8c6db]"></i>
                        </div>
                        <div>
                            <h4 class="text-xs font-medium text-[var(--color-text-primary)] mb-1">增加水行单品</h4>
                            <p class="text-xs text-[var(--color-text-secondary)]">
                                建议添加蓝色、黑色系服饰，尤其是流线型、柔软质地的单品，如丝质衬衫、雪纺上衣等，提升衣橱灵活性。
                            </p>
                        </div>
                    </div>
                    
                    <div class="flex">
                        <div class="w-8 h-8 rounded-full bg-white/20 flex items-center justify-center mr-3 flex-shrink-0">
                            <i class="fas fa-fire text-[#ff9a9e]"></i>
                        </div>
                        <div>
                            <h4 class="text-xs font-medium text-[var(--color-text-primary)] mb-1">补充火行单品</h4>
                            <p class="text-xs text-[var(--color-text-secondary)]">
                                适当增加红色、紫色系单品，如亮色毛衣、红色外套等，为形象增添活力和温暖感。
                            </p>
                        </div>
                    </div>
                    
                    <div class="flex">
                        <div class="w-8 h-8 rounded-full bg-white/20 flex items-center justify-center mr-3 flex-shrink-0">
                            <i class="fas fa-snowflake text-[#b8c6db]"></i>
                        </div>
                        <div>
                            <h4 class="text-xs font-medium text-[var(--color-text-primary)] mb-1">增加冬季单品</h4>
                            <p class="text-xs text-[var(--color-text-secondary)]">
                                冬季衣物比例过低，建议添加保暖外套、厚毛衣等基础单品，尤其是深色系和中性色调的款式，提高实用性。
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 缺失单品推荐 -->
            <div class="enhanced-glass rounded-xl p-4 mb-4">
                <h3 class="text-sm font-medium text-[var(--color-text-primary)] mb-3">缺失单品推荐</h3>
                
                <div class="space-y-4">
                    <!-- 单品1 -->
                    <div class="flex">
                        <div class="w-20 h-20 rounded-xl overflow-hidden flex-shrink-0">
                            <img src="https://images.unsplash.com/photo-1551028719-00167b16eac5" 
                                 alt="深蓝色丝质衬衫" class="w-full h-full object-cover">
                        </div>
                        <div class="ml-3 flex-1">
                            <h4 class="text-xs font-medium text-[var(--color-text-primary)]">丝质衬衫</h4>
                            <div class="flex flex-wrap mt-1">
                                <span class="text-[10px] px-2 py-0.5 rounded-full bg-white/30 text-[var(--color-text-secondary)] mr-1 mb-1">蓝色系</span>
                                <span class="text-[10px] px-2 py-0.5 rounded-full bg-white/30 text-[var(--color-text-secondary)] mr-1 mb-1">水行</span>
                                <span class="text-[10px] px-2 py-0.5 rounded-full bg-white/30 text-[var(--color-text-secondary)] mb-1">春夏秋</span>
                            </div>
                            <p class="text-[10px] text-[var(--color-text-secondary)] mt-1">
                                增强水行能量，提升衣橱灵活性和搭配可能性
                            </p>
                        </div>
                    </div>
                    
                    <!-- 单品2 -->
                    <div class="flex">
                        <div class="w-20 h-20 rounded-xl overflow-hidden flex-shrink-0">
                            <img src="https://images.unsplash.com/photo-1520207588543-570825fec5c1" 
                                 alt="红色针织衫" class="w-full h-full object-cover">
                        </div>
                        <div class="ml-3 flex-1">
                            <h4 class="text-xs font-medium text-[var(--color-text-primary)]">针织毛衣</h4>
                            <div class="flex flex-wrap mt-1">
                                <span class="text-[10px] px-2 py-0.5 rounded-full bg-white/30 text-[var(--color-text-secondary)] mr-1 mb-1">红色系</span>
                                <span class="text-[10px] px-2 py-0.5 rounded-full bg-white/30 text-[var(--color-text-secondary)] mr-1 mb-1">火行</span>
                                <span class="text-[10px] px-2 py-0.5 rounded-full bg-white/30 text-[var(--color-text-secondary)] mb-1">秋冬</span>
                            </div>
                            <p class="text-[10px] text-[var(--color-text-secondary)] mt-1">
                                补充火行能量，增加温暖感和活力，适合秋冬季节
                            </p>
                        </div>
                    </div>
                    
                    <!-- 单品3 -->
                    <div class="flex">
                        <div class="w-20 h-20 rounded-xl overflow-hidden flex-shrink-0">
                            <img src="https://images.unsplash.com/photo-1545594861-3bef43ff2fc8" 
                                 alt="深色羊毛大衣" class="w-full h-full object-cover">
                        </div>
                        <div class="ml-3 flex-1">
                            <h4 class="text-xs font-medium text-[var(--color-text-primary)]">羊毛大衣</h4>
                            <div class="flex flex-wrap mt-1">
                                <span class="text-[10px] px-2 py-0.5 rounded-full bg-white/30 text-[var(--color-text-secondary)] mr-1 mb-1">深色系</span>
                                <span class="text-[10px] px-2 py-0.5 rounded-full bg-white/30 text-[var(--color-text-secondary)] mr-1 mb-1">水/土</span>
                                <span class="text-[10px] px-2 py-0.5 rounded-full bg-white/30 text-[var(--color-text-secondary)] mb-1">冬季</span>
                            </div>
                            <p class="text-[10px] text-[var(--color-text-secondary)] mt-1">
                                填补冬季单品空缺，提高衣橱完整性和季节适应能力
                            </p>
                        </div>
                    </div>
                </div>
                
                <button class="w-full text-center text-xs text-[var(--color-text-primary)] mt-3">
                    查看更多推荐
                    <i class="fas fa-chevron-right ml-1"></i>
                </button>
            </div>
        </div>
        
        <!-- 搭配改进建议 -->
        <div class="enhanced-glass rounded-xl p-4 mb-6">
            <h3 class="text-sm font-medium text-[var(--color-text-primary)] mb-3">搭配改进建议</h3>
            
            <p class="text-xs text-[var(--color-text-secondary)] mb-3">
                根据您的衣橱结构，我们发现以下几个可能的搭配改进点：
            </p>
            
            <div class="space-y-2">
                <div class="flex">
                    <div class="w-5 h-5 rounded-full bg-white/20 flex items-center justify-center mr-2 flex-shrink-0">
                        <i class="fas fa-lightbulb text-[10px] text-[#ffd57e]"></i>
                    </div>
                    <p class="text-xs text-[var(--color-text-secondary)]">
                        <span class="font-medium text-[var(--color-text-primary)]">五行平衡：</span>
                        尝试在每套搭配中至少包含2-3种五行属性，通过混搭不同属性单品提升整体平衡感。
                    </p>
                </div>
                
                <div class="flex">
                    <div class="w-5 h-5 rounded-full bg-white/20 flex items-center justify-center mr-2 flex-shrink-0">
                        <i class="fas fa-lightbulb text-[10px] text-[#ffd57e]"></i>
                    </div>
                    <p class="text-xs text-[var(--color-text-secondary)]">
                        <span class="font-medium text-[var(--color-text-primary)]">跨季活用：</span>
                        通过叠穿技巧，将春夏单品合理过渡到秋季，如搭配薄款外套或围巾，延长衣物使用周期。
                    </p>
                </div>
                
                <div class="flex">
                    <div class="w-5 h-5 rounded-full bg-white/20 flex items-center justify-center mr-2 flex-shrink-0">
                        <i class="fas fa-lightbulb text-[10px] text-[#ffd57e]"></i>
                    </div>
                    <p class="text-xs text-[var(--color-text-secondary)]">
                        <span class="font-medium text-[var(--color-text-primary)]">单品重组：</span>
                        您的白色上衣可与8种不同下装搭配，创造更多穿搭变化，提高衣橱单品利用率。
                    </p>
                </div>
            </div>
            
            <button class="w-full glass-button py-2 rounded-xl text-sm text-[var(--color-text-primary)] mt-4">
                <i class="fas fa-magic mr-1"></i>
                获取个性化搭配方案
            </button>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 标签页切换
            const tabButtons = document.querySelectorAll('[data-tab]');
            tabButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // 更新标签样式
                    tabButtons.forEach(btn => {
                        btn.classList.remove('tab-active');
                        btn.classList.add('tab-inactive');
                    });
                    this.classList.add('tab-active');
                    this.classList.remove('tab-inactive');
                    
                    // 显示对应内容
                    const tabId = this.getAttribute('data-tab');
                    document.querySelectorAll('.tab-content').forEach(content => {
                        content.classList.remove('active');
                    });
                    document.getElementById(tabId).classList.add('active');
                });
            });
            
            // 图表动画
            setTimeout(function() {
                const radarPolygon = document.querySelector('.radar-polygon');
                if (radarPolygon) {
                    radarPolygon.style.opacity = '0';
                    radarPolygon.style.transition = 'opacity 0.5s ease';
                    
                    setTimeout(function() {
                        radarPolygon.style.opacity = '1';
                    }, 100);
                }
            }, 300);
        });
    </script>
</body>
</html> 