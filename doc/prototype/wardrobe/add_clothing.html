<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <title>添加衣物 - 穿搭推荐小程序</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --color-text-primary: #333333;
            --color-text-secondary: #666666;
            --color-text-tertiary: #999999;
        }
        
        /* 隐藏滚动条 */
        ::-webkit-scrollbar {
            display: none;
        }
        
        * {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        
        body {
            overflow-y: scroll;
            -webkit-overflow-scrolling: touch;
        }

        /* 内容区域统一间距 */
        .content-container {
            padding-left: 5%;
            padding-right: 5%;
        }
        
        .gradient-background {
            background: linear-gradient(135deg, #c2e9fb 0%, #d8c6fd 100%);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: -1;
        }
        
        .enhanced-glass {
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.05);
        }
        
        .glass-button {
            background: rgba(255, 255, 255, 0.4);
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.5);
            transition: all 0.3s ease;
        }
        
        .glass-button:hover {
            background: rgba(255, 255, 255, 0.6);
        }
        
        /* 文件上传 */
        .upload-container {
            position: relative;
            width: 100%;
            overflow: hidden;
        }
        
        .upload-container input[type="file"] {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            cursor: pointer;
        }
        
        /* 自定义选择器 */
        .custom-selector {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            padding: 8px 12px;
            appearance: none;
            color: var(--color-text-primary);
            width: 100%;
            font-size: 14px;
        }
        
        .custom-selector:focus {
            outline: none;
            border-color: rgba(255, 255, 255, 0.5);
        }
        
        /* 加载动画 */
        .loader {
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        /* AI识别动画 */
        .ai-scan {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(to bottom, 
                rgba(168, 230, 207, 0.3) 0%, 
                rgba(255, 255, 255, 0) 50%, 
                rgba(168, 230, 207, 0.3) 100%);
            background-size: 100% 200%;
            animation: scan 2s linear infinite;
            opacity: 0;
            pointer-events: none;
        }
        
        .ai-scanning .ai-scan {
            opacity: 1;
        }
        
        @keyframes scan {
            0% { background-position: 0 -100%; }
            100% { background-position: 0 100%; }
        }
        
        /* 季节标签 */
        .season-tag {
            position: relative;
            display: inline-block;
            padding: 8px;
            margin-right: 8px;
        }
        
        .season-tag input[type="checkbox"] {
            position: absolute;
            opacity: 0;
        }
        
        .season-tag label {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            width: 60px;
            height: 60px;
            border-radius: 12px;
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .season-tag input[type="checkbox"]:checked + label {
            background: rgba(168, 230, 207, 0.4);
            border-color: rgba(168, 230, 207, 0.8);
        }
        
        /* 颜色选择器 */
        .color-selector {
            display: inline-block;
            margin-right: 8px;
            margin-bottom: 8px;
        }
        
        .color-selector input[type="radio"] {
            position: absolute;
            opacity: 0;
        }
        
        .color-selector label {
            display: block;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            border: 2px solid rgba(255, 255, 255, 0.5);
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .color-selector input[type="radio"]:checked + label {
            transform: scale(1.2);
            box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.8);
        }
    </style>
</head>
<body>
    <!-- 渐变背景 -->
    <div class="gradient-background"></div>

    <!-- 顶部导航 -->
    <div class="sticky top-0 bg-white/30 backdrop-blur-md z-40 shadow-sm">
        <div class="flex items-center justify-between p-4">
            <div class="flex items-center">
                <a href="../pages/wardrobe.html" class="mr-4">
                    <i class="fas fa-arrow-left text-[var(--color-text-primary)]"></i>
                </a>
                <h1 class="text-sm font-semibold text-[var(--color-text-primary)]">添加衣物</h1>
            </div>
            <button class="text-sm text-[var(--color-text-primary)] save-button">保存</button>
        </div>
    </div>

    <!-- 主内容区 -->
    <div class="content-container py-4 pb-20">
        <!-- 上传图片区域 -->
        <div class="mb-6">
            <div class="enhanced-glass rounded-xl overflow-hidden mb-2">
                <!-- 上传容器 -->
                <div class="upload-container ai-scanning relative w-full aspect-square flex flex-col items-center justify-center" id="upload-container">
                    <!-- 预览区域 -->
                    <div class="absolute inset-0 flex items-center justify-center" id="preview-container">
                        <img src="https://images.unsplash.com/photo-1434389677669-e08b4cac3105" alt="衣物照片" class="w-full h-full object-cover" id="preview-image">
                        <!-- AI扫描动画 -->
                        <div class="ai-scan"></div>
                    </div>
                    
                    <!-- 上传提示 -->
                    <div class="text-center z-10 hidden" id="upload-placeholder">
                        <div class="w-16 h-16 rounded-full bg-white/30 backdrop-blur-sm flex items-center justify-center mx-auto mb-3">
                            <i class="fas fa-camera text-[var(--color-text-primary)] text-xl"></i>
                        </div>
                        <p class="text-sm text-[var(--color-text-primary)]">添加衣物照片</p>
                        <p class="text-xs text-[var(--color-text-secondary)] mt-1">拍摄清晰的衣物照片</p>
                    </div>
                    
                    <!-- 文件上传输入 -->
                    <input type="file" accept="image/*" id="upload-input" class="z-20">
                </div>
            </div>
            
           
        </div>
        
        <!-- AI识别结果 -->
        <div class="enhanced-glass rounded-xl p-4 mb-6">
            <div class="flex justify-between items-center mb-3">
                <h3 class="text-sm font-medium text-[var(--color-text-primary)]">AI识别结果</h3>
                <div class="flex items-center">
                    <div class="loader mr-2"></div>
                    <span class="text-xs text-[var(--color-text-secondary)]">识别中...</span>
                </div>
            </div>
            
            <div class="space-y-2">
                <div class="flex justify-between items-center">
                    <span class="text-xs text-[var(--color-text-secondary)]">服装种类</span>
                    <span class="text-xs text-[var(--color-text-primary)]">衬衫</span>
                </div>
                
                <div class="flex justify-between items-center">
                    <span class="text-xs text-[var(--color-text-secondary)]">主要颜色</span>
                    <span class="text-xs text-[var(--color-text-primary)]">白色</span>
                </div>
                
                <div class="flex justify-between items-center">
                    <span class="text-xs text-[var(--color-text-secondary)]">材质推测</span>
                    <span class="text-xs text-[var(--color-text-primary)]">棉质</span>
                </div>
                
                <div class="flex justify-between items-center">
                    <span class="text-xs text-[var(--color-text-secondary)]">五行属性</span>
                    <span class="text-xs text-[var(--color-text-primary)] px-2 py-0.5 rounded-full bg-white">金</span>
                </div>
            </div>
            
            <div class="mt-3 pt-3 border-t border-white/20">
                <p class="text-xs text-[var(--color-text-secondary)]">
                    AI已完成基础识别，您可以修改或补充以下信息
                </p>
            </div>
        </div>
        
        <!-- 衣物信息表单 -->
        <form>
            <!-- 基本信息 -->
            
            
            <!-- 颜色信息 -->
            
            
            <!-- 季节适应性 -->
            <div class="enhanced-glass rounded-xl p-4 mb-4">
                <h3 class="text-sm font-medium text-[var(--color-text-primary)] mb-3">季节适应性</h3>
                
                <div class="flex justify-between">
                    <div class="season-tag">
                        <input type="checkbox" id="season-spring" checked>
                        <label for="season-spring">
                            <i class="fas fa-seedling text-[#a8e6cf] mb-1"></i>
                            <span class="text-[10px] text-[var(--color-text-secondary)]">春季</span>
                        </label>
                    </div>
                    
                    <div class="season-tag">
                        <input type="checkbox" id="season-summer" checked>
                        <label for="season-summer">
                            <i class="fas fa-sun text-[#ffd57e] mb-1"></i>
                            <span class="text-[10px] text-[var(--color-text-secondary)]">夏季</span>
                        </label>
                    </div>
                    
                    <div class="season-tag">
                        <input type="checkbox" id="season-autumn">
                        <label for="season-autumn">
                            <i class="fas fa-leaf text-[#ff9a9e] mb-1"></i>
                            <span class="text-[10px] text-[var(--color-text-secondary)]">秋季</span>
                        </label>
                    </div>
                    
                    <div class="season-tag">
                        <input type="checkbox" id="season-winter">
                        <label for="season-winter">
                            <i class="fas fa-snowflake text-[#b8c6db] mb-1"></i>
                            <span class="text-[10px] text-[var(--color-text-secondary)]">冬季</span>
                        </label>
                    </div>
                </div>
            </div>
            
            <!-- 场合适用性 -->
            <div class="enhanced-glass rounded-xl p-4 mb-4">
                <h3 class="text-sm font-medium text-[var(--color-text-primary)] mb-3">场合适用性</h3>
                
                <div class="grid grid-cols-2 gap-2">
                    <div class="enhanced-glass bg-white/10 rounded-xl p-2 relative">
                        <input type="checkbox" id="occasion-casual" class="absolute opacity-0" checked>
                        <label for="occasion-casual" class="flex items-center cursor-pointer">
                            <div class="w-5 h-5 rounded-md border border-white/50 flex items-center justify-center mr-2">
                                <i class="fas fa-check text-[10px] text-white opacity-0 transition-opacity"></i>
                            </div>
                            <span class="text-sm text-[var(--color-text-secondary)]">日常休闲</span>
                        </label>
                    </div>
                    
                    <div class="enhanced-glass bg-white/10 rounded-xl p-2 relative">
                        <input type="checkbox" id="occasion-work" class="absolute opacity-0" checked>
                        <label for="occasion-work" class="flex items-center cursor-pointer">
                            <div class="w-5 h-5 rounded-md border border-white/50 flex items-center justify-center mr-2">
                                <i class="fas fa-check text-[10px] text-white opacity-0 transition-opacity"></i>
                            </div>
                            <span class="text-sm text-[var(--color-text-secondary)]">职场商务</span>
                        </label>
                    </div>
                    
                    <div class="enhanced-glass bg-white/10 rounded-xl p-2 relative">
                        <input type="checkbox" id="occasion-dating" class="absolute opacity-0">
                        <label for="occasion-dating" class="flex items-center cursor-pointer">
                            <div class="w-5 h-5 rounded-md border border-white/50 flex items-center justify-center mr-2">
                                <i class="fas fa-check text-[10px] text-white opacity-0 transition-opacity"></i>
                            </div>
                            <span class="text-sm text-[var(--color-text-secondary)]">约会交际</span>
                        </label>
                    </div>
                    
                    <div class="enhanced-glass bg-white/10 rounded-xl p-2 relative">
                        <input type="checkbox" id="occasion-party" class="absolute opacity-0">
                        <label for="occasion-party" class="flex items-center cursor-pointer">
                            <div class="w-5 h-5 rounded-md border border-white/50 flex items-center justify-center mr-2">
                                <i class="fas fa-check text-[10px] text-white opacity-0 transition-opacity"></i>
                            </div>
                            <span class="text-sm text-[var(--color-text-secondary)]">派对活动</span>
                        </label>
                    </div>
                    
                    <div class="enhanced-glass bg-white/10 rounded-xl p-2 relative">
                        <input type="checkbox" id="occasion-sports" class="absolute opacity-0">
                        <label for="occasion-sports" class="flex items-center cursor-pointer">
                            <div class="w-5 h-5 rounded-md border border-white/50 flex items-center justify-center mr-2">
                                <i class="fas fa-check text-[10px] text-white opacity-0 transition-opacity"></i>
                            </div>
                            <span class="text-sm text-[var(--color-text-secondary)]">运动健身</span>
                        </label>
                    </div>
                    
                    <div class="enhanced-glass bg-white/10 rounded-xl p-2 relative">
                        <input type="checkbox" id="occasion-formal" class="absolute opacity-0">
                        <label for="occasion-formal" class="flex items-center cursor-pointer">
                            <div class="w-5 h-5 rounded-md border border-white/50 flex items-center justify-center mr-2">
                                <i class="fas fa-check text-[10px] text-white opacity-0 transition-opacity"></i>
                            </div>
                            <span class="text-sm text-[var(--color-text-secondary)]">正式场合</span>
                        </label>
                    </div>
                </div>
            </div>
            
            <!-- 备注信息 -->
            <div class="enhanced-glass rounded-xl p-4 mb-6">
                <h3 class="text-sm font-medium text-[var(--color-text-primary)] mb-3">备注信息</h3>
                
                <div>
                    <label for="notes" class="block text-xs text-[var(--color-text-secondary)] mb-1">
                        备注
                    </label>
                    <textarea id="notes" rows="3" 
                              class="w-full bg-white/20 border border-white/30 rounded-xl px-3 py-2 text-sm text-[var(--color-text-primary)]"
                              placeholder="添加一些备注信息，如购买地点、搭配建议等"></textarea>
                </div>
            </div>
            
            <!-- 提交按钮 -->
            <button type="button" class="w-full glass-button py-3 rounded-xl text-sm font-medium text-[var(--color-text-primary)] save-button">
                保存到衣橱
            </button>
        </form>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 上传图片预览
            const uploadInput = document.getElementById('upload-input');
            const previewImage = document.getElementById('preview-image');
            const previewContainer = document.getElementById('preview-container');
            const uploadPlaceholder = document.getElementById('upload-placeholder');
            const uploadContainer = document.getElementById('upload-container');
            
            // 检查是否有初始图片
            if (previewImage.src && !previewImage.src.endsWith('#')) {
                previewContainer.classList.remove('hidden');
                uploadPlaceholder.classList.add('hidden');
            } else {
                previewContainer.classList.add('hidden');
                uploadPlaceholder.classList.remove('hidden');
            }
            
            // 上传图片处理
            uploadInput.addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        previewImage.src = e.target.result;
                        previewContainer.classList.remove('hidden');
                        uploadPlaceholder.classList.add('hidden');
                        
                        // 模拟AI识别过程
                        uploadContainer.classList.add('ai-scanning');
                        
                        setTimeout(() => {
                            uploadContainer.classList.remove('ai-scanning');
                            document.querySelector('.loader').style.display = 'none';
                            document.querySelector('.loader').nextElementSibling.textContent = '识别完成';
                        }, 2000);
                    };
                    reader.readAsDataURL(file);
                }
            });
            
            // 拍照按钮点击
            document.getElementById('take-photo').addEventListener('click', function() {
                uploadInput.click();
            });
            
            // 从相册选择按钮点击
            document.getElementById('select-from-album').addEventListener('click', function() {
                uploadInput.click();
            });
            
            // 场合复选框效果
            const occasionCheckboxes = document.querySelectorAll('[id^="occasion-"]');
            occasionCheckboxes.forEach(checkbox => {
                const icon = checkbox.nextElementSibling.querySelector('i');
                
                // 初始状态
                if (checkbox.checked) {
                    icon.style.opacity = '1';
                    checkbox.parentElement.classList.add('bg-white/20');
                }
                
                checkbox.addEventListener('change', function() {
                    if (this.checked) {
                        icon.style.opacity = '1';
                        this.parentElement.classList.add('bg-white/20');
                    } else {
                        icon.style.opacity = '0';
                        this.parentElement.classList.remove('bg-white/20');
                    }
                });
            });
            
            // 保存按钮点击
            const saveButtons = document.querySelectorAll('.save-button');
            saveButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // 简单的表单验证
                    const nameInput = document.getElementById('clothing-name');
                    if (!nameInput.value.trim()) {
                        alert('请输入衣物名称');
                        nameInput.focus();
                        return;
                    }
                    
                    // 模拟保存成功
                    alert('衣物保存成功！');
                    window.location.href = '../pages/wardrobe.html';
                });
            });
        });
    </script>
</body>
</html> 