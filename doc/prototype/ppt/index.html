<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>穿搭推荐小程序 - 灵感值运营系统</title>
    <style>
        :root {
            --primary-color: #FF8A00;
            --primary-dark: #E67A00;
            --secondary-color: #333333;
            --background-light: #FFF9F2;
            --text-light: #FFFFFF;
            --text-dark: #222222;
            --accent-color: #FF5722;
            --slide-height: 100vh;
            --slide-width: 100vw;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
        }
        
        body {
            background-color: #111;
            color: var(--text-dark);
            overflow: hidden;
        }
        
        .slides-container {
            width: 100vw;
            height: 100vh;
            overflow: hidden;
            position: relative;
        }
        
        .slide {
            position: absolute;
            top: 0;
            left: 0;
            width: var(--slide-width);
            height: var(--slide-height);
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            opacity: 0;
            transition: opacity 0.5s ease;
            background: var(--secondary-color);
            overflow: hidden;
        }
        
        .slide.active {
            opacity: 1;
            z-index: 1;
        }
        
        .slide-header {
            background: var(--primary-color);
            color: var(--text-light);
            padding: 20px 40px;
            position: relative;
            overflow: hidden;
        }
        
        .slide-header h1 {
            font-size: 2.2rem;
            font-weight: bold;
            position: relative;
            z-index: 2;
        }
        
        .slide-header::after {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 30%;
            height: 100%;
            background: var(--primary-dark);
            clip-path: polygon(100% 0, 0 0, 100% 100%);
            z-index: 1;
        }
        
        .slide-content {
            flex: 1;
            padding: 40px;
            background: var(--background-light);
            position: relative;
            overflow-y: auto;
        }
        
        .slide-number {
            position: absolute;
            bottom: 20px;
            right: 20px;
            font-size: 16px;
            color: var(--secondary-color);
            opacity: 0.5;
        }
        
        .title-slide {
            background: var(--secondary-color);
            color: var(--text-light);
            justify-content: center;
            align-items: center;
            text-align: center;
        }
        
        .title-slide h1 {
            font-size: 3rem;
            margin-bottom: 20px;
            color: var(--primary-color);
        }
        
        .title-slide h2 {
            font-size: 1.5rem;
            font-weight: 300;
            margin-bottom: 40px;
        }
        
        .title-slide .logo {
            font-size: 5rem;
            margin-bottom: 30px;
            color: var(--primary-color);
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        th {
            background-color: var(--primary-color);
            color: white;
            padding: 12px;
            text-align: left;
        }
        
        td {
            padding: 12px;
            border-bottom: 1px solid #eee;
        }
        
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .section-title {
            color: var(--primary-color);
            font-size: 1.8rem;
            margin-bottom: 20px;
            font-weight: bold;
            border-left: 5px solid var(--primary-color);
            padding-left: 15px;
        }
        
        .subsection-title {
            color: var(--secondary-color);
            font-size: 1.5rem;
            margin: 25px 0 15px 0;
            font-weight: 600;
        }
        
        .point-list {
            list-style-type: none;
            margin-bottom: 20px;
        }
        
        .point-list li {
            margin-bottom: 12px;
            position: relative;
            padding-left: 28px;
            line-height: 1.5;
        }
        
        .point-list li:before {
            content: '';
            position: absolute;
            left: 0;
            top: 4px;
            width: 16px;
            height: 16px;
            background-color: var(--primary-color);
            border-radius: 50%;
        }
        
        .bullet-list {
            list-style-type: none;
            margin-left: 15px;
            margin-bottom: 20px;
        }
        
        .bullet-list li {
            margin-bottom: 8px;
            position: relative;
            padding-left: 18px;
            line-height: 1.5;
        }
        
        .bullet-list li:before {
            content: '';
            position: absolute;
            left: 0;
            top: 8px;
            width: 8px;
            height: 8px;
            background-color: var(--primary-color);
            border-radius: 50%;
        }
        
        .two-col {
            display: flex;
            gap: 30px;
            margin: 20px 0;
        }
        
        .two-col > div {
            flex: 1;
        }
        
        .navigation {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 15px;
            z-index: 100;
        }
        
        .nav-btn {
            background: rgba(0,0,0,0.3);
            color: white;
            border: none;
            padding: 8px 16px;
            cursor: pointer;
            border-radius: 4px;
            font-size: 14px;
            transition: background 0.3s;
        }
        
        .nav-btn:hover {
            background: rgba(0,0,0,0.5);
        }
        
        .highlight-box {
            background: rgba(255, 138, 0, 0.1);
            border-left: 4px solid var(--primary-color);
            padding: 15px;
            margin: 20px 0;
        }
        
        .metrics-container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin: 20px 0;
        }
        
        .metric-box {
            flex: 1;
            min-width: 200px;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .metric-box .value {
            font-size: 2.5rem;
            font-weight: bold;
            color: var(--primary-color);
            margin: 10px 0;
        }
        
        .metric-box .label {
            font-size: 1rem;
            color: var(--secondary-color);
        }
        
        .chart-container {
            width: 100%;
            height: 250px;
            background: white;
            border-radius: 8px;
            margin: 20px 0;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: relative;
        }
        
        .chart-container::before {
            content: "数据图表展示区域";
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #999;
            font-style: italic;
        }
    </style>
</head>
<body>
    <!-- 导航按钮 -->
    <div class="navigation">
        <button class="nav-btn" id="prevBtn">上一页</button>
        <button class="nav-btn" id="nextBtn">下一页</button>
    </div>
    
    <!-- 幻灯片容器 -->
    <div class="slides-container">
        <!-- 封面页 -->
        <div class="slide title-slide active">
            <div class="logo">
                <i class="fas fa-lightbulb"></i>
            </div>
            <h1>穿搭推荐小程序 - 灵感值运营系统</h1>
            <h2>设计与实施方案</h2>
        </div>

        <!-- 第一章：系统概述 -->
        <div class="slide">
            <div class="slide-header">
                <h1>一、系统概述</h1>
            </div>
            <div class="slide-content">
                <h2 class="section-title">背景与目标</h2>
                <p>穿搭推荐小程序通过"自然模式"与"能量模式"双轨设计，为用户提供个性化穿搭推荐。为增强用户粘性、提升活跃度并构建可持续的商业模式，我们建立以"灵感值"为核心的综合运营体系。</p>
                
                <h3 class="subsection-title">灵感值定义</h3>
                <div class="highlight-box">
                    <p>灵感值是平台核心虚拟货币，代表用户在平台积累的创意能量，既是用户活跃度与贡献度的量化体现，也是获取高级服务的通行证。</p>
                </div>
                
                <h3 class="subsection-title">系统目标</h3>
                <ul class="point-list">
                    <li>建立完整的用户激励与消费闭环</li>
                    <li>提供可持续的商业化路径</li>
                    <li>增强用户粘性与活跃度</li>
                    <li>打造差异化的穿搭推荐体验</li>
                    <li>鼓励用户完善个人信息与衣橱管理</li>
                </ul>
                
                <h3 class="subsection-title">核心运营思路</h3>
                <div class="two-col">
                    <div>
                        <h4 style="color:var(--primary-color);margin-bottom:10px;">价值驱动</h4>
                        <ul class="bullet-list">
                            <li>将用户行为价值化，鼓励持续贡献</li>
                            <li>通过灵感值量化用户活跃度与参与度</li>
                            <li>建立价值交换体系，促进用户互动</li>
                        </ul>
                    </div>
                    <div>
                        <h4 style="color:var(--primary-color);margin-bottom:10px;">循环经济</h4>
                        <ul class="bullet-list">
                            <li>构建完整的获取-消耗-再获取闭环</li>
                            <li>平衡免费与付费功能边界</li>
                            <li>通过多样化途径提供灵感值流通</li>
                        </ul>
                    </div>
                </div>
                
                <div class="slide-number">1/15</div>
            </div>
        </div>

        <!-- 第二章：灵感值体系设计 -->
        <div class="slide">
            <div class="slide-header">
                <h1>二、灵感值体系设计</h1>
            </div>
            <div class="slide-content">
                <h2 class="section-title">价值定位与主张</h2>
                <div class="highlight-box">
                    <p><strong>核心定位：</strong>创意能量的数字化体现</p>
                    <p><strong>品牌叙事：</strong>"每位用户都拥有独特的时尚灵感能量，通过平台互动可以激发更多创意火花"</p>
                </div>
                
                <h3 class="subsection-title">用户价值</h3>
                <ul class="point-list">
                    <li>获取个性化高级服务</li>
                    <li>彰显个人品味与地位</li>
                    <li>享受更精准的穿搭推荐</li>
                </ul>
                
                <h3 class="subsection-title">灵感值分类</h3>
                <table>
                    <tr>
                        <th>灵感值类型</th>
                        <th>获取方式</th>
                        <th>是否可赠送</th>
                        <th>使用优先级</th>
                    </tr>
                    <tr>
                        <td>充值灵感值</td>
                        <td>直接充值</td>
                        <td>是</td>
                        <td>低(后消耗)</td>
                    </tr>
                    <tr>
                        <td>活动灵感值</td>
                        <td>行为奖励、任务完成</td>
                        <td>否</td>
                        <td>高(先消耗)</td>
                    </tr>
                    <tr>
                        <td>受赠灵感值</td>
                        <td>他人赠送</td>
                        <td>否</td>
                        <td>中</td>
                    </tr>
                </table>
                
                <h3 class="subsection-title">视觉呈现</h3>
                <ul class="bullet-list">
                    <li>设计与五行能量相关的独特灵感值图标</li>
                    <li>不同等级用户拥有差异化的灵感值动效</li>
                    <li>灵感值账户页面采用能量流动视觉设计</li>
                    <li>重要数值变动配有精美动画效果</li>
                </ul>
                
                <div class="slide-number">2/15</div>
            </div>
        </div>

        <!-- 第三章-1：灵感值获取机制(基础行为) -->
        <div class="slide">
            <div class="slide-header">
                <h1>三、灵感值获取机制(1)</h1>
            </div>
            <div class="slide-content">
                <h2 class="section-title">基础行为奖励</h2>
                <table>
                    <tr>
                        <th>行为</th>
                        <th>灵感值</th>
                        <th>频率限制</th>
                    </tr>
                    <tr>
                        <td>首次注册</td>
                        <td>+200</td>
                        <td>一次性</td>
                    </tr>
                    <tr>
                        <td>完善基础信息</td>
                        <td>+50</td>
                        <td>一次性</td>
                    </tr>
                    <tr>
                        <td>完善五行信息(能量模式)</td>
                        <td>+100</td>
                        <td>一次性</td>
                    </tr>
                    <tr>
                        <td>每日首次登录</td>
                        <td>+20</td>
                        <td>每日一次</td>
                    </tr>
                    <tr>
                        <td>查看每日运势</td>
                        <td>+10</td>
                        <td>每日一次</td>
                    </tr>
                    <tr>
                        <td>上传一件衣物</td>
                        <td>+10</td>
                        <td>每日上限20件</td>
                    </tr>
                    <tr>
                        <td>分享到朋友圈</td>
                        <td>+15</td>
                        <td>每日上限3次</td>
                    </tr>
                    <tr>
                        <td>受邀用户成功注册</td>
                        <td>+150</td>
                        <td>无上限</td>
                    </tr>
                    <tr>
                        <td>好友成功注册</td>
                        <td>+50</td>
                        <td>无上限</td>
                    </tr>
                </table>
                
                <h2 class="subsection-title">奖励机制设计合理性</h2>
                <div class="highlight-box">
                    <ul class="bullet-list">
                        <li><strong>新用户激励</strong>：注册与信息完善阶段提供高额一次性奖励，降低入门门槛</li>
                        <li><strong>活跃度驱动</strong>：日常行为如登录、查看运势设置合理频率，鼓励每日使用</li>
                        <li><strong>核心功能推广</strong>：上传衣物设置较高上限，激励衣橱管理核心功能使用</li>
                        <li><strong>用户增长驱动</strong>：分享与邀请奖励丰厚且无严格上限，促进自然增长</li>
                    </ul>
                </div>
                
                <div class="slide-number">3/15</div>
            </div>
        </div>
        
        <!-- 第三章-2：灵感值获取机制(互动奖励) -->
        <div class="slide">
            <div class="slide-header">
                <h1>三、灵感值获取机制(2)</h1>
            </div>
            <div class="slide-content">
                <div class="two-col">
                    <div>
                        <h2 class="section-title">系统互动奖励</h2>
                        <table>
                            <tr>
                                <th>行为</th>
                                <th>灵感值</th>
                                <th>频率限制</th>
                            </tr>
                            <tr>
                                <td>点赞推荐搭配</td>
                                <td>+2</td>
                                <td>每日上限30</td>
                            </tr>
                            <tr>
                                <td>收藏推荐搭配</td>
                                <td>+3</td>
                                <td>每日上限20</td>
                            </tr>
                            <tr>
                                <td>参与投票活动</td>
                                <td>+3</td>
                                <td>每次活动一次</td>
                            </tr>
                            <tr>
                                <td>完成每日穿搭测试</td>
                                <td>+15</td>
                                <td>每日一次</td>
                            </tr>
                        </table>
                    </div>
                    
                    <div>
                        <h2 class="section-title">衣橱管理奖励</h2>
                        <table>
                            <tr>
                                <th>行为</th>
                                <th>灵感值</th>
                                <th>频率限制</th>
                            </tr>
                            <tr>
                                <td>上传衣物并完成分类</td>
                                <td>+15</td>
                                <td>每日上限10</td>
                            </tr>
                            <tr>
                                <td>更新穿着频率记录</td>
                                <td>+10</td>
                                <td>每周一次</td>
                            </tr>
                            <tr>
                                <td>完成季节性衣橱整理</td>
                                <td>+50</td>
                                <td>每季度一次</td>
                            </tr>
                            <tr>
                                <td>达成衣橱里程碑<br>(30/60/100件)</td>
                                <td>+30/60/100</td>
                                <td>一次性</td>
                            </tr>
                            <tr>
                                <td>衣物搭配组合达成新纪录</td>
                                <td>+20</td>
                                <td>每周上限3次</td>
                            </tr>
                        </table>
                        
                        <h3 class="subsection-title" style="margin-top:40px;">成就与任务系统</h3>
                        <ul class="bullet-list">
                            <li><strong>日常任务</strong>：每日任务组合奖励30灵感值</li>
                            <li><strong>周常任务</strong>：每周任务组合奖励100灵感值</li>
                            <li><strong>成就系统</strong>：多级成就目标，阶梯式奖励</li>
                            <li><strong>连续签到</strong>：7/15/30天连续签到阶梯奖励</li>
                        </ul>
                    </div>
                </div>
                
                <div class="slide-number">4/15</div>
            </div>
        </div>

        <!-- 第三章-3：灵感值获取机制(充值系统) -->
        <div class="slide">
            <div class="slide-header">
                <h1>三、灵感值获取机制(3)</h1>
            </div>
            <div class="slide-content">
                <h2 class="section-title">充值获取</h2>
                <table>
                    <tr>
                        <th>档位</th>
                        <th>价格(元)</th>
                        <th>基础灵感值</th>
                        <th>赠送灵感值</th>
                        <th>总计</th>
                        <th>折合比例</th>
                    </tr>
                    <tr>
                        <td>小灵感</td>
                        <td>6</td>
                        <td>60</td>
                        <td>0</td>
                        <td>60</td>
                        <td>10灵感值/元</td>
                    </tr>
                    <tr>
                        <td>中灵感</td>
                        <td>30</td>
                        <td>300</td>
                        <td>30</td>
                        <td>330</td>
                        <td>11灵感值/元</td>
                    </tr>
                    <tr>
                        <td>大灵感</td>
                        <td>68</td>
                        <td>680</td>
                        <td>88</td>
                        <td>768</td>
                        <td>11.3灵感值/元</td>
                    </tr>
                    <tr>
                        <td>超级灵感</td>
                        <td>128</td>
                        <td>1280</td>
                        <td>220</td>
                        <td>1500</td>
                        <td>11.7灵感值/元</td>
                    </tr>
                    <tr>
                        <td>灵感礼包</td>
                        <td>328</td>
                        <td>3280</td>
                        <td>720</td>
                        <td>4000</td>
                        <td>12.2灵感值/元</td>
                    </tr>
                </table>
                
                <div class="two-col">
                    <div>
                        <h3 class="subsection-title">充值特权</h3>
                        <ul class="bullet-list">
                            <li><strong>首充特权</strong>：首次充值额外赠送30%灵感值</li>
                            <li><strong>月卡</strong>：30元/月，每日领取40灵感值(共1200)</li>
                            <li><strong>季卡/年卡</strong>：更大折扣的长期订阅选项</li>
                            <li><strong>限时特惠</strong>：节假日充值优惠，灵感值翻倍</li>
                        </ul>
                    </div>
                    
                    <div>
                        <h3 class="subsection-title">定价合理性分析</h3>
                        <div class="highlight-box">
                            <ul class="bullet-list">
                                <li><strong>梯度效益</strong>：随金额增加，性价比逐级提升</li>
                                <li><strong>月卡高效性</strong>：月卡设计提供最高性价比，但需用户每日领取，促进活跃</li>
                                <li><strong>入门门槛低</strong>：最低6元可体验付费服务</li>
                                <li><strong>数值易懂</strong>：10灵感值约等于1元，便于用户理解价值</li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <div class="metrics-container">
                    <div class="metric-box">
                        <div class="label">基础性价比</div>
                        <div class="value">10:1</div>
                        <div class="label">灵感值:人民币</div>
                    </div>
                    
                    <div class="metric-box">
                        <div class="label">月卡最高性价比</div>
                        <div class="value">40:1</div>
                        <div class="label">灵感值:人民币</div>
                    </div>
                    
                    <div class="metric-box">
                        <div class="label">充值档位</div>
                        <div class="value">5个</div>
                        <div class="label">覆盖不同消费能力</div>
                    </div>
                </div>
                
                <div class="slide-number">5/15</div>
            </div>
        </div>

        <!-- 第四章-1：灵感值消耗机制(基础功能) -->
        <div class="slide">
            <div class="slide-header">
                <h1>四、灵感值消耗机制(1)</h1>
            </div>
            <div class="slide-content">
                <h2 class="section-title">基础功能消耗</h2>
                <table>
                    <tr>
                        <th>功能</th>
                        <th>灵感值消耗</th>
                        <th>说明</th>
                    </tr>
                    <tr>
                        <td>基础自定义搭配</td>
                        <td>10/次</td>
                        <td>每日首次免费</td>
                    </tr>
                    <tr>
                        <td>高级自定义搭配</td>
                        <td>30/次</td>
                        <td>多场景、多约束条件</td>
                    </tr>
                    <tr>
                        <td>试穿系统搭配</td>
                        <td>5/次</td>
                        <td>每日3次免费</td>
                    </tr>
                    <tr>
                        <td>能量场深度分析</td>
                        <td>20/次</td>
                        <td>能量模式专属</td>
                    </tr>
                    <tr>
                        <td>导出高清搭配方案</td>
                        <td>15/次</td>
                        <td>分享到社交媒体免费</td>
                    </tr>
                    <tr>
                        <td>搭配方案保存</td>
                        <td>5/次</td>
                        <td>每日首次免费</td>
                    </tr>
                    <tr>
                        <td>衣橱兼容性分析</td>
                        <td>20/次</td>
                        <td>每周首次免费</td>
                    </tr>
                    <tr>
                        <td>观看推荐搭配视频</td>
                        <td>40/次</td>
                        <td>Lv3以上用户每日1次免费</td>
                    </tr>
                </table>
                
                <h3 class="subsection-title">消耗设计合理性</h3>
                <div class="highlight-box">
                    <ul class="bullet-list">
                        <li><strong>免费尝鲜</strong>：核心功能设置每日/每周首次免费，保证基础体验</li>
                        <li><strong>梯度消耗</strong>：功能价值越高，消耗量相应提高，引导用户理性使用</li>
                        <li><strong>视频资源定价</strong>：因视频生成成本较高，定价相对高于其他功能</li>
                        <li><strong>分享激励</strong>：分享到社交媒体可免费使用部分功能，促进推广</li>
                    </ul>
                </div>
                
                <div class="slide-number">6/15</div>
            </div>
        </div>

        <!-- 第四章-2：灵感值消耗机制(高级功能) -->
        <div class="slide">
            <div class="slide-header">
                <h1>四、灵感值消耗机制(2)</h1>
            </div>
            <div class="slide-content">
                <h2 class="section-title">高级功能消耗</h2>
                <table>
                    <tr>
                        <th>功能</th>
                        <th>灵感值消耗</th>
                        <th>说明</th>
                    </tr>
                    <tr>
                        <td>AI造型师建议</td>
                        <td>50/次</td>
                        <td>会员折扣50%</td>
                    </tr>
                    <tr>
                        <td>季节衣橱规划</td>
                        <td>100/次</td>
                        <td>会员每季度一次免费</td>
                    </tr>
                    <tr>
                        <td>定制节日穿搭方案</td>
                        <td>80/次</td>
                        <td>重要节日专属服务</td>
                    </tr>
                    <tr>
                        <td>高精度虚拟试衣</td>
                        <td>30/次</td>
                        <td>会员每日3次免费</td>
                    </tr>
                    <tr>
                        <td>形象全面诊断</td>
                        <td>150/次</td>
                        <td>包含专业建议报告</td>
                    </tr>
                    <tr>
                        <td>个人风格画像</td>
                        <td>200/次</td>
                        <td>生成专属风格报告</td>
                    </tr>
                    <tr>
                        <td>定制搭配视频生成</td>
                        <td>100/次</td>
                        <td>Lv5及以上用户每周1次免费</td>
                    </tr>
                </table>
                
                <div class="two-col">
                    <div>
                        <h3 class="subsection-title">消费优惠与保护</h3>
                        <ul class="bullet-list">
                            <li><strong>新用户保护</strong>：首周内搭配功能消耗减半</li>
                            <li><strong>免费额度</strong>：每日更新固定免费额度</li>
                            <li><strong>会员特权</strong>：不同等级享有消耗折扣</li>
                            <li><strong>批量优惠</strong>：一次性规划多日搭配享受折扣</li>
                            <li><strong>应急措施</strong>："急用"免费额度(每周一次)</li>
                        </ul>
                    </div>
                    
                    <div>
                        <h3 class="subsection-title">消耗优先级</h3>
                        <p></p>系统自动按以下优先级消耗灵感值：</p>
                        <ul class="bullet-list" style="margin-top: 10px;">
                            <li><strong>新用户保护</strong>：首周内搭配功能消耗减半</li>
                            <li><strong>免费额度</strong>：每日更新固定免费额度</li>
                            <li><strong>会员特权</strong>：不同等级享有消耗折扣</li>
                            <li><strong>批量优惠</strong>：一次性规划多日搭配享受折扣</li>
                            <li><strong>应急措施</strong>："急用"免费额度(每周一次)</li>
                        </ul>
                        
                        
                    </div>
                    <div>
                    <h3 class="subsection-title" style="margin-top: 20px;">高级功能定价策略</h3>
                    <p>高级功能定价遵循三个原则：</p>
                        <ul class="bullet-list" style="margin-top: 10px;">
                            <li><strong>成本对应</strong>：算力消耗大的功能定价更高</li>
                            <li><strong>价值匹配</strong>：价值越高的服务，定价相应提高</li>
                            <li><strong>频率考量</strong>：使用频率低的功能定价更高，但会员有免费额度平衡</li>
                        </ul>
                    </div>
                </div>
                
                <div class="slide-number">7/15</div>
            </div>
        </div>

        <!-- 第五章：等级与特权系统 -->
        <div class="slide">
            <div class="slide-header">
                <h1>五、等级与特权系统</h1>
            </div>
            <div class="slide-content">
                <h2 class="section-title">用户等级体系</h2>
                <table>
                    <tr>
                        <th>等级</th>
                        <th>所需累计消耗灵感值</th>
                        <th>称号</th>
                        <th>标识</th>
                    </tr>
                    <tr>
                        <td>Lv1</td>
                        <td>0-399</td>
                        <td>新锐设计师</td>
                        <td>青铜灵感徽章</td>
                    </tr>
                    <tr>
                        <td>Lv2</td>
                        <td>400-1599</td>
                        <td>时尚达人</td>
                        <td>白银灵感徽章</td>
                    </tr>
                    <tr>
                        <td>Lv3</td>
                        <td>1600-3999</td>
                        <td>风格专家</td>
                        <td>黄金灵感徽章</td>
                    </tr>
                    <tr>
                        <td>Lv4</td>
                        <td>4000-7999</td>
                        <td>灵感大师</td>
                        <td>铂金灵感徽章</td>
                    </tr>
                    <tr>
                        <td>Lv5</td>
                        <td>8000-14999</td>
                        <td>时尚先锋</td>
                        <td>钻石灵感徽章</td>
                    </tr>
                    <tr>
                        <td>Lv6</td>
                        <td>15000+</td>
                        <td>传奇造型师</td>
                        <td>星耀灵感徽章</td>
                    </tr>
                </table>
                
                <h3 class="subsection-title">核心特权对比</h3>
                <div class="metrics-container">
                    <div class="metric-box">
                        <div class="label">Lv2</div>
                        <div class="value">10%</div>
                        <div class="label">消耗折扣</div>
                    </div>
                    <div class="metric-box">
                        <div class="label">Lv4</div>
                        <div class="value">30%</div>
                        <div class="label">消耗折扣</div>
                    </div>
                    <div class="metric-box">
                        <div class="label">Lv6</div>
                        <div class="value">50%</div>
                        <div class="label">消耗折扣</div>
                    </div>
                </div>
                
                <h3 class="subsection-title">等级阶梯设计合理性</h3>
                <div class="highlight-box">
                    <ul class="bullet-list">
                        <li><strong>入门友好</strong>：Lv1无门槛、Lv2门槛较低(400消耗值)，新用户能快速体验</li>
                        <li><strong>成长曲线</strong>：等级所需灵感值呈非线性增长，保持挑战性但不至于遥不可及</li>
                        <li><strong>价值体现</strong>：每升一级提供明显的功能折扣和特权提升，价值感直观</li>
                        <li><strong>终极目标</strong>：Lv6设置为长期目标(15000+消耗值)，为高付费用户提供专属体验</li>
                        <li><strong>消耗计算机制</strong>：等级基于用户累计消耗的灵感值，鼓励用户实际使用功能而非囤积，形成良性循环</li>
                    </ul>
                </div>
                
                <div class="slide-number">8/15</div>
            </div>
        </div>

        <!-- 第六章：灵感值赠送机制 -->
        <div class="slide">
            <div class="slide-header">
                <h1>六、灵感值赠送机制</h1>
            </div>
            <div class="slide-content">
                <div class="two-col">
                    <div>
                        <h2 class="section-title">赠送规则</h2>
                        <ul class="point-list">
                            <li><strong>赠送资格</strong>：所有用户均可赠送灵感值</li>
                            <li><strong>赠送限制</strong>：只能赠送充值获得的灵感值</li>
                            <li><strong>防循环机制</strong>：受赠灵感值不可再次赠送</li>
                            <li><strong>数量限制</strong>：单次最低10灵感值，最高无上限</li>
                            <li><strong>频率限制</strong>：向同一用户单日最多赠送3次</li>
                            <li><strong>安全措施</strong>：首次赠送需完成手机验证</li>
                            <li><strong>新用户保护</strong>：注册7天内接收有每日上限</li>
                        </ul>
                    </div>
                    
                    <div>
                        <h2 class="section-title">赠送流程</h2>
                        <ol style="list-style-type: decimal; margin-left: 20px; margin-bottom: 20px;">
                            <li>进入好友主页或搭配详情页</li>
                            <li>点击"赠送灵感值"按钮</li>
                            <li>选择/输入赠送数量</li>
                            <li>添加赠送留言(选填)</li>
                            <li>确认赠送</li>
                            <li>接收方收到推送通知</li>
                            <li>接收方确认接收</li>
                        </ol>
                        
                    </div>

                    <div>
                    
                        <h3 class="section-title">赠送场景设计</h3>
                        <ul class="bullet-list">
                            <li><strong>推荐致谢</strong>：感谢精准搭配推荐</li>
                            <li><strong>生日礼物</strong>：系统提醒好友生日</li>
                            <li><strong>新人引导</strong>：老用户帮助新用户入门</li>
                            <li><strong>灵感助力</strong>：帮助解锁高级功能</li>
                            <li><strong>纪念日礼物</strong>：特殊日子的礼物</li>
                        </ul>
                    </div>
                </div>
                
                <h3 class="subsection-title">赠送机制战略意义</h3>
                <div class="highlight-box">
                    <div class="two-col">
                        <div>
                            <h4 style="color:var(--primary-color);margin-bottom:10px;">社交驱动</h4>
                            <ul class="bullet-list">
                                <li>通过赠送建立用户间连接</li>
                                <li>促进用户间互动与分享</li>
                                <li>增强社区归属感</li>
                            </ul>
                        </div>
                        <div>
                            <h4 style="color:var(--primary-color);margin-bottom:10px;">商业价值</h4>
                            <ul class="bullet-list">
                                <li>赠送仅限充值灵感值，刺激付费</li>
                                <li>灵感值流通增加总体消费量</li>
                                <li>赠送排行榜提高高消费用户曝光</li>
                                <li>消耗驱动等级：通过消耗激励用户主动使用高级功能</li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <div class="metrics-container">
                    <div class="metric-box">
                        <div class="label">赠送返利</div>
                        <div class="value">5%</div>
                        <div class="label">赠送100获得5灵感值</div>
                    </div>
                    
                    <div class="metric-box">
                        <div class="label">赠送最低限额</div>
                        <div class="value">10</div>
                        <div class="label">灵感值/次</div>
                    </div>
                    
                    <div class="metric-box">
                        <div class="label">赠送频率</div>
                        <div class="value">3次</div>
                        <div class="label">每日对同一用户上限</div>
                    </div>
                </div>
                
                <div class="slide-number">9/15</div>
            </div>
        </div>

        <!-- 第七章-1：运营活动设计(常规活动) -->
        <div class="slide">
            <div class="slide-header">
                <h1>七、运营活动设计(1)</h1>
            </div>
            <div class="slide-content">
                <h2 class="section-title">常规活动</h2>
                <div class="two-col">
                    <div>
                        <h3 class="subsection-title">每周穿搭主题</h3>
                        <ul class="bullet-list">
                            <li>系统推出每周主题搭配推荐</li>
                            <li>尝试推荐搭配获得灵感值奖励</li>
                            <li>节假日推出特别版本主题</li>
                        </ul>
                        
                        <h3 class="subsection-title">季度风格焕新</h3>
                        <ul class="bullet-list">
                            <li>每季度推出季节性穿搭指南</li>
                            <li>完成季节性衣橱整理获得奖励</li>
                            <li>提前预告下季流行趋势</li>
                        </ul>
                    </div>
                    
                    <div>
                        <h3 class="subsection-title">灵感能量训练营</h3>
                        <ul class="bullet-list">
                            <li>新手引导系列任务</li>
                            <li>阶梯式完成获得奖励</li>
                            <li>全部完成获得"训练营毕业生"称号</li>
                        </ul>
                        
                        <h3 class="subsection-title">搭配视频展示周</h3>
                        <ul class="bullet-list">
                            <li>定期推出搭配视频主题周活动</li>
                            <li>活动期间观看搭配视频享受折扣</li>
                            <li>分享视频获得额外灵感值奖励</li>
                            <li>用户通过点赞选出最佳搭配视频</li>
                        </ul>
                    </div>
                </div>
                
                <h2 class="section-title">节庆活动</h2>
                <div class="two-col">
                    <div>
                        <h3 class="subsection-title">春节系列</h3>
                        <ul class="bullet-list">
                            <li>"红运当头"主题搭配推荐</li>
                            <li>灵感值红包互送活动</li>
                            <li>充值翻倍优惠</li>
                            <li>新春限定装饰道具</li>
                        </ul>
                        
                        <h3 class="subsection-title">情人节系列</h3>
                        <ul class="bullet-list">
                            <li>"浪漫约会"穿搭推荐</li>
                            <li>好友灵感值互送有额外奖励</li>
                            <li>约会穿搭特别推荐服务</li>
                        </ul>
                    </div>
                    
                    <div>
                        <h3 class="subsection-title">中秋/国庆系列</h3>
                        <ul class="bullet-list">
                            <li>传统元素现代搭配推荐</li>
                            <li>假期穿搭规划服务</li>
                            <li>充值优惠与限定礼包</li>
                            <li>假期灵感值每日奖励翻倍</li>
                        </ul>
                        
                        <h3 class="subsection-title">节气文化系列</h3>
                        <ul class="bullet-list">
                            <li>结合24节气推出主题搭配</li>
                            <li>传统文化与现代穿搭结合</li>
                            <li>收集全年节气搭配获"岁时雅士"称号</li>
                        </ul>
                    </div>
                </div>
                
                <div class="slide-number">10/15</div>
            </div>
        </div>

        <!-- 第七章-2：运营活动设计(特殊活动) -->
        <div class="slide">
            <div class="slide-header">
                <h1>七、运营活动设计(2)</h1>
            </div>
            <div class="slide-content">
                <h2 class="section-title">特殊运营活动</h2>
                
                <div class="two-col">
                    <div>
                        <h3 class="subsection-title">新用户成长计划</h3>
                        <ul class="bullet-list">
                            <li>为期14天的新手任务</li>
                            <li>每日完成任务获得额外灵感值</li>
                            <li>全部完成可获500灵感值大礼包</li>
                            <li>新手专属指导与教程</li>
                        </ul>
                        
                        <h3 class="subsection-title">回归用户激励</h3>
                        <ul class="bullet-list">
                            <li>针对流失用户的回归奖励</li>
                            <li>回归礼包包含灵感值与限定道具</li>
                            <li>回归期间享受消耗折扣</li>
                            <li>"老朋友"专属标识</li>
                        </ul>
                    </div>
                    
                    <div>
                        <h3 class="subsection-title">会员专享活动</h3>
                        <ul class="bullet-list">
                            <li>高等级用户专属功能体验</li>
                            <li>VIP专属穿搭推荐</li>
                            <li>专属抽奖与限定奖励</li>
                            <li>新功能优先体验资格</li>
                        </ul>
                        
                        <h3 class="subsection-title">品牌合作系列</h3>
                        <ul class="bullet-list">
                            <li>与时尚品牌跨界合作</li>
                            <li>品牌特别款式推荐</li>
                            <li>限时品牌专属搭配服务</li>
                            <li>品牌专属滤镜与装饰</li>
                        </ul>
                    </div>
                </div>
                
                <h2 class="section-title">活动设计合理性与执行方案</h2>
                <div class="highlight-box">
                    <div class="two-col">
                        <div>
                            <h4 style="color:var(--primary-color);margin-bottom:10px;">活动频率规划</h4>
                            <ul class="bullet-list">
                                <li><strong>日常活动</strong>：每日任务、每周主题，保持用户高频互动</li>
                                <li><strong>季节活动</strong>：每季度更新，与服装季节性特点匹配</li>
                                <li><strong>节日活动</strong>：根据重要节假日规划，增强社交属性</li>
                                <li><strong>用户生命周期活动</strong>：针对新用户、活跃用户、休眠用户的全覆盖</li>
                            </ul>
                        </div>
                        <div>
                            <h4 style="color:var(--primary-color);margin-bottom:10px;">执行重点</h4>
                            <ul class="bullet-list">
                                <li><strong>文化融合</strong>：结合中国传统节气与五行理念，增强产品差异化</li>
                                <li><strong>数据监控</strong>：实时监控活动参与度与灵感值流通情况，及时调整</li>
                                <li><strong>阶梯式推进</strong>：活动难度设计由易到难，保证高完成率</li>
                                <li><strong>社交传播</strong>：优化分享链路，增强活动社交传播属性</li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <div class="metrics-container">
                    <div class="metric-box">
                        <div class="label">新用户成长计划完成奖励</div>
                        <div class="value">500</div>
                        <div class="label">灵感值</div>
                    </div>
                    
                    <div class="metric-box">
                        <div class="label">常规活动频率</div>
                        <div class="value">3+</div>
                        <div class="label">种/月</div>
                    </div>
                    
                    <div class="metric-box">
                        <div class="label">节气活动</div>
                        <div class="value">24</div>
                        <div class="label">个/年</div>
                    </div>
                </div>
                
                <div class="slide-number">11/15</div>
            </div>
        </div>

        <!-- 第八章：商业化路径 -->
        <div class="slide">
            <div class="slide-header">
                <h1>八、商业化路径</h1>
            </div>
            <div class="slide-content">
                <div class="two-col">
                    <div>
                        <h2 class="section-title">直接变现方式</h2>
                        <ul class="point-list">
                            <li><strong>灵感值充值</strong>：主要收入来源</li>
                            <li><strong>会员订阅</strong>：月卡、季卡、年卡等订阅制产品</li>
                            <li><strong>限定道具</strong>：付费获取的专属装饰道具</li>
                            <li><strong>高级服务</strong>：付费专业造型指导服务</li>
                            <li><strong>视频功能</strong>：观看和生成搭配视频的付费服务</li>
                        </ul>
                        
                        <h3 class="subsection-title">间接变现方式</h3>
                        <ul class="bullet-list">
                            <li><strong>电商导流</strong>：搭配方案关联商品导购佣金</li>
                            <li><strong>品牌合作</strong>：品牌植入与定制活动</li>
                            <li><strong>广告投放</strong>：精准用户画像支持的定向广告</li>
                            <li><strong>数据服务</strong>：基于用户授权的穿搭趋势数据分析</li>
                        </ul>
                    </div>
                    
                    <div>
                        <h2 class="section-title">转化策略</h2>
                        <ul class="point-list">
                            <li><strong>免费尝鲜</strong>：重点功能限次免费体验</li>
                            <li><strong>梯度体验</strong>：基础功能免费，高级功能付费</li>
                            <li><strong>场景触发</strong>：在高需求场景推荐付费服务</li>
                            <li><strong>节日促销</strong>：重要节假日特别优惠活动</li>
                            <li><strong>视频体验转化</strong>：通过视频展示增强付费意愿</li>
                        </ul>
                        
                        <h3 class="subsection-title">核心商业逻辑</h3>
                        <div class="highlight-box">
                            <p>灵感值运营系统的商业化路径基于三个核心支柱：</p>
                            <ol style="margin-left: 20px; margin-top: 10px;">
                                <li><strong>价值传递</strong>：用户能清晰感知付费功能的差异化价值</li>
                                <li><strong>体验分层</strong>：免费体验与付费服务形成合理梯度</li>
                                <li><strong>循环经济</strong>：灵感值获取-消耗-再获取形成闭环</li>
                            </ol>
                        </div>
                    </div>
                </div>
                
                <h2 class="section-title">收入预测</h2>
             
                <div class="two-col">
                    <div>
                        <h3 class="subsection-title">收入结构预测</h3>
                        <ul class="bullet-list">
                            <li><strong>灵感值充值</strong>：占总收入65%</li>
                            <li><strong>会员订阅</strong>：占总收入20%</li>
                            <li><strong>电商导流</strong>：占总收入10%</li>
                            <li><strong>品牌合作</strong>：占总收入5%</li>
                        </ul>
                    </div>
                    
                    <div>
                        <h3 class="subsection-title">付费转化率目标</h3>
                        <ul class="bullet-list">
                            <li><strong>首月付费率</strong>：3-5%</li>
                            <li><strong>核心用户付费率</strong>：15-20%</li>
                            <li><strong>月卡订阅率</strong>：活跃用户的8-10%</li>
                            <li><strong>高级功能转化率</strong>：付费用户的30%</li>
                        </ul>
                    </div>
                </div>
                
                <div class="slide-number">12/15</div>
            </div>
        </div>

        <!-- 第十一章-1：风险评估与应对策略(风险识别) -->
        <div class="slide">
            <div class="slide-header">
                <h1>九、风险评估与应对策略(1)</h1>
            </div>
            <div class="slide-content">
                <h2 class="section-title">潜在风险</h2>
                
                <div class="two-col">
                    <div>
                        <h3 class="subsection-title">经济系统风险</h3>
                        <div class="highlight-box">
                            <h4 style="color:var(--primary-color);margin-bottom:10px;">通货膨胀</h4>
                            <p>灵感值过度发放导致贬值，降低用户购买动力</p>
                            
                            <h4 style="color:var(--primary-color);margin:15px 0 10px 0;">马太效应</h4>
                            <p>高等级用户优势过大，低等级用户体验受限明显</p>
                            
                            <h4 style="color:var(--primary-color);margin:15px 0 10px 0;">刷单行为</h4>
                            <p>恶意操作薅羊毛，扰乱正常经济秩序</p>
                        </div>
                    </div>
                    
                    <div>
                        <h3 class="subsection-title">用户体验风险</h3>
                        <div class="highlight-box">
                            <h4 style="color:var(--primary-color);margin-bottom:10px;">用户抵触</h4>
                            <p>核心功能收费引发不满，损害用户体验</p>
                            
                            <h4 style="color:var(--primary-color);margin:15px 0 10px 0;">流失风险</h4>
                            <p>付费门槛设置不当导致用户流失</p>
                            
                            <h4 style="color:var(--primary-color);margin:15px 0 10px 0;">UGC移除影响</h4>
                            <p>移除评论等功能可能影响部分用户参与感</p>
                        </div>
                    </div>
                </div>
                
                <h3 class="subsection-title">技术运营风险</h3>
                <div class="highlight-box">
                    <div class="two-col">
                        <div>
                            <h4 style="color:var(--primary-color);margin-bottom:10px;">资源压力</h4>
                            <p>视频生成与存储对系统资源需求过大，成本控制困难</p>
                        </div>
                        <div>
                            <h4 style="color:var(--primary-color);margin-bottom:10px;">数据安全</h4>
                            <p>虚拟货币交易带来的账户安全风险与数据保护挑战</p>
                        </div>
                    </div>
                </div>
                
                
                <div class="slide-number">13/15</div>
            </div>
        </div>

        <!-- 第十一章-2：风险评估与应对策略(应对策略) -->
        <div class="slide">
            <div class="slide-header">
                <h1>九、风险评估与应对策略(2)</h1>
            </div>
            <div class="slide-content">
                <h2 class="section-title">应对策略</h2>
                
                <div class="two-col">
                    <div>
                        <h3 class="subsection-title">经济平衡策略</h3>
                        <ul class="point-list">
                            <li><strong>灵感值经济模型</strong>
                                <p class="text-xs">建立灵感值经济模型，控制发放与消耗比例，设立预警阈值</p>
                            </li>
                            <li><strong>合理等级设计</strong>
                                <p class="text-xs">设计合理的等级特权，避免两极分化，保持各层级用户的体验差异</p>
                            </li>
                            <li><strong>风控系统</strong>
                                <p class="text-xs">完善风控系统，多维度识别异常行为，防范刷单与自动化操作</p>
                            </li>
                        </ul>
                    </div>
                    
                    <div>
                        <h3 class="subsection-title">用户体验优化</h3>
                        <ul class="point-list">
                            <li><strong>基础功能保障</strong>
                                <p class="text-xs">确保基础功能免费可用，付费仅针对高级服务，避免基础体验受阻</p>
                            </li>
                            <li><strong>付费优化</strong>
                                <p class="text-xs">基于用户行为数据持续优化付费策略，调整付费门槛与价值体验</p>
                            </li>
                            <li><strong>互动功能强化</strong>
                                <p class="text-xs">强化点赞、分享等保留的互动功能，弥补可能的参与度降低</p>
                            </li>
                        </ul>
                    </div>
                </div>
                
                <h3 class="subsection-title">技术与资源优化</h3>
                <div class="highlight-box">
                    <ul class="point-list">
                        <li><strong>资源弹性扩容</strong>
                            <p class="text-xs">建立视频资源弹性扩缩容机制，平衡成本与用户体验，避免资源浪费</p>
                        </li>
                        <li><strong>分级缓存策略</strong>
                            <p class="text-xs">实施分级缓存策略，减轻高峰期系统压力，提升响应速度</p>
                        </li>
                        <li><strong>安全防护加强</strong>
                            <p class="text-xs">灵感值账户安全多重验证，大额操作需确认，异常行为智能锁定</p>
                        </li>
                    </ul>
                </div>
                
                <h2 class="section-title">风险应对执行计划</h2>
                <div class="metrics-container">
                    <div class="metric-box">
                        <div class="label">健康指标监控</div>
                        <div class="value">周报</div>
                        <div class="label">定期数据分析</div>
                    </div>
                    
                    <div class="metric-box">
                        <div class="label">调价周期</div>
                        <div class="value">季度</div>
                        <div class="label">基于用户反馈</div>
                    </div>
                    
                    <div class="metric-box">
                        <div class="label">风控系统响应</div>
                        <div class="value">实时</div>
                        <div class="label">异常行为监控</div>
                    </div>
                </div>
                
                <div class="slide-number">14/15</div>
            </div>
        </div>

        <!-- 灵感值运营系统综合分析 -->
        <div class="slide">
            <div class="slide-header">
                <h1>灵感值运营系统综合分析</h1>
            </div>
            <div class="slide-content">
                <h2 class="section-title">运营思路总结</h2>
                <div class="highlight-box">
                    <div class="two-col">
                        <div>
                            <h4 style="color:var(--primary-color);margin-bottom:10px;">价值驱动</h4>
                            <p>灵感值系统通过将用户行为价值化，构建虚拟经济体系，驱动用户参与度和留存。每个交互行为都有明确价值，使用户感受到贡献与成长。</p>
                        </div>
                        <div>
                            <h4 style="color:var(--primary-color);margin-bottom:10px;">体验分层</h4>
                            <p>基础服务与高级功能明确区分，等级体系设计合理，保证新用户体验顺畅的同时，为高付费用户提供差异化价值。</p>
                        </div>
                    </div>
                </div>
                
                <h2 class="section-title">数值合理性分析</h2>
                <div class="two-col">
                    <div>
                        <h3 class="subsection-title">获取与消耗平衡</h3>
                        <ul class="bullet-list">
                            <li>平均活跃用户每月免费获取约1000-1500灵感值</li>
                            <li>基础功能消耗设计在10-40灵感值/次区间</li>
                            <li>高频功能设置免费额度，保障基础体验</li>
                            <li>高级功能价格区间50-200灵感值，约合人民币5-20元</li>
                            <li>基础需求月消耗约800-1200灵感值，与免费获取量基本平衡</li>
                        </ul>
                    </div>
                    
                    <div>
                        <h3 class="subsection-title">用户成长路径</h3>
                        <ul class="bullet-list">
                            <li>新用户7天内可免费获得约350灵感值</li>
                            <li>普通活跃用户1个月可达到Lv2(500灵感值)</li>
                            <li>中度付费用户3个月可达到Lv3(2000灵感值)</li>
                            <li>高度活跃+付费用户6个月可达到Lv4(5000灵感值)</li>
                            <li>忠诚高付费用户1年内可达到Lv5(10000灵感值)</li>
                        </ul>
                    </div>
                </div>
                
                <h2 class="section-title">运营策略执行建议</h2>
                <div class="highlight-box">
                    <ul class="point-list">
                        <li><strong>阶段式推进</strong>
                            <p class="text-xs">先建立基础消费习惯，后逐步推出高级功能，让用户适应虚拟经济系统</p>
                        </li>
                        <li><strong>持续数据监控</strong>
                            <p class="text-xs">建立灵感值经济系统健康度指标，每周审核数据，每月进行用户满意度调研</p>
                        </li>
                        <li><strong>灵活调整机制</strong>
                            <p class="text-xs">初期可能需要频繁调整数值设定，应建立完善的版本迭代与通知机制</p>
                        </li>
                        <li><strong>文化特色融合</strong>
                            <p class="text-xs">充分结合中国传统五行和节气文化，打造差异化竞争优势</p>
                        </li>
                    </ul>
                </div>
                
                <div class="slide-number">15/15</div>
            </div>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const slides = document.querySelectorAll('.slide');
            const prevBtn = document.getElementById('prevBtn');
            const nextBtn = document.getElementById('nextBtn');
            let currentSlide = 0;
            
            // 初始化只显示第一张幻灯片
            slides.forEach((slide, index) => {
                if(index === 0) {
                    slide.classList.add('active');
                } else {
                    slide.classList.remove('active');
                }
            });
            
            // 上一张和下一张按钮事件
            prevBtn.addEventListener('click', function() {
                slides[currentSlide].classList.remove('active');
                currentSlide = (currentSlide - 1 + slides.length) % slides.length;
                slides[currentSlide].classList.add('active');
            });
            
            nextBtn.addEventListener('click', function() {
                slides[currentSlide].classList.remove('active');
                currentSlide = (currentSlide + 1) % slides.length;
                slides[currentSlide].classList.add('active');
            });
            
            // 键盘事件
            document.addEventListener('keydown', function(e) {
                if(e.key === 'ArrowLeft') {
                    prevBtn.click();
                } else if(e.key === 'ArrowRight') {
                    nextBtn.click();
                }
            });
        });
    </script>
</body>
</html> 