/* 通用五行样式 */
.wuxing-item {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

/* 五行图标颜色 */
.wuxing-icon-jin {
    color: #c0c0c0;
}

.wuxing-icon-mu {
    color: #2d5a27;
}

.wuxing-icon-shui {
    color: #a8d8ff;
}

.wuxing-icon-huo {
    color: #ff2020;
}

.wuxing-icon-tu {
    color: #d4a017;
}

/* 五行背景渐变 */
.wuxing-bg-jin {
    background: linear-gradient(135deg, #ffffff, #f0f0f0);
}

.wuxing-bg-mu {
    background: linear-gradient(135deg, #a8e6cf, #73c1a8);
}

.wuxing-bg-shui {
    background: linear-gradient(135deg, #b8c6db, #648dae);
}

.wuxing-bg-huo {
    background: linear-gradient(135deg, #ff9a9e, #ff5458);
}

.wuxing-bg-tu {
    background: linear-gradient(135deg, #ffeaa7, #ffc25c);
}

/* 五行标识样式 */
.wuxing-jin {
    background: linear-gradient(135deg, #ffffff, #f0f0f0);
    color: #1a1a1a;
    padding: 2px 4px;
    border-radius: 4px;
}

.wuxing-mu {
    background: linear-gradient(135deg, #a8e6cf, #73c1a8);
    color: #1a1a1a;
    padding: 2px 4px;
    border-radius: 4px;
}

.wuxing-shui {
    background: linear-gradient(135deg, #b8c6db, #648dae);
    color: #ffffff;
    padding: 2px 4px;
    border-radius: 4px;
}

.wuxing-huo {
    background: linear-gradient(135deg, #ff9a9e, #ff5458);
    color: #ffffff;
    padding: 2px 4px;
    border-radius: 4px;
}

.wuxing-tu {
    background: linear-gradient(135deg, #ffeaa7, #ffc25c);
    color: #1a1a1a;
    padding: 2px 4px;
    border-radius: 4px;
}

/* 五行图标基础样式 */
.wuxing-icon {
    font-size: 10px;
    margin-right: 2px;
} 