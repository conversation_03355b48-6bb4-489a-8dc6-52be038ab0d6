:root {
  /* 2024年度色彩系统 */
  --color-peach-fuzz: #FF8E7F;
  --color-peach-light: #FFD6CE;
  --color-peach-dark: #E89686;
  
  /* 辅助色系 */
  --color-sage: #D1E2C4;
  --color-lavender: #E6E6FA;
  --color-cream: #F9F3DF;
  
  /* 中性色 */
  --color-text-primary: #1a1a1a;
  --color-text-secondary: #666666;
  --color-text-tertiary: #AEAEC0;
  --color-background: #F8F9FF;
  
  /* 新拟态效果 */
  --neo-shadow-light: 4px 4px 8px rgba(174, 174, 192, 0.1);
  --neo-shadow-dark: -4px -4px 8px rgba(255, 255, 255, 0.3);
  --neo-inset: inset 1px 1px 2px rgba(174, 174, 192, 0.1),
               inset -1px -1px 2px rgba(255, 255, 255, 0.3);
  
  /* 圆角 */
  --radius-sm: 8px;
  --radius-md: 12px;
  --radius-lg: 24px;
  
  /* 间距 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  
  /* 阴影 */
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.05);
  --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.05);

  --color-like: #ff5458;
  --color-favorite: #ffd700;
  --color-share: #3b82f6;
}

/* 新拟态卡片基础样式 */
.neo-card {
  background: var(--color-background);
  border-radius: var(--radius-md);
  box-shadow: var(--neo-shadow-light), var(--neo-shadow-dark);
  transition: all 0.3s ease;
}

.neo-card:hover {
  transform: translateY(-1px);
  box-shadow: var(--neo-shadow-light), var(--neo-shadow-dark),
              0 4px 8px rgba(0, 0, 0, 0.03);
}

/* 新拟态按钮 */
.neo-button {
  padding: 12px 24px;
  border-radius: var(--radius-sm);
  background: var(--color-background);
  box-shadow: var(--neo-shadow-light), var(--neo-shadow-dark);
  transition: all 0.2s ease;
  color: var(--color-text-primary);
  font-weight: 500;
}

.neo-button:active {
  box-shadow: var(--neo-inset);
  transform: translateY(1px);
}

/* 渐变背景 */
.gradient-peach {
  background: linear-gradient(135deg, var(--color-peach-fuzz), var(--color-peach-light));
}

/* 手势交互区域 */
.swipe-area {
  touch-action: pan-x pan-y;
  user-select: none;
  -webkit-user-select: none;
}

/* 动画 */
@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.animate-slide-up {
  animation: slideUp 0.3s ease-out forwards;
}

/* 响应式布局基础 */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

/* 字体系统 */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  color: var(--color-text-primary);
  background: var(--color-background);
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 文字样式 */
.text-h1 {
  font-size: 32px;
  font-weight: 700;
  letter-spacing: -0.02em;
}

.text-h2 {
  font-size: 24px;
  font-weight: 600;
  letter-spacing: -0.01em;
}

.text-body {
  font-size: 16px;
  font-weight: 400;
}

.text-caption {
  font-size: 14px;
  color: var(--color-text-secondary);
}

/* 颜色变量 */
--color-background: #f8fafc;
--color-text-primary: #1f2937;
--color-text-secondary: #4b5563;
--color-text-tertiary: #9ca3af;
--color-primary: #8b5cf6;
--color-primary-light: #a78bfa;
--color-primary-dark: #7c3aed;
--color-success: #10b981;
--color-warning: #f59e0b;
--color-error: #ef4444;

/* 阴影变量 */
--shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
--shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
--shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);

/* 圆角变量 */
--radius-sm: 0.25rem;
--radius-md: 0.375rem;
--radius-lg: 0.5rem;
--radius-xl: 1rem;
--radius-full: 9999px;

/* 动画变量 */
--transition-base: all 0.3s ease;
--transition-smooth: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);

/* 基础重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 通用工具类 */
.glass-effect {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.form-input {
  background: rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.5);
  transition: var(--transition-base);
}

.form-input:focus {
  background: rgba(255, 255, 255, 0.6);
  border-color: var(--color-primary);
  outline: none;
  box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.2);
}

/* 动画类 */
.fade-in {
  animation: fadeIn 0.3s ease-in;
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

/* 响应式设计 */
@media (max-width: 640px) {
  :root {
    --radius-lg: 0.375rem;
    --radius-xl: 0.75rem;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  :root {
    --color-background: #1a1a1a;
    --color-text-primary: #f3f4f6;
    --color-text-secondary: #d1d5db;
    --color-text-tertiary: #9ca3af;
  }
}

/* 交互按钮基础样式 */
.action-button {
  @apply glass-button text-[10px] px-3 py-1.5 rounded-full flex items-center space-x-1 transition-all duration-300;
}

.action-button i {
  @apply transition-transform duration-300;
}

.action-button:active i {
  @apply scale-125;
}

/* 点赞按钮样式 */
.like-button {
  @apply action-button;
}

.like-button i {
  @apply text-[var(--color-like)] opacity-60;
}

.like-button.active i {
  @apply opacity-100;
}

.like-button:hover i {
  @apply opacity-80;
}

/* 收藏按钮样式 */
.favorite-button {
  @apply action-button;
}

.favorite-button i {
  @apply text-[var(--color-favorite)] opacity-60;
}

.favorite-button.active i {
  @apply opacity-100;
}

.favorite-button:hover i {
  @apply opacity-80;
}

/* 分享按钮样式 */
.share-button {
  @apply action-button;
}

.share-button i {
  @apply text-[var(--color-share)] opacity-60;
}

.share-button:hover i {
  @apply opacity-80;
}

/* 按钮组样式 */
.action-buttons-group {
  @apply flex space-x-2;
}

.glass-button {
  background: rgba(255,255,255,0.4);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  border: 1px solid rgba(255,255,255,0.5);
  box-shadow: 0 8px 32px rgba(0,0,0,0.05);
  transition: all 0.3s ease;
  color: var(--color-text-primary);
}
.glass-button:hover {
  background: rgba(255,255,255,0.6);
} 