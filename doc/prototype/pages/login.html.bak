<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - StylishLink</title>
    <link href="../styles/main.css" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="min-h-screen bg-gradient-to-br from-purple-100 to-indigo-200 flex items-center justify-center p-4">
    <div class="w-full max-w-md glass-effect rounded-xl p-8 space-y-6 fade-in">
        <div class="text-center">
            <h1 class="text-3xl font-bold text-gray-800 mb-2">欢迎使用</h1>
            <p class="text-gray-600">登录StylishLink开启您的穿搭之旅</p>
        </div>

        <form class="space-y-4">
            <div>
                <label for="phone" class="block text-sm font-medium text-gray-700 mb-1">手机号码</label>
                <div class="relative">
                    <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                        <i class="fas fa-phone"></i>
                    </span>
                    <input type="tel" id="phone" name="phone" 
                           class="form-input w-full pl-10 pr-4 py-2 rounded-lg"
                           placeholder="请输入手机号码" required>
                </div>
            </div>

            <div>
                <label for="code" class="block text-sm font-medium text-gray-700 mb-1">验证码</label>
                <div class="relative flex gap-2">
                    <div class="relative flex-1">
                        <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                            <i class="fas fa-lock"></i>
                        </span>
                        <input type="text" id="code" name="code" 
                               class="form-input w-full pl-10 pr-4 py-2 rounded-lg"
                               placeholder="请输入验证码" required>
                    </div>
                    <button type="button" 
                            class="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors">
                        获取验证码
                    </button>
                </div>
            </div>

            <div class="flex items-center justify-between text-sm">
                <label class="flex items-center">
                    <input type="checkbox" class="form-checkbox h-4 w-4 text-indigo-600">
                    <span class="ml-2 text-gray-600">记住我</span>
                </label>
                <a href="#" class="text-indigo-600 hover:text-indigo-700">需要帮助？</a>
            </div>

            <button type="submit" 
                    class="w-full bg-indigo-600 text-white py-2 px-4 rounded-lg hover:bg-indigo-700 transition-colors">
                登录
            </button>
        </form>

        <div class="text-center">
            <p class="text-gray-600">
                还没有账号？
                <a href="#" class="text-indigo-600 hover:text-indigo-700">立即注册</a>
            </p>
        </div>

        <div class="text-center text-sm text-gray-500">
            <p>登录即代表您同意</p>
            <p class="mt-1">
                <a href="#" class="text-indigo-600 hover:text-indigo-700">《用户协议》</a>
                和
                <a href="#" class="text-indigo-600 hover:text-indigo-700">《隐私政策》</a>
            </p>
        </div>
    </div>

    <script>
        // 简单的表单验证
        document.querySelector('form').addEventListener('submit', function(e) {
            e.preventDefault();
            const phone = document.getElementById('phone').value;
            const code = document.getElementById('code').value;
            
            if (!phone.match(/^1[3-9]\d{9}$/)) {
                alert('请输入正确的手机号码');
                return;
            }
            
            if (!code.match(/^\d{6}$/)) {
                alert('请输入6位数字验证码');
                return;
            }
            
            // 这里添加实际的登录逻辑
            window.location.href = 'basic-info.html';
        });

        // 获取验证码按钮倒计时
        const codeButton = document.querySelector('button[type="button"]');
        codeButton.addEventListener('click', function() {
            let countdown = 60;
            this.disabled = true;
            const timer = setInterval(() => {
                this.textContent = `${countdown}秒后重试`;
                countdown--;
                if (countdown < 0) {
                    clearInterval(timer);
                    this.textContent = '获取验证码';
                    this.disabled = false;
                }
            }, 1000);
        });
    </script>
</body>
</html> 