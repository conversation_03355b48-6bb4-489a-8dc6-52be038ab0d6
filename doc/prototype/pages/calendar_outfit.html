<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <title>日历搭配 - StylishLink</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../styles/main.css" rel="stylesheet">
    <style>
        ::-webkit-scrollbar {
            display: none;
        }
        
        * {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        
        .content-container {
            padding-left: 5%;
            padding-right: 5%;
        }

        .outfit-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            grid-template-rows: repeat(2, 1fr);
            gap: 12px;
            aspect-ratio: 1;
        }

        .outfit-main {
            grid-row: span 2;
            grid-column: span 2;
        }
        
        .gradient-background {
            background: linear-gradient(135deg, #c2e9fb 0%, #d8c6fd 100%);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: -1;
        }
        
        .enhanced-glass {
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.05);
        }
        
        .glass-button {
            background: rgba(255, 255, 255, 0.4);
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.5);
            transition: all 0.3s ease;
        }
        
        .glass-button:hover {
            background: rgba(255, 255, 255, 0.6);
        }
        
        .glow-icon {
            box-shadow: 0 0 15px rgba(255, 255, 255, 0.8);
        }

        /* 日历样式 */
        .calendar-grid {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 2px;
        }
        
        .calendar-day {
            aspect-ratio: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            border-radius: 8px;
            font-size: 10px;
            position: relative;
        }
        
        .calendar-day.active {
            background: rgba(255, 255, 255, 0.5);
            font-weight: 500;
        }
        
        .calendar-day.has-outfit::after {
            content: '';
            position: absolute;
            bottom: 2px;
            width: 4px;
            height: 4px;
            border-radius: 50%;
            background-color: rgba(255, 154, 158, 0.8);
        }
        
        /* 五行标识色彩 */
        .wuxing-jin {
            background: linear-gradient(135deg, #ffffff, #f0f0f0);
            color: #1a1a1a;
        }
        
        .wuxing-mu {
            background: linear-gradient(135deg, #a8e6cf, #73c1a8);
            color: #1a1a1a;
        }
        
        .wuxing-shui {
            background: linear-gradient(135deg, #b8c6db, #648dae);
            color: #ffffff;
        }
        
        .wuxing-huo {
            background: linear-gradient(135deg, #ff9a9e, #ff5458);
            color: #ffffff;
        }
        
        .wuxing-tu {
            background: linear-gradient(135deg, #ffeaa7, #ffc25c);
            color: #1a1a1a;
        }
        
        /* 日期选择器按钮样式 */
        .date-selector {
            cursor: pointer;
            transition: background-color 0.2s ease;
        }
        
        .date-selector:active {
            background-color: rgba(255, 255, 255, 0.3);
        }

        /* 模态框样式 */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 100;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease, visibility 0.3s ease;
        }
        
        .modal.active {
            opacity: 1;
            visibility: visible;
        }
        
        .modal-content {
            width: 90%;
            max-width: 350px;
            max-height: 90vh;
            overflow-y: auto;
            transform: translateY(20px);
            transition: transform 0.3s ease;
        }
        
        .modal.active .modal-content {
            transform: translateY(0);
        }
    </style>
</head>
<body>
    <!-- 渐变背景 -->
    <div class="gradient-background"></div>

    <!-- 主内容区 -->
    <div class="pt-2 pb-20 h-full overflow-y-scroll">
        <div class="content-container py-2">
            <!-- 顶部标题区 -->
            <div class="flex items-center justify-between mb-3">
                <div class="flex items-center">
                    <a href="outfit.html" class="mr-2">
                        <i class="fas fa-arrow-left text-[var(--color-text-primary)]"></i>
                    </a>
                    <h1 class="text-sm font-semibold text-[var(--color-text-primary)]">创建日历搭配</h1>
                </div>
                <button class="w-6 h-6 flex items-center justify-center rounded-full bg-white/40 backdrop-blur-sm">
                    <i class="fas fa-sliders text-[var(--color-text-primary)] text-xs"></i>
                </button>
            </div>

            <!-- 选中日期信息 -->
            <div class="enhanced-glass rounded-xl p-3 mb-3">
                <div class="flex justify-between items-start mb-2">
                    <div class="flex items-center">
                        <!-- 日历图标按钮 -->
                        <div id="calendar-button" class="w-8 h-8 rounded-full bg-white/40 flex items-center justify-center mr-2 date-selector">
                            <i class="fas fa-calendar-days text-[var(--color-text-primary)]"></i>
                        </div>
                        <div>
                            <h2 class="text-xs font-medium text-[var(--color-text-primary)]">3月18日 星期二</h2>
                            <p class="text-[10px] text-[var(--color-text-secondary)]">农历：壬寅年二月初八</p>
                        </div>
                    </div>
                    <div class="px-2 py-1 rounded-full bg-white/30 text-[10px] text-[var(--color-text-secondary)]">
                        今天
                    </div>
                </div>
                
                <!-- 天气与五行信息 -->
                <div class="flex space-x-3 mb-3">
                    <div class="flex items-center">
                        <i class="fas fa-sun text-[var(--color-text-secondary)] mr-1 text-[10px]"></i>
                        <span class="text-[10px] text-[var(--color-text-secondary)]">晴 22°C</span>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-yin-yang text-[var(--color-text-secondary)] mr-1 text-[10px]"></i>
                        <span class="text-[10px] text-[var(--color-text-secondary)]">今日五行：金</span>
                    </div>
                </div>
                
                <!-- 五行命理提示 -->
                <div class="p-2 rounded-lg wuxing-jin mb-3">
                    <p class="text-[10px]">
                        <i class="fas fa-circle-info mr-1"></i>
                        宜穿白色系提升运势，适量添加木属性元素平衡整体
                    </p>
                </div>
                
                <!-- 搭配条件设置 -->
                <h3 class="text-xs font-medium text-[var(--color-text-primary)] mb-2">搭配条件</h3>
                <div class="space-y-2 mb-2">
                    <!-- 场合选择 -->
                    <div class="flex flex-wrap gap-2">
                        <button class="h-6 px-3 rounded-full bg-white/50 text-[var(--color-text-primary)] text-[10px] flex items-center whitespace-nowrap">
                            <i class="fas fa-briefcase mr-1"></i>工作
                        </button>
                        <button class="h-6 px-3 rounded-full bg-white/30 backdrop-blur-sm text-[var(--color-text-secondary)] text-[10px] flex items-center whitespace-nowrap">
                            <i class="fas fa-champagne-glasses mr-1"></i>约会
                        </button>
                        <button class="h-6 px-3 rounded-full bg-white/30 backdrop-blur-sm text-[var(--color-text-secondary)] text-[10px] flex items-center whitespace-nowrap">
                            <i class="fas fa-sun mr-1"></i>休闲
                        </button>
                        <button class="h-6 px-3 rounded-full bg-white/30 backdrop-blur-sm text-[var(--color-text-secondary)] text-[10px] flex items-center whitespace-nowrap">
                            <i class="fas fa-plane mr-1"></i>旅行
                        </button>
                    </div>
                    
                    <!-- 风格偏好 -->
                    <div class="flex items-center">
                        <span class="text-[10px] text-[var(--color-text-secondary)] w-14">风格：</span>
                        <select class="bg-white/30 text-[10px] text-[var(--color-text-primary)] rounded-md px-2 py-1 w-full">
                            <option>正式商务</option>
                            <option>职场简约</option>
                            <option>轻松休闲</option>
                            <option>约会浪漫</option>
                        </select>
                    </div>
                    
                    <!-- 必选单品 -->
                    <div class="flex items-center">
                        <span class="text-[10px] text-[var(--color-text-secondary)] w-14">必选：</span>
                        <button class="bg-white/30 text-[10px] text-[var(--color-text-primary)] rounded-md px-2 py-1 w-full text-left">
                            <i class="fas fa-plus mr-1"></i>添加必选单品
                        </button>
                    </div>
                    
                    <!-- 特殊条件 -->
                    <div class="flex items-center">
                        <span class="text-[10px] text-[var(--color-text-secondary)] w-14">特殊：</span>
                        <select class="bg-white/30 text-[10px] text-[var(--color-text-primary)] rounded-md px-2 py-1 w-full">
                            <option>无特殊要求</option>
                            <option>重要会议</option>
                            <option>户外活动</option>
                            <option>摄影拍照</option>
                        </select>
                    </div>
                </div>
                
                <!-- 生成搭配按钮 -->
                <button class="glass-button w-full py-2 rounded-md text-[var(--color-text-primary)] text-xs mt-3">
                    <i class="fas fa-wand-magic-sparkles mr-1"></i>
                    生成搭配方案
                </button>
            </div>
            
            <!-- 常用条件模板 -->
            <div class="mb-3">
                <h3 class="text-xs font-medium text-[var(--color-text-primary)] mb-2">常用条件模板</h3>
                <div class="flex overflow-x-auto space-x-2 pb-2">
                    <div class="enhanced-glass rounded-xl p-2 min-w-[100px]">
                        <p class="text-[10px] font-medium text-[var(--color-text-primary)]">工作日常</p>
                        <p class="text-[8px] text-[var(--color-text-secondary)]">职场简约风格</p>
                    </div>
                    <div class="enhanced-glass rounded-xl p-2 min-w-[100px]">
                        <p class="text-[10px] font-medium text-[var(--color-text-primary)]">周末休闲</p>
                        <p class="text-[8px] text-[var(--color-text-secondary)]">轻松舒适风格</p>
                    </div>
                    <div class="enhanced-glass rounded-xl p-2 min-w-[100px]">
                        <p class="text-[10px] font-medium text-[var(--color-text-primary)]">重要会议</p>
                        <p class="text-[8px] text-[var(--color-text-secondary)]">正式商务风格</p>
                    </div>
                    <div class="enhanced-glass rounded-xl p-2 min-w-[100px]">
                        <p class="text-[10px] font-medium text-[var(--color-text-primary)]">添加模板</p>
                        <p class="text-[8px] text-[var(--color-text-secondary)]">保存当前条件</p>
                    </div>
                </div>
            </div>

            <!-- 日历搭配历史 -->
            <div class="enhanced-glass rounded-xl p-3 mb-3">
                <div class="flex justify-between items-center mb-2">
                    <h3 class="text-xs font-semibold text-[var(--color-text-primary)]">近期搭配计划</h3>
                    <a href="#" class="text-[10px] text-[var(--color-text-primary)]">查看全部</a>
                </div>
                
                <!-- 日历简略显示 -->
                <div class="mb-2">
                    <div class="calendar-grid mb-1">
                        <div class="text-[8px] text-center text-[var(--color-text-secondary)]">日</div>
                        <div class="text-[8px] text-center text-[var(--color-text-secondary)]">一</div>
                        <div class="text-[8px] text-center text-[var(--color-text-secondary)]">二</div>
                        <div class="text-[8px] text-center text-[var(--color-text-secondary)]">三</div>
                        <div class="text-[8px] text-center text-[var(--color-text-secondary)]">四</div>
                        <div class="text-[8px] text-center text-[var(--color-text-secondary)]">五</div>
                        <div class="text-[8px] text-center text-[var(--color-text-secondary)]">六</div>
                    </div>
                    
                    <div class="calendar-grid">
                        <div class="calendar-day text-[var(--color-text-tertiary)]">16</div>
                        <div class="calendar-day text-[var(--color-text-tertiary)]">17</div>
                        <div class="calendar-day text-[var(--color-text-primary)] active">18</div>
                        <div class="calendar-day text-[var(--color-text-primary)] has-outfit">19</div>
                        <div class="calendar-day text-[var(--color-text-primary)]">20</div>
                        <div class="calendar-day text-[var(--color-text-primary)]">21</div>
                        <div class="calendar-day text-[var(--color-text-primary)]">22</div>
                    </div>
                </div>
                
                <!-- 已安排搭配列表 -->
                <div class="space-y-2">
                    <div class="flex justify-between items-center py-2 border-b border-white/20">
                        <div>
                            <p class="text-xs text-[var(--color-text-primary)]">3月19日 · 周三</p>
                            <p class="text-[10px] text-[var(--color-text-secondary)]">职场简约风格</p>
                        </div>
                        <button class="glass-button text-[10px] px-2 py-0.5 rounded-md">
                            查看
                        </button>
                    </div>
                    <div class="flex justify-between items-center py-2">
                        <div>
                            <p class="text-xs text-[var(--color-text-primary)]">3月13日 · 周四</p>
                            <p class="text-[10px] text-[var(--color-text-secondary)]">重要会议风格</p>
                        </div>
                        <button class="glass-button text-[10px] px-2 py-0.5 rounded-md">
                            查看
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 日历模态框 -->
    <div id="calendar-modal" class="modal">
        <div class="modal-content enhanced-glass rounded-xl p-4">
            <!-- 模态框头部 -->
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-sm font-medium text-[var(--color-text-primary)]">选择日期</h3>
                <button id="close-modal" class="w-6 h-6 flex items-center justify-center rounded-full bg-white/40">
                    <i class="fas fa-xmark text-[var(--color-text-primary)]"></i>
                </button>
            </div>
            
            <!-- 月份选择器 -->
            <div class="flex justify-between items-center mb-3">
                <button class="w-6 h-6 flex items-center justify-center rounded-full bg-white/30">
                    <i class="fas fa-chevron-left text-[var(--color-text-secondary)] text-[8px]"></i>
                </button>
                <h2 class="text-xs font-medium text-[var(--color-text-primary)]">2025年3月</h2>
                <button class="w-6 h-6 flex items-center justify-center rounded-full bg-white/30">
                    <i class="fas fa-chevron-right text-[var(--color-text-secondary)] text-[8px]"></i>
                </button>
            </div>
            
            <!-- 日历星期头部 -->
            <div class="calendar-grid mb-1">
                <div class="text-[10px] text-[var(--color-text-secondary)] text-center py-1">日</div>
                <div class="text-[10px] text-[var(--color-text-secondary)] text-center py-1">一</div>
                <div class="text-[10px] text-[var(--color-text-secondary)] text-center py-1">二</div>
                <div class="text-[10px] text-[var(--color-text-secondary)] text-center py-1">三</div>
                <div class="text-[10px] text-[var(--color-text-secondary)] text-center py-1">四</div>
                <div class="text-[10px] text-[var(--color-text-secondary)] text-center py-1">五</div>
                <div class="text-[10px] text-[var(--color-text-secondary)] text-center py-1">六</div>
            </div>
            
            <!-- 日历完整视图 -->
            <div class="calendar-grid mb-4">
                <!-- 第一行 -->
                <div class="calendar-day text-[var(--color-text-tertiary)]">23</div>
                <div class="calendar-day text-[var(--color-text-tertiary)]">24</div>
                <div class="calendar-day text-[var(--color-text-tertiary)]">25</div>
                <div class="calendar-day text-[var(--color-text-tertiary)]">26</div>
                <div class="calendar-day text-[var(--color-text-tertiary)]">27</div>
                <div class="calendar-day text-[var(--color-text-tertiary)]">28</div>
                <div class="calendar-day text-[var(--color-text-primary)]">1</div>
                
                <!-- 第二行 -->
                <div class="calendar-day text-[var(--color-text-primary)]">2</div>
                <div class="calendar-day text-[var(--color-text-primary)]">3</div>
                <div class="calendar-day text-[var(--color-text-primary)]">4</div>
                <div class="calendar-day text-[var(--color-text-primary)]">5</div>
                <div class="calendar-day text-[var(--color-text-primary)]">6</div>
                <div class="calendar-day text-[var(--color-text-primary)]">7</div>
                <div class="calendar-day text-[var(--color-text-primary)]">8</div>
                
                <!-- 第三行 -->
                <div class="calendar-day text-[var(--color-text-primary)]">9</div>
                <div class="calendar-day text-[var(--color-text-primary)]">10</div>
                <div class="calendar-day text-[var(--color-text-primary)]">11</div>
                <div class="calendar-day text-[var(--color-text-primary)]">12</div>
                <div class="calendar-day text-[var(--color-text-primary)] has-outfit">13</div>
                <div class="calendar-day text-[var(--color-text-primary)] has-outfit">14</div>
                <div class="calendar-day text-[var(--color-text-primary)]">15</div>
                
                <!-- 第四行 -->
                <div class="calendar-day text-[var(--color-text-primary)]">16</div>
                <div class="calendar-day text-[var(--color-text-primary)]">17</div>
                <div class="calendar-day text-[var(--color-text-primary)] active">18</div>
                <div class="calendar-day text-[var(--color-text-primary)] has-outfit">19</div>
                <div class="calendar-day text-[var(--color-text-primary)]">20</div>
                <div class="calendar-day text-[var(--color-text-primary)]">21</div>
                <div class="calendar-day text-[var(--color-text-primary)]">22</div>
                
                <!-- 第五行 -->
                <div class="calendar-day text-[var(--color-text-primary)]">23</div>
                <div class="calendar-day text-[var(--color-text-primary)]">24</div>
                <div class="calendar-day text-[var(--color-text-primary)]">25</div>
                <div class="calendar-day text-[var(--color-text-primary)]">26</div>
                <div class="calendar-day text-[var(--color-text-primary)]">27</div>
                <div class="calendar-day text-[var(--color-text-primary)]">28</div>
                <div class="calendar-day text-[var(--color-text-primary)]">29</div>
                
                <!-- 第六行 -->
                <div class="calendar-day text-[var(--color-text-primary)]">30</div>
                <div class="calendar-day text-[var(--color-text-primary)]">31</div>
                <div class="calendar-day text-[var(--color-text-tertiary)]">1</div>
                <div class="calendar-day text-[var(--color-text-tertiary)]">2</div>
                <div class="calendar-day text-[var(--color-text-tertiary)]">3</div>
                <div class="calendar-day text-[var(--color-text-tertiary)]">4</div>
                <div class="calendar-day text-[var(--color-text-tertiary)]">5</div>
            </div>
            
            <!-- 日历底部按钮 -->
            <button class="glass-button w-full py-2 rounded-md text-[var(--color-text-primary)] text-xs">
                <i class="fas fa-check mr-1"></i>
                确认选择
            </button>
        </div>
    </div>

    <!-- 底部导航栏 -->
    <div class="fixed bottom-0 left-0 right-0 bg-white/60 backdrop-blur-md border-t border-white/60">
        <div class="grid grid-cols-4 h-20">
            <a href="home.html" class="flex flex-col items-center justify-center text-[var(--color-text-secondary)]">
                <i class="fas fa-home text-xl"></i>
                <span class="text-xs mt-1">首页</span>
            </a>
            <a href="wardrobe.html" class="flex flex-col items-center justify-center text-[var(--color-text-secondary)]">
                <i class="fas fa-tshirt text-xl"></i>
                <span class="text-xs mt-1">衣橱</span>
            </a>
            <a href="outfit.html" class="flex flex-col items-center justify-center text-[var(--color-text-primary)]">
                <div class="w-10 h-10 rounded-full bg-white/70 backdrop-blur-md flex items-center justify-center">
                    <i class="fas fa-wand-magic-sparkles text-lg"></i>
                </div>
                <span class="text-xs mt-1 font-medium">搭配</span>
            </a>
            <a href="profile.html" class="flex flex-col items-center justify-center text-[var(--color-text-secondary)]">
                <i class="fas fa-user text-xl"></i>
                <span class="text-xs mt-1">我的</span>
            </a>
        </div>
    </div>
    
    <script>
        // 日历模态框控制
        const calendarButton = document.getElementById('calendar-button');
        const calendarModal = document.getElementById('calendar-modal');
        const closeModal = document.getElementById('close-modal');
        
        // 打开日历模态框
        calendarButton.addEventListener('click', function() {
            calendarModal.classList.add('active');
            document.body.style.overflow = 'hidden'; // 防止背景滚动
        });
        
        // 关闭日历模态框
        closeModal.addEventListener('click', function() {
            calendarModal.classList.remove('active');
            document.body.style.overflow = ''; // 恢复背景滚动
        });
        
        // 点击日历外部区域关闭
        calendarModal.addEventListener('click', function(e) {
            if (e.target === calendarModal) {
                calendarModal.classList.remove('active');
                document.body.style.overflow = '';
            }
        });
        
        // 日历日期点击
        const calendarDays = document.querySelectorAll('.calendar-day');
        calendarDays.forEach(day => {
            day.addEventListener('click', function() {
                // 移除其他日期的active状态
                calendarDays.forEach(d => d.classList.remove('active'));
                // 添加当前日期的active状态
                this.classList.add('active');
                // 更新日期信息（实际应用中应该是动态获取数据）
                console.log('选择日期: ' + this.textContent);
            });
        });
        
        // 场景标签点击事件
        const sceneTabs = document.querySelectorAll('.flex.flex-wrap.gap-2 button');
        sceneTabs.forEach(tab => {
            tab.addEventListener('click', function() {
                // 移除其他标签的active状态
                sceneTabs.forEach(t => {
                    t.classList.remove('bg-white/50');
                    t.classList.add('bg-white/30');
                    t.classList.remove('text-[var(--color-text-primary)]');
                    t.classList.add('text-[var(--color-text-secondary)]');
                });
                // 添加当前标签的active状态
                this.classList.remove('bg-white/30');
                this.classList.add('bg-white/50');
                this.classList.remove('text-[var(--color-text-secondary)]');
                this.classList.add('text-[var(--color-text-primary)]');
                // 更新场景内容（实际应用中应该是异步加载数据）
                console.log('切换到场景: ' + this.textContent.trim());
            });
        });
    </script>
</body>
</html> 