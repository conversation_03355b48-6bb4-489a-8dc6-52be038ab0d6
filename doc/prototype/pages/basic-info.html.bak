<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>基本信息 - StylishLink</title>
    <link href="../styles/main.css" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .bg-custom-gradient {
            background: linear-gradient(135deg, #c2e9fb 0%, #d8c6fd 100%);
        }
        .glass-effect {
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        .form-input {
            background: rgba(255, 255, 255, 0.4);
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.5);
            transition: all 0.3s ease;
        }
        .form-input:focus {
            background: rgba(255, 255, 255, 0.6);
            border-color: #8B5CF6;
            outline: none;
            box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.2);
        }
    </style>
</head>
<body class="min-h-screen bg-gradient-to-br from-purple-100 to-indigo-200 flex items-center justify-center p-4">
    <div class="w-full max-w-2xl glass-effect rounded-xl p-8 space-y-6 fade-in">
        <div class="text-center">
            <h1 class="text-3xl font-bold text-gray-800 mb-2">完善基本信息</h1>
            <p class="text-gray-600">请填写您的基本信息，帮助我们为您提供更好的服务</p>
        </div>

        <form class="space-y-6" onsubmit="handleSubmit(event)">
            <!-- 头像上传 -->
            <div class="flex flex-col items-center space-y-4">
                <div class="relative">
                    <div class="w-24 h-24 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden">
                        <img id="avatar-preview" src="#" alt="" class="w-full h-full object-cover hidden">
                        <i id="avatar-placeholder" class="fas fa-user text-4xl text-gray-400"></i>
                    </div>
                    <label for="avatar-upload" class="absolute bottom-0 right-0 w-8 h-8 bg-indigo-600 rounded-full flex items-center justify-center cursor-pointer hover:bg-indigo-700 transition-colors">
                        <i class="fas fa-camera text-white text-sm"></i>
                    </label>
                    <input type="file" id="avatar-upload" class="hidden" accept="image/*">
                </div>
                <p class="text-sm text-gray-500">点击上传头像</p>
            </div>

            <!-- 基本信息表单 -->
            <div class="space-y-4">
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label for="nickname" class="block text-sm font-medium text-gray-700 mb-1">昵称</label>
                        <input type="text" id="nickname" name="nickname" 
                               class="form-input w-full rounded-lg"
                               placeholder="请输入昵称" required>
                    </div>
                    <div>
                        <label for="gender" class="block text-sm font-medium text-gray-700 mb-1">性别</label>
                        <select id="gender" name="gender" 
                                class="form-input w-full rounded-lg" required>
                            <option value="">请选择</option>
                            <option value="male">男</option>
                            <option value="female">女</option>
                            <option value="other">其他</option>
                        </select>
                    </div>
                </div>

                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label for="birthday" class="block text-sm font-medium text-gray-700 mb-1">生日</label>
                        <input type="date" id="birthday" name="birthday" 
                               class="form-input w-full rounded-lg" required>
                    </div>
                    <div>
                        <label for="location" class="block text-sm font-medium text-gray-700 mb-1">所在地</label>
                        <input type="text" id="location" name="location" 
                               class="form-input w-full rounded-lg"
                               placeholder="请输入所在地" required>
                    </div>
                </div>

                <div>
                    <label for="bio" class="block text-sm font-medium text-gray-700 mb-1">个人简介</label>
                    <textarea id="bio" name="bio" rows="3" 
                              class="form-input w-full rounded-lg resize-none"
                              placeholder="请简单介绍一下自己（选填）"></textarea>
                </div>
            </div>

            <div class="flex justify-between items-center pt-4">
                <button type="button" onclick="history.back()" 
                        class="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
                    返回
                </button>
                <button type="submit" 
                        class="px-6 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors">
                    下一步
                </button>
            </div>
        </form>
    </div>

    <script>
        // 头像上传预览
        document.getElementById('avatar-upload').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const preview = document.getElementById('avatar-preview');
                    const placeholder = document.getElementById('avatar-placeholder');
                    preview.src = e.target.result;
                    preview.classList.remove('hidden');
                    placeholder.classList.add('hidden');
                }
                reader.readAsDataURL(file);
            }
        });

        // 表单提交处理
        function handleSubmit(e) {
            e.preventDefault();
            const formData = new FormData(e.target);
            const data = Object.fromEntries(formData.entries());
            
            // 这里可以添加数据验证逻辑
            
            // 保存数据并跳转
            localStorage.setItem('basicInfo', JSON.stringify(data));
            window.location.href = 'optional-info.html';
        }
    </script>
</body>
</html> 