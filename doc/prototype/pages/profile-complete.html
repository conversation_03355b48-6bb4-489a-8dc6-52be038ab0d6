<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>资料完成 - StylishLink</title>
    <link href="../styles/main.css" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .bg-custom-gradient {
            background: linear-gradient(135deg, #c2e9fb 0%, #d8c6fd 100%);
        }
        .glass-effect {
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        .form-input {
            background: rgba(255, 255, 255, 0.4);
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.5);
            transition: all 0.3s ease;
        }
        .form-input:focus {
            background: rgba(255, 255, 255, 0.6);
            border-color: #8B5CF6;
            outline: none;
            box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.2);
        }
        .info-card {
            transition: transform 0.3s ease;
        }
        .info-card:hover {
            transform: translateY(-2px);
        }
    </style>
</head>
<body class="min-h-screen bg-gradient-to-br from-purple-100 to-indigo-200 flex items-center justify-center p-4">
    <div class="w-full max-w-md glass-effect rounded-xl p-8 space-y-8 fade-in">
        <div class="text-center space-y-4">
            <!-- 成功图标 -->
            <div class="inline-flex items-center justify-center w-20 h-20 rounded-full bg-green-100 text-green-500 mb-4">
                <i class="fas fa-check text-4xl"></i>
            </div>
            
            <h1 class="text-3xl font-bold text-gray-800">资料完善成功！</h1>
            <p class="text-gray-600">感谢您完善个人资料，现在开始享受个性化的穿搭推荐吧</p>
        </div>

        <!-- 用户信息预览 -->
        <div class="space-y-6">
            <div class="flex items-center space-x-4">
                <div class="w-16 h-16 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden">
                    <img id="avatar-preview" src="#" alt="" class="w-full h-full object-cover hidden">
                    <i id="avatar-placeholder" class="fas fa-user text-2xl text-gray-400"></i>
                </div>
                <div>
                    <h2 id="user-nickname" class="text-xl font-semibold text-gray-800">用户昵称</h2>
                    <p id="user-location" class="text-gray-600">所在地区</p>
                </div>
            </div>

            <div class="grid grid-cols-2 gap-4">
                <div class="p-4 rounded-lg bg-white/30">
                    <div class="text-sm text-gray-600 mb-1">个人风格</div>
                    <div id="style-tags" class="flex flex-wrap gap-2">
                        <!-- 风格标签将通过 JavaScript 动态添加 -->
                    </div>
                </div>
                <div class="p-4 rounded-lg bg-white/30">
                    <div class="text-sm text-gray-600 mb-1">颜色偏好</div>
                    <div id="color-tags" class="flex flex-wrap gap-2">
                        <!-- 颜色标签将通过 JavaScript 动态添加 -->
                    </div>
                </div>
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="space-y-4">
            <button onclick="window.location.href = '../index.html'" 
                    class="w-full py-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors">
                开始体验
            </button>
            <button onclick="editProfile()" 
                    class="w-full py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
                修改资料
            </button>
        </div>
    </div>

    <script>
        // 页面加载时显示用户信息
        document.addEventListener('DOMContentLoaded', function() {
            // 获取基本信息
            const basicInfo = JSON.parse(localStorage.getItem('basicInfo') || '{}');
            const optionalInfo = JSON.parse(localStorage.getItem('optionalInfo') || '{}');
            
            // 更新用户信息显示
            document.getElementById('user-nickname').textContent = basicInfo.nickname || '新用户';
            document.getElementById('user-location').textContent = basicInfo.location || '未设置地区';
            
            // 显示头像
            if (basicInfo.avatar) {
                const preview = document.getElementById('avatar-preview');
                const placeholder = document.getElementById('avatar-placeholder');
                preview.src = basicInfo.avatar;
                preview.classList.remove('hidden');
                placeholder.classList.add('hidden');
            }
            
            // 添加风格标签
            const styleContainer = document.getElementById('style-tags');
            const styles = (optionalInfo.stylePreferences || '').split(',').filter(Boolean);
            styles.forEach(style => {
                const tag = document.createElement('span');
                tag.className = 'px-2 py-1 text-xs rounded-full bg-indigo-100 text-indigo-600';
                tag.textContent = getStyleLabel(style);
                styleContainer.appendChild(tag);
            });
            
            // 添加颜色标签
            const colorContainer = document.getElementById('color-tags');
            const colors = (optionalInfo.colorPreferences || '').split(',').filter(Boolean);
            colors.forEach(color => {
                const tag = document.createElement('span');
                tag.className = 'px-2 py-1 text-xs rounded-full bg-indigo-100 text-indigo-600';
                tag.textContent = getColorLabel(color);
                colorContainer.appendChild(tag);
            });
        });
        
        // 获取风格标签显示文本
        function getStyleLabel(value) {
            const styleMap = {
                casual: '休闲',
                formal: '正式',
                sports: '运动',
                elegant: '优雅',
                street: '街头',
                vintage: '复古'
            };
            return styleMap[value] || value;
        }
        
        // 获取颜色标签显示文本
        function getColorLabel(value) {
            const colorMap = {
                black: '黑色',
                white: '白色',
                gray: '灰色',
                blue: '蓝色',
                red: '红色',
                pink: '粉色',
                purple: '紫色',
                green: '绿色'
            };
            return colorMap[value] || value;
        }
        
        // 修改资料
        function editProfile() {
            window.location.href = 'basic-info.html';
        }
    </script>
</body>
</html> 