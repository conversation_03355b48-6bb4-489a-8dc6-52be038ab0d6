<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>可选信息 - StylishLink</title>
    <link href="../styles/main.css" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .bg-custom-gradient {
            background: linear-gradient(135deg, #c2e9fb 0%, #d8c6fd 100%);
        }
        .glass-effect {
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        .form-input {
            background: rgba(255, 255, 255, 0.4);
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.5);
            transition: all 0.3s ease;
        }
        .form-input:focus {
            background: rgba(255, 255, 255, 0.6);
            border-color: #8B5CF6;
            outline: none;
            box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.2);
        }
        .style-tag {
            transition: all 0.3s ease;
        }
        .style-tag.selected {
            background: #8B5CF6;
            color: white;
            border-color: #8B5CF6;
        }
    </style>
</head>
<body class="min-h-screen bg-gradient-to-br from-purple-100 to-indigo-200 flex items-center justify-center p-4">
    <div class="w-full max-w-2xl glass-effect rounded-xl p-8 space-y-6 fade-in">
        <div class="text-center">
            <h1 class="text-3xl font-bold text-gray-800 mb-2">完善可选信息</h1>
            <p class="text-gray-600">这些信息将帮助我们为您提供更精准的穿搭推荐</p>
        </div>

        <form class="space-y-6" onsubmit="handleSubmit(event)">
            <div class="space-y-4">
                <!-- 身材信息 -->
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label for="height" class="block text-sm font-medium text-gray-700 mb-1">身高 (cm)</label>
                        <input type="number" id="height" name="height" 
                               class="form-input w-full rounded-lg"
                               placeholder="请输入身高">
                    </div>
                    <div>
                        <label for="weight" class="block text-sm font-medium text-gray-700 mb-1">体重 (kg)</label>
                        <input type="number" id="weight" name="weight" 
                               class="form-input w-full rounded-lg"
                               placeholder="请输入体重">
                    </div>
                </div>

                <!-- 体型和肤色 -->
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">体型</label>
                        <div class="grid grid-cols-3 gap-2">
                            <button type="button" class="body-type-btn px-4 py-2 rounded-lg form-input text-center" data-value="slim">
                                偏瘦
                            </button>
                            <button type="button" class="body-type-btn px-4 py-2 rounded-lg form-input text-center" data-value="normal">
                                标准
                            </button>
                            <button type="button" class="body-type-btn px-4 py-2 rounded-lg form-input text-center" data-value="chubby">
                                偏胖
                            </button>
                        </div>
                        <input type="hidden" name="bodyType" id="bodyType">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">肤色</label>
                        <div class="grid grid-cols-3 gap-2">
                            <button type="button" class="skin-tone-btn px-4 py-2 rounded-lg form-input text-center" data-value="fair">
                                白皙
                            </button>
                            <button type="button" class="skin-tone-btn px-4 py-2 rounded-lg form-input text-center" data-value="medium">
                                自然
                            </button>
                            <button type="button" class="skin-tone-btn px-4 py-2 rounded-lg form-input text-center" data-value="dark">
                                小麦色
                            </button>
                        </div>
                        <input type="hidden" name="skinTone" id="skinTone">
                    </div>
                </div>

                <!-- 风格偏好 -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">风格偏好（可多选）</label>
                    <div class="grid grid-cols-3 gap-2">
                        <button type="button" class="style-btn px-4 py-2 rounded-lg form-input text-center" data-value="casual">
                            休闲
                        </button>
                        <button type="button" class="style-btn px-4 py-2 rounded-lg form-input text-center" data-value="formal">
                            正式
                        </button>
                        <button type="button" class="style-btn px-4 py-2 rounded-lg form-input text-center" data-value="sports">
                            运动
                        </button>
                        <button type="button" class="style-btn px-4 py-2 rounded-lg form-input text-center" data-value="elegant">
                            优雅
                        </button>
                        <button type="button" class="style-btn px-4 py-2 rounded-lg form-input text-center" data-value="street">
                            街头
                        </button>
                        <button type="button" class="style-btn px-4 py-2 rounded-lg form-input text-center" data-value="vintage">
                            复古
                        </button>
                    </div>
                    <input type="hidden" name="stylePreferences" id="stylePreferences">
                </div>

                <!-- 颜色偏好 -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">颜色偏好（可多选）</label>
                    <div class="grid grid-cols-4 gap-2">
                        <button type="button" class="color-btn px-4 py-2 rounded-lg form-input text-center" data-value="black">
                            黑色
                        </button>
                        <button type="button" class="color-btn px-4 py-2 rounded-lg form-input text-center" data-value="white">
                            白色
                        </button>
                        <button type="button" class="color-btn px-4 py-2 rounded-lg form-input text-center" data-value="gray">
                            灰色
                        </button>
                        <button type="button" class="color-btn px-4 py-2 rounded-lg form-input text-center" data-value="blue">
                            蓝色
                        </button>
                        <button type="button" class="color-btn px-4 py-2 rounded-lg form-input text-center" data-value="red">
                            红色
                        </button>
                        <button type="button" class="color-btn px-4 py-2 rounded-lg form-input text-center" data-value="pink">
                            粉色
                        </button>
                        <button type="button" class="color-btn px-4 py-2 rounded-lg form-input text-center" data-value="purple">
                            紫色
                        </button>
                        <button type="button" class="color-btn px-4 py-2 rounded-lg form-input text-center" data-value="green">
                            绿色
                        </button>
                    </div>
                    <input type="hidden" name="colorPreferences" id="colorPreferences">
                </div>
            </div>

            <div class="flex justify-between items-center pt-4">
                <button type="button" onclick="history.back()" 
                        class="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
                    返回
                </button>
                <div class="space-x-4">
                    <button type="button" onclick="skipOptionalInfo()" 
                            class="px-6 py-2 text-gray-600 hover:text-gray-800 transition-colors">
                        跳过
                    </button>
                    <button type="submit" 
                            class="px-6 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors">
                        下一步
                    </button>
                </div>
            </div>
        </form>
    </div>

    <script>
        // 按钮选择处理
        function setupButtonGroup(selector, isMultiple = false) {
            const buttons = document.querySelectorAll(selector);
            const hiddenInput = document.getElementById(selector.split('-')[0].slice(1));
            
            buttons.forEach(button => {
                button.addEventListener('click', () => {
                    if (isMultiple) {
                        button.classList.toggle('selected');
                        button.classList.toggle('bg-indigo-600');
                        button.classList.toggle('text-white');
                    } else {
                        buttons.forEach(b => {
                            b.classList.remove('selected', 'bg-indigo-600', 'text-white');
                        });
                        button.classList.add('selected', 'bg-indigo-600', 'text-white');
                    }
                    
                    // 更新隐藏输入值
                    const selectedButtons = Array.from(buttons).filter(b => b.classList.contains('selected'));
                    const values = selectedButtons.map(b => b.dataset.value);
                    hiddenInput.value = isMultiple ? values.join(',') : values[0] || '';
                });
            });
        }

        // 初始化按钮组
        setupButtonGroup('.body-type-btn');
        setupButtonGroup('.skin-tone-btn');
        setupButtonGroup('.style-btn', true);
        setupButtonGroup('.color-btn', true);

        // 表单提交处理
        function handleSubmit(e) {
            e.preventDefault();
            const formData = new FormData(e.target);
            const data = Object.fromEntries(formData.entries());
            
            // 保存数据并跳转
            localStorage.setItem('optionalInfo', JSON.stringify(data));
            window.location.href = 'profile-complete.html';
        }

        // 跳过可选信息
        function skipOptionalInfo() {
            window.location.href = 'profile-complete.html';
        }
    </script>
</body>
</html> 