<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <title>我的衣橱 - StylishLink</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <link href="../styles/main.css" rel="stylesheet">
    <style>
        ::-webkit-scrollbar {
            display: none;
        }
        
        * {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        
        .content-container {
            padding-left: 5%;
            padding-right: 5%;
        }

        .category-scroll {
            scrollbar-width: none;
            -ms-overflow-style: none;
            -webkit-overflow-scrolling: touch;
        }
        
        .gradient-background {
            background: linear-gradient(135deg, #c2e9fb 0%, #d8c6fd 100%);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: -1;
        }
        
        .enhanced-glass {
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.05);
        }
        
        .glass-button {
            background: rgba(255, 255, 255, 0.4);
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.5);
            transition: all 0.3s ease;
        }
        
        .glass-button:hover {
            background: rgba(255, 255, 255, 0.6);
        }
        
        .glow-icon {
            box-shadow: 0 0 15px rgba(255, 255, 255, 0.8);
        }
        
        /* 雷达图样式 */
        .radar-chart {
            position: relative;
            width: 120px;
            height: 120px;
            background-image: url('data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg" width="120" height="120" viewBox="0 0 120 120"><polygon fill="rgba(168, 230, 207, 0.3)" points="60,60 100,80 90,110 30,100 20,50" stroke="rgba(168, 230, 207, 0.8)" stroke-width="1"/><circle cx="60" cy="60" r="50" fill="none" stroke="rgba(255,255,255,0.2)" stroke-width="1" stroke-dasharray="3,3"/><circle cx="60" cy="60" r="35" fill="none" stroke="rgba(255,255,255,0.2)" stroke-width="1" stroke-dasharray="3,3"/><circle cx="60" cy="60" r="20" fill="none" stroke="rgba(255,255,255,0.2)" stroke-width="1" stroke-dasharray="3,3"/><line x1="60" y1="10" x2="60" y2="110" stroke="rgba(255,255,255,0.2)" stroke-width="1" stroke-dasharray="3,3"/><line x1="10" y1="60" x2="110" y2="60" stroke="rgba(255,255,255,0.2)" stroke-width="1" stroke-dasharray="3,3"/><line x1="25" y1="25" x2="95" y2="95" stroke="rgba(255,255,255,0.2)" stroke-width="1" stroke-dasharray="3,3"/><line x1="25" y1="95" x2="95" y2="25" stroke="rgba(255,255,255,0.2)" stroke-width="1" stroke-dasharray="3,3"/></svg>');
            background-size: contain;
            background-repeat: no-repeat;
        }
        
        /* 进度条样式 */
        .progress-bar {
            height: 6px;
            border-radius: 3px;
            background: rgba(255, 255, 255, 0.3);
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            border-radius: 3px;
            background: linear-gradient(90deg, #a8e6cf, #73c1a8);
        }
    </style>
</head>
<body>
    <!-- 渐变背景 -->
    <div class="gradient-background"></div>

    <!-- 主内容区 -->
    <div class="pt-2 pb-20 h-full overflow-y-scroll">
        <div class="content-container py-2">
            <!-- 顶部搜索栏 -->
            <div class="flex items-center justify-between mb-3">
                <h1 class="text-sm font-semibold text-[var(--color-text-primary)]">我的衣橱</h1>
                <div class="flex items-center space-x-2">
                    <button class="w-6 h-6 flex items-center justify-center rounded-full bg-white/40 backdrop-blur-sm">
                        <i class="fas fa-chart-pie text-[var(--color-text-primary)] text-xs"></i>
                    </button>
                    <button class="w-6 h-6 flex items-center justify-center rounded-full bg-white/40 backdrop-blur-sm">
                        <i class="fas fa-search text-[var(--color-text-primary)] text-xs"></i>
                    </button>
                </div>
            </div>

            <!-- 衣橱统计分析卡片（新增） -->
            <div class="enhanced-glass rounded-xl p-3 mb-3">
                <div class="flex justify-between items-start">
                    <div>
                        <h2 class="text-xs font-semibold text-[var(--color-text-primary)] mb-1">衣橱分析</h2>
                        <div class="flex items-center space-x-1 mb-1">
                            <span class="text-sm font-medium text-[var(--color-text-primary)]">126</span>
                            <span class="text-[10px] text-[var(--color-text-secondary)]">件单品</span>
                            <span class="text-[10px] px-1.5 py-0.5 rounded-full bg-white/30 text-[var(--color-text-secondary)]">
                                超过82%用户
                            </span>
                        </div>
                        <p class="text-[10px] text-[var(--color-text-secondary)]">您的风格定位：
                            <span class="text-[var(--color-text-primary)]">洒脱自然风格</span>
                        </p>
                    </div>
                    <button class="glass-button text-[10px] px-2 py-1 rounded-md text-[var(--color-text-primary)]" onclick="location.href='../wardrobe/wardrobe_analysis.html'">
                        查看详情
                    </button>
                </div>
            </div>

            <!-- 分类标签滚动区 -->
            <div class="overflow-x-auto category-scroll mb-3">
                <div class="flex space-x-2 pb-2">
                    <button class="h-8 px-4 rounded-full bg-white/50 text-[var(--color-text-primary)] text-xs flex items-center whitespace-nowrap">
                        <i class="ri-archive-fill mr-1.5 text-sm"></i>全部(126)
                    </button>
                    <button class="h-8 px-4 rounded-full bg-white/30 backdrop-blur-sm text-[var(--color-text-secondary)] text-xs flex items-center whitespace-nowrap">
                        <i class="ri-t-shirt-fill mr-1.5 text-sm"></i>上装(38)
                    </button>
                    <button class="h-8 px-4 rounded-full bg-white/30 backdrop-blur-sm text-[var(--color-text-secondary)] text-xs flex items-center whitespace-nowrap">
                        <i class="ri-pants-fill mr-1.5 text-sm"></i>裤子(27)
                    </button>
                    <button class="h-8 px-4 rounded-full bg-white/30 backdrop-blur-sm text-[var(--color-text-secondary)] text-xs flex items-center whitespace-nowrap">
                        <i class="ri-shirt-fill mr-1.5 text-sm"></i>外套(22)
                    </button>
                    <button class="h-8 px-4 rounded-full bg-white/30 backdrop-blur-sm text-[var(--color-text-secondary)] text-xs flex items-center whitespace-nowrap">
                        <i class="ri-footprint-fill mr-1.5 text-sm"></i>鞋子(15)
                    </button>
                    <button class="h-8 px-4 rounded-full bg-white/30 backdrop-blur-sm text-[var(--color-text-secondary)] text-xs flex items-center whitespace-nowrap">
                        <i class="ri-handbag-fill mr-1.5 text-sm"></i>配饰(24)
                    </button>
                </div>
            </div>

            <!-- 衣物展示网格 -->
            <div class="grid grid-cols-3 gap-2">
                <!-- 衣物卡片 -->
                <div class="enhanced-glass rounded-xl overflow-hidden cursor-pointer" onclick="location.href='../wardrobe/clothing_detail.html'">
                    <div class="aspect-square bg-white/20 relative">
                        <img src="https://images.unsplash.com/photo-1434389677669-e08b4cac3105" 
                             class="w-full h-full object-cover" alt="白色T恤">
                    </div>
                    <div class="p-2">
                        <p class="text-xs font-medium text-[var(--color-text-primary)]">春夏可穿</p>
                    </div>
                </div>

                <div class="enhanced-glass rounded-xl overflow-hidden cursor-pointer" onclick="location.href='../wardrobe/clothing_detail.html'">
                    <div class="aspect-square bg-white/20 relative">
                        <img src="https://images.unsplash.com/photo-1475178626620-a4d074967452" 
                             class="w-full h-full object-cover" alt="牛仔裤">
                    </div>
                    <div class="p-2">
                        <p class="text-xs font-medium text-[var(--color-text-primary)]">百搭</p>
                    </div>
                </div>

                <div class="enhanced-glass rounded-xl overflow-hidden cursor-pointer" onclick="location.href='../wardrobe/clothing_detail.html'">
                    <div class="aspect-square bg-white/20 relative">
                        <img src="https://images.unsplash.com/photo-1591047139829-d91aecb6caea" 
                             class="w-full h-full object-cover" alt="针织开衫">
                    </div>
                    <div class="p-2">
                        <p class="text-xs font-medium text-[var(--color-text-primary)]">春秋季节</p>
                    </div>
                </div>

                <div class="enhanced-glass rounded-xl overflow-hidden cursor-pointer" onclick="location.href='../wardrobe/clothing_detail.html'">
                    <div class="aspect-square bg-white/20 relative">
                        <img src="https://images.unsplash.com/photo-1560769629-975ec94e6a86" 
                             class="w-full h-full object-cover" alt="运动鞋">
                    </div>
                    <div class="p-2">
                        <p class="text-xs font-medium text-[var(--color-text-primary)]">日常休闲</p>
                    </div>
                </div>
                
                <div class="enhanced-glass rounded-xl overflow-hidden cursor-pointer" onclick="location.href='../wardrobe/clothing_detail.html'">
                    <div class="aspect-square bg-white/20 relative">
                        <img src="https://images.unsplash.com/photo-1551232864-3f0890e580d9" 
                             class="w-full h-full object-cover" alt="黑色连衣裙">
                    </div>
                    <div class="p-2">
                        <p class="text-xs font-medium text-[var(--color-text-primary)]">正式场合</p>
                    </div>
                </div>
                
                <div class="enhanced-glass rounded-xl overflow-hidden cursor-pointer" onclick="location.href='../wardrobe/clothing_detail.html'">
                    <div class="aspect-square bg-white/20 relative">
                        <img src="https://images.unsplash.com/photo-1543076447-215ad9ba6923" 
                             class="w-full h-full object-cover" alt="珍珠耳环">
                    </div>
                    <div class="p-2">
                        <p class="text-xs font-medium text-[var(--color-text-primary)]">搭配裙装</p>
                    </div>
                </div>

                <!-- 新增卡片 -->
                <div class="enhanced-glass rounded-xl overflow-hidden cursor-pointer" onclick="location.href='../wardrobe/clothing_detail.html'">
                    <div class="aspect-square bg-white/20 relative">
                        <img src="https://images.unsplash.com/photo-1525507119028-ed4c629a60a3" 
                             class="w-full h-full object-cover" alt="蓝色西装">
                    </div>
                    <div class="p-2">
                        <p class="text-xs font-medium text-[var(--color-text-primary)]">商务正式</p>
                    </div>
                </div>
                
                <div class="enhanced-glass rounded-xl overflow-hidden cursor-pointer" onclick="location.href='../wardrobe/clothing_detail.html'">
                    <div class="aspect-square bg-white/20 relative">
                        <img src="https://images.unsplash.com/photo-1541099649105-f69ad21f3246" 
                             class="w-full h-full object-cover" alt="墨镜">
                    </div>
                    <div class="p-2">
                        <p class="text-xs font-medium text-[var(--color-text-primary)]">夏季必备</p>
                    </div>
                </div>
                
                <div class="enhanced-glass rounded-xl overflow-hidden cursor-pointer" onclick="location.href='../wardrobe/clothing_detail.html'">
                    <div class="aspect-square bg-white/20 relative">
                        <img src="https://images.unsplash.com/photo-1551107696-a4b0c5a0d9a2" 
                             class="w-full h-full object-cover" alt="绿色连衣裙">
                    </div>
                    <div class="p-2">
                        <p class="text-xs font-medium text-[var(--color-text-primary)]">约会聚会</p>
                    </div>
                </div>
                
                <div class="enhanced-glass rounded-xl overflow-hidden cursor-pointer" onclick="location.href='../wardrobe/clothing_detail.html'">
                    <div class="aspect-square bg-white/20 relative">
                        <img src="https://images.unsplash.com/photo-1617627143750-d86bc21e42bb" 
                             class="w-full h-full object-cover" alt="米色手提包">
                    </div>
                    <div class="p-2">
                        <p class="text-xs font-medium text-[var(--color-text-primary)]">职场百搭</p>
                    </div>
                </div>
                
                <div class="enhanced-glass rounded-xl overflow-hidden cursor-pointer" onclick="location.href='../wardrobe/clothing_detail.html'">
                    <div class="aspect-square bg-white/20 relative">
                        <img src="https://images.unsplash.com/photo-1575428652377-a2d80e2277fc" 
                             class="w-full h-full object-cover" alt="黑色高跟鞋">
                    </div>
                    <div class="p-2">
                        <p class="text-xs font-medium text-[var(--color-text-primary)]">提升气场</p>
                    </div>
                </div>
                
                <div class="enhanced-glass rounded-xl overflow-hidden cursor-pointer" onclick="location.href='../wardrobe/clothing_detail.html'">
                    <div class="aspect-square bg-white/20 relative">
                        <img src="https://images.unsplash.com/photo-1516975080664-ed2fc6a32937" 
                             class="w-full h-full object-cover" alt="针织帽">
                    </div>
                    <div class="p-2">
                        <p class="text-xs font-medium text-[var(--color-text-primary)]">秋冬保暖</p>
                    </div>
                </div>
                
                <div class="enhanced-glass rounded-xl overflow-hidden cursor-pointer" onclick="location.href='../wardrobe/clothing_detail.html'">
                    <div class="aspect-square bg-white/20 relative">
                        <img src="https://images.unsplash.com/photo-1551488831-00ddcb6c6bd3" 
                             class="w-full h-full object-cover" alt="花卉围巾">
                    </div>
                    <div class="p-2">
                        <p class="text-xs font-medium text-[var(--color-text-primary)]">增添亮点</p>
                    </div>
                </div>
                
                <div class="enhanced-glass rounded-xl overflow-hidden cursor-pointer" onclick="location.href='../wardrobe/clothing_detail.html'">
                    <div class="aspect-square bg-white/20 relative">
                        <img src="https://images.unsplash.com/photo-1606760227091-3dd870d97f1d" 
                             class="w-full h-full object-cover" alt="金色项链">
                    </div>
                    <div class="p-2">
                        <p class="text-xs font-medium text-[var(--color-text-primary)]">点缀气质</p>
                    </div>
                </div>
                
                <div class="enhanced-glass rounded-xl overflow-hidden cursor-pointer" onclick="location.href='../wardrobe/clothing_detail.html'">
                    <div class="aspect-square bg-white/20 relative">
                        <img src="https://images.unsplash.com/photo-1523381294911-8d3cead13475" 
                             class="w-full h-full object-cover" alt="运动卫衣">
                    </div>
                    <div class="p-2">
                        <p class="text-xs font-medium text-[var(--color-text-primary)]">运动健身</p>
                    </div>
                </div>
                
                <div class="enhanced-glass rounded-xl overflow-hidden cursor-pointer" onclick="location.href='../wardrobe/clothing_detail.html'">
                    <div class="aspect-square bg-white/20 relative">
                        <img src="https://images.unsplash.com/photo-1578587018452-892bacefd3f2" 
                             class="w-full h-full object-cover" alt="通勤风格衬衫">
                    </div>
                    <div class="p-2">
                        <p class="text-xs font-medium text-[var(--color-text-primary)]">通勤必备</p>
                    </div>
                </div>
                
                <div class="enhanced-glass rounded-xl overflow-hidden cursor-pointer" onclick="location.href='../wardrobe/clothing_detail.html'">
                    <div class="aspect-square bg-white/20 relative">
                        <img src="https://images.unsplash.com/photo-1552374196-1ab2a1c593e8" 
                             class="w-full h-full object-cover" alt="休闲外套">
                    </div>
                    <div class="p-2">
                        <p class="text-xs font-medium text-[var(--color-text-primary)]">休闲约会</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部导航栏 -->
    <div class="fixed bottom-0 left-0 right-0 bg-white/60 backdrop-blur-md border-t border-white/60">
        <div class="grid grid-cols-4 h-20">
            <a href="home.html" class="flex flex-col items-center justify-center text-[var(--color-text-secondary)]">
                <i class="fas fa-home text-xl"></i>
                <span class="text-xs mt-1">首页</span>
            </a>
            <a href="wardrobe.html" class="flex flex-col items-center justify-center text-[var(--color-text-primary)]">
                <div class="w-10 h-10 rounded-full bg-white/70 backdrop-blur-md flex items-center justify-center">
                    <i class="fas fa-tshirt text-lg"></i>
                </div>
                <span class="text-xs mt-1 font-medium">衣橱</span>
            </a>
            <a href="outfit.html" class="flex flex-col items-center justify-center text-[var(--color-text-secondary)]">
                <i class="fas fa-wand-magic-sparkles text-xl"></i>
                <span class="text-xs mt-1">搭配</span>
            </a>
            <a href="profile.html" class="flex flex-col items-center justify-center text-[var(--color-text-secondary)]">
                <i class="fas fa-user text-xl"></i>
                <span class="text-xs mt-1">我的</span>
            </a>
        </div>
    </div>

    <!-- 悬浮添加按钮 -->
    <button class="fixed right-5 bottom-24 w-14 h-14 rounded-full glass-button flex items-center justify-center shadow-lg glow-icon" onclick="location.href='../wardrobe/add_clothing.html'">
        <i class="fas fa-plus text-[var(--color-text-primary)] text-xl"></i>
    </button>
    
    <!-- 衣橱分析弹窗 (隐藏，需要时显示) -->
    <div class="fixed inset-0 bg-black/20 backdrop-blur-sm z-50 items-center justify-center hidden" id="analysisModal">
        <div class="enhanced-glass m-4 rounded-xl p-4 max-w-md w-full max-h-[80vh] overflow-y-auto">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-sm font-semibold text-[var(--color-text-primary)]">衣橱详细分析</h2>
                <button class="w-6 h-6 flex items-center justify-center rounded-full bg-white/40">
                    <i class="fas fa-xmark text-[var(--color-text-primary)]"></i>
                </button>
            </div>
            
            <div class="flex justify-between mb-4">
                <div class="radar-chart"></div>
                <div class="space-y-2">
                    <div class="flex items-center">
                        <div class="w-3 h-3 rounded-full bg-[#a8e6cf]"></div>
                        <span class="text-[10px] text-[var(--color-text-secondary)] ml-1">多样性</span>
                    </div>
                    <div class="flex items-center">
                        <div class="w-3 h-3 rounded-full bg-[#ffd3b6]"></div>
                        <span class="text-[10px] text-[var(--color-text-secondary)] ml-1">时尚度</span>
                    </div>
                    <div class="flex items-center">
                        <div class="w-3 h-3 rounded-full bg-[#ffaaa5]"></div>
                        <span class="text-[10px] text-[var(--color-text-secondary)] ml-1">实用性</span>
                    </div>
                    <div class="flex items-center">
                        <div class="w-3 h-3 rounded-full bg-[#b8c6db]"></div>
                        <span class="text-[10px] text-[var(--color-text-secondary)] ml-1">季节均衡</span>
                    </div>
                    <div class="flex items-center">
                        <div class="w-3 h-3 rounded-full bg-[#ffc25c]"></div>
                        <span class="text-[10px] text-[var(--color-text-secondary)] ml-1">五行均衡</span>
                    </div>
                </div>
            </div>
            
            <div class="mb-4">
                <h3 class="text-xs font-medium text-[var(--color-text-primary)] mb-2">五行属性分布</h3>
                <div class="grid grid-cols-5 gap-2">
                    <div class="flex flex-col items-center">
                        <div class="w-full h-16 bg-white/20 rounded-t-md relative">
                            <div class="absolute bottom-0 left-0 right-0 bg-[#ffffff] h-[60%] rounded-t-md"></div>
                        </div>
                        <span class="text-[10px] text-[var(--color-text-secondary)] mt-1">金 60%</span>
                    </div>
                    <div class="flex flex-col items-center">
                        <div class="w-full h-16 bg-white/20 rounded-t-md relative">
                            <div class="absolute bottom-0 left-0 right-0 bg-[#a8e6cf] h-[45%] rounded-t-md"></div>
                        </div>
                        <span class="text-[10px] text-[var(--color-text-secondary)] mt-1">木 45%</span>
                    </div>
                    <div class="flex flex-col items-center">
                        <div class="w-full h-16 bg-white/20 rounded-t-md relative">
                            <div class="absolute bottom-0 left-0 right-0 bg-[#b8c6db] h-[30%] rounded-t-md"></div>
                        </div>
                        <span class="text-[10px] text-[var(--color-text-secondary)] mt-1">水 30%</span>
                    </div>
                    <div class="flex flex-col items-center">
                        <div class="w-full h-16 bg-white/20 rounded-t-md relative">
                            <div class="absolute bottom-0 left-0 right-0 bg-[#ff9a9e] h-[25%] rounded-t-md"></div>
                        </div>
                        <span class="text-[10px] text-[var(--color-text-secondary)] mt-1">火 25%</span>
                    </div>
                    <div class="flex flex-col items-center">
                        <div class="w-full h-16 bg-white/20 rounded-t-md relative">
                            <div class="absolute bottom-0 left-0 right-0 bg-[#ffeaa7] h-[50%] rounded-t-md"></div>
                        </div>
                        <span class="text-[10px] text-[var(--color-text-secondary)] mt-1">土 50%</span>
                    </div>
                </div>
            </div>
            
            <div class="mb-4">
                <h3 class="text-xs font-medium text-[var(--color-text-primary)] mb-2">衣橱优化建议</h3>
                <div class="space-y-2 text-[10px] text-[var(--color-text-secondary)]">
                    <p>● 建议添加更多火属性服饰，平衡五行</p>
                    <p>● 工作场合服饰偏少，可增加正装单品</p>
                    <p>● 有6件超过3个月未穿的单品，建议考虑搭配或处理</p>
                    <p>● 秋季单品较少，建议适当添加</p>
                </div>
            </div>
            
            <button class="glass-button w-full py-2 rounded-md text-[var(--color-text-primary)] text-xs">
                导出完整分析报告
            </button>
        </div>
    </div>
    
    <script>
        // 分析详情按钮点击事件
        document.querySelector('button.glass-button:contains("查看详情")').addEventListener('click', function() {
            document.getElementById('analysisModal').classList.remove('hidden');
            document.getElementById('analysisModal').classList.add('flex');
        });
        
        // 关闭弹窗事件
        document.querySelector('#analysisModal button').addEventListener('click', function() {
            document.getElementById('analysisModal').classList.add('hidden');
            document.getElementById('analysisModal').classList.remove('flex');
        });
    </script>
</body>
</html> 