<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>StylishLink - 预览</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="styles/main.css" rel="stylesheet">
    <style>
        .device-frame {
            width: 340px;
            height: 734px;
            border-radius: 42px;
            overflow: hidden;
            position: relative;
            background: #000;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }

        .device-frame iframe {
            width: 100%;
            height: 100%;
            border: none;
        }

        .preview-title {
            position: absolute;
            bottom: -40px;
            left: 0;
            right: 0;
            text-align: center;
            color: var(--color-text-primary);
            font-size: 14px;
        }

        .preview-section {
            margin-bottom: 4rem;
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 2rem;
            color: var(--color-text-primary);
            text-align: center;
        }
    </style>
</head>
<body class="bg-[var(--color-background)] min-h-screen">
    <div class="container mx-auto p-8">
        <h1 class="text-2xl font-bold mb-12 text-center text-[var(--color-text-primary)]">StylishLink 界面预览</h1>
        
        <!-- 用户引导流程 -->
        <div class="preview-section">
            <h2 class="section-title">用户引导流程</h2>
            <div class="flex flex-wrap justify-center items-start gap-16">
                <!-- 用户信息页面 -->
                <div class="relative">
                    <div class="device-frame">
                        <iframe src="pages/user-info.html" frameborder="0"></iframe>
                    </div>
                    <div class="preview-title">用户信息填写（自然/能量模式）</div>
                </div>
            </div>
        </div>

        <!-- 主要功能页面 -->
        <div class="preview-section">
            <h2 class="section-title">主要功能页面</h2>
            <div class="flex flex-wrap justify-center items-start gap-16">
                <!-- 首页预览 -->
                <div class="relative">
                    <div class="device-frame">
                        <iframe src="pages/home.html" frameborder="0"></iframe>
                    </div>
                    <div class="preview-title">首页</div>
                </div>

                <!-- 衣橱预览 -->
                <div class="relative">
                    <div class="device-frame">
                        <iframe src="pages/wardrobe.html" frameborder="0"></iframe>
                    </div>
                    <div class="preview-title">衣橱</div>
                </div>

                <!-- 搭配预览 -->
                <div class="relative">
                    <div class="device-frame">
                        <iframe src="pages/outfit.html" frameborder="0"></iframe>
                    </div>
                    <div class="preview-title">搭配</div>
                </div>

                <!-- 个人中心预览 -->
                <div class="relative">
                    <div class="device-frame">
                        <iframe src="pages/profile.html" frameborder="0"></iframe>
                    </div>
                    <div class="preview-title">个人中心</div>
                </div>
            </div>
        </div>
        
        <!-- 首页二级页面 -->
        <div class="preview-section">
            <h2 class="section-title">首页模块二级页面</h2>
            <div class="flex flex-wrap justify-center items-start gap-16">
                <!-- 运势详情 -->
                <div class="relative">
                    <div class="device-frame">
                        <iframe src="home/fortune_detail.html" frameborder="0"></iframe>
                    </div>
                    <div class="preview-title">运势详情</div>
                </div>

                <!-- 搭配详情 -->
                <div class="relative">
                    <div class="device-frame">
                        <iframe src="home/outfit_detail.html" frameborder="0"></iframe>
                    </div>
                    <div class="preview-title">搭配详情</div>
                </div>

                <!-- 搭配视频 -->
                <div class="relative">
                    <div class="device-frame">
                        <iframe src="home/outfit_video.html" frameborder="0"></iframe>
                    </div>
                    <div class="preview-title">搭配视频</div>
                </div>

                <!-- 风格建议 -->
                <div class="relative">
                    <div class="device-frame">
                        <iframe src="home/style_advice.html" frameborder="0"></iframe>
                    </div>
                    <div class="preview-title">风格建议</div>
                </div>
            </div>
        </div>
        
        <!-- 衣橱二级页面 -->
        <div class="preview-section">
            <h2 class="section-title">衣橱模块二级页面</h2>
            <div class="flex flex-wrap justify-center items-start gap-16">
                <!-- 添加单品 -->
                <div class="relative">
                    <div class="device-frame">
                        <iframe src="wardrobe/add_clothing.html" frameborder="0"></iframe>
                    </div>
                    <div class="preview-title">添加单品</div>
                </div>

                <!-- 单品详情 -->
                <div class="relative">
                    <div class="device-frame">
                        <iframe src="wardrobe/clothing_detail.html" frameborder="0"></iframe>
                    </div>
                    <div class="preview-title">单品详情</div>
                </div>

                <!-- 衣橱分析 -->
                <div class="relative">
                    <div class="device-frame">
                        <iframe src="wardrobe/wardrobe_analysis.html" frameborder="0"></iframe>
                    </div>
                    <div class="preview-title">衣橱分析</div>
                </div>
            </div>
        </div>
        
        <!-- 搭配二级页面 -->
        <div class="preview-section">
            <h2 class="section-title">搭配模块二级页面</h2>
            <div class="flex flex-wrap justify-center items-start gap-16">
                <!-- 创建搭配 -->
                <div class="relative">
                    <div class="device-frame">
                        <iframe src="outfit/custom_outfit.html" frameborder="0"></iframe>
                    </div>
                    <div class="preview-title">创建搭配</div>
                </div>

                <!-- 预览搭配 -->
                <div class="relative">
                    <div class="device-frame">
                        <iframe src="outfit/outfit_preview.html" frameborder="0"></iframe>
                    </div>
                    <div class="preview-title">场景搭配</div>
                </div>
            </div>
        </div>
        
        <!-- 个人中心二级页面 -->
        <div class="preview-section">
            <h2 class="section-title">个人中心模块二级页面</h2>
            <div class="flex flex-wrap justify-center items-start gap-16">
                <!-- 设置 -->
                <div class="relative">
                    <div class="device-frame">
                        <iframe src="profile/settings.html" frameborder="0"></iframe>
                    </div>
                    <div class="preview-title">设置</div>
                </div>
                
                <!-- 灵感值中心 -->
                <div class="relative">
                    <div class="device-frame">
                        <iframe src="profile/inspiration_center.html" frameborder="0"></iframe>
                    </div>
                    <div class="preview-title">灵感值中心</div>
                </div>
                
                <!-- 收藏夹 -->
                <div class="relative">
                    <div class="device-frame">
                        <iframe src="profile/favorites.html" frameborder="0"></iframe>
                    </div>
                    <div class="preview-title">收藏夹</div>
                </div>
                
                <!-- 风格诊断-->
                <div class="relative">
                    <div class="device-frame">
                        <iframe src="home/style_advice.html" frameborder="0"></iframe>
                    </div>
                    <div class="preview-title">风格诊断</div>
                </div>
            </div>
        </div>
    </div>
</body>
</html> 