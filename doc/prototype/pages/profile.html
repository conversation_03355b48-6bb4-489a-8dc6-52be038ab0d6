<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <title>个人中心 - StylishLink</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../styles/main.css" rel="stylesheet">
    <style>
        ::-webkit-scrollbar {
            display: none;
        }
        
        * {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        
        .content-container {
            padding-left: 5%;
            padding-right: 5%;
        }
        
        .gradient-background {
            background: linear-gradient(135deg, #c2e9fb 0%, #d8c6fd 100%);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: -1;
        }
        
        .enhanced-glass {
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.05);
        }
        
        .glass-button {
            background: rgba(255, 255, 255, 0.4);
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.5);
            transition: all 0.3s ease;
        }
        
        .glass-button:hover {
            background: rgba(255, 255, 255, 0.6);
        }
        
        /* 五行标识色彩 */
        .wuxing-jin {
            background: linear-gradient(135deg, #ffffff, #f0f0f0);
            color: #1a1a1a;
        }
        
        .wuxing-mu {
            background: linear-gradient(135deg, #a8e6cf, #73c1a8);
            color: #1a1a1a;
        }
        
        .wuxing-shui {
            background: linear-gradient(135deg, #b8c6db, #648dae);
            color: #ffffff;
        }
        
        .wuxing-huo {
            background: linear-gradient(135deg, #ff9a9e, #ff5458);
            color: #ffffff;
        }
        
        .wuxing-tu {
            background: linear-gradient(135deg, #ffeaa7, #ffc25c);
            color: #1a1a1a;
        }
        
        /* 功能入口样式 */
        .function-entry {
            width: 50px;
            height: 50px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            transition: transform 0.2s ease;
            margin-bottom: 8px;
        }
        
        .function-entry:active {
            transform: scale(0.95);
        }
        
        .function-icon {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(255, 255, 255, 0.6);
            backdrop-filter: blur(8px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            overflow: hidden;
            aspect-ratio: 1;
        }
        
        /* 基础信息项样式 */
        .info-item {
            display: flex;
            align-items: center;
            margin-bottom: 4px;
        }
        
        .info-item span {
            margin-right: 0;
        }
        
        /* 虚拟形象容器 */
        .profile-card {
            position: relative;
            width: 100%;
            overflow: hidden;
            height: 70vh;
        }
        
        .profile-image {
            position: absolute;
            width: 100%;
            height: 100%;
            object-fit: cover;
            z-index: 1;
        }
        
        .info-panel {
            position: absolute;
            top: 10px;
            left: 10px;
            z-index: 10;
            padding: 4px;
            width: 32%;
        }
        
        .function-panel {
            position: absolute;
            top: 20px;
            right: 10px;
            z-index: 10;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        
        /* 下拉选择器样式 */
        .selector-wrapper {
            position: relative;
            z-index: 20;
        }
        
        .selector-wrapper.active {
            z-index: 40;
        }
        
        .selector-button {
            background: rgba(255, 255, 255, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.5);
            border-radius: 8px;
            padding: 4px 8px;
            font-size: 12px;
            color: var(--color-text-secondary);
            text-align: left;
            transition: all 0.3s ease;
            width: 100px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .selector-button span {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            flex: 1;
            margin-right: 4px;
        }
        
        .selector-button:disabled {
            background: transparent;
            border: none;
            padding: 0;
            opacity: 1;
            width: auto;
            min-width: 60px;
            justify-content: flex-end;
        }
        
        .selector-button:disabled .fa-chevron-down {
            display: none;
        }
        
        .selector-button:not(:disabled):hover {
            background: rgba(255, 255, 255, 0.4);
        }
        
        .dropdown-menu {
            position: absolute;
            top: calc(100% + 4px);
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.5);
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            max-height: 160px;
            overflow-y: auto;
            z-index: 30;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.2s ease;
            width: 100px;
        }
        
        .dropdown-menu.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }
        
        .dropdown-item {
            padding: 6px 8px;
            font-size: 12px;
            color: var(--color-text-primary);
            transition: all 0.2s ease;
            cursor: pointer;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        .dropdown-item:hover {
            background: rgba(255, 255, 255, 0.5);
        }
        
        .dropdown-item.selected {
            background: rgba(168, 230, 207, 0.3);
        }

        /* 移除旧的下拉框样式 */
        .select-box {
            display: none;
        }
    </style>
</head>
<body>
    <!-- 渐变背景 -->
    <div class="gradient-background"></div>

    <!-- 主内容区 -->
    <div class="pt-2 pb-20 h-full">
        <div class="content-container py-2">
            <!-- 顶部标题区 -->
            <div class="flex items-center justify-between mb-3">
                <h1 class="text-sm font-semibold text-[var(--color-text-primary)]">个人中心</h1>
            </div>

            <!-- 个人形象卡片 -->
            <div class="enhanced-glass rounded-xl overflow-hidden profile-card">
                <!-- 个人形象图片 -->
                <img src="../assets/images/2.png" 
                     alt="虚拟形象" class="profile-image">
                
                <!-- 基础信息面板 -->
                <div class="info-panel">
                    <div class="flex items-center justify-between mb-3">
                        <h3 class="text-xs font-medium text-[var(--color-text-primary)]">基础信息</h3>
                        <button id="editButton" class="text-xs text-[var(--color-text-primary)]">
                            编辑
                        </button>
                    </div>
                    
                    <!-- 身高 -->
                    <div class="info-item">
                        <span class="text-xs text-[var(--color-text-secondary)] w-12">身高</span>
                        <div class="selector-wrapper">
                            <button class="selector-button" data-type="height" disabled>
                                <span class="text-xs text-[var(--color-text-secondary)]">标准身高</span>
                                <i class="fas fa-chevron-down text-[10px] ml-2"></i>
                            </button>
                            <div class="dropdown-menu" id="heightDropdown"></div>
                        </div>
                    </div>
                    
                    <!-- 体型 -->
                    <div class="info-item">
                        <span class="text-xs text-[var(--color-text-secondary)] w-12">体型</span>
                        <div class="selector-wrapper">
                            <button class="selector-button" data-type="bodyType" disabled>
                                <span class="text-xs text-[var(--color-text-secondary)]">匀称型</span>
                                <i class="fas fa-chevron-down text-[10px] ml-2"></i>
                            </button>
                            <div class="dropdown-menu" id="bodyTypeDropdown"></div>
                        </div>
                    </div>
                    
                    <!-- 体重 -->
                    <div class="info-item">
                        <span class="text-xs text-[var(--color-text-secondary)] w-12">体重</span>
                        <div class="selector-wrapper">
                            <button class="selector-button" data-type="weight" disabled>
                                <span class="text-xs text-[var(--color-text-secondary)]">标准</span>
                                <i class="fas fa-chevron-down text-[10px] ml-2"></i>
                            </button>
                            <div class="dropdown-menu" id="weightDropdown"></div>
                    </div>
                    </div>
                    
                    <!-- 年龄 -->
                    <div class="info-item">
                        <span class="text-xs text-[var(--color-text-secondary)] w-12">年龄</span>
                        <div class="selector-wrapper">
                            <button class="selector-button" data-type="age" disabled>
                                <span class="text-xs text-[var(--color-text-secondary)]">青年</span>
                                <i class="fas fa-chevron-down text-[10px] ml-2"></i>
                            </button>
                            <div class="dropdown-menu" id="ageDropdown"></div>
                        </div>
                    </div>
                    
                    <!-- 保存按钮 -->
                    <button id="saveButton" class="w-2/3 mx-auto mt-3 glass-button text-xs py-2 rounded-full hidden">
                        保存
                    </button>
                </div>
                
                <!-- 功能入口面板 -->
                <div class="function-panel">
                    <!-- 风格诊断 -->
                    <div class="function-entry" onclick="location.href='../profile/style_diagnosis.html'">
                        <div class="function-icon">
                            <i class="fas fa-palette text-[var(--color-text-primary)]"></i>
                        </div>
                        <span class="text-[10px] text-white font-medium">风格诊断</span>
                    </div>
                    
                    <!-- 尺寸身材 -->
                    <div class="function-entry" onclick="location.href='../profile/body_data.html'">
                        <div class="function-icon">
                            <i class="fas fa-ruler-combined text-[var(--color-text-primary)]"></i>
                        </div>
                        <span class="text-[10px] text-white font-medium">尺寸身材</span>
                    </div>
                    
                    <!-- 收藏夹 -->
                    <div class="function-entry" onclick="location.href='../profile/favorites.html'">
                        <div class="function-icon">
                            <i class="fas fa-star text-[var(--color-text-primary)]"></i>
                        </div>
                        <span class="text-[10px] text-white font-medium">收藏夹</span>
                    </div>
                    
                    <!-- 赞过 -->
                    <div class="function-entry" onclick="location.href='../profile/liked.html'">
                        <div class="function-icon">
                            <i class="fas fa-heart text-[var(--color-text-primary)]"></i>
                        </div>
                        <span class="text-[10px] text-white font-medium">赞过</span>
                    </div>
                    
                    <!-- 灵感值中心 -->
                    <div class="function-entry" onclick="location.href='../profile/inspiration_center.html'">
                        <div class="function-icon">
                            <i class="fas fa-gem text-[var(--color-text-primary)]"></i>
                        </div>
                        <span class="text-[10px] text-white font-medium">灵感值</span>
                    </div>

                    <!-- 完善资料 -->
                    <div class="function-entry" onclick="location.href='../pages/user-info.html'">
                        <div class="function-icon">
                            <i class="fas fa-id-card text-[var(--color-text-primary)]"></i>
                        </div>
                        <span class="text-[10px] text-white font-medium">完善资料</span>
                    </div>

                    <!-- 设置 -->
                    <div class="function-entry" onclick="location.href='../profile/settings.html'">
                        <div class="function-icon">
                            <i class="fas fa-cog text-[var(--color-text-primary)]"></i>
                        </div>
                        <span class="text-[10px] text-white font-medium">设置</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部导航栏 -->
    <div class="fixed bottom-0 left-0 right-0 bg-white/60 backdrop-blur-md border-t border-white/60">
        <div class="grid grid-cols-4 h-20">
            <a href="home.html" class="flex flex-col items-center justify-center text-[var(--color-text-secondary)]">
                <i class="fas fa-home text-xl"></i>
                <span class="text-xs mt-1">首页</span>
            </a>
            <a href="wardrobe.html" class="flex flex-col items-center justify-center text-[var(--color-text-secondary)]">
                <i class="fas fa-tshirt text-xl"></i>
                <span class="text-xs mt-1">衣橱</span>
            </a>
            <a href="outfit.html" class="flex flex-col items-center justify-center text-[var(--color-text-secondary)]">
                <i class="fas fa-wand-magic-sparkles text-xl"></i>
                <span class="text-xs mt-1">搭配</span>
            </a>
            <a href="profile.html" class="flex flex-col items-center justify-center text-[var(--color-text-primary)]">
                <div class="w-10 h-10 rounded-full bg-white/70 backdrop-blur-md flex items-center justify-center">
                    <i class="fas fa-user text-lg"></i>
                </div>
                <span class="text-xs mt-1 font-medium">我的</span>
            </a>
        </div>
    </div>

    <script>
        // 选择器数据
        const selectorData = {
            'height': {
                options: [
                    '娇小型',
                    '偏矮',
                    '中等偏下',
                    '标准身高',
                    '中等偏上',
                    '偏高',
                    '高挑型'
                ]
            },
            'bodyType': {
                options: [
                    '纤细型',
                    '匀称型',
                    '健康型',
                    '丰腴型',
                    '运动型'
                ]
            },
            'weight': {
                options: [
                    '偏轻',
                    '标准',
                    '适中',
                    '偏重'
                ]
            },
            'age': {
                options: [
                    '青少年',
                    '青年',
                    '中青年',
                    '中年'
                ]
            }
        };

        // 初始化下拉菜单
        function initializeDropdowns() {
            const selectorButtons = document.querySelectorAll('.selector-button');
            let activeWrapper = null;
            
            selectorButtons.forEach(button => {
                const type = button.getAttribute('data-type');
                const wrapper = button.closest('.selector-wrapper');
                const dropdown = button.nextElementSibling;
                
                // 创建下拉选项
                selectorData[type].options.forEach(option => {
                    const item = document.createElement('div');
                    item.className = 'dropdown-item';
                    item.textContent = option;
                    if (option === button.querySelector('span').textContent) {
                        item.classList.add('selected');
                    }
                    
                    item.addEventListener('click', (e) => {
                        e.stopPropagation();
                        // 更新按钮文本
                        button.querySelector('span').textContent = option;
                        
                        // 更新选中状态
                        dropdown.querySelectorAll('.dropdown-item').forEach(item => {
                            item.classList.remove('selected');
                        });
                        item.classList.add('selected');
                        
                        // 关闭下拉菜单
                        dropdown.classList.remove('show');
                        wrapper.classList.remove('active');
                        activeWrapper = null;
                    });
                    
                    dropdown.appendChild(item);
                });
                
                // 点击按钮显示/隐藏下拉菜单
                button.addEventListener('click', (e) => {
                    e.stopPropagation();
                    if (button.disabled) return;
                    
                    // 如果当前有活动的下拉框，先关闭它
                    if (activeWrapper && activeWrapper !== wrapper) {
                        activeWrapper.querySelector('.dropdown-menu').classList.remove('show');
                        activeWrapper.classList.remove('active');
                    }
                    
                    // 切换当前下拉框的状态
                    const isShowing = dropdown.classList.toggle('show');
                    wrapper.classList.toggle('active');
                    activeWrapper = isShowing ? wrapper : null;
                });
            });
            
            // 点击其他区域关闭下拉菜单
            document.addEventListener('click', () => {
                if (activeWrapper) {
                    activeWrapper.querySelector('.dropdown-menu').classList.remove('show');
                    activeWrapper.classList.remove('active');
                    activeWrapper = null;
                }
            });
        }

        // 编辑模式切换
        const editButton = document.getElementById('editButton');
        const saveButton = document.getElementById('saveButton');
        let isEditMode = false;
        
        editButton.addEventListener('click', function() {
            isEditMode = !isEditMode;
            
            if (isEditMode) {
                // 进入编辑模式
                editButton.textContent = '取消';
                saveButton.classList.remove('hidden');
                
                // 启用所有选择器按钮
                document.querySelectorAll('.selector-button').forEach(button => {
                    button.disabled = false;
                });
            } else {
                // 退出编辑模式
                editButton.textContent = '编辑';
                saveButton.classList.add('hidden');
                
                // 禁用所有选择器按钮
                document.querySelectorAll('.selector-button').forEach(button => {
                    button.disabled = true;
                });
                
                // 关闭所有下拉菜单
                document.querySelectorAll('.dropdown-menu').forEach(menu => {
                    menu.classList.remove('show');
                });
                document.querySelectorAll('.selector-wrapper').forEach(wrapper => {
                    wrapper.classList.remove('active');
                });
            }
        });

        // 保存按钮点击事件
        saveButton.addEventListener('click', function() {
            // 这里可以添加保存数据的逻辑
            
            // 退出编辑模式
            isEditMode = false;
            editButton.textContent = '编辑';
            saveButton.classList.add('hidden');
            
            // 禁用所有选择器按钮
            document.querySelectorAll('.selector-button').forEach(button => {
                button.disabled = true;
            });
            
            // 关闭所有下拉菜单
            document.querySelectorAll('.dropdown-menu').forEach(menu => {
                menu.classList.remove('show');
            });
            document.querySelectorAll('.selector-wrapper').forEach(wrapper => {
                wrapper.classList.remove('active');
            });
        });

        // 初始化下拉菜单
        initializeDropdowns();
    </script>
</body>
</html> 