<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <title>搭配 - StylishLink</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../styles/main.css" rel="stylesheet">
    <style>
        ::-webkit-scrollbar {
            display: none;
        }
        
        * {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        
        .content-container {
            padding-left: 5%;
            padding-right: 5%;
        }

        .outfit-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            grid-template-rows: repeat(3, 1fr);
            gap: 15px;
            aspect-ratio: 1.1;
        }

        .outfit-main {
            grid-row: span 3;
            grid-column: span 2;
        }
        
        .gradient-background {
            background: linear-gradient(135deg, #c2e9fb 0%, #d8c6fd 100%);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: -1;
        }
        
        .enhanced-glass {
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.05);
        }
        
        .glass-button {
            background: rgba(255, 255, 255, 0.4);
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.5);
            transition: all 0.3s ease;
        }
        
        .glass-button:hover {
            background: rgba(255, 255, 255, 0.6);
        }
        
        .glow-icon {
            box-shadow: 0 0 15px rgba(255, 255, 255, 0.8);
        }

        /* 日历样式 */
        .calendar-grid {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 2px;
        }
        
        .calendar-day {
            aspect-ratio: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            border-radius: 8px;
            font-size: 10px;
            position: relative;
        }
        
        .calendar-day.active {
            background: rgba(255, 255, 255, 0.5);
            font-weight: 500;
        }
        
        .calendar-day.has-outfit::after {
            content: '';
            position: absolute;
            bottom: 2px;
            width: 4px;
            height: 4px;
            border-radius: 50%;
            background-color: rgba(255, 154, 158, 0.8);
        }
        
        /* 五行标识色彩 */
        .wuxing-jin {
            background: linear-gradient(135deg, #ffffff, #f0f0f0);
            color: #1a1a1a;
        }
        
        .wuxing-mu {
            background: linear-gradient(135deg, #a8e6cf, #73c1a8);
            color: #1a1a1a;
        }
        
        .wuxing-shui {
            background: linear-gradient(135deg, #b8c6db, #648dae);
            color: #ffffff;
        }
        
        .wuxing-huo {
            background: linear-gradient(135deg, #ff9a9e, #ff5458);
            color: #ffffff;
        }
        
        .wuxing-tu {
            background: linear-gradient(135deg, #ffeaa7, #ffc25c);
            color: #1a1a1a;
        }
        
        /* 切换页签样式 */
        .tab-active {
            color: var(--color-text-primary);
            font-weight: 500;
            border-bottom: 2px solid var(--color-text-primary);
        }
        
        .tab-inactive {
            color: var(--color-text-secondary);
        }
        
        /* 模态框样式 */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 100;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease, visibility 0.3s ease;
        }
        
        .modal.active {
            opacity: 1;
            visibility: visible;
        }
        
        .modal-content {
            width: 90%;
            max-width: 350px;
            max-height: 90vh;
            overflow-y: auto;
            transform: translateY(20px);
            transition: transform 0.3s ease;
        }
        
        .modal.active .modal-content {
            transform: translateY(0);
        }
        
        /* 日期选择器按钮样式 */
        .date-selector {
            cursor: pointer;
            transition: background-color 0.2s ease;
        }
        
        .date-selector:active {
            background-color: rgba(255, 255, 255, 0.3);
        }

        /* 点赞、收藏、分享按钮样式 */
        .action-buttons-group {
            display: flex;
            gap: 8px;
        }

        .action-buttons-group button {
            display: flex;
            align-items: center;
            padding: 4px 8px;
            border-radius: 16px;
            font-size: 12px;
            background: rgba(255, 255, 255, 0.4);
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.5);
            transition: all 0.3s ease;
            color: var(--color-text-primary);
            min-width: 52px;
            justify-content: center;
        }

        .action-buttons-group button:hover {
            background: rgba(255, 255, 255, 0.6);
            transform: translateY(-1px);
        }

        .action-buttons-group button:active {
            transform: scale(0.98);
        }

        .action-buttons-group button i {
            margin-right: 4px;
            transition: transform 0.2s ease;
        }

        .action-buttons-group button.active i.fa-heart {
            color: #ff5458;
        }

        .action-buttons-group button.active i.fa-star {
            color: #ffd700;
        }

        .action-buttons-group button i.fa-share-alt {
            color: #38BDF8;
        }
    </style>
</head>
<body>
    <!-- 渐变背景 -->
    <div class="gradient-background"></div>

    <!-- 主内容区 -->
    <div class="pt-2 pb-20 h-full overflow-y-scroll">
        <div class="content-container py-2">
            <!-- 顶部标题区 -->
            <div class="flex items-center justify-between mb-3">
                <h1 class="text-sm font-semibold text-[var(--color-text-primary)]">搭配</h1>
                <button class="glass-button px-2 py-1 rounded-md flex items-center text-[var(--color-text-primary)] text-[10px]" onclick="location.href='../outfit/custom_outfit.html'">
                    <i class="fas fa-wand-magic-sparkles mr-1 text-xs"></i>定制搭配
                </button>
            </div>

            <!-- 场景分类tab -->
            <div class="flex space-x-2 mb-4 overflow-x-auto no-scrollbar">
                <button class="h-7 px-4 rounded-full bg-white/50 text-[var(--color-text-primary)] text-xs flex items-center whitespace-nowrap">
                    <i class="fas fa-th-large mr-1.5 text-sm"></i>全部
                </button>
                <button class="h-7 px-4 rounded-full bg-white/30 backdrop-blur-sm text-[var(--color-text-secondary)] text-xs flex items-center whitespace-nowrap">
                    <i class="fas fa-sun mr-1.5 text-sm"></i>日常休闲
                </button>
                <button class="h-7 px-4 rounded-full bg-white/30 backdrop-blur-sm text-[var(--color-text-secondary)] text-xs flex items-center whitespace-nowrap">
                    <i class="fas fa-briefcase mr-1.5 text-sm"></i>职场通勤
                </button>
                <button class="h-7 px-4 rounded-full bg-white/30 backdrop-blur-sm text-[var(--color-text-secondary)] text-xs flex items-center whitespace-nowrap">
                    <i class="fas fa-champagne-glasses mr-1.5 text-sm"></i>约会
                </button>
                <button class="h-7 px-4 rounded-full bg-white/30 backdrop-blur-sm text-[var(--color-text-secondary)] text-xs flex items-center whitespace-nowrap">
                    <i class="fas fa-plane mr-1.5 text-sm"></i>旅行
                </button>
                <button class="h-7 px-4 rounded-full bg-white/30 backdrop-blur-sm text-[var(--color-text-secondary)] text-xs flex items-center whitespace-nowrap">
                    <i class="fas fa-graduation-cap mr-1.5 text-sm"></i>学院风
                </button>
                <button class="h-7 px-4 rounded-full bg-white/30 backdrop-blur-sm text-[var(--color-text-secondary)] text-xs flex items-center whitespace-nowrap">
                    <i class="fas fa-umbrella-beach mr-1.5 text-sm"></i>度假
                </button>
            </div>

            <!-- 推荐搭配1 -->
            <div class="mb-6">
                <div class="enhanced-glass rounded-xl p-3 transform transition-all duration-300 hover:scale-[1.02]">
                    <div class="outfit-grid gap-3 cursor-pointer" onclick="location.href='../home/<USER>'">
                        <div class="outfit-main rounded-lg overflow-hidden bg-white/20 relative group">
                            <img src="https://images.unsplash.com/photo-1515886657613-9f3515b0c78f" 
                                 class="w-full h-full object-cover transform transition-transform duration-700 group-hover:scale-110" alt="整体搭配">
                            <div class="absolute top-2 right-2 flex items-center space-x-0.5 bg-black/20 backdrop-blur-sm px-2 py-1 rounded-full">
                                <i class="fas fa-star text-[10px] text-amber-400"></i>
                                <span class="text-[10px] text-white font-medium">4.8</span>
                            </div>
                            <!-- 场景标签 -->
                            <div class="absolute bottom-2 left-2 bg-black/20 backdrop-blur-sm px-2 py-1 rounded-full">
                                <span class="text-[10px] text-white"><i class="fas fa-briefcase mr-1"></i>职场通勤</span>
                            </div>
                        </div>
                        <div class="rounded-lg overflow-hidden bg-white/20 group">
                            <img src="https://images.unsplash.com/photo-1434389677669-e08b4cac3105" 
                                 class="w-full h-full object-cover transform transition-transform duration-700 group-hover:scale-110" alt="上衣">
                        </div>
                        <div class="rounded-lg overflow-hidden bg-white/20 group">
                            <img src="https://images.unsplash.com/photo-1475178626620-a4d074967452" 
                                 class="w-full h-full object-cover transform transition-transform duration-700 group-hover:scale-110" alt="裤子">
                        </div>
                        <div class="rounded-lg overflow-hidden bg-white/20 group">
                            <img src="https://images.unsplash.com/photo-1560769629-975ec94e6a86" 
                                 class="w-full h-full object-cover transform transition-transform duration-700 group-hover:scale-110" alt="鞋子">
                        </div>
                    </div>

                    <div class="mt-3 space-y-1.5">
                        <div class="flex justify-between items-center">
                            <h3 class="text-[13px] font-medium text-[var(--color-text-primary)]">简约商务风搭配</h3>
                            <div class="flex space-x-1.5">
                                <button class="text-[11px] flex items-center glass-button px-2 py-0.5 rounded-full min-w-[52px] justify-center" onclick="toggleLike(this, event)">
                                    <i class="far fa-heart text-pink-400 mr-1"></i>
                                    <span>12</span>
                                </button>
                                <button class="text-[11px] flex items-center glass-button px-2 py-0.5 rounded-full min-w-[52px] justify-center" onclick="toggleFavorite(this, event)">
                                    <i class="far fa-star text-amber-400 mr-1"></i>
                                    <span>8</span>
                        </button>
                                <button class="text-[11px] flex items-center glass-button px-2 py-0.5 rounded-full min-w-[52px] justify-center" onclick="shareOutfit(event)">
                                    <i class="fas fa-share-alt text-sky-400 mr-1"></i>
                                    <span>分享</span>
                        </button>
                            </div>
                        </div>
                        <p class="text-[11px] text-[var(--color-text-secondary)]">
                            <i class="fas fa-circle-info mr-1"></i>金属性白色提升财运，搭配木质配饰平衡五行
                        </p>
                    </div>
                </div>
            </div>

            <!-- 推荐搭配2 -->
            <div class="mb-4">
                <div class="enhanced-glass rounded-xl p-2">
                    <div class="outfit-grid gap-2 cursor-pointer" onclick="location.href='../home/<USER>'">
                        <div class="outfit-main rounded-lg overflow-hidden bg-white/20 relative cursor-pointer">
                            <img src="https://images.unsplash.com/photo-1485230895905-ec40ba36b9bc" 
                                 class="w-full h-full object-cover" alt="整体搭配">
                            <div class="absolute top-2 right-2 flex items-center text-[#ffc25c]">
                                <i class="fas fa-star text-xs"></i>
                                <i class="fas fa-star text-xs"></i>
                                <i class="fas fa-star text-xs"></i>
                                <i class="fas fa-star text-xs"></i>
                                <i class="far fa-star text-xs"></i>
                            </div>
                        </div>
                        <div class="rounded-lg overflow-hidden bg-white/20">
                            <img src="https://images.unsplash.com/photo-1551107696-a4b0c5a0d9a2" 
                                 class="w-full h-full object-cover" alt="连衣裙">
                        </div>
                        <div class="rounded-lg overflow-hidden bg-white/20">
                            <img src="https://images.unsplash.com/photo-1560769629-975ec94e6a86" 
                                 class="w-full h-full object-cover" alt="鞋子">
                        </div>
                        <div class="rounded-lg overflow-hidden bg-white/20">
                            <img src="https://images.unsplash.com/photo-1541099649105-f69ad21f3246" 
                                 class="w-full h-full object-cover" alt="墨镜">
                        </div>
                    </div>

                    <div class="mt-2 space-y-1.5">
                        <div class="flex justify-between items-center">
                            <h3 class="text-[13px] font-medium text-[var(--color-text-primary)]">优雅连衣裙搭配</h3>
                            <div class="flex space-x-1.5">
                                <button class="text-[11px] flex items-center glass-button px-2 py-0.5 rounded-full min-w-[52px] justify-center" onclick="toggleLike(this, event)">
                                    <i class="far fa-heart text-pink-400 mr-1"></i>
                                    <span>24</span>
                                </button>
                                <button class="text-[11px] flex items-center glass-button px-2 py-0.5 rounded-full min-w-[52px] justify-center" onclick="toggleFavorite(this, event)">
                                    <i class="far fa-star text-amber-400 mr-1"></i>
                                    <span>16</span>
                        </button>
                                <button class="text-[11px] flex items-center glass-button px-2 py-0.5 rounded-full min-w-[52px] justify-center" onclick="shareOutfit(event)">
                                    <i class="fas fa-share-alt text-sky-400 mr-1"></i>
                                    <span>分享</span>
                        </button>
                            </div>
                        </div>
                        <p class="text-[11px] text-[var(--color-text-secondary)]">
                            <i class="fas fa-circle-info mr-1"></i>水属性蓝色增强气场，搭配金属饰品提升魅力
                        </p>
                    </div>
                </div>
            </div>

            <!-- 推荐搭配3 -->
            <div class="mb-4">
                <div class="enhanced-glass rounded-xl p-2">
                    <div class="outfit-grid gap-2 cursor-pointer" onclick="location.href='../home/<USER>'">
                        <div class="outfit-main rounded-lg overflow-hidden bg-white/20 relative cursor-pointer">
                            <img src="https://images.unsplash.com/photo-1552374196-1ab2a1c593e8" 
                                 class="w-full h-full object-cover" alt="整体搭配">
                            <div class="absolute top-2 right-2 flex items-center text-[#ffc25c]">
                                <i class="fas fa-star text-xs"></i>
                                <i class="fas fa-star text-xs"></i>
                                <i class="fas fa-star text-xs"></i>
                                <i class="fas fa-star-half-alt text-xs"></i>
                                <i class="far fa-star text-xs"></i>
                            </div>
                        </div>
                        <div class="rounded-lg overflow-hidden bg-white/20">
                            <img src="https://images.unsplash.com/photo-1523381294911-8d3cead13475" 
                                 class="w-full h-full object-cover" alt="卫衣">
                        </div>
                        <div class="rounded-lg overflow-hidden bg-white/20">
                            <img src="https://images.unsplash.com/photo-1517438476312-10d79c077509" 
                                 class="w-full h-full object-cover" alt="运动鞋">
                        </div>
                        <div class="rounded-lg overflow-hidden bg-white/20">
                            <img src="https://images.unsplash.com/photo-1516975080664-ed2fc6a32937" 
                                 class="w-full h-full object-cover" alt="帽子">
                        </div>
                    </div>

                    <div class="mt-2 space-y-1.5">
                        <div class="flex justify-between items-center">
                            <h3 class="text-[13px] font-medium text-[var(--color-text-primary)]">休闲运动风搭配</h3>
                            <div class="flex space-x-1.5">
                                <button class="text-[11px] flex items-center glass-button px-2 py-0.5 rounded-full min-w-[52px] justify-center" onclick="toggleLike(this, event)">
                                    <i class="far fa-heart text-pink-400 mr-1"></i>
                                    <span>18</span>
                                </button>
                                <button class="text-[11px] flex items-center glass-button px-2 py-0.5 rounded-full min-w-[52px] justify-center" onclick="toggleFavorite(this, event)">
                                    <i class="far fa-star text-amber-400 mr-1"></i>
                                    <span>10</span>
                        </button>
                                <button class="text-[11px] flex items-center glass-button px-2 py-0.5 rounded-full min-w-[52px] justify-center" onclick="shareOutfit(event)">
                                    <i class="fas fa-share-alt text-sky-400 mr-1"></i>
                                    <span>分享</span>
                        </button>
                            </div>
                        </div>
                        <p class="text-[11px] text-[var(--color-text-secondary)]">
                            <i class="fas fa-circle-info mr-1"></i>木属性绿色增添活力，搭配运动元素提升朝气
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部导航栏 -->
    <div class="fixed bottom-0 left-0 right-0 bg-white/60 backdrop-blur-md border-t border-white/60">
        <div class="grid grid-cols-4 h-20">
            <a href="home.html" class="flex flex-col items-center justify-center text-[var(--color-text-secondary)]">
                <i class="fas fa-home text-xl"></i>
                <span class="text-xs mt-1">首页</span>
            </a>
            <a href="wardrobe.html" class="flex flex-col items-center justify-center text-[var(--color-text-secondary)]">
                <i class="fas fa-tshirt text-xl"></i>
                <span class="text-xs mt-1">衣橱</span>
            </a>
            <a href="outfit.html" class="flex flex-col items-center justify-center text-[var(--color-text-primary)]">
                <div class="w-10 h-10 rounded-full bg-white/70 backdrop-blur-md flex items-center justify-center">
                    <i class="fas fa-wand-magic-sparkles text-lg"></i>
                </div>
                <span class="text-xs mt-1 font-medium">搭配</span>
            </a>
            <a href="profile.html" class="flex flex-col items-center justify-center text-[var(--color-text-secondary)]">
                <i class="fas fa-user text-xl"></i>
                <span class="text-xs mt-1">我的</span>
            </a>
        </div>
    </div>

    <!-- 悬浮创建按钮 -->
    <a href="../outfit/custom_outfit.html" class="fixed right-5 bottom-24 w-14 h-14 rounded-full glass-button flex items-center justify-center shadow-lg glow-icon">
        <i class="fas fa-wand-magic-sparkles text-[var(--color-text-primary)] text-xl"></i>
    </a>
    
    <script>
        // 点赞功能
        function toggleLike(button, event) {
            event.stopPropagation(); // 阻止事件冒泡
            const icon = button.querySelector('i');
            const count = button.querySelector('span');
            const currentCount = parseInt(count.textContent);
            
            button.classList.toggle('active');
            
            if (button.classList.contains('active')) {
                icon.classList.remove('far');
                icon.classList.add('fas');
                icon.style.transform = 'scale(1.2)';
                setTimeout(() => icon.style.transform = 'scale(1)', 200);
                count.textContent = currentCount + 1;
            } else {
                icon.classList.remove('fas');
                icon.classList.add('far');
                count.textContent = currentCount - 1;
            }
        }

        // 收藏功能
        function toggleFavorite(button, event) {
            event.stopPropagation(); // 阻止事件冒泡
            const icon = button.querySelector('i');
            const count = button.querySelector('span');
            const currentCount = parseInt(count.textContent);
            
            button.classList.toggle('active');
            
            if (button.classList.contains('active')) {
                icon.classList.remove('far');
                icon.classList.add('fas');
                icon.style.transform = 'scale(1.2)';
                setTimeout(() => icon.style.transform = 'scale(1)', 200);
                count.textContent = currentCount + 1;
            } else {
                icon.classList.remove('fas');
                icon.classList.add('far');
                count.textContent = currentCount - 1;
            }
        }

        // 分享功能
        function shareOutfit(event) {
            event.stopPropagation(); // 阻止事件冒泡
            const button = event.currentTarget;
            const icon = button.querySelector('i');
            
            // 添加动画效果
            icon.style.transform = 'scale(1.2)';
            setTimeout(() => icon.style.transform = 'scale(1)', 200);
            
            // TODO: 实现分享功能
            console.log('分享功能待实现');
        }
        
        // 场景标签点击事件
        function initializeSceneTabs() {
            try {
        const sceneTabs = document.querySelectorAll('.flex.space-x-2.mb-4 button');
                if (!sceneTabs?.length) return;

        sceneTabs.forEach(tab => {
                    if (!tab) return;
                    
            tab.addEventListener('click', function() {
                // 移除其他标签的active状态
                sceneTabs.forEach(t => {
                            if (!t) return;
                    t.classList.remove('bg-white/50');
                    t.classList.add('bg-white/30');
                    t.classList.remove('text-[var(--color-text-primary)]');
                    t.classList.add('text-[var(--color-text-secondary)]');
                });
                // 添加当前标签的active状态
                this.classList.remove('bg-white/30');
                this.classList.add('bg-white/50');
                this.classList.remove('text-[var(--color-text-secondary)]');
                this.classList.add('text-[var(--color-text-primary)]');
                // 更新搭配内容（实际应用中应该是异步加载数据）
                console.log('切换到场景: ' + this.textContent.trim());
            });
        });
            } catch (error) {
                console.error('初始化场景标签时发生错误:', error);
            }
        }

        // 确保 DOM 完全加载后再初始化场景标签
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initializeSceneTabs);
        } else {
            initializeSceneTabs();
        }
    </script>
</body>
</html> 