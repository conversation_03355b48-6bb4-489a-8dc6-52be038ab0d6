<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <title>主页 - StylishLink</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../styles/main.css" rel="stylesheet">
    <style>
        /* 隐藏滚动条 */
        ::-webkit-scrollbar {
            display: none;
        }
        
        * {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        
        body {
            overflow-y: scroll;
            -webkit-overflow-scrolling: touch;
        }

        /* 内容区域统一间距 */
        .content-container {
            padding-left: 5%;
            padding-right: 5%;
        }
        
        .gradient-background {
            background: linear-gradient(135deg, #c2e9fb 0%, #d8c6fd 100%);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: -1;
        }
        
        .enhanced-glass {
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.05);
        }
        
        .glass-button {
            background: rgba(255, 255, 255, 0.4);
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.5);
            transition: all 0.3s ease;
        }
        
        .glass-button:hover {
            background: rgba(255, 255, 255, 0.6);
        }
        
        .glow-icon {
            box-shadow: 0 0 15px rgba(255, 255, 255, 0.8);
        }
        
        /* 五行标识色彩 */
        .wuxing-jin {
            background: linear-gradient(135deg, #ffffff, #f0f0f0);
            color: #1a1a1a;
        }
        
        .wuxing-mu {
            background: linear-gradient(135deg, #a8e6cf, #73c1a8);
            color: #1a1a1a;
        }
        
        .wuxing-shui {
            background: linear-gradient(135deg, #b8c6db, #648dae);
            color: #ffffff;
        }
        
        .wuxing-huo {
            background: linear-gradient(135deg, #ff9a9e, #ff5458);
            color: #ffffff;
        }
        
        .wuxing-tu {
            background: linear-gradient(135deg, #ffeaa7, #ffc25c);
            color: #1a1a1a;
        }
        
        /* 推荐搭配轮播 */
        .outfit-slider {
            position: relative;
            overflow: hidden;
        }
        
        .outfit-slides {
            display: flex;
            transition: transform 0.3s ease;
        }
        
        .outfit-slide {
            flex: 0 0 100%;
        }
        
        /* 评分星星 */
        .star-rating {
            color: rgba(255, 215, 0, 0.9);
        }
        
        /* 拍照浮窗 */
        .floating-camera {
            position: fixed;
            right: 4%;
            bottom: 100px;
            z-index: 40;
            width: 52px;
            height: 52px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background: linear-gradient(135deg, #ff9a9e, #ff5458);
            box-shadow: 0 4px 15px rgba(255, 84, 88, 0.4);
            border: 2px solid rgba(255, 255, 255, 0.8);
        }
        
        .camera-icon-wrapper {
            width: 44px;
            height: 44px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* 隐藏滚动条但保留滚动功能 */
        .no-scrollbar {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        .no-scrollbar::-webkit-scrollbar {
            display: none;
        }

        /* 自定义视频播放器中央播放按钮大小 */
        .video-custom-controls::-webkit-media-controls-overlay-play-button {
            transform: scale(0.7);
        }
        
        /* 兼容不同浏览器 */
        .video-custom-controls::-webkit-media-controls-play-button {
            transform: scale(0.7);
        }
    </style>
</head>
<body>
    <!-- 渐变背景 -->
    <div class="gradient-background"></div>

    <!-- 主内容区 -->
    <div class="pt-2 pb-20 h-full overflow-y-scroll">
        <!-- 天气和穿搭建议 -->
        <div class="content-container py-4">
            <!-- 天气信息与运势建议（优化版） -->
            <div class="flex flex-col">
                <!-- 顶部日期与天气区域 -->
                <div class="flex items-center justify-between mb-3">
                    <div>
                        <h2 class="text-base font-bold text-[var(--color-text-primary)]">晴, 22°</h2>
                        <p class="text-[10px] text-[var(--color-text-secondary)] mt-1">二月十九 己巳卯 丙成</p>
                    </div>
                    <div class="text-xs text-[var(--color-text-secondary)]">
                        <i class="fas fa-location-dot mr-1"></i>北京市
                    </div>
                </div>
                
                <!-- 修改后的内容区域 - 更进一步调整左右比例 -->
                <div class="flex mt-1">
                    <!-- 左侧运势区域 - 进一步减小比例，调整位置以便于右侧对齐 -->
                    <div class="w-1/3 pr-2 cursor-pointer" onclick="location.href='../home/<USER>'">
                        <!-- 运势得分容器 - 调整大小使其上下居中 -->
                        <div class="w-full flex flex-col items-center">
                            <!-- 能量圈容器 -->
                            <div class="w-full aspect-square relative flex flex-col items-center justify-center">
                                <!-- 圆形进度条 SVG -->
                                <svg viewBox="0 0 100 100" class="w-full">
                                    <!-- 渐变和发光效果定义 -->
                                    <defs>
                                        <!-- 更平滑的颜色过渡 -->
                                        <linearGradient id="progressGradient" gradientUnits="userSpaceOnUse" x1="50" y1="8" x2="15.8" y2="29.5">
                                            <stop offset="0%" stop-color="#d8b4fe" /> <!-- 浅紫色 -->
                                            <stop offset="33%" stop-color="#c4a6fa" /> <!-- 中浅紫色 -->
                                            <stop offset="67%" stop-color="#b095f8" /> <!-- 中深紫色 -->
                                            <stop offset="100%" stop-color="#9f84f7" /> <!-- 深紫色 -->
                                        </linearGradient>
                                        <filter id="progressGlow" x="-20%" y="-20%" width="140%" height="140%">
                                            <feGaussianBlur stdDeviation="1.5" result="blur" />
                                            <feComposite in="SourceGraphic" in2="blur" operator="over" />
                                        </filter>
                                    </defs>
                                    
                                    <!-- 圆形背景 -->
                                    <circle 
                                        cx="50" 
                                        cy="50" 
                                        r="40" 
                                        fill="none" 
                                        stroke="rgba(255,255,255,0.15)" 
                                        stroke-width="4" 
                                        stroke-linecap="round" />
                                    
                                    <!-- 进度条圆弧 (78%) -->
                                    <circle 
                                        cx="50" 
                                        cy="50" 
                                        r="40" 
                                        fill="none" 
                                        stroke="url(#progressGradient)" 
                                        stroke-width="5"
                                        stroke-linecap="round"
                                        stroke-dasharray="251.33 251.33"
                                        stroke-dashoffset="55.29"
                                        filter="url(#progressGlow)"
                                        transform="rotate(-90, 50, 50)" />
                                </svg>
                                
                                <!-- 分数显示 -->
                                <div class="absolute inset-0 flex flex-col items-center justify-center">
                                    <span class="text-[10px] font-medium text-[var(--color-text-secondary)]">今日能量</span>
                                    <p class="text-2xl font-bold text-[var(--color-text-primary)]">78</p>
                                </div>
                            </div>
                            <!-- 百分比说明 - 移到能量圈下方 -->
                            <div class="mt-1 text-center">
                                <span class="text-[10px] text-[var(--color-text-secondary)] opacity-80">超过了82%的用户</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 右侧穿搭建议列表 - 进一步增加比例 -->
                    <div class="w-2/3 pl-2 border-l border-white/20">
                        <!-- 服饰建议 -->
                        <div class="mb-2.5 cursor-pointer" onclick="location.href='../home/<USER>'">
                            <div class="flex items-start">
                                <div class="w-6 h-6 rounded-full bg-purple-400/30 flex items-center justify-center mr-2 mt-0.5">
                                    <i class="fas fa-tshirt text-[11px] text-purple-400"></i>
                                </div>
                                <div>
                                    <p class="text-xs font-medium text-[var(--color-text-primary)]">服饰建议</p>
                                    <p class="text-[10px] leading-relaxed text-[var(--color-text-secondary)]">宜木属性绿色系轻薄面料，提升事业运</p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 配饰建议 -->
                        <div class="mb-2.5 cursor-pointer" onclick="location.href='../home/<USER>'">
                            <div class="flex items-start">
                                <div class="w-6 h-6 rounded-full bg-blue-400/30 flex items-center justify-center mr-2 mt-0.5">
                                    <i class="fas fa-gem text-[11px] text-blue-400"></i>
                                </div>
                                <div>
                                    <p class="text-xs font-medium text-[var(--color-text-primary)]">配饰建议</p>
                                    <p class="text-[10px] leading-relaxed text-[var(--color-text-secondary)]">佩戴水属性蓝色饰品，增强人际关系</p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 妆容建议 -->
                        <div class="cursor-pointer" onclick="location.href='../home/<USER>'">
                            <div class="flex items-start">
                                <div class="w-6 h-6 rounded-full bg-pink-400/30 flex items-center justify-center mr-2 mt-0.5">
                                    <i class="fas fa-palette text-[11px] text-pink-400"></i>
                                </div>
                                <div>
                                    <p class="text-xs font-medium text-[var(--color-text-primary)]">妆容建议</p>
                                    <p class="text-[10px] leading-relaxed text-[var(--color-text-secondary)]">适合清透底妆，嘴唇点缀暖橘色提升气色</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 今日推荐搭配（增加上边距） -->
            <div class="enhanced-glass rounded-xl overflow-hidden mb-4 p-2 mt-6">
                <div class="flex justify-between items-center mb-2">
                    <h2 class="text-sm font-semibold text-[var(--color-text-primary)]">今日推荐搭配</h2>
                    <!-- 右上角评分 -->
                    <div class="flex items-center">
                        <div class="star-rating flex">
                            <i class="fas fa-star text-[#ffc25c] text-xs"></i>
                            <i class="fas fa-star text-[#ffc25c] text-xs"></i>
                            <i class="fas fa-star text-[#ffc25c] text-xs"></i>
                            <i class="fas fa-star text-[#ffc25c] text-xs"></i>
                            <i class="fas fa-star-half-alt text-[#ffc25c] text-xs"></i>
                        </div>
                        <span class="text-xs text-[var(--color-text-primary)] ml-1 font-medium">4.5</span>
                    </div>
                </div>
                
                <!-- 扑克牌效果搭配区域 - 左转角度显示更多卡片 -->
                <div class="outfit-cards-container relative mx-0 mb-3" style="height: 400px; max-width: 100%; margin: 0 auto;">
                    <!-- 卡片1（顶部卡片） - 左转角度 -->
                    <div class="outfit-card absolute w-[90%] left-[2%] h-full z-30 rounded-xl overflow-hidden transform transition-all duration-300 outfit-shadow-1 cursor-pointer" id="card-0" style="transform: rotate(-2deg);" onclick="location.href='../home/<USER>'">
                        <div class="relative h-full">
                            <img src="../assets/images/4.png" 
                                 class="w-full h-full object-cover" alt="推荐搭配1">
                            
                            <!-- 左上角索引指示器 -->
                            <div class="absolute top-2 left-2 px-2 py-1 rounded-full bg-black/40 backdrop-blur-sm">
                                <span class="text-[10px] text-white">1/3</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 卡片2（第二张卡片） - 进一步左转 -->
                    <div class="outfit-card absolute w-[90%] left-[-3%] h-full z-20 rounded-xl overflow-hidden transform transition-all duration-300 rotate-[-5deg] translate-x-[-15px] translate-y-6 opacity-90 outfit-shadow-2 cursor-pointer" id="card-1" onclick="location.href='../home/<USER>'">
                        <img src="../assets/images/1.png" 
                             class="w-full h-full object-cover" alt="推荐搭配2">
                    </div>
                                
                    <!-- 卡片3（第三张卡片） - 更多左转和错位 -->
                    <div class="outfit-card absolute w-[90%] left-[-8%] h-full z-10 rounded-xl overflow-hidden transform transition-all duration-300 rotate-[-8deg] translate-x-[-30px] translate-y-12 opacity-80 outfit-shadow-3 cursor-pointer" id="card-2" onclick="location.href='../home/<USER>'">
                        <img src="../assets/images/3.png" 
                             class="w-full h-full object-cover" alt="推荐搭配3">
                    </div>
                </div>
                
                <!-- 搭配信息区域（简化设计） -->
                <div class="px-1 py-2 mb-0 mt-4">
                    <div class="flex justify-between items-center mb-2">
                        <div>
                            <p class="text-sm font-medium text-[var(--color-text-primary)]">清新白裙搭配</p>
                        </div>
                        <div class="flex space-x-2">
                            <button class="text-xs flex items-center glass-button px-2 py-0.5 rounded-full min-w-[52px] justify-center">
                                <i class="fas fa-heart text-pink-400 mr-1"></i>
                                <span class="text-[var(--color-text-primary)]">12</span>
                            </button>
                            <button class="text-xs flex items-center glass-button px-2 py-0.5 rounded-full min-w-[52px] justify-center">
                                <i class="far fa-star text-amber-400 mr-1"></i>
                                <span class="text-[var(--color-text-primary)]">8</span>
                            </button>
                            <button class="text-xs flex items-center glass-button px-2 py-0.5 rounded-full min-w-[52px] justify-center">
                                <i class="fas fa-share-alt text-blue-400 mr-1"></i>
                                <span class="text-[var(--color-text-primary)]">分享</span>
                            </button>
                        </div>
                    </div>
                    
                    <p class="text-[10px] text-[var(--color-text-secondary)]">
                        <i class="fas fa-circle-info mr-1"></i>
                        金属性白色提升今日财运，木质配饰平衡整体五行
                    </p>
                </div>
                
                <style>
                    /* 扑克牌效果样式 - 增强版 */
                    .outfit-cards-container {
                        perspective: 1500px;
                        transform-style: preserve-3d;
                    }
                    
                    .outfit-card {
                        backface-visibility: hidden;
                        transform-style: preserve-3d;
                        transition: all 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
                    }
                    
                    /* 增强阴影效果 */
                    .outfit-shadow-1 {
                        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
                    }
                    
                    .outfit-shadow-2 {
                        box-shadow: 0 6px 15px rgba(0, 0, 0, 0.12);
                    }
                    
                    .outfit-shadow-3 {
                        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
                    }
                    
                    /* 卡片状态样式 */
                    .outfit-card.active {
                        z-index: 30;
                        transform: translateZ(20px) !important;
                        opacity: 1 !important;
                        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2) !important;
                        left: 2% !important;
                    }
                    
                    .outfit-card.prev {
                        z-index: 20;
                        transform: rotate(2deg) translateX(6px) translateY(6px) translateZ(10px) !important;
                        opacity: 0.9 !important;
                        left: 5% !important;
                    }
                    
                    .outfit-card.next {
                        z-index: 10;
                        transform: rotate(4deg) translateX(12px) translateY(12px) translateZ(0px) !important;
                        opacity: 0.8 !important;
                        left: 8% !important;
                    }
                    
                    /* 添加微妙的悬停效果 */
                    .outfit-card:hover {
                        transform: scale(1.01) translateZ(30px);
                    }
                    
                    /* 扑克牌边缘装饰 */
                    .outfit-card::after {
                        content: '';
                        position: absolute;
                        top: 0;
                        left: 0;
                        right: 0;
                        bottom: 0;
                        border-radius: inherit;
                        pointer-events: none;
                        box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.15);
                    }
                </style>
                
                <script>
                    // 卡片切换效果脚本
                    document.addEventListener('DOMContentLoaded', function() {
                        const cards = document.querySelectorAll('.outfit-card');
                        const cardsContainer = document.querySelector('.outfit-cards-container');
                        const totalCards = cards.length;
                        let currentIndex = 0;
                        
                        // 初始化卡片状态
                        updateCardStates();
                        
                        // 触摸滑动事件
                        let touchstartX = 0;
                        let touchendX = 0;
                        
                        cardsContainer.addEventListener('touchstart', function(e) {
                            touchstartX = e.changedTouches[0].screenX;
                        });
                        
                        cardsContainer.addEventListener('touchend', function(e) {
                            touchendX = e.changedTouches[0].screenX;
                            handleSwipe();
                        });
                        
                        function handleSwipe() {
                            if (touchendX < touchstartX - 50) {
                                nextCard();
                            }
                            if (touchendX > touchstartX + 50) {
                                prevCard();
                            }
                        }
                        
                        function nextCard() {
                            currentIndex = (currentIndex + 1) % totalCards;
                            updateCardStates();
                            updateCardIndicator();
                            updateCardInfo();
                        }
                        
                        function prevCard() {
                            currentIndex = (currentIndex - 1 + totalCards) % totalCards;
                            updateCardStates();
                            updateCardIndicator();
                            updateCardInfo();
                        }
                        
                        function updateCardStates() {
                            cards.forEach((card, index) => {
                                card.classList.remove('active', 'prev', 'next');
                                
                                if (index === currentIndex) {
                                    card.classList.add('active');
                                } else if (index === (currentIndex + 1) % totalCards) {
                                    card.classList.add('prev');
                                } else {
                                    card.classList.add('next');
                                }
                            });
                        }
                        
                        function updateCardIndicator() {
                            document.querySelector('.absolute.top-2.left-2 span').textContent = `${currentIndex + 1}/${totalCards}`;
                        }
                        
                        function updateCardInfo() {
                            const titles = ["清新白裙搭配", "优雅蓝色系搭配", "活力橙色系搭配"];
                            const descriptions = [
                                "金属性白色提升今日财运，木质配饰平衡整体五行",
                                "水属性蓝色增强智慧，金质饰品助力人际关系",
                                "火属性橙色激发创造力，土系配饰稳定整体气场"
                            ];
                            
                            document.querySelector('p.text-sm.font-medium').textContent = titles[currentIndex];
                            document.querySelector('p.text-\\[10px\\]').innerHTML = 
                                `<i class="fas fa-circle-info mr-1"></i>${descriptions[currentIndex]}`;
                        }
                    });
                </script>
            </div>

            <!-- 案例分享（移除外部大卡片，保留内容） -->
            <!-- 场景分类tab栏 -->
            <div class="flex space-x-2 overflow-x-auto mb-2 pb-1 no-scrollbar px-1 mt-8">
                <button class="h-7 px-4 rounded-full bg-white/50 text-[var(--color-text-primary)] text-xs font-medium flex items-center whitespace-nowrap">
                    <i class="fas fa-th-large mr-1.5 text-sm"></i>全部
                </button>
                <button class="h-7 px-4 rounded-full bg-white/30 backdrop-blur-sm text-[var(--color-text-secondary)] text-xs flex items-center whitespace-nowrap">
                    <i class="fas fa-briefcase mr-1.5 text-sm"></i>职场
                </button>
                <button class="h-7 px-4 rounded-full bg-white/30 backdrop-blur-sm text-[var(--color-text-secondary)] text-xs flex items-center whitespace-nowrap">
                    <i class="fas fa-champagne-glasses mr-1.5 text-sm"></i>约会
                </button>
                <button class="h-7 px-4 rounded-full bg-white/30 backdrop-blur-sm text-[var(--color-text-secondary)] text-xs flex items-center whitespace-nowrap">
                    <i class="fas fa-mug-saucer mr-1.5 text-sm"></i>休闲
                </button>
                <button class="h-7 px-4 rounded-full bg-white/30 backdrop-blur-sm text-[var(--color-text-secondary)] text-xs flex items-center whitespace-nowrap">
                    <i class="fas fa-plane mr-1.5 text-sm"></i>旅行
                </button>
                <button class="h-7 px-4 rounded-full bg-white/30 backdrop-blur-sm text-[var(--color-text-secondary)] text-xs flex items-center whitespace-nowrap">
                    <i class="fas fa-graduation-cap mr-1.5 text-sm"></i>学院
                </button>
                <button class="h-7 px-4 rounded-full bg-white/30 backdrop-blur-sm text-[var(--color-text-secondary)] text-xs flex items-center whitespace-nowrap">
                    <i class="fas fa-umbrella-beach mr-1.5 text-sm"></i>度假
                </button>
            </div>

            <!-- 案例卡片网格 - 直接展示小卡片，保留所有案例 -->
            <div class="grid grid-cols-2 gap-5 px-1 mb-4">
                <!-- 案例1 -->
                <div class="bg-white/15 backdrop-blur-sm rounded-xl p-2 shadow-md hover:shadow-lg transition-all duration-300 cursor-pointer" onclick="location.href='../home/<USER>'">
                    <div class="aspect-square rounded-lg overflow-hidden bg-white/20 relative">
                        <img src="https://images.unsplash.com/photo-1483985988355-763728e1935b" 
                             class="w-full h-full object-cover" alt="案例分享1">
                        <!-- 魔法棒按钮 -->
                        <button class="absolute top-2 right-2 glass-button rounded-full w-8 h-8 flex items-center justify-center text-[var(--color-text-primary)] backdrop-blur-sm shadow-sm">
                            <i class="fas fa-wand-magic-sparkles text-xs"></i>
                        </button>
                    </div>
                    <div class="mt-2 flex justify-between items-start">
                        <p class="text-xs font-medium text-[var(--color-text-primary)]">春季踏青穿搭</p>
                        <div class="flex space-x-2">
                            <button class="text-xs flex items-center glass-button px-2 py-0.5 rounded-full min-w-[52px] justify-center">
                                <i class="fas fa-heart text-pink-400 mr-1"></i>
                                <span class="text-[var(--color-text-primary)]">28</span>
                            </button>
                            <button class="text-xs flex items-center glass-button px-2 py-0.5 rounded-full min-w-[52px] justify-center">
                                <i class="far fa-star text-amber-400 mr-1"></i>
                                <span class="text-[var(--color-text-primary)]">12</span>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 案例2 -->
                <div class="bg-white/15 backdrop-blur-sm rounded-xl p-2 shadow-md hover:shadow-lg transition-all duration-300 cursor-pointer" onclick="location.href='../home/<USER>'">
                    <div class="aspect-square rounded-lg overflow-hidden bg-white/20 relative">
                        <img src="https://images.unsplash.com/photo-1485230895905-ec40ba36b9bc" 
                             class="w-full h-full object-cover" alt="案例分享2">
                        <button class="absolute top-2 right-2 glass-button rounded-full w-8 h-8 flex items-center justify-center text-[var(--color-text-primary)] backdrop-blur-sm shadow-sm">
                            <i class="fas fa-wand-magic-sparkles text-xs"></i>
                        </button>
                    </div>
                    <div class="mt-2 flex justify-between items-start">
                        <p class="text-xs font-medium text-[var(--color-text-primary)]">职场通勤搭配</p>
                        <div class="flex space-x-2">
                            <button class="text-xs flex items-center glass-button px-2 py-0.5 rounded-full min-w-[52px] justify-center">
                                <i class="fas fa-heart text-pink-400 mr-1"></i>
                                <span class="text-[var(--color-text-primary)]">42</span>
                            </button>
                            <button class="text-xs flex items-center glass-button px-2 py-0.5 rounded-full min-w-[52px] justify-center">
                                <i class="far fa-star text-amber-400 mr-1"></i>
                                <span class="text-[var(--color-text-primary)]">18</span>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 案例3 -->
                <div class="bg-white/15 backdrop-blur-sm rounded-xl p-2 shadow-md hover:shadow-lg transition-all duration-300 cursor-pointer" onclick="location.href='../home/<USER>'">
                    <div class="aspect-square rounded-lg overflow-hidden bg-white/20 relative">
                        <img src="https://images.unsplash.com/photo-1475180098004-ca77a66827be" 
                             class="w-full h-full object-cover" alt="案例分享3">
                        <button class="absolute top-2 right-2 glass-button rounded-full w-8 h-8 flex items-center justify-center text-[var(--color-text-primary)] backdrop-blur-sm shadow-sm">
                            <i class="fas fa-wand-magic-sparkles text-xs"></i>
                        </button>
                    </div>
                    <div class="mt-2 flex justify-between items-start">
                        <p class="text-xs font-medium text-[var(--color-text-primary)]">浪漫约会穿搭</p>
                        <div class="flex space-x-2">
                            <button class="text-xs flex items-center glass-button px-2 py-0.5 rounded-full min-w-[52px] justify-center">
                                <i class="fas fa-heart text-pink-400 mr-1"></i>
                                <span class="text-[var(--color-text-primary)]">56</span>
                            </button>
                            <button class="text-xs flex items-center glass-button px-2 py-0.5 rounded-full min-w-[52px] justify-center">
                                <i class="far fa-star text-amber-400 mr-1"></i>
                                <span class="text-[var(--color-text-primary)]">24</span>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 案例4 - 极简主义风格 -->
                <div class="bg-white/15 backdrop-blur-sm rounded-xl p-2 shadow-md hover:shadow-lg transition-all duration-300 cursor-pointer" onclick="location.href='../home/<USER>'">
                    <div class="aspect-square rounded-lg overflow-hidden bg-white/20 relative">
                        <img src="https://images.unsplash.com/photo-1554412933-514a83d2f3c8" 
                             class="w-full h-full object-cover" alt="案例分享4">
                        <button class="absolute top-2 right-2 glass-button rounded-full w-8 h-8 flex items-center justify-center text-[var(--color-text-primary)] backdrop-blur-sm shadow-sm">
                            <i class="fas fa-wand-magic-sparkles text-xs"></i>
                        </button>
                    </div>
                    <div class="mt-2 flex justify-between items-start">
                        <p class="text-xs font-medium text-[var(--color-text-primary)]">极简主义风格</p>
                        <div class="flex space-x-2">
                            <button class="text-xs flex items-center glass-button px-2 py-0.5 rounded-full min-w-[52px] justify-center">
                                <i class="fas fa-heart text-pink-400 mr-1"></i>
                                <span class="text-[var(--color-text-primary)]">61</span>
                            </button>
                            <button class="text-xs flex items-center glass-button px-2 py-0.5 rounded-full min-w-[52px] justify-center">
                                <i class="far fa-star text-amber-400 mr-1"></i>
                                <span class="text-[var(--color-text-primary)]">27</span>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 案例5 -->
                <div class="bg-white/15 backdrop-blur-sm rounded-xl p-2 shadow-md hover:shadow-lg transition-all duration-300 cursor-pointer" onclick="location.href='../home/<USER>'">
                    <div class="aspect-square rounded-lg overflow-hidden bg-white/20 relative">
                        <img src="https://images.unsplash.com/photo-1581044777550-4cfa60707c03" 
                             class="w-full h-full object-cover" alt="案例分享5">
                        <button class="absolute top-2 right-2 glass-button rounded-full w-8 h-8 flex items-center justify-center text-[var(--color-text-primary)] backdrop-blur-sm shadow-sm">
                            <i class="fas fa-wand-magic-sparkles text-xs"></i>
                        </button>
                    </div>
                    <div class="mt-2 flex justify-between items-start">
                        <p class="text-xs font-medium text-[var(--color-text-primary)]">秋季层叠风格</p>
                        <div class="flex space-x-2">
                            <button class="text-xs flex items-center glass-button px-2 py-0.5 rounded-full min-w-[52px] justify-center">
                                <i class="fas fa-heart text-pink-400 mr-1"></i>
                                <span class="text-[var(--color-text-primary)]">47</span>
                            </button>
                            <button class="text-xs flex items-center glass-button px-2 py-1 rounded-full min-w-[52px] justify-center">
                                <i class="far fa-star text-amber-400 mr-1"></i>
                                <span class="text-[var(--color-text-primary)]">19</span>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 案例6 -->
                <div class="bg-white/15 backdrop-blur-sm rounded-xl p-2 shadow-md hover:shadow-lg transition-all duration-300 cursor-pointer" onclick="location.href='../home/<USER>'">
                    <div class="aspect-square rounded-lg overflow-hidden bg-white/20 relative">
                        <img src="https://images.unsplash.com/photo-1529139574466-a303027c1d8b" 
                             class="w-full h-full object-cover" alt="案例分享6">
                        <button class="absolute top-2 right-2 glass-button rounded-full w-8 h-8 flex items-center justify-center text-[var(--color-text-primary)] backdrop-blur-sm shadow-sm">
                            <i class="fas fa-wand-magic-sparkles text-xs"></i>
                        </button>
                    </div>
                    <div class="mt-2 flex justify-between items-start">
                        <p class="text-xs font-medium text-[var(--color-text-primary)]">街头潮流搭配</p>
                        <div class="flex space-x-2">
                            <button class="text-xs flex items-center glass-button px-2 py-1 rounded-full min-w-[52px] justify-center">
                                <i class="fas fa-heart text-pink-400 mr-1"></i>
                                <span class="text-[var(--color-text-primary)]">65</span>
                            </button>
                            <button class="text-xs flex items-center glass-button px-2 py-1 rounded-full min-w-[52px] justify-center">
                                <i class="far fa-star text-amber-400 mr-1"></i>
                                <span class="text-[var(--color-text-primary)]">32</span>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 案例7 -->
                <div class="bg-white/15 backdrop-blur-sm rounded-xl p-2 shadow-md hover:shadow-lg transition-all duration-300 cursor-pointer" onclick="location.href='../home/<USER>'">
                    <div class="aspect-square rounded-lg overflow-hidden bg-white/20 relative">
                        <img src="https://images.unsplash.com/photo-1499939667766-4afceb292d05" 
                             class="w-full h-full object-cover" alt="案例分享7">
                        <button class="absolute top-2 right-2 glass-button rounded-full w-8 h-8 flex items-center justify-center text-[var(--color-text-primary)] backdrop-blur-sm shadow-sm">
                            <i class="fas fa-wand-magic-sparkles text-xs"></i>
                        </button>
                    </div>
                    <div class="mt-2 flex justify-between items-start">
                        <p class="text-xs font-medium text-[var(--color-text-primary)]">优雅轻熟风</p>
                        <div class="flex space-x-2">
                            <button class="text-xs flex items-center glass-button px-2 py-1 rounded-full min-w-[52px] justify-center">
                                <i class="fas fa-heart text-pink-400 mr-1"></i>
                                <span class="text-[var(--color-text-primary)]">51</span>
                            </button>
                            <button class="text-xs flex items-center glass-button px-2 py-1 rounded-full min-w-[52px] justify-center">
                                <i class="far fa-star text-amber-400 mr-1"></i>
                                <span class="text-[var(--color-text-primary)]">22</span>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 案例8 -->
                <div class="bg-white/15 backdrop-blur-sm rounded-xl p-2 shadow-md hover:shadow-lg transition-all duration-300 cursor-pointer" onclick="location.href='../home/<USER>'">
                    <div class="aspect-square rounded-lg overflow-hidden bg-white/20 relative">
                        <img src="https://images.unsplash.com/photo-1503185912284-5271ff81b9a8" 
                             class="w-full h-full object-cover" alt="案例分享8">
                        <button class="absolute top-2 right-2 glass-button rounded-full w-8 h-8 flex items-center justify-center text-[var(--color-text-primary)] backdrop-blur-sm shadow-sm">
                            <i class="fas fa-wand-magic-sparkles text-xs"></i>
                        </button>
                    </div>
                    <div class="mt-2 flex justify-between items-start">
                        <p class="text-xs font-medium text-[var(--color-text-primary)]">复古学院风</p>
                        <div class="flex space-x-2">
                            <button class="text-xs flex items-center glass-button px-2 py-1 rounded-full min-w-[52px] justify-center">
                                <i class="fas fa-heart text-pink-400 mr-1"></i>
                                <span class="text-[var(--color-text-primary)]">44</span>
                            </button>
                            <button class="text-xs flex items-center glass-button px-2 py-1 rounded-full min-w-[52px] justify-center">
                                <i class="far fa-star text-amber-400 mr-1"></i>
                                <span class="text-[var(--color-text-primary)]">16</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 拍照浮窗按钮 - 统一风格 -->
    <button class="fixed right-5 bottom-24 w-14 h-14 rounded-full glass-button flex items-center justify-center shadow-lg glow-icon">
        <i class="fas fa-camera text-[var(--color-text-primary)] text-xl"></i>
    </button>

    <!-- 底部导航栏 -->
    <div class="fixed bottom-0 left-0 right-0 bg-white/60 backdrop-blur-md border-t border-white/60">
        <div class="grid grid-cols-4 h-20">
            <a href="home.html" class="flex flex-col items-center justify-center text-[var(--color-text-primary)]">
                <div class="w-10 h-10 rounded-full bg-white/70 backdrop-blur-md flex items-center justify-center">
                    <i class="fas fa-home text-lg"></i>
                </div>
                <span class="text-xs mt-1 font-medium">首页</span>
            </a>
            <a href="wardrobe.html" class="flex flex-col items-center justify-center text-[var(--color-text-secondary)]">
                <i class="fas fa-tshirt text-xl"></i>
                <span class="text-xs mt-1">衣橱</span>
            </a>
            <a href="outfit.html" class="flex flex-col items-center justify-center text-[var(--color-text-secondary)]">
                <i class="fas fa-wand-magic-sparkles text-xl"></i>
                <span class="text-xs mt-1">搭配</span>
            </a>
            <a href="profile.html" class="flex flex-col items-center justify-center text-[var(--color-text-secondary)]">
                <i class="fas fa-user text-xl"></i>
                <span class="text-xs mt-1">我的</span>
            </a>
        </div>
    </div>

    <script>
        // 初始化所有交互按钮
        function initializeActionButtons() {
            try {
                // 获取所有点赞按钮
                document.querySelectorAll('button:has(i.fa-heart)').forEach(button => {
                    if (!button) return;
                    
                    const icon = button.querySelector('i');
                    const count = button.querySelector('span');
                    if (!icon || !count) return;
                    
                    let isLiked = icon.classList.contains('fas');
                    let likeCount = parseInt(count.textContent) || 0;

                    button.addEventListener('click', (e) => {
                        e.stopPropagation(); // 阻止冒泡，避免触发卡片点击
                        isLiked = !isLiked;
                        
                        // 更新图标
                        icon.classList.toggle('far');
                        icon.classList.toggle('fas');
                        
                        // 更新数字
                        likeCount = isLiked ? likeCount + 1 : likeCount - 1;
                        count.textContent = likeCount;
                        
                        // 添加动画效果
                        icon.style.transform = 'scale(1.25)';
                        setTimeout(() => {
                            icon.style.transform = 'scale(1)';
                        }, 200);
                    });
                });

                // 获取所有收藏按钮
                document.querySelectorAll('button:has(i.fa-star)').forEach(button => {
                    if (!button) return;
                    
                    const icon = button.querySelector('i');
                    const count = button.querySelector('span');
                    if (!icon || !count) return;
                    
                    let isFavorited = icon.classList.contains('fas');
                    let favoriteCount = parseInt(count.textContent) || 0;

                    button.addEventListener('click', (e) => {
                        e.stopPropagation(); // 阻止冒泡，避免触发卡片点击
                        isFavorited = !isFavorited;
                        
                        // 更新图标
                        icon.classList.toggle('far');
                        icon.classList.toggle('fas');
                        
                        // 更新数字
                        favoriteCount = isFavorited ? favoriteCount + 1 : favoriteCount - 1;
                        count.textContent = favoriteCount;
                        
                        // 添加动画效果
                        icon.style.transform = 'scale(1.25)';
                        setTimeout(() => {
                            icon.style.transform = 'scale(1)';
                        }, 200);
                    });
                });

                // 获取所有分享按钮
                document.querySelectorAll('button:has(i.fa-share-alt)').forEach(button => {
                    if (!button) return;
                    
                    const icon = button.querySelector('i');
                    if (!icon) return;

                    button.addEventListener('click', (e) => {
                        e.stopPropagation(); // 阻止冒泡，避免触发卡片点击
                        
                        // 添加动画效果
                        icon.style.transform = 'scale(1.25)';
                        setTimeout(() => {
                            icon.style.transform = 'scale(1)';
                        }, 200);
                        
                        // TODO: 实现分享功能
                        console.log('分享功能待实现');
                    });
                });
            } catch (error) {
                console.error('初始化交互按钮时发生错误:', error);
            }
        }

        // 确保 DOM 完全加载后再初始化按钮
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initializeActionButtons);
        } else {
            initializeActionButtons();
        }

        // 轮播功能初始化
        function initializeSlider() {
            try {
                // 获取必要的DOM元素
                const swipeArea = document.querySelector('.swipe-area');
        const outfitSlides = document.getElementById('outfitSlides');
        const indicators = document.querySelectorAll('.flex.justify-center.py-2 .w-2.h-2');
                const slideCounter = document.querySelector('.flex.space-x-2 span');
                
                // 检查必要元素是否存在
                if (!swipeArea || !outfitSlides || !indicators.length) {
                    console.warn('轮播所需的某些元素不存在，轮播功能将不会初始化');
                    return;
                }

                let currentSlide = 0;
                const totalSlides = 3; // 设置总共的搭配数量
                
                // 初始化触摸事件
        let xDown = null;
        let yDown = null;

        function handleTouchStart(evt) {
            xDown = evt.touches[0].clientX;
            yDown = evt.touches[0].clientY;
        }

        function handleTouchMove(evt) {
            if (!xDown || !yDown) {
                return;
            }

            let xUp = evt.touches[0].clientX;
            let yUp = evt.touches[0].clientY;

            let xDiff = xDown - xUp;
            let yDiff = yDown - yUp;

            if (Math.abs(xDiff) > Math.abs(yDiff)) {
                if (xDiff > 0) {
                    // 向左滑 - 下一张
                    nextSlide();
                } else {
                    // 向右滑 - 上一张
                    prevSlide();
                }
            }

            xDown = null;
            yDown = null;
        }
        
        function nextSlide() {
            currentSlide = (currentSlide + 1) % totalSlides;
            updateSlider();
        }
        
        function prevSlide() {
            currentSlide = (currentSlide - 1 + totalSlides) % totalSlides;
            updateSlider();
        }
        
        function updateSlider() {
            // 更新轮播位置
            outfitSlides.style.transform = `translateX(-${currentSlide * 100}%)`;
            
            // 更新数字指示器
                    if (slideCounter) {
                        slideCounter.textContent = `${currentSlide + 1}/${totalSlides}`;
                    }
            
            // 更新轮播指示点
            indicators.forEach((indicator, index) => {
                if (index === currentSlide) {
                    indicator.classList.remove('bg-white/40');
                    indicator.classList.add('bg-white/80');
                } else {
                    indicator.classList.remove('bg-white/80');
                    indicator.classList.add('bg-white/40');
                }
            });
                }

                // 添加触摸事件监听器
                swipeArea.addEventListener('touchstart', handleTouchStart);
                swipeArea.addEventListener('touchmove', handleTouchMove);
                
                // 初始化轮播状态
                updateSlider();
            } catch (error) {
                console.error('初始化轮播功能时发生错误:', error);
            }
        }

        // 确保 DOM 完全加载后再初始化轮播
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initializeSlider);
        } else {
            initializeSlider();
        }
        
        // 拍照按钮点击事件
        function initializeCameraButton() {
            try {
                const cameraButton = document.querySelector('.floating-camera');
                if (!cameraButton) {
                    console.warn('拍照按钮不存在');
                    return;
                }

                cameraButton.addEventListener('click', function() {
            console.log('打开拍照功能');
            // 此处添加调用微信拍照API的代码
        });
            } catch (error) {
                console.error('初始化拍照按钮时发生错误:', error);
            }
        }

        // 确保 DOM 完全加载后再初始化拍照按钮
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initializeCameraButton);
        } else {
            initializeCameraButton();
        }
    </script>
</body>
</html> 