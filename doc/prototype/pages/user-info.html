<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>完善资料 - StylishLink</title>
    <link href="../styles/main.css" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            min-height: 100vh;
            background: linear-gradient(135deg, #c2e9fb 0%, #d8c6fd 100%);
            padding: 0;
            margin: 0;
            overflow-x: hidden;
        }

        /* 隐藏滚动条但保持滚动功能 */
        * {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        
        *::-webkit-scrollbar {
            display: none;
        }

        .app-container {
            display: flex;
            flex-direction: column;
            height: 100vh;
            max-height: 100vh;
            overflow: hidden;
        }

        .header {
            background-color: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.3);
            padding: 0.75rem 1.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10;
            flex-shrink: 0;
        }

        .content-area {
            flex: 1;
            overflow-y: auto;
            padding: 1.5rem;
            -webkit-overflow-scrolling: touch;
        }

        .card {
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border-radius: 1rem;
            margin-bottom: 1rem;
        }

        .form-input {
            background: rgba(255, 255, 255, 0.4);
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.5);
            transition: all 0.3s ease;
            width: 100%;
            padding: 0.5rem;
            border-radius: 0.5rem;
        }

        .form-input:focus {
            background: rgba(255, 255, 255, 0.6);
            border-color: #8B5CF6;
            outline: none;
            box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.2);
        }

        .style-tag {
            transition: all 0.3s ease;
        }

        .style-tag.selected, 
        .btn-option.selected {
            background: #8B5CF6;
            color: white;
            border-color: #8B5CF6;
        }

        .progress-step {
            transition: all 0.3s ease;
            width: 2rem;
            height: 2rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 500;
            position: relative;
            z-index: 1;
        }

        .progress-step.active {
            background: #8B5CF6;
            color: white;
        }

        .progress-step.completed {
            background: #10B981;
            color: white;
        }

        .step-content {
            min-height: auto;
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .slider-container {
            position: relative;
            margin-top: 1.5rem;
        }

        .slider-value {
            position: absolute;
            top: -25px;
            left: 50%;
            transform: translateX(-50%);
            background: #8B5CF6;
            color: white;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
        }

        input[type="range"] {
            -webkit-appearance: none;
            width: 100%;
            height: 8px;
            border-radius: 4px;
            background: #e4e4e7;
            outline: none;
        }

        input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #8B5CF6;
            cursor: pointer;
            border: 2px solid white;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
        }

        .energy-info {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.5s ease;
        }

        .energy-info.show {
            max-height: 1000px;
        }

        /* 优化表单布局 */
        .form-group {
            margin-bottom: 1rem;
        }

        .form-label {
            display: block;
            font-size: 0.875rem;
            font-weight: 500;
            margin-bottom: 0.5rem;
            color: var(--color-text-primary);
        }

        /* 优化按钮样式 */
        .btn {
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #8B5CF6;
            color: white;
        }

        .btn-primary:hover {
            background: #7C3AED;
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.3);
            color: var(--color-text-primary);
        }

        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.4);
        }

        .photo-guide {
            max-height: 200px;
            object-fit: contain;
        }
        .full-body-photo {
            min-height: 200px;
            background-size: contain;
            background-position: center;
            background-repeat: no-repeat;
        }

        /* 预览卡片样式优化 */
        .preview-container {
            background: rgba(255, 255, 255, 0.25);
            border-radius: 1rem;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            margin-bottom: 1.5rem;
            border: 1px solid rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }
        
        .preview-container:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 28px rgba(0, 0, 0, 0.15);
            border: 1px solid rgba(255, 255, 255, 0.5);
        }
        
        .preview-header {
            background: rgba(139, 92, 246, 0.6);
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
            color: white;
            padding: 1rem 1.5rem;
            font-weight: 600;
            letter-spacing: 0.02em;
            font-size: 1.1rem;
            display: flex;
            align-items: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .preview-header i {
            margin-right: 0.5rem;
            filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
        }
        
        .preview-content {
            padding: 1.5rem;
            background: rgba(255, 255, 255, 0.1);
        }
        
        /* 能量模式预览样式 */
        #energy-info-preview .preview-header {
            background: rgba(99, 102, 241, 0.6);
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
        }
        
        /* 改进信息项样式 */
        .info-item {
            display: flex;
            margin-bottom: 0.875rem;
            align-items: baseline;
            padding-bottom: 0.875rem;
            border-bottom: 1px dashed rgba(209, 213, 219, 0.3);
            position: relative;
            z-index: 1;
        }
        
        .info-item::before {
            content: '';
            position: absolute;
            left: 0;
            right: 0;
            top: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(4px);
            -webkit-backdrop-filter: blur(4px);
            border-radius: 0.5rem;
            opacity: 0;
            transition: opacity 0.2s ease;
            z-index: -1;
        }
        
        .info-item:hover::before {
            opacity: 1;
        }
        
        .info-item:last-child {
            margin-bottom: 0;
            padding-bottom: 0;
            border-bottom: none;
        }
        
        .info-label {
            color: #4B5563;
            width: 5.5rem;
            flex-shrink: 0;
            font-weight: 500;
            font-size: 0.9rem;
        }
        
        .info-value {
            color: #1F2937;
            flex: 1;
            font-size: 1rem;
        }
        
        /* 标签样式优化 */
        .tag {
            display: inline-block;
            padding: 0.35rem 0.75rem;
            background: rgba(238, 242, 255, 0.4);
            backdrop-filter: blur(4px);
            -webkit-backdrop-filter: blur(4px);
            color: #4F46E5;
            border-radius: 9999px;
            font-size: 0.75rem;
            margin-right: 0.4rem;
            margin-bottom: 0.4rem;
            font-weight: 500;
            border: 1px solid rgba(79, 70, 229, 0.2);
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
            transition: all 0.2s;
        }
        
        .tag:hover {
            background: rgba(224, 231, 255, 0.6);
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        /* 照片预览优化 */
        #preview-photo {
            height: 240px !important;
            background-size: contain;
            background-position: center;
            background-repeat: no-repeat;
            border-radius: 0.5rem;
            position: relative;
            border: none;
            box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.1);
            overflow: hidden;
        }
        
        #preview-photo::before {
            content: '';
            position: absolute;
            left: 0;
            right: 0;
            top: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(2px);
            -webkit-backdrop-filter: blur(2px);
            border-radius: 0.5rem;
            z-index: -1;
        }
        
        /* 为空数据添加样式 */
        .empty-value {
            color: #9CA3AF;
            font-style: italic;
            font-size: 0.9rem;
        }

        /* === 迁移基础体型模块样式（避免命名冲突，直接追加） === */
        .enhanced-glass {
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.05);
            position: relative;
            z-index: 10;
        }
        .data-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 6px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }
        .data-item:last-child { border-bottom: none; }
        .selector-wrapper { position: relative; z-index: 20; }
        .selector-wrapper.active { z-index: 40; }
        .selector-button {
            background: rgba(255, 255, 255, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.5);
            border-radius: 8px;
            padding: 6px 12px;
            font-size: 12px;
            color: var(--color-text-primary);
            text-align: left;
            transition: all 0.3s ease;
            width: 120px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .selector-button:not(:disabled):hover { background: rgba(255,255,255,0.4); }
        .dropdown-menu {
            position: absolute;
            top: calc(100% + 4px);
            left: 0;
            right: 0;
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.5);
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            max-height: 200px;
            overflow-y: auto;
            z-index: 30;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.2s ease;
            width: 120px;
        }
        .dropdown-menu.show { opacity: 1; visibility: visible; transform: translateY(0); }
        .dropdown-item {
            padding: 8px 12px;
            font-size: 12px;
            color: var(--color-text-primary);
            transition: all 0.2s ease;
            cursor: pointer;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .dropdown-item:hover { background: rgba(255,255,255,0.5); }
        .dropdown-item.selected { background: rgba(168,230,207,0.3); }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- 标题栏 -->
        <header class="header">
            <div class="flex items-center">
                <h1 id="dynamic-header-title" class="text-sm font-semibold text-gray-800"></h1>
            </div>
        </header>

        <!-- 主内容区域 -->
        <main class="content-area">
            <!-- 进度指示器 -->
            <div class="mx-auto max-w-2xl mb-6">
                <div class="flex justify-between items-center relative">
                    <div class="absolute h-1 bg-gray-200 left-0 right-0 top-1/2 transform -translate-y-1/2 z-0"></div>
                    <div class="progress-step active w-8 h-8 rounded-full flex items-center justify-center text-white z-10 font-medium">
                        1
                    </div>
                    <div class="progress-step w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center text-gray-600 z-10 font-medium">
                        2
                    </div>
                    <div class="progress-step w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center text-gray-600 z-10 font-medium">
                        3
                    </div>
                </div>
            </div>

            <form id="user-info-form" class="mx-auto max-w-2xl">
                <!-- 步骤1: 全身照上传 -->
                <div id="step-1" class="space-y-6 step-content">
                    <div class="text-center pb-3 border-b border-gray-200">
                        <p class="text-xs text-gray-600">上传一张全身照，帮助我们为您提供更贴合的穿搭推荐</p>
                    </div>
                    <div class="section flex-1">
                        <div class="flex items-center justify-center gap-4 flex-wrap">
                            <!-- 上传区域 -->
                            <div class="text-center flex-1 min-w-[200px]">
                                <div id="full-body-photo" class="full-body-photo h-[280px] rounded-lg border-2 border-dashed border-gray-300 flex items-center justify-center cursor-pointer hover:border-indigo-400 transition-colors">
                                    <div id="upload-placeholder" class="text-center p-4">
                                        <i class="fas fa-camera text-4xl text-gray-400 mb-2"></i>
                                        <p class="text-gray-500">点击上传全身照</p>
                                        <p class="text-xs text-gray-400 mt-1">建议使用站立正面照</p>
                                    </div>
                                </div>
                                <input type="file" id="photo-upload" class="hidden" accept="image/*">
                            </div>
                        </div>
                    </div>
                    <!-- 基础体型模块，默认隐藏 -->
                    <div id="basic-body-data-section" class="hidden">
                        <div class="enhanced-glass rounded-xl p-4 mt-0 mb-4">
                            <h3 class="text-sm font-medium text-[var(--color-text-primary)] mb-3">智能识别出您的体型特点如下，若不准确请手动修改：</h3>
                            <!-- 前4项：肩膀、腰型、臀型、胯型 -->
                            <div class="data-item">
                                <span class="text-xs text-[var(--color-text-primary)]">肩膀</span>
                                <div class="selector-wrapper">
                                    <button class="selector-button" data-type="shoulderWidth" type="button">
                                        <span class="text-xs text-[var(--color-text-secondary)]">正常</span>
                                        <i class="fas fa-chevron-down text-[10px] ml-2"></i>
                                    </button>
                                    <div class="dropdown-menu" id="shoulderWidthDropdown"></div>
                                </div>
                            </div>
                            <div class="data-item">
                                <span class="text-xs text-[var(--color-text-primary)]">腰型</span>
                                <div class="selector-wrapper">
                                    <button class="selector-button" data-type="waistShape" type="button">
                                        <span class="text-xs text-[var(--color-text-secondary)]">直筒</span>
                                        <i class="fas fa-chevron-down text-[10px] ml-2"></i>
                                    </button>
                                    <div class="dropdown-menu" id="waistShapeDropdown"></div>
                                </div>
                            </div>
                            <div class="data-item">
                                <span class="text-xs text-[var(--color-text-primary)]">臀型</span>
                                <div class="selector-wrapper">
                                    <button class="selector-button" data-type="hip" type="button">
                                        <span class="text-xs text-[var(--color-text-secondary)]">下榻</span>
                                        <i class="fas fa-chevron-down text-[10px] ml-2"></i>
                                    </button>
                                    <div class="dropdown-menu" id="hipDropdown"></div>
                                </div>
                            </div>
                            <div class="data-item">
                                <span class="text-xs text-[var(--color-text-primary)]">胯型</span>
                                <div class="selector-wrapper">
                                    <button class="selector-button" data-type="hipShape" type="button">
                                        <span class="text-xs text-[var(--color-text-secondary)]">直筒</span>
                                        <i class="fas fa-chevron-down text-[10px] ml-2"></i>
                                    </button>
                                    <div class="dropdown-menu" id="hipShapeDropdown"></div>
                                </div>
                            </div>
                            <!-- 折叠内容：其余8项 -->
                            <div class="body-data-extra-items" style="display:none">
                                <div class="data-item">
                                    <span class="text-xs text-[var(--color-text-primary)]">肚腩</span>
                                    <div class="selector-wrapper">
                                        <button class="selector-button" data-type="belly" type="button">
                                            <span class="text-xs text-[var(--color-text-secondary)]">没有</span>
                                            <i class="fas fa-chevron-down text-[10px] ml-2"></i>
                                        </button>
                                        <div class="dropdown-menu" id="bellyDropdown"></div>
                                    </div>
                                </div>
                                <div class="data-item">
                                    <span class="text-xs text-[var(--color-text-primary)]">臂长</span>
                                    <div class="selector-wrapper">
                                        <button class="selector-button" data-type="armLength" type="button">
                                            <span class="text-xs text-[var(--color-text-secondary)]">正常</span>
                                            <i class="fas fa-chevron-down text-[10px] ml-2"></i>
                                        </button>
                                        <div class="dropdown-menu" id="armLengthDropdown"></div>
                                    </div>
                                </div>
                                <div class="data-item">
                                    <span class="text-xs text-[var(--color-text-primary)]">臂围</span>
                                    <div class="selector-wrapper">
                                        <button class="selector-button" data-type="armCircum" type="button">
                                            <span class="text-xs text-[var(--color-text-secondary)]">正常</span>
                                            <i class="fas fa-chevron-down text-[10px] ml-2"></i>
                                        </button>
                                        <div class="dropdown-menu" id="armCircumDropdown"></div>
                                    </div>
                                </div>
                                <div class="data-item">
                                    <span class="text-xs text-[var(--color-text-primary)]">胯部</span>
                                    <div class="selector-wrapper">
                                        <button class="selector-button" data-type="hipWidth" type="button">
                                            <span class="text-xs text-[var(--color-text-secondary)]">正常</span>
                                            <i class="fas fa-chevron-down text-[10px] ml-2"></i>
                                        </button>
                                        <div class="dropdown-menu" id="hipWidthDropdown"></div>
                                    </div>
                                </div>
                                <div class="data-item">
                                    <span class="text-xs text-[var(--color-text-primary)]">大腿</span>
                                    <div class="selector-wrapper">
                                        <button class="selector-button" data-type="thigh" type="button">
                                            <span class="text-xs text-[var(--color-text-secondary)]">正常</span>
                                            <i class="fas fa-chevron-down text-[10px] ml-2"></i>
                                        </button>
                                        <div class="dropdown-menu" id="thighDropdown"></div>
                                    </div>
                                </div>
                                <div class="data-item">
                                    <span class="text-xs text-[var(--color-text-primary)]">小腿</span>
                                    <div class="selector-wrapper">
                                        <button class="selector-button" data-type="calf" type="button">
                                            <span class="text-xs text-[var(--color-text-secondary)]">正常</span>
                                            <i class="fas fa-chevron-down text-[10px] ml-2"></i>
                                        </button>
                                        <div class="dropdown-menu" id="calfDropdown"></div>
                                    </div>
                                </div>
                                <div class="data-item">
                                    <span class="text-xs text-[var(--color-text-primary)]">上下身粗</span>
                                    <div class="selector-wrapper">
                                        <button class="selector-button" data-type="bodyFat" type="button">
                                            <span class="text-xs text-[var(--color-text-secondary)]">匀称</span>
                                            <i class="fas fa-chevron-down text-[10px] ml-2"></i>
                                        </button>
                                        <div class="dropdown-menu" id="bodyFatDropdown"></div>
                                    </div>
                                </div>
                                <div class="data-item">
                                    <span class="text-xs text-[var(--color-text-primary)]">上下身长</span>
                                    <div class="selector-wrapper">
                                        <button class="selector-button" data-type="bodyLength" type="button">
                                            <span class="text-xs text-[var(--color-text-secondary)]">匀称</span>
                                            <i class="fas fa-chevron-down text-[10px] ml-2"></i>
                                        </button>
                                        <div class="dropdown-menu" id="bodyLengthDropdown"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="text-center mt-2">
                                <button type="button" id="toggle-body-data-extra" class="glass-button rounded-full text-[10px] px-4 py-1 mt-2 hover:bg-white/60 transition-colors">更多 <i class="fas fa-chevron-down ml-1 text-gray-400" style="font-size:10px;"></i></button>
                            </div>
                        </div>
                        <div class="enhanced-glass rounded-xl p-4 mb-4">
                            <div class="flex justify-between items-center mb-3">
                                <h3 class="text-sm font-medium text-[var(--color-text-primary)]">体型特点</h3>
                                <span id="bodyTypeTag" class="tag">匀称体型</span>
                            </div>
                            <div class="space-y-3">
                                <p class="text-[10px] text-[var(--color-text-secondary)]">
                                    <i class="fas fa-check-circle text-[#a8e6cf] mr-2"></i>
                                    肩部和臀部宽度相近，腰部明显收窄
                                </p>
                                <p class="text-[10px] text-[var(--color-text-secondary)]">
                                    <i class="fas fa-check-circle text-[#a8e6cf] mr-2"></i>
                                    身材比例协调，曲线优美
                                </p>
                                <p class="text-[10px] text-[var(--color-text-secondary)]">
                                    <i class="fas fa-check-circle text-[#a8e6cf] mr-2"></i>
                                    适合展现腰部曲线的服装款式
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="text-center mt-auto pt-4 border-t border-gray-200">
                        <button type="button" id="next-to-step-2" class="glass-button rounded-md text-xs px-6 py-2 hover:bg-white/60 transition-colors">下一步</button>
                    </div>
                </div>
                <!-- 步骤2: 基础信息 -->
                <div id="step-2" class="space-y-6 hidden step-content">
                    <div class="text-center pb-3 border-b border-gray-200">
                        <p class="text-xs text-gray-600">填写基本信息，让我们了解您的穿搭需求</p>
                    </div>
                    <!-- 基础信息区域 -->
                    <div class="section">
                        <h3 class="font-medium text-gray-800 mb-4">穿搭基础信息</h3>
                        <div class="space-y-4">
                            <!-- 身高滑动条 -->
                            <div>
                                <label class="block text-xs font-medium text-gray-700 mb-1">身高</label>
                                <div class="slider-container">
                                    <span id="height-value" class="slider-value">170cm</span>
                                    <input type="range" id="height-slider" min="140" max="210" value="170" class="w-full" oninput="updateSliderValue(this, 'height-value', 'cm')">
                                    <div class="flex justify-between text-xs text-gray-500 mt-1">
                                        <span>140cm</span>
                                        <span>210cm</span>
                                    </div>
                                </div>
                                <input type="hidden" id="height" name="height" value="170">
                            </div>
                            <!-- 体重滑动条 -->
                            <div>
                                <label class="block text-xs font-medium text-gray-700 mb-1">体重</label>
                                <div class="slider-container">
                                    <span id="weight-value" class="slider-value">60kg</span>
                                    <input type="range" id="weight-slider" min="40" max="120" value="60" class="w-full" oninput="updateSliderValue(this, 'weight-value', 'kg')">
                                    <div class="flex justify-between text-xs text-gray-500 mt-1">
                                        <span>40kg</span>
                                        <span>120kg</span>
                                    </div>
                                </div>
                                <input type="hidden" id="weight" name="weight" value="60">
                            </div>
                            <!-- 体型 -->
                            <div>
                                <label class="block text-xs font-medium text-gray-700 mb-1">体型</label>
                                <div class="grid grid-cols-3 gap-2">
                                    <button type="button" class="btn-option glass-button rounded-lg text-xs px-3 py-2 text-center" data-name="bodyType" data-value="slim">
                                        偏瘦
                                    </button>
                                    <button type="button" class="btn-option glass-button rounded-lg text-xs px-3 py-2 text-center" data-name="bodyType" data-value="normal">
                                        标准
                                    </button>
                                    <button type="button" class="btn-option glass-button rounded-lg text-xs px-3 py-2 text-center" data-name="bodyType" data-value="chubby">
                                        偏胖
                                    </button>
                                </div>
                                <input type="hidden" name="bodyType" id="bodyType">
                            </div>
                            <!-- 肤色 -->
                            <div>
                                <label class="block text-xs font-medium text-gray-700 mb-1">肤色</label>
                                <div class="grid grid-cols-3 gap-2">
                                    <button type="button" class="btn-option glass-button rounded-lg text-xs px-3 py-2 text-center" data-name="skinTone" data-value="fair">
                                        白皙
                                    </button>
                                    <button type="button" class="btn-option glass-button rounded-lg text-xs px-3 py-2 text-center" data-name="skinTone" data-value="medium">
                                        自然
                                    </button>
                                    <button type="button" class="btn-option glass-button rounded-lg text-xs px-3 py-2 text-center" data-name="skinTone" data-value="dark">
                                        小麦色
                                    </button>
                                </div>
                                <input type="hidden" name="skinTone" id="skinTone">
                            </div>
                        </div>
                    </div>
                    <!-- 风格偏好区域 -->
                    <div class="section">
                        <h3 class="font-medium text-gray-800 mb-4">风格偏好</h3>
                        <div>
                            <label class="block text-xs font-medium text-gray-700 mb-1">喜欢的穿搭风格（可多选）</label>
                            <div class="grid grid-cols-3 gap-2">
                                <button type="button" class="btn-option glass-button rounded-lg text-xs px-3 py-2 text-center" data-name="stylePreferences" data-value="casual" data-multiple="true">
                                    休闲
                                </button>
                                <button type="button" class="btn-option glass-button rounded-lg text-xs px-3 py-2 text-center" data-name="stylePreferences" data-value="formal" data-multiple="true">
                                    正式
                                </button>
                                <button type="button" class="btn-option glass-button rounded-lg text-xs px-3 py-2 text-center" data-name="stylePreferences" data-value="sports" data-multiple="true">
                                    运动
                                </button>
                                <button type="button" class="btn-option glass-button rounded-lg text-xs px-3 py-2 text-center" data-name="stylePreferences" data-value="elegant" data-multiple="true">
                                    优雅
                                </button>
                                <button type="button" class="btn-option glass-button rounded-lg text-xs px-3 py-2 text-center" data-name="stylePreferences" data-value="street" data-multiple="true">
                                    街头
                                </button>
                                <button type="button" class="btn-option glass-button rounded-lg text-xs px-3 py-2 text-center" data-name="stylePreferences" data-value="vintage" data-multiple="true">
                                    复古
                                </button>
                            </div>
                            <input type="hidden" name="stylePreferences" id="stylePreferences">
                        </div>
                    </div>
                    <div class="flex justify-between items-center mt-auto pt-4 border-t border-gray-200">
                        <button type="button" id="back-to-step-1" class="glass-button rounded-full text-xs px-4 py-2 hover:bg-white/60 transition-colors">返回</button>
                        <button type="button" id="next-to-step-3" class="glass-button rounded-md text-xs px-6 py-2 hover:bg-white/60 transition-colors">下一步</button>
                    </div>
                </div>
                <!-- 步骤3: 能量信息 -->
                <div id="step-3" class="space-y-6 hidden step-content">
                    <div class="text-center pb-3 border-b border-gray-200">
                        <p class="text-xs text-gray-600">填写能量信息，获得更精准的五行穿搭建议（选填）</p>
                    </div>
                    <!-- 能量信息区域 -->
                    <div class="section">
                        <h3 class="font-medium text-gray-800 mb-4">能量信息</h3>
                        <div class="space-y-4">
                            <div>
                                <label for="fullName" class="block text-xs font-medium text-gray-700 mb-1">姓名</label>
                                <input type="text" id="fullName" name="fullName" class="form-input w-full rounded-lg" placeholder="请输入您的姓名">
                            </div>
                            <div>
                                <label class="block text-xs font-medium text-gray-700 mb-1">性别</label>
                                <div class="grid grid-cols-2 gap-2">
                                    <button type="button" class="btn-option glass-button rounded-lg text-xs px-3 py-2 text-center" data-name="gender" data-value="male">男</button>
                                    <button type="button" class="btn-option glass-button rounded-lg text-xs px-3 py-2 text-center" data-name="gender" data-value="female">女</button>
                                </div>
                                <input type="hidden" name="gender" id="gender">
                            </div>
                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <label for="birthDate" class="block text-xs font-medium text-gray-700 mb-1">出生日期</label>
                                    <input type="date" id="birthDate" name="birthDate" class="form-input w-full rounded-lg">
                                </div>
                                <div>
                                    <label for="birthTime" class="block text-xs font-medium text-gray-700 mb-1">出生时间</label>
                                    <input type="time" id="birthTime" name="birthTime" class="form-input w-full rounded-lg">
                                </div>
                            </div>
                            <div>
                                <label for="birthPlace" class="block text-xs font-medium text-gray-700 mb-1">出生地点</label>
                                <input type="text" id="birthPlace" name="birthPlace" class="form-input w-full rounded-lg" placeholder="请输入出生城市">
                            </div>
                        </div>
                    </div>
                    <div class="flex justify-between items-center mt-auto pt-4 border-t border-gray-200">
                        <button type="button" id="back-to-step-2" class="glass-button rounded-full text-xs px-4 py-2 hover:bg-white/60 transition-colors">返回</button>
                        <button type="submit" class="glass-button rounded-md text-xs px-6 py-2 hover:bg-white/60 transition-colors">完成</button>
                    </div>
                </div>
            </form>
        </main>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 变量定义
            const steps = ['step-1', 'step-2', 'step-3'];
            const progressSteps = document.querySelectorAll('.progress-step');
            let currentStep = 0;
            let photoFile = null;
            // 步骤主标题映射
            const stepTitles = [
                '上传全身照',
                '基础信息',
                '能量信息'
            ];
            // 初始化header主标题
            document.getElementById('dynamic-header-title').textContent = stepTitles[0];
            
            // 全身照上传处理
            const photoUpload = document.getElementById('photo-upload');
            const fullBodyPhoto = document.getElementById('full-body-photo');
            const uploadPlaceholder = document.getElementById('upload-placeholder');
            
            fullBodyPhoto.addEventListener('click', function() {
                photoUpload.click();
            });
            
            photoUpload.addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (file) {
                    photoFile = file;
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        fullBodyPhoto.style.backgroundImage = `url(${e.target.result})`;
                        uploadPlaceholder.classList.add('hidden');
                        
                        // 更新预览
                        document.getElementById('preview-photo').style.backgroundImage = `url(${e.target.result})`;
                    }
                    reader.readAsDataURL(file);
                }
            });
            
            // 滑动条值更新
            window.updateSliderValue = function(slider, valueId, unit) {
                const value = slider.value;
                document.getElementById(valueId).textContent = value + unit;
                
                // 更新隐藏输入值
                const fieldId = slider.id.replace('-slider', '');
                document.getElementById(fieldId).value = value;
                
                // 更新滑块值位置
                const percent = ((value - slider.min) / (slider.max - slider.min)) * 100;
                const valueElem = document.getElementById(valueId);
                
                // 限制在滑块范围内
                let leftPos = percent;
                if (leftPos < 5) leftPos = 5;
                if (leftPos > 95) leftPos = 95;
                
                valueElem.style.left = leftPos + '%';
            };
            
            // 初始化滑动条值
            updateSliderValue(document.getElementById('height-slider'), 'height-value', 'cm');
            updateSliderValue(document.getElementById('weight-slider'), 'weight-value', 'kg');
            
            // 按钮选择处理
            const optionButtons = document.querySelectorAll('.btn-option');
            optionButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const name = this.dataset.name;
                    const value = this.dataset.value;
                    const isMultiple = this.dataset.multiple === 'true';
                    const hiddenInput = document.getElementById(name);
                    
                    if (isMultiple) {
                        // 多选处理
                        this.classList.toggle('selected');
                        
                        // 更新隐藏输入值
                        const selectedButtons = Array.from(document.querySelectorAll(`.btn-option[data-name="${name}"][data-multiple="true"].selected`));
                        const values = selectedButtons.map(b => b.dataset.value);
                        hiddenInput.value = values.join(',');
                    } else {
                        // 单选处理
                        document.querySelectorAll(`.btn-option[data-name="${name}"]:not([data-multiple="true"])`).forEach(b => {
                            b.classList.remove('selected');
                        });
                        this.classList.add('selected');
                        hiddenInput.value = value;
                    }
                });
            });
            
            // 步骤导航处理
            function showStep(stepIndex) {
                // 隐藏所有步骤
                steps.forEach(stepId => {
                    document.getElementById(stepId).classList.add('hidden');
                });
                
                // 显示当前步骤
                document.getElementById(steps[stepIndex]).classList.remove('hidden');
                
                // 更新进度指示器
                progressSteps.forEach((step, index) => {
                    step.classList.remove('active', 'completed');
                    if (index === stepIndex) {
                        step.classList.add('active');
                    } else if (index < stepIndex) {
                        step.classList.add('completed');
                    }
                });
                
                // 同步header主标题
                document.getElementById('dynamic-header-title').textContent = stepTitles[stepIndex];
                currentStep = stepIndex;
            }
            
            // 步骤1到步骤2
            document.getElementById('next-to-step-2').addEventListener('click', function() {
                if (!photoFile) {
                    alert('请上传全身照');
                    return;
                }
                showStep(1);
            });
            
            // 步骤2到步骤1
            document.getElementById('back-to-step-1').addEventListener('click', function() {
                showStep(0);
            });
            
            // 步骤2到步骤3
            document.getElementById('next-to-step-3').addEventListener('click', function() {
                // 验证必填信息
                const bodyType = document.getElementById('bodyType').value;
                const skinTone = document.getElementById('skinTone').value;
                
                if (!bodyType || !skinTone) {
                    alert('请填写必填信息：性别、体型和肤色');
                    return;
                }
                
                showStep(2);
            });
            
            // 步骤3到步骤2
            document.getElementById('back-to-step-2').addEventListener('click', function() {
                showStep(1);
            });
            
            // 表单提交处理
            document.getElementById('user-info-form').addEventListener('submit', function(e) {
                e.preventDefault();
                
                // 获取所有数据
                const formData = new FormData(this);
                const userData = Object.fromEntries(formData.entries());
                
                // 添加照片信息
                if (photoFile) {
                    userData.photoUrl = URL.createObjectURL(photoFile);
                }
                
                // 确定模式(自然模式或能量模式)
                userData.mode = (userData.fullName && userData.birthDate && userData.birthPlace) ? 'energy' : 'natural';
                
                // 保存数据
                localStorage.setItem('userData', JSON.stringify(userData));
                
                // 跳转到首页
                window.location.href = './home.html';
            });

            // 图片选择后显示基础体型模块
            document.getElementById('photo-upload').addEventListener('change', function() {
                document.getElementById('basic-body-data-section').classList.remove('hidden');
                setTimeout(() => {
                    initializeDropdowns();
                }, 0);
            });

            // === 迁移基础体型模块下拉交互 ===
            const selectorData = {
                waistShape: { options: ['直筒','略有曲线','有曲线','曲线较明显','曲线明显'] },
                belly: { options: ['没有','略有小肚腩','小肚腩','偏大肚腩','大肚腩'] },
                hip: { options: ['下榻','略有上翘','正常','较上翘','上翘'] },
                hipShape: { options: ['直筒','略有曲线','有曲线','曲线较明显','曲线明显'] },
                shoulderWidth: { options: ['窄','偏窄','正常','偏宽','宽'] },
                armLength: { options: ['短','偏短','正常','偏长','长'] },
                armCircum: { options: ['细','偏细','正常','偏粗','粗'] },
                hipWidth: { options: ['窄','偏窄','正常','偏宽','宽'] },
                thigh: { options: ['细','偏细','正常','偏粗','粗'] },
                calf: { options: ['细','偏细','正常','偏粗','粗'] },
                bodyFat: { options: ['上身粗','偏上身粗','匀称','偏下身粗','下身粗'] },
                bodyLength: { options: ['上身长','偏上身长','匀称','偏下身长','下身长'] }
            };
            let activeWrapper = null;
            function initializeDropdowns() {
                let selectorButtons = document.querySelectorAll('.selector-button');
                selectorButtons.forEach((button, idx) => {
                    // 先移除所有旧事件：用cloneNode替换
                    const newButton = button.cloneNode(true);
                    button.parentNode.replaceChild(newButton, button);
                    // 重新获取新节点
                    selectorButtons[idx] = newButton;
                });
                // 重新获取所有新button节点
                selectorButtons = document.querySelectorAll('.selector-button');
                selectorButtons.forEach(button => {
                    const type = button.getAttribute('data-type');
                    const wrapper = button.closest('.selector-wrapper');
                    const dropdown = wrapper ? wrapper.querySelector('.dropdown-menu') : null;
                    if (!type || !selectorData[type]) {
                        return;
                    }
                    if (!dropdown) {
                        return;
                    }
                    // 清空旧选项
                    dropdown.innerHTML = '';
                    const options = selectorData[type].options;
                    if (!Array.isArray(options) || options.length === 0) {
                        return;
                    }
                    options.forEach(option => {
                        const item = document.createElement('div');
                        item.className = 'dropdown-item';
                        item.textContent = option;
                        if (option === button.querySelector('span').textContent) {
                            item.classList.add('selected');
                        }
                        item.addEventListener('click', (e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            button.querySelector('span').textContent = option;
                            dropdown.querySelectorAll('.dropdown-item').forEach(item => { item.classList.remove('selected'); });
                            item.classList.add('selected');
                            dropdown.classList.remove('show');
                            wrapper.classList.remove('active');
                            activeWrapper = null;
                            updateBodyTypeAnalysis();
                        });
                        dropdown.appendChild(item);
                    });
                    button.addEventListener('click', (e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        if (activeWrapper && activeWrapper !== wrapper) {
                            activeWrapper.querySelector('.dropdown-menu').classList.remove('show');
                            activeWrapper.classList.remove('active');
                        }
                        const isShowing = dropdown.classList.toggle('show');
                        wrapper.classList.toggle('active');
                        activeWrapper = isShowing ? wrapper : null;
                    });
                });
            }
            document.addEventListener('click', function() {
                if (activeWrapper) {
                    activeWrapper.querySelector('.dropdown-menu').classList.remove('show');
                    activeWrapper.classList.remove('active');
                    activeWrapper = null;
                }
            });
            function updateBodyTypeAnalysis() {
                const waistShape = document.querySelector('[data-type="waistShape"] span').textContent;
                document.querySelector('#bodyTypeTag').textContent = `${waistShape}体型`;
            }
            initializeDropdowns();
            // === END 迁移 ===

            // 折叠/展开逻辑
            const toggleBtn = document.getElementById('toggle-body-data-extra');
            const extraItems = document.querySelector('.body-data-extra-items');
            let expanded = false;
            if (toggleBtn && extraItems) {
                toggleBtn.addEventListener('click', function() {
                    expanded = !expanded;
                    if (expanded) {
                        extraItems.style.display = '';
                        toggleBtn.innerHTML = '收起 <i class="fas fa-chevron-up ml-1 text-gray-400" style="font-size:10px;"></i>';
                        initializeDropdowns();
                    } else {
                        extraItems.style.display = 'none';
                        toggleBtn.innerHTML = '更多 <i class="fas fa-chevron-down ml-1 text-gray-400" style="font-size:10px;"></i>';
                    }
                });
            }
        });
    </script>
</body>
</html> 