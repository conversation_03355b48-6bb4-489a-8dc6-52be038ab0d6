# 穿搭推荐小程序数据库设计方案（优化版）

## 一、整体设计策略

### 1.1 数据库选型

基于系统需求分析和性能考虑，采用最优化的数据库组合:

| 数据库 | 用途 | 优势 |
|-------|------|------|
| **MongoDB** | 核心业务数据存储 | 文档模型灵活、原生JSON支持、水平扩展能力强、支持向量搜索 |
| **Redis** | 缓存与临时数据 | 高性能、多数据结构支持、适合缓存场景 |

这种组合适合我们的微服务架构，特别是整合后的推荐服务（包含原五行命理和天气功能）需要高度灵活的数据模型和快速的数据访问能力。

### 1.2 数据分层策略

采用热/冷数据分层存储策略，提高系统性能和降低存储成本:

1. **热数据层 (Redis)**
   - 用户会话信息
   - 推荐结果缓存
   - 五行命理计算结果
   - 天气数据缓存
   - 高频访问的用户数据
   - 分布式锁和计数器

2. **核心数据层 (MongoDB)**
   - 用户基础信息
   - 衣物与饰品数据
   - 搭配组合数据
   - 五行属性映射
   - 天气与穿衣指数数据
   - AI模型分析结果

3. **冷数据层 (MongoDB 时间分区)**
   - 历史穿搭记录
   - 历史天气数据
   - 用户行为日志
   - 系统操作日志

### 1.3 Spring Data MongoDB配置

对于Java + Spring Boot技术栈，我们使用Spring Data MongoDB来简化数据操作：

```java
@Configuration
@EnableMongoRepositories(basePackages = "com.stylishlink.repository")
public class MongoConfig extends AbstractMongoClientConfiguration {
    
    @Value("${spring.data.mongodb.uri}")
    private String mongoUri;
    
    @Value("${spring.data.mongodb.database}")
    private String database;
    
    @Override
    protected String getDatabaseName() {
        return database;
    }
    
    @Override
    public MongoClient mongoClient() {
        return MongoClients.create(mongoUri);
    }
    
    @Bean
    public MongoTemplate mongoTemplate() throws Exception {
        return new MongoTemplate(mongoClient(), getDatabaseName());
    }
    
    // 自定义转换器，处理Date、ObjectId等类型转换
    @Bean
    public MappingMongoConverter mappingMongoConverter(MongoDatabaseFactory databaseFactory, 
                                                     MongoMappingContext mappingContext,
                                                     BeanFactory beanFactory) {
        DbRefResolver dbRefResolver = new DefaultDbRefResolver(databaseFactory);
        MappingMongoConverter converter = new MappingMongoConverter(dbRefResolver, mappingContext);
        converter.setCustomConversions(beanFactory.getBean(MongoCustomConversions.class));
        // 去除_class字段
        converter.setTypeMapper(new DefaultMongoTypeMapper(null));
        return converter;
    }
}
```

## 二、数据模型设计

### 2.1 用户相关模型

#### User 集合

MongoDB文档结构：
```json
{
  "_id": ObjectId("用户唯一ID"),
  "userId": "唯一用户标识",
  "openId": "微信OpenID",
  "unionId": "微信UnionID",
  "nickname": "用户昵称",
  "avatar": "头像URL",
  "gender": 1,  // 1-男 2-女 0-未知
  "phone": "手机号",
  "email": "邮箱",
  "registerDate": ISODate("2023-01-01T00:00:00Z"),
  "lastLoginDate": ISODate("2023-01-01T00:00:00Z"),
  "preferences": {
    "favoriteColors": ["红色", "蓝色"],
    "favoriteStyles": ["休闲", "正式"],
    "dislikedItems": ["ObjectId1", "ObjectId2"],
    "seasonalPreferences": {
      "spring": ["清新", "明亮"],
      "summer": ["轻薄", "凉爽"],
      "autumn": ["层次", "温暖"],
      "winter": ["保暖", "厚重"]
    },
    "occasionPreferences": {
      "work": ["正式", "专业"],
      "casual": ["舒适", "休闲"],
      "party": ["时尚", "亮眼"]
    }
  },
  "bodyInfo": {
    "height": 170,
    "weight": 60,
    "bodyShape": "梨形", // 身形类型
    "skinTone": "暖色调", // 肤色
    "bodyProportions": {
      "shoulder": 42,
      "chest": 90,
      "waist": 70,
      "hip": 90
    }
  },
  "wuxingProfile": {
    "birthDate": ISODate("1990-01-01T08:00:00Z"),
    "lunarBirth": {
      "year": "庚午",
      "month": "戊子",
      "day": "己巳",
      "hour": "壬申"
    },
    "elementAnalysis": {
      "wood": 3,  // 木
      "fire": 1,  // 火
      "earth": 0, // 土
      "metal": 2, // 金
      "water": 2  // 水
    },
    "favorable": ["木", "水"],
    "unfavorable": ["火"],
    "eightChar": "庚午 戊子 己巳 壬申",
    "updated": ISODate("2023-01-01T00:00:00Z")
  },
  "settings": {
    "pushNotification": true,
    "dailyRecommendation": true,
    "privacyLevel": 2, // 1-公开 2-好友可见 3-私密
    "language": "zh_CN",
    "recommendationPriority": {
      "weather": 0.7,
      "wuxing": 0.3,
      "style": 0.5,
      "occasion": 0.8
    }
  },
  "stats": {
    "outfitCount": 23,
    "clothingCount": 45,
    "accessoryCount": 12,
    "favoriteCount": 5,
    "avgRating": 4.2
  },
  "extensions": {}, // 扩展字段，用于存储未来可能添加的用户属性
  "createdAt": ISODate("2023-01-01T00:00:00Z"),
  "updatedAt": ISODate("2023-01-01T00:00:00Z")
}
```

对应的Java实体类：
```java
@Document(collection = "users")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class User {
    @Id
    private String id;
    private String userId;
    private String openId;
    private String unionId;
    private String nickname;
    private String avatar;
    private Integer gender;
    private String phone;
    private String email;
    private Date registerDate;
    private Date lastLoginDate;
    
    @Field("preferences")
    private UserPreferences preferences;
    
    @Field("bodyInfo")
    private BodyInfo bodyInfo;
    
    @Field("wuxingProfile")
    private WuxingProfile wuxingProfile;
    
    @Field("settings")
    private UserSettings settings;
    
    @Field("stats")
    private UserStats stats;
    
    @Field("extensions")
    private Map<String, Object> extensions;
    
    private Date createdAt;
    private Date updatedAt;
}

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserPreferences {
    private List<String> favoriteColors;
    private List<String> favoriteStyles;
    private List<String> dislikedItems;
    private Map<String, List<String>> seasonalPreferences;
    private Map<String, List<String>> occasionPreferences;
}

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BodyInfo {
    private Integer height;
    private Integer weight;
    private String bodyShape;
    private String skinTone;
    private Map<String, Integer> bodyProportions;
}

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WuxingProfile {
    private Date birthDate;
    private Map<String, String> lunarBirth;
    private Map<String, Integer> elementAnalysis;
    private List<String> favorable;
    private List<String> unfavorable;
    private String eightChar;
    private Date updated;
}

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserSettings {
    private Boolean pushNotification;
    private Boolean dailyRecommendation;
    private Integer privacyLevel;
    private String language;
    private Map<String, Double> recommendationPriority;
}

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserStats {
    private Integer outfitCount;
    private Integer clothingCount;
    private Integer accessoryCount;
    private Integer favoriteCount;
    private Double avgRating;
}
```

#### UserToken 集合

MongoDB文档结构：
```json
{
  "_id": ObjectId("令牌唯一ID"),
  "userId": "用户ID",
  "token": "JWT令牌",
  "refreshToken": "刷新令牌",
  "deviceInfo": {
    "deviceId": "设备唯一标识",
    "deviceType": "iOS/Android/Web",
    "deviceModel": "设备型号",
    "osVersion": "操作系统版本"
  },
  "ipAddress": "登录IP",
  "lastActivity": ISODate("2023-01-01T00:00:00Z"),
  "expiresAt": ISODate("2023-01-01T00:00:00Z"),
  "createdAt": ISODate("2023-01-01T00:00:00Z")
}
```

对应的Java实体类：
```java
@Document(collection = "user_tokens")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserToken {
    @Id
    private String id;
    private String userId;
    private String token;
    private String refreshToken;
    
    @Field("deviceInfo")
    private DeviceInfo deviceInfo;
    
    private String ipAddress;
    private Date lastActivity;
    private Date expiresAt;
    private Date createdAt;
}

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeviceInfo {
    private String deviceId;
    private String deviceType;
    private String deviceModel;
    private String osVersion;
}
```

### 2.2 衣物与饰品模型

#### Clothing 集合

MongoDB文档结构：
```json
{
  "_id": ObjectId("衣物唯一ID"),
  "userId": "所属用户ID",
  "name": "衣物名称",
  "category": "上衣", // 上衣、下装、外套、鞋子等
  "subCategory": "T恤", // 具体类别，如T恤、衬衫、牛仔裤等
  "images": [
    {
      "url": "图片URL",
      "type": "main", // main-主图 detail-细节图 model-模特图
      "backgroundRemoved": true, // 是否已去除背景
      "vectorId": "向量ID" // 图像向量ID，用于相似检索
    }
  ],
  "colors": [
    {
      "name": "红色",
      "colorCode": "#FF0000",
      "percentage": 80 // 该颜色在衣物中的占比
    }
  ],
  "attributes": {
    "material": ["棉", "聚酯纤维"],
    "pattern": "纯色", // 纯色、条纹、格子等
    "style": ["休闲", "运动"],
    "season": ["春", "秋"],
    "occasion": ["日常", "运动"],
    "fit": "宽松", // 宽松、紧身、标准等
    "neckline": "圆领", // 圆领、V领、立领等
    "sleeve": "长袖" // 长袖、短袖、无袖等
  },
  "brand": "优衣库",
  "size": "M",
  "purchaseInfo": {
    "date": ISODate("2022-06-15T00:00:00Z"),
    "price": 99.00,
    "store": "线上",
    "link": "购买链接"
  },
  "wuxingAttributes": {
    "primaryElement": "火", // 主五行属性
    "secondaryElement": "土", // 次五行属性
    "elementScores": {
      "wood": 0.2, // 木
      "fire": 0.7, // 火
      "earth": 0.5, // 土
      "metal": 0.1, // 金
      "water": 0.0  // 水
    },
    "analysis": "红色属火，搭配黄色系列的配饰可增强火土相生效果"
  },
  "weatherSuitability": {
    "temperature": {
      "min": 10, // 适合的最低温度
      "max": 25  // 适合的最高温度
    },
    "humidity": {
      "min": 30,
      "max": 70
    },
    "rainproof": false, // 是否防雨
    "windproof": true,  // 是否防风
    "uvProtection": false // 是否防紫外线
  },
  "usage": {
    "wearCount": 12, // 穿着次数
    "lastWorn": ISODate("2023-01-01T00:00:00Z"),
    "favoriteOutfits": ["搭配ID1", "搭配ID2"] // 常用搭配
  },
  "tags": ["舒适", "百搭", "最爱"],
  "extensions": {}, // 扩展字段
  "createdAt": ISODate("2023-01-01T00:00:00Z"),
  "updatedAt": ISODate("2023-01-01T00:00:00Z")
}
```

对应的Java实体类：
```java
@Document(collection = "clothing")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Clothing {
    @Id
    private String id;
    private String userId;
    private String name;
    private String category;
    private String subCategory;
    private List<ClothingImage> images;
    private List<ClothingColor> colors;
    
    @Field("attributes")
    private ClothingAttributes attributes;
    
    private String brand;
    private String size;
    
    @Field("purchaseInfo")
    private PurchaseInfo purchaseInfo;
    
    @Field("wuxingAttributes")
    private WuxingAttributes wuxingAttributes;
    
    @Field("weatherSuitability")
    private WeatherSuitability weatherSuitability;
    
    @Field("usage")
    private ClothingUsage usage;
    
    private List<String> tags;
    private Map<String, Object> extensions;
    private Date createdAt;
    private Date updatedAt;
}

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ClothingImage {
    private String url;
    private String type;
    private Boolean backgroundRemoved;
    private String vectorId;
}

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ClothingColor {
    private String name;
    private String colorCode;
    private Integer percentage;
}

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ClothingAttributes {
    private List<String> material;
    private String pattern;
    private List<String> style;
    private List<String> season;
    private List<String> occasion;
    private String fit;
    private String neckline;
    private String sleeve;
}

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PurchaseInfo {
    private Date date;
    private Double price;
    private String store;
    private String link;
}

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WuxingAttributes {
    private String primaryElement;
    private String secondaryElement;
    private Map<String, Double> elementScores;
    private String analysis;
}

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WeatherSuitability {
    private TemperatureRange temperature;
    private HumidityRange humidity;
    private Boolean rainproof;
    private Boolean windproof;
    private Boolean uvProtection;
}

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TemperatureRange {
    private Integer min;
    private Integer max;
}

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HumidityRange {
    private Integer min;
    private Integer max;
}

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ClothingUsage {
    private Integer wearCount;
    private Date lastWorn;
    private List<String> favoriteOutfits;
}
```

#### Accessory 集合

// ... 以下省略原有MongoDB集合数据结构 ...
// 下面添加Spring Data MongoDB相关Repository实现

## 三、数据访问层设计

### 3.1 Spring Data MongoDB Repository

使用Spring Data MongoDB的Repository接口简化数据访问：

```java
@Repository
public interface UserRepository extends MongoRepository<User, String> {
    Optional<User> findByUserId(String userId);
    Optional<User> findByOpenId(String openId);
    List<User> findByGender(Integer gender);
}

@Repository
public interface ClothingRepository extends MongoRepository<Clothing, String> {
    List<Clothing> findByUserId(String userId);
    List<Clothing> findByUserIdAndCategory(String userId, String category);
    List<Clothing> findByUserIdAndTags(String userId, String tag);
    
    @Query("{'userId': ?0, 'attributes.season': ?1}")
    List<Clothing> findByUserIdAndSeason(String userId, String season);
    
    @Query("{'userId': ?0, 'wuxingAttributes.primaryElement': ?1}")
    List<Clothing> findByUserIdAndWuxingElement(String userId, String element);
    
    @Query("{'userId': ?0, 'weatherSuitability.temperature.min': {$lte: ?1}, 'weatherSuitability.temperature.max': {$gte: ?1}}")
    List<Clothing> findByUserIdAndSuitableTemperature(String userId, Integer temperature);
}

@Repository
public interface OutfitRepository extends MongoRepository<Outfit, String> {
    List<Outfit> findByUserId(String userId);
    List<Outfit> findByUserIdAndOccasion(String userId, String occasion);
    
    @Query("{'userId': ?0, 'season': ?1}")
    List<Outfit> findByUserIdAndSeason(String userId, String season);
    
    @Query("{'userId': ?0, 'items.clothingId': ?1}")
    List<Outfit> findByUserIdAndContainsClothing(String userId, String clothingId);
}
```

### 3.2 自定义数据访问方法

对于复杂查询，使用MongoTemplate：

```java
@Repository
public class CustomClothingRepositoryImpl implements CustomClothingRepository {
    
    private final MongoTemplate mongoTemplate;
    
    public CustomClothingRepositoryImpl(MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }
    
    @Override
    public List<Clothing> findByMultipleCriteria(ClothingSearchCriteria criteria) {
        Query query = new Query();
        query.addCriteria(Criteria.where("userId").is(criteria.getUserId()));
        
        if (criteria.getCategory() != null) {
            query.addCriteria(Criteria.where("category").is(criteria.getCategory()));
        }
        
        if (criteria.getColors() != null && !criteria.getColors().isEmpty()) {
            query.addCriteria(Criteria.where("colors.name").in(criteria.getColors()));
        }
        
        if (criteria.getSeasons() != null && !criteria.getSeasons().isEmpty()) {
            query.addCriteria(Criteria.where("attributes.season").in(criteria.getSeasons()));
        }
        
        if (criteria.getOccasions() != null && !criteria.getOccasions().isEmpty()) {
            query.addCriteria(Criteria.where("attributes.occasion").in(criteria.getOccasions()));
        }
        
        if (criteria.getWuxingElements() != null && !criteria.getWuxingElements().isEmpty()) {
            query.addCriteria(Criteria.where("wuxingAttributes.primaryElement").in(criteria.getWuxingElements()));
        }
        
        if (criteria.getTemperature() != null) {
            query.addCriteria(Criteria.where("weatherSuitability.temperature.min").lte(criteria.getTemperature()));
            query.addCriteria(Criteria.where("weatherSuitability.temperature.max").gte(criteria.getTemperature()));
        }
        
        return mongoTemplate.find(query, Clothing.class);
    }
    
    @Override
    public List<ClothingWithScore> findRecommendedClothing(RecommendationCriteria criteria) {
        // 复杂推荐逻辑实现
        // ...
    }
}
```

### 3.3 MongoDB索引设计

为了优化查询性能，我们在各个集合上设置了以下索引：

```java
@Configuration
public class MongoIndexConfig {
    
    private final MongoTemplate mongoTemplate;
    
    public MongoIndexConfig(MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }
    
    @PostConstruct
    public void initIndexes() {
        // User 集合索引
        mongoTemplate.indexOps(User.class).ensureIndex(
            new Index().on("userId", Sort.Direction.ASC).unique(true));
        mongoTemplate.indexOps(User.class).ensureIndex(
            new Index().on("openId", Sort.Direction.ASC).unique(true));
        
        // Clothing 集合索引
        mongoTemplate.indexOps(Clothing.class).ensureIndex(
            new Index().on("userId", Sort.Direction.ASC));
        mongoTemplate.indexOps(Clothing.class).ensureIndex(
            new Index().on("category", Sort.Direction.ASC));
        mongoTemplate.indexOps(Clothing.class).ensureIndex(
            new Index().on("userId", Sort.Direction.ASC)
                    .on("category", Sort.Direction.ASC));
        mongoTemplate.indexOps(Clothing.class).ensureIndex(
            new Index().on("userId", Sort.Direction.ASC)
                    .on("attributes.season", Sort.Direction.ASC));
        mongoTemplate.indexOps(Clothing.class).ensureIndex(
            new Index().on("userId", Sort.Direction.ASC)
                    .on("wuxingAttributes.primaryElement", Sort.Direction.ASC));
        
        // Outfit 集合索引
        mongoTemplate.indexOps(Outfit.class).ensureIndex(
            new Index().on("userId", Sort.Direction.ASC));
        mongoTemplate.indexOps(Outfit.class).ensureIndex(
            new Index().on("userId", Sort.Direction.ASC)
                    .on("occasion", Sort.Direction.ASC));
        mongoTemplate.indexOps(Outfit.class).ensureIndex(
            new Index().on("userId", Sort.Direction.ASC)
                    .on("season", Sort.Direction.ASC));
    }
}
```

## 四、缓存策略实现

### 4.1 Spring Cache配置

使用Spring Cache简化缓存操作：

```java
@Configuration
@EnableCaching
public class CacheConfig {
    
    @Bean
    public CacheManager cacheManager(RedisConnectionFactory redisConnectionFactory) {
        RedisCacheConfiguration cacheConfiguration = RedisCacheConfiguration.defaultCacheConfig()
                .entryTtl(Duration.ofMinutes(30))
                .disableCachingNullValues()
                .serializeKeysWith(
                    RedisSerializationContext.SerializationPair.fromSerializer(new StringRedisSerializer()))
                .serializeValuesWith(
                    RedisSerializationContext.SerializationPair.fromSerializer(
                        new GenericJackson2JsonRedisSerializer()));
        
        // 为不同类型的数据配置不同的TTL
        Map<String, RedisCacheConfiguration> cacheConfigurations = new HashMap<>();
        cacheConfigurations.put("userProfile", 
                cacheConfiguration.entryTtl(Duration.ofHours(1)));
        cacheConfigurations.put("dailyRecommendation", 
                cacheConfiguration.entryTtl(Duration.ofHours(6)));
        cacheConfigurations.put("weatherInfo", 
                cacheConfiguration.entryTtl(Duration.ofMinutes(30)));
        cacheConfigurations.put("wuxingAnalysis", 
                cacheConfiguration.entryTtl(Duration.ofDays(7)));
        
        return RedisCacheManager.builder(redisConnectionFactory)
                .cacheDefaults(cacheConfiguration)
                .withInitialCacheConfigurations(cacheConfigurations)
                .build();
    }
}
```

### 4.2 缓存使用示例

在Service层使用缓存注解：

```java
@Service
@Slf4j
public class UserServiceImpl implements UserService {
    
    private final UserRepository userRepository;
    
    public UserServiceImpl(UserRepository userRepository) {
        this.userRepository = userRepository;
    }
    
    @Override
    @Cacheable(value = "userProfile", key = "#userId")
    public UserProfile getUserProfile(GetUserRequest request) {
        String userId = request.getUserId();
        log.info("Fetching user profile from database for userId: {}", userId);
        
        User user = userRepository.findByUserId(userId)
                .orElseThrow(() -> new EntityNotFoundException("User not found: " + userId));
        
        return mapToUserProfile(user);
    }
    
    @Override
    @CachePut(value = "userProfile", key = "#request.userId")
    public UserProfile updateUserProfile(UpdateProfileRequest request) {
        String userId = request.getUserId();
        User user = userRepository.findByUserId(userId)
                .orElseThrow(() -> new EntityNotFoundException("User not found: " + userId));
        
        // Update user properties
        updateUserFromRequest(user, request);
        User savedUser = userRepository.save(user);
        
        return mapToUserProfile(savedUser);
    }
    
    @Override
    @CacheEvict(value = "userProfile", key = "#userId")
    public void refreshUserCache(String userId) {
        log.info("Evicting user profile cache for userId: {}", userId);
    }
    
    private UserProfile mapToUserProfile(User user) {
        // Mapping logic
        // ...
    }
    
    private void updateUserFromRequest(User user, UpdateProfileRequest request) {
        // Update logic
        // ...
    }
}
```

## 五、数据迁移与脚本

### 5.1 Flyway/Mongock数据迁移

使用Mongock实现MongoDB的数据迁移：

```java
@ChangeUnit(id = "user-init", order = "001")
public class UserChangeUnit {
    
    @Execution
    public void execution(MongoTemplate mongoTemplate) {
        // Create indexes
        mongoTemplate.indexOps("users").ensureIndex(
            new Index().on("userId", Sort.Direction.ASC).unique(true));
        mongoTemplate.indexOps("users").ensureIndex(
            new Index().on("openId", Sort.Direction.ASC).unique(true));
            
        // Add sample user for testing
        if (mongoTemplate.findOne(
                new Query(Criteria.where("userId").is("admin")), 
                User.class) == null) {
            User admin = User.builder()
                    .userId("admin")
                    .nickname("Admin")
                    .gender(0)
                    .registerDate(new Date())
                    .lastLoginDate(new Date())
                    .createdAt(new Date())
                    .updatedAt(new Date())
                    .build();
            mongoTemplate.save(admin);
        }
    }
    
    @RollbackExecution
    public void rollback(MongoTemplate mongoTemplate) {
        // Drop indexes
        mongoTemplate.indexOps("users").dropIndex("userId");
        mongoTemplate.indexOps("users").dropIndex("openId");
        
        // Remove test user
        mongoTemplate.remove(
            new Query(Criteria.where("userId").is("admin")), 
            User.class);
    }
}
```

// ... 其余内容保持不变 ... 