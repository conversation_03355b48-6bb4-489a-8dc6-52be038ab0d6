# StylishLink 系统架构流程图

本文档提供系统各部分的流程图，用于可视化理解系统架构和流程。

## 系统架构图

```mermaid
graph TD
    Client[小程序客户端] --> Gateway[API网关服务]
    Gateway --> UserService[用户服务]
    Gateway --> WardrobeService[衣橱服务]
    Gateway --> RecommendationService[推荐服务]
    
    UserService --> MongoDB[(MongoDB)]
    WardrobeService --> MongoDB
    RecommendationService --> MongoDB
    
    UserService --> Redis[(Redis缓存)]
    WardrobeService --> Redis
    RecommendationService --> Redis
    
    RecommendationService --> WeatherAPI[外部天气API]
    
    subgraph 用户服务模块
        UserService --> Auth[认证授权]
        UserService --> Profile[用户档案]
        UserService --> Settings[用户设置]
        UserService --> WuxingProfile[五行命理档案]
    end
    
    subgraph 衣橱服务模块
        WardrobeService --> ClothingMgmt[衣物管理]
        WardrobeService --> AccessoryMgmt[饰品管理]
        WardrobeService --> OutfitMgmt[搭配管理]
        WardrobeService --> ImageProcess[图像处理]
    end
    
    subgraph 推荐服务模块
        RecommendationService --> OutfitRecommend[搭配推荐]
        RecommendationService --> WuxingAnalysis[五行分析]
        RecommendationService --> WeatherProcessing[天气处理]
        RecommendationService --> PersonalPreference[个人偏好]
    end
```

## 用户认证流程

```mermaid
sequenceDiagram
    actor User as 用户
    participant Client as 小程序客户端
    participant Gateway as API网关
    participant UserService as 用户服务
    participant MongoDB as 数据库
    
    User->>Client: 输入登录信息
    Client->>Gateway: 发送登录请求
    Gateway->>UserService: 转发登录请求
    UserService->>MongoDB: 验证用户凭证
    MongoDB-->>UserService: 返回用户数据
    UserService->>UserService: 生成JWT令牌
    UserService-->>Gateway: 返回令牌和用户信息
    Gateway-->>Client: 返回认证结果
    Client->>Client: 存储令牌
    Client-->>User: 显示登录成功
```

## 衣物上传和识别流程

```mermaid
sequenceDiagram
    actor User as 用户
    participant Client as 小程序客户端
    participant WardrobeService as 衣橱服务
    participant AIService as AI服务
    participant DB as 数据库
    
    User->>Client: 拍摄/选择衣物图片
    Client->>WardrobeService: 上传图片
    WardrobeService->>AIService: 分析图片
    AIService->>AIService: 图像识别(类别、颜色等)
    AIService-->>WardrobeService: 返回分析结果
    WardrobeService-->>Client: 返回初步识别结果
    Client->>User: 显示结果并请求确认
    User->>Client: 确认或修改识别结果
    Client->>WardrobeService: 提交确认后的数据
    WardrobeService->>DB: 保存衣物信息
    DB-->>WardrobeService: 确认保存
    WardrobeService-->>Client: 返回保存成功响应
    Client-->>User: 显示添加成功
```

## 穿搭推荐流程

```mermaid
sequenceDiagram
    actor User as 用户
    participant Client as 小程序客户端
    participant RecService as 推荐服务
    participant UserService as 用户服务
    participant WardrobeService as 衣橱服务
    participant WeatherAPI as 天气API
    participant DB as 数据库
    
    User->>Client: 请求穿搭推荐
    Client->>RecService: 发送推荐请求
    
    par 并行获取数据
        RecService->>UserService: 获取用户五行档案
        UserService->>DB: 查询用户数据
        DB-->>UserService: 返回用户数据
        UserService-->>RecService: 返回五行档案
        
        RecService->>WardrobeService: 获取用户衣橱数据
        WardrobeService->>DB: 查询衣物和饰品
        DB-->>WardrobeService: 返回衣橱数据
        WardrobeService-->>RecService: 返回衣橱列表
        
        RecService->>WeatherAPI: 获取天气数据
        WeatherAPI-->>RecService: 返回天气信息
    end
    
    RecService->>RecService: 综合分析(五行+天气+偏好)
    RecService->>RecService: 生成推荐搭配
    RecService->>DB: 保存推荐记录
    RecService-->>Client: 返回推荐结果
    Client-->>User: 展示推荐搭配
```

## 数据模型关系

```mermaid
erDiagram
    USER ||--o{ CLOTHING : owns
    USER ||--o{ ACCESSORY : owns
    USER ||--o{ OUTFIT : creates
    USER ||--o{ RECOMMENDATION : receives
    
    CLOTHING ||--o{ OUTFIT_ITEM : includes
    ACCESSORY ||--o{ OUTFIT_ITEM : includes
    
    OUTFIT ||--|{ OUTFIT_ITEM : contains
    OUTFIT_ITEM }|--|| ITEM_TYPE : categorizes
    
    RECOMMENDATION ||--|{ OUTFIT : suggests
    
    USER {
        string id PK
        string userId
        string openId
        string nickname
        string avatar
        int gender
        string phone
        string email
        datetime registerDate
        datetime lastLoginDate
        object preferences
        object bodyInfo
        object wuxingProfile
        object settings
        object stats
        datetime createTime
        datetime updateTime
    }
    
    CLOTHING {
        string id PK
        string userId FK
        string name
        string category
        string subCategory
        array colors
        array tags
        array seasons
        array occasions
        map wuxingAttributes
        string mainImageUrl
        array imageUrls
        object weatherSuitability
        int favoriteLevel
        datetime createdAt
        datetime updatedAt
    }
    
    ACCESSORY {
        string id PK
        string userId FK
        string name
        string type
        string subType
        array colors
        array tags
        array seasons
        array occasions
        map wuxingAttributes
        string mainImageUrl
        array imageUrls
        int favoriteLevel
        datetime createdAt
        datetime updatedAt
    }
    
    OUTFIT {
        string id PK
        string userId FK
        string name
        string description
        array items
        string occasion
        array seasons
        array styles
        object weatherCondition
        map wuxingScores
        double totalScore
        datetime wearDate
        int wearCount
        int favoriteLevel
        datetime createdAt
        datetime updatedAt
    }
    
    RECOMMENDATION {
        string id PK
        string userId FK
        string type
        datetime date
        object weatherInfo
        array outfits
        map wuxingAnalysis
        object context
        int rating
        string feedback
        datetime createdAt
    }
``` 