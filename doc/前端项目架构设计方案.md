# StylishLink 微信小程序前端项目架构设计（uni-app + Vue3 + TS + unocss）

## 1. 技术选型

- **框架**：uni-app（Vue3 + Composition API + TypeScript）
- **原子化CSS**：unocss（含preset-weapp，支持微信小程序原生语法）
- **状态管理**：Pinia
- **路由管理**：uni-app内置路由
- **API服务层**：自定义api服务模块，TS类型推断
- **主题系统**：unocss动态变量+Pinia全局状态
- **测试**：Vitest/Jest（单元）、Cypress/uni-app内置（端到端）
- **CI/CD**：Gitee Go流水线/云开发流水线
- **项目管理**：Gitee仓库、Gitee代码评审、Gitee Issue
- **国际化**：vue-i18n（如有多语言需求）
- **无障碍**：aria标签、键盘可达性、色彩对比

---

## 2. 目录结构

```
/src
  /pages         # 页面级视图（一级/二级/三级页面，自动路由）
  /components    # 复用型UI组件（卡片、按钮、标签、下拉、弹窗、进度条等）
  /layouts       # 页面布局（主导航、底部导航、内容区、状态栏等）
  /store         # Pinia状态管理（用户、衣橱、搭配、灵感值、主题等）
  /services      # API服务层（RESTful/GraphQL、mock、类型定义）
  /utils         # 工具函数（格式化、校验、五行算法、动效等）
  /styles        # unocss配置、全局样式、主题变量、五行色彩
  /assets        # 静态资源（图片、icon、字体等）
  /constants     # 枚举、常量、配置项
  /hooks         # 复用型组合式函数（如useUser、useTheme、useResponsive等）
  /locales       # 国际化资源
  /tests         # 单元/集成/端到端测试
  /mocks         # 本地mock数据
  /config        # 构建/环境配置
```

---

## 3. 核心架构思想

- **组件化/模块化**：所有UI和业务功能均拆分为可复用组件，参数化支持主题/五行/响应式/动效。
- **原子化CSS**：所有样式用unocss原子类+shortcuts实现，主题/五行/毛玻璃/圆角/阴影/动效等全局统一。
- **主题系统**：Pinia全局管理五行/深色/自定义主题，unocss动态变量驱动主题切换。
- **状态管理**：Pinia模块化store，支持本地缓存+云端同步，重要数据持久化。
- **API服务层**：统一请求封装，TS类型安全，支持拦截器、mock、错误处理。
- **响应式/无障碍/动效**：断点适配、aria标签、键盘可达、全局动效参数。
- **工程化**：TS全量类型校验、ESLint/Prettier、单元/集成/端到端测试、CI/CD自动化。

---

## 4. 组件体系

### 4.1 基础UI组件

- GlassButton（主/次/标签/图标/圆形/五行主题/毛玻璃等变体）
- EnhancedGlassCard
- Stepper/ProgressBar（分步表单/进度指示）
- Dropdown/Selector（下拉选择器，支持只读/编辑/毛玻璃/多选/溢出省略）
- Tag/Label/IconLabel
- Modal/Dialog/ActionSheet（弹窗、批量操作、反馈提示）
- List/Grid（分割线、hover、响应式）
- Avatar/UserInfoCard
- WuxingCard/WuxingTag（五行视觉系统专用）
- ResponsiveContainer（内容区边距、断点适配）

### 4.2 业务组件

- OutfitRecommendationCard
- WardrobeItemCard
- OutfitCreateForm（分步、进度、校验、动效）
- AIResultCard
- InspirationCenterPanel
- UserProfilePanel
- FavoritesList
- CalendarOutfitView
- BodyDataForm（体型数据采集，分步/下拉/枚举/毛玻璃）

---

## 5. 路由与页面层级

- 一级页面：home、wardrobe、outfit、profile（底部导航）
- 二级页面：详情、编辑、分析、创建、设置、灵感值、收藏、体型数据、user-info等
- 三级页面：弹窗、批量操作、深度编辑、AI反馈等
- 路由守卫：登录/权限校验、资料完善引导

---

## 6. 状态管理与数据流

- Pinia模块化store：user、wardrobe、outfit、inspiration、theme、settings等
- 支持本地缓存+云端同步
- 重要数据（如user-info、衣橱、搭配、灵感值等）持久化
- 主题/五行/响应式状态全局可用

---

## 7. API服务层

- 统一API请求封装（支持拦截器、错误处理、自动JWT token注入到请求头、loading、mock）
- JWT处理：自动从响应头提取token，自动在请求头添加Authorization: Bearer {token}
- 类型安全（TS接口定义、自动推断）
- 支持RESTful/GraphQL
- 与后端接口文档（API目录）自动对齐

---

## 8. 响应式与无障碍

- unocss断点+自定义CSS变量，适配主流机型
- 所有组件/页面支持响应式布局
- 重点交互/信息区加aria标签、键盘可达性、色彩对比
- 动效参数全局统一，支持用户关闭动效

---

## 9. 工程化与开发体验

- TS全量类型校验
- 代码分层、职责清晰、易于维护和扩展
- 组件/页面/业务逻辑分离
- 单元/集成/端到端测试全覆盖
- CI/CD自动化部署、lint、格式化、预览
- 主题/样式/五行系统可热更新

---

## 10. 设计-开发-验收协作机制

- 设计师输出tailwind原型，标注所有样式变量、主题色、五行色、圆角、阴影、动效等。
- 前端开发在uno.config.ts中定义所有shortcut/变量，组件开发时直接复用。
- 组件库/样式库集中管理，团队复用和维护。
- 开发完成后，设计师/产品经理对照原型和设计规范验收UI/交互/动效/主题等还原度。
- 设计规范/原型变更需同步到unocss配置和组件库，重大变更需评审、全局通知、版本管理。

---

## 11. 开发环境与工具链
- 推荐IDE：VSCode（插件：Volar、unocss、ESLint、Prettier、Pinia、uni-app助手等）
- 代码规范：统一使用ESLint+Prettier，支持自动修复，commit前自动校验
- Git分支策略：main/dev/feature/bugfix/release，采用Git Flow或Trunk Based
- Commit规范：约定式提交（如@commitlint/conventional），自动生成changelog
- 自动化工具：husky、lint-staged、cz-git、commitizen

## 12. unocss主题系统与五行切换实现细节
- 主题变量统一在uno.config.ts中定义，支持五行/深色/自定义主题
- 五行色彩、圆角、阴影、毛玻璃等全局变量，组件通过class绑定动态切换
- Pinia全局store管理当前主题/五行，切换时自动刷新全局样式
- 支持用户自定义主题色，持久化到本地
- 主题切换动效：过渡动画、渐变、全局同步
- 示例：
```ts
// store/theme.ts
export const useThemeStore = defineStore('theme', {
  state: () => ({ wuxing: 'jin', dark: false, customColor: '' }),
  actions: { setWuxing(w) { this.wuxing = w } }
})
```

## 13. 组件开发与复用最佳实践
- 组件命名统一PascalCase，目录与文件同名
- props/emit严格类型定义，props默认值、必填、校验
- slots灵活支持，文档注释齐全
- 样式隔离：scoped/unocss原子类，避免全局污染
- 组件文档与Storybook/示例驱动开发，便于团队复用
- 复用型组件优先抽象，业务组件组合实现

## 14. API服务层实现细节
- 所有接口统一在/services目录，TS类型定义、自动推断
- axios/fetch二次封装，支持拦截器、全局loading、错误处理、JWT token自动注入到请求头
- JWT处理机制：拦截器自动从登录响应头提取token，自动在后续请求头中添加Authorization: Bearer {token}
- mock方案：本地mock+easy-mock/rap2等，开发/测试环境自动切换
- 接口文档自动同步（如openapi-generator），与后端协作流程标准化

## 15. 页面还原与验收流程
- 设计-开发-验收三方协作，Figma原型+设计规范为唯一验收标准
- 还原度自测清单：UI、交互、动效、无障碍、响应式、主题切换等
- 验收流程：开发自测→设计师验收→产品经理终验，问题记录与跟踪

## 16. 性能优化与包体积控制
- 页面/组件懒加载，分包（subpackages）
- 图片/字体/资源压缩与按需加载
- unocss按需生成、tree-shaking、无用样式清理
- 代码分层、依赖优化、避免重复包

## 17. 测试策略
- 单元测试（Vitest/Jest）：组件、工具函数、store
- 集成测试：页面/模块组合、mock数据
- 端到端测试（Cypress/uni-app内置）：关键流程、表单、主题切换
- CI自动化测试，测试覆盖率统计，PR强制通过测试

## 18. 无障碍与国际化细则
- 重点交互/信息区加aria标签，键盘可达性、色彩对比
- 国际化资源统一在/locales，vue-i18n动态切换，支持RTL
- 组件/页面多语言适配，日期/数字本地化

## 19. 上线与运维
- 小程序发布流程：开发→测试→预发布→正式发布，支持灰度/回滚
- 监控埋点、错误上报（如Sentry）、用户反馈通道
- 版本管理、变更日志、用户通知

## 20. 持续集成与质量保障
- CI/CD流水线：Gitee Go流水线，支持lint、类型检查、单元/集成/端到端测试、自动预览、自动部署
- PR流程：Gitee Pull Request，代码评审、自动检查、预览环境、合并策略
- 代码评审机制：Gitee强制双人review，问题跟踪与整改
- 支持Gitee Issue、Wiki、里程碑等项目管理工具

## 21. 微信小程序官方框架对齐

### 21.1 目录结构与文件类型映射
- 微信小程序核心文件：app.js（全局逻辑）、app.json（全局配置）、app.wxss（全局样式）
- 页面四件套：index.js（页面逻辑）、index.json（页面配置）、index.wxml（页面结构）、index.wxss（页面样式）
- uni-app开发时，/src/pages、/src/components等目录，HBuilderX/cli自动生成对应小程序原生结构，需关注页面路径、命名一致性
- 仅白名单文件可上传，详见[官方文档](https://developers.weixin.qq.com/miniprogram/dev/framework/structure.html)

### 21.2 WXSS样式体系与rpx单位
- 所有样式建议使用rpx单位，适配不同屏幕和设备像素比
- 全局样式（app.wxss）与局部样式（page.wxss）分离，避免全局污染
- unocss原子类需通过uni-app插件/预处理转译为兼容WXSS的样式
- 仅支持部分CSS选择器，复杂嵌套/伪类需规避

### 21.3 WXML与数据绑定机制
- uni-app模板语法与WXML高度兼容，支持Mustache插值、条件/循环渲染、事件绑定
- 数据响应式：Page/Component的data与视图自动同步，setData触发视图更新
- 事件系统：bindtap、catchtap等事件，uni-app自动映射为小程序事件

### 21.4 基础组件与API调用规范
- 常用原生组件：view、button、image、scroll-view、input、picker、swiper等，uni-app组件自动转译为对应小程序组件
- API调用：优先使用uni-app跨端API（如uni.request、uni.login），如需用wx.xxx原生API，需判断平台兼容性
- 组件/页面间通信：props、事件、全局store（Pinia）

### 21.5 页面/应用生命周期管理
- App注册：onLaunch、onShow、onHide、onError等
- Page注册：onLoad、onShow、onReady、onHide、onUnload、onPullDownRefresh等
- uni-app生命周期（onLoad、onShow、onReady等）自动映射到小程序生命周期

### 21.6 开发工具链与调试上线
- 推荐使用微信开发者工具进行真机调试、代码上传、预览、发布
- uni-app项目通过HBuilderX/cli打包为小程序代码包，再用微信开发者工具导入
- 支持云开发、云函数、云数据库等微信原生能力

### 21.7 平台兼容性与限制说明
- 仅支持白名单文件类型，详见官方文档
- 某些CSS/JS特性、第三方库可能不兼容，需查阅[小程序兼容性指南](https://developers.weixin.qq.com/miniprogram/dev/framework/compatibility.html)
- 性能优化建议：分包、懒加载、图片压缩、避免大循环/大DOM树

### 21.8 官方最佳实践与推荐
- 性能优化：分包加载、图片/字体/资源压缩、unocss按需生成
- 无障碍：aria标签、色彩对比、键盘可达性
- 国际化：多语言资源、RTL支持、日期/数字本地化
- 埋点与监控：使用微信云开发/第三方SaaS埋点、错误上报
- 设计规范：遵循微信小程序官方设计体系，参考[官方设计文档](https://developers.weixin.qq.com/miniprogram/design/)

---

如需进一步补充uni-app与微信小程序原生能力的详细对照表、常见问题FAQ、官方推荐代码片段等，请继续说明。

**结论**：  
本架构设计方案高度还原原型与设计规范，具备极强的可维护性、可扩展性和高体验标准。所有实现细节均可直接指导uni-app+Vue3+TS+unocss的微信小程序前端开发，确保最终产品在UI、交互、性能、无障碍、主题等方面达到行业顶级水准。

如需输出具体代码模板、组件实现示例、CI/CD配置、主题系统源码等，请继续说明。

# 项目初始化与安装指南

## 推荐方式：pnpm + Vitesse Uni App 模板

### 1. 环境准备
- 安装 [Node.js](https://nodejs.org/)（建议 LTS 版本）
- 安装 [pnpm](https://pnpm.io/zh/installation)：
  ```bash
  npm install -g pnpm
  ```

### 2. 创建项目（Vue3 + TypeScript + unocss + Pinia + Vite 工程化）
- 在 frontend 目录下执行：
  ```bash
  pnpm create uni stylishlink-miniprogram -t vitesse
  ```
  或（如上命令不可用，可用 giget 方案）：
  ```bash
  pnpx giget gh:uni-helper/vitesse-uni-app stylishlink-miniprogram
  ```
- 进入项目目录：
  ```bash
  cd stylishlink-miniprogram
  ```
- 安装依赖：
  ```bash
  pnpm install
  ```

### 3. 目录结构说明
- 项目结构已自动包含 /src/pages、/src/components、/src/store、/src/services 等，完全对齐本架构设计方案。
- 支持多端（微信小程序、H5、App等）开发与编译。

### 4. 启动开发/预览
- 启动本地开发服务器（H5模式）：
  ```bash
  pnpm dev
  ```
- 启动微信小程序开发模式：
  ```bash
  pnpm dev:mp-weixin
  ```
- 构建微信小程序产物（dist/build/mp-weixin），用微信开发者工具导入预览/真机调试：
  ```bash
  pnpm build:mp-weixin
  ```

### 5. HBuilderX/微信开发者工具配合
- 可用 HBuilderX/VSCode 打开 stylishlink-frontend 目录进行开发。
- 微信小程序端建议用微信开发者工具导入 dist/build/mp-weixin 目录进行真机调试、上传、发布。

### 6. 依赖与工程化
- 已内置 Vue3、TypeScript、Pinia、unocss、Vite、ESLint、Prettier 等现代前端工程能力。
- 支持自动化测试、CI/CD、主题系统、五行切换、国际化、无障碍等。 