# StylishLink 文档目录

> 本目录按文档类型分组，每组内文档按名称括号中的日期升序排列，无日期文档排在最后。建议每次文档更新后同步维护本目录。

---

## 需求文档
- 主需求: [穿搭推荐小程序产品需求分析.md](./穿搭推荐小程序产品需求分析.md)
- 2025.03.10更新: [五行命理结合天气的穿搭推荐系统需求文档.md](./五行命理结合天气的穿搭推荐系统需求文档.md) —— 五行命理与天气穿搭推荐系统详细需求
- 2025.03.20更新: [穿搭推荐小程序新增需求分析文档(3.18).md](./穿搭推荐小程序新增需求分析文档(3.18).md)
- 2025.03.26更新: [穿搭推荐小程序新增需求分析文档(3.21).md](./穿搭推荐小程序新增需求分析文档(3.21).md)  [穿搭推荐小程序灵感值运营系统需求文档(3.27).md](./穿搭推荐小程序灵感值运营系统需求文档(3.27).md)
- 2025.04.10更新: [穿搭推荐小程序原型优化文档.md](./穿搭推荐小程序原型优化文档.md) —— UI/UX设计规范和五行能量视觉系统说明
- 2025.04.21更新: [原型优化需求分析(4.21).md](./原型优化需求分析(4.21).md) —— 原型优化需求的结构化归纳与主需求文档的补充
- [stylishlink_core_requirements.md](./stylishlink_core_requirements.md)

## 设计/原型文档
- [prototype_structure.md](./prototype_structure.md)
- [穿搭小程序界面设计方案.md](./穿搭小程序界面设计方案.md)
- [穿搭小程序二级界面设计方案.md](./穿搭小程序二级界面设计方案.md)
- [prototype/](./prototype/)

## 技术/实现方案
- [StylishLink微信小程序实现方案.md](./StylishLink微信小程序实现方案.md)
- [stylishlink_architecture.md](./stylishlink_architecture.md)
- [前端实现方案.md](./前端实现方案.md)
- 2025.05更新: [后端技术架构设计.md](./后端技术架构设计.md) —— Java+Spring Cloud微服务架构设计，支持灵感值运营、五行命理、视频生成等全部功能
- [数据库设计方案.md](./数据库设计方案.md)
- 2025.12更新: [后端与云开发实现方案.md](./后端与云开发实现方案.md) —— 后端架构方案，已集成文件存取服务设计
- [文件存取服务设计文档.md](./文件存取服务设计文档.md) —— 基于腾讯云COS的统一文件管理服务设计

## API接口文档
- [common-service-api.md](./api/common-service-api.md)
- [eureka-server-api.md](./api/eureka-server-api.md)
- [gateway-service-api.md](./api/gateway-service-api.md)
- [user-service-api.md](./api/user-service-api.md)
- [wardrobe-service-api.md](./api/wardrobe-service-api.md)
- [recommendation-service-api.md](./api/recommendation-service-api.md)
- [ai-service-api.md](./api/ai-service-api.md)
- [operation-service-api.md](./api/operation-service-api.md)
- [payment-service-api.md](./api/payment-service-api.md)
- [social-service-api.md](./api/social-service-api.md)
- [file-service-api.md](./api/file-service-api.md) —— 文件存取服务API接口文档
