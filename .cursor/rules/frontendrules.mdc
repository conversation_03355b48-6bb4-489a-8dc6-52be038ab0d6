---
description: 
globs: 
alwaysApply: true
---
# StylishLink 微信小程序开发规范 (.cursorrules)

## 项目概述

**项目名称**: StylishLink - 时尚搭配助手微信小程序
**核心定位**: 基于五行命理与天气数据的智能穿搭推荐系统，支持数字衣橱管理、真人试衣、社交分享等功能

### 核心功能模块
- **个人数字衣橱**: 智能识别上传服饰、AI分类管理、穿着频率统计
- **智能穿搭推荐**: 基于五行命理+天气+场景的个性化推荐
- **真人形象试衣**: 基于用户照片的虚拟试衣效果
- **社交分享互动**: 穿搭展示、用户互动、专业咨询
- **运营系统**: 灵感值管理、任务成就、会员特权

---

## 技术栈与架构

### 前端技术栈
- **框架**: uni-app (Vue3 + TypeScript)
- **UI组件**: @dcloudio/uni-ui + 自定义组件
- **状态管理**: Pinia
- **构建工具**: Vite
- **样式方案**: SCSS + UnoCSS
- **代码规范**: ESLint + Prettier
- **类型检查**: TypeScript 严格模式

### 后端架构
- **架构模式**: 微服务架构 (Spring Cloud)
- **核心服务**: user-service, wardrobe-service, recommendation-service, ai-service, operation-service, social-service, payment-service
- **API网关**: Spring Cloud Gateway
- **数据库**: MongoDB + MySQL 混合方案
- **缓存**: Redis
- **消息队列**: RabbitMQ
- **对象存储**: 阿里云OSS/腾讯云COS

---

## 开发规范

### 文件结构规范
```
src/
├── pages/           # 页面文件
│   ├── index/       # 首页
│   ├── wardrobe/    # 衣橱
│   ├── outfit/      # 搭配
│   └── profile/     # 个人中心
├── components/      # 组件库
│   ├── common/      # 通用组件
│   ├── business/    # 业务组件
│   └── layout/      # 布局组件
├── static/          # 静态资源
│   ├── styles/      # 全局样式
│   ├── images/      # 图片资源
│   └── iconfont/    # 字体图标
├── store/           # 状态管理
├── api/             # API接口
├── utils/           # 工具函数
├── types/           # TypeScript类型定义
└── config/          # 配置文件
```

### 命名规范
- **文件命名**: kebab-case (例: `user-profile.vue`)
- **组件命名**: PascalCase (例: `TheTabBar`, `UserProfile`)
- **函数/变量**: camelCase (例: `getUserInfo`, `currentUser`)
- **常量**: SCREAMING_SNAKE_CASE (例: `API_BASE_URL`)
- **CSS类名**: kebab-case (例: `.tab-item`, `.user-avatar`)

### TypeScript 规范
- **启用严格模式**: 在 `tsconfig.json` 中开启所有严格检查
- **类型定义**: 所有API接口、组件Props、状态都必须有明确类型定义
- **避免any**: 禁止使用 `any` 类型，使用 `unknown` 或具体联合类型
- **接口优先**: 优先使用 `interface` 而非 `type`

```typescript
// ✅ 推荐
interface UserInfo {
  id: string
  nickname: string
  avatar?: string
}

// ❌ 避免
const userInfo: any = {}
```

---

## 微信小程序特定规范

### WXSS/CSS 兼容性
- **不支持的属性**: `backdrop-filter`, `@supports`, CSS嵌套语法
- **替代方案**: 使用渐变背景、多重阴影模拟毛玻璃效果
- **选择器限制**: 避免使用高级CSS选择器，优先使用类选择器

```scss
// ✅ 微信小程序兼容写法
.glass-effect {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.85) 0%,
    rgba(255, 255, 255, 0.65) 50%,
    rgba(255, 255, 255, 0.85) 100%
  );
  box-shadow: 
    0 -8px 32px rgba(0, 0, 0, 0.06),
    0 -2px 8px rgba(0, 0, 0, 0.03);
}

// ❌ 不兼容写法
.glass-effect {
  backdrop-filter: blur(20px);
  @supports (backdrop-filter: blur(20px)) {
    background: rgba(255, 255, 255, 0.7);
  }
}
```

### 小程序API使用
- **统一前缀**: 使用 `uni.*` API替代 `wx.*`，确保跨平台兼容
- **错误处理**: 所有异步操作都要有完整的错误处理
- **权限申请**: 相机、相册等敏感权限要先检查再使用

```typescript
// ✅ 推荐写法
const chooseImage = async () => {
  try {
    const res = await uni.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['camera', 'album']
    })
    return res.tempFilePaths[0]
  } catch (error) {
    uni.showToast({
      title: '选择图片失败',
      icon: 'none'
    })
    throw error
  }
}
```

### 性能优化要求
- **图片优化**: 使用 `mode="aspectFit"` 或 `mode="aspectFill"`
- **列表优化**: 长列表使用虚拟滚动或分页加载
- **包体积**: 单个包不超过2MB，合理使用分包
- **内存管理**: 及时清理定时器、事件监听器

### 系统UI约束
微信小程序的系统UI会占用屏幕的固定区域，开发者必须避免在这些区域放置可交互元素，防止功能冲突和用户体验问题。

#### 右上角官方菜单区域
- **占用区域**: 右上角固定位置，包含转发、设置、关闭等功能
- **约束要求**: 
  - 禁止在右上角区域放置任何可交互元素
  - 自定义导航按钮应与官方菜单保持足够距离（建议≥16px）
  - 浮动按钮、开发工具入口等应避开此区域

```scss
// ❌ 错误：与系统菜单冲突
.dev-tools {
  position: fixed;
  top: 50px;
  right: 16px; // 与微信菜单冲突
}

// ✅ 正确：放置在左上角或其他安全区域
.dev-tools {
  position: fixed;
  top: 50px;
  left: 16px; // 安全区域
}
```

#### 状态栏安全区域适配
- **占用区域**: 顶部状态栏区域，高度因设备而异
- **适配方法**: 使用安全区域CSS变量获取准确高度

```scss
// 顶部导航适配
.custom-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  padding-top: constant(safe-area-inset-top);
  padding-top: env(safe-area-inset-top);
}
```

#### 底部安全区域适配
- **占用区域**: 底部Home指示器区域（iPhone X系列及以上）
- **适配要求**: TabBar、浮动按钮等底部元素必须适配

```scss
// TabBar 安全区域适配
.tab-bar {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

// 浮动操作按钮适配
.floating-action-btn {
  bottom: calc(20px + constant(safe-area-inset-bottom));
  bottom: calc(20px + env(safe-area-inset-bottom));
}
```

#### 导航设计建议
- **返回按钮**: 所有次级页面左上角提供返回操作
- **标题居中**: 页面标题应居中显示，避免与官方菜单重叠
- **可点击区域**: 所有交互元素最小点击区域不小于44px × 44px

### 胶囊按钮动态适配规范

微信小程序右上角胶囊按钮位置会根据不同iOS设备动态变化，传统的固定CSS值无法满足精准适配需求。使用动态计算方案解决兼容性问题。

#### 核心原理
- **API获取**: 使用 `uni.getMenuButtonBoundingClientRect()` 获取胶囊按钮实际位置
- **动态计算**: 基于胶囊按钮位置计算页面元素定位
- **兼容性**: 提供默认值确保在API失效时的基础可用性

#### useMenuButton Composable 实现

```typescript
// src/utils/useMenuButton.ts
import { computed, ref } from 'vue'

/**
 * 微信小程序胶囊按钮动态适配 Composable
 * 解决不同iOS设备胶囊按钮位置差异问题
 */
export function useMenuButton() {
  const menuButtonInfo = ref({
    width: 87,
    height: 32,
    top: 59,
    right: 278,
    bottom: 91,
    left: 191,
  })

  // 获取胶囊按钮信息
  const getMenuButtonBoundingClientRect = () => {
    try {
      const rect = uni.getMenuButtonBoundingClientRect()
      if (rect && rect.width > 0) {
        menuButtonInfo.value = rect
      }
    } catch (error) {
      console.warn('获取胶囊按钮信息失败，使用默认值:', error)
    }
  }

  // 计算属性 - 各种业务场景的动态高度
  const statusBarHeight = computed(() => {
    const systemInfo = uni.getSystemInfoSync()
    return systemInfo.statusBarHeight || 20
  })

  // 胶囊按钮底部位置
  const menuButtonBottom = computed(() => menuButtonInfo.value.bottom)

  // 主要内容区域顶部间距（胶囊按钮底部 + 间距）
  const mainContentPaddingTop = computed(() => `${menuButtonBottom.value + 20}px`)

  // 天气信息对齐胶囊按钮顶部（首页专用）
  const weatherAlignTop = computed(() => `${menuButtonInfo.value.top}px`)

  // 内容区域距离胶囊按钮底部12px（其他页面专用）
  const contentOffset12px = computed(() => `${menuButtonBottom.value + 12}px`)

  // 页面标题定位（与胶囊按钮垂直居中对齐）
  const pageHeaderTop = computed(() => {
    const capsuleCenter = menuButtonInfo.value.top + menuButtonInfo.value.height / 2
    const headerHeight = 28
    return `${capsuleCenter - headerHeight / 2}px`
  })

  // 导航栏总高度（含状态栏）
  const navigationBarHeight = computed(() => menuButtonBottom.value)

  // 初始化
  getMenuButtonBoundingClientRect()

  return {
    menuButtonInfo: computed(() => menuButtonInfo.value),
    statusBarHeight,
    menuButtonBottom,
    mainContentPaddingTop,
    weatherAlignTop,
    contentOffset12px,
    pageHeaderTop,
    navigationBarHeight,
    getMenuButtonBoundingClientRect,
  }
}
```

#### 业务场景使用示例

**1. 首页天气信息对齐胶囊按钮**
```vue
<template>
  <view class="weather-section" :style="{ marginTop: weatherAlignTop }">
    <!-- 天气信息与胶囊按钮水平对齐 -->
  </view>
</template>

<script setup>
import { useMenuButton } from '@/utils/useMenuButton'

const { weatherAlignTop } = useMenuButton()
</script>
```

**2. 其他页面内容区域距离胶囊按钮12px**
```vue
<template>
  <scroll-view 
    class="main-scroll-container"
    :style="{ 
      height: `calc(100vh - ${contentOffset12px})`,
      marginTop: contentOffset12px 
    }"
  >
    <!-- 页面内容 -->
  </scroll-view>
</template>

<script setup>
import { useMenuButton } from '@/utils/useMenuButton'

const { contentOffset12px } = useMenuButton()
</script>
```

**3. 页面标题与胶囊按钮垂直居中**
```vue
<template>
  <view 
    class="page-header" 
    :style="{ top: pageHeaderTop }"
  >
    <text class="page-title">页面标题</text>
  </view>
</template>

<script setup>
import { useMenuButton } from '@/utils/useMenuButton'

const { pageHeaderTop } = useMenuButton()
</script>
```

### 5. 页面标题布局规范
使用动态胶囊按钮适配替代固定CSS值：

```vue
<!-- 页面标题组件 -->
<template>
  <view 
    class="page-header" 
    :style="{ top: pageHeaderTop }"
  >
    <text class="page-title">{{ title }}</text>
  </view>
</template>

<script setup>
import { useMenuButton } from '@/utils/useMenuButton'

interface Props {
  title: string
}

defineProps<Props>()

const { pageHeaderTop } = useMenuButton()
</script>

<style scoped lang="scss">
.page-header {
  position: fixed;
  left: 5%;
  z-index: 100;
  height: 28px;
  display: flex;
  align-items: center;
}

.page-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}
</style>
```

**重要规范**：
- **动态定位**: 使用 `pageHeaderTop` 实现与胶囊按钮精确垂直居中对齐
- **设备兼容**: 自动适配不同iOS设备的胶囊按钮位置差异
- **业务场景**: 首页通常无需页面标题，次级页面建议添加
- **视觉平衡**: 标题居中显示，与右上角微信菜单形成视觉平衡

### 6. scroll-view 布局规范
使用动态胶囊按钮适配的 scroll-view 布局：

```vue
<template>
  <view class="page-container">
    <!-- 固定标题（可选） -->
    <PageHeader 
      :title="pageTitle"
      @back="handleBack"
    />

    <!-- 滚动容器 - 使用动态高度适配 -->
    <scroll-view 
      class="main-scroll-container" 
      enable-flex 
      scroll-y 
      :enhanced="true" 
      :bounces="false" 
      :show-scrollbar="false"
      :style="{
        height: `calc(100vh - ${contentOffset12px})`,
        marginTop: contentOffset12px
      }"
    >
      <!-- 滚动内容 -->
      <view class="main-content">
        <!-- 页面内容 -->
      </view>

      <!-- 全宽组件（如分类tab） -->
      <CategoryTabs />
    </scroll-view>
  </view>
</template>

<script setup>
import { useMenuButton } from '@/utils/useMenuButton'
import PageHeader from '@/components/common/PageHeader.vue'

const { contentOffset12px } = useMenuButton()

const handleBack = () => {
  uni.navigateBack()
}
</script>
```

**关键优势**：
- **精确适配**: 使用 `contentOffset12px` 确保内容区域距离胶囊按钮底部正好12px
- **设备兼容**: 自动适应不同iOS设备的胶囊按钮位置差异
- **动态计算**: 实时获取胶囊按钮位置，无需硬编码CSS值
- **布局稳定**: 避免内容与胶囊按钮区域重叠

---

## UI/UX 设计规范

### 设计系统
- **主色调**: 蓝紫渐变 `linear-gradient(135deg, #667eea 0%, #764ba2 100%)`
- **背景**: 毛玻璃效果 `rgba(255, 255, 255, 0.25)` + 边框光晕
- **圆角**: 统一使用 `border-radius: 16px, 20px, 24px`
- **间距**: 遵循 4px 基准，使用 4, 8, 12, 16, 20, 24px

### 五行色彩系统
```scss
$wuxing-colors: (
  金: linear-gradient(135deg, #ffffff, #f0f0f0),
  木: linear-gradient(135deg, #a8e6cf, #73c1a8),
  水: linear-gradient(135deg, #b8c6db, #648dae),
  火: linear-gradient(135deg, #ff9a9e, #ff5458),
  土: linear-gradient(135deg, #ffeaa7, #ffc25c)
);
```

### 字体规范
- **页面标题**: 14px (text-sm) + font-semibold
- **卡片标题**: 14px (text-sm) + font-medium  
- **正文内容**: 12px (text-xs)
- **辅助说明**: 10px (text-[10px])

### 组件规范
- **TabBar高度**: 68px + 安全区域
- **卡片内边距**: 16px
- **按钮最小点击区域**: 44px × 44px
- **图标尺寸**: 16px, 20px, 24px
- **iconfont**: 不要使用emoji, 要使用不要使用iconfont. iconfont 可以参考 [demo_index.html](mdc:frontend/stylishlink-miniprogram/src/static/iconfont/demo_index.html), 如遇到iconfont中不存在合适的,提醒我添加新的iconfont.

---

## 代码质量标准

### 组件开发规范
```vue
<!-- ✅ 推荐的组件结构 -->
<script setup lang="ts">
// 1. 类型定义
interface Props {
  title: string
  items: TabItem[]
}

// 2. Props 和 Emits
const props = withDefaults(defineProps<Props>(), {
  title: '默认标题'
})

const emit = defineEmits<{
  change: [value: string]
}>()

// 3. 响应式数据
const currentTab = ref('')

// 4. 计算属性
const filteredItems = computed(() => {
  return props.items.filter(item => item.visible)
})

// 5. 方法
const handleTabChange = (value: string) => {
  currentTab.value = value
  emit('change', value)
}

// 6. 生命周期
onMounted(() => {
  // 组件挂载逻辑
})
</script>

<template>
  <view class="tab-container">
    <!-- 模板内容 -->
  </view>
</template>

<style scoped lang="scss">
.tab-container {
  // 样式定义
}
</style>
```
### API 调用规范
```typescript
// ✅ 推荐的API调用方式
export const useUserApi = () => {
  const getUserInfo = async (userId: string): Promise<UserInfo> => {
    try {
      const { data } = await request.get<ApiResponse<UserInfo>>(`/user/${userId}`)
      if (data.code === 200) {
        return data.data
      }
      throw new Error(data.message)
    } catch (error) {
      console.error('获取用户信息失败:', error)
      throw error
    }
  }

  return {
    getUserInfo
  }
}
```

### 状态管理规范
```typescript
// ✅ 推荐的Pinia store结构
export const useUserStore = defineStore('user', () => {
  // 状态
  const userInfo = ref<UserInfo | null>(null)
  const loading = ref(false)

  // 计算属性  
  const isLoggedIn = computed(() => !!userInfo.value)

  // 方法
  const login = async (loginData: LoginRequest) => {
    loading.value = true
    try {
      const response = await authApi.login(loginData)
      userInfo.value = response.userInfo
      uni.setStorageSync('token', response.token)
    } finally {
      loading.value = false
    }
  }

  const logout = () => {
    userInfo.value = null
    uni.removeStorageSync('token')
  }

  return {
    userInfo,
    loading,
    isLoggedIn,
    login,
    logout
  }
})
```

---

## 常见问题解决方案

### 1. WXSS编译错误
**问题**: `unexpected '@' at pos xxx`
**原因**: 微信小程序不支持 CSS `@supports` 规则和 SCSS 嵌套语法
**解决**: 
- 移除 `@supports` 规则
- 展开嵌套语法为平级CSS
- 使用标准CSS实现兼容性

### 2. 毛玻璃效果实现
**问题**: `backdrop-filter` 在小程序中不生效
**解决方案**:
```scss
.glass-card {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.85) 0%,
    rgba(255, 255, 255, 0.65) 50%,
    rgba(255, 255, 255, 0.85) 100%
  );
  box-shadow: 
    0 -8px 32px rgba(0, 0, 0, 0.08),
    0 -2px 8px rgba(0, 0, 0, 0.03),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.6);
}
```

### 3. 图标颜色不变
**问题**: iconfont图标颜色无法通过CSS修改
**解决方案**:
```scss
// 使用更高特异性的选择器
.bottom-nav .tab-item .tab-icon .iconfont {
  color: var(--color-primary, #8b5cf6) !important;
}
```

### 4. CSS变量不生效
**问题**: CSS变量在某些组件中无效
**解决方案**:
- 在 `App.vue` 中全局引入变量文件
- 为CSS变量提供后备值: `var(--color-primary, #8b5cf6)`

### 5. 安全区域适配
```scss
// TabBar 安全区域适配
.tab-bar {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
```

### 6. 分类tab延伸问题
**问题**: 分类tab没有延伸到屏幕边缘
**原因**: tab组件被包含在有padding的容器内
**解决方案**:
```vue
<!-- ✅ 正确：将CategoryTabs放在scroll-view下，避免被有padding的容器包含 -->
<scroll-view>
  <view class="main-content">
    <!-- 有padding的内容 -->
  </view>
  <!-- CategoryTabs直接在scroll-view下，可以延伸到边缘 -->
  <CategoryTabs />
</scroll-view>

<!-- ❌ 错误：被有padding的容器包含 -->
<view class="content-container"> <!-- 这里有padding -->
  <CategoryTabs /> <!-- 会被padding限制 -->
</view>
```

### 7. 滚动条显示问题
**问题**: 页面滚动时显示滚动条
**解决方案**: 使用完整的滚动条隐藏CSS
```scss
.scrollable-container {
  // 微信小程序隐藏滚动条 - 多重方案确保兼容性
  ::-webkit-scrollbar {
    display: none !important;
    width: 0 !important;
    height: 0 !important;
    background: transparent !important;
    -webkit-appearance: none !important;
  }
  
  ::-webkit-scrollbar-track {
    display: none !important;
    background: transparent !important;
  }
  
  ::-webkit-scrollbar-thumb {
    display: none !important;
    background: transparent !important;
  }
  
  scrollbar-width: none !important;
  -ms-overflow-style: none !important;
}
```

### 8. ESLint import 顺序问题
**问题**: TypeScript导入顺序不符合规范
**解决方案**:
```typescript
// ✅ 正确顺序
import { ref } from 'vue'                    // 外部库
import type { ActionType } from '@/types'    // 内部类型
import Component from '@/components'         // 内部组件

// ❌ 错误顺序
import type { ActionType } from '@/types'    // 内部类型在前
import { ref } from 'vue'                    // 外部库在后
```

---

## 提交规范

### Git Commit 格式
```
<type>(<scope>): <description>

[optional body]

[optional footer]
```

**Type类型**:
- `feat`: 新功能
- `fix`: 修复bug
- `style`: 样式调整
- `refactor`: 重构
- `perf`: 性能优化
- `test`: 测试相关
- `docs`: 文档更新
- `chore`: 构建/工具链相关

**示例**:
```
feat(tabbar): 实现底部导航栏毛玻璃效果

- 使用渐变背景模拟毛玻璃效果
- 添加多重阴影增强层次感
- 兼容微信小程序WXSS限制

Closes #123
```

---

## 最佳实践

### 1. 组件设计原则
- **单一职责**: 每个组件只负责一个功能
- **可复用**: 通过Props配置不同状态
- **可测试**: 逻辑与视图分离，便于单元测试

### 2. 性能优化
- **图片懒加载**: 使用 `<image lazy-load="true">`
- **列表虚拟化**: 长列表使用虚拟滚动
- **代码分割**: 合理使用动态导入和分包

### 3. 用户体验
- **加载状态**: 所有异步操作提供loading反馈
- **错误处理**: 优雅的错误提示和恢复机制
- **无网络状态**: 离线场景的友好提示

### 4. 可维护性
- **文档完善**: 关键组件和函数要有注释说明
- **类型安全**: 充分利用TypeScript类型检查
- **代码复用**: 抽取公共逻辑到utils或composables

---

## 工具链配置

### VSCode 推荐插件
- uni-app官方插件
- Vetur/Volar (Vue支持)
- TypeScript 语言服务
- ESLint
- Prettier
- SCSS IntelliSense

### 开发调试
- 微信开发者工具
- Chrome DevTools (H5调试)
- uni-app调试插件

---

遵循以上规范，确保代码质量、用户体验和项目可维护性。在开发过程中如遇到规范之外的问题，优先考虑小程序兼容性和用户体验。 必要时, 请查阅 微信小程序官方文档, 我已添加到项目docs里, 文档名均以"WeChat Mini Program"开头.

