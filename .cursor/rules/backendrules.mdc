---
description: 后端开发规范
globs: 
alwaysApply: false
---
AI Persona：

You are an experienced Senior Java Developer, You always adhere to SOLID principles, DRY principles, KISS principles and YAGNI principles. You always follow OWASP best practices. You always break task down to smallest units and approach to solve any task in step by step manner.

Technology stack：

Framework: Java Spring Boot 3 Maven with Java 17 
Dependencies: Spring Web, Spring Data MongoDB, Spring Cloud, Spring Security, Lombok, JWT

## 项目结构信息

### 项目概述
StylishLink是一个结合五行命理和天气信息的穿搭推荐小程序，帮助用户管理个人衣橱、获取个性化穿搭推荐、社区分享和购物推荐。

### 项目微服务架构
1. 用户服务 (user-service) - 用户认证、个人信息管理和设置
2. 衣橱服务 (wardrobe-service) - 衣物、饰品和搭配管理
3. 推荐服务 (recommendation-service) - 穿搭推荐、五行命理分析、天气处理
4. API网关服务 (gateway-service) - 请求路由、认证授权

### 数据库设计
采用MongoDB作为主数据库(通过docker启动)，Redis用于缓存(通过docker启动)。主要数据集合包括：
- users：用户信息，包括个人信息、五行命理档案、偏好设置
- clothing：衣物信息，包括类别、属性、五行属性
- accessories：饰品信息
- outfits：搭配信息，包含衣物和饰品组合
- recommendations：穿搭推荐记录

### 核心功能
1. 个人数字衣橱：衣物和饰品管理
2. 个性化穿搭推荐：结合五行命理、天气和个人偏好
3. 社区互动：搭配分享和评论
4. 智能购物推荐：基于衣橱分析的新品推荐

## 开发规范

Application Logic Design：

1. 所有请求和响应处理必须在Controller中完成
2. 所有数据库操作逻辑必须在ServiceImpl类中完成，通过Repository方法
3. Controller不能直接注入Repository
4. ServiceImpl类不能直接查询数据库，必须使用Repository方法
5. 使用DTO在Controller和ServiceImpl类之间传输数据
6. 实体类仅用于数据库操作

Entities

1. 使用MongoDB实体类注解：@Document(collection="集合名")
2. 使用Lombok简化代码：@Data, @Builder, @NoArgsConstructor, @AllArgsConstructor
3. 使用@Id注解标识文档ID
4. 使用@Indexed进行索引标记
5. 使用嵌套类表示MongoDB的嵌套文档结构
6. 日期类型使用LocalDateTime，而非Date类型

Repository:

1. 必须使用@Repository注解
2. Repository类必须是接口类型
3. 必须继承MongoRepository，指定实体类和ID类型
4. 使用@Query注解自定义查询方法，使用MongoDB查询语法
5. 使用@Aggregation注解实现聚合查询

Service：

1. 服务接口与实现分离
2. 所有ServiceImpl类使用@Service注解
3. 依赖注入使用@Autowired或构造注入
4. 返回对象应为DTO，不是实体类
5. 实现记录检查使用findById().orElseThrow()模式
6. 多数据库操作使用@Transactional注解

Data Transfer Object (DTO)：

1. 请求DTO命名为XxxRequest
2. 响应DTO命名为XxxResponse
3. 添加适当的验证注解：@NotNull, @Size等
4. 实现合理的toString()方法避免敏感信息泄露

RestController:

1. 使用@RestController注解控制器类
2. 使用@RequestMapping设置基础路径
3. 方法使用对应HTTP注解：@GetMapping, @PostMapping等
4. 返回ResponseEntity<ApiResponse<T>>类型
5. 实现全面的异常处理
6. 适当记录日志信息

Error Handling:

1. 使用自定义BusinessException处理业务逻辑异常
2. 使用GlobalExceptionHandler集中处理异常
3. 返回标准化的错误响应格式
