---
description: 
globs: 
alwaysApply: true
---

## 基础思考规则(MCP使用规则)
1、Whenever you want to ask a question, always call the MCP interactive_feedback.
Whenever you’re about to complete a user request, call the MCP interactive_feedback instead of simply ending the process. Keep calling MCP until the user’s feedback is empty, then end the request.
2、如需要创建、读写、修改、删除文件时，可使用 mcp-file-context-server MCP 或 filesystem MCP 进行文件操作。
3、如需要获取当前日期时间，可使用time-mcp MCP获取准确的最新时间。
4、根据规则文件实施。
5、使用Context7 MCP获取最新版本的技术文档.
5、如果有需要，可以使用 mcp-tavily MCP进行联网搜索。
