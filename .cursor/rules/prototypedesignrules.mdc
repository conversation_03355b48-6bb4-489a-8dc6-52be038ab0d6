---
description: 原型设计规范, 在设计、修改页面原型时使用.
globs: 
alwaysApply: false
---
原型设计或创建html时：参考 [穿搭小程序界面设计方案.md](mdc:doc/穿搭小程序界面设计方案.md)

同时遵守一下基本规范:

1. 优秀的用户体验（UX ）
要求：页面需确保交互流程直观、高效，并符合用户习惯。

2. 优秀的产品界面规划

要求：设计清晰的界面层级，合理的信息架构, 确保用户能够快速访问主要功能，同时保持整体结构的简洁性和可理解性。

3. 高保真 UI 设计

要求：  
基于真实的 iOS 或 Android 设计规范，打造现代化的高保真界面。  
严格遵循 iOS Human Interface Guidelines 或 Android Material Design 规范。  
严格遵循 iOS 人类界面指南或 Android 材料设计规范。

注重视觉设计的统一性，包括色彩搭配、排版规范及图标风格。  

使用真实的 UI 图片（可从 Unsplash、Pexels ），避免使用占位符图像，确保设计具备良好的视觉体验。

4. HTML 原型实现
技术要求：  
使用 HTML + Tailwind CSS（或 Bootstrap）开发所有原型界面。  

集成 FontAwesome（或其他开源 UI 组件）以提升界面的精美度和真实感。

代码结构：  
每个一级界面以独立 HTML 文件形式存储，例如 home.html、profile.html、settings.html 等。  
每个二级界面以独立 HTML 文件形式存储在其模块路径, 例如: \profile\favorite.html d

index.html 作为主入口，不直接包含所有界面的完整代码，而是通过 <iframe> 嵌入各界面文件，并在 index 页面中平铺展示所有页面，避免使用链接跳转。

真实感增强：  
界面尺寸需模拟 iPhone 15 Pro 的屏幕规格，并应用圆角设计，以贴近真实移动设备的外观。  

一级界面需添加 App 导航栏（类似于 iOS 底部 Tab Bar）。

5. 交付物
提供完整的需求文档、和原型设计的完整 HTML 代码，确保其可直接用于开发阶段。  

代码文件结构清晰，命名规范，便于开发团队理解和后续维护。

注意事项：
所有设计和代码实现需符合现代移动应用的标准，兼顾视觉美观性、技术可行性以及未来的可扩展性。



