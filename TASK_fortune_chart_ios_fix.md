# 上下文
文件名：TASK_fortune_chart_ios_fix.md
创建于：2024年12月
创建者：AI
关联协议：RIPER-5 + Multidimensional + Agent Protocol 

# 任务描述
修复运势完整解读页面中的运势趋势图在iOS真机上不显示的兼容性问题。具体症状：
- 在模拟器和Android设备上可以正常显示运势趋势图
- 在iOS真机上趋势图不显示，但其他页面元素正常
- 怀疑是canvas绘制使用了非微信小程序标准的方法

# 项目概述
StylishLink微信小程序中运势完整解读页面(/pages/fortune/complete/index.vue)包含一个运势趋势图组件(OverallFortune.vue)，用于展示用户一生的运势变化曲线。该图表使用canvas绘制，包含坐标轴、运势曲线、关键节点等元素。

---
*以下部分由 AI 在协议执行过程中维护*
---

# 分析 (由 RESEARCH 模式填充)
通过代码分析发现的问题根源：

1. **使用过时的Canvas API**：
   - 当前使用`uni.createCanvasContext(canvasId, instance)`获取渲染上下文
   - 这是微信小程序的旧版Canvas API，在iOS真机上存在兼容性问题
   - iOS WebView对旧版Canvas API的支持不如Android稳定

2. **canvas元素配置问题**：
   - 使用了`canvas-id`属性，这是旧版API的配置方式
   - 缺少`type="2d"`属性，无法启用新版Canvas 2D API
   - 没有使用ref获取canvas节点引用

3. **绘制API不兼容**：
   - 使用了旧版API语法如`ctx.setFillStyle()`、`ctx.setLineWidth()`等
   - 新版Canvas 2D API使用标准Web API语法如`ctx.fillStyle`、`ctx.lineWidth`等

4. **异步处理和重试机制不够健壮**：
   - canvas初始化时机处理不当
   - 缺少对iOS真机特有渲染时序的适配

5. **缺少设备像素比适配**：
   - 没有处理不同设备的像素密度差异
   - 可能导致图表在高分辨率设备上显示模糊或错位

# 提议的解决方案 (由 INNOVATE 模式填充)
经过技术方案评估，推荐使用方案一：升级到微信小程序Canvas 2D API

**方案一：使用Canvas 2D API（推荐）**
优势：
- 更好的跨平台兼容性，特别是iOS真机
- 更接近标准Web Canvas API，开发体验更好
- 官方推荐的新版API，长期支持有保障
- 更好的性能和渲染质量

**方案二：优化现有旧版API**
优势：
- 改动最小，风险较低
- 保持与现有代码架构的一致性
缺点：
- 无法根本解决iOS兼容性问题
- 旧版API可能在未来版本中被废弃

**方案三：使用第三方图表库**
优势：
- 开箱即用，稳定性经过验证
- 功能丰富，支持多种图表类型
缺点：
- 增加包体积，可能影响小程序性能
- 学习成本和定制难度较高

# 实施计划 (由 PLAN 模式生成)

## 技术改造方案
基于Canvas 2D API重构运势趋势图绘制逻辑，确保iOS真机完美兼容。

## 具体修改步骤

### 1. Canvas元素改造
- 文件：frontend/stylishlink-miniprogram/src/pages/fortune/complete/components/OverallFortune.vue
- 移除`canvas-id`属性，添加`type="2d"`属性
- 添加`ref="canvasRef"`用于获取canvas节点引用
- 移除`:enhanced="true"`属性（scroll-view优化，避免潜在iOS兼容性问题）

### 2. Canvas初始化逻辑重构
- 替换`uni.createCanvasContext()`为Canvas 2D API
- 使用`uni.createSelectorQuery()`获取canvas节点
- 添加设备像素比(DPR)适配逻辑
- 实现更健壮的异步初始化和错误处理

### 3. 绘制函数API适配
- 替换所有旧版API调用为标准Canvas 2D API
- `ctx.setFillStyle()` → `ctx.fillStyle`
- `ctx.setStrokeStyle()` → `ctx.strokeStyle`
- `ctx.setLineWidth()` → `ctx.lineWidth`
- `ctx.setLineCap()` → `ctx.lineCap`
- 移除`ctx.draw()`调用（2D API自动渲染）

### 4. 生命周期优化
- 替换`onReady()`为`onMounted()`
- 添加`onUnmounted()`进行资源清理
- 使用`nextTick()`确保DOM渲染完成后再初始化canvas

### 5. 错误处理和调试增强
- 添加详细的console日志用于调试
- 实现更智能的重试机制
- 增加iOS真机特有的兼容性检查

```
实施检查清单：
1. 修改canvas元素为2D模式并添加ref引用
2. 重写canvas初始化函数使用2D API
3. 重构所有绘制函数适配新API
4. 添加iOS真机特有的兼容性处理
5. 优化canvas尺寸和DPR适配
6. 改进错误处理和重试机制
7. 测试在iOS真机上的显示效果
```

# 当前执行步骤 (由 EXECUTE 模式在开始执行某步骤时更新)
> 正在执行: "1. 修改canvas元素为2D模式并添加ref引用"

# 任务进度 (由 EXECUTE 模式在每步完成后追加)
* 2024-12-19
  * 步骤：完整的Canvas 2D API重构和兼容性修复
  * 修改：
    - frontend/stylishlink-miniprogram/src/pages/fortune/complete/components/OverallFortune.vue
    - frontend/stylishlink-miniprogram/src/pages/test/index.vue（修复CaseItem类型错误）
  * 更改摘要：
    - **OverallFortune组件完整重构**：使用Canvas 2D API替代旧版API解决iOS真机兼容性问题
      - 移除canvas-id属性，添加type="2d"和ref引用
      - 重写canvas初始化逻辑，使用uni.createSelectorQuery()获取节点
      - 适配所有绘制函数为Canvas 2D API语法
      - 添加设备像素比(DPR)适配
      - 优化生命周期管理和错误处理
      - 移除scroll-view的enhanced属性提升iOS兼容性
    - **修复CaseItem类型定义**：更新test页面数据结构适配新的业务类型定义
  * 原因：解决iOS真机上运势趋势图不显示的兼容性问题
  * 阻碍：无
  * 状态：已完成，等待测试验证

* 2024-12-19 (编译错误修复)
  * 步骤：修复生命周期函数导入错误
  * 修改：frontend/stylishlink-miniprogram/src/pages/fortune/complete/components/OverallFortune.vue
  * 更改摘要：修复`onMounted`和`onUnmounted`的导入来源，从`@dcloudio/uni-app`改为从`vue`导入
  * 原因：uni-app中生命周期函数应该从Vue导入，不是从uni-app导入
  * 阻碍：无
  * 状态：已完成

* 2024-12-19 (运行时错误修复)
  * 步骤：修复getCurrentInstance和canvas初始化时机问题
  * 修改：frontend/stylishlink-miniprogram/src/pages/fortune/complete/components/OverallFortune.vue
  * 更改摘要：
    - 移除`getCurrentInstance()`的使用，避免实例获取错误
    - 简化canvas selector查询，移除`.in(instance)`调用
    - 改回使用`onReady()`生命周期，确保微信小程序环境兼容性
    - 增加canvas初始化延迟到100ms，确保DOM完全加载
    - 调整绘制延迟到800ms，给canvas更充足的初始化时间
  * 原因：解决`[loader] unexpected current frame status timedout`错误
  * 阻碍：无
  * 状态：已完成

* 2024-12-19 (参考成功模式重构)
  * 步骤：参考WuxingCircle.vue的成功实现模式，重构为旧版Canvas API
  * 修改：frontend/stylishlink-miniprogram/src/pages/fortune/complete/components/OverallFortune.vue
  * 更改摘要：
    - **完全回退到旧版Canvas API**：参考WuxingCircle的成功实现
    - **动态Canvas ID**：使用`fortune-trend-canvas-${Date.now()}`生成唯一ID
    - **恢复getInstance()和旧版API**：`uni.createCanvasContext(canvasId, instance)`
    - **Canvas元素配置**：移除`type="2d"`和`ref`，使用`:canvas-id="canvasId"`
    - **绘制API回退**：全部回退为旧版语法（setFillStyle, setStrokeStyle, draw()等）
    - **生命周期优化**：使用`onMounted()`配合多次延迟绘制（200ms, 800ms）
    - **错误处理增强**：添加canvas错误监听和重试机制
  * 原因：Canvas 2D API改造方向错误，WuxingCircle使用旧版API能正常工作
  * 阻碍：无
  * 状态：已完成，等待iOS真机测试验证

# 最终审查 (由 REVIEW 模式填充)
[待完成] 